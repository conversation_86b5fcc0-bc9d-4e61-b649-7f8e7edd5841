export enum Access {
  READ = "READ",
  CREATE = "CREATE",
  EDIT = "EDIT",
  DELETE = "DELETE",
}

export type StaticPropsPermissions = {
  props: {
    permissions: string[];
  };
};

export type Menu = {
  title: string;
  permissions: string[];
  isMainMenu?: boolean;
  isSettings?: boolean;
  heroIconName?: string;
};

type Menus = {
  [key: string]: Menu;
};

const menus: Menus = {
  dashboard: {
    title: "Dashboard",
    permissions: [
      "MAINTENANCE_READ",
      "ADDRESS_VALIDATION_OVERRIDE_READ",
      "FINEOS_API_SMOKE_TEST_READ",
    ],
    isMainMenu: true,
    heroIconName: "HomeIcon",
  },

  maintenance: {
    title: "Portal Maintenance",
    permissions: ["MAINTENANCE_READ", "MAINTENANCE_EDIT"],
    isMainMenu: true,
    heroIconName: "CogIcon",
  },
  address: {
    title: "Address Lookup",
    permissions: ["MAINTENANCE_READ"],
    isMainMenu: true,
    heroIconName: "HomeIcon",
  },
  addressValidationOverride: {
    title: "Address Validation Override",
    permissions: ["ADDRESS_VALIDATION_OVERRIDE_READ"],
    isMainMenu: true,
    heroIconName: "PencilIcon",
  },
  applicationReassignment: {
    title: "Application Reassignment",
    permissions: ["MAINTENANCE_READ"],
    isMainMenu: true,
    heroIconName: "ArrowRightStartOnRectangleIcon",
  },
  fineosApiSmokeTest: {
    title: "FINEOS API Smoke Test",
    permissions: ["FINEOS_API_SMOKE_TEST_READ"],
    isMainMenu: true,
    heroIconName: "BeakerIcon",
  },
  omniSearch: {
    title: "Omni Search",
    permissions: ["MAINTENANCE_READ"],
    isMainMenu: true,
    heroIconName: "MagnifyingGlassIcon",
  },
  overpaymentCollection: {
    title: "Overpayment Collection",
    permissions: ["OVERPAYMENT_COLLECTION_READ"],
    isMainMenu: true,
    heroIconName: "CurrencyDollarIcon",
  },
};

export default menus;
