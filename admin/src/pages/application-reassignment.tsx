import Alert from "../components/Alert";
import {
  ApiResponse,
  ApplicationReassignmentPreviewResponse,
  ApplicationReassignmentRequest,
  patchAdminApplicationsReassign,
  UserApplication,
  postAdminApplicationsReassignPreview,
} from "../_api";
import {
  Field,
  FieldHookConfig,
  Formik,
  FormikErrors,
  FormikHelpers,
  useField,
} from "formik";
import React from "react";
import { useState } from "react";
import { HelmetProvider } from "react-helmet-async";
import { StaticPropsPermissions } from "../menus";
import Table from "../components/Table";
import { formatDateTime } from "../utils/content_helpers";
import { useRouter } from "next/router";

type FormValues = {
  from_email: string;
  to_email: string;
};

const TextField = (props: { label?: string } & FieldHookConfig<string>) => {
  const [field, meta] = useField(props);
  return (
    <>
      {props.label && (
        <label
          htmlFor={props.name} // Ensure the label is correctly associated with the input
          className="maintenance-configure__label"
        >
          {props.label}
          {props.required && <span style={{ color: "red" }}>*</span>}
        </label>
      )}
      <Field
        {...field}
        {...props}
        id={props.name} // Add id to match the htmlFor attribute in the label
        type="text"
        className={`maintenance-configure__input${
          meta.touched && meta.error
            ? " maintenance-configure__input--error"
            : ""
        }`}
      />
      {meta.touched && meta.error ? (
        <div className="maintenance-configure__error">{meta.error}</div>
      ) : null}
    </>
  );
};

const renderUserSection = (
  title: string,
  userId: string,
  userEmail: string,
  applicationsList: UserApplication[],
  isInUnfFlow: boolean = false,
  hasMultipleSSN: boolean = false,
  selectable: boolean = false,
  handleSelection?: (
    e: React.ChangeEvent<HTMLInputElement>,
    applicationId: string | undefined,
  ) => void,
) => (
  <div
    style={{
      border: "1px solid #ccc",
      borderRadius: "8px",
      padding: "1rem",
      marginBottom: "2rem",
    }}
  >
    <div className="user-detail__panel">
      <h3>{title}</h3>
      <div className="user-detail__container" style={{ marginLeft: "1.5em" }}>
        <div className="user-detail__header">User ID:</div>
        <div>{userId}</div>
        <div className="user-detail__header">User Email:</div>
        <div>{userEmail}</div>
      </div>
      {isInUnfFlow && (
        <Alert type="warn" closeable={false}>
          {"An application for this user is in an User Not Found (UNF) Flow."}
        </Alert>
      )}
      {hasMultipleSSN && (
        <Alert type="warn" closeable={false}>
          {"Multiple SSNs found across this user's application"}
        </Alert>
      )}
    </div>
    <Table
      noResults={
        <p style={{ paddingLeft: "4em", paddingTop: "1em" }}>
          No applications found
        </p>
      }
      rows={applicationsList}
      cols={[
        ...(selectable
          ? [
              {
                title: "Select",
                align: "center" as "center",
                width: "50px",
                content: (row: UserApplication) => (
                  <input
                    type="checkbox"
                    onChange={(e) => handleSelection?.(e, row?.application_id)}
                    aria-label={`Select application ${row?.application_id}`}
                  />
                ),
              },
            ]
          : []),
        {
          title: "Application ID",
          align: "left",
          content: (row: UserApplication) => row?.application_id || "-",
        },
        {
          title: "Employee Status",
          align: "left",
          content: (row: UserApplication) =>
            !row?.employee_id ? (
              <Alert type="warn" closeable={false}>
                {"No employee found!"}
              </Alert>
            ) : (
              ""
            ),
        },
        {
          title: "Fineos Absence ID",
          align: "left",
          content: (row: UserApplication) => row?.fineos_absence_id || "-",
        },
        {
          title: "Imported From Fineos At",
          align: "left",
          content: (row: UserApplication) =>
            row?.imported_from_fineos_at
              ? formatDateTime(row.imported_from_fineos_at)
              : "-",
        },
        {
          title: "Created At",
          align: "left",
          content: (row: UserApplication) => formatDateTime(row?.created_at),
        },
        {
          title: "",
          align: "left",
          content: () => null,
        },
      ]}
    />
  </div>
);

export enum Statuses {
  initial,
  preview,
  final,
}

export default function ApplicationReassignment() {
  const router = useRouter();
  const [status, setStatus] = useState<Statuses>(Statuses.initial);
  const defaultFormValues: FormValues = {
    from_email: "",
    to_email: "",
  };
  const [responsePayload, setResponsePayload] = useState<
    ApplicationReassignmentPreviewResponse | undefined
  >(undefined);
  const [error, setError] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const [message, setMessage] = useState<string>("");
  const [selectedApplicationIds, setSelectedApplicationIds] = useState<
    string[]
  >([]);
  const [ticketNumber, setTicketNumber] = useState<string>("");
  const validate = (values: FormValues) => {
    const errors: FormikErrors<FormValues> = {};
    return errors;
  };

  function clearSessionVariables() {
    setError(false); // Clear previous error
    setSuccess(false); // Clear previous success message
    setStatus(Statuses.initial);
    setMessage("");
    setResponsePayload(undefined); // Reset response payload
    setSelectedApplicationIds([]); // Reset selected application IDs
    setTicketNumber("");
  }

  const onSubmitHandler = async (
    values: FormValues,
    actions: FormikHelpers<FormValues>,
  ) => {
    clearSessionVariables();

    if (
      values.from_email &&
      values.to_email &&
      values.from_email === values.to_email
    ) {
      setError(true);
      setMessage("Source and target email addresses cannot be the same.");
      return;
    }

    let response;

    try {
      response = await postAdminApplicationsReassignPreview({
        from_user_email: values.from_email,
        to_user_email: values.to_email,
      });

      setResponsePayload(response.data);
    } catch (error) {
      const errors =
        ((error as any).data?.errors as { field: string; message: string }[]) ??
        [];
      const errorMessages = errors.map((error) => error.message).join(",");
      setMessage(errorMessages);
      setError(true);
      return;
    }
    setStatus(Statuses.preview);
  };

  const handleReassignment = async () => {
    if (
      !responsePayload?.from_user?.user_id ||
      !responsePayload?.to_user?.user_id
    ) {
      setError(true);
      setMessage("An error occurred. Please refresh the page and try again.");
      return;
    }
    const applicationReassingmentRequest: ApplicationReassignmentRequest = {
      from_user_id: responsePayload.from_user.user_id,
      to_user_id: responsePayload.to_user.user_id,
      application_ids: selectedApplicationIds,
      ticket_num: ticketNumber,
    };

    await patchAdminApplicationsReassign(applicationReassingmentRequest)
      .then((response: ApiResponse<ApplicationReassignmentPreviewResponse>) => {
        updateUserStates(response.data);
        setStatus(Statuses.final);
        setSuccess(true);

        setMessage(
          `Following Application(s) successfully reassigned to the target user: [${selectedApplicationIds.join(", ")}]`,
        );
        setSelectedApplicationIds([]);
      })
      .catch(() => {
        setMessage(
          "Reassignment failed. Please refresh the page and try again",
        );
        setError(true);
      });
  };

  const updateUserStates = (
    data: ApplicationReassignmentPreviewResponse | undefined,
  ) => {
    if (!data) return;

    setResponsePayload(data);
  };

  const handleApplicationSelection = (
    e: React.ChangeEvent<HTMLInputElement>,
    applicationId: string | undefined,
  ) => {
    if (!applicationId) return;
    const isChecked = e.target.checked;

    if (isChecked) {
      // if checked, add the application Id to the selected list
      setSelectedApplicationIds((prev) => [...prev, applicationId]);
    } else {
      // if unchecked, remove the application Id from the selected list
      setSelectedApplicationIds((prev) =>
        prev.filter((id) => id !== applicationId),
      );
    }
  };

  return (
    <>
      <HelmetProvider>
        <title>Application Reassignment</title>
      </HelmetProvider>

      <h1>{router.query?.action || "Application Reassignment"}</h1>

      <div className="maintenance-configure">
        <div className="maintenance-configure__description">
          <h2>Steps:</h2>
          <ol>
            <li>
              Enter the source and target email addresses and click{" "}
              <strong>Preview</strong>.
            </li>
            <li>Select the applications to transfer from the source user.</li>
            <li>
              Click <strong>Confirm Reassignment</strong> to finalize the
              process.
            </li>
          </ol>
        </div>
        <Formik
          validate={validate}
          initialValues={defaultFormValues}
          onSubmit={onSubmitHandler}
        >
          {(props) => {
            return (
              <form
                className="maintenance-configure__form"
                onSubmit={props.handleSubmit}
                autoComplete="off"
              >
                <TextField
                  name="from_email"
                  label="Source Email Address"
                  required
                />
                <br />
                <br />
                <TextField
                  name="to_email"
                  label="Target Email Address"
                  required
                />
                <fieldset
                  className="maintenance-configure__fieldset maintenance-configure__buttons"
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "1rem",
                  }}
                >
                  {/* Preview Button */}
                  <div style={{ marginBottom: "1rem" }}>
                    <button
                      className="maintenance-configure__btn maintenance-configure__btn--submit btn"
                      type="submit"
                      disabled={props.isSubmitting}
                    >
                      Preview
                    </button>
                  </div>

                  {error && (
                    <Alert
                      type="error"
                      closeable={true}
                      onClose={() => setError(false)}
                    >
                      {message ||
                        "An error occurred while processing your request."}
                    </Alert>
                  )}

                  {success && (
                    <Alert
                      type="success"
                      closeable={true}
                      onClose={() => setSuccess(false)}
                    >
                      {message || "Reassignment preview successful."}
                    </Alert>
                  )}
                </fieldset>
              </form>
            );
          }}
        </Formik>

        <div>
          {(status == Statuses.preview || status == Statuses.final) &&
            responsePayload && (
              <>
                <div
                  className="reassignment-tables"
                  style={{ display: "flex", flexDirection: "row", gap: "2rem" }}
                >
                  {renderUserSection(
                    "Source User",
                    responsePayload.from_user.user_id ?? "",
                    responsePayload.from_user.email_address ?? "",
                    responsePayload.from_user?.applications ?? [],
                    responsePayload.from_user?.is_in_unf_flow ?? false,
                    false, // we ignore whether source user has multiple ssn in its applications
                    status == Statuses.preview,
                    handleApplicationSelection,
                  )}
                  {renderUserSection(
                    "Target User",
                    responsePayload.to_user.user_id ?? "",
                    responsePayload.to_user.email_address ?? "",
                    responsePayload.to_user?.applications ?? [],
                    responsePayload.to_user?.is_in_unf_flow ?? false,
                    responsePayload.to_user?.has_multiple_ssns ?? false,
                  )}
                </div>

                {status == Statuses.preview && (
                  <div>
                    <div
                      className="reassignment-button-container"
                      style={{ padding: "1.5rem" }}
                    >
                      <div className="user-detail__content">
                        <label
                          htmlFor="ticketNumber"
                          className="maintenance-configure__label"
                        >
                          Jira Ticket (ID or link)
                          <span style={{ color: "red" }}>*</span>
                        </label>
                        <input
                          type="text"
                          name="ticketNumber"
                          value={ticketNumber}
                          placeholder="[https://lwd.atlassian.net/browse/]PSD-12690"
                          onChange={(event) => {
                            setTicketNumber(event.target.value);
                          }}
                        />
                      </div>
                      <button
                        type="submit"
                        className="maintenance-configure__btn maintenance-configure__btn--submit btn"
                        onClick={() => {
                          handleReassignment();
                        }}
                        disabled={
                          selectedApplicationIds.length == 0 ||
                          ticketNumber.trim() === ""
                        }
                      >
                        Confirm Reassignment
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
        </div>
      </div>
    </>
  );
}
export async function getStaticProps(): Promise<StaticPropsPermissions> {
  return {
    props: {
      permissions: [],
    },
  };
}
