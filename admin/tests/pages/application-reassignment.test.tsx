import "@testing-library/jest-dom";

import React, { act } from "react";

import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import * as api from "../../src/_api";

import ApplicationReassignment from "../../src/pages/application-reassignment";

const mockRouteChangeHandler = jest.fn();
jest.mock("next/router", () => ({
  useRouter: () => ({
    push: mockRouteChangeHandler,
  }),
}));

const validPreviewResponse = {
  data: {
    from_user: {
      applications: [
        {
          application_id: "d5a0ff8f-19af-4e8e-9747-a995e95aa844",
          created_at: "2021-10-18T06:36:53.355283+00:00",
          employee_id: "10925599-4bca-47dd-b71e-de100d503b3d",
          fineos_absence_id: "NTN-2635-ABS-01",
          imported_from_fineos_at: "2025-02-01T00:00:00+00:00",
        },
        {
          application_id: "36e49034-43da-4ab6-afc7-d912b6e92f45",
          created_at: "2020-09-27T16:04:21.253587+00:00",
          employee_id: "b5ca3555-43c0-4cb0-b9be-7de49cc08e88",
          fineos_absence_id: "NTN-2366-ABS-01",
          imported_from_fineos_at: null,
        },
        {
          application_id: "2c33d391-e2b3-4adc-a588-f0314be90519",
          created_at: "2020-06-11T22:39:08.309798+00:00",
          employee_id: "10925599-4bca-47dd-b71e-de100d503b3d",
          fineos_absence_id: "NTN-3596-ABS-01",
          imported_from_fineos_at: null,
        },
      ],
      email_address:
        "<EMAIL>",
      has_multiple_ssns: false,
      is_in_unf_flow: false,
      user_id: "508710a4-bc70-434d-8a4e-3e12c26745c6",
    },
    to_user: {
      applications: [
        {
          application_id: "63a0ec63-c7b4-4538-9784-0193d7c4f97e",
          created_at: "2020-01-09T11:40:41.377021+00:00",
          employee_id: null,
          fineos_absence_id: "NTN-2045-ABS-01",
          imported_from_fineos_at: "2025-01-01T00:00:00+00:00",
        },
      ],
      email_address:
        "<EMAIL>",
      has_multiple_ssns: false,
      is_in_unf_flow: false,
      user_id: "9f25599f-b6d9-47c3-b3fb-00ad43511797",
    },
  },
  message: "Successfully retrieved applications for preview.",
  status_code: 200,
  meta: {
    method: "POST",
    resource: "/v1/admin/applications/reassign/preview",
  },
};

const userNotFoundErrorResponse = {
  data: {
    errors: [
      {
        field: "from_user_email",
        message: "User <NAME_EMAIL> does not exist.",
        type: "object_not_found",
      },
    ],
  },
};

//source user with no applications
const sourceUserNoApplicationErrorResponse = {
  data: {
    errors: [
      {
        field: "from_user_email",
        message:
          "No applications found for the user <NAME_EMAIL>.",
        type: "object_not_found",
      },
    ],
  },
};

const targetUserIsEmployerErrorResponse = {
  data: {
    errors: [
      {
        field: "from_user_email",
        message:
          "<NAME_EMAIL> is not an employee user.",
        type: "invalid",
      },
    ],
  },
};

const targetUserWithUnfResponse = {
  data: {
    from_user: {
      applications: [
        {
          application_id: "d5a0ff8f-19af-4e8e-9747-a995e95aa844",
          created_at: "2021-10-18T06:36:53.355283+00:00",
          employee_id: "10925599-4bca-47dd-b71e-de100d503b3d",
          fineos_absence_id: "NTN-2635-ABS-01",
          imported_from_fineos_at: "2025-02-01T00:00:00+00:00",
        },
      ],
      email_address:
        "<EMAIL>",
      has_multiple_ssns: false,
      is_in_unf_flow: false,
      user_id: "508710a4-bc70-434d-8a4e-3e12c26745c6",
    },
    to_user: {
      applications: [
        {
          application_id: "63a0ec63-c7b4-4538-9784-0193d7c4f97e",
          created_at: "2020-01-09T11:40:41.377021+00:00",
          employee_id: null,
          fineos_absence_id: "NTN-2045-ABS-01",
          imported_from_fineos_at: "2025-01-01T00:00:00+00:00",
        },
      ],
      email_address:
        "<EMAIL>",
      has_multiple_ssns: false,
      is_in_unf_flow: true,
      user_id: "9f25599f-b6d9-47c3-b3fb-00ad43511797",
    },
  },
  message: "Successfully retrieved applications for preview.",
  status_code: 200,
  meta: {
    method: "POST",
    resource: "/v1/admin/applications/reassign/preview",
  },
};

const targetUserWithMultipleSSNResponse = {
  data: {
    from_user: {
      applications: [
        {
          application_id: "d5a0ff8f-19af-4e8e-9747-a995e95aa844",
          created_at: "2021-10-18T06:36:53.355283+00:00",
          employee_id: "10925599-4bca-47dd-b71e-de100d503b3d",
          fineos_absence_id: "NTN-2635-ABS-01",
          imported_from_fineos_at: "2025-02-01T00:00:00+00:00",
        },
      ],
      email_address:
        "<EMAIL>",
      has_multiple_ssns: false,
      is_in_unf_flow: false,
      user_id: "508710a4-bc70-434d-8a4e-3e12c26745c6",
    },
    to_user: {
      applications: [
        {
          application_id: "63a0ec63-c7b4-4538-9784-0193d7c4f97e",
          created_at: "2020-01-09T11:40:41.377021+00:00",
          employee_id: null,
          fineos_absence_id: "NTN-2045-ABS-01",
          imported_from_fineos_at: "2025-01-01T00:00:00+00:00",
        },
      ],
      email_address:
        "<EMAIL>",
      has_multiple_ssns: true,
      is_in_unf_flow: false,
      user_id: "9f25599f-b6d9-47c3-b3fb-00ad43511797",
    },
  },
  message: "Successfully retrieved applications for preview.",
  status_code: 200,
  meta: {
    method: "POST",
    resource: "/v1/admin/applications/reassign/preview",
  },
};

const targetUserWithApplicationWithoutEmployeeResponse = {
  data: {
    from_user: {
      applications: [
        {
          application_id: "d5a0ff8f-19af-4e8e-9747-a995e95aa844",
          created_at: "2021-10-18T06:36:53.355283+00:00",
          employee_id: "10925599-4bca-47dd-b71e-de100d503b3d",
          fineos_absence_id: "NTN-2635-ABS-01",
          imported_from_fineos_at: "2025-02-01T00:00:00+00:00",
        },
      ],
      email_address:
        "<EMAIL>",
      has_multiple_ssns: false,
      is_in_unf_flow: false,
      user_id: "508710a4-bc70-434d-8a4e-3e12c26745c6",
    },
    to_user: {
      applications: [
        {
          application_id: "63a0ec63-c7b4-4538-9784-0193d7c4f97e",
          created_at: "2020-01-09T11:40:41.377021+00:00",
          employee_id: null,
          fineos_absence_id: "NTN-2045-ABS-01",
          imported_from_fineos_at: "2025-01-01T00:00:00+00:00",
        },
      ],
      email_address:
        "<EMAIL>",
      has_multiple_ssns: true,
      is_in_unf_flow: false,
      user_id: "9f25599f-b6d9-47c3-b3fb-00ad43511797",
    },
  },
  message: "Successfully retrieved applications for preview.",
  status_code: 200,
  meta: {
    method: "POST",
    resource: "/v1/admin/applications/reassign/preview",
  },
};

describe("ApplicationReassignment", () => {
  test("renders the page", () => {
    const { container } = render(<ApplicationReassignment />);

    // Verify key elements are present
    expect(
      screen.getByRole("heading", { name: /application reassignment/i }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: /preview/i }),
    ).toBeInTheDocument();

    expect(screen.getByLabelText(/Source Email Address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Target Email Address/i)).toBeInTheDocument();

    // Ensure snapshot matches the rendered component
    expect(container).toMatchSnapshot();
  });

  test("click preview button with no entry", async () => {
    render(<ApplicationReassignment />);

    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);
    const submitButton = screen.getByRole("button", { name: "Preview" });

    await userEvent.click(submitButton);

    expect(sourceInput).toBeInvalid();
    expect(targetInput).toBeInvalid();
  });

  test("click preview button with the same source and targe email", async () => {
    render(<ApplicationReassignment />);
    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);

    const email_address = "<EMAIL>";
    await userEvent.type(sourceInput, email_address);
    await userEvent.type(targetInput, email_address);
    const submitButton = screen.getByRole("button", { name: "Preview" });
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText(
          /Source and target email addresses cannot be the same/i,
        ),
      ).toBeInTheDocument();
    });
  });

  test("click preview button with source user not found", async () => {
    api.http.fetchJson = jest.fn().mockImplementation(() => {
      throw userNotFoundErrorResponse;
    });

    render(<ApplicationReassignment />);

    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);

    await userEvent.type(sourceInput, "<EMAIL>");
    await userEvent.type(targetInput, "<EMAIL>");
    const submitButton = screen.getByRole("button", { name: "Preview" });
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText(
          /User <NAME_EMAIL> does not exist./i,
        ),
      ).toBeInTheDocument();
    });
  });

  test("click preview button with source user with no applications", async () => {
    api.http.fetchJson = jest.fn().mockImplementation(() => {
      throw sourceUserNoApplicationErrorResponse;
    });

    render(<ApplicationReassignment />);

    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);

    await userEvent.type(sourceInput, "<EMAIL>");
    await userEvent.type(targetInput, "<EMAIL>");
    const submitButton = screen.getByRole("button", { name: "Preview" });
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText(
          /No applications found for the user <NAME_EMAIL>./i,
        ),
      ).toBeInTheDocument();
    });
  });

  test("click preview button where target email is an employer user", async () => {
    api.http.fetchJson = jest.fn().mockImplementation(() => {
      throw targetUserIsEmployerErrorResponse;
    });

    render(<ApplicationReassignment />);

    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);

    await userEvent.type(sourceInput, "<EMAIL>");
    await userEvent.type(targetInput, "<EMAIL>");
    const submitButton = screen.getByRole("button", { name: "Preview" });
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText(
          /<NAME_EMAIL> is not an employee user./i,
        ),
      ).toBeInTheDocument();
    });
  });

  test("click preview button with valid source and target email", async () => {
    api.http.fetchJson = jest.fn().mockResolvedValueOnce(validPreviewResponse);

    render(<ApplicationReassignment />);
    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);

    await userEvent.type(sourceInput, "<EMAIL>");
    await userEvent.type(targetInput, "<EMAIL>");
    const submitButton = screen.getByRole("button", { name: "Preview" });
    await userEvent.click(submitButton);

    // verify that the confirmation panel is displayed
    await waitFor(() => {
      const confirmButton = screen.getByRole("button", {
        name: /confirm reassignment/i,
      });
      expect(confirmButton).toBeInTheDocument();
      // the button is disabled since no applications are selected
      expect(confirmButton).toBeDisabled();

      // Validate "From User" (Source) panel - should have 3 applications
      // First application
      const sourceApplicationID1 = screen.getByText(
        /d5a0ff8f-19af-4e8e-9747-a995e95aa844/i,
      );
      expect(sourceApplicationID1).toBeInTheDocument();

      const sourceApplicationFineosAbsenceId1 = screen.getByRole("cell", {
        name: /ntn-2635-abs-01/i,
      });
      expect(sourceApplicationFineosAbsenceId1).toBeInTheDocument();

      const sourceApplicationFineosImportDate1 = screen.getByRole("cell", {
        name: /1\/31\/2025, 7:00:00 pm/i,
      });
      expect(sourceApplicationFineosImportDate1).toBeInTheDocument();

      const sourceApplicationCreatedAt1 = screen.getByRole("cell", {
        name: /10\/18\/2021, 2:36:53 am/i,
      });
      expect(sourceApplicationCreatedAt1).toBeInTheDocument();

      // Second application
      const sourceApplicationID2 = screen.getByText(
        /36e49034-43da-4ab6-afc7-d912b6e92f45/i,
      );
      expect(sourceApplicationID2).toBeInTheDocument();

      const sourceApplicationFineosAbsenceId2 = screen.getByRole("cell", {
        name: /ntn-2366-abs-01/i,
      });
      expect(sourceApplicationFineosAbsenceId2).toBeInTheDocument();

      // Third application
      const sourceApplicationID3 = screen.getByText(
        /2c33d391-e2b3-4adc-a588-f0314be90519/i,
      );
      expect(sourceApplicationID3).toBeInTheDocument();

      const sourceApplicationFineosAbsenceId3 = screen.getByRole("cell", {
        name: /ntn-3596-abs-01/i,
      });
      expect(sourceApplicationFineosAbsenceId3).toBeInTheDocument();

      // Validate "To User" (Target) panel - should have 1 application
      const targetApplicationID = screen.getByText(
        /63a0ec63-c7b4-4538-9784-0193d7c4f97e/i,
      );
      expect(targetApplicationID).toBeInTheDocument();

      const targetApplicationFineosAbsenceId = screen.getByRole("cell", {
        name: /ntn-2045-abs-01/i,
      });
      expect(targetApplicationFineosAbsenceId).toBeInTheDocument();

      const targetApplicationFineosImportDate = screen.getByRole("cell", {
        name: /12\/31\/2024, 7:00:00 pm/i,
      });
      expect(targetApplicationFineosImportDate).toBeInTheDocument();

      const targetApplicationCreatedAt = screen.getByRole("cell", {
        name: /1\/9\/2020, 6:40:41 am/i,
      });
      expect(targetApplicationCreatedAt).toBeInTheDocument();
    });
  });

  test("click preview button where target user is in UNF flow", async () => {
    api.http.fetchJson = jest
      .fn()
      .mockResolvedValueOnce(targetUserWithUnfResponse);

    render(<ApplicationReassignment />);
    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);

    await userEvent.type(sourceInput, "<EMAIL>");
    await userEvent.type(targetInput, "<EMAIL>");
    const submitButton = screen.getByRole("button", { name: "Preview" });
    await userEvent.click(submitButton);

    await waitFor(() => {
      const unfWarning = screen.getByText(
        /an application for this user is in an user not found \(unf\) flow\./i,
      );
      expect(unfWarning).toBeInTheDocument();
    });
  });

  test("click preview button where target user has applications with multiple SSN", async () => {
    api.http.fetchJson = jest
      .fn()
      .mockResolvedValueOnce(targetUserWithMultipleSSNResponse);

    render(<ApplicationReassignment />);
    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);

    await userEvent.type(sourceInput, "<EMAIL>");
    await userEvent.type(targetInput, "<EMAIL>");
    const submitButton = screen.getByRole("button", { name: "Preview" });
    await userEvent.click(submitButton);

    await waitFor(() => {
      const multipleSSNWarning = screen.getByText(
        /multiple ssns found across this user's application/i,
      );
      expect(multipleSSNWarning).toBeInTheDocument();
    });
  });

  test("click preview button where target user has applications with no employee attached", async () => {
    api.http.fetchJson = jest
      .fn()
      .mockResolvedValueOnce(targetUserWithApplicationWithoutEmployeeResponse);

    render(<ApplicationReassignment />);
    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);

    await userEvent.type(sourceInput, "<EMAIL>");
    await userEvent.type(targetInput, "<EMAIL>");
    const submitButton = screen.getByRole("button", { name: "Preview" });
    await userEvent.click(submitButton);
    await waitFor(() => {
      const multipleSSNWarning = screen.getByText(/no employee found!/i);
      expect(multipleSSNWarning).toBeInTheDocument();
    });
  });

  test("selecting an application enables the confirm button", async () => {
    api.http.fetchJson = jest.fn().mockResolvedValueOnce(validPreviewResponse);

    render(<ApplicationReassignment />);
    const sourceInput = screen.getByLabelText(/Source Email Address/i);
    const targetInput = screen.getByLabelText(/Target Email Address/i);

    await userEvent.type(sourceInput, "<EMAIL>");
    await userEvent.type(targetInput, "<EMAIL>");
    const submitButton = screen.getByRole("button", { name: "Preview" });
    await userEvent.click(submitButton);

    const sourceApplicationID1 = screen.getByRole("checkbox", {
      name: /select application d5a0ff8f\-19af\-4e8e\-9747\-a995e95aa844/i, //d5a0ff8f-19af-4e8e-9747-a995e95aa844
    });
    userEvent.click(sourceApplicationID1);

    await waitFor(() => {
      expect(
        screen.getByRole("button", { name: /confirm reassignment/i }),
      ).toBeInTheDocument();
    });

    // verify button is initially disabled
    const confirmButton = screen.getByRole("button", {
      name: /confirm reassignment/i,
    });
    expect(confirmButton).toBeDisabled();

    const checkbox = screen.getByRole("checkbox", {
      name: /select application d5a0ff8f\-19af\-4e8e\-9747\-a995e95aa844/i,
    }) as HTMLInputElement;

    // Use await for the click event
    await fireEvent.click(checkbox);

    // validate button is still disabled. Jira ticket number is also required
    expect(confirmButton).toBeDisabled();

    const jiraTicketInput = screen.getByPlaceholderText(
      "[https://lwd.atlassian.net/browse/]PSD-12690",
    );

    fireEvent.change(jiraTicketInput, { target: { value: "PSD-12345" } });

    // validate button is enabled after click
    await waitFor(() => {
      expect(
        screen.getByRole("button", { name: /confirm reassignment/i }),
      ).toBeEnabled();
    });
  });
});
