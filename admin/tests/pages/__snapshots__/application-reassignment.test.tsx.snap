// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ApplicationReassignment renders the page 1`] = `
<div>
  <h1>
    Application Reassignment
  </h1>
  <div
    class="maintenance-configure"
  >
    <div
      class="maintenance-configure__description"
    >
      <h2>
        Steps:
      </h2>
      <ol>
        <li>
          Enter the source and target email addresses and click
           
          <strong>
            Preview
          </strong>
          .
        </li>
        <li>
          Select the applications to transfer from the source user.
        </li>
        <li>
          Click 
          <strong>
            Confirm Reassignment
          </strong>
           to finalize the process.
        </li>
      </ol>
    </div>
    <form
      autocomplete="off"
      class="maintenance-configure__form"
    >
      <label
        class="maintenance-configure__label"
        for="from_email"
      >
        Source Email Address
        <span
          style="color: red;"
        >
          *
        </span>
      </label>
      <input
        class="maintenance-configure__input"
        id="from_email"
        label="Source Email Address"
        name="from_email"
        required=""
        type="text"
        value=""
      />
      <br />
      <br />
      <label
        class="maintenance-configure__label"
        for="to_email"
      >
        Target Email Address
        <span
          style="color: red;"
        >
          *
        </span>
      </label>
      <input
        class="maintenance-configure__input"
        id="to_email"
        label="Target Email Address"
        name="to_email"
        required=""
        type="text"
        value=""
      />
      <fieldset
        class="maintenance-configure__fieldset maintenance-configure__buttons"
        style="display: flex; flex-direction: column; gap: 1rem;"
      >
        <div
          style="margin-bottom: 1rem;"
        >
          <button
            class="maintenance-configure__btn maintenance-configure__btn--submit btn"
            type="submit"
          >
            Preview
          </button>
        </div>
      </fieldset>
    </form>
    <div />
  </div>
</div>
`;
