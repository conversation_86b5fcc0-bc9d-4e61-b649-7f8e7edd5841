// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Sidebar renders the default component 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <aside
        class="page__sidebar"
        tabindex="0"
      >
        <nav
          class="menu"
        >
          <ul
            class="menu__list"
          >
            <li
              class="menu__list-item"
            >
              <a
                class="menu__link menu__link--address"
                data-testid="address-navigation-link"
                href="/address"
              >
                <svg
                  aria-hidden="true"
                  class="menu__link-icon menu__link-icon--address"
                  data-slot="icon"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M11.47 3.841a.75.75 0 0 1 1.06 0l8.69 8.69a.75.75 0 1 0 1.06-1.061l-8.689-8.69a2.25 2.25 0 0 0-3.182 0l-8.69 8.69a.75.75 0 1 0 1.061 1.06l8.69-8.689Z"
                  />
                  <path
                    d="m12 5.432 8.159 8.159c.************.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0-.75.75V21a.75.75 0 0 1-.75.75H5.625a1.875 1.875 0 0 1-1.875-1.875v-6.198a2.29 2.29 0 0 0 .091-.086L12 5.432Z"
                  />
                </svg>
                <span>
                  Address Lookup
                </span>
              </a>
            </li>
            <li
              class="menu__list-item"
            >
              <a
                class="menu__link menu__link--applicationReassignment"
                data-testid="applicationReassignment-navigation-link"
                href="/application-reassignment"
              >
                <svg
                  aria-hidden="true"
                  class="menu__link-icon menu__link-icon--applicationReassignment"
                  data-slot="icon"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M7.5 3.75A1.5 1.5 0 0 0 6 5.25v13.5a1.5 1.5 0 0 0 1.5 1.5h6a1.5 1.5 0 0 0 1.5-1.5V15a.75.75 0 0 1 1.5 0v3.75a3 3 0 0 1-3 3h-6a3 3 0 0 1-3-3V5.25a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3V9A.75.75 0 0 1 15 9V5.25a1.5 1.5 0 0 0-1.5-1.5h-6Zm10.72 4.72a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1 0 1.06l-3 3a.75.75 0 1 1-1.06-1.06l1.72-1.72H9a.75.75 0 0 1 0-1.5h10.94l-1.72-1.72a.75.75 0 0 1 0-1.06Z"
                    fill-rule="evenodd"
                  />
                </svg>
                <span>
                  Application Reassignment
                </span>
              </a>
            </li>
            <li
              class="menu__list-item"
            >
              <a
                class="menu__link menu__link--omniSearch"
                data-testid="omniSearch-navigation-link"
                href="/omni-search"
              >
                <svg
                  aria-hidden="true"
                  class="menu__link-icon menu__link-icon--omniSearch"
                  data-slot="icon"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10.5 3.75a6.75 6.75 0 1 0 0 13.5 6.75 6.75 0 0 0 0-13.5ZM2.25 10.5a8.25 8.25 0 1 1 14.59 5.28l4.69 4.69a.75.75 0 1 1-1.06 1.06l-4.69-4.69A8.25 8.25 0 0 1 2.25 10.5Z"
                    fill-rule="evenodd"
                  />
                </svg>
                <span>
                  Omni Search
                </span>
              </a>
            </li>
          </ul>
        </nav>
        <div
          class="settings"
        >
          <ul
            class="settings__list"
          />
        </div>
        <div
          class="environment"
        >
          <div
            class="environment__label"
          >
            Environment
          </div>
          <div
            class="environment__flag"
            data-testid="environment-flag"
          >
            Production
          </div>
        </div>
      </aside>
    </div>
  </body>,
  "container": <div>
    <aside
      class="page__sidebar"
      tabindex="0"
    >
      <nav
        class="menu"
      >
        <ul
          class="menu__list"
        >
          <li
            class="menu__list-item"
          >
            <a
              class="menu__link menu__link--address"
              data-testid="address-navigation-link"
              href="/address"
            >
              <svg
                aria-hidden="true"
                class="menu__link-icon menu__link-icon--address"
                data-slot="icon"
                fill="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M11.47 3.841a.75.75 0 0 1 1.06 0l8.69 8.69a.75.75 0 1 0 1.06-1.061l-8.689-8.69a2.25 2.25 0 0 0-3.182 0l-8.69 8.69a.75.75 0 1 0 1.061 1.06l8.69-8.689Z"
                />
                <path
                  d="m12 5.432 8.159 8.159c.************.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0-.75.75V21a.75.75 0 0 1-.75.75H5.625a1.875 1.875 0 0 1-1.875-1.875v-6.198a2.29 2.29 0 0 0 .091-.086L12 5.432Z"
                />
              </svg>
              <span>
                Address Lookup
              </span>
            </a>
          </li>
          <li
            class="menu__list-item"
          >
            <a
              class="menu__link menu__link--applicationReassignment"
              data-testid="applicationReassignment-navigation-link"
              href="/application-reassignment"
            >
              <svg
                aria-hidden="true"
                class="menu__link-icon menu__link-icon--applicationReassignment"
                data-slot="icon"
                fill="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M7.5 3.75A1.5 1.5 0 0 0 6 5.25v13.5a1.5 1.5 0 0 0 1.5 1.5h6a1.5 1.5 0 0 0 1.5-1.5V15a.75.75 0 0 1 1.5 0v3.75a3 3 0 0 1-3 3h-6a3 3 0 0 1-3-3V5.25a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3V9A.75.75 0 0 1 15 9V5.25a1.5 1.5 0 0 0-1.5-1.5h-6Zm10.72 4.72a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1 0 1.06l-3 3a.75.75 0 1 1-1.06-1.06l1.72-1.72H9a.75.75 0 0 1 0-1.5h10.94l-1.72-1.72a.75.75 0 0 1 0-1.06Z"
                  fill-rule="evenodd"
                />
              </svg>
              <span>
                Application Reassignment
              </span>
            </a>
          </li>
          <li
            class="menu__list-item"
          >
            <a
              class="menu__link menu__link--omniSearch"
              data-testid="omniSearch-navigation-link"
              href="/omni-search"
            >
              <svg
                aria-hidden="true"
                class="menu__link-icon menu__link-icon--omniSearch"
                data-slot="icon"
                fill="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10.5 3.75a6.75 6.75 0 1 0 0 13.5 6.75 6.75 0 0 0 0-13.5ZM2.25 10.5a8.25 8.25 0 1 1 14.59 5.28l4.69 4.69a.75.75 0 1 1-1.06 1.06l-4.69-4.69A8.25 8.25 0 0 1 2.25 10.5Z"
                  fill-rule="evenodd"
                />
              </svg>
              <span>
                Omni Search
              </span>
            </a>
          </li>
        </ul>
      </nav>
      <div
        class="settings"
      >
        <ul
          class="settings__list"
        />
      </div>
      <div
        class="environment"
      >
        <div
          class="environment__label"
        >
          Environment
        </div>
        <div
          class="environment__flag"
          data-testid="environment-flag"
        >
          Production
        </div>
      </div>
    </aside>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
