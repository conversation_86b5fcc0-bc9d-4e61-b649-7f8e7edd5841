# FINEOS_CLIENT_OAUTH2_CLIENT_SECRET=replace_me_with_client_secret
# FINEOS_CLIENT_SOAP_PASSWORD=replace_me_with_soap_password

# EXPERIAN_AUTH_TOKEN=replace_me_with_auth_token

##########  Admin Portal ######################################################
## Non-prod (use TST2 value)
# https://us-east-1.console.aws.amazon.com/systems-manager/parameters/%252Fservice%252Fpfml-api%252Ftst2%252Fazure_ad_client_secret/description?region=us-east-1&tab=Table#list_parameter_filters=Name:Contains:azure_ad_client_secret
# AZURE_AD_CLIENT_SECRET=replace_with_client_secret
################################################################################


##########  MMG vars for running local-local ###################################
#   PORTAL_BASE_URL=http://localhost:3000
#   ENABLE_LMG_AUTH=1

# Get the OAuth client secrets for the below vars from Concluence doc
# https://lwd.atlassian.net/wiki/spaces/DD/pages/3222274087#Non-production

## Non-prod - Personal
# Get from https://us-east-1.console.aws.amazon.com/systems-manager/parameters/%252Fservice%252Fpfml-api%252Fnon_prod%252Flmg_personal%252Fmfa_off%252Fclient_secret/description?region=us-east-1&tab=Table
#   LMG_PERSONAL_OAUTH_CLIENT_SECRET=replace_me_with_personal_oauth_client_secret
## Non-prod - Business
# Get from https://us-east-1.console.aws.amazon.com/systems-manager/parameters/%252Fservice%252Fpfml-api%252Fnon_prod%252Flmg_business%252Fmfa_off%252Fclient_secret/description?region=us-east-1&tab=Table
#   LMG_BUSINESS_OAUTH_CLIENT_SECRET=replace_me_with_business_oauth_client_secret
################################################################################


##########  Unit test table dump  ##############################################
# Change the unit test table dump behavior
# Comma-separated list of tables to include in the unit test table dump.
#   Suppress everything:
#       UNIT_TEST_TABLE_DUMP_INCLUDE_LIST=x
#   Include specific tables ():
#       UNIT_TEST_TABLE_DUMP_INCLUDE_LIST=unemployment_metric,benefits_metrics
################################################################################


##########  Adjusting DB logging output  #######################################
# DB_ECHO_STATEMENTS=True
# DB_HIDE_SQL_PARAMETER_LOGS=False
################################################################################