from unittest import mock

from freezegun import freeze_time

import massgov.pfml.util.dynamic_config as dynamic_config_util


def test_db_load(mock_dynamic_config_entries, test_db_session):
    result = dynamic_config_util._load_config_from_db(test_db_session)
    assert len(result) == len(mock_dynamic_config_entries)


@mock.patch("massgov.pfml.util.dynamic_config._load_config_from_db")
def test_get_list_calls_db_load(mock_load_config, mock_dynamic_config_entries, test_db_session):
    dynamic_config_util._get_dynamic_config_list(test_db_session)
    assert mock_load_config.called


@mock.patch("massgov.pfml.util.dynamic_config._load_config_from_db")
def test_get_list_calls_db_load_once_per_cache_period(
    mock_load_config, mock_dynamic_config_entries, test_db_session
):
    mock_load_config.return_value = mock_dynamic_config_entries

    with freeze_time("1947-07-30 12:00:00"):
        dynamic_config_util._get_dynamic_config_list(test_db_session)
        assert mock_load_config.called

    mock_load_config.reset_mock()

    # Call again within the cache period
    with freeze_time("1947-07-30 12:01:00"):
        dynamic_config_util._get_dynamic_config_list(test_db_session)
        # Should not call the DB again
        assert not mock_load_config.called


@mock.patch("massgov.pfml.util.dynamic_config._load_config_from_db")
def test_get_list_calls_db_load_again_after_cache_expires(
    mock_load_config, mock_dynamic_config_entries, test_db_session
):
    mock_load_config.return_value = mock_dynamic_config_entries

    with freeze_time("1947-07-30 12:00:00"):
        dynamic_config_util._get_dynamic_config_list(test_db_session)
        assert mock_load_config.called

    mock_load_config.reset_mock()

    # Call again after the cache period has expired
    with freeze_time(
        f"1947-07-30 12:0{dynamic_config_util.DYNAMIC_CONFIG_CACHE_EXPIRY_MINUTES + 1}:00"
    ):
        dynamic_config_util._get_dynamic_config_list(test_db_session)
        # Should call the DB again
        assert mock_load_config.called

    mock_load_config.reset_mock()

    with freeze_time(
        f"1947-07-30 12:0{dynamic_config_util.DYNAMIC_CONFIG_CACHE_EXPIRY_MINUTES + 2}:00"
    ):
        dynamic_config_util._get_dynamic_config_list(test_db_session)
        # Should not call the DB again after reload within the cache period
        assert not mock_load_config.called


def test_get_value_for_key_no_context(mock_dynamic_config_entries, test_db_session):
    result = dynamic_config_util.get_dynamic_config_value(
        db_session=test_db_session, config_key="foo_key"
    )
    assert result == "foo_context_blank_val"


def test_get_value_for_context(mock_dynamic_config_entries, test_db_session):
    result = dynamic_config_util.get_dynamic_config_value(
        db_session=test_db_session, config_key="foo_key", config_context="foo_context_1"
    )
    assert result == "foo_val_1"


def test_get_value_for_key_fallback(mock_dynamic_config_entries, test_db_session):
    result = dynamic_config_util.get_dynamic_config_value(
        db_session=test_db_session, config_key="foo_key", config_context="foo_context_no_match"
    )
    assert result == "foo_context_blank_val"


def test_get_value_for_default_fallback(mock_dynamic_config_entries, test_db_session):
    result = dynamic_config_util.get_dynamic_config_value(
        db_session=test_db_session,
        config_key="non_existent_key",
        config_context=None,
        default="default_value",
    )
    assert result == "default_value"


def test_get_value_accepts_none_context(mock_dynamic_config_entries, test_db_session):
    result = dynamic_config_util.get_dynamic_config_value(
        db_session=test_db_session, config_key="foo_key", config_context=None
    )
    assert result == "foo_context_blank_val"


def test_get_value_accepts_empty_string_context(mock_dynamic_config_entries, test_db_session):
    result = dynamic_config_util.get_dynamic_config_value(
        db_session=test_db_session, config_key="foo_key", config_context=""
    )
    assert result == "foo_context_blank_val"


def test_get_value_accepts_whitespace_context(mock_dynamic_config_entries, test_db_session):
    # this works without any special handling in the code,
    # as it treats whitespace as no conext match, so it defaults to the blank context value
    result = dynamic_config_util.get_dynamic_config_value(
        db_session=test_db_session, config_key="foo_key", config_context="   "
    )
    assert result == "foo_context_blank_val"
