import datetime
from unittest.mock import PropertyMock, patch

import pytest

from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.models.factories import ClaimFactory, NotificationFactory
from massgov.pfml.notifications.query_applications_for_rfi_renotification import (
    QueryApplicationsForRFIReNotificationStep,
)
from tests.helpers.assertions import assert_metrics


# Run `initialize_factories_session` for all tests,
# so that it doesn't need to be manually included
@pytest.fixture(autouse=True)
def setup_factories(initialize_factories_session):
    return


@pytest.fixture(autouse=True)
def query_applications_for_rfi_renotification(
    test_db_session,
) -> QueryApplicationsForRFIReNotificationStep:
    return QueryApplicationsForRFIReNotificationStep(
        db_session=test_db_session, log_entry_db_session=test_db_session
    )


@pytest.fixture
def recent_notification_with_rfi():
    notification = NotificationFactory.create()
    return notification


@pytest.fixture
def older_notification_with_rfi():
    notification = NotificationFactory.create(
        created_at=datetime.datetime.now() - datetime.timedelta(days=31)
    )
    return notification


@pytest.fixture
def recent_notification_without_rfi():
    notification = NotificationFactory.create(
        trigger="Employer Confirmation of Leave Data",
    )
    return notification


@pytest.fixture
def notifications_with_the_same_absence_id():
    claim = ClaimFactory.create()
    notification1 = NotificationFactory.create(
        fineos_absence_id=claim.fineos_absence_id,
        created_at=datetime.datetime.now() - datetime.timedelta(days=5),
    )
    notification2 = NotificationFactory.create(
        fineos_absence_id=claim.fineos_absence_id,
        created_at=datetime.datetime.now() - datetime.timedelta(days=10),
    )
    return [notification1, notification2]


def test_get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days(
    test_db_session,
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    recent_notification_with_rfi: NotificationFactory,
    older_notification_with_rfi: NotificationFactory,
    recent_notification_without_rfi: NotificationFactory,
):
    test_db_session.add(recent_notification_with_rfi)
    test_db_session.add(older_notification_with_rfi)
    test_db_session.add(recent_notification_without_rfi)
    test_db_session.commit()

    notifications = (
        query_applications_for_rfi_renotification.get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days()
    )

    assert len(notifications) == 1
    assert notifications[0].notification_id == recent_notification_with_rfi.notification_id


def test_get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days_with_the_same_absence_id(
    test_db_session,
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    notifications_with_the_same_absence_id: list[NotificationFactory],
):
    test_db_session.add(*notifications_with_the_same_absence_id)
    test_db_session.commit()

    notifications = (
        query_applications_for_rfi_renotification.get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days()
    )
    assert len(notifications) == 1
    assert (
        notifications[0].notification_id
        == notifications_with_the_same_absence_id[1].notification_id
    )


def test_get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days_and_none_found(
    test_db_session,
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    older_notification_with_rfi: NotificationFactory,
    recent_notification_without_rfi: NotificationFactory,
):
    test_db_session.add(older_notification_with_rfi)
    test_db_session.add(recent_notification_without_rfi)
    test_db_session.commit()

    notifications = (
        query_applications_for_rfi_renotification.get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days()
    )

    assert len(notifications) == 0


def test_get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days_with_simulated_error(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    caplog,
):
    with patch(
        "massgov.pfml.db.Session.query",
        side_effect=Exception("Simulated Error"),
    ):
        with caplog.at_level("ERROR"):
            with pytest.raises(Exception, match="Simulated Error"):
                query_applications_for_rfi_renotification.get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days()

    assert any(
        "Unable to get notifications: Simulated Error" in message and record.levelname == "ERROR"
        for record, message in zip(caplog.records, caplog.messages)
    )


@pytest.mark.parametrize(
    "created_at, last_notified_at",
    [
        (9, None),
        (14, 5),
        (24, 10),
    ],
)
def test_filter_out_recently_notified_claims_with_valid_requirements(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    created_at: int,
    last_notified_at: int | None,
):
    today = datetime.datetime.now()
    notification = NotificationFactory.create(
        created_at=today - datetime.timedelta(days=created_at),
        last_notified_at=(
            today - datetime.timedelta(days=last_notified_at) if last_notified_at else None
        ),
    )

    applications = query_applications_for_rfi_renotification.filter_out_recently_notified_claims(
        [notification]
    )

    assert len(applications) == 1


@pytest.mark.parametrize(
    "created_at, last_notified_at",
    [
        (0, None),
        (1, None),
        (2, None),
        (3, None),
        (4, None),
        (5, None),
        (6, None),
        (7, None),
        (8, None),
        (10, 1),
        (11, 2),
        (12, 3),
        (13, 4),
        (15, 1),
        (16, 2),
        (17, 3),
        (18, 4),
        (19, 5),
        (20, 6),
        (21, 7),
        (22, 8),
        (23, 9),
        (25, 1),
        (26, 2),
        (27, 3),
        (28, 4),
        (29, 5),
        (30, 6),
    ],
)
def test_filter_out_recently_notified_claims_with_invalid_requirements(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    created_at: int,
    last_notified_at: int | None,
):
    today = datetime.datetime.now()
    notification = NotificationFactory.create(
        created_at=today - datetime.timedelta(days=created_at),
        last_notified_at=(
            today - datetime.timedelta(days=last_notified_at) if last_notified_at else None
        ),
    )

    applications = query_applications_for_rfi_renotification.filter_out_recently_notified_claims(
        [notification]
    )

    assert len(applications) == 0


@pytest.mark.parametrize(
    "created_at, last_notified_at",
    [
        (10, None),
        (11, None),
        (15, 6),
        (16, 7),
        (25, 11),
        (26, 12),
    ],
)
def test_filter_out_recently_notified_claims_with_self_healing_logic(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    created_at: int,
    last_notified_at: int | None,
):
    today = datetime.datetime.now()
    notification = NotificationFactory.create(
        created_at=today - datetime.timedelta(days=created_at),
        last_notified_at=(
            today - datetime.timedelta(days=last_notified_at) if last_notified_at else None
        ),
    )

    applications = query_applications_for_rfi_renotification.filter_out_recently_notified_claims(
        [notification]
    )

    assert len(applications) == 1


@pytest.mark.parametrize(
    "created_at, last_notified_at",
    [(14, 4), (14, 3), (24, 9), (24, 8)],
)
def test_filter_out_recently_notifed_claims_after_self_healing_logic_is_triggered(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    created_at: int,
    last_notified_at: int | None,
):
    today = datetime.datetime.now()
    notification = NotificationFactory.create(
        created_at=today - datetime.timedelta(days=created_at),
        last_notified_at=(
            today - datetime.timedelta(days=last_notified_at) if last_notified_at else None
        ),
    )

    applications = query_applications_for_rfi_renotification.filter_out_recently_notified_claims(
        [notification]
    )

    assert len(applications) == 1


def test_filter_out_recently_notified_claims_with_simulated_error(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    recent_notification_with_rfi: NotificationFactory,
    caplog,
):
    with patch.object(
        type(recent_notification_with_rfi),
        "created_at",
        new_callable=PropertyMock,
        side_effect=Exception("Simulated Error"),
    ):
        with caplog.at_level("ERROR"):
            with pytest.raises(Exception, match="Simulated Error"):
                query_applications_for_rfi_renotification.filter_out_recently_notified_claims(
                    [recent_notification_with_rfi]
                )

    assert any(
        "Unable to remove recently notified claims: Simulated Error" in message
        and record.levelname == "ERROR"
        for record, message in zip(caplog.records, caplog.messages)
    )


def test_get_claims_by_fineos_absence_ids_with_an_existing_claim(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    test_db_session,
):
    claim = ClaimFactory.create()
    notification = NotificationFactory.create(fineos_absence_id=claim.fineos_absence_id)
    test_db_session.add(claim)
    test_db_session.add(notification)
    test_db_session.commit()

    claims = query_applications_for_rfi_renotification.get_claims_by_fineos_absence_ids(
        [claim.fineos_absence_id]
    )

    assert len(claims) == 1
    assert claims[claim.fineos_absence_id] == claim


def test_get_claims_by_fineos_absence_ids_with_a_non_existing_claim(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
):
    claims = query_applications_for_rfi_renotification.get_claims_by_fineos_absence_ids(
        ["some_absence_id"]
    )

    assert len(claims) == 0


def test_get_claims_by_fineos_absence_ids_with_a_simulated_error(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep, caplog
):
    with patch(
        "massgov.pfml.db.Session.query",
        side_effect=Exception("Simulated Error"),
    ):
        with caplog.at_level("ERROR"):
            with pytest.raises(Exception, match="Simulated Error"):
                query_applications_for_rfi_renotification.get_claims_by_fineos_absence_ids(
                    ["some_absence_id"]
                )

    assert any(
        record.levelname == "ERROR"
        and "Unable to retrieve claims associated to a notification: Simulated Error"
        in record.message
        for record in caplog.records
    )


@pytest.mark.parametrize(
    "fineos_absence_status_id",
    [
        AbsenceStatus.ADJUDICATION.absence_status_id,
        AbsenceStatus.IN_REVIEW.absence_status_id,
        AbsenceStatus.INTAKE_IN_PROGRESS.absence_status_id,
    ],
)
def test_filter_for_notifications_with_an_active_claim_status_with_a_valid_status(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    fineos_absence_status_id: int,
):
    claim = ClaimFactory.create(fineos_absence_status_id=fineos_absence_status_id)
    notification = NotificationFactory.create(fineos_absence_id=claim.fineos_absence_id)

    notifications = query_applications_for_rfi_renotification.filter_for_notifications_with_an_active_claim_status(
        [notification], {claim.fineos_absence_id: claim}
    )

    assert len(notifications) == 1


@pytest.mark.parametrize(
    "fineos_absence_status_id",
    [
        AbsenceStatus.APPROVED.absence_status_id,
        AbsenceStatus.CLOSED.absence_status_id,
        AbsenceStatus.COMPLETED.absence_status_id,
        AbsenceStatus.DECLINED.absence_status_id,
    ],
)
def test_filter_for_notifications_with_an_active_claim_status_with_an_invalid_status(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    fineos_absence_status_id: int,
    caplog,
):
    claim = ClaimFactory.create(fineos_absence_status_id=fineos_absence_status_id)
    notification = NotificationFactory.create(fineos_absence_id=claim.fineos_absence_id)

    with caplog.at_level("WARNING"):
        notifications = query_applications_for_rfi_renotification.filter_for_notifications_with_an_active_claim_status(
            [notification], {claim.fineos_absence_id: claim}
        )

    assert len(notifications) == 0
    assert any(
        f"Inactive claim found for notification: {notification.notification_id}" in message
        and record.levelname == "WARNING"
        for record, message in zip(caplog.records, caplog.messages)
    )


def test_filter_for_notifications_with_an_active_claim_status_with_no_claim(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    caplog,
):
    claim = ClaimFactory.create()
    notification = NotificationFactory.create(fineos_absence_id="some_absence_id")

    with caplog.at_level("WARNING"):
        notifications = query_applications_for_rfi_renotification.filter_for_notifications_with_an_active_claim_status(
            [notification], {claim.fineos_absence_id: claim}
        )

    assert len(notifications) == 0
    assert any(
        f"No claim found for notification: {notification.notification_id}" in message
        and record.levelname == "WARNING"
        for record, message in zip(caplog.records, caplog.messages)
    )


@pytest.mark.parametrize(
    ("created_at", "days_remaining"),
    [
        (10, 20),
        (15, 15),
        (25, 5),
    ],
)
def test_calculate_notification_deadlines(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    created_at: int,
    days_remaining: int,
):
    today = datetime.datetime.now()
    notification = NotificationFactory.create(
        created_at=today - datetime.timedelta(days=created_at),
    )

    notification_with_deadline = (
        query_applications_for_rfi_renotification.calculate_notification_deadlines([notification])
    )
    returned_notification, returned_days_remaining = notification_with_deadline[0]

    assert returned_notification.fineos_absence_id == notification.fineos_absence_id
    assert returned_days_remaining == days_remaining


def test_calculate_notification_deadlines_with_simulated_error(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    recent_notification_with_rfi: NotificationFactory,
    caplog,
):
    with patch.object(
        type(recent_notification_with_rfi),
        "created_at",
        new_callable=PropertyMock,
        side_effect=Exception("Simulated Error"),
    ):
        with caplog.at_level("ERROR"):
            with pytest.raises(Exception, match="Simulated Error"):
                query_applications_for_rfi_renotification.calculate_notification_deadlines(
                    [recent_notification_with_rfi]
                )

    assert any(
        "Unable to calculate the notification deadlines: Simulated Error" in message
        and record.levelname == "ERROR"
        and getattr(record, "status", None) == "Failed"
        for record, message in zip(caplog.records, caplog.messages)
    )


def test_run_with_no_rfi_records_found(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    caplog,
):
    with caplog.at_level("INFO"):
        query_applications_for_rfi_renotification.run()

    assert_metrics(
        query_applications_for_rfi_renotification,
        {
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_RFI_NOTIFICATION_COUNT_IN_LAST_30_DAYS: 0,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_DUE_FOR_RENOTIFICATION: 0,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_CLAIMS_BY_ABSENCE_ID: 0,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_WITH_ACTIVE_CLAIMS_READY_FOR_RENOTIFICATION: 0,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_WITH_CALCULATED_DEADLINES: 0,
        },
    )

    assert any(
        record.levelname == "INFO"
        and record.message == "No RFI records found in the last 30 days"
        and getattr(record, "status", None) == "Completed"
        for record in caplog.records
    )


def test_run_with_no_records_ready_for_renotification(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    test_db_session,
    caplog,
):
    today = datetime.datetime.now()
    notification = NotificationFactory.create(
        last_notified_at=(today - datetime.timedelta(days=5)),
    )
    test_db_session.add(notification)
    test_db_session.commit()

    with caplog.at_level("INFO"):
        query_applications_for_rfi_renotification.run()

    assert_metrics(
        query_applications_for_rfi_renotification,
        {
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_RFI_NOTIFICATION_COUNT_IN_LAST_30_DAYS: 1,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_DUE_FOR_RENOTIFICATION: 0,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_CLAIMS_BY_ABSENCE_ID: 0,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_WITH_ACTIVE_CLAIMS_READY_FOR_RENOTIFICATION: 0,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_WITH_CALCULATED_DEADLINES: 0,
        },
    )

    assert any(
        record.levelname == "INFO"
        and record.message
        == "No records found for renotifications after filtering out recently notified claims"
        and getattr(record, "status", None) == "Completed"
        for record in caplog.records
    )


def test_run_with_no_records_with_active_claim_status(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    test_db_session,
    caplog,
):
    today = datetime.datetime.now()
    notification = NotificationFactory.create(
        created_at=(today - datetime.timedelta(days=10)), fineos_absence_id="some_absence_id"
    )
    test_db_session.add(notification)
    test_db_session.commit()

    with caplog.at_level("INFO"):
        query_applications_for_rfi_renotification.run()

    assert_metrics(
        query_applications_for_rfi_renotification,
        {
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_RFI_NOTIFICATION_COUNT_IN_LAST_30_DAYS: 1,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_DUE_FOR_RENOTIFICATION: 1,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_CLAIMS_BY_ABSENCE_ID: 0,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_WITH_ACTIVE_CLAIMS_READY_FOR_RENOTIFICATION: 0,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_WITH_CALCULATED_DEADLINES: 0,
        },
    )

    assert any(
        record.levelname == "INFO"
        and record.message == "No claims found for the provided fineos absence ids"
        and getattr(record, "status", None) == "Completed"
        for record in caplog.records
    )


@pytest.mark.parametrize(
    "created_at, last_notified_at, fineos_absence_status_id",
    [
        (9, None, 1),
        (14, 5, 6),
        (24, 10, 7),
    ],
)
def test_run_with_valid_records(
    query_applications_for_rfi_renotification: QueryApplicationsForRFIReNotificationStep,
    test_db_session,
    created_at: int,
    last_notified_at: int | None,
    fineos_absence_status_id: int,
):
    today = datetime.datetime.now()
    claim = ClaimFactory.create(fineos_absence_status_id=fineos_absence_status_id)
    notification = NotificationFactory.create(
        created_at=today - datetime.timedelta(days=created_at),
        last_notified_at=(
            today - datetime.timedelta(days=last_notified_at) if last_notified_at else None
        ),
        fineos_absence_id=claim.fineos_absence_id,
    )
    test_db_session.add(notification)
    test_db_session.commit()

    query_applications_for_rfi_renotification.run()

    assert_metrics(
        query_applications_for_rfi_renotification,
        {
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_RFI_NOTIFICATION_COUNT_IN_LAST_30_DAYS: 1,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_DUE_FOR_RENOTIFICATION: 1,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_CLAIMS_BY_ABSENCE_ID: 1,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_WITH_ACTIVE_CLAIMS_READY_FOR_RENOTIFICATION: 1,
            QueryApplicationsForRFIReNotificationStep.Metrics.TOTAL_NOTIFICATIONS_WITH_CALCULATED_DEADLINES: 1,
        },
    )
