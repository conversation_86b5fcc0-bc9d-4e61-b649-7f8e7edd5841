from massgov.pfml.tasks.leave_admin_csv_export import LeaveAdminCsvExportTaskRunner


def test_leave_admin_csv_export_logs_expected_messages(caplog, test_db_session):
    with caplog.at_level("INFO"):
        LeaveAdminCsvExportTaskRunner().run_steps(test_db_session, test_db_session)

    messages = [record.message for record in caplog.records]

    assert "Start - Leave Admin CSV Export job" in messages
    assert "Processing leave admin CSV export" in messages
    assert "End - Leave Admin CSV Export job" in messages
