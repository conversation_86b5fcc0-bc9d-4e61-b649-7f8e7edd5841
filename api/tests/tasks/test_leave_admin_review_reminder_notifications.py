from massgov.pfml.tasks.leave_admin_review_reminder_notifications import (
    LeaveAdminReviewNotificationTaskRunner,
)


def test_leave_admin_review_notification_runs_all_intervals(caplog, test_db_session):
    with caplog.at_level("INFO"):
        LeaveAdminReviewNotificationTaskRunner().run_steps(test_db_session, test_db_session)

    messages = [record.message for record in caplog.records]

    assert "Start - Leave Admin Review Reminder Notifications job" in messages
    assert "End - Leave Admin Review Reminder Notifications job" in messages

    for offset in [-5, -2, 1]:
        assert (
            f"Processing leave admin review notification step for offset: {offset} day" in messages
        )
