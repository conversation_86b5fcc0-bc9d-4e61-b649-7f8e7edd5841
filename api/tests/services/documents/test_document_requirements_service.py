import itertools
from datetime import date, datetime, timedelta
from unittest import mock

import pytest

from massgov.pfml.api.models.documents.common import (
    DocumentRequirement,
    DocumentRequirementCategory,
    DocumentType,
    DocumentTypeRequirement,
)
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.lookup_data.absences import AbsenceReason
from massgov.pfml.db.lookup_data.applications import LeaveReason
from massgov.pfml.db.models.factories import AbsencePeriodFactory
from massgov.pfml.fineos.models.customer_api import OutstandingSupportingEvidence
from massgov.pfml.services.documents.required_documents import DocumentRequirementService


@pytest.fixture(autouse=True)
def setup_factories(initialize_factories_session):
    return


@pytest.fixture
def service(test_db_session):
    # Create an instance of the service with the mocked test_db_session
    return DocumentRequirementService(test_db_session)


@pytest.fixture
def claim_with_medical_leave_period(application_with_claim):
    claim = application_with_claim.claim
    claim.absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )
    ]
    return claim


# It's good to test against a hard-coded version, but this comes up repeatedly and is too big
# to write out each time.
SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS = [
    DocumentTypeRequirement(
        document_requirement_category=DocumentRequirementCategory.IDENTIFICATION,
        document_types=(DocumentType.identification_proof,),
    ),
    DocumentTypeRequirement(
        document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
        document_types=(
            DocumentType.own_serious_health_condition_form,
            DocumentType.healthcare_provider_form,
        ),
    ),
]


@pytest.fixture
def claim_with_no_absence_periods(application_with_claim):
    claim = application_with_claim.claim
    claim.absence_periods = []
    return claim


def test_are_all_required_documents_received_with_no_claim(service, application):
    # Test when the application has no claim
    all_required_documents_received = service.are_all_required_documents_received(application)
    assert all_required_documents_received is False, "Expected False when application has no claim"


def test_are_all_required_documents_received_with_unreceived_documents(
    service, application_with_claim
):
    # Mock the service method to return unreceived document types
    service.get_unreceived_required_document_types = mock.MagicMock(
        return_value=[
            DocumentTypeRequirement(
                document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
                document_types=(DocumentType.own_serious_health_condition_form,),
            )
        ]
    )

    # Test when there are unreceived documents
    all_required_documents_received = service.are_all_required_documents_received(
        application_with_claim
    )
    assert (
        all_required_documents_received is False
    ), "Expected False when there are unreceived documents"


def test_are_all_required_documents_received_with_all_documents_received(
    service, application_with_claim
):
    # Mock the service method to return an empty list (all documents received)
    service.get_unreceived_required_document_types = mock.MagicMock(return_value=[])

    # Test when all documents are received
    all_required_documents_received = service.are_all_required_documents_received(
        application_with_claim
    )
    assert (
        all_required_documents_received is True
    ), "Expected True when all required documents are received"


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.get_outstanding_evidence")
def test_get_unreceived_required_document_types_when_application_has_received_all_required_documents(
    mock_get_outstanding_evidence, service, application_with_claim
):
    mock_get_outstanding_evidence.return_value = [
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": True,
                "name": DocumentType.identification_proof,
                "rootCaseId": "NTN-#",
                "source": "John Doe",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": True,
                "name": DocumentType.own_serious_health_condition_form,
                "rootCaseId": "NTN-#",
                "source": "John Doe",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
    ]
    service.get_required_document_types_for_application = mock.MagicMock(
        return_value=SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS
    )

    document_types = service.get_unreceived_required_document_types(application_with_claim)

    # Assert the result
    assert document_types == [], "Expected no unreceived documents"


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.get_outstanding_evidence")
def test_get_unreceived_required_document_types_when_application_has_no_outstanding_evidence(
    mock_get_outstanding_evidence, service, application_with_claim
):
    # Not appearing in the response means the evidence is either satisfied or not required.
    mock_get_outstanding_evidence.return_value = []
    service.get_required_document_types_for_application = mock.MagicMock(
        return_value=SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS
    )

    document_types = service.get_unreceived_required_document_types(application_with_claim)

    # Assert the result
    assert document_types == [], "No outstanding evidence, so no unreceived required documents"


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.get_outstanding_evidence")
def test_get_unreceived_required_document_types_when_employer_response_is_outstanding_with_no_documents(
    mock_get_outstanding_evidence, service, application_with_claim
):
    mock_get_outstanding_evidence.return_value = [
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": False,
                "name": "Employer Confirmation of Leave Data",
                "rootCaseId": "NTN-#",
                "source": "Acme Corp.",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
    ]

    service.get_required_document_types_for_application = mock.MagicMock(
        return_value=SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS
    )

    document_types = service.get_unreceived_required_document_types(application_with_claim)

    # Assert the result
    assert (
        document_types == []
    ), "No RELEVANT outstanding evidence, so no unreceived required documents"


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.get_outstanding_evidence")
def test_get_unreceived_required_document_types_when_application_has_an_unreceived_required_document(
    mock_get_outstanding_evidence, service, application_with_claim
):
    mock_get_outstanding_evidence.return_value = [
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": False,
                "name": DocumentType.identification_proof,
                "rootCaseId": "NTN-#",
                "source": "John Doe",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": True,
                "name": DocumentType.own_serious_health_condition_form,
                "rootCaseId": "NTN-#",
                "source": "John Doe",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
    ]

    service.get_required_document_types_for_application = mock.MagicMock(
        return_value=SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS
    )

    document_types = service.get_unreceived_required_document_types(application_with_claim)

    # Assert the result
    assert document_types == [
        DocumentTypeRequirement(
            document_requirement_category=DocumentRequirementCategory.IDENTIFICATION,
            document_types=(DocumentType.identification_proof,),
        )
    ], "identification_proof is outstanding evidence"


def test_get_unreceived_required_document_types_when_application_has_no_claim(
    service,
    application,
):
    with pytest.raises(
        ValueError,
        match=f"Cannot get unreceived required document types for application {application.application_id}. The application does not have a claim.",
    ):
        service.get_unreceived_required_document_types(application)


def test_get_document_requirements_for_claim_via_absence_periods_with_no_absence_periods(
    service,
    claim_with_no_absence_periods,
):

    with pytest.raises(
        ValueError,
        match=f"Cannot get document requirements for claim {claim_with_no_absence_periods.claim_id}. The claim must have associated absence periods.",
    ):
        service.get_document_requirements_for_claim_via_absence_periods(
            claim_with_no_absence_periods
        )


def test_get_document_requirements_for_claim_via_absence_periods_with_no_document_type_requirements(
    service,
    claim_with_medical_leave_period,
):

    service.get_required_document_types_for_absence_periods = mock.MagicMock(return_value=[])

    document_requirements = service.get_document_requirements_for_claim_via_absence_periods(
        claim_with_medical_leave_period
    )

    assert (
        document_requirements == []
    ), "Expected no document requirements when there are no document type requirements"


def test_get_document_requirements_for_application_with_no_document_type_requirements(
    service,
    application_with_claim,
):

    service.get_required_document_types_for_application = mock.MagicMock(return_value=[])

    document_requirements = service.get_document_requirements_for_application(
        application_with_claim
    )

    assert (
        document_requirements == []
    ), "Expected no document requirements when there are no document type requirements"


def test_determine_document_requirements_when_one_of_each_document_uploaded(service):
    uploaded_documents = [
        DocumentResponse(
            name="Identification Proof",
            description=DocumentType.identification_proof,
            document_type=DocumentType.identification_proof,
            created_at=datetime(2020, 1, 1),
            is_legal_notice=False,
            pfml_document_type=None,
        ),
        DocumentResponse(
            name="Own serious health condition form",
            description=DocumentType.own_serious_health_condition_form,
            document_type=DocumentType.own_serious_health_condition_form,
            created_at=datetime(2020, 2, 1),
            is_legal_notice=False,
            pfml_document_type=None,
        ),
    ]

    document_requirements = service.determine_document_requirements(
        SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.IDENTIFICATION,
            allowed_document_types=(DocumentType.identification_proof,),
            document_type=DocumentType.identification_proof,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=DocumentType.identification_proof,
            pfml_document_type=None,
            upload_date=datetime(2020, 1, 1),
        )
        in document_requirements
    )
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
            allowed_document_types=(
                DocumentType.own_serious_health_condition_form,
                DocumentType.healthcare_provider_form,
            ),
            document_type=DocumentType.own_serious_health_condition_form,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=DocumentType.own_serious_health_condition_form,
            pfml_document_type=None,
            upload_date=datetime(2020, 2, 1),
        )
        in document_requirements
    )


def test_determine_document_requirements_when_two_identification_documents_uploaded(service):
    uploaded_documents = [
        DocumentResponse(
            name="Identification Proof 1",
            description=DocumentType.identification_proof,
            document_type=DocumentType.identification_proof,
            created_at=datetime(2020, 1, 1),
            is_legal_notice=False,
            pfml_document_type=None,
        ),
        DocumentResponse(
            name="Identification Proof 2",
            description=DocumentType.identification_proof,
            document_type=DocumentType.identification_proof,
            created_at=datetime(2020, 5, 5),
            is_legal_notice=False,
            pfml_document_type=None,
        ),
    ]

    document_requirements = service.determine_document_requirements(
        SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.IDENTIFICATION,
            allowed_document_types=(DocumentType.identification_proof,),
            document_type=DocumentType.identification_proof,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=DocumentType.identification_proof,
            pfml_document_type=None,
            upload_date=datetime(2020, 5, 5),
        )
        in document_requirements
    )
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
            allowed_document_types=(
                DocumentType.own_serious_health_condition_form,
                DocumentType.healthcare_provider_form,
            ),
            document_type=None,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=None,
            pfml_document_type=None,
            upload_date=None,
        )
        in document_requirements
    )


def test_determine_document_requirements_when_no_documents_uploaded(service):
    uploaded_documents = []

    document_requirements = service.determine_document_requirements(
        SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.IDENTIFICATION,
            allowed_document_types=(DocumentType.identification_proof,),
            document_type=None,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=None,
            pfml_document_type=None,
            upload_date=None,
        )
        in document_requirements
    )
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
            allowed_document_types=(
                DocumentType.own_serious_health_condition_form,
                DocumentType.healthcare_provider_form,
            ),
            document_type=None,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=None,
            pfml_document_type=None,
            upload_date=None,
        )
        in document_requirements
    )


def test_determine_document_requirements_when_healthcare_provider_form_uploaded(service):
    """A Healthcare Provider Form should satisfy the medical certification requirement"""
    uploaded_documents = [
        DocumentResponse(
            name="Healthcare Provider Form",
            description=DocumentType.healthcare_provider_form,
            document_type=DocumentType.healthcare_provider_form,
            created_at=datetime(2020, 2, 1),
            is_legal_notice=False,
        ),
    ]

    document_requirements = service.determine_document_requirements(
        SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    # No need to check the content of the missing ID doc, since that's tested elsewhere
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
            allowed_document_types=(
                DocumentType.own_serious_health_condition_form,
                DocumentType.healthcare_provider_form,
            ),
            document_type=DocumentType.healthcare_provider_form,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=DocumentType.healthcare_provider_form,
            pfml_document_type=None,
            upload_date=datetime(2020, 2, 1),
        )
        in document_requirements
    )


def test_determine_document_requirements_when_two_cert_forms_uploaded(service):
    """When two cert forms are present, it should return the newer one"""
    uploaded_documents = [
        DocumentResponse(
            name="Healthcare Provider Form",
            description=DocumentType.healthcare_provider_form,
            document_type=DocumentType.healthcare_provider_form,
            created_at=datetime(2020, 3, 1),
            is_legal_notice=False,
        ),
        DocumentResponse(
            name="Own serious health condition form",
            description=DocumentType.own_serious_health_condition_form,
            document_type=DocumentType.own_serious_health_condition_form,
            created_at=datetime(2020, 2, 1),
            is_legal_notice=False,
        ),
    ]

    document_requirements = service.determine_document_requirements(
        SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
            allowed_document_types=(
                DocumentType.own_serious_health_condition_form,
                DocumentType.healthcare_provider_form,
            ),
            document_type=DocumentType.healthcare_provider_form,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=DocumentType.healthcare_provider_form,
            pfml_document_type=None,
            upload_date=datetime(2020, 3, 1),
        )
        in document_requirements
    )


def test_determine_document_requirements_when_two_cert_forms_uploaded_oldest_first(service):
    """When two cert forms are present, it should return the newer one even if it's not listed first"""
    uploaded_documents = [
        DocumentResponse(
            name="Healthcare Provider Form",
            description=DocumentType.healthcare_provider_form,
            document_type=DocumentType.healthcare_provider_form,
            created_at=datetime(2020, 3, 1),
            is_legal_notice=False,
        ),
        DocumentResponse(
            name="Own serious health condition form",
            description=DocumentType.own_serious_health_condition_form,
            document_type=DocumentType.own_serious_health_condition_form,
            created_at=datetime(2020, 4, 1),
            is_legal_notice=False,
        ),
    ]

    document_requirements = service.determine_document_requirements(
        SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
            allowed_document_types=(
                DocumentType.own_serious_health_condition_form,
                DocumentType.healthcare_provider_form,
            ),
            document_type=DocumentType.own_serious_health_condition_form,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=DocumentType.own_serious_health_condition_form,
            pfml_document_type=None,
            upload_date=datetime(2020, 4, 1),
        )
        in document_requirements
    )


def test_determine_document_requirements_when_unsupported_types_uploaded(service):
    """Unsupported document types do not satisfy document requirements"""
    uploaded_documents = [
        DocumentResponse(
            name="Covered Service Member Identification Proof",
            description=DocumentType.covered_service_member_identification_proof,
            document_type=DocumentType.covered_service_member_identification_proof,
            created_at=datetime(2020, 3, 1),
            is_legal_notice=False,
        ),
        DocumentResponse(
            name="Care for a family member form",
            description=DocumentType.care_for_a_family_member_form,
            document_type=DocumentType.care_for_a_family_member_form,
            created_at=datetime(2020, 3, 2),
            is_legal_notice=False,
        ),
    ]

    document_requirements = service.determine_document_requirements(
        SERIOUS_HEALTH_CONDITION_DOC_TYPE_REQUIREMENTS, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.IDENTIFICATION,
            allowed_document_types=(DocumentType.identification_proof,),
            document_type=None,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=None,
            pfml_document_type=None,
            upload_date=None,
        )
        in document_requirements
    )
    assert (
        DocumentRequirement(
            document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
            allowed_document_types=(
                DocumentType.own_serious_health_condition_form,
                DocumentType.healthcare_provider_form,
            ),
            document_type=None,  # TODO (PFMLPB-25359): remove document_type
            fineos_document_type=None,
            pfml_document_type=None,
            upload_date=None,
        )
        in document_requirements
    )


def test_get_required_document_types_for_application_for_serious_health_condition(
    service, application
):

    document_types = service.get_required_document_types_for_application(application)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = service.get_required_document_types_by_absence_reason(
        LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_application_for_child_bonding(service, application):

    application.leave_reason_id = LeaveReason.CHILD_BONDING.leave_reason_id

    document_types = service.get_required_document_types_for_application(application)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = service.get_required_document_types_by_absence_reason(
        LeaveReason.CHILD_BONDING.leave_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_application_for_care_of_family_member(
    service, application
):

    application.leave_reason_id = LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id

    document_types = service.get_required_document_types_for_application(application)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = service.get_required_document_types_by_absence_reason(
        LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_application_for_pregnancy_maternity(
    app, service, application
):
    with app.app.app_context():

        application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id

        document_types = service.get_required_document_types_for_application(application)

        assert len(document_types) == 2, "Expected two required document types"
        expected_document_types = service.get_required_document_types_by_absence_reason(
            LeaveReason.PREGNANCY_MATERNITY.leave_reason_description
        )
        for doc_type in expected_document_types:
            assert doc_type in document_types, f"Expected {doc_type} to be in document types"


# TODO (PFMLPB-25436): once the FINEOS 22->24 transition is fully complete, remove both of these
# "test_get_required_document_types_for_pregnancy_maternity_fineos_XX" tests. The test above
# checks coherency, so once the FINEOS version difference is gone, these two would just be
# comparing to a hard-coded value that shouldn't normally be an explicit test expectation.
def test_get_required_document_types_for_pregnancy_maternity_fineos_22(
    app, service, application, fineos_pre_v24_feature_config
):
    # mock_get_features_config.return_value = FeaturesConfig()
    # mock_get_features_config.return_value.fineos.is_running_v24 = False

    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    with app.app.app_context():
        document_types = service.get_required_document_types_for_application(application)
    cert_req = next(
        filter(
            lambda i: i.document_requirement_category == DocumentRequirementCategory.CERTIFICATION,
            document_types,
        )
    )
    assert cert_req.document_types[0] == DocumentType.pregnancy_maternity_form


def test_get_required_document_types_for_pregnancy_maternity_fineos_24(
    app, service, application, fineos_v24_feature_config
):
    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    with app.app.app_context():
        document_types = service.get_required_document_types_for_application(application)
    cert_req = next(
        filter(
            lambda i: i.document_requirement_category == DocumentRequirementCategory.CERTIFICATION,
            document_types,
        )
    )
    assert cert_req.document_types[0] == DocumentType.pregnancy_and_maternity_form


def test_get_required_document_types_for_application_for_invalid_leave_reason(service, application):

    application.leave_reason_id = 999

    document_types = service.get_required_document_types_for_application(application)

    assert document_types == [], "No mapping for invalid leave reason, so expect an empty list"


def test_get_required_document_types_for_absence_periods_for_serious_health_condition(service):

    absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )
    ]

    document_types = service.get_required_document_types_for_absence_periods(absence_periods)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = service.get_required_document_types_by_absence_reason(
        AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_absence_periods_for_child_bonding(service):

    absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.CHILD_BONDING.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )
    ]

    document_types = service.get_required_document_types_for_absence_periods(absence_periods)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = service.get_required_document_types_by_absence_reason(
        AbsenceReason.CHILD_BONDING.absence_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_absence_periods_for_care_of_family_member(service):

    absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.CARE_OF_A_FAMILY_MEMBER.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )
    ]

    document_types = service.get_required_document_types_for_absence_periods(absence_periods)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = service.get_required_document_types_by_absence_reason(
        AbsenceReason.CARE_OF_A_FAMILY_MEMBER.absence_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_absence_periods_for_pregnancy_maternity(app, service):
    # TODO (PFMLPB-25436): this `app_context` wrapper can be removed once the FINEOS 22/24
    # condition is removed from the get_required_document_types_by_absence_reason method
    with app.app.app_context():

        absence_periods = [
            AbsencePeriodFactory.create(
                absence_reason_id=AbsenceReason.PREGNANCY_MATERNITY.absence_reason_id,
                absence_period_start_date=date.today() + timedelta(days=5),
                absence_period_end_date=date.today() + timedelta(days=20),
            )
        ]

        document_types = service.get_required_document_types_for_absence_periods(absence_periods)

        assert len(document_types) == 2, "Expected two required document types"
        expected_document_types = service.get_required_document_types_by_absence_reason(
            AbsenceReason.PREGNANCY_MATERNITY.absence_reason_description
        )
        for doc_type in expected_document_types:
            assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_absence_periods_for_med_preg(app, service):
    with app.app.app_context():

        absence_periods = [
            AbsencePeriodFactory.create(
                absence_reason_id=AbsenceReason.PREGNANCY_MATERNITY.absence_reason_id,
                absence_period_start_date=date.today() + timedelta(days=5),
                absence_period_end_date=date.today() + timedelta(days=20),
            ),
            AbsencePeriodFactory.create(
                absence_reason_id=AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id,
                absence_period_start_date=date.today() + timedelta(days=5),
                absence_period_end_date=date.today() + timedelta(days=20),
            ),
        ]

        document_requirements = service.get_required_document_types_for_absence_periods(
            absence_periods
        )
        document_types = list(
            itertools.chain.from_iterable(
                doc_req.document_types for doc_req in document_requirements
            )
        )

        assert len(document_requirements) == 3
        assert "Identification Proof" in document_types
        assert "Pregnancy/Maternity form" in document_types
        assert "Own serious health condition form" in document_types


def test_get_required_document_types_for_absence_periods_for_no_absence_periods(service):

    document_types = service.get_required_document_types_for_absence_periods([])

    assert document_types == [], "No absence periods, so expect an empty list"


def test_get_required_document_types_by_absence_reason_valid_reason(service):
    absence_reason = AbsenceReason.CHILD_BONDING.absence_reason_description

    document_types = service.get_required_document_types_by_absence_reason(absence_reason)

    assert len(document_types) == 2, "Expected two required document types"

    expected_document_types = service.get_required_document_types_by_absence_reason(
        AbsenceReason.CHILD_BONDING.absence_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_by_absence_reason_invalid_reason(service):
    absence_reason = "INVALID"

    document_types = service.get_required_document_types_by_absence_reason(absence_reason)

    assert document_types == [], "Expected an empty list for invalid absence reason"


def test_get_required_document_types_by_absence_reason_no_reason(service):
    absence_reason = None

    document_types = service.get_required_document_types_by_absence_reason(absence_reason)

    assert document_types == [], "Expected an empty list for missing absence reason"
