import pytest

import massgov.pfml.db
import massgov.pfml.fineos
import massgov.pfml.fineos.employers as fineos_employers
import massgov.pfml.fineos.mock.field
from massgov.pfml.db.models.dor import EmployerDORExemption, FineosServiceAgreement
from massgov.pfml.db.models.employees import Employer, EmployerPushToFineosQueue
from massgov.pfml.db.models.factories import (
    EmployerOnlyDORDataFactory,
    EmployerPushToFineosQueueFactory,
)


# Use a persistent database for all the tests here, as they need to support multiple concurrent
# connections. This means we can't use a transaction based fixture, so wipe relevant tables before
# each individual test function.
@pytest.fixture(scope="function", autouse=True)
def clear_tables(module_persistent_db, module_persistent_db_session):
    module_persistent_db_session.query(FineosServiceAgreement).delete()
    # Updating the employer_id to None is necessary to avoid a foreign key constraint violation
    # since we have no cascading delete setup
    module_persistent_db_session.query(EmployerDORExemption).update(
        {
            EmployerDORExemption.employer_id: None,
        }
    )
    module_persistent_db_session.query(EmployerDORExemption).delete()
    module_persistent_db_session.query(Employer).delete()
    module_persistent_db_session.query(EmployerPushToFineosQueue).delete()
    module_persistent_db_session.commit()


class SpecialTestException(Exception):
    """Exception only defined here for ensure mocked exception is bubbled up"""


def test_get_new_or_updated_employers(module_persistent_db_session, mocker):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employers = EmployerOnlyDORDataFactory.create_batch(size=10)
    for employer in employers:
        EmployerPushToFineosQueueFactory.create(employer_id=employer.employer_id, process_id=None)

    skip_locked_query_spy = mocker.spy(fineos_employers.db, "skip_locked_query")

    employer_loader = fineos_employers.EmployerLoader(
        module_persistent_db_session, fineos_client, process_id=1, batch_size=5
    )

    # with 10 fresh Employers, grab them 5 at a time
    values = employer_loader.get_new_or_updated_employers()
    for i, (employer, actions) in enumerate(values, 1):
        assert employer in employers
        assert "INSERT" in actions

        employer_queue_items = (
            module_persistent_db_session.query(EmployerPushToFineosQueue.process_id)
            .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
            .all()
        )

        assert len(employer_queue_items) == 1
        assert employer_queue_items[0].process_id == 1

        # check the batching behavior, if we are in the range of the first batch
        # of 5, the other 5 should not be marked by this process
        if i < 5:
            assert (
                module_persistent_db_session.query(EmployerPushToFineosQueue.process_id)
                .filter(EmployerPushToFineosQueue.process_id.is_(None))
                .count()
            ) == 5

    assert skip_locked_query_spy.call_count == 2

    # if we try to grab updates again, but only fresh ones
    employers_to_process = employer_loader.get_new_or_updated_employers(
        pickup_existing_at_start=False
    )

    # ...we should grab none
    assert len(list(employers_to_process)) == 0

    # if we try to grab updates again, but allow old ones
    employers_to_process = employer_loader.get_new_or_updated_employers(
        pickup_existing_at_start=True
    )

    # ...we should grab them all
    assert len(list(employers_to_process)) == 10

    # and all the queue items are still there, since we didn't delete them
    employer_queue_items_after = module_persistent_db_session.query(EmployerPushToFineosQueue).all()
    assert len(employer_queue_items_after) == 10


def make_test_db():
    return massgov.pfml.db.init()


def test_load_employers_updates_simple(module_persistent_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer = EmployerOnlyDORDataFactory.create()
    EmployerPushToFineosQueueFactory.create(employer_id=employer.employer_id)

    assert employer.fineos_employer_id is None

    queue_items_before = (
        module_persistent_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
        .all()
    )
    assert len(queue_items_before) == 1

    employer_loader = fineos_employers.EmployerLoader(module_persistent_db_session, fineos_client)
    employer_loader.run()
    employers_report = employer_loader.employers_report
    log_entry = employer_loader.log_entry
    assert employers_report.total_employers_count == 1
    assert employers_report.loaded_employers_count == 1
    assert employers_report.errored_employers_count == 0
    assert employers_report.warning_message is None
    assert log_entry.status == "success"

    queue_items_after = (
        module_persistent_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
        .all()
    )
    assert len(queue_items_after) == 0

    module_persistent_db_session.refresh(employer)

    assert employer.fineos_employer_id is not None


def test_load_employers_updates_does_not_get_stuck_in_loop_with_failing_employer(
    module_persistent_db_session, monkeypatch
):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer = EmployerOnlyDORDataFactory.create()
    EmployerPushToFineosQueueFactory.create(employer_id=employer.employer_id)

    assert employer.fineos_employer_id is None

    employer_queue_items_before = (
        module_persistent_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
        .all()
    )
    assert len(employer_queue_items_before) == 1

    # have the call to FINEOS fail
    def mock(*args, **kwargs):
        raise SpecialTestException

    monkeypatch.setattr(fineos_client, "create_or_update_employer", mock)

    employer_loader = fineos_employers.EmployerLoader(module_persistent_db_session, fineos_client)
    employer_loader.run()
    employers_report = employer_loader.employers_report
    log_entry = employer_loader.log_entry
    assert employers_report.total_employers_count == 1
    assert employers_report.loaded_employers_count == 0
    assert employers_report.errored_employers_count == 1
    assert employers_report.warning_message is None
    assert log_entry.status == "success"

    employer_queue_items_after = (
        module_persistent_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
        .all()
    )
    assert len(employer_queue_items_after) == 1

    module_persistent_db_session.refresh(employer)

    assert employer.fineos_employer_id is None


def test_load_employers_updates_picks_up_left_behind_work(module_persistent_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer = EmployerOnlyDORDataFactory.create()
    EmployerPushToFineosQueueFactory.create(employer_id=employer.employer_id)

    assert employer.fineos_employer_id is None

    employer_queue_items = (
        module_persistent_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
        .all()
    )
    assert len(employer_queue_items) == 1

    process_id = 1

    # update the log row to indicate it had already been attempted processing by
    # this process id, simulating a failed run in the past
    module_persistent_db_session.query(EmployerPushToFineosQueue).filter(
        EmployerPushToFineosQueue.employer_id == employer.employer_id
    ).update({EmployerPushToFineosQueue.process_id: process_id})

    employer_loader = fineos_employers.EmployerLoader(
        module_persistent_db_session, fineos_client, process_id
    )
    employer_loader.run()
    employers_report = employer_loader.employers_report
    log_entry = employer_loader.log_entry
    assert employers_report.total_employers_count == 1
    assert employers_report.loaded_employers_count == 1
    assert employers_report.errored_employers_count == 0
    assert employers_report.warning_message is None
    assert log_entry.status == "success"

    employer_queue_items_after = (
        module_persistent_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
        .all()
    )
    assert len(employer_queue_items_after) == 0

    module_persistent_db_session.refresh(employer)

    assert employer.fineos_employer_id is not None


def test_load_employers_updates_does_not_pick_up_work_from_other_process(
    module_persistent_db_session, db_config_scope_module
):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer = EmployerOnlyDORDataFactory.create()
    EmployerPushToFineosQueueFactory.create(employer_id=employer.employer_id)

    assert employer.fineos_employer_id is None

    employer_queue_items = (
        module_persistent_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
        .all()
    )
    assert len(employer_queue_items) == 1

    process_id = 1

    # update the log row to indicate it had already been attempted processing by
    # this process id, simulating a failed run in the past
    module_persistent_db_session.query(EmployerPushToFineosQueue).filter(
        EmployerPushToFineosQueue.employer_id == employer.employer_id
    ).update({EmployerPushToFineosQueue.process_id: process_id})
    module_persistent_db_session.commit()

    other_session = massgov.pfml.db.init(config=db_config_scope_module)
    employer_loader = fineos_employers.EmployerLoader(other_session, fineos_client, process_id=2)
    employer_loader.run()
    employers_report = employer_loader.employers_report
    log_entry = employer_loader.log_entry

    assert employers_report.total_employers_count == 0
    assert employers_report.loaded_employers_count == 0
    assert employers_report.errored_employers_count == 0
    assert employers_report.warning_message is None
    assert log_entry.status == "success"

    employer_queue_items_after = (
        module_persistent_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
        .all()
    )
    assert len(employer_queue_items_after) == 1

    module_persistent_db_session.refresh(employer)

    assert employer.fineos_employer_id is None


def test_load_employers_updates_limit_set_warning_status(module_persistent_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()
    test_limit_size = 6
    employer_num = 10

    employers = EmployerOnlyDORDataFactory.create_batch(size=employer_num)
    for employer in employers:
        EmployerPushToFineosQueueFactory.create(employer_id=employer.employer_id)

    employer_queue_items_before = module_persistent_db_session.query(
        EmployerPushToFineosQueue
    ).all()

    assert len(employer_queue_items_before) == employer_num

    employer_loader = fineos_employers.EmployerLoader(
        module_persistent_db_session, fineos_client, employer_update_limit=test_limit_size
    )
    employer_loader.run()
    employers_report = employer_loader.employers_report
    log_entry = employer_loader.log_entry

    assert employers_report.total_employers_count == test_limit_size
    assert employers_report.loaded_employers_count == test_limit_size
    assert employers_report.errored_employers_count == 0
    assert employers_report.warning_message == f"Employer update limit reached: {test_limit_size}."
    assert log_entry.status == "warning"

    [module_persistent_db_session.refresh(employer) for employer in employers]

    employer_ids = [employer.fineos_employer_id for employer in employers]

    assert sum([bool(fineos_id) for fineos_id in employer_ids]) == test_limit_size
    assert (
        sum([not bool(fineos_id) for fineos_id in employer_ids]) == employer_num - test_limit_size
    )

    employer_queue_items_after = module_persistent_db_session.query(EmployerPushToFineosQueue).all()

    assert len(employer_queue_items_after) == employer_num - test_limit_size

    # delete lingering EmployerPushToFineosQueue items
    module_persistent_db_session.query(EmployerPushToFineosQueue).delete()
    module_persistent_db_session.commit()
