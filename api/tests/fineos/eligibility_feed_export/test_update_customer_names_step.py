from datetime import date
from unittest import mock
from uuid import UUID

import pytest

from massgov.pfml.db import Session
from massgov.pfml.db.models.employees import (
    Employee,
    EmployeePushToFineosQueue,
    Employer,
    TaxIdentifier,
)
from massgov.pfml.db.models.factories import (
    EmployeeFactory,
    EmployeeOccupationFactory,
    EmployeePushToFineosQueueFactory,
    EmployerFactory,
    FINEOSWebIdExtFactory,
    TaxIdentifierFactory,
)
from massgov.pfml.db.models.fineos_web_id import FINEOSWebIdExt
from massgov.pfml.fineos.eligibility_feed_export.update_customer_names_step import (
    UpdateCustomerNamesStep,
)
from massgov.pfml.fineos.models.customer_api import (
    Address,
    Customer,
    CustomerAddress,
    ExtensionAttribute,
)
from tests.helpers.assertions import assert_metrics


def test_update_customer_names_step_does_nothing_with_no_employee_push_to_fineos_queue_records(
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    update_customer_names_step.run()

    fineos_web_id_exts = test_db_session.query(FINEOSWebIdExt).all()
    assert len(fineos_web_id_exts) == 0
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 0,
        },
    )


def test_update_customer_names_step_does_not_process_update_new_employer_actions(
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    create_entities_for_testing(
        employee_push_to_fineos_queue_action="UPDATE_NEW_EMPLOYER",
        should_reference_employer_in_queue=True,
    )

    update_customer_names_step.run()

    fineos_web_id_ext = test_db_session.query(FINEOSWebIdExt).one_or_none()
    assert fineos_web_id_ext is not None
    update_new_employer_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .one_or_none()
    )
    assert update_new_employer_record is not None
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 0,
        },
    )


def test_update_customer_names_step_does_not_process_updates_for_employees_with_pending_employer_updates(
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    tax_identifier = TaxIdentifierFactory.create()
    employee = EmployeeFactory.create(tax_identifier=tax_identifier)
    employer = EmployerFactory.create()
    create_entities_for_testing(employee=employee, employer=employer, tax_identifier=tax_identifier)
    EmployeePushToFineosQueueFactory.create(
        action="UPDATE_NEW_EMPLOYER",
        employee_id=employee.employee_id,
        employer_id=employer.employer_id,
        priority=3,
    )

    update_customer_names_step.run()

    update_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .one_or_none()
    )
    assert update_record is not None
    update_new_employer_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .one_or_none()
    )
    assert update_new_employer_record is not None
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 0,
        },
    )


def test_update_customer_names_step_does_not_process_queue_records_without_associated_employees(
    update_customer_names_step: UpdateCustomerNamesStep,
):
    create_entities_for_testing(
        should_create_fineos_web_id_ext=False, should_reference_employee_in_queue=False
    )

    update_customer_names_step.run()

    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 0,
        },
    )


def test_update_customer_names_step_does_not_process_updates_for_employees_without_an_employee_occupation(
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    create_entities_for_testing(should_create_employee_occupation=False)

    update_customer_names_step.run()

    update_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .one_or_none()
    )
    assert update_record is not None
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 0,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.register_api_user")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_details")
def test_update_customer_names_step_processes_one_update(
    mock_update_customer_details,
    mock_register_api_user,
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    mock_read_customer_details.return_value = Customer(
        dateOfBirth=None, firstName="John", lastName="Doe"
    )
    tax_identifier = TaxIdentifierFactory.create()
    employee = EmployeeFactory.create(
        first_name="Mary", last_name="Sue", tax_identifier=tax_identifier
    )
    create_entities_for_testing(employee=employee, tax_identifier=tax_identifier)

    update_customer_names_step.run()

    fineos_web_id_ext = test_db_session.query(FINEOSWebIdExt).one_or_none()
    assert fineos_web_id_ext is not None
    update_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .one_or_none()
    )
    assert update_record is None
    mock_register_api_user.assert_not_called()
    mock_update_customer_details.assert_called_once()
    customer_argument = mock_update_customer_details.call_args_list[0].args[1]
    assert customer_argument.firstName == employee.first_name
    assert customer_argument.lastName == employee.last_name
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 1,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.register_api_user")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_details")
def test_update_customer_names_step_processes_multiple_updates(
    mock_update_customer_details,
    mock_register_api_user,
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    mock_read_customer_details.side_effect = lambda _: Customer(
        dateOfBirth=None, firstName="John", lastName="Doe"
    )
    employer = EmployerFactory.create()

    for _ in range(2):
        create_entities_for_testing(employer=employer)

    update_customer_names_step.run()

    fineos_web_id_exts = test_db_session.query(FINEOSWebIdExt).all()
    assert len(fineos_web_id_exts) == 2
    update_records = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(update_records) == 0
    mock_register_api_user.assert_not_called()
    assert mock_update_customer_details.call_count == 2
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 2,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 2,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.register_api_user")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_details")
def test_update_customer_names_step_processes_a_number_of_updates_greater_than_its_batch_size(
    mock_update_customer_details,
    mock_register_api_user,
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    mock_read_customer_details.side_effect = lambda _: Customer(
        dateOfBirth=None, firstName="Unlikely", lastName="Randomname"
    )
    employer = EmployerFactory.create()
    record_count = UpdateCustomerNamesStep.BATCH_SIZE + 1

    for _ in range(record_count):
        create_entities_for_testing(employer=employer)

    update_customer_names_step.run()

    fineos_web_id_exts = test_db_session.query(FINEOSWebIdExt).all()
    assert len(fineos_web_id_exts) == record_count
    update_records = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(update_records) == 0
    mock_register_api_user.assert_not_called()
    assert mock_update_customer_details.call_count == record_count
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: record_count,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: record_count,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.register_api_user")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_details")
def test_update_customer_names_step_creates_registers_employee_with_fineos_when_not_already_registered(
    mock_update_customer_details,
    mock_register_api_user,
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    mock_read_customer_details.return_value = Customer(
        dateOfBirth=None, firstName="John", lastName="Doe"
    )
    create_entities_for_testing(should_create_fineos_web_id_ext=False)

    update_customer_names_step.run()

    fineos_web_id_ext = test_db_session.query(FINEOSWebIdExt).one_or_none()
    assert fineos_web_id_ext is not None
    update_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .one_or_none()
    )
    assert update_record is None
    mock_register_api_user.assert_called_once()
    mock_update_customer_details.assert_called_once()
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 1,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
def test_update_customer_names_step_handles_update_processing_errors_gracefully(
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    mock_read_customer_details.side_effect = Exception("Oops!")
    create_entities_for_testing()

    update_customer_names_step.run()

    update_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .one_or_none()
    )
    assert update_record is not None
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 1,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
def test_update_customer_names_step_continues_processing_updates_after_an_error(
    mock_read_customer_details,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    employer = EmployerFactory.create()

    for _ in range(2):
        create_entities_for_testing(employer=employer)

    def raise_error_once(_):
        mock_read_customer_details.side_effect = None
        raise Exception("Oops!")

    mock_read_customer_details.side_effect = raise_error_once

    update_customer_names_step.run()

    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 2,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.register_api_user")
def test_update_customer_names_step_persists_fineos_registration_even_if_an_error_occurs(
    mock_register_api_user,
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    mock_read_customer_details.side_effect = Exception("Oops!")
    create_entities_for_testing(should_create_fineos_web_id_ext=False)

    update_customer_names_step.run()

    update_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .one_or_none()
    )
    assert update_record is not None
    mock_register_api_user.assert_called_once()
    fineos_web_id_ext = test_db_session.query(FINEOSWebIdExt).one_or_none()
    assert fineos_web_id_ext is not None
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 1,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_details")
def test_update_customer_names_step_only_processes_the_same_employee_once_when_queued_multiple_times(
    mock_update_customer_details,
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    tax_identifier = TaxIdentifierFactory.create()
    employee = EmployeeFactory.create(tax_identifier=tax_identifier)
    create_entities_for_testing(employee=employee, tax_identifier=tax_identifier)
    EmployeePushToFineosQueueFactory.create(
        action="UPDATE",
        employee_id=employee.employee_id,
    )

    update_customer_names_step.run()

    update_records = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(update_records) == 0
    mock_read_customer_details.assert_called_once()
    mock_update_customer_details.assert_called_once()
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 1,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_details")
def test_update_customer_names_step_only_processes_the_same_employee_once_when_there_are_multiple_employers_and_wages(
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):

    mock_read_customer_details.return_value = Customer(
        dateOfBirth=None,
        firstName="John",
        lastName="Doe",
    )
    tax_identifier = TaxIdentifierFactory.create()
    employee = EmployeeFactory.create(tax_identifier=tax_identifier)

    for _ in range(2):
        create_entities_for_testing(employee=employee, tax_identifier=tax_identifier)

    update_customer_names_step.run()

    update_records = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(update_records) == 0
    mock_read_customer_details.assert_called_once()

    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 1,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_details")
def test_update_customer_names_step_observes_record_process_limit(
    mock_update_customer_details,
    mock_read_customer_details,
    test_db_session: Session,
):
    record_process_limit = 5
    records_to_create_count = record_process_limit + 1
    employer = EmployerFactory.create()

    for _ in range(records_to_create_count):
        create_entities_for_testing(employer=employer)

    update_customer_names_step = UpdateCustomerNamesStep(
        test_db_session, test_db_session, record_process_limit=record_process_limit
    )

    update_customer_names_step.run()

    update_records = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(update_records) == 1
    assert mock_read_customer_details.call_count == record_process_limit
    assert mock_update_customer_details.call_count == record_process_limit
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: record_process_limit,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: record_process_limit,
        },
    )


def test_update_customer_names_step_processes_updates_in_queue_order(
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    employer = EmployerFactory.create()
    low_priority_tax_identifier = TaxIdentifierFactory.create()
    # The uniqueness of an employee is determined by its ID. Since this step
    # should only process a given employee once, it is likely that the
    # implementation involves sorting and filtering using the ID. Fixed IDs
    # guard against possible flaky behavior being introduced by randomization of
    # the IDs.
    low_priority_employee_id = UUID("08e5b1b0-f9ee-45c8-9742-04e9da8857b8")
    low_priority_employee = EmployeeFactory.create(
        employee_id=low_priority_employee_id, tax_identifier=low_priority_tax_identifier
    )
    create_entities_for_testing(
        employee=low_priority_employee,
        employer=employer,
        queue_priority=1,
        tax_identifier=low_priority_tax_identifier,
    )
    high_priority_tax_identifier = TaxIdentifierFactory.create()
    high_priority_employee_id = UUID("08e5b1b0-f9ee-45c8-9742-04e9da8857b9")
    high_priority_employee = EmployeeFactory.create(
        employee_id=high_priority_employee_id, tax_identifier=high_priority_tax_identifier
    )
    create_entities_for_testing(
        employee=high_priority_employee,
        employer=employer,
        queue_priority=2,
        tax_identifier=high_priority_tax_identifier,
    )
    update_customer_names_step = UpdateCustomerNamesStep(
        test_db_session, test_db_session, record_process_limit=1
    )

    update_customer_names_step.run()

    update_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .one()
    )
    assert update_record.employee_id == low_priority_employee.employee_id
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 1,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_details")
def test_update_customer_names_step_corrects_invalid_customer_received_from_fineos(
    mock_update_customer_details,
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    mock_read_customer_details.return_value = Customer(
        classExtensionInformation=[
            ExtensionAttribute(name="MassachusettsID"),
            ExtensionAttribute(name="OutOfStateID"),
        ],
        customerAddress=CustomerAddress(address=Address(classExtensionInformation=[], country="")),
        dateOfBirth=None,
        firstName="John",
        lastName="Doe",
    )
    create_entities_for_testing()

    update_customer_names_step.run()

    fineos_web_id_ext = test_db_session.query(FINEOSWebIdExt).one_or_none()
    assert fineos_web_id_ext is not None
    update_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .one_or_none()
    )
    assert update_record is None
    customer_argument = mock_update_customer_details.call_args_list[0].args[1]
    assert customer_argument.customerAddress is None
    assert customer_argument.dateOfBirth == date(1753, 1, 1)

    for extension_attribute in customer_argument.classExtensionInformation:
        assert extension_attribute.stringValue == ""

    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 1,
        },
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_customer_details")
@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_details")
def test_update_customer_names_step_sends_back_valid_fields_received_from_fineos(
    mock_update_customer_details,
    mock_read_customer_details,
    test_db_session: Session,
    update_customer_names_step: UpdateCustomerNamesStep,
):
    customer_address = CustomerAddress(
        address=Address(
            addressLine1="123 Foo Street",
            addressLine4="Anytown",
            addressLine6="MA",
            classExtensionInformation=[],
            country="USA",
            postCode="12345-6789",
        )
    )
    date_of_birth = date(2000, 1, 1)
    extension_attributes = [
        ExtensionAttribute(name="MassachusettsID", stringValue="123456789"),
        ExtensionAttribute(name="OutOfStateID", stringValue="987654321"),
    ]
    mock_read_customer_details.return_value = Customer(
        classExtensionInformation=extension_attributes,
        customerAddress=customer_address,
        dateOfBirth=date_of_birth,
        firstName="John",
        lastName="Doe",
    )
    create_entities_for_testing()

    update_customer_names_step.run()

    fineos_web_id_ext = test_db_session.query(FINEOSWebIdExt).one_or_none()
    assert fineos_web_id_ext is not None
    update_record = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .one_or_none()
    )
    assert update_record is None
    customer_argument = mock_update_customer_details.call_args_list[0].args[1]
    assert customer_argument.customerAddress == customer_address
    assert customer_argument.dateOfBirth == date_of_birth
    assert customer_argument.classExtensionInformation == extension_attributes
    assert_metrics(
        update_customer_names_step,
        {
            UpdateCustomerNamesStep.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: 1,
            UpdateCustomerNamesStep.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_ERRORED_COUNT: 0,
            UpdateCustomerNamesStep.Metrics.RECORDS_PROCESSED_COUNT: 1,
        },
    )


def create_entities_for_testing(
    employee: Employee | None = None,
    employee_push_to_fineos_queue_action: str = "UPDATE",
    employer: Employer | None = None,
    queue_priority: int | None = None,
    should_create_employee_occupation: bool = True,
    should_create_fineos_web_id_ext: bool = True,
    should_reference_employee_in_queue: bool = True,
    should_reference_employer_in_queue: bool = False,
    tax_identifier: TaxIdentifier | None = None,
):
    if employee is None:
        tax_identifier = tax_identifier or TaxIdentifierFactory.create()
        employee = EmployeeFactory.create(tax_identifier=tax_identifier)

    employer = employer or EmployerFactory.create()

    if should_create_employee_occupation:
        EmployeeOccupationFactory.create(employee=employee, employer=employer)

    if queue_priority is None:
        EmployeePushToFineosQueueFactory.create(
            action=employee_push_to_fineos_queue_action,
            employee_id=employee.employee_id if should_reference_employee_in_queue else None,
            employer_id=employer.employer_id if should_reference_employer_in_queue else None,
        )
    else:
        EmployeePushToFineosQueueFactory.create(
            action=employee_push_to_fineos_queue_action,
            employee_id=employee.employee_id if should_reference_employee_in_queue else None,
            employer_id=employer.employer_id if should_reference_employer_in_queue else None,
            priority=queue_priority,
        )

    if should_create_fineos_web_id_ext:
        if tax_identifier is None:
            raise ValueError(
                "tax_identifier must be provided when creating a fineos_web_id_ext using an "
                "existing employee"
            )

        FINEOSWebIdExtFactory.create(
            employee_tax_identifier=tax_identifier.tax_identifier,
            employer_fein=employer.employer_fein,
        )


@pytest.fixture(autouse=True)
def update_customer_names_step(
    test_db_session: Session,
) -> UpdateCustomerNamesStep:
    return UpdateCustomerNamesStep(test_db_session, test_db_session)


@pytest.fixture(autouse=True)
def set_up_factories(initialize_factories_session):
    pass
