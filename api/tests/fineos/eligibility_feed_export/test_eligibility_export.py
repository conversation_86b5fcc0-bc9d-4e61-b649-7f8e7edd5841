import boto3
import moto
import pydantic
import pytest

from massgov.pfml.db.models.employees import EmployeePushToFineosQueue
from massgov.pfml.db.models.factories import (
    EmployeePushToFineosQueueFactory,
    WagesAndContributionsFactory,
)
from massgov.pfml.features.config.new_eligibility_feed import NewEligibilityFeedConfig
from massgov.pfml.fineos.eligibility_feed_export import eligibility_export


@moto.mock_aws()
@pytest.mark.parametrize("new_eligibility_feed", ["true", "false"])
def test_main_requires_non_empty_env_vars(
    test_db_session, monkeypatch, logging_fix, reset_aws_env_vars, new_eligibility_feed
):
    monkeypatch.setenv("ENABLED_NEW_FINEOS_ELIGIBILITY_FEED_EXPORT", new_eligibility_feed)

    # check that error is raised if none of the environment variables are set
    with pytest.raises(pydantic.ValidationError):
        eligibility_export.CreateEligibiltyExportTaskRunner().run_steps(
            test_db_session, test_db_session
        )

    # and also check that they are required to be non-empty
    monkeypatch.setenv("OUTPUT_DIRECTORY_PATH", "")
    monkeypatch.setenv("ARCHIVE_DIRECTORY_PATH", "")
    monkeypatch.setenv("TEMP_DIRECTORY_PATH", "")
    monkeypatch.setenv("FINEOS_AWS_IAM_ROLE_ARN", "")
    monkeypatch.setenv("FINEOS_AWS_IAM_ROLE_EXTERNAL_ID", "")

    with pytest.raises(pydantic.ValidationError):
        eligibility_export.CreateEligibiltyExportTaskRunner().run_steps(
            test_db_session, test_db_session
        )


@moto.mock_aws()
@pytest.mark.parametrize("new_eligibility_feed", ["true", "false"])
def test_main_success_fineos_location(
    local_test_db_session,
    local_initialize_factories_session,
    monkeypatch,
    logging_fix,
    reset_aws_env_vars,
    tmp_path,
    new_eligibility_feed,
):
    monkeypatch.setenv("ENABLED_NEW_FINEOS_ELIGIBILITY_FEED_EXPORT", new_eligibility_feed)

    mock_fineos_bucket_name = "fin-som-foo"
    mock_pfml_bucket_name = "pfml"

    fineos_s3_client = boto3.client("s3")
    fineos_s3_client.create_bucket(Bucket=mock_fineos_bucket_name)

    pfml_s3_client = boto3.client("s3")
    pfml_s3_client.create_bucket(Bucket=mock_pfml_bucket_name)

    WagesAndContributionsFactory.create()

    temp_output_dir = tmp_path / "tmp"
    temp_output_dir.mkdir(parents=True)

    monkeypatch.setenv("OUTPUT_DIRECTORY_PATH", f"s3://{mock_fineos_bucket_name}/test-output-dir")
    monkeypatch.setenv("ARCHIVE_DIRECTORY_PATH", f"s3://{mock_pfml_bucket_name}/test-archive-dir")
    monkeypatch.setenv("TEMP_DIRECTORY_PATH", str(temp_output_dir))
    monkeypatch.setenv(
        "FINEOS_AWS_IAM_ROLE_ARN",
        "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-foobar",
    )
    monkeypatch.setenv("FINEOS_AWS_IAM_ROLE_EXTERNAL_ID", "123")

    eligibility_export.CreateEligibiltyExportTaskRunner().run_steps(
        local_test_db_session, local_test_db_session
    )


@moto.mock_aws()
@pytest.mark.parametrize("new_eligibility_feed", ["true", "false"])
def test_main_success_non_fineos_location(
    local_test_db_session,
    local_initialize_factories_session,
    monkeypatch,
    logging_fix,
    reset_aws_env_vars,
    tmp_path,
    new_eligibility_feed,
):
    monkeypatch.setenv("ENABLED_NEW_FINEOS_ELIGIBILITY_FEED_EXPORT", new_eligibility_feed)

    WagesAndContributionsFactory.create()

    batch_output_dir = tmp_path / "absence-eligibility" / "upload"
    batch_output_dir.mkdir(parents=True)
    temp_output_dir = tmp_path / "tmp"
    temp_output_dir.mkdir(parents=True)

    monkeypatch.setenv("OUTPUT_DIRECTORY_PATH", str(tmp_path))
    monkeypatch.setenv("ARCHIVE_DIRECTORY_PATH", str(tmp_path))
    monkeypatch.setenv("TEMP_DIRECTORY_PATH", str(temp_output_dir))
    monkeypatch.setenv(
        "FINEOS_AWS_IAM_ROLE_ARN",
        "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-foobar",
    )
    monkeypatch.setenv("FINEOS_AWS_IAM_ROLE_EXTERNAL_ID", "123")

    eligibility_export.CreateEligibiltyExportTaskRunner().run_steps(
        local_test_db_session, local_test_db_session
    )


@moto.mock_aws()
@pytest.mark.parametrize("new_eligibility_feed", ["true", "false"])
def test_main_success_non_fineos_location_updates(
    local_test_db_session,
    local_initialize_factories_session,
    monkeypatch,
    logging_fix,
    reset_aws_env_vars,
    tmp_path,
    new_eligibility_feed,
):
    monkeypatch.setenv("ENABLED_NEW_FINEOS_ELIGIBILITY_FEED_EXPORT", new_eligibility_feed)
    new_eligibility_feed_config = NewEligibilityFeedConfig()

    wages = WagesAndContributionsFactory.create_batch(size=10)
    for wage in wages:
        if new_eligibility_feed_config.enabled_new_fineos_eligibility_feed_export:
            EmployeePushToFineosQueueFactory.create(
                employee_id=wage.employee_id,
                employer_id=wage.employer_id,
                action="UPDATE_NEW_EMPLOYER",
            )
        else:
            EmployeePushToFineosQueueFactory.create(
                employee_id=wage.employee_id, employer_id=wage.employer_id, action="INSERT"
            )

    batch_output_dir = tmp_path / "absence-eligibility" / "upload"
    batch_output_dir.mkdir(parents=True)
    temp_output_dir = tmp_path / "tmp"
    temp_output_dir.mkdir(parents=True)

    monkeypatch.setenv("OUTPUT_DIRECTORY_PATH", str(tmp_path))
    monkeypatch.setenv("ARCHIVE_DIRECTORY_PATH", str(tmp_path))
    monkeypatch.setenv("TEMP_DIRECTORY_PATH", str(temp_output_dir))
    monkeypatch.setenv(
        "FINEOS_AWS_IAM_ROLE_ARN",
        "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-foobar",
    )
    monkeypatch.setenv("FINEOS_AWS_IAM_ROLE_EXTERNAL_ID", "123")

    monkeypatch.setenv("ELIGIBILITY_FEED_MODE", "updates")
    monkeypatch.delenv("ELIGIBILITY_FEED_EXPORT_FILE_NUMBER_LIMIT", raising=False)

    assert local_test_db_session.query(EmployeePushToFineosQueue).count() == 10

    eligibility_export.CreateEligibiltyExportTaskRunner().run_steps(
        local_test_db_session, local_test_db_session
    )

    assert local_test_db_session.query(EmployeePushToFineosQueue).count() == 0


@moto.mock_aws()
@pytest.mark.parametrize("new_eligibility_feed", ["true", "false"])
def test_main_success_non_fineos_location_updates_with_limit(
    local_test_db_session,
    local_initialize_factories_session,
    monkeypatch,
    logging_fix,
    reset_aws_env_vars,
    tmp_path,
    new_eligibility_feed,
):
    monkeypatch.setenv("ENABLED_NEW_FINEOS_ELIGIBILITY_FEED_EXPORT", new_eligibility_feed)
    new_eligibility_feed_config = NewEligibilityFeedConfig()

    wages = WagesAndContributionsFactory.create_batch(size=10)
    for wage in wages:
        if new_eligibility_feed_config.enabled_new_fineos_eligibility_feed_export:
            EmployeePushToFineosQueueFactory.create(
                employee_id=wage.employee_id,
                employer_id=wage.employer_id,
                action="UPDATE_NEW_EMPLOYER",
            )
        else:
            EmployeePushToFineosQueueFactory.create(
                employee_id=wage.employee_id, employer_id=wage.employer_id, action="INSERT"
            )

    batch_output_dir = tmp_path / "absence-eligibility" / "upload"
    batch_output_dir.mkdir(parents=True)
    temp_output_dir = tmp_path / "tmp"
    temp_output_dir.mkdir(parents=True)

    monkeypatch.setenv("OUTPUT_DIRECTORY_PATH", str(tmp_path))
    monkeypatch.setenv("ARCHIVE_DIRECTORY_PATH", str(tmp_path))
    monkeypatch.setenv("TEMP_DIRECTORY_PATH", str(temp_output_dir))
    monkeypatch.setenv(
        "FINEOS_AWS_IAM_ROLE_ARN",
        "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-foobar",
    )
    monkeypatch.setenv("FINEOS_AWS_IAM_ROLE_EXTERNAL_ID", "123")

    monkeypatch.setenv("ELIGIBILITY_FEED_MODE", "updates")
    monkeypatch.setenv("ELIGIBILITY_FEED_EXPORT_FILE_NUMBER_LIMIT", "5")

    assert local_test_db_session.query(EmployeePushToFineosQueue).count() == 10

    eligibility_export.CreateEligibiltyExportTaskRunner().run_steps(
        local_test_db_session, local_test_db_session
    )

    assert local_test_db_session.query(EmployeePushToFineosQueue).count() == 5


@moto.mock_aws()
@pytest.mark.parametrize("new_eligibility_feed", ["true", "false"])
def test_main_success_non_fineos_location_list(
    local_test_db_session,
    local_initialize_factories_session,
    monkeypatch,
    logging_fix,
    reset_aws_env_vars,
    tmp_path,
    new_eligibility_feed,
):
    monkeypatch.setenv("ENABLED_NEW_FINEOS_ELIGIBILITY_FEED_EXPORT", new_eligibility_feed)

    wages = WagesAndContributionsFactory.create_batch(size=5)
    employer_ids = []
    for wage in wages:
        EmployeePushToFineosQueueFactory.create(
            employee_id=wage.employee_id, employer_id=wage.employer_id, action="INSERT"
        )
        employer_ids.append(wage.employer_id)

    batch_output_dir = tmp_path / "absence-eligibility" / "upload"
    batch_output_dir.mkdir(parents=True)
    temp_output_dir = tmp_path / "tmp"
    temp_output_dir.mkdir(parents=True)

    monkeypatch.setenv("OUTPUT_DIRECTORY_PATH", str(tmp_path))
    monkeypatch.setenv("ARCHIVE_DIRECTORY_PATH", str(tmp_path))
    monkeypatch.setenv("TEMP_DIRECTORY_PATH", str(temp_output_dir))
    monkeypatch.setenv(
        "FINEOS_AWS_IAM_ROLE_ARN",
        "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-foobar",
    )
    monkeypatch.setenv("FINEOS_AWS_IAM_ROLE_EXTERNAL_ID", "123")
    monkeypatch.setenv("ELIGIBILITY_FEED_MODE", "list")
    monkeypatch.delenv("ELIGIBILITY_FEED_EXPORT_FILE_NUMBER_LIMIT", raising=False)
    monkeypatch.setenv(
        "ELIGIBILITY_FEED_LIST_OF_EMPLOYER_IDS", ",".join([str(id) for id in employer_ids])
    )

    eligibility_export.CreateEligibiltyExportTaskRunner().run_steps(
        local_test_db_session, local_test_db_session
    )
