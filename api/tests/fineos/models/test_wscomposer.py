#
# Tests for massgov.pfml.fineos.models.wscomposer
#

import pytest

from massgov.pfml.fineos.models.wscomposer import OCOrganisation, OCOrganisationItem


class TestOCOrganisationItemOID:
    """Test OID functionality in OCOrganisationItem model."""

    @pytest.fixture
    def mock_oc_organisation_item_with_oid(self):
        """Fixture to create a mock OCOrganisationItem with OID."""
        return OCOrganisationItem(
            OID="PE:11528:0000001256",
            CustomerNo="12345",
            CorporateTaxNumber="*********",
            Name="Test Company",
            DoingBusinessAs="Test DBA",
            LegalBusinessName="Test Legal Name",
        )

    @pytest.fixture
    def mock_oc_organisation_item_without_oid(self):
        """Fixture to create a mock OCOrganisationItem without OID."""
        return OCOrganisationItem(
            CustomerNo="12345",
            CorporateTaxNumber="*********",
            Name="Test Company",
            DoingBusinessAs="Test DBA",
            LegalBusinessName="Test Legal Name",
        )

    def test_oc_organisation_item_with_oid(self, mock_oc_organisation_item_with_oid):
        """Test that OCOrganisationItem can be created with OID field."""
        org_item = mock_oc_organisation_item_with_oid
        assert org_item.OID == "PE:11528:0000001256"

    def test_oc_organisation_item_without_oid(self, mock_oc_organisation_item_without_oid):
        """Test that OCOrganisationItem defaults OID to None when not provided."""
        org_item = mock_oc_organisation_item_without_oid
        assert org_item.OID is None

    def test_oc_organisation_serialization_excludes_none_oid(
        self, mock_oc_organisation_item_without_oid
    ):
        """Test that None OID is excluded when serializing with exclude_none=True."""
        org_item = mock_oc_organisation_item_without_oid
        org = OCOrganisation(OCOrganisation=[org_item])
        serialized = org.dict(by_alias=True, exclude_none=True)

        assert "OID" not in serialized["OCOrganisation"][0]

    def test_oc_organisation_serialization_includes_oid(self, mock_oc_organisation_item_with_oid):
        """Test that OID is included when present."""
        org_item = mock_oc_organisation_item_with_oid
        org = OCOrganisation(OCOrganisation=[org_item])
        serialized = org.dict(by_alias=True)

        assert serialized["OCOrganisation"][0]["OID"] == "PE:11528:0000001256"

    def test_oc_organisation_serialization_includes_none_oid_when_exclude_none_false(
        self, mock_oc_organisation_item_without_oid
    ):
        """Test that None OID is included when exclude_none=False."""
        org_item = mock_oc_organisation_item_without_oid
        org = OCOrganisation(OCOrganisation=[org_item])
        serialized = org.dict(by_alias=True, exclude_none=False)

        assert "OID" in serialized["OCOrganisation"][0]
        assert serialized["OCOrganisation"][0]["OID"] is None
