from datetime import date

from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import (
    PFML_ContactDetails,
    PFML_EForm,
    PFML_EFormAttribute,
    PFML_EFormSummary,
    PFML_EmailAddress,
    PFML_ExtensionAttribute,
    PFML_ModelEnum,
    PFML_PhoneNumber,
)
from massgov.pfml.fineos.models.abstraction_layer.model_mapper import ModelMapper
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import ContactDetails
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import EForm as EForm_v22_5_1
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import (
    EFormAttribute as EFormAttribute_v22_5_1,
)
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import EFormSummary as EFormSummary_v22_5_1
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import <PERSON>ail<PERSON>dd<PERSON>, ExtensionAttribute
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import ModelEnum as ModelEnum_v22_5_1
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import PhoneNumber


class TestModelMapper:

    ### E F O R M S #################################################

    def test_map_eform_v22_5_1_to_pfml_eform(self):
        fineos_eform_v22_5_1 = EForm_v22_5_1(
            eformId=1,
            eformType="Example Form",
            eformAttributes=[
                EFormAttribute_v22_5_1(
                    booleanValue=True,
                    dateValue=date(2023, 12, 31),
                    decimalValue=12.34,
                    enumValue=ModelEnum_v22_5_1(
                        domainName="exampleDomain", instanceValue="exampleInstance"
                    ),
                    integerValue=123,
                    name="exampleName",
                    stringValue="exampleString",
                )
            ],
        )

        pfml_eform = ModelMapper.map_eform_v22_5_1_to_pfml_eform(fineos_eform_v22_5_1)

        assert pfml_eform.eformId == "1"
        assert pfml_eform.eformType == "Example Form"
        assert len(pfml_eform.eformAttributes) == 1
        assert pfml_eform.eformAttributes[0].booleanValue is True
        assert pfml_eform.eformAttributes[0].dateValue == date(2023, 12, 31)
        assert pfml_eform.eformAttributes[0].decimalValue == 12.34
        assert pfml_eform.eformAttributes[0].enumValue.domainName == "exampleDomain"
        assert pfml_eform.eformAttributes[0].enumValue.instanceValue == "exampleInstance"
        assert pfml_eform.eformAttributes[0].integerValue == 123
        assert pfml_eform.eformAttributes[0].name == "exampleName"
        assert pfml_eform.eformAttributes[0].stringValue == "exampleString"

    def test_map_pfml_eform_to_eform_v22_5_1(self):
        pfml_eform = PFML_EForm(
            eformId="2",
            eformType="Example Form 2",
            eformAttributes=[
                PFML_EFormAttribute(
                    booleanValue=False,
                    dateValue=date(2024, 12, 31),
                    decimalValue=23.45,
                    enumValue=PFML_ModelEnum(
                        domainName="exampleDomain 2", instanceValue="exampleInstance 2"
                    ),
                    integerValue=456,
                    name="exampleName 2",
                    stringValue="exampleString 2",
                )
            ],
        )

        fineos_eform_v22_5_1 = ModelMapper.map_pfml_eform_to_eform_v22_5_1(pfml_eform)

        assert fineos_eform_v22_5_1.eformId == 2
        assert fineos_eform_v22_5_1.eformType == "Example Form 2"
        assert len(fineos_eform_v22_5_1.eformAttributes) == 1
        assert fineos_eform_v22_5_1.eformAttributes[0].booleanValue is False
        assert fineos_eform_v22_5_1.eformAttributes[0].dateValue == date(2024, 12, 31)
        assert fineos_eform_v22_5_1.eformAttributes[0].decimalValue == 23.45
        assert fineos_eform_v22_5_1.eformAttributes[0].enumValue.domainName == "exampleDomain 2"
        assert (
            fineos_eform_v22_5_1.eformAttributes[0].enumValue.instanceValue == "exampleInstance 2"
        )
        assert fineos_eform_v22_5_1.eformAttributes[0].integerValue == 456
        assert fineos_eform_v22_5_1.eformAttributes[0].name == "exampleName 2"
        assert fineos_eform_v22_5_1.eformAttributes[0].stringValue == "exampleString 2"

    def test_map_eform_summary_v22_5_1_to_pfml_eform_summary(self):
        eform_summary_v22_5_1 = EFormSummary_v22_5_1(
            effectiveDateFrom=date(2023, 1, 1),
            effectiveDateTo=date(2023, 12, 31),
            eformId=123,
            eformType="TypeA",
            eformTypeId="PE-00012-0000001234",
        )

        pfml_eform_summary = ModelMapper.map_eform_summary_v22_5_1_to_pfml_eform_summary(
            eform_summary_v22_5_1
        )

        assert pfml_eform_summary.effectiveDateFrom == eform_summary_v22_5_1.effectiveDateFrom
        assert pfml_eform_summary.effectiveDateTo == eform_summary_v22_5_1.effectiveDateTo
        assert pfml_eform_summary.eformId == "123"
        assert pfml_eform_summary.eformType == eform_summary_v22_5_1.eformType
        assert pfml_eform_summary.eformTypeId == eform_summary_v22_5_1.eformTypeId

    def test_map_pfml_eform_summary_to_eform_summary_v22_5_1(self):
        pfml_eform_summary = PFML_EFormSummary(
            effectiveDateFrom=date(2023, 1, 1),
            effectiveDateTo=date(2023, 12, 31),
            eformId="123",
            eformType="TypeA",
            eformTypeId="PE-00012-0000001234",
        )

        eform_summary_v22_5_1 = ModelMapper.map_pfml_eform_summary_to_eform_summary_v22_5_1(
            pfml_eform_summary
        )

        assert eform_summary_v22_5_1.effectiveDateFrom == pfml_eform_summary.effectiveDateFrom
        assert eform_summary_v22_5_1.effectiveDateTo == pfml_eform_summary.effectiveDateTo
        assert eform_summary_v22_5_1.eformId == 123
        assert eform_summary_v22_5_1.eformType == pfml_eform_summary.eformType
        assert eform_summary_v22_5_1.eformTypeId == pfml_eform_summary.eformTypeId

    ### C U S T O M E R ### D E T A I L S #################################################

    def test_map_contact_details_v22_5_1_to_pfml_contact_details(self):
        fineos_contact_details = ContactDetails(
            emailAddresses=[
                EmailAddress(
                    classExtensionInformation=[
                        ExtensionAttribute(
                            booleanValue=True,
                            dateOnlyValue=date(2023, 12, 31),
                            decimalValue=12.34,
                            enumValue=ModelEnum_v22_5_1(
                                domainName="exampleDomain", instanceValue="exampleInstance"
                            ),
                            integerValue=123,
                            name="exampleName",
                            stringValue="exampleString",
                        )
                    ],
                    emailAddress="<EMAIL>",
                    emailAddressType="Work",
                    id=1,
                    preferred=True,
                )
            ],
            phoneNumbers=[
                PhoneNumber(
                    areaCode="123",
                    classExtensionInformation=[
                        ExtensionAttribute(
                            booleanValue=False,
                            dateOnlyValue=date(2022, 11, 30),
                            decimalValue=56.78,
                            enumValue=ModelEnum_v22_5_1(
                                domainName="anotherDomain", instanceValue="anotherInstance"
                            ),
                            integerValue=456,
                            name="anotherName",
                            stringValue="anotherString",
                        )
                    ],
                    id=2,
                    intCode="456",
                    phoneNumberType="Mobile",
                    preferred=False,
                    telephoneNo="7890123",
                )
            ],
            preferredContactMethod=1,
        )

        pfml_contact_details = ModelMapper.map_contact_details_v22_5_1_to_pfml_contact_details(
            fineos_contact_details
        )

        assert len(pfml_contact_details.emailAddresses) == 1
        assert pfml_contact_details.emailAddresses[0].emailAddress == "<EMAIL>"
        assert pfml_contact_details.emailAddresses[0].emailAddressType == "Work"
        assert pfml_contact_details.emailAddresses[0].id == 1
        assert pfml_contact_details.emailAddresses[0].preferred is True
        assert len(pfml_contact_details.emailAddresses[0].classExtensionInformation) == 1
        assert (
            pfml_contact_details.emailAddresses[0].classExtensionInformation[0].booleanValue is True
        )
        assert pfml_contact_details.emailAddresses[0].classExtensionInformation[
            0
        ].dateOnlyValue == date(2023, 12, 31)
        assert (
            pfml_contact_details.emailAddresses[0].classExtensionInformation[0].decimalValue
            == 12.34
        )
        assert (
            pfml_contact_details.emailAddresses[0].classExtensionInformation[0].enumValue.domainName
            == "exampleDomain"
        )
        assert (
            pfml_contact_details.emailAddresses[0]
            .classExtensionInformation[0]
            .enumValue.instanceValue
            == "exampleInstance"
        )
        assert (
            pfml_contact_details.emailAddresses[0].classExtensionInformation[0].integerValue == 123
        )
        assert (
            pfml_contact_details.emailAddresses[0].classExtensionInformation[0].name
            == "exampleName"
        )
        assert (
            pfml_contact_details.emailAddresses[0].classExtensionInformation[0].stringValue
            == "exampleString"
        )

        assert len(pfml_contact_details.phoneNumbers) == 1
        assert pfml_contact_details.phoneNumbers[0].areaCode == "123"
        assert pfml_contact_details.phoneNumbers[0].id == 2
        assert pfml_contact_details.phoneNumbers[0].intCode == "456"
        assert pfml_contact_details.phoneNumbers[0].phoneNumberType == "Mobile"
        assert pfml_contact_details.phoneNumbers[0].preferred is False
        assert pfml_contact_details.phoneNumbers[0].telephoneNo == "7890123"
        assert len(pfml_contact_details.phoneNumbers[0].classExtensionInformation) == 1
        assert (
            pfml_contact_details.phoneNumbers[0].classExtensionInformation[0].booleanValue is False
        )
        assert pfml_contact_details.phoneNumbers[0].classExtensionInformation[
            0
        ].dateOnlyValue == date(2022, 11, 30)
        assert (
            pfml_contact_details.phoneNumbers[0].classExtensionInformation[0].decimalValue == 56.78
        )
        assert (
            pfml_contact_details.phoneNumbers[0].classExtensionInformation[0].enumValue.domainName
            == "anotherDomain"
        )
        assert (
            pfml_contact_details.phoneNumbers[0]
            .classExtensionInformation[0]
            .enumValue.instanceValue
            == "anotherInstance"
        )
        assert pfml_contact_details.phoneNumbers[0].classExtensionInformation[0].integerValue == 456
        assert (
            pfml_contact_details.phoneNumbers[0].classExtensionInformation[0].name == "anotherName"
        )
        assert (
            pfml_contact_details.phoneNumbers[0].classExtensionInformation[0].stringValue
            == "anotherString"
        )

        assert pfml_contact_details.preferredContactMethod == 1

    def test_map_pfml_contact_details_to_contact_details_v22_5_1(self):
        pfml_contact_details = PFML_ContactDetails(
            emailAddresses=[
                PFML_EmailAddress(
                    classExtensionInformation=[
                        PFML_ExtensionAttribute(
                            booleanValue=True,
                            dateOnlyValue=date(2023, 12, 31),
                            decimalValue=12.34,
                            enumValue=PFML_ModelEnum(
                                domainName="exampleDomain", instanceValue="exampleInstance"
                            ),
                            integerValue=123,
                            name="exampleName",
                            stringValue="exampleString",
                        )
                    ],
                    emailAddress="<EMAIL>",
                    emailAddressType="Work",
                    id=1,
                    preferred=True,
                )
            ],
            phoneNumbers=[
                PFML_PhoneNumber(
                    areaCode="123",
                    classExtensionInformation=[
                        PFML_ExtensionAttribute(
                            booleanValue=False,
                            dateOnlyValue=date(2022, 11, 30),
                            decimalValue=56.78,
                            enumValue=PFML_ModelEnum(
                                domainName="anotherDomain", instanceValue="anotherInstance"
                            ),
                            integerValue=456,
                            name="anotherName",
                            stringValue="anotherString",
                        )
                    ],
                    id=2,
                    intCode="456",
                    phoneNumberType="Mobile",
                    preferred=False,
                    telephoneNo="7890123",
                )
            ],
            preferredContactMethod=1,
        )

        fineos_contact_details = ModelMapper.map_pfml_contact_details_to_contact_details_v22_5_1(
            pfml_contact_details
        )

        assert len(fineos_contact_details.emailAddresses) == 1
        assert fineos_contact_details.emailAddresses[0].emailAddress == "<EMAIL>"
        assert fineos_contact_details.emailAddresses[0].emailAddressType == "Work"
        assert fineos_contact_details.emailAddresses[0].id == 1
        assert fineos_contact_details.emailAddresses[0].preferred is True
        assert len(fineos_contact_details.emailAddresses[0].classExtensionInformation) == 1
        assert (
            fineos_contact_details.emailAddresses[0].classExtensionInformation[0].booleanValue
            is True
        )
        assert fineos_contact_details.emailAddresses[0].classExtensionInformation[
            0
        ].dateOnlyValue == date(2023, 12, 31)
        assert (
            fineos_contact_details.emailAddresses[0].classExtensionInformation[0].decimalValue
            == 12.34
        )
        assert (
            fineos_contact_details.emailAddresses[0]
            .classExtensionInformation[0]
            .enumValue.domainName
            == "exampleDomain"
        )
        assert (
            fineos_contact_details.emailAddresses[0]
            .classExtensionInformation[0]
            .enumValue.instanceValue
            == "exampleInstance"
        )
        assert (
            fineos_contact_details.emailAddresses[0].classExtensionInformation[0].integerValue
            == 123
        )
        assert (
            fineos_contact_details.emailAddresses[0].classExtensionInformation[0].name
            == "exampleName"
        )
        assert (
            fineos_contact_details.emailAddresses[0].classExtensionInformation[0].stringValue
            == "exampleString"
        )

        assert len(fineos_contact_details.phoneNumbers) == 1
        assert fineos_contact_details.phoneNumbers[0].areaCode == "123"
        assert fineos_contact_details.phoneNumbers[0].id == 2
        assert fineos_contact_details.phoneNumbers[0].intCode == "456"
        assert fineos_contact_details.phoneNumbers[0].phoneNumberType == "Mobile"
        assert fineos_contact_details.phoneNumbers[0].preferred is False
        assert fineos_contact_details.phoneNumbers[0].telephoneNo == "7890123"
        assert len(fineos_contact_details.phoneNumbers[0].classExtensionInformation) == 1
        assert (
            fineos_contact_details.phoneNumbers[0].classExtensionInformation[0].booleanValue
            is False
        )
        assert fineos_contact_details.phoneNumbers[0].classExtensionInformation[
            0
        ].dateOnlyValue == date(2022, 11, 30)
        assert (
            fineos_contact_details.phoneNumbers[0].classExtensionInformation[0].decimalValue
            == 56.78
        )
        assert (
            fineos_contact_details.phoneNumbers[0].classExtensionInformation[0].enumValue.domainName
            == "anotherDomain"
        )
        assert (
            fineos_contact_details.phoneNumbers[0]
            .classExtensionInformation[0]
            .enumValue.instanceValue
            == "anotherInstance"
        )
        assert (
            fineos_contact_details.phoneNumbers[0].classExtensionInformation[0].integerValue == 456
        )
        assert (
            fineos_contact_details.phoneNumbers[0].classExtensionInformation[0].name
            == "anotherName"
        )
        assert (
            fineos_contact_details.phoneNumbers[0].classExtensionInformation[0].stringValue
            == "anotherString"
        )

        assert fineos_contact_details.preferredContactMethod == 1
