import datetime

import massgov.pfml.dor.util.employer_exemption_dor_export as employer_exemptions_dor_export
from massgov.pfml.db.lookup_data.employer_exemptions import EmployerExemptionApplicationStatus
from massgov.pfml.db.models.factories import (
    EmployerExemptionApplicationFactory,
    PhoneFactory,
    UserLeaveAdministratorFactory,
)


def test_get_modified_employer_exemptions(
    test_db_session, initialize_factories_session
):  # Call the method

    EmployerExemptionApplicationFactory.create(
        employer_exemption_application_status_id=EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
        synced_to_dor=False,
    )
    EmployerExemptionApplicationFactory.create(
        employer_exemption_application_status_id=EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
        synced_to_dor=True,
    )
    EmployerExemptionApplicationFactory.create(
        employer_exemption_application_status_id=EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id,
        synced_to_dor=False,
    )
    result = employer_exemptions_dor_export.get_modified_employer_exemptions(test_db_session)

    # Assert that the query was called with the expected filters
    assert len(result) == 1


def test_format_employer_exemption_line_new(
    test_db_session, initialize_factories_session
):  # set up the exemption fields for matching against the test
    leave_admin = UserLeaveAdministratorFactory.create()
    phone = PhoneFactory.create(
        phone_number="**********",
    )
    employer_exemption = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
        employer_exemption_application_status_id=EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
        synced_to_dor=False,
        insurance_plan_effective_at="2025-01-01",
        insurance_plan_expires_at="2026-04-01",
        has_medical_exemption=True,
        has_family_exemption=False,
        is_self_insured_plan=False,
        insurance_provider_id=3,
        insurance_plan_id=7,
        average_workforce_count=400,
        contact_first_name="Rumple",
        contact_last_name="Stiltskin",
        contact_title="Sir",
        contact_phone_id=phone.phone_id,
        contact_email_address="<EMAIL>",
    )

    # Run the string formatting function
    result = employer_exemptions_dor_export.format_employer_exemption_line(
        test_db_session, employer_exemption
    )

    # Assert that the query was called with the expected filters
    assert len(result) == 1147
    assert result[0:14] == "N" + leave_admin.employer.employer_fein + "FEIN"
    assert result[14:22] == "20250101"
    assert result[22:30] == "20260401"
    assert result[30] == "1"
    assert result[31:131] == "05 DBL002P 22 05 20".ljust(100, " ")
    assert result[131] == "0"
    assert result[232:242] == "Private   "
    assert result[497:507] == "**********"


def test_format_employer_exemption_line_updated(
    test_db_session, initialize_factories_session
):  # set up the exemption fields for matching against the test
    leave_admin = UserLeaveAdministratorFactory.create()
    phone = PhoneFactory.create(
        phone_number="**********",
    )
    employer_exemption = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
        employer_exemption_application_status_id=EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
        synced_to_dor=False,
        insurance_plan_effective_at="2025-01-01",
        insurance_plan_expires_at=None,  # Simulate an exemption that has no expiration date
        has_medical_exemption=True,
        has_family_exemption=False,
        is_self_insured_plan=False,
        insurance_provider_id=3,
        insurance_plan_id=7,
        average_workforce_count=400,
        contact_first_name="Rumple",
        contact_last_name="Stiltskin",
        contact_title="Sir",
        contact_phone_id=phone.phone_id,
        contact_email_address="<EMAIL>",
        synced_to_dor_at=datetime.datetime.now(),  # Simulate an updated exemption,
    )

    # Run the string formatting function
    result = employer_exemptions_dor_export.format_employer_exemption_line(
        test_db_session, employer_exemption
    )

    # Assert that the query was called with the expected filters
    assert len(result) == 1147
    assert result[0:14] == "C" + leave_admin.employer.employer_fein + "FEIN"
    assert result[14:22] == "20250101"
    assert result[22:30] == "99991231"  # DOR uses 99991231 for no expiration date
    assert result[30] == "1"
    assert result[131] == "0"
    assert result[232:242] == "Private   "
    assert result[497:507] == "**********"
