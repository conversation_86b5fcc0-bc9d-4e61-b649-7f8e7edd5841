import os

import pytest

from massgov.pfml.dor.employer_exemptions.employer_exemptions_moveit_step import (
    EmployerExemptionsMoveITStep,
)
from massgov.pfml.util.batch.log import LogEntry
from massgov.pfml.util.datetime import get_now_us_eastern

now = get_now_us_eastern()


@pytest.fixture
def exemptions_file_path(tmp_path):
    file_path = tmp_path / f"employer_exemptions_export_{now.strftime('%Y%m%d')}.txt"
    # Create the actual file with test content
    file_path.write_text("H0000000001\nN123456789FEIN202501012026040110Private   ...\n")
    return str(file_path)


@pytest.fixture
def moveit_output_path(tmp_path):
    output_path = tmp_path / "dor" / "send"
    output_path.mkdir(parents=True)
    return str(output_path)


@pytest.fixture
def employer_exemptions_moveit_step(test_db_session, exemptions_file_path, moveit_output_path):
    step = EmployerExemptionsMoveITStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        exemptions_file_path=exemptions_file_path,
        moveit_dor_file_path=moveit_output_path,
        exemptions_file_name="employer_exemptions_export.txt",
    )
    step._log_entry = LogEntry(test_db_session, "")
    return step


def test_employer_exemptions_moveit_step_copies_file(
    test_db_session, employer_exemptions_moveit_step, exemptions_file_path, moveit_output_path
):
    """Test that the MoveIT step successfully copies the exemptions file to the output directory."""
    # Verify preconditions
    assert os.path.exists(exemptions_file_path)
    assert os.path.isdir(moveit_output_path)

    # Run the step
    employer_exemptions_moveit_step.run_step()

    # Verify the file was copied/moved to the output directory
    output_files = list(os.listdir(moveit_output_path))
    assert len(output_files) == 1

    copied_file_path = os.path.join(moveit_output_path, output_files[0])
    with open(copied_file_path, "r") as f:
        content = f.read()

    # Verify content matches original
    assert "H0000000001" in content
    assert "N123456789FEIN" in content


def test_employer_exemptions_moveit_step_configuration(
    employer_exemptions_moveit_step, exemptions_file_path, moveit_output_path
):
    """Test that the MoveIT step is configured correctly."""
    step = employer_exemptions_moveit_step

    assert step.exemptions_file_path == exemptions_file_path
    assert step.moveit_dor_file_path == moveit_output_path
    assert step.moveit_dor_file_path.endswith("send")
    assert step._log_entry is not None


def test_employer_exemptions_moveit_step_handles_missing_file(
    test_db_session, moveit_output_path, tmp_path
):
    """Test that the step handles missing exemptions file gracefully."""
    missing_file_path = str(tmp_path / "nonexistent_file.txt")

    step = EmployerExemptionsMoveITStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        exemptions_file_path=missing_file_path,
        moveit_dor_file_path=moveit_output_path,
        exemptions_file_name="employer_exemptions_export.txt",
    )
    step._log_entry = LogEntry(test_db_session, "")

    # This should raise an exception or handle the missing file appropriately
    with pytest.raises((FileNotFoundError, IOError)):
        step.run_step()
