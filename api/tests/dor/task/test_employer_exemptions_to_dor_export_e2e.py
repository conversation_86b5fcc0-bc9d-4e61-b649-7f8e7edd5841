import json
import logging  # noqa: B1
import os

import pytest
from freezegun import freeze_time

import massgov.pfml.util.logging
from massgov.pfml import features
from massgov.pfml.db.lookup_data.employer_exemptions import EmployerExemptionApplicationStatus
from massgov.pfml.db.models.employees import ImportLog
from massgov.pfml.db.models.factories import (
    EmployerExemptionApplicationFactory,
    ImportLogFactory,
    PhoneFactory,
    UserLeaveAdministratorFactory,
)
from massgov.pfml.dor.importer.dor_extract_config import DorS3Config
from massgov.pfml.dor.task.employer_exemptions_to_dor_export import (
    EmployerExemptionsDORExporterTaskRunner,
)

logger = massgov.pfml.util.logging.get_logger(f"massgov.{__name__}")


# needed to initialize the feature config before code being tested checks feature flags
@pytest.fixture(autouse=True)
def use_initialize_feature_config(initialize_feature_config):
    return initialize_feature_config


@pytest.fixture
def mock_dor_s3_config(tmp_path):
    """Mock DOR S3 configuration using test specific temporary directory, isolated from any other test."""
    dor_output_path = tmp_path / "dor" / "send"
    dor_output_path.mkdir(parents=True)

    return DorS3Config(
        dor_output_folder_path=str(dor_output_path),
        # Add other required config parameters as needed
    )


def set_employer_exemptions_env_vars(monkeypatch):
    """Set environment variables needed for employer exemptions export."""
    monkeypatch.setenv("EMPLOYER_EXEMPTIONS_EXPORT_FILE_CREATION", True)


def create_test_employer_exemption(test_db_session):
    """Create a test employer exemption application for export."""
    leave_admin = UserLeaveAdministratorFactory.create()
    phone = PhoneFactory.create(phone_number="**********")

    employer_exemption = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
        employer_exemption_application_status_id=EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
        synced_to_dor=False,
        insurance_plan_effective_at="2025-01-01",
        insurance_plan_expires_at="2026-04-01",
        has_medical_exemption=True,
        has_family_exemption=False,
        is_self_insured_plan=False,
        insurance_provider_id=3,
        insurance_plan_id=7,
        average_workforce_count=400,
        contact_first_name="Rumple",
        contact_last_name="Stiltskin",
        contact_title="Sir",
        contact_phone_id=phone.phone_id,
        contact_email_address="<EMAIL>",
    )

    # Create associated import log
    import_log = ImportLogFactory.create(
        source="EmployerExemptionsExportStep",
        import_type="Employer Exemptions Export",
        report=json.dumps({"records_count": 1}),
    )
    employer_exemption.import_log_id = import_log.import_log_id

    test_db_session.add(employer_exemption)
    test_db_session.commit()

    return employer_exemption, import_log


@pytest.mark.timeout(30)
def test_employer_exemptions_to_dor_export_e2e(
    initialize_factories_session,
    test_db_session,
    mock_dor_s3_config,
    monkeypatch,
):
    """End-to-end test for the employer exemptions DOR export task runner."""

    with freeze_time("2025-06-25 02:00:00") as frozen_time:
        set_employer_exemptions_env_vars(monkeypatch)

        # Enable the feature flag
        features.initialize()
        features.get_config().employer_exemptions.enable_employer_exemptions_dor_data_transfer = (
            True
        )

        # Create test data
        employer_exemption, import_log = create_test_employer_exemption(test_db_session)

        # Verify initial state
        assert employer_exemption.synced_to_dor is False
        assert employer_exemption.has_medical_exemption is True
        assert employer_exemption.has_family_exemption is False

        # Run the task runner
        runner = EmployerExemptionsDORExporterTaskRunner(
            s3_config=mock_dor_s3_config, input_args=["--steps", "ALL"]
        )
        runner.run_steps(test_db_session, test_db_session)

        # Verify the export file was created in the output directory
        output_files = list(os.listdir(mock_dor_s3_config.dor_output_folder_path))
        assert len(output_files) == 1

        export_file_path = os.path.join(mock_dor_s3_config.dor_output_folder_path, output_files[0])
        assert os.path.exists(export_file_path)

        # Verify file content
        with open(export_file_path, "r") as f:
            content = f.readlines()

        # Should have header and one exemption record
        assert len(content) == 2

        # Verify header format
        header = content[0].strip()
        assert header.startswith("H")
        assert len(header) == 11  # Header format: H + 10 digit record count

        # Verify exemption record format
        exemption_line = content[1].replace("\n", "")
        assert exemption_line.startswith("N")  # New record indicator
        assert employer_exemption.employer.employer_fein in exemption_line
        assert "FEIN" in exemption_line
        assert "20250101" in exemption_line  # Effective date
        assert "20260401" in exemption_line  # Expiration date
        assert exemption_line[30] == "1"  # Medical exemption flag
        assert exemption_line[131] == "0"  # Family exemption flag
        assert "**********" in exemption_line  # Workforce count

        # Verify the record is properly formatted (should be 1147 characters)
        assert len(exemption_line) == 1147

        # Verify import log was created for the export
        export_logs = (
            test_db_session.query(ImportLog)
            .filter(ImportLog.source == "EmployerExemptionsExportStep")
            .all()
        )
        assert len(export_logs) >= 1

        # Test running again with no changes - should not create duplicate files
        frozen_time.tick(3600)  # Advance time by 1 hour

        runner.run_steps(test_db_session, test_db_session)

        # Should still only have one file (or a new one with different timestamp)
        output_files_after = list(os.listdir(mock_dor_s3_config.dor_output_folder_path))
        # The exact behavior depends on implementation - could be 1 file (overwrite) or 2 files (new timestamp)
        assert len(output_files_after) >= 1


def test_employer_exemptions_to_dor_export_disabled_feature_flag(
    initialize_factories_session, test_db_session, mock_dor_s3_config, monkeypatch, caplog
):
    """Test that export is skipped when feature flag is disabled."""

    set_employer_exemptions_env_vars(monkeypatch)

    # Disable the feature flag
    features.initialize()
    features.get_config().employer_exemptions.enable_employer_exemptions_dor_data_transfer = False

    caplog.set_level(logging.INFO)  # noqa: B1

    # Create test data
    create_test_employer_exemption(test_db_session)

    # Run the task runner
    runner = EmployerExemptionsDORExporterTaskRunner(
        s3_config=mock_dor_s3_config, input_args=["--steps", "ALL"]
    )
    runner.run_steps(test_db_session, test_db_session)

    # Verify no files were created
    output_files = list(os.listdir(mock_dor_s3_config.dor_output_folder_path))
    assert len(output_files) == 0

    # Verify the skip message was logged
    assert "Employer Exemptions Export to DOR is disabled" in caplog.text

    caplog.clear()


def test_employer_exemptions_to_dor_export_individual_steps(
    initialize_factories_session,
    test_db_session,
    mock_dor_s3_config,
    monkeypatch,
):
    """Test running individual steps of the export process."""

    set_employer_exemptions_env_vars(monkeypatch)
    features.initialize()
    features.get_config().employer_exemptions.enable_employer_exemptions_dor_data_transfer = True

    create_test_employer_exemption(test_db_session)

    # Test only file creation step
    runner = EmployerExemptionsDORExporterTaskRunner(
        s3_config=mock_dor_s3_config, input_args=["--steps", "employer-exemptions-export"]
    )
    runner.run_steps(test_db_session, test_db_session)

    # File should be created in temp directory but not moved to output
    assert os.path.exists(runner.exemptions_file_path)
    output_files = list(os.listdir(mock_dor_s3_config.dor_output_folder_path))
    assert len(output_files) == 0

    # Test only move step (requires existing file)
    runner2 = EmployerExemptionsDORExporterTaskRunner(
        s3_config=mock_dor_s3_config, input_args=["--steps", "employer-exemptions-export-moveit"]
    )
    # Set the file path to the existing temp file
    runner2.exemptions_file_path = runner.exemptions_file_path
    runner2.run_steps(test_db_session, test_db_session)

    # File should now be in output directory
    output_files = list(os.listdir(mock_dor_s3_config.dor_output_folder_path))
    assert len(output_files) == 1


def test_employer_exemptions_to_dor_export_no_data(
    initialize_factories_session,
    test_db_session,
    mock_dor_s3_config,
    monkeypatch,
):
    """Test export behavior when there are no exemptions to export."""

    set_employer_exemptions_env_vars(monkeypatch)
    features.initialize()
    features.get_config().employer_exemptions.enable_employer_exemptions_dor_data_transfer = True

    # Don't create any test data

    # Run the task runner
    runner = EmployerExemptionsDORExporterTaskRunner(
        s3_config=mock_dor_s3_config, input_args=["--steps", "ALL"]
    )
    runner.run_steps(test_db_session, test_db_session)

    # Should still create a file but with only header (0 records)
    output_files = list(os.listdir(mock_dor_s3_config.dor_output_folder_path))
    assert len(output_files) == 1

    export_file_path = os.path.join(mock_dor_s3_config.dor_output_folder_path, output_files[0])
    with open(export_file_path, "r") as f:
        content = f.read().strip()

    # Should only have header line with 0 records
    lines = content.split("\n") if content else []
    assert len(lines) == 1
    assert lines[0] == "H0000000000"  # Header with 0 record count
