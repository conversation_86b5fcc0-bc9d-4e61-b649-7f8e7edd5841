import json
import os
from datetime import date
from unittest import mock

import sqlalchemy

from massgov.pfml.db.execute_sql import (
    ExecuteSqlReport,
    execute_sql_statement,
    execute_sql_statement_file_path,
    execute_sql_statement_s3,
    get_file_path,
    get_file_target,
    run_sql,
)
from massgov.pfml.db.models.import_log import ImportLog
from massgov.pfml.util.batch.log import create_log_entry


def write_file(file_path, text: str = ""):
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w") as file:
        if text:
            file.write(text)


class TestExportLeaveAdminsCreatedExecuteSql:
    args = mock.Mock()
    args.s3_bucket = "local_s3"
    args.s3_output = "api_db"
    args.use_date = False
    args.job_name = "ExportLeaveAdminsCreated"
    args.sql = ['SELECT email_address FROM "user"']

    def test_get_file_target(self):
        count = 1

        # w/o use_date
        target = get_file_target(self.args, count)
        assert target == f"{self.args.s3_output}-query{count}.csv"

        # w use_date
        self.args.use_date = True
        target = get_file_target(self.args, count)
        assert target == f"{self.args.s3_output}/{date.today()}/query{count}.csv"

    def test_get_file_path_for_local_s3(self):
        target = f"{self.args.s3_output}-query1.csv"
        file_path = get_file_path(self.args.s3_bucket, target)
        assert file_path == f"{self.args.s3_bucket}/{target}"

    def test_get_file_path_for_s3(self):
        target = f"{self.args.s3_output}-query1.csv"
        file_path = get_file_path("pfml", target)
        assert file_path == f"s3://pfml/{target}"

    def test_execute_sql_statement_s3(self, test_db_session):
        target = f"{self.args.s3_output}-query1.csv"
        with mock.patch(
            "massgov.pfml.db.execute_sql.get_file_path"
        ) as mock_get_file_path, mock.patch(
            "massgov.pfml.db.execute_sql.execute_sql_statement_file_path"
        ) as mock_execute_sql_statement_file_path:
            mock_get_file_path.return_value = f"{self.args.s3_bucket}/{target}"
            mock_execute_sql_statement_file_path.return_value = None
            execute_sql_statement_s3(test_db_session, self.args.sql[0], self.args.s3_bucket, target)
            mock_get_file_path.assert_called_once_with(self.args.s3_bucket, target)

    def test_execute_sql_statement(self, test_db_session):
        limit = 1
        result = test_db_session.execute(sqlalchemy.text(self.args.sql[0]))
        with mock.patch(
            "massgov.pfml.db.execute_sql.output_result_rows"
        ) as mock_output_result_rows:
            mock_output_result_rows.return_value = None
            execute_sql_statement(test_db_session, self.args.sql[0], limit)
            mock_output_result_rows.assert_called_once()

            actual_args, _ = mock_output_result_rows.call_args

            assert actual_args[0].keys() == result.keys()
            assert actual_args[0].fetchall() == result.fetchall()
            assert actual_args[1] == limit

    def test_execute_sql_statement_file_path(self, test_db_session):
        file_path = f"{self.args.s3_bucket}/{self.args.s3_output}-query1.csv"
        with mock.patch("massgov.pfml.db.execute_sql.file_util.open_stream") as mock_open_stream:
            mock_open_stream.return_value = mock.MagicMock()
            execute_sql_statement_file_path(test_db_session, self.args.sql[0], file_path)
            mock_open_stream.assert_called_once_with(file_path, mode="w")

    def test_run_sql(self, test_db_session):
        file_path = f"{self.args.s3_bucket}/{self.args.s3_output}/{date.today()}/query1.csv"
        write_file(file_path)
        report = ExecuteSqlReport()
        log_entry = create_log_entry(test_db_session, "execute-sql", "", "execute-sql")
        run_sql(test_db_session, log_entry, self.args, report)

        import_logs = test_db_session.query(ImportLog).all()
        assert len(import_logs) == 1
        assert import_logs[0].source == self.args.job_name
        assert import_logs[0].source_context == "execute-sql"
        assert import_logs[0].status == "success"
        report = json.loads(import_logs[0].report)
        assert report["total_statements_executed_count"] == 1
        assert report["output_file"] == file_path


class TestExportPsdReportExecuteSql:
    args = mock.Mock()
    args.s3_bucket = "local_s3"
    args.s3_output = "dml_report"
    args.use_date = True
    args.job_name = "ExportPsdReport"
    args.sql = ["select claim_id, fineos_absence_id, updated_at FROM claim"]

    def test_run_sql(self, test_db_session):
        file_path = f"{self.args.s3_bucket}/{self.args.s3_output}/{date.today()}/query1.csv"
        write_file(file_path)
        report = ExecuteSqlReport()
        log_entry = create_log_entry(test_db_session, "execute-sql", "", "execute-sql")
        run_sql(test_db_session, log_entry, self.args, report)

        import_logs = test_db_session.query(ImportLog).all()
        assert len(import_logs) == 1
        assert import_logs[0].source == self.args.job_name
        assert import_logs[0].source_context == "execute-sql"
        assert import_logs[0].status == "success"
        report = json.loads(import_logs[0].report)
        assert report["total_statements_executed_count"] == 1
        assert report["output_file"] == file_path


class TestExport60DayCommsReportExecuteSql:
    args = mock.Mock()
    args.s3_bucket = "local_s3"
    args.s3_output = "dml_report/60_day_window"
    args.use_date = True
    args.job_name = "ExportPsdReport"
    args.sql = [""]

    def test_run_sql(self, test_db_session):
        sql_file = f"{self.args.s3_bucket}/execute-sql/sql_reports/report.sql"
        write_file(sql_file, 'SELECT * FROM "user"')
        file_path = f"{self.args.s3_bucket}/{self.args.s3_output}/{date.today()}/query1.csv"
        write_file(file_path)
        self.args.sql = [open(sql_file).read()]
        report = ExecuteSqlReport()
        log_entry = create_log_entry(test_db_session, "execute-sql", "", "execute-sql")
        run_sql(test_db_session, log_entry, self.args, report)

        import_logs = test_db_session.query(ImportLog).all()
        assert len(import_logs) == 1
        assert import_logs[0].source == self.args.job_name
        assert import_logs[0].source_context == "execute-sql"
        assert import_logs[0].status == "success"
        report = json.loads(import_logs[0].report)
        assert report["total_statements_executed_count"] == 1
        assert report["output_file"] == file_path
