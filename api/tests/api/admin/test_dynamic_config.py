import uuid

from massgov.pfml.db.models.dynamic_configuration import DynamicConfig
from massgov.pfml.db.models.factories import DynamicConfigLogFactory
from massgov.pfml.db.queries.dynamic_config import ADMIN_UX_DYNAMIC_CONFIG_LOGS_LOAD_LIMIT_KEY


def test_get_all(
    client, mock_admin_user_headers, mock_valid_admin_user, mock_dynamic_config_entries
):
    """Test that the API returns the dynamic configuration entries from the DB."""

    response = client.get(
        "/v1/admin/dynamic-configs",
        headers=mock_admin_user_headers,
    )

    assert response.status_code == 200

    dynamic_configs = response.json().get("data", [])
    assert isinstance(dynamic_configs, list)
    assert len(dynamic_configs) == len(mock_dynamic_config_entries)

    # Check that each dynamic config has the expected fields
    for config in dynamic_configs:
        assert "config_key" in config
        assert "config_context" in config
        assert "config_value" in config
        assert "created_at" in config
        assert "updated_at" in config


def test_post_create(client, mock_admin_user_headers, mock_valid_admin_user, test_db_session):
    """Test that the API creates and returns the dynamic configuration entry."""

    request_body = {
        "config_key": "new_test_key",
        "config_context": "new_test_context",
        "config_value": "new_test_value",
    }

    response = client.post(
        "/v1/admin/dynamic-configs",
        headers=mock_admin_user_headers,
        json=request_body,
    )

    assert response.status_code == 201

    # Check that dynamic config was created in the database
    db_configs = test_db_session.query(DynamicConfig).all()
    assert len(db_configs) == 1

    dynamic_config = db_configs[0]
    assert dynamic_config.config_key == "new_test_key"
    assert dynamic_config.config_context == request_body["config_context"]
    assert dynamic_config.config_value == request_body["config_value"]
    assert dynamic_config.created_at is not None
    assert dynamic_config.updated_at is not None

    # Check that the response contains the created dynamic config
    response_data = response.json().get("data")
    assert response_data is not None
    assert response_data["config_key"] == request_body["config_key"]
    assert response_data["config_context"] == request_body["config_context"]
    assert response_data["config_value"] == request_body["config_value"]
    assert "created_at" in response_data
    assert "updated_at" in response_data


def test_patch(
    client,
    mock_admin_user_headers,
    mock_valid_admin_user,
    test_db_session,
    mock_dynamic_config_entries,
):
    """Test that the API updates and returns the dynamic configuration entry."""
    item_to_update = mock_dynamic_config_entries[0]

    new_value = "new_test_value"

    request_body = {"config_value": new_value}

    response = client.patch(
        f"/v1/admin/dynamic-configs/{item_to_update.dynamic_config_id}",
        headers=mock_admin_user_headers,
        json=request_body,
    )

    assert response.status_code == 200

    # Check that dynamic config was created in the database
    db_config = (
        test_db_session.query(DynamicConfig)
        .filter(DynamicConfig.dynamic_config_id == item_to_update.dynamic_config_id)
        .one()
    )

    assert db_config.config_value == new_value

    # Check that the response contains the updated dynamic config
    response_data = response.json().get("data")
    assert response_data is not None
    assert response_data["config_value"] == new_value
    assert "created_at" in response_data
    assert "updated_at" in response_data


def test_patch_non_existing(
    client, mock_admin_user_headers, mock_valid_admin_user, test_db_session
):
    """Test that the API returns 404 for non-existing dynamic configuration entry."""
    non_existing_id = uuid.uuid4()

    request_body = {"config_value": "new_test_value"}

    response = client.patch(
        f"/v1/admin/dynamic-configs/{non_existing_id}",
        headers=mock_admin_user_headers,
        json=request_body,
    )

    assert response.status_code == 404


def test_delete_existing(
    client,
    mock_admin_user_headers,
    mock_valid_admin_user,
    mock_dynamic_config_entries,
    test_db_session,
):
    """Test that the API call deletes dynamic configuration entry from the DB."""
    item_to_delete = mock_dynamic_config_entries[0]

    response = client.delete(
        f"/v1/admin/dynamic-configs/{item_to_delete.dynamic_config_id}",
        headers=mock_admin_user_headers,
    )

    assert response.status_code == 204

    # Check that dynamic config was created in the database
    db_config = (
        test_db_session.query(DynamicConfig)
        .filter(DynamicConfig.dynamic_config_id == item_to_delete.dynamic_config_id)
        .one_or_none()
    )
    assert db_config is None


def test_delete_non_existing(
    client,
    mock_admin_user_headers,
    mock_valid_admin_user,
    mock_dynamic_config_entries,
    test_db_session,
):
    """Test that the API returns 404 for an ID that does not exist."""

    response = client.delete(
        f"/v1/admin/dynamic-configs/{uuid.uuid4()}",
        headers=mock_admin_user_headers,
    )

    assert response.status_code == 404


def test_get_all_logs(client, mock_admin_user_headers, mock_valid_admin_user, monkeypatch):
    """Test that the API returns the dynamic configuration entries from the DB."""
    monkeypatch.setenv(ADMIN_UX_DYNAMIC_CONFIG_LOGS_LOAD_LIMIT_KEY, "2")

    DynamicConfigLogFactory.create_batch(3)

    response = client.get(
        "/v1/admin/dynamic-config-logs",
        headers=mock_admin_user_headers,
    )

    assert response.status_code == 200

    dynamic_configs = response.json().get("data", [])
    assert isinstance(dynamic_configs, list)
    # check limiting
    assert len(dynamic_configs) == 2
