from datetime import date

import faker
import pytest

from massgov.pfml.api.models.employer_exemptions.common import SelfInsuredPlanQuestions
from massgov.pfml.db.models.factories import UserFactory
from massgov.pfml.util import logging
from massgov.pfml.util.collections.dict import flatten
from tests.api import apply_custom_encoder
from tests.helpers.employer_exemptions import (
    EmployerExemptionApplicationFixtureReturn,
    create_new_employer_exemption_application,
    get_auth_token,
)
from tests.helpers.logging import assert_log_has_match

fake = faker.Faker()


def execute_patch_request(
    client,
    auth_token: str,
    employer_exemption_application_id: int,
    patch_request_body,
    feature_flag_enabled=True,
):
    return client.patch(
        f"/v1/employer-exemption-applications/{str(employer_exemption_application_id)}",
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-enable_employer_exemptions_portal": "true" if feature_flag_enabled else "false",
        },
        json=apply_custom_encoder(patch_request_body),
    )


@pytest.fixture
def patch_employer_exemption_application_contact_details(
    # create_new_employer_exemption_application,
) -> EmployerExemptionApplicationFixtureReturn:
    patch_request_body = {
        "contact_first_name": "Jane",
        "contact_last_name": "Doe",
        "contact_phone": {
            "int_code": "1",
            "phone_number": "************",
            "phone_type": "Cell",
        },
        "contact_title": "Software Engineer",
        "contact_email_address": "<EMAIL>",
        "has_third_party_administrator": True,
        "tpa_business_name": "TPA Business Name",
        "tpa_contact_first_name": "First",
        "tpa_contact_last_name": "Last",
        "tpa_contact_title": "Title",
        "tpa_contact_phone": {
            "int_code": "1",
            "phone_number": "************",
            "phone_type": "Cell",
        },
        "tpa_contact_email_address": "<EMAIL>",
    }

    return EmployerExemptionApplicationFixtureReturn(
        employer_exemption_application=create_new_employer_exemption_application(),
        request_body=patch_request_body,
        expected_response_status_code=200,
    )


@pytest.fixture
def patch_employer_exemption_application_organization_details(
    #    create_new_employer_exemption_application,
) -> EmployerExemptionApplicationFixtureReturn:

    patch_request_body = {
        "should_workforce_count_include_1099_misc": True,
        "average_workforce_count": 100,
    }

    return EmployerExemptionApplicationFixtureReturn(
        employer_exemption_application=create_new_employer_exemption_application(),
        request_body=patch_request_body,
        expected_response_status_code=200,
    )


@pytest.fixture
def patch_employer_exemption_application_insurance_details(
    # create_new_employer_exemption_application,
) -> EmployerExemptionApplicationFixtureReturn:

    patch_request_body = {
        "has_family_exemption": True,
        "has_medical_exemption": True,
        "is_self_insured_plan": False,
    }

    return EmployerExemptionApplicationFixtureReturn(
        employer_exemption_application=create_new_employer_exemption_application(),
        request_body=patch_request_body,
        expected_response_status_code=200,
    )


@pytest.fixture
def patch_employer_exemption_application_self_insured_plan(
    # create_new_employer_exemption_application,
) -> EmployerExemptionApplicationFixtureReturn:
    patch_request_body = {}

    self_insured_plan_question_fields = SelfInsuredPlanQuestions()

    for i in SelfInsuredPlanQuestions.__fields__:
        setattr(self_insured_plan_question_fields, i, fake.boolean())

    self_insured_plan_details_fields = {
        "has_obtained_surety_bond": True,
        "surety_bond_amount": fake.random_number(digits=7, fix_len=True) / 100,
        "surety_company": "Surety Company",
        "questions": self_insured_plan_question_fields.__dict__,
    }

    patch_request_body["self_insured"] = self_insured_plan_details_fields
    patch_request_body["insurance_plan_effective_at"] = f"{date.today().year}-01-01"

    return EmployerExemptionApplicationFixtureReturn(
        employer_exemption_application=create_new_employer_exemption_application(),
        request_body=patch_request_body,
        expected_response_status_code=200,
    )


@pytest.fixture
def patch_employer_exemption_application_purchased_private_plan(
    # create_new_employer_exemption_application,
) -> EmployerExemptionApplicationFixtureReturn:

    patch_request_body = {
        "purchased_plan": {
            "insurance_provider_id": 3,
            "insurance_plan_id": 6,
        },
    }

    return EmployerExemptionApplicationFixtureReturn(
        employer_exemption_application=create_new_employer_exemption_application(),
        request_body=patch_request_body,
        expected_response_status_code=200,
    )


@pytest.fixture
def patch_employer_exemption_application_not_leave_admin(
    auth_private_key,
) -> EmployerExemptionApplicationFixtureReturn:
    user = UserFactory.create()
    user_auth_token = get_auth_token(auth_private_key, user)
    patch_request_body = {
        "contact_first_name": "Jane",
        "contact_last_name": "Doe",
    }

    return EmployerExemptionApplicationFixtureReturn(
        employer_exemption_application=create_new_employer_exemption_application(),
        request_body=patch_request_body,
        expected_response_status_code=403,
        auth_token=user_auth_token,
    )


@pytest.fixture
def patch_employer_exemption_application_invalid_phone() -> (
    EmployerExemptionApplicationFixtureReturn
):

    # ************ is a valid phone number in terms of length and structure
    # however, phone numbers starting with 555 are considered invalid by the
    # phonenumbers library (eg. phonenumbers.is_valid_number(************) returns
    # false
    patch_request_body = {
        "contact_phone": {
            "phone_number": "************",
        },
        "tpa_contact_phone": {
            "phone_number": "************",
        },
    }

    expected_error_list = [
        {
            "field": "contact_phone.phone_number",
            "message": "Phone number must be a valid number",
            "rule": "phone_number_must_be_valid_number",
            "type": "invalid_phone_number",
        },
        {
            "field": "tpa_contact_phone.phone_number",
            "message": "Phone number must be a valid number",
            "rule": "phone_number_must_be_valid_number",
            "type": "invalid_phone_number",
        },
    ]

    return EmployerExemptionApplicationFixtureReturn(
        employer_exemption_application=create_new_employer_exemption_application(),
        request_body=patch_request_body,
        expected_response_status_code=400,
        expected_response_errors=expected_error_list,
    )


@pytest.fixture
def patch_employer_exemption_application_email_addr_exceeds_max_length() -> (
    EmployerExemptionApplicationFixtureReturn
):
    MAX_EMAIL_ADDRESS_LENGTH = 320

    email_fields = ["contact_email_address", "tpa_contact_email_address"]
    test_email_address = "a" * MAX_EMAIL_ADDRESS_LENGTH + "@example.com"
    patch_request_body = {}
    expected_error_list = []

    for field in email_fields:
        patch_request_body[field] = test_email_address
        expected_error_list.append(
            {
                "field": field,
                "message": f"'{test_email_address}' is too long",
                "rule": MAX_EMAIL_ADDRESS_LENGTH,
                "type": "maxLength",
            }
        )

    return EmployerExemptionApplicationFixtureReturn(
        employer_exemption_application=create_new_employer_exemption_application(),
        request_body=patch_request_body,
        expected_response_status_code=400,
        expected_response_errors=expected_error_list,
    )


@pytest.mark.parametrize(
    "employer_exemption_application_fixture_name, expected_employer_exemption_status",
    [
        ("patch_employer_exemption_application_contact_details", "Draft"),
        ("patch_employer_exemption_application_organization_details", "Draft"),
        ("patch_employer_exemption_application_insurance_details", "Draft"),
        ("patch_employer_exemption_application_self_insured_plan", "Draft"),
        ("patch_employer_exemption_application_purchased_private_plan", "Draft"),
    ],
)
def test_patch_employer_exemption_application_success_no_errors(
    request,
    client,
    auth_private_key,
    caplog,
    employer_exemption_application_fixture_name,
    expected_employer_exemption_status,
):

    caplog.set_level(logging.INFO)  # noqa: B1
    fixture = request.getfixturevalue(employer_exemption_application_fixture_name)
    patch_request_body = fixture.request_body
    auth_token = fixture.auth_token or get_auth_token(auth_private_key, fixture.leave_admin_user)

    response = execute_patch_request(
        client,
        auth_token,
        fixture.employer_exemption_application_id,
        patch_request_body,
    )

    assert response.status_code == fixture.expected_response_status_code
    data = flatten(response.json().get("data"))
    patch_request_body = flatten(patch_request_body)

    for request_field_name in patch_request_body.keys():
        assert data[request_field_name] == patch_request_body[request_field_name]

    assert_log_has_match(
        caplog,
        "employer_exemptions_applications_update success",
        {
            "employer_exemption_application.employer_exemption_application_status": expected_employer_exemption_status
        },
    )


@pytest.mark.parametrize(
    "employer_exemption_application_fixture_name, expected_employer_exemption_status",
    [
        ("patch_employer_exemption_application_invalid_phone", "Draft"),
        ("patch_employer_exemption_application_not_leave_admin", "Draft"),
    ],
)
def test_patch_employer_exemption_application_success_with_errors(
    request,
    client,
    auth_private_key,
    caplog,
    employer_exemption_application_fixture_name,
    expected_employer_exemption_status,
):

    caplog.set_level(logging.INFO)  # noqa: B1
    fixture = request.getfixturevalue(employer_exemption_application_fixture_name)
    auth_token = fixture.auth_token or get_auth_token(auth_private_key, fixture.leave_admin_user)

    response = execute_patch_request(
        client,
        auth_token,
        fixture.employer_exemption_application_id,
        fixture.request_body,
    )

    assert response.status_code == fixture.expected_response_status_code

    # check patch data matches response data if reponse actually contains data
    # (eg. skip this for 403 errors as data will be None)
    response_data = response.json().get("data")

    if response_data:
        response_data = flatten(response_data)
        patch_request_body = flatten(fixture.request_body)

        for request_field_name in patch_request_body.keys():
            assert response_data[request_field_name] == patch_request_body[request_field_name]

        assert_log_has_match(
            caplog,
            "employer_exemptions_applications_update success",
            {
                "employer_exemption_application.employer_exemption_application_status": expected_employer_exemption_status
            },
        )

    # check expected errors matche response errors
    response_errors = response.json()["errors"]

    if response_errors:
        assert len(response_errors) == len(fixture.expected_response_errors)
        assert response_errors == fixture.expected_response_errors


@pytest.mark.parametrize(
    "employer_exemption_application_fixture_name",
    [
        "patch_employer_exemption_application_email_addr_exceeds_max_length",
    ],
)
def test_patch_employer_exemption_application_failure(
    request, client, auth_private_key, employer_exemption_application_fixture_name
):

    fixture = request.getfixturevalue(employer_exemption_application_fixture_name)
    auth_token = fixture.auth_token or get_auth_token(auth_private_key, fixture.leave_admin_user)

    response = execute_patch_request(
        client,
        auth_token,
        fixture.employer_exemption_application_id,
        fixture.request_body,
    )

    assert response.status_code == fixture.expected_response_status_code
    response_errors = response.json()["errors"]
    assert len(response_errors) == len(fixture.expected_response_errors)
    assert response_errors == fixture.expected_response_errors
