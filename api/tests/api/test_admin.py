import json
import uuid
from datetime import date, datetime, timedelta
from decimal import Decimal
from unittest import mock
from unittest.mock import patch

import pytest
from authlib.jose import jwt
from flask import g

from massgov.pfml.api.admin import SERVICE_UNAVAILABLE_MESSAGE
from massgov.pfml.api.models.payments.responses import VCMComparisonResponse
from massgov.pfml.api.models.users.requests import ApplicationReassignmentPreviewRequest
from massgov.pfml.api.models.users.responses import ApplicationReassignmentPreviewResponse
from massgov.pfml.db.lookup_data.azure import AzureGroup, AzureGroupPermission, AzurePermission
from massgov.pfml.db.lookup_data.employees import Role
from massgov.pfml.db.lookup_data.flags import FeatureFlag
from massgov.pfml.db.lookup_data.geo import GeoState
from massgov.pfml.db.lookup_data.payments import (
    MmarsEventActionType,
    MmarsEventStatusType,
    MmarsEventType,
    PaymentEventType,
)
from massgov.pfml.db.models.admin import Admin<PERSON>uditLog, AdminUser
from massgov.pfml.db.models.employees import ExperianAddressPair, User, UserRole
from massgov.pfml.db.models.factories import (
    AddressFactory,
    AdminAuditLogFactory,
    ApplicationFactory,
    ApplicationUserNotFoundInfoFactory,
    ClaimantAddressFactory,
    ClaimFactory,
    EmployeeFactory,
    MmarsCustomerDetailFactory,
    MmarsEventFactory,
    OverpaymentFactory,
    TaxIdentifierFactory,
    UserAuthLogFactory,
    UserFactory,
)
from massgov.pfml.db.models.flags import FeatureFlagValue
from massgov.pfml.db.models.payments import MmarsEvent, MmarsEventActionLog
from tests.api import apply_custom_encoder, delete_request_with_body
from tests.helpers.logging import assert_log_contains

FAKE_AUTH_URI_RESPONSE = {
    "auth_uri": "test",
    "code_verifier": "test",
    "nonce": "test",
    "redirect_uri": "test",
    "scope": ["test"],
    "state": "test",
}

FAKE_ADMIN_TOKEN_POST_BODY = {
    "auth_uri_res": FAKE_AUTH_URI_RESPONSE,
    "auth_code_res": {"code": "test", "session_state": "test", "state": "test"},
}


@patch("massgov.pfml.api.admin.build_auth_code_flow")
def test_admin_authorize_unavailable(mock_build, client):
    mock_build.return_value = None
    response = client.get("/v1/admin/authorize")
    assert response.status_code == 503
    json = response.json()
    assert json.get("message") == SERVICE_UNAVAILABLE_MESSAGE


@patch("massgov.pfml.api.admin.build_auth_code_flow")
def test_admin_authorize_success(mock_build, mock_azure, client):
    mock_build.return_value = FAKE_AUTH_URI_RESPONSE
    response = client.get("/v1/admin/authorize")
    assert response.status_code == 200
    json = response.json()
    assert json.get("data").get("auth_uri") == "test"


def test_admin_flag_get_logs_by_name_success(
    app, client, test_db_session, azure_auth_claims_unit, azure_auth_private_key
):
    azure_token = azure_auth_claims_unit.copy()
    azure_token["unique_name"] = "<EMAIL>"
    azure_token["given_name"] = "john"
    azure_token["family_name"] = "doe"
    azure_token["groups"] = [
        AzureGroup.NON_PROD.azure_group_guid,
        AzureGroup.NON_PROD_PGMINTEG01.azure_group_guid,
    ]
    test_db_session.add(
        AzureGroupPermission(
            azure_group_id=AzureGroup.NON_PROD_PGMINTEG01.azure_group_id,
            azure_permission_id=AzurePermission.MAINTENANCE_EDIT.azure_permission_id,
        )
    )
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        azure_token,
        azure_auth_private_key,
    ).decode("utf-8")
    flag = FeatureFlag.get_instance(test_db_session, description="maintenance")
    feature_flag_value = FeatureFlagValue()
    feature_flag_value.feature_flag = flag
    feature_flag_value.enabled = True
    feature_flag_value.email_address = azure_token["unique_name"]
    feature_flag_value.sub_id = azure_token["sub"]
    feature_flag_value.family_name = azure_token["family_name"]
    feature_flag_value.given_name = azure_token["given_name"]
    feature_flag_value.action = "INSERT"
    test_db_session.add(feature_flag_value)
    test_db_session.commit()
    response = client.get(
        "/v1/admin/flag-logs/maintenance", headers={"Authorization": f"Bearer {encoded}"}
    )
    assert response.status_code == 200
    response_data = response.json().get("data")
    assert len(response_data) == 1
    flag_response = response_data[0]
    assert flag_response.get("enabled") is True


def test_admin_flag_get_logs_by_name_unauthorized(
    app, client, test_db_session, azure_auth_claims_unit, azure_auth_private_key
):
    azure_token = azure_auth_claims_unit.copy()
    azure_token["groups"] = [
        AzureGroup.NON_PROD.azure_group_guid,
        AzureGroup.NON_PROD_PGMINTEG01.azure_group_guid,
    ]
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        azure_token,
        azure_auth_private_key,
    ).decode("utf-8")
    flag = FeatureFlag.get_instance(test_db_session, description="maintenance")
    feature_flag_value = FeatureFlagValue()
    feature_flag_value.feature_flag = flag
    feature_flag_value.enabled = True
    feature_flag_value.email_address = "<EMAIL>"
    feature_flag_value.sub_id = azure_token["sub"]
    feature_flag_value.family_name = "doe"
    feature_flag_value.given_name = "john"
    feature_flag_value.action = "INSERT"
    test_db_session.add(feature_flag_value)
    test_db_session.commit()
    response = client.get(
        "/v1/admin/flag-logs/maintenance", headers={"Authorization": f"Bearer {encoded}"}
    )
    assert response.status_code == 403


def test_admin_flags_patch_success(
    app, client, test_db_session, azure_auth_claims_unit, azure_auth_private_key
):
    azure_token = azure_auth_claims_unit.copy()
    azure_token["unique_name"] = "<EMAIL>"
    azure_token["given_name"] = "john"
    azure_token["family_name"] = "doe"
    azure_token["groups"] = [
        AzureGroup.NON_PROD.azure_group_guid,
        AzureGroup.NON_PROD_PGMINTEG01.azure_group_guid,
    ]
    test_db_session.add(
        AzureGroupPermission(
            azure_group_id=AzureGroup.NON_PROD_PGMINTEG01.azure_group_id,
            azure_permission_id=AzurePermission.MAINTENANCE_EDIT.azure_permission_id,
        )
    )
    test_db_session.commit()
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        azure_token,
        azure_auth_private_key,
    ).decode("utf-8")
    body = {
        "enabled": True,
        "start": "2022-01-14T00:00:00-05:00",
        "end": "2022-01-15T00:00:00-05:00",
        "options": {
            "name": "applications and custom",
            "page_routes": ["/applications/*", "/custom/*"],
        },
    }
    with app.app.test_request_context("/v1/admin/flags/maintenance"):
        response = client.patch(
            "/v1/admin/flags/maintenance",
            headers={"Authorization": f"Bearer {encoded}"},
            json=body,
        )
        assert response.status_code == 200
        assert g.azure_user.sub_id == "foo"
        assert g.azure_user.permissions != [
            AzurePermission.MAINTENANCE_EDIT.azure_permission_id,
        ]
        response_json = response.json()
        data = response_json.get("data")
        assert data.get("enabled") is True
        assert data.get("start") == "2022-01-14T00:00:00-05:00"
        assert data.get("end") == "2022-01-15T00:00:00-05:00"
        assert data.get("options").get("name") == "applications and custom"
        assert len(data.get("options").get("page_routes")) == 2
        assert data.get("options").get("page_routes") == ["/applications/*", "/custom/*"]


def test_admin_flags_patch_no_permissions(
    mock_azure, client, app, azure_auth_claims_unit, azure_auth_private_key
):
    azure_token = azure_auth_claims_unit.copy()
    azure_token["groups"] = [
        AzureGroup.NON_PROD_PGMINTEG01.azure_group_guid,
    ]
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        azure_token,
        azure_auth_private_key,
    ).decode("utf-8")
    body = {
        "enabled": True,
        "start": "2022-01-14T00:00:00-05:00",
        "end": "2022-01-15T00:00:00-05:00",
        "options": {
            "name": "applications and custom",
            "page_routes": ["/applications/*", "/custom/*"],
        },
    }
    with app.app.test_request_context("/v1/admin/flags/maintenance"):
        response = client.patch(
            "/v1/admin/flags/maintenance",
            headers={"Authorization": f"Bearer {encoded}"},
            json=body,
        )
        assert response.status_code == 401


def test_admin_login_success(
    mock_azure, client, app, azure_auth_claims_unit, azure_auth_private_key, test_db_session
):
    azure_token = azure_auth_claims_unit.copy()
    azure_token["groups"] = [
        AzureGroup.NON_PROD.azure_group_guid,
        AzureGroup.NON_PROD_PGMINTEG01.azure_group_guid,
    ]
    azure_token["unique_name"] = "<EMAIL>"
    test_db_session.add(
        AzureGroupPermission(
            azure_group_id=AzureGroup.NON_PROD_PGMINTEG01.azure_group_id,
            azure_permission_id=AzurePermission.MAINTENANCE_READ.azure_permission_id,
        )
    )
    test_db_session.add(
        AzureGroupPermission(
            azure_group_id=AzureGroup.NON_PROD_PGMINTEG01.azure_group_id,
            azure_permission_id=AzurePermission.MAINTENANCE_EDIT.azure_permission_id,
        )
    )
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        azure_token,
        azure_auth_private_key,
    ).decode("utf-8")
    test_db_session.commit()
    with app.app.test_request_context("/v1/admin/login"):
        response = client.get("/v1/admin/login", headers={"Authorization": f"Bearer {encoded}"})
        data = response.json().get("data")
        assert response.status_code == 200
        assert g.azure_user.sub_id == "foo"
        assert g.azure_user.permissions == [
            AzurePermission.MAINTENANCE_READ.azure_permission_id,
            AzurePermission.MAINTENANCE_EDIT.azure_permission_id,
            AzurePermission.ADDRESS_VALIDATION_OVERRIDE_READ.azure_permission_id,
            AzurePermission.ADDRESS_VALIDATION_OVERRIDE_EDIT.azure_permission_id,
            AzurePermission.OVERPAYMENT_COLLECTION_READ.azure_permission_id,
            AzurePermission.OVERPAYMENT_COLLECTION_EDIT.azure_permission_id,
        ]
        assert data.get("email_address") == "<EMAIL>"
        assert data.get("permissions") == [
            "MAINTENANCE_READ",
            "MAINTENANCE_EDIT",
            "ADDRESS_VALIDATION_OVERRIDE_READ",
            "ADDRESS_VALIDATION_OVERRIDE_EDIT",
            "OVERPAYMENT_COLLECTION_READ",
            "OVERPAYMENT_COLLECTION_EDIT",
        ]


def test_admin_login_unauthorized(mock_azure, client, app, auth_token_unit):
    with app.app.test_request_context("/v1/admin/login"):
        response = client.get(
            "/v1/admin/login", headers={"Authorization": f"Bearer {auth_token_unit}"}
        )
        assert g.get("azure_user") is None
        assert response.status_code == 401


def test_admin_login_unauthorized_no_permissions(mock_azure, client, app, azure_auth_token_unit):
    with app.app.test_request_context("/v1/admin/login"):
        response = client.get(
            "/v1/admin/login", headers={"Authorization": f"Bearer {azure_auth_token_unit}"}
        )
        assert g.get("azure_user") is None
        assert response.status_code == 401


@patch("massgov.pfml.api.admin.build_logout_flow")
def test_admin_logout_service_unavailable(mock_build, client):
    mock_build.return_value = None
    response = client.get("/v1/admin/logout")
    assert response.status_code == 503
    json = response.json()
    assert json.get("message") == SERVICE_UNAVAILABLE_MESSAGE


def test_admin_logout_success(mock_azure, client):
    response = client.get("/v1/admin/logout")
    assert response.status_code == 200
    json = response.json()
    assert (
        json.get("data").get("logout_uri")
        == "https://example.com/tenant_id/oauth2/v2.0/logout?post_logout_redirect_uri=http://localhost:3001?logged_out=true"
    )
    assert json.get("message") == "Retrieved logout url!"


@patch("massgov.pfml.api.admin.build_access_token")
def test_admin_token_error_in_tokens(mock_build, client):
    mock_build.return_value = {"error": "Test error"}
    response = client.post("/v1/admin/token", json=FAKE_ADMIN_TOKEN_POST_BODY)
    json = response.json()
    errors = json.get("errors")
    assert errors is not None
    assert len(errors) == 1
    assert errors[0].get("message") == "Test error"
    assert errors[0].get("field") == "auth_uri_res"
    assert errors[0].get("type") == "invalid"
    assert response.status_code == 400


def test_admin_token_missing_request_body(client):
    response = client.post("/v1/admin/token")
    json = response.json()
    errors = json.get("errors")
    assert errors is not None
    # connexion 3.1.0 upgrade - missing request body won't have any errors.
    # assert len(errors) == 1
    # assert errors[0].get("message") == "Missing request body"
    assert json.get("message") == "RequestBody is required"
    assert response.status_code == 400


@patch("massgov.pfml.api.admin.build_access_token")
def test_admin_token_service_unavailable(mock_build, client):
    mock_build.return_value = None
    response = client.post("/v1/admin/token", json=FAKE_ADMIN_TOKEN_POST_BODY)
    json = response.json()
    assert json.get("message") == "Azure AD is not configured."
    # assert json.get("title") == "Service Unavailable"
    assert response.status_code == 503


@patch("massgov.pfml.api.admin.build_access_token")
def test_admin_token_success(
    mock_build, client, test_db_session, mock_azure_tokens, mock_azure_id_token_claims
):
    mock_build.return_value = mock_azure_tokens
    response = client.post("/v1/admin/token", json=FAKE_ADMIN_TOKEN_POST_BODY)
    json = response.json()
    errors = json.get("errors")
    data = json.get("data")

    admin_user = test_db_session.query(AdminUser).first()

    assert errors is None
    assert json.get("message") == "Successfully logged in!"
    assert data.get("access_token") == "test"
    assert data.get("refresh_token") == "test"
    assert data.get("id_token") == "test"
    assert admin_user is not None
    assert admin_user.email_address == mock_azure_id_token_claims["preferred_username"]
    assert admin_user.oauth_id == mock_azure_id_token_claims["sub"]
    assert admin_user.name == mock_azure_id_token_claims["name"]
    assert response.status_code == 200


@patch("massgov.pfml.api.admin.build_access_token")
def test_admin_token_db_failure(mock_build, client, test_db_session, mock_azure_tokens):
    mock_build.return_value = mock_azure_tokens

    with mock.patch.object(test_db_session, "query", side_effect=Exception("Database Error")):
        response = client.post("/v1/admin/token", json=FAKE_ADMIN_TOKEN_POST_BODY)
        assert response.status_code == 500


@patch("massgov.pfml.api.admin.build_access_token")
def test_admin_token_value_error(mock_build, client):
    mock_build.side_effect = ValueError
    mock_build.return_value = None
    response = client.post("/v1/admin/token", json=FAKE_ADMIN_TOKEN_POST_BODY)
    errors = response.json().get("errors")
    assert errors is not None
    assert len(errors) == 1
    assert errors[0].get("field") == "auth_uri_res"
    assert errors[0].get("message") == "Value error"
    assert errors[0].get("type") == "invalid"
    assert response.status_code == 400


def test_admin_users_no_permissions(
    mock_azure, client, app, azure_auth_claims_unit, azure_auth_private_key
):
    azure_token = azure_auth_claims_unit.copy()
    azure_token["groups"] = [
        AzureGroup.NON_PROD.azure_group_guid,
        AzureGroup.NON_PROD_PGMINTEG01.azure_group_guid,
    ]
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        azure_token,
        azure_auth_private_key,
    ).decode("utf-8")
    with app.app.test_request_context("/v1/admin/users"):
        response = client.get(
            "/v1/admin/users?page_size=10", headers={"Authorization": f"Bearer {encoded}"}
        )
        assert response.status_code == 403


def test_admin_users_cognito(mock_azure, client, app, auth_token):
    response = client.get("/v1/admin/users", headers={"Authorization": f"Bearer {auth_token}"})
    assert response.status_code == 401


def test_admin_users_success(
    mock_azure, client, app, azure_auth_claims_unit, azure_auth_private_key, test_db_session
):
    azure_token = azure_auth_claims_unit.copy()
    azure_token["groups"] = [
        AzureGroup.NON_PROD.azure_group_guid,
        AzureGroup.NON_PROD_PGMINTEG01.azure_group_guid,
    ]
    test_db_session.add(
        AzureGroupPermission(
            azure_group_id=AzureGroup.NON_PROD_PGMINTEG01.azure_group_id,
            azure_permission_id=AzurePermission.MAINTENANCE_READ.azure_permission_id,
        )
    )
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        azure_token,
        azure_auth_private_key,
    ).decode("utf-8")
    UserFactory.create_batch(25)
    with app.app.test_request_context("/v1/admin/users"):
        response = client.get(
            "/v1/admin/users?page_size=10", headers={"Authorization": f"Bearer {encoded}"}
        )
        assert response.status_code == 200
        assert g.azure_user.sub_id == "foo"
        assert g.azure_user.permissions == [
            AzurePermission.MAINTENANCE_READ.azure_permission_id,
            AzurePermission.ADDRESS_VALIDATION_OVERRIDE_READ.azure_permission_id,
            AzurePermission.ADDRESS_VALIDATION_OVERRIDE_EDIT.azure_permission_id,
            AzurePermission.OVERPAYMENT_COLLECTION_READ.azure_permission_id,
            AzurePermission.OVERPAYMENT_COLLECTION_EDIT.azure_permission_id,
        ]
        response_json = response.json()
        data = response_json.get("data")
        paging = response_json.get("meta").get("paging")

        assert paging.get("order_by") == "created_at"
        assert paging.get("order_direction") == "descending"
        assert paging.get("page_offset") == 1
        assert paging.get("page_size") == 10
        assert paging.get("total_pages") == 3
        assert paging.get("total_records") == 25
        assert len(data) == 10


def test_admin_users_email_address_filter(
    mock_azure, client, app, azure_auth_claims_unit, azure_auth_private_key, test_db_session
):
    azure_token = azure_auth_claims_unit.copy()
    azure_token["groups"] = [
        AzureGroup.NON_PROD.azure_group_guid,
        AzureGroup.NON_PROD_PGMINTEG01.azure_group_guid,
    ]
    test_db_session.add(
        AzureGroupPermission(
            azure_group_id=AzureGroup.NON_PROD_PGMINTEG01.azure_group_id,
            azure_permission_id=AzurePermission.MAINTENANCE_READ.azure_permission_id,
        )
    )
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        azure_token,
        azure_auth_private_key,
    ).decode("utf-8")
    for x in range(3):
        UserFactory.create(email_address=f"janeDo+{x}@example.com")
    for x in range(3):
        UserFactory.create(email_address=f"johnDo+{x}@example.com")
    with app.app.test_request_context("/v1/admin/users"):
        # Just showing that it is case insensitive.
        response = client.get(
            "/v1/admin/users?page_size=10&email_address=anedo",
            headers={"Authorization": f"Bearer {encoded}"},
        )
        assert response.status_code == 200
        assert g.azure_user.sub_id == "foo"
        assert g.azure_user.permissions == [
            AzurePermission.MAINTENANCE_READ.azure_permission_id,
            AzurePermission.ADDRESS_VALIDATION_OVERRIDE_READ.azure_permission_id,
            AzurePermission.ADDRESS_VALIDATION_OVERRIDE_EDIT.azure_permission_id,
            AzurePermission.OVERPAYMENT_COLLECTION_READ.azure_permission_id,
            AzurePermission.OVERPAYMENT_COLLECTION_EDIT.azure_permission_id,
        ]
        response_json = response.json()
        data = response_json.get("data")
        paging = response_json.get("meta").get("paging")

        assert paging.get("order_by") == "created_at"
        assert paging.get("order_direction") == "descending"
        assert paging.get("page_offset") == 1
        assert paging.get("page_size") == 10
        assert paging.get("total_pages") == 1
        assert paging.get("total_records") == 3
        assert len(data) == 3


def test_omni_search_users(client, app, mock_admin_user_headers):
    UserFactory.create(email_address="<EMAIL>")
    UserFactory.create(first_name="test")
    UserFactory.create(last_name="test")

    UserFactory.create()
    post_body = {"search": "test"}
    with app.app.test_request_context("/v1/admin/users"):
        # Just showing that it is case insensitive.
        response = client.post(
            "/v1/admin/omnisearch/users",
            headers=mock_admin_user_headers,
            json=post_body,
        )
        response_json = response.json()
        data = response_json.get("data")
        assert response.status_code == 200
        assert len(data) == 3


def test_omni_search_users_query_results_limit(client, app, mock_admin_user_headers):

    for i in range(0, 30):
        UserFactory.create(first_name=f"test{i}")

    post_body = {"search": "test"}
    with app.app.test_request_context("/v1/admin/users"):
        # Just showing that it is case insensitive.
        response = client.post(
            "/v1/admin/omnisearch/users",
            headers=mock_admin_user_headers,
            json=post_body,
        )
        response_json = response.json()
        data = response_json.get("data")
        assert response.status_code == 200
        assert len(data) == 20


def test_overpayment_search_single_overpayment_no_transaction(
    client, test_db_session, app, mock_admin_user_headers
):

    # Create test data
    employee = EmployeeFactory.create(first_name="John", last_name="Doe")
    employee.fineos_customer_number = "123456"
    claim = ClaimFactory.create()
    claim.employee = employee

    overpayment = OverpaymentFactory.create(claim_id=claim.claim_id)
    overpayment.claim = claim
    overpayment.overpayment_casenumber = "PL ABS-1234567-PL ABS-01-OP1234567"
    # Amount is set to negative value in Fineos
    overpayment.amount = -200
    overpayment.recovered_to_date_amount = 50
    # Almost always, outstanding amount is set to negative value in Fineos. But in some cases it is positive where employee has overpaid
    overpayment.outstanding_amount = -140
    overpayment.overpayment_adjustment_amount = 10
    overpayment.agreed_recovery_amount = -190
    overpayment.adjustment_description = "Test adjustment description"

    # We expect `overpayment search` endpoint to return mailing address
    ClaimantAddressFactory(
        employee_id=employee.employee_id,
        residential_address=ExperianAddressPair(
            fineos_address=AddressFactory(
                address_line_one="original residential address",
                address_line_two="Apt 1",
                city="Boston",
                geo_state_text="MA",
                geo_state_id=GeoState.MA.geo_state_id,
                zip_code="02118",
                country_id=232,
            )
        ),
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(
                address_line_one="original mailing address",
                address_line_two="Suite 200",
                city="Cambridge",
                geo_state_id=GeoState.MA.geo_state_id,
                zip_code="02139",
                country_id=232,
            )
        ),
    )

    test_db_session.commit()

    post_body = {"fineos_customer_number": employee.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    assert response.status_code == 200
    assert data is not None, "Data should not be None"
    assert isinstance(data, dict), "Data should be a dictionary"
    assert data["employee_firstname"] == employee.first_name
    assert data["employee_lastname"] == employee.last_name
    assert len(data["overpayment_cases"]) == 1
    assert data["fineos_customer_number"] == employee.fineos_customer_number
    assert (
        data["overpayment_cases"][0]["overpayment_casenumber"] == overpayment.overpayment_casenumber
    )
    assert (
        data["overpayment_cases"][0]["recovered_to_date_amount"]
        == overpayment.recovered_to_date_amount
    )
    # Sign of `amount`, `outstanding_amount` and `agreed_recovery_amount` flipped in the backend since they are set as negative by Fineos
    # and we would like to show them as positive to users
    assert data["overpayment_cases"][0]["amount"] == (-1) * overpayment.amount
    assert data["overpayment_cases"][0]["agreed_recovery_amount"] == pytest.approx(
        (-1) * overpayment.agreed_recovery_amount
    )
    assert data["overpayment_cases"][0]["outstanding_amount"] == pytest.approx(
        (-1) * overpayment.outstanding_amount
    )
    assert data["overpayment_cases"][0]["overpayment_adjustment_amount"] == pytest.approx(
        overpayment.overpayment_adjustment_amount
    )
    assert data["overpayment_cases"][0]["overpayment_id"] == str(overpayment.overpayment_id)
    actual_period_start_period = date.fromisoformat(
        data["overpayment_cases"][0]["period_start_date"]
    )
    assert actual_period_start_period == overpayment.period_start_date
    actual_period_end_date = date.fromisoformat(data["overpayment_cases"][0]["period_end_date"])
    assert actual_period_end_date == overpayment.period_end_date
    assert (
        data["overpayment_cases"][0]["adjustment_description"] == overpayment.adjustment_description
    )

    # Verify the employee address
    assert data["employee_address"] is not None, "Employee address should not be None"
    assert data["employee_address"]["line_1"] == "original mailing address"
    assert data["employee_address"]["line_2"] == "Suite 200"
    assert data["employee_address"]["city"] == "Cambridge"
    assert data["employee_address"]["state"] == GeoState.MA.geo_state_description
    assert data["employee_address"]["zip"] == "02139"


def test_overpayment_search_with_mailing_address(
    client, test_db_session, app, mock_admin_user_headers
):

    # Create test data
    employee = EmployeeFactory.create(
        first_name="John", last_name="Doe", fineos_customer_number="123456"
    )
    claim = ClaimFactory.create(employee=employee)

    OverpaymentFactory.create(
        claim=claim,
        claim_id=claim.claim_id,
        overpayment_casenumber="PL ABS-1234567-PL ABS-01-OP1234567",
    )

    # We expect `overpayment search` endpoint to return mailing address
    ClaimantAddressFactory(
        employee_id=employee.employee_id,
        residential_address=None,
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(
                address_line_one="original mailing address",
                address_line_two="Suite 200",
                city="Cambridge",
                geo_state_id=GeoState.MA.geo_state_id,
                zip_code="02139",
                country_id=232,
            )
        ),
    )

    post_body = {"fineos_customer_number": employee.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    assert response.status_code == 200
    assert data is not None, "Data should not be None"
    assert isinstance(data, dict), "Data should be a dictionary"

    # Verify the employee address
    assert data["employee_address"] is not None, "Employee address should not be None"
    assert data["employee_address"]["line_1"] == "original mailing address"
    assert data["employee_address"]["line_2"] == "Suite 200"
    assert data["employee_address"]["city"] == "Cambridge"
    assert data["employee_address"]["state"] == GeoState.MA.geo_state_description
    assert data["employee_address"]["zip"] == "02139"

    # Test broken mailing address
    # This step we test the case where the mailing address has an ExperianAddressPair but the fineos_address is None

    # Create test data
    employee = EmployeeFactory.create(
        first_name="John", last_name="Doe", fineos_customer_number="654321"
    )
    claim = ClaimFactory.create(employee=employee)

    OverpaymentFactory.create(
        claim=claim,
        claim_id=claim.claim_id,
        overpayment_casenumber="PL ABS-1234568-PL ABS-01-OP1234568",
    )

    # We expect `overpayment search` endpoint does not return an address since it only returns the mailing address. In this case, the mailing address is None
    ClaimantAddressFactory(
        employee_id=employee.employee_id,
        residential_address=ExperianAddressPair(
            fineos_address=AddressFactory(
                address_line_one="original residential address",
                address_line_two="Apt 1",
                city="Boston",
                geo_state_text="MA",
                geo_state_id=GeoState.MA.geo_state_id,
                zip_code="02118",
                country_id=232,
            )
        ),
        mailing_address=None,
    )

    post_body = {"fineos_customer_number": employee.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    assert response.status_code == 200
    assert data is not None, "Data should not be None"
    assert isinstance(data, dict), "Data should be a dictionary"

    # Verify the employee address
    assert data["employee_address"] is None


def test_overpayment_search_single_overpayment_with_transaction(
    client, test_db_session, app, mock_admin_user_headers
):
    employee = EmployeeFactory.create(first_name="John", last_name="Doe")
    employee.fineos_customer_number = "123456"
    claim = ClaimFactory.create()
    claim.employee = employee

    overpayment = OverpaymentFactory.create(claim_id=claim.claim_id)
    overpayment.claim = claim
    overpayment.overpayment_casenumber = "PL ABS-1234567-PL ABS-01-OP1234567"
    overpayment.recovered_to_date_amount = 50
    overpayment.outstanding_amount = 150
    overpayment.amount = 210

    # We expect `overpayment search` endpoint to return mailing address
    ClaimantAddressFactory(
        employee_id=employee.employee_id,
        residential_address=ExperianAddressPair(
            fineos_address=AddressFactory(
                address_line_one="original residential address",
                address_line_two="Apt 1",
                city="Boston",
                geo_state_text="MA",
                geo_state_id=GeoState.MA.geo_state_id,
                zip_code="02118",
                country_id=232,
            )
        ),
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(
                address_line_one="original mailing address",
                address_line_two="Suite 200",
                city="Cambridge",
                geo_state_id=GeoState.MA.geo_state_id,
                zip_code="02139",
                country_id=232,
            )
        ),
    )

    first_transaction_date = date(2024, 11, 22)

    # Create transaction in the queue that states overpayment has been referred MMARS including VCC and REs
    mmars_event_1 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.RE_SUCCESS.mmars_event_status_type_id,
        overpayment_id=overpayment.overpayment_id,
        created_at=first_transaction_date,
    )

    mmars_event_1.overpayment_id = overpayment.overpayment_id

    # Create transaction in the queue that states that an REM is Pending
    # This is a scenario where referred overpayment needs a change. And we send an REM file to MMARS that contains the change
    mmars_event_2 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment_id=overpayment.overpayment_id,
        created_at=first_transaction_date + timedelta(days=7),
    )

    mmars_event_2.overpayment_id = overpayment.overpayment_id

    test_db_session.commit()

    post_body = {"fineos_customer_number": employee.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    assert response.status_code == 200

    overpayment_cases = data["overpayment_cases"]

    # the overpayment cases are referrable
    assert not data["prevent_referral"]

    # we expect the referral prevention reason to be empty
    assert not data["referral_prevention_reason"]

    # we expect the referral date still to be the first transaction date
    assert overpayment_cases[0]["referral_date"] == first_transaction_date.strftime("%Y-%m-%d")

    assert (
        overpayment_cases[0]["referral_status"]
        == MmarsEventStatusType.REM_PENDING.mmars_event_status_type_description
    )

    assert overpayment_cases[0][
        "overpayment_case_creation_date"
    ] == overpayment.overpayment_date.strftime("%Y-%m-%d")

    # Ensure there are 2 mmars events
    assert len(overpayment_cases[0]["mmars_events"]) == 2

    # Sort events based on their creation date
    mmars_events_returned = sorted(
        overpayment_cases[0]["mmars_events"], key=lambda x: x["created_at"]
    )

    # Extract the mmars events
    mmars_event_1_returned = mmars_events_returned[0]
    mmars_event_2_returned = mmars_events_returned[1]

    # Assert the fields of the first mmars event
    assert (
        mmars_event_1_returned["mmars_event_type_id"] == MmarsEventType.RE_TRX.mmars_event_type_id
    )
    assert (
        mmars_event_1_returned["mmars_status_type_id"]
        == MmarsEventStatusType.RE_SUCCESS.mmars_event_status_type_id
    )
    assert mmars_event_1_returned["created_at"] == first_transaction_date.strftime("%Y-%m-%d")

    # Assert the fields of the second mmars event
    assert (
        mmars_event_2_returned["mmars_event_type_id"] == MmarsEventType.REM_TRX.mmars_event_type_id
    )
    assert (
        mmars_event_2_returned["mmars_status_type_id"]
        == MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id
    )
    assert mmars_event_2_returned["created_at"] == mmars_event_2.created_at.strftime("%Y-%m-%d")


def test_overpayment_with_adjustment(client, test_db_session, app, mock_admin_user_headers):
    # In this scenario, we create overpayment with an adjustment record
    # In admin panel, we expect to see only the overpayment but not adjustment

    # Create test data
    employee = EmployeeFactory.create(first_name="John", last_name="Doe")
    employee.fineos_customer_number = "123456"

    # Create multiple claims for the employee
    claim = ClaimFactory.create()
    claim.employee = employee

    # Create multiple overpayments for one claim
    overpayment1 = OverpaymentFactory.create(claim_id=claim.claim_id)
    overpayment1.claim = claim
    overpayment1.overpayment_casenumber = "PL ABS-1234567-PL ABS-01-OP1234567"
    overpayment1.recovered_to_date_amount = 50
    overpayment1.outstanding_amount = -150
    # For older overpayments, this field is None. We want to ensure that we could search for overpayments even if this field is None
    overpayment1.payment_transaction_type_id = None
    overpayment1.payment_event_type_id = PaymentEventType.OVERPAYMENT.payment_event_type_id

    overpayment2 = OverpaymentFactory.create(claim_id=claim.claim_id)
    overpayment2.claim = claim
    overpayment2.amount = -10
    overpayment2.payment_event_type_id = (
        PaymentEventType.OVERPAYMENT_ADJUSTMENT.payment_event_type_id
    )

    test_db_session.commit()

    post_body = {"fineos_customer_number": employee.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    assert response.status_code == 200
    assert data is not None, "Data should not be None"
    assert isinstance(data, dict), "Data should be a dictionary"
    assert data["employee_firstname"] == employee.first_name
    assert data["employee_lastname"] == employee.last_name
    assert len(data["overpayment_cases"]) == 1

    # Verify the overpayment cases
    overpayment_cases = data["overpayment_cases"]
    assert overpayment_cases[0]["overpayment_casenumber"] == overpayment1.overpayment_casenumber
    assert overpayment_cases[0]["recovered_to_date_amount"] == overpayment1.recovered_to_date_amount
    assert overpayment_cases[0]["outstanding_amount"] == (-1 * overpayment1.outstanding_amount)


def test_overpayment_search_multiple_cases(client, test_db_session, app, mock_admin_user_headers):

    # Create test data
    employee = EmployeeFactory.create(first_name="John", last_name="Doe")
    employee.fineos_customer_number = "123456"

    # Create multiple claims for the employee
    claim1 = ClaimFactory.create()
    claim1.employee = employee

    claim2 = ClaimFactory.create()
    claim2.employee = employee

    # Create multiple overpayments for each claim
    overpayment1 = OverpaymentFactory.create(claim_id=claim1.claim_id)
    overpayment1.claim = claim1
    overpayment1.overpayment_casenumber = "PL ABS-1234567-PL ABS-01-OP1234567"
    overpayment1.recovered_to_date_amount = 50
    overpayment1.outstanding_amount = -150

    overpayment2 = OverpaymentFactory.create(claim_id=claim1.claim_id)
    overpayment2.claim = claim1
    overpayment2.overpayment_casenumber = "PL ABS-1234568-PL ABS-01-OP1234568"
    overpayment2.recovered_to_date_amount = 60
    overpayment2.outstanding_amount = -140

    overpayment3 = OverpaymentFactory.create(claim_id=claim2.claim_id)
    overpayment3.claim = claim2
    overpayment3.overpayment_casenumber = "PL ABS-1234569-PL ABS-01-OP1234569"
    overpayment3.recovered_to_date_amount = 70
    overpayment3.outstanding_amount = -130

    test_db_session.commit()

    post_body = {"fineos_customer_number": employee.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    assert response.status_code == 200
    assert data is not None, "Data should not be None"
    assert isinstance(data, dict), "Data should be a dictionary"
    assert data["employee_firstname"] == employee.first_name
    assert data["employee_lastname"] == employee.last_name
    assert len(data["overpayment_cases"]) == 3

    # Verify the overpayment cases
    overpayment_cases = data["overpayment_cases"]
    assert any(
        case["overpayment_casenumber"] == overpayment1.overpayment_casenumber
        for case in overpayment_cases
    )
    assert any(
        case["recovered_to_date_amount"] == overpayment1.recovered_to_date_amount
        for case in overpayment_cases
    )
    assert any(
        case["outstanding_amount"] == (-1 * overpayment1.outstanding_amount)
        for case in overpayment_cases
    )

    assert any(
        case["overpayment_casenumber"] == overpayment2.overpayment_casenumber
        for case in overpayment_cases
    )
    assert any(
        case["recovered_to_date_amount"] == overpayment2.recovered_to_date_amount
        for case in overpayment_cases
    )
    assert any(
        case["outstanding_amount"] == (-1 * overpayment2.outstanding_amount)
        for case in overpayment_cases
    )

    assert any(
        case["overpayment_casenumber"] == overpayment3.overpayment_casenumber
        for case in overpayment_cases
    )
    assert any(
        case["recovered_to_date_amount"] == overpayment3.recovered_to_date_amount
        for case in overpayment_cases
    )
    assert any(
        case["outstanding_amount"] == (-1 * overpayment3.outstanding_amount)
        for case in overpayment_cases
    )


def test_overpayment_search_no_overpayments(client, test_db_session, app, mock_admin_user_headers):

    # Create test data
    employee = EmployeeFactory.create(first_name="John", last_name="Doe")
    employee.fineos_customer_number = "123456"

    # Commit the test data to the database
    test_db_session.add(employee)
    test_db_session.commit()

    post_body = {"fineos_customer_number": employee.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    assert response.status_code == 200
    assert data is not None, "Data should not be None"
    assert isinstance(data, dict), "Data should be a dictionary"
    assert data["employee_firstname"] == employee.first_name
    assert data["employee_lastname"] == employee.last_name
    assert data["overpayment_cases"] == [], "Overpayment cases should be an empty list"


def test_overpayment_search_missing_headers(client, test_db_session, app):

    # Create test data
    employee = EmployeeFactory.create(first_name="John", last_name="Doe")
    employee.fineos_customer_number = "123456"

    # Commit the test data to the database
    test_db_session.add(employee)
    test_db_session.commit()

    post_body = {"search": employee.fineos_customer_number}

    # Send POST request to the endpoint without headers
    response = client.post(
        "/v1/admin/overpaymentsearch",
        json=post_body,
    )

    assert response.status_code == 401, "Expected status code to be 401 Unauthorized"


def test_overpayment_search_prevent_referral(client, test_db_session, app, mock_admin_user_headers):
    # Create test data
    employee_1 = EmployeeFactory.create(first_name="John", last_name="Doe")
    employee_1.fineos_customer_number = "123456"
    claim_1 = ClaimFactory.create(employee=employee_1)

    # create an overpayment case with no address
    OverpaymentFactory.create(claim=claim_1)

    # Do not create a ClaimantAddressFactory to simulate missing address

    post_body = {"fineos_customer_number": employee_1.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    # the overpayment cases are non referrable
    assert data["prevent_referral"]

    # we expect the referral prevention reason to be provided
    assert (
        data["referral_prevention_reason"]
        == "Claimant needs to have a valid mailing address provided in Fineos"
    )

    employee_2 = EmployeeFactory.create()
    employee_2.fineos_customer_number = "123457"
    claim_2 = ClaimFactory.create(employee=employee_2)

    # Create an overpayment case with an empty address
    OverpaymentFactory.create(claim=claim_2, employee=employee_2)

    # Create an empty address for the employee
    ClaimantAddressFactory(
        employee_id=employee_2.employee_id, residential_address=None, mailing_address=None
    )

    # Send POST request to the endpoint for the second employee
    # This time, the employee has an empty address
    post_body = {"fineos_customer_number": employee_2.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    # the overpayment cases are non referrable
    assert data["prevent_referral"]

    # we expect the referral prevention reason to be provided
    assert (
        data["referral_prevention_reason"]
        == "Claimant needs to have a valid mailing address provided in Fineos"
    )


def test_overpayment_refer_case(
    client, test_db_session, app, mock_admin_user_headers, mock_valid_admin_user
):
    # The endpoint `/v1/admin/overpayment/refer` is used to refer an overpayment case to MMARS

    overpayment = OverpaymentFactory.create(
        overpayment_casenumber="PL ABS-1234567-PL ABS-01-OP1234567",
        recovered_to_date_amount=50,
        outstanding_amount=-150,
        payment_event_type_id=PaymentEventType.OVERPAYMENT.payment_event_type_id,
        # For older overpayments, this field is None. We want to ensure that we could search for overpayments even if this field is None
        payment_transaction_type_id=None,
    )

    post_body = {"overpayment_id": str(overpayment.overpayment_id)}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/refer",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 200
    response_json = response.json()

    data = response_json.get("data")

    # ensure the data returned is correct
    assert data["overpayment_id"] == str(overpayment.overpayment_id)
    assert data["overpayment_casenumber"] == overpayment.overpayment_casenumber
    assert Decimal(data["amount"]) == pytest.approx(-1 * overpayment.amount)
    assert data["recovered_to_date_amount"] == overpayment.recovered_to_date_amount
    assert Decimal(data["outstanding_amount"]) == pytest.approx(-1 * overpayment.outstanding_amount)
    assert data["overpayment_case_creation_date"] == overpayment.overpayment_date.strftime(
        "%Y-%m-%d"
    )
    assert data["period_start_date"] == overpayment.period_start_date.strftime("%Y-%m-%d")
    assert data["period_end_date"] == overpayment.period_end_date.strftime("%Y-%m-%d")
    assert data["referral_date"] == date.today().strftime("%Y-%m-%d")
    assert (
        data["referral_status"]
        == MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_description
    )

    # Ensure MMArs event is created successfully in the database

    mmars_event = (
        test_db_session.query(MmarsEvent)
        .filter(MmarsEvent.overpayment_id == overpayment.overpayment_id)
        .one()
    )

    assert mmars_event is not None
    assert mmars_event.mmars_event_type_id == MmarsEventType.RE_TRX.mmars_event_type_id
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )
    assert mmars_event.employee_id == overpayment.claim.employee_id

    mmars_event_action_logs = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event.mmars_event_id)
        .all()
    )
    assert len(mmars_event_action_logs) == 1
    mmars_event_action_log = mmars_event_action_logs[0]
    assert mmars_event_action_log is not None
    assert mmars_event_action_log.admin_user_id == mock_valid_admin_user.admin_user_id
    assert (
        mmars_event_action_log.mmars_event_action_type_id
        == MmarsEventActionType.REFERRED.mmars_event_action_type_id
    )
    assert mmars_event_action_log.reason is None


def test_overpayment_refer_case_missing_admin_user(
    client, test_db_session, app, mock_admin_user_headers
):
    # This scenario we mimic the case where the admin user is not present in the database we expect adding mamrs event action log to not raise an expection

    overpayment = OverpaymentFactory.create(
        overpayment_casenumber="PL ABS-1234567-PL ABS-01-OP1234567",
        recovered_to_date_amount=50,
        outstanding_amount=-150,
        payment_event_type_id=PaymentEventType.OVERPAYMENT.payment_event_type_id,
        # For older overpayments, this field is None. We want to ensure that we could search for overpayments even if this field is None
        payment_transaction_type_id=None,
    )

    post_body = {"overpayment_id": str(overpayment.overpayment_id)}

    # Send POST request to the endpoint
    client.post(
        "/v1/admin/overpayment/refer",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    mmars_event = (
        test_db_session.query(MmarsEvent)
        .filter(MmarsEvent.overpayment_id == overpayment.overpayment_id)
        .one()
    )

    mmars_event_action_logs = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event.mmars_event_id)
        .all()
    )
    assert mmars_event_action_logs is not None

    assert len(mmars_event_action_logs) == 1

    mmars_event_action_log = mmars_event_action_logs[0]
    # when session have a user id that is not present admin_user table then this field is assigned as None
    assert mmars_event_action_log.admin_user_id is None
    assert (
        mmars_event_action_log.mmars_event_action_type_id
        == MmarsEventActionType.REFERRED.mmars_event_action_type_id
    )
    assert mmars_event_action_log.reason is None


def test_overpayment_refer_case_missing_overpayment_id(client, mock_admin_user_headers):
    post_body = {}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/refer",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 400
    response_json = response.json()
    errors = response_json.get("errors")
    assert errors is not None
    assert len(errors) == 1
    assert errors[0].get("message") == "'overpayment_id' is a required property"


def test_overpayment_refer_case_overpayment_id_not_in_db(client, mock_admin_user_headers):

    non_existent_overpayment_id = str(uuid.uuid4())
    post_body = {"overpayment_id": non_existent_overpayment_id}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/refer",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 404
    response_json = response.json()
    assert (
        response_json["message"]
        == f"Could not find Overpayment with ID {non_existent_overpayment_id}"
    )


def test_overpayment_refer_case_already_has_mmarsevent(
    client, test_db_session, mock_admin_user_headers
):
    overpayment = OverpaymentFactory.create(
        overpayment_casenumber="PL ABS-1234567-PL ABS-01-OP1234567",
        recovered_to_date_amount=50,
        outstanding_amount=150,
    )

    # Create an existing MMarsEvent for the overpayment
    MmarsEventFactory.create(
        overpayment=overpayment,
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCC_SUBMITTED.mmars_event_status_type_id,
    )

    post_body = {"overpayment_id": str(overpayment.overpayment_id)}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/refer",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 400
    response_json = response.json()
    errors = response_json.get("errors")
    assert errors is not None
    assert len(errors) == 1
    assert (
        response_json["message"]
        == "A transaction for this overpayment already exists, indicating it has been previously referred"
    )


def test_overpayment_refer_case_unauthorized(client, auth_token_unit, mock_admin_user_patch_data):
    post_body = {"overpayment_id": "non_existent_overpayment_id"}

    response = client.post(
        "/v1/admin/overpayment/refer",
        headers={"Authorization": "Bearer fake_bearer_token"},
        json=post_body,
    )

    assert response.status_code == 401


def test_retry_overpayment_transaction(
    client, test_db_session, mock_admin_user_headers, mock_valid_admin_user
):
    # The endpoint `/v1/admin/overpayment/retry-transaction` is used to retry sending a transaction to MMARS
    # for an overpayment that was previously sent to MMARS but failed.
    # The endpoint will update failed MMarsEvent for the overpayment and set the status to ACTIVE.

    # Create an existing MMarsEvent for the overpayment in VCC_ERROR status
    mmars_event = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCC_ERROR.mmars_event_status_type_id,
    )

    post_body = {"mmars_event_id": str(mmars_event.mmars_event_id)}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/retry-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 200
    response_json = response.json()
    data = response_json.get("data")
    assert data is not None
    assert data["mmars_event_id"] == str(mmars_event.mmars_event_id)
    assert data["mmars_event_type_id"] == mmars_event.mmars_event_type_id
    # We expect the status to be  swtiched from MmarsEventStatusType.VCC_ERROR to MmarsEventStatusType.VCC_PENDING
    assert (
        data["mmars_status_type_id"] == MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )
    assert data["created_at"] is not None
    assert data["updated_at"] is not None

    # Create an existing MMarsEvent for the overpayment in RE_ERROR status
    mmars_event = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.RE_ERROR.mmars_event_status_type_id,
    )

    post_body = {"mmars_event_id": str(mmars_event.mmars_event_id)}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/retry-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 200
    response_json = response.json()
    data = response_json.get("data")
    assert data is not None
    assert data["mmars_event_id"] == str(mmars_event.mmars_event_id)
    assert data["mmars_event_type_id"] == mmars_event.mmars_event_type_id
    # We expect the status to be  swtiched from MmarsEventStatusType.RE_ERROR to MmarsEventStatusType.RE_PENDING
    assert (
        data["mmars_status_type_id"] == MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id
    )
    assert data["created_at"] is not None
    assert data["updated_at"] is not None

    mmars_event_action_logs = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event.mmars_event_id)
        .all()
    )
    assert mmars_event_action_logs is not None

    assert len(mmars_event_action_logs) == 1

    mmars_event_action_log = mmars_event_action_logs[0]
    assert mmars_event_action_log.admin_user_id == mock_valid_admin_user.admin_user_id
    assert (
        mmars_event_action_log.mmars_event_action_type_id
        == MmarsEventActionType.RETRIED.mmars_event_action_type_id
    )
    assert mmars_event_action_log.reason is None


def test_retry_overpayment_transaction_missing_mmars_event_id(client, mock_admin_user_headers):
    post_body = {}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/retry-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 400
    response_json = response.json()
    errors = response_json.get("errors")
    assert errors is not None
    assert len(errors) == 1
    assert errors[0].get("message") == "'mmars_event_id' is a required property"


def test_retry_overpayment_transaction_mmars_event_id_not_in_db(client, mock_admin_user_headers):
    non_existent_mmars_event_id = str(uuid.uuid4())
    post_body = {"mmars_event_id": non_existent_mmars_event_id}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/retry-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 404
    response_json = response.json()
    assert (
        response_json["message"]
        == f"Could not find MmarsEvent with ID {non_existent_mmars_event_id}"
    )


def test_retry_overpayment_transaction_invalid_status(
    client, test_db_session, mock_admin_user_headers
):

    # Create an existing MMarsEvent for the overpayment with a status other than MMARS_ERROR
    mmars_event = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCC_SUBMITTED.mmars_event_status_type_id,
    )

    post_body = {"mmars_event_id": str(mmars_event.mmars_event_id)}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/retry-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 400
    response_json = response.json()
    errors = response_json["errors"]
    assert errors is not None
    assert len(errors) > 0
    assert errors[0]["field"] == "mmars_event_id"
    assert errors[0]["message"] == "Invalid status for retry"
    assert (
        response_json["message"]
        == "Cannot retry overpayment transaction, status is not in an error stage."
    )


def test_hold_overpayment_transaction(
    client, test_db_session, mock_admin_user_headers, mock_valid_admin_user
):
    # The endpoint `/v1/admin/overpayment/hold-transaction` is used to holding a transaction to send to MMARS
    # The endpoint will update MMarsEvent for the overpayment and set the status to ON_HOLD.

    # Create an existing MMarsEvent for the overpayment in VCC_PENDING status
    mmars_event = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id,
    )

    hold_reason = "Hold for further investigation"
    post_body = {"mmars_event_id": str(mmars_event.mmars_event_id), "reason": hold_reason}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/hold-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 200
    response_json = response.json()
    data = response_json.get("data")
    assert data is not None
    assert data["mmars_event_id"] == str(mmars_event.mmars_event_id)
    assert data["mmars_event_type_id"] == mmars_event.mmars_event_type_id
    # We expect the status to be  swtiched from MmarsEventStatusType.VCC_ERROR to MmarsEventStatusType.VCC_PENDING
    assert data["mmars_status_type_id"] == MmarsEventStatusType.ON_HOLD.mmars_event_status_type_id
    assert data["created_at"] is not None
    assert data["updated_at"] is not None

    mmars_event_action_log = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event.mmars_event_id)
        .one()
    )
    assert mmars_event_action_log is not None
    assert mmars_event_action_log.admin_user_id == mock_valid_admin_user.admin_user_id
    assert (
        mmars_event_action_log.mmars_event_action_type_id
        == MmarsEventActionType.HELD.mmars_event_action_type_id
    )
    assert mmars_event_action_log.reason == hold_reason

    # Create an existing MMarsEvent for the overpayment in RE_ERROR status
    mmars_event_2 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
    )

    hold_reason_2 = "Hold for further investigation #2"
    post_body = {"mmars_event_id": str(mmars_event_2.mmars_event_id), "reason": hold_reason_2}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/hold-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Assert returned data
    assert response.status_code == 200
    response_json = response.json()
    data = response_json.get("data")
    assert data is not None
    assert data["mmars_event_id"] == str(mmars_event_2.mmars_event_id)
    assert data["mmars_event_type_id"] == mmars_event_2.mmars_event_type_id
    # We expect the status to be  swtiched from MmarsEventStatusType.REM_PENDING to MmarsEventStatusType.ON_HOLD
    assert data["mmars_status_type_id"] == MmarsEventStatusType.ON_HOLD.mmars_event_status_type_id
    assert data["created_at"] is not None
    assert data["updated_at"] is not None

    mmars_event_action_log = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event_2.mmars_event_id)
        .one()
    )
    assert mmars_event_action_log is not None
    assert mmars_event_action_log.admin_user_id == mock_valid_admin_user.admin_user_id
    assert (
        mmars_event_action_log.mmars_event_action_type_id
        == MmarsEventActionType.HELD.mmars_event_action_type_id
    )
    assert mmars_event_action_log.reason == hold_reason_2


def test_release_overpayment_transaction(
    client, test_db_session, mock_admin_user_headers, mock_valid_admin_user
):
    # The endpoint `/v1/admin/overpayment/release-transaction` is used to remove a hold on a transaction.
    # Removing hold will set the status of the MMarsEvent to VCC_PENDING or REM_PENDING.

    # Create an existing MMarsEvent for the overpayment in VCC_PENDING status
    mmars_event = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCC_SUBMITTED.mmars_event_status_type_id,
    )

    hold_post_body = {
        "mmars_event_id": str(mmars_event.mmars_event_id),
        "reason": "Hold for further investigation",
    }

    # Send POST request to the endpoint
    # This will hold the transaction
    response = client.post(
        "/v1/admin/overpayment/hold-transaction",
        headers=mock_admin_user_headers,
        json=hold_post_body,
    )

    release_post_body = {
        "mmars_event_id": str(mmars_event.mmars_event_id),
        "reason": "Investigation is completed and transaction is ready to be sent to MMARS",
    }
    response = client.post(
        "/v1/admin/overpayment/release-transaction",
        headers=mock_admin_user_headers,
        json=release_post_body,
    )

    # Assert returned data
    assert response.status_code == 200
    response_json = response.json()
    data = response_json.get("data")
    assert data is not None
    assert data["mmars_event_id"] == str(mmars_event.mmars_event_id)
    assert data["mmars_event_type_id"] == mmars_event.mmars_event_type_id
    # We expect the status to be  swtiched from MmarsEventStatusType.VCC_ERROR to MmarsEventStatusType.VCC_PENDING
    assert (
        data["mmars_status_type_id"] == MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )
    assert data["created_at"] is not None
    assert data["updated_at"] is not None

    mmars_event_action_log = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event.mmars_event_id)
        .filter(
            MmarsEventActionLog.mmars_event_action_type_id
            == MmarsEventActionType.RELEASED.mmars_event_action_type_id
        )
        .first()
    )

    assert mmars_event_action_log is not None
    assert mmars_event_action_log.reason == release_post_body["reason"]

    # Create an existing MMarsEvent for the overpayment in REM_ERROR status
    mmars_event = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_ERROR.mmars_event_status_type_id,
    )

    hold_post_body = {
        "mmars_event_id": str(mmars_event.mmars_event_id),
        "reason": "Hold for further investigation",
    }

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpayment/hold-transaction",
        headers=mock_admin_user_headers,
        json=hold_post_body,
    )

    release_post_body = {
        "mmars_event_id": str(mmars_event.mmars_event_id),
    }

    response = client.post(
        "/v1/admin/overpayment/release-transaction",
        headers=mock_admin_user_headers,
        json=release_post_body,
    )

    # Assert returned data
    assert response.status_code == 200
    response_json = response.json()
    data = response_json.get("data")
    assert data is not None
    assert data["mmars_event_id"] == str(mmars_event.mmars_event_id)
    assert data["mmars_event_type_id"] == mmars_event.mmars_event_type_id
    # We expect the status to be  swtiched from MmarsEventStatusType.ON_HOLD to MmarsEventStatusType.REM_PENDING
    assert (
        data["mmars_status_type_id"] == MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id
    )
    assert data["created_at"] is not None
    assert data["updated_at"] is not None

    mmars_event_action_log = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event.mmars_event_id)
        .filter(
            MmarsEventActionLog.mmars_event_action_type_id
            == MmarsEventActionType.RELEASED.mmars_event_action_type_id
        )
        .first()
    )

    # test mmars event action log is created for the release action without a reason provided
    assert mmars_event_action_log.reason is None


def test_hold_overpayment_transaction_invalid_inputs(
    client, test_db_session, mock_admin_user_headers
):
    # Test case for missing mmars_event_id
    post_body = {"reason": "Hold for further investigation"}
    response = client.post(
        "/v1/admin/overpayment/hold-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )
    assert response.status_code == 400
    response_json = response.json()
    errors = response_json.get("errors")
    assert errors is not None
    assert len(errors) == 1
    assert errors[0].get("message") == "'mmars_event_id' is a required property"

    # Test case for invalid mmars_event_id format
    post_body = {"mmars_event_id": "invalid-uuid", "reason": "Hold for further investigation"}
    response = client.post(
        "/v1/admin/overpayment/hold-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )
    assert response.status_code == 400
    response_json = response.json()
    errors = response_json.get("errors")
    assert errors is not None
    assert len(errors) == 1
    assert errors[0].get("message") == "'invalid-uuid' is not a 'uuid'"

    # Test case for non-existent mmars_event_id
    non_existent_mmars_event_id = str(uuid.uuid4())
    post_body = {
        "mmars_event_id": non_existent_mmars_event_id,
        "reason": "Hold for further investigation",
    }
    response = client.post(
        "/v1/admin/overpayment/hold-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )
    assert response.status_code == 404
    response_json = response.json()
    assert (
        response_json["message"]
        == f"Could not find MmarsEvent with ID {non_existent_mmars_event_id}"
    )

    # Test case for mmars_event_id in non-holdable status
    mmars_event = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.RE_SUBMITTED.mmars_event_status_type_id,
    )
    post_body = {
        "mmars_event_id": str(mmars_event.mmars_event_id),
        "reason": "Hold for further investigation",
    }
    response = client.post(
        "/v1/admin/overpayment/hold-transaction",
        headers=mock_admin_user_headers,
        json=post_body,
    )
    assert response.status_code == 400
    response_json = response.json()
    errors = response_json.get("errors")
    assert errors is not None
    assert len(errors) == 1
    assert errors[0].get("message") == "Invalid status for hold"
    assert (
        response_json["message"]
        == "Cannot hold overpayment transaction, status is not in a holdable stage."
    )


def test_overpayment_search_with_holdable_and_releasable_flags(
    client, test_db_session, app, mock_admin_user_headers
):
    # Create test data
    employee = EmployeeFactory.create(first_name="John", last_name="Doe")
    employee.fineos_customer_number = "123456"
    claim = ClaimFactory.create(employee=employee)

    # Create overpayment
    overpayment = OverpaymentFactory.create(claim_id=claim.claim_id)
    overpayment.claim = claim
    overpayment.employee = employee
    overpayment.overpayment_casenumber = "PL ABS-1234567-PL ABS-01-OP1234567"

    # Create MMarsEvent with is_holdable and is_releasable flags
    mmars_event_holdable_1 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event_holdable_2 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event_holdable_3 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event_holdable_4 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCC_SUBMITTED.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event_holdable_5 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event_releasable = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.ON_HOLD.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event_non_holdable_non_releasable_1 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.RE_SUBMITTED.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event_non_holdable_non_releasable_2 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_SUBMITTED.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event_non_holdable_non_releasable_3 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.RE_SUCCESS.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event_non_holdable_non_releasable_4 = MmarsEventFactory.create(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_SUCCESS.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    test_db_session.commit()
    post_body = {"fineos_customer_number": employee.fineos_customer_number}

    # Send POST request to the endpoint
    response = client.post(
        "/v1/admin/overpaymentsearch",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    assert response.status_code == 200
    assert data is not None, "Data should not be None"
    assert isinstance(data, dict), "Data should be a dictionary"

    overpayment_cases = data["overpayment_cases"]
    assert len(overpayment_cases) == 1

    mmars_events = overpayment_cases[0]["mmars_events"]
    assert len(mmars_events) == 10

    # Create a dictionary to map mmars_event_id to the event
    mmars_events_dict = {event["mmars_event_id"]: event for event in mmars_events}

    # Verify the holdable MMarsEvent
    holdable_event = mmars_events_dict[str(mmars_event_holdable_1.mmars_event_id)]
    assert holdable_event["is_holdable"] is True
    assert holdable_event["is_releasable"] is False

    # Verify the holdable MMarsEvent
    holdable_event = mmars_events_dict[str(mmars_event_holdable_2.mmars_event_id)]
    assert holdable_event["is_holdable"] is True
    assert holdable_event["is_releasable"] is False

    # Verify the holdable MMarsEvent
    holdable_event = mmars_events_dict[str(mmars_event_holdable_3.mmars_event_id)]
    assert holdable_event["is_holdable"] is True
    assert holdable_event["is_releasable"] is False

    # Verify the holdable MMarsEvent
    holdable_event = mmars_events_dict[str(mmars_event_holdable_4.mmars_event_id)]
    assert holdable_event["is_holdable"] is True
    assert holdable_event["is_releasable"] is False

    # Verify the holdable MMarsEvent
    holdable_event = mmars_events_dict[str(mmars_event_holdable_5.mmars_event_id)]
    assert holdable_event["is_holdable"] is True
    assert holdable_event["is_releasable"] is False

    # Verify the releasable MMarsEvent
    releasable_event = mmars_events_dict[str(mmars_event_releasable.mmars_event_id)]
    assert releasable_event["is_holdable"] is False
    assert releasable_event["is_releasable"] is True

    # Verify the releasable MMarsEvent
    releasable_event = mmars_events_dict[
        str(mmars_event_non_holdable_non_releasable_1.mmars_event_id)
    ]
    assert releasable_event["is_holdable"] is False
    assert releasable_event["is_releasable"] is False

    # Verify the releasable MMarsEvent
    releasable_event = mmars_events_dict[
        str(mmars_event_non_holdable_non_releasable_2.mmars_event_id)
    ]
    assert releasable_event["is_holdable"] is False
    assert releasable_event["is_releasable"] is False

    # Verify the releasable MMarsEvent
    releasable_event = mmars_events_dict[
        str(mmars_event_non_holdable_non_releasable_3.mmars_event_id)
    ]
    assert releasable_event["is_holdable"] is False
    assert releasable_event["is_releasable"] is False

    # Verify the releasable MMarsEvent
    releasable_event = mmars_events_dict[
        str(mmars_event_non_holdable_non_releasable_4.mmars_event_id)
    ]
    assert releasable_event["is_holdable"] is False
    assert releasable_event["is_releasable"] is False


def test_overpayment_vcm_report(client, test_db_session, app, mock_admin_user_headers):

    mmars_customer_detail_factory_1 = MmarsCustomerDetailFactory.create()

    # Factory does not add fineos_customer_number to the employee, so we add it manually
    mmars_customer_detail_factory_1.mmars_event.employee.fineos_customer_number = "123456"

    mmars_customer_detail_factory_2 = MmarsCustomerDetailFactory.create()

    # Factory does not add fineos_customer_number to the employee, so we add it manually
    mmars_customer_detail_factory_2.mmars_event.employee.fineos_customer_number = "654321"

    # call endpoint
    response = client.get(
        "/v1/admin/overpayment/vcm-report",
        headers=mock_admin_user_headers,
    )

    response_json = response.json()
    data = response_json.get("data")
    assert data is not None
    assert len(data) == 2

    data_dict = {item["fineos_customer_number"]: item for item in data}

    assert "123456" in data_dict
    vcm_response_json = data_dict["123456"]
    vcm_response = VCMComparisonResponse.parse_obj(vcm_response_json)

    assert vcm_response.customer_active_status_name is not None
    assert vcm_response.customer_approval_status_name is not None
    assert vcm_response.employee_id is not None
    assert vcm_response.pfml is not None
    assert vcm_response.edm is not None

    assert vcm_response.pfml.first_name is not None
    assert vcm_response.pfml.last_name is not None
    assert vcm_response.pfml.legal_name is not None
    assert vcm_response.pfml.address is not None
    assert vcm_response.pfml.address.line_1 is not None
    assert vcm_response.pfml.address.city is not None
    assert vcm_response.pfml.address.state is not None
    assert vcm_response.pfml.address.zip is not None

    assert vcm_response.edm.first_name is not None
    assert vcm_response.edm.last_name is not None
    assert vcm_response.edm.legal_name is not None
    assert vcm_response.edm.address is not None
    assert vcm_response.edm.address.line_1 is not None
    assert vcm_response.edm.address.city is not None
    assert vcm_response.edm.address.state is not None
    assert vcm_response.edm.address.zip is not None

    assert "654321" in data_dict
    vcm_response_json = data_dict["654321"]
    vcm_response = VCMComparisonResponse.parse_obj(vcm_response_json)

    assert vcm_response.customer_active_status_name is not None
    assert vcm_response.customer_approval_status_name is not None
    assert vcm_response.employee_id is not None
    assert vcm_response.pfml is not None
    assert vcm_response.edm is not None

    assert vcm_response.pfml.first_name is not None
    assert vcm_response.pfml.last_name is not None
    assert vcm_response.pfml.legal_name is not None
    assert vcm_response.pfml.address is not None
    assert vcm_response.pfml.address.line_1 is not None
    assert vcm_response.pfml.address.city is not None
    assert vcm_response.pfml.address.state is not None
    assert vcm_response.pfml.address.zip is not None

    assert vcm_response.edm.first_name is not None
    assert vcm_response.edm.last_name is not None
    assert vcm_response.edm.legal_name is not None
    assert vcm_response.edm.address is not None
    assert vcm_response.edm.address.line_1 is not None
    assert vcm_response.edm.address.city is not None
    assert vcm_response.edm.address.state is not None
    assert vcm_response.edm.address.zip is not None


# In thsi scenario we ensure that duplicate employees are skipped in the VCM report
def test_overpayment_vcm_report_duplicate_employee(
    client, test_db_session, app, mock_admin_user_headers
):

    employee = EmployeeFactory.create(
        first_name="John", last_name="Doe", fineos_customer_number="123456"
    )

    ClaimantAddressFactory(
        employee_id=employee.employee_id,
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_two="original mailing address")
        ),
    )

    # Create two MmarsEvent records for the same employee with VCM_REQUIRE status
    MmarsEventFactory.create(
        employee=employee,
        mmars_event_type_id=MmarsEventType.VCC_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
    )

    MmarsEventFactory.create(
        employee=employee,
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
    )

    # Create corresponding MmarsCustomerDetail records for the events
    MmarsCustomerDetailFactory.create(mmars_event=MmarsEventFactory.create(employee=employee))

    # Call the endpoint
    response = client.get(
        "/v1/admin/overpayment/vcm-report",
        headers=mock_admin_user_headers,
    )

    # Parse the response
    response_json = response.json()
    data = response_json.get("data")

    # Assert the response
    assert response.status_code == 200
    assert data is not None, "Data should not be None"
    assert isinstance(data, list), "Data should be a list"

    # Ensure only one record is returned for the employee
    assert len(data) == 1, "Duplicate employees should be skipped in the VCM report"

    # Validate the returned data
    vcm_response = VCMComparisonResponse.parse_obj(data[0])
    assert vcm_response.fineos_customer_number == "123456"
    assert vcm_response.pfml is not None
    assert vcm_response.edm is None


def test_overpayment_vcm_report_without_mmars_customer_detail(
    client, test_db_session, app, mock_admin_user_headers
):

    employee = EmployeeFactory.create(
        first_name="John", last_name="Doe", fineos_customer_number="123456"
    )

    MmarsEventFactory.create(
        employee=employee,
        mmars_event_type_id=MmarsEventType.VCC_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
    )

    ClaimantAddressFactory(
        employee_id=employee.employee_id,
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_two="original mailing address")
        ),
    )

    response = client.get(
        "/v1/admin/overpayment/vcm-report",
        headers=mock_admin_user_headers,
    )

    response_json = response.json()
    data = response_json.get("data")

    assert response.status_code == 200
    assert data is not None, "Data should not be None"
    assert isinstance(data, list), "Data should be a list"
    assert len(data) == 1, "There should be one VCMComparisonResponse in the response"

    # validate the returned VCMComparisonResponse
    vcm_response = VCMComparisonResponse.parse_obj(data[0])

    assert vcm_response.fineos_customer_number == "123456"
    assert vcm_response.customer_active_status_name is None
    assert vcm_response.customer_approval_status_name is None
    assert vcm_response.pfml is not None
    # we expect edm to be null
    assert vcm_response.edm is None

    # Validate the PFML data
    pfml = vcm_response.pfml
    assert pfml.first_name == "John"
    assert pfml.last_name == "Doe"
    assert pfml.legal_name == "John Doe"
    assert pfml.address is not None
    assert pfml.address.line_1 is not None
    assert pfml.address.city is not None
    assert pfml.address.state is not None
    assert pfml.address.zip is not None


def test_mark_overpayment_vcm_reviewed(
    client, test_db_session, app, mock_admin_user_headers, mock_valid_admin_user
):
    # The endpoint `/v1/admin/overpayment/mark-vcm-reviewed` is used to marking a VCM Required mmars events as reviewed.
    # Marking a VCM Required mmars events as reviewed will set the status of the MMarsEvent to VCC_PENDING.

    employee = EmployeeFactory.create(
        first_name="John", last_name="Doe", fineos_customer_number="123456"
    )

    mmars_event = MmarsEventFactory.create(
        employee=employee,
        mmars_event_type_id=MmarsEventType.VCC_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
    )

    post_body = {"employee_id": str(employee.employee_id)}

    response = client.post(
        "/v1/admin/overpayment/mark-vcm-reviewed",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    test_db_session.refresh(mmars_event)

    assert response.status_code == 200
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )

    mmars_event_actions = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event.mmars_event_id)
        .all()
    )

    assert mmars_event_actions is not None

    mmars_event_action = mmars_event_actions[0]
    assert mmars_event_action.admin_user_id == mock_valid_admin_user.admin_user_id
    assert (
        mmars_event_action.mmars_event_action_type_id
        == MmarsEventActionType.VCM_REVIEWED.mmars_event_action_type_id
    )


# in this scenario, we test that multiple mmars events for a single employee are marked as reviewed
def test_mark_overpayment_vcm_reviewed_multiple_mmars_event_for_single_employee(
    client, test_db_session, app, mock_admin_user_headers, mock_valid_admin_user
):

    employee = EmployeeFactory.create(
        first_name="John", last_name="Doe", fineos_customer_number="123456"
    )

    mmars_event_1 = MmarsEventFactory.create(
        employee=employee,
        mmars_event_type_id=MmarsEventType.VCC_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
    )

    mmars_event_2 = MmarsEventFactory.create(
        employee=employee,
        mmars_event_type_id=MmarsEventType.VCC_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
    )

    post_body = {"employee_id": str(employee.employee_id)}

    response = client.post(
        "/v1/admin/overpayment/mark-vcm-reviewed",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    test_db_session.refresh(mmars_event_1)
    test_db_session.refresh(mmars_event_2)

    assert response.status_code == 200
    assert (
        mmars_event_1.mmars_status_type_id
        == MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )

    mmars_event_action = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event_1.mmars_event_id)
        .one()
    )

    assert mmars_event_action is not None
    assert mmars_event_action.admin_user_id == mock_valid_admin_user.admin_user_id
    assert (
        mmars_event_action.mmars_event_action_type_id
        == MmarsEventActionType.VCM_REVIEWED.mmars_event_action_type_id
    )

    assert (
        mmars_event_2.mmars_status_type_id
        == MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )

    mmars_event_action = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event_2.mmars_event_id)
        .one()
    )

    assert mmars_event_action is not None
    assert mmars_event_action.admin_user_id == mock_valid_admin_user.admin_user_id
    assert (
        mmars_event_action.mmars_event_action_type_id
        == MmarsEventActionType.VCM_REVIEWED.mmars_event_action_type_id
    )


# In this scenario, we test that multiple mmars events for a single employee with one mmars event in VCM_REQUIRE status and other in RE_SUBMITTED
# Expected behavior is that only the mmars event in VCM_REQUIRE status is marked as reviewed (and swithced to VCC_PENDING)
def test_mark_overpayment_vcm_reviewed_multiple_mmars_event_for_single_employee_different_status(
    client, test_db_session, app, mock_admin_user_headers, mock_valid_admin_user
):

    employee = EmployeeFactory.create(
        first_name="John", last_name="Doe", fineos_customer_number="123456"
    )

    mmars_event_vcm_require = MmarsEventFactory.create(
        employee=employee,
        mmars_event_type_id=MmarsEventType.VCC_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
    )

    # Create another MmarsEvent with a different status (RE_SUBMITTED)
    # This overpayment is already submitted to MMARS
    mmars_event_re_submitted = MmarsEventFactory.create(
        employee=employee,
        mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.RE_SUBMITTED.mmars_event_status_type_id,
    )

    post_body = {"employee_id": str(employee.employee_id)}

    response = client.post(
        "/v1/admin/overpayment/mark-vcm-reviewed",
        headers=mock_admin_user_headers,
        json=post_body,
    )

    # Refresh the database session to get updated data
    test_db_session.refresh(mmars_event_vcm_require)
    test_db_session.refresh(mmars_event_re_submitted)

    assert response.status_code == 200

    # MmarsEvent with VCM_REQUIRE status is updated to VCC_PENDING
    assert (
        mmars_event_vcm_require.mmars_status_type_id
        == MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )

    # MmarsEvent with RE_SUBMITTED status remains unchanged
    assert (
        mmars_event_re_submitted.mmars_status_type_id
        == MmarsEventStatusType.RE_SUBMITTED.mmars_event_status_type_id
    )

    # Verify that an action log is created for the updated MmarsEvent
    mmars_event_action = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event_vcm_require.mmars_event_id)
        .one()
    )

    assert mmars_event_action is not None
    assert mmars_event_action.admin_user_id == mock_valid_admin_user.admin_user_id
    assert (
        mmars_event_action.mmars_event_action_type_id
        == MmarsEventActionType.VCM_REVIEWED.mmars_event_action_type_id
    )

    mmars_event_action = (
        test_db_session.query(MmarsEventActionLog)
        .filter(MmarsEventActionLog.mmars_event_id == mmars_event_re_submitted.mmars_event_id)
        .first()
    )

    assert mmars_event_action is None


def test_omni_search_users_ids(client, app, mock_admin_user_headers):
    user = UserFactory.create()
    UserFactory.create()
    UserFactory.create(auth_id=user.user_id)
    post_body = {"search": str(user.user_id)}
    with app.app.test_request_context("/v1/admin/users"):
        response = client.post(
            "/v1/admin/omnisearch/users",
            headers=mock_admin_user_headers,
            json=post_body,
        )
        response_json = response.json()
        data = response_json.get("data")
        assert response.status_code == 200
        assert len(data) == 2


def test_omni_search_users_escaped(client, app, mock_admin_user_headers):
    UserFactory.create(email_address="<EMAIL>")
    UserFactory.create(email_address="<EMAIL>")
    UserFactory.create(email_address="<EMAIL>")
    UserFactory.create()
    post_body = {"search": "test_foo"}

    with app.app.test_request_context("/v1/admin/users"):
        response = client.post(
            "/v1/admin/omnisearch/users",
            headers=mock_admin_user_headers,
            json=post_body,
        )
        response_json = response.json()
        data = response_json.get("data")
        assert response.status_code == 200
        assert len(data) == 1


def test_omni_search_get_user_detail(client, app, mock_admin_user_headers):
    user = UserFactory.create(
        email_address="<EMAIL>", first_name="test_first_name", last_name="test_last_name"
    )
    with app.app.test_request_context("/v1/admin/omnisearch/"):
        response = client.get(
            f"/v1/admin/omnisearch/users/{user.user_id}",
            headers=mock_admin_user_headers,
        )
        response_json = response.json()
        data = response_json.get("data")
        assert response.status_code == 200
        assert data.get("email_address") == user.email_address


def test_omni_search_get_user_auth_logs(client, app, mock_admin_user_headers):
    user = UserFactory.create(
        email_address="<EMAIL>", first_name="test_first_name", last_name="test_last_name"
    )
    user_auth_log1 = UserAuthLogFactory.create(user_id=user.user_id)
    user_auth_log2 = UserAuthLogFactory.create(user_id=user.user_id, oauth_operation_id=2)
    with app.app.test_request_context("/v1/admin/omnisearch/users/auth-log/"):
        response = client.get(
            f"/v1/admin/omnisearch/users/{user.user_id}/auth-log",
            headers=mock_admin_user_headers,
        )
        response_json = response.json()
        data = response_json.get("data")
        assert response.status_code == 200
        assert len(data) == 2
        assert int(data[0].get("oauth_operation_id")) == user_auth_log1.oauth_operation_id
        assert int(data[1].get("oauth_operation_id")) == user_auth_log2.oauth_operation_id


@pytest.fixture
def mock_admin_update_data():
    return {
        "ticket_num": "TICKET-1234",
    }


@pytest.fixture
def mock_admin_user_patch_data(mock_admin_update_data):
    return {**mock_admin_update_data, "email_address": "<EMAIL>", "auth_id": uuid.uuid4()}


def test_admin_users_patch_unauthorized(client, auth_token_unit, mock_admin_user_patch_data):
    response = client.patch(
        f"/v1/admin/users/{uuid.uuid4()}",
        headers={"Authorization": f"Bearer {auth_token_unit}"},
        json=apply_custom_encoder(mock_admin_user_patch_data),
    )
    assert response.status_code == 401


def test_admin_users_patch_no_user(client, mock_admin_user_headers, mock_admin_user_patch_data):
    response = client.patch(
        f"/v1/admin/users/{uuid.uuid4()}",
        headers=mock_admin_user_headers,
        json=apply_custom_encoder(mock_admin_user_patch_data),
    )
    assert response.status_code == 404


@pytest.fixture
def mock_admin_update_ticket_error():
    return {"field": "ticket_num", "message": "Ticket number is required.", "type": "required"}


def test_admin_users_patch_empty_ticket(
    client, mock_admin_user_headers, mock_admin_user_patch_data, mock_admin_update_ticket_error
):
    patch_body = {**mock_admin_user_patch_data, "ticket_num": "   "}
    response = client.patch(
        f"/v1/admin/users/{uuid.uuid4()}",
        headers=mock_admin_user_headers,
        json=apply_custom_encoder(patch_body),
    )
    assert response.status_code == 400
    assert response.json().get("errors") == [mock_admin_update_ticket_error]


@pytest.fixture
def mock_admin_user_patch_user(test_db_session):
    user_to_update: User = UserFactory.create(
        email_address="<EMAIL>",
    )
    # detach created instance to avoid reference cross-talk
    test_db_session.expunge(user_to_update)
    return user_to_update


def test_admin_users_patch_success(
    client,
    mock_admin_user_headers,
    test_db_session,
    mock_admin_user_patch_user,
    mock_admin_user_patch_data,
    mock_valid_admin_user,
):
    new_email_address = "<EMAIL>"
    new_auth_id = uuid.uuid4()
    patch_body = {
        **mock_admin_user_patch_data,
        "email_address": new_email_address,
        "auth_id": new_auth_id,
    }

    response = client.patch(
        f"/v1/admin/users/{mock_admin_user_patch_user.user_id}",
        headers=mock_admin_user_headers,
        json=apply_custom_encoder(patch_body),
    )
    assert response.status_code == 200

    updated_user = (
        test_db_session.query(User).filter_by(user_id=mock_admin_user_patch_user.user_id).first()
    )
    admin_audit: AdminAuditLog = test_db_session.query(AdminAuditLog).first()

    assert admin_audit is not None
    assert admin_audit.admin_user_id == mock_valid_admin_user.admin_user_id
    assert admin_audit.record_id == mock_admin_user_patch_user.user_id
    assert admin_audit.record_type == "user"
    assert admin_audit.ticket_num == mock_admin_user_patch_data["ticket_num"]
    assert mock_admin_user_patch_user.email_address in admin_audit.state_before
    assert mock_admin_user_patch_user.auth_id in admin_audit.state_before
    assert new_email_address.lower() in admin_audit.state_after
    assert str(new_auth_id) in admin_audit.state_after
    assert updated_user.email_address == new_email_address.lower()
    assert updated_user.auth_id == str(new_auth_id)


def test_admin_users_patch_empty_authid_success(
    client,
    mock_admin_user_headers,
    test_db_session,
    mock_admin_user_patch_user,
    mock_admin_user_patch_data,
    mock_valid_admin_user,
):
    """Tests that the auth_id can be set to an empty string"""

    # make another user with an empty auth_id (even though this would cause api validation issues)
    UserFactory.create(email_address="<EMAIL>", auth_id="")
    # make some users with an empty auth_id
    UserFactory.create_batch(5, auth_id=None)

    patch_body = {
        **mock_admin_user_patch_data,
        "email_address": mock_admin_user_patch_user.email_address,
        "auth_id": "",
    }
    response = client.patch(
        f"/v1/admin/users/{mock_admin_user_patch_user.user_id}",
        headers=mock_admin_user_headers,
        json=patch_body,
    )
    assert response.status_code == 200

    updated_user = (
        test_db_session.query(User).filter_by(user_id=mock_admin_user_patch_user.user_id).first()
    )
    assert updated_user.auth_id is None


@pytest.fixture
def mock_admin_user_patch_email_conflict(initialize_factories_session):
    # make conflict user
    return UserFactory.create(email_address="<EMAIL>")


@pytest.fixture
def mock_admin_user_patch_email_error():
    return {
        "field": "email_address",
        "message": "User already exists with email address: '<EMAIL>'",
        "type": "exists",
    }


def test_admin_users_patch_email_conflict(
    client,
    mock_admin_user_headers,
    mock_admin_user_patch_user,
    mock_admin_user_patch_email_conflict,
    mock_admin_user_patch_email_error,
    mock_admin_user_patch_data,
):
    patch_body = {
        **mock_admin_user_patch_data,
        "email_address": mock_admin_user_patch_email_conflict.email_address,
    }
    response = client.patch(
        f"/v1/admin/users/{mock_admin_user_patch_user.user_id}",
        headers=mock_admin_user_headers,
        json=apply_custom_encoder(patch_body),
    )

    assert response.json().get("errors") == [mock_admin_user_patch_email_error]
    assert response.status_code == 400


@pytest.fixture
def mock_admin_user_patch_authid_conflict(initialize_factories_session):
    # make conflict user
    return UserFactory.create(auth_id="1dff529b-fb00-403b-abee-133f8258e134")


@pytest.fixture
def mock_admin_user_patch_authid_error():
    return {
        "field": "auth_id",
        "message": "User already exists with auth id: 1dff529b-fb00-403b-abee-133f8258e134",
        "type": "exists",
    }


def test_admin_users_patch_auth_id_conflict(
    client,
    mock_admin_user_headers,
    mock_admin_user_patch_user,
    mock_admin_user_patch_authid_conflict,
    mock_admin_user_patch_authid_error,
    mock_admin_user_patch_data,
):
    patch_body = {
        **mock_admin_user_patch_data,
        "auth_id": mock_admin_user_patch_authid_conflict.auth_id,
    }
    response = client.patch(
        f"/v1/admin/users/{mock_admin_user_patch_user.user_id}",
        headers=mock_admin_user_headers,
        json=patch_body,
    )

    assert response.json().get("errors") == [mock_admin_user_patch_authid_error]
    assert response.status_code == 400


def test_admin_users_patch_multi_conflict(
    client,
    mock_admin_user_headers,
    mock_admin_user_patch_user,
    mock_admin_user_patch_email_conflict,
    mock_admin_user_patch_email_error,
    mock_admin_user_patch_authid_conflict,
    mock_admin_user_patch_authid_error,
    mock_admin_user_patch_data,
):
    patch_body = {
        **mock_admin_user_patch_data,
        "email_address": mock_admin_user_patch_email_conflict.email_address,
        "auth_id": mock_admin_user_patch_authid_conflict.auth_id,
    }
    response = client.patch(
        f"/v1/admin/users/{mock_admin_user_patch_user.user_id}",
        headers=mock_admin_user_headers,
        json=patch_body,
    )

    errors = response.json().get("errors")

    assert mock_admin_user_patch_authid_error in errors
    assert mock_admin_user_patch_email_error in errors
    assert response.status_code == 400


def test_admin_user_role_add_to_claimant(
    client,
    mock_admin_user_headers,
    user,
    mock_admin_update_data,
    mock_valid_admin_user,
    test_db_session,
):
    user_query = test_db_session.query(UserRole).filter_by(user_id=user.user_id)
    user_role = user_query.first()
    assert user_role is None

    response = client.post(
        f"/v1/admin/users/{user.user_id}/roles/{Role.EMPLOYER.role_id}",
        headers=mock_admin_user_headers,
        json=mock_admin_update_data,
    )

    user_role = user_query.first()
    assert user_role is not None
    assert "Role was added" in response.json().get("message")
    assert response.status_code == 200


def test_admin_user_role_add_to_employer(
    client,
    mock_admin_user_headers,
    employer_user,
    mock_admin_update_data,
    mock_valid_admin_user,
    test_db_session,
):
    user_query = test_db_session.query(UserRole).filter_by(user_id=employer_user.user_id)
    user_role = user_query.first()
    assert user_role is not None

    response = client.post(
        f"/v1/admin/users/{employer_user.user_id}/roles/{employer_user.roles[0].role_id}",
        headers=mock_admin_user_headers,
        json=mock_admin_update_data,
    )

    assert "Role was already assigned" in response.json().get("message")
    assert response.status_code == 200


def test_admin_user_role_delete_employer(
    client,
    mock_admin_user_headers,
    employer_user,
    mock_admin_update_data,
    mock_valid_admin_user,
    test_db_session,
):
    user_query = test_db_session.query(UserRole).filter_by(user_id=employer_user.user_id)
    user_role = user_query.first()
    assert user_role is not None

    response = delete_request_with_body(
        client,
        url=f"/v1/admin/users/{employer_user.user_id}/roles/{employer_user.roles[0].role_id}",
        headers=mock_admin_user_headers,
        json=apply_custom_encoder(mock_admin_update_data),
    )
    user_role = user_query.first()
    assert user_role is None
    assert "Role was deleted" in response.json().get("message")
    assert response.status_code == 200


def test_admin_user_role_delete_claimant(
    client,
    mock_admin_user_headers,
    user,
    mock_admin_update_data,
    mock_valid_admin_user,
    test_db_session,
):
    user_query = test_db_session.query(UserRole).filter_by(user_id=user.user_id)
    user_role = user_query.first()
    assert user_role is None

    response = delete_request_with_body(
        client,
        url=f"/v1/admin/users/{user.user_id}/roles/{Role.EMPLOYER.role_id}",
        headers=mock_admin_user_headers,
        json=mock_admin_update_data,
    )

    assert "Role was not assigned" in response.json().get("message")
    assert response.status_code == 200


def test_admin_audit_logs_by_admin_user_id(
    client, mock_admin_user_headers, test_db_session, mock_valid_admin_user
):
    AdminAuditLogFactory.create_batch(5)
    AdminAuditLogFactory.create_batch(5, admin_user=mock_valid_admin_user)
    assert test_db_session.query(AdminAuditLog).count() == 10

    response = client.get(
        f"/v1/admin/audit-logs?filter_id={mock_valid_admin_user.admin_user_id}",
        headers=mock_admin_user_headers,
    )

    assert response.status_code == 200
    data = response.json().get("data")
    assert len(data) == 5


def test_admin_audit_logs_by_record_id(
    client, mock_admin_user_headers, test_db_session, mock_valid_admin_user
):
    record_id = uuid.uuid4()
    AdminAuditLogFactory.create_batch(5)
    AdminAuditLogFactory.create_batch(5, record_id=record_id)
    assert test_db_session.query(AdminAuditLog).order_by(AdminAuditLog.created_at).count() == 10

    response = client.get(
        f"/v1/admin/audit-logs?filter_id={record_id}",
        headers=mock_admin_user_headers,
    )

    assert response.status_code == 200
    data = response.json().get("data")
    assert len(data) == 5
    pass


def test_admin_users_email_address_wildcard(client, app, test_db_session, mock_admin_user_headers):
    test_db_session.add(
        AzureGroupPermission(
            azure_group_id=AzureGroup.NON_PROD_PGMINTEG01.azure_group_id,
            azure_permission_id=AzurePermission.MAINTENANCE_READ.azure_permission_id,
        )
    )
    for x in range(3):
        UserFactory.create(email_address=f"jane_Do+{x}@example.com")
    for x in range(3):
        UserFactory.create(email_address=f"johnDo+{x}@example.com")
    with app.app.test_request_context("/v1/admin/users"):
        response = client.get(
            "/v1/admin/users?page_size=10&email_address=_do",
            headers=mock_admin_user_headers,
        )
        assert response.status_code == 200
        response_json = response.json()
        data = response_json.get("data")
        assert len(data) == 3


def test_omni_search_user_auth_logs(client, app, mock_admin_user_headers):
    user = UserFactory.create(
        email_address="<EMAIL>", first_name="test_first_name", last_name="test_last_name"
    )

    UserAuthLogFactory.create(
        user_id=user.user_id,
        meta_data={
            "outcomes": ["new_mmg_user", "authid_notfound", "email_notfound", "account_created"]
        },
        completed_at=datetime(2025, 3, 21),
    )
    UserAuthLogFactory.create(
        user_id=user.user_id,
        meta_data={
            "outcomes": ["new_mmg_user", "authid_notfound", "email_found", "account_linked"]
        },
        completed_at=datetime(2025, 3, 24),
    )
    UserAuthLogFactory.create(
        user_id=user.user_id,
        meta_data={"outcomes": ["account_found"]},
        completed_at=datetime(2025, 3, 24),
    )

    post_body = {"search": "authid_notfound"}
    with app.app.test_request_context("/v1/admin/users"):
        # Just showing that it is case insensitive.
        response = client.post(
            "/v1/admin/omnisearch/auth-logs",
            headers=mock_admin_user_headers,
            json=post_body,
        )
        response_json = response.json()
        data = response_json.get("data")
        assert response.status_code == 200
        assert len(data) == 2
        assert data[0]["completed_at"] > data[1]["completed_at"]


def test_get_user_applications(
    client, test_db_session, mock_admin_user_headers, mock_valid_admin_user
):
    user = UserFactory()
    claim_1 = ClaimFactory()
    application_1 = ApplicationFactory(user=user, claim=claim_1)
    claim_2 = ClaimFactory()
    application_2 = ApplicationFactory(user=user, claim=claim_2)

    response = client.get(
        f"/v1/admin/users/{user.user_id}/applications",
        headers=mock_admin_user_headers,
    )

    assert response.status_code == 200
    assert (
        response.json().get("message")
        == f"Successfully retrieved 2 applications for user {user.email_address}."
    )

    response_data = response.json().get("data")
    assert response_data is not None

    assert isinstance(response_data, list)
    assert len(response_data) == 2

    retreived_application_ids = [application["application_id"] for application in response_data]
    assert str(application_1.application_id) in retreived_application_ids
    assert str(application_2.application_id) in retreived_application_ids
    retreived_fineos_absence_ids = [
        application["fineos_absence_id"] for application in response_data
    ]
    assert application_1.fineos_absence_id in retreived_fineos_absence_ids
    assert application_2.fineos_absence_id in retreived_fineos_absence_ids


def test_get_user_applications_no_applications(
    client, test_db_session, mock_admin_user_headers, mock_valid_admin_user
):
    user = UserFactory.create()
    response = client.get(
        f"/v1/admin/users/{user.user_id}/applications",
        headers=mock_admin_user_headers,
    )
    assert response.status_code == 200
    assert response.json().get("message") == "No applications found for the user."

    response_data = response.json().get("data")

    assert isinstance(response_data, list)
    assert len(response_data) == 0


def test_reassign_applications(
    client, test_db_session, mock_admin_user_headers, mock_valid_admin_user, mock_admin_update_data
):

    application_1 = ApplicationFactory()
    user_1 = application_1.user

    application_2 = ApplicationFactory()
    user_2 = application_2.user

    # prepare request for moving application_1 from user_1 to user_2
    reassignement_request = {
        **mock_admin_update_data,
        "from_user_id": str(user_1.user_id),
        "to_user_id": str(user_2.user_id),
        "application_ids": [str(application_1.application_id)],
    }
    response = client.patch(
        "/v1/admin/applications/reassign",
        headers=mock_admin_user_headers,
        json=reassignement_request,
    )

    response_json = response.json().get("data")
    assert response.status_code == 200

    response_obj = ApplicationReassignmentPreviewResponse.parse_obj(response_json)

    # user_1 does not have an application anymore
    assert len(response_obj.from_user.applications) == 0

    # user_2 has two applications now
    assert len(response_obj.to_user.applications) == 2

    to_application_dict = {
        application.application_id: application for application in response_obj.to_user.applications
    }

    # both applications are assigned to user_2 now
    assert application_1.application_id in to_application_dict
    assert application_2.application_id in to_application_dict

    returned_application_1 = to_application_dict[application_1.application_id]
    returned_application_2 = to_application_dict[application_2.application_id]

    # ensure the returned object contains the expected applications and their details
    assert returned_application_1.fineos_absence_id == application_1.fineos_absence_id
    assert returned_application_2.fineos_absence_id == application_2.fineos_absence_id

    assert returned_application_1.created_at == application_1.created_at
    assert returned_application_2.created_at == application_2.created_at

    assert returned_application_1.imported_from_fineos_at == application_1.imported_from_fineos_at
    assert returned_application_2.imported_from_fineos_at == application_2.imported_from_fineos_at

    audit_log = (
        test_db_session.query(AdminAuditLog)
        .filter(AdminAuditLog.record_id == application_1.application_id)
        .one_or_none()
    )

    assert audit_log is not None

    assert audit_log.admin_user_id == mock_valid_admin_user.admin_user_id
    assert audit_log.record_type == "application"
    assert audit_log.ticket_num == mock_admin_update_data["ticket_num"]

    # check that before state contains the old user id
    state_before = json.loads(audit_log.state_before)
    assert state_before["user_id"] == str(user_1.user_id)

    # check that after state contains the new user id
    state_after = json.loads(audit_log.state_after)
    assert state_after["user_id"] == str(user_2.user_id)


def test_reassign_applications_invalid_application_id(
    client, mock_admin_user_headers, mock_valid_admin_user, mock_admin_update_data
):
    application_1 = ApplicationFactory()
    from_user = application_1.user

    application_2 = ApplicationFactory()
    to_user = application_2.user

    non_existing_applicaiton_id = uuid.uuid4()

    reassignment_request = {
        **mock_admin_update_data,
        "from_user_id": str(from_user.user_id),
        "to_user_id": str(to_user.user_id),
        "application_ids": [str(application_1.application_id), str(non_existing_applicaiton_id)],
    }

    response = client.patch(
        "/v1/admin/applications/reassign",
        headers=mock_admin_user_headers,
        json=reassignment_request,
    )

    assert response.status_code == 400

    errors = response.json().get("errors")

    expected_error = {
        "field": "application_ids",
        "message": f"Some application IDs: [UUID('{non_existing_applicaiton_id}')] are not present for source user: {from_user.user_id}",
        "type": "object_not_found",
    }

    assert expected_error in errors


def test_reassign_applications_nonexisting_users(
    client, mock_admin_user_headers, mock_valid_admin_user, mock_admin_update_data
):
    # This test ensures that the endpoint returns an error when the from_user or to_user does not exist

    non_existing_from_user_id = uuid.uuid4()
    non_existing_to_user_id = uuid.uuid4()

    non_existing_applicaiton_id = uuid.uuid4()

    reassignment_request = {
        **mock_admin_update_data,
        "from_user_id": str(non_existing_from_user_id),
        "to_user_id": str(non_existing_to_user_id),
        "application_ids": [str(non_existing_applicaiton_id)],
    }

    response = client.patch(
        "/v1/admin/applications/reassign",
        headers=mock_admin_user_headers,
        json=reassignment_request,
    )

    assert response.status_code == 400

    errors = response.json().get("errors")

    from_user_error = {
        "field": "from_user_id",
        "message": f"User with ID {non_existing_from_user_id} does not exist.",
        "type": "object_not_found",
    }

    assert from_user_error in errors

    to_user_error = {
        "field": "to_user_id",
        "message": f"User with ID {non_existing_to_user_id} does not exist.",
        "type": "object_not_found",
    }

    assert to_user_error in errors


def test_prevent_reassign_applications_to_employer_users(
    client, mock_admin_user_headers, mock_valid_admin_user, mock_admin_update_data
):

    application_1 = ApplicationFactory()
    source_user = application_1.user

    target_user = UserFactory.create(roles=[Role.EMPLOYER])

    application_2 = ApplicationFactory(user=target_user)
    target_user = application_2.user

    # prepare request for moving application_1 from user_1 to user_2
    reassignement_request = {
        **mock_admin_update_data,
        "from_user_id": str(source_user.user_id),
        "to_user_id": str(target_user.user_id),
        "application_ids": [str(application_1.application_id)],
    }
    response = client.patch(
        "/v1/admin/applications/reassign",
        headers=mock_admin_user_headers,
        json=reassignement_request,
    )

    assert response.status_code == 400


def test_logging_applications_has_no_employee_attached(
    client,
    test_db_session,
    mock_admin_user_headers,
    mock_valid_admin_user,
    caplog_warning,
    mock_admin_update_data,
):
    application_1 = ApplicationFactory()
    claim1 = ClaimFactory()
    claim1.employee = None
    application_1.claim = claim1
    user_1 = application_1.user

    application_2 = ApplicationFactory()
    user_2 = application_2.user

    # prepare request for moving application_1 from user_1 to user_2
    reassignement_request = {
        **mock_admin_update_data,
        "from_user_id": str(user_1.user_id),
        "to_user_id": str(user_2.user_id),
        "application_ids": [str(application_1.application_id)],
    }
    response = client.patch(
        "/v1/admin/applications/reassign",
        headers=mock_admin_user_headers,
        json=reassignement_request,
    )

    assert response.status_code == 200
    # if the application is attached to a claim without an employee, we log a warning
    assert_log_contains(
        caplog_warning,
        f"Application {application_1.application_id} is attached to a claim that has no employee attached.",
        {
            "claim_id": claim1.claim_id,
        },
    )


def test_application_reassignment_preview(
    client, test_db_session, mock_admin_user_headers, mock_valid_admin_user
):

    application_1 = ApplicationFactory()
    user_1 = application_1.user

    application_2 = ApplicationFactory()
    user_2 = application_2.user

    preview_request = ApplicationReassignmentPreviewRequest(
        from_user_email=user_1.email_address, to_user_email=user_2.email_address
    )

    # make request get preview of users' applications
    response = client.post(
        "/v1/admin/applications/reassign/preview",
        headers=mock_admin_user_headers,
        json=preview_request.dict(),
    )

    assert response.status_code == 200

    response_json = response.json().get("data")

    response_obj = ApplicationReassignmentPreviewResponse.parse_obj(response_json)

    # ensure preview response contains both users and their applications
    assert response_obj.from_user is not None
    assert response_obj.to_user is not None
    assert len(response_obj.from_user.applications) == 1
    assert len(response_obj.from_user.applications) == 1

    assert response_obj.from_user.user_id == user_1.user_id
    assert response_obj.to_user.user_id == user_2.user_id

    from_application = response_obj.from_user.applications[0]
    to_application = response_obj.to_user.applications[0]

    # ensure the returned object contains the correct applications and their details
    assert from_application.application_id == application_1.application_id
    assert to_application.application_id == application_2.application_id

    assert from_application.fineos_absence_id == application_1.fineos_absence_id
    assert to_application.fineos_absence_id == application_2.fineos_absence_id

    assert from_application.created_at == application_1.created_at
    assert to_application.created_at == application_2.created_at

    assert from_application.imported_from_fineos_at == application_1.imported_from_fineos_at
    assert to_application.imported_from_fineos_at == application_2.imported_from_fineos_at


def test_application_reassignment_preview_target_user_is_employer(
    client, test_db_session, mock_admin_user_headers
):

    application_1 = ApplicationFactory()
    user_1 = application_1.user

    user_2 = UserFactory.create(roles=[Role.EMPLOYER])

    preview_request = ApplicationReassignmentPreviewRequest(
        from_user_email=user_1.email_address, to_user_email=user_2.email_address
    )

    # make request get preview of users' applications
    response = client.post(
        "/v1/admin/applications/reassign/preview",
        headers=mock_admin_user_headers,
        json=preview_request.dict(),
    )

    assert response.status_code == 400

    errors = response.json().get("errors")

    expected_error = {
        "field": "to_user_email",
        "message": f"Target user {user_2.email_address} is not an employee user.",
        "type": "invalid",
    }

    assert expected_error in errors


def test_application_reassignment_preview_no_employee_attached_guardrail(
    client, test_db_session, mock_admin_user_headers
):

    application_1 = ApplicationFactory(
        tax_identifier=None, tax_identifier_id=None
    )  # create application without an employee
    user_1 = application_1.user

    employee_2 = EmployeeFactory()

    # create application with an employee
    application_2 = ApplicationFactory(tax_identifier=employee_2.tax_identifier)
    user_2 = application_2.user

    preview_request = ApplicationReassignmentPreviewRequest(
        from_user_email=user_1.email_address, to_user_email=user_2.email_address
    )

    # make request get preview of users' applications
    response = client.post(
        "/v1/admin/applications/reassign/preview",
        headers=mock_admin_user_headers,
        json=preview_request.dict(),
    )

    assert response.status_code == 200

    response_json = response.json().get("data")

    response_obj = ApplicationReassignmentPreviewResponse.parse_obj(response_json)

    from_application = response_obj.from_user.applications[0]
    to_application = response_obj.to_user.applications[0]

    assert from_application.employee_id is None
    assert to_application.employee_id is not None  # employee is attached to the second application


def test_application_reassignment_preview_unf_user_guardrail(
    client, test_db_session, mock_admin_user_headers
):

    application_1 = ApplicationFactory()
    ApplicationUserNotFoundInfoFactory(application_id=application_1.application_id)
    user_1 = application_1.user

    application_2 = ApplicationFactory()
    user_2 = application_2.user

    preview_request = ApplicationReassignmentPreviewRequest(
        from_user_email=user_1.email_address, to_user_email=user_2.email_address
    )

    # make request get preview of users' applications
    response = client.post(
        "/v1/admin/applications/reassign/preview",
        headers=mock_admin_user_headers,
        json=preview_request.dict(),
    )

    assert response.status_code == 200

    response_json = response.json().get("data")

    response_obj = ApplicationReassignmentPreviewResponse.parse_obj(response_json)

    assert response_obj.from_user.is_in_unf_flow
    assert not response_obj.to_user.is_in_unf_flow


def test_application_reassignment_preview_multiple_ssn_guardrail(
    client, test_db_session, mock_admin_user_headers
):

    user_1 = UserFactory()
    tax_identifier_1 = TaxIdentifierFactory()
    tax_identifier_2 = TaxIdentifierFactory()
    # create applications for user_1 with multiple tax identifiers including one without a tax identifier
    ApplicationFactory(user=user_1, tax_identifier=tax_identifier_1)
    ApplicationFactory(user=user_1, tax_identifier=tax_identifier_2)
    ApplicationFactory(user=user_1, tax_identifier=None, tax_identifier_id=None)

    user_2 = UserFactory()
    tax_identifier_1 = TaxIdentifierFactory()
    # create applications for user_2 with a single tax identifier and one without a tax identifier
    ApplicationFactory(user=user_2, tax_identifier=tax_identifier_1)
    ApplicationFactory(user=user_2, tax_identifier=tax_identifier_1)
    ApplicationFactory(user=user_2, tax_identifier=None, tax_identifier_id=None)

    preview_request = ApplicationReassignmentPreviewRequest(
        from_user_email=user_1.email_address, to_user_email=user_2.email_address
    )

    # make request get preview of users' applications
    response = client.post(
        "/v1/admin/applications/reassign/preview",
        headers=mock_admin_user_headers,
        json=preview_request.dict(),
    )

    assert response.status_code == 200

    response_json = response.json().get("data")

    response_obj = ApplicationReassignmentPreviewResponse.parse_obj(response_json)

    assert response_obj.from_user.has_multiple_ssns is True
    assert response_obj.to_user.has_multiple_ssns is False
