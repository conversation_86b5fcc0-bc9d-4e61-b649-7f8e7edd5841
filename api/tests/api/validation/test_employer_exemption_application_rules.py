from datetime import date

from freezegun import freeze_time

from massgov.pfml.api.validation.employer_exemption_application_rules import get_date_range_issues
from massgov.pfml.api.validation.exceptions import IssueType, ValidationErrorDetail
from massgov.pfml.db.models.factories import EmployerExemptionApplicationFactory

# Mocking the current date allows us to test
# both valid and invalid scenarios without changing the actual date.
MOCK_REQUEST_DATE = "2025-07-02"


@freeze_time(MOCK_REQUEST_DATE)
def test_effective_date_valid():
    """Test valid effective date for a mocked date returns no issues."""
    employer_exemption_application = EmployerExemptionApplicationFactory.build(
        insurance_plan_effective_at=date(2025, 10, 1)
    )
    issues = get_date_range_issues(employer_exemption_application)
    assert len(issues) == 0


@freeze_time(MOCK_REQUEST_DATE)
def test_effective_date_before_program_start():
    """Test effective date before program start returns validation error."""
    employer_exemption_application = EmployerExemptionApplicationFactory.build(
        insurance_plan_effective_at=date(2018, 10, 1)
    )
    issues = get_date_range_issues(employer_exemption_application)
    assert [
        ValidationErrorDetail(
            type=IssueType.minimum,
            message="insurance_plan_effective_at must be on or after 2019-10-01",
            field="insurance_plan_effective_at",
            extra={
                "max_plan_start_date": "October 01, 2025",
            },
        )
    ] == issues


@freeze_time(MOCK_REQUEST_DATE)
def test_effective_date_after_next_quarter():
    """Test effective date after next quarter returns validation error."""
    employer_exemption_application = EmployerExemptionApplicationFactory.build(
        insurance_plan_effective_at=date(2025, 10, 2)
    )
    issues = get_date_range_issues(employer_exemption_application)
    assert [
        ValidationErrorDetail(
            type=IssueType.maximum,
            message="insurance_plan_effective_at must be on or before 2025-10-01",
            field="insurance_plan_effective_at",
            extra={
                "max_plan_start_date": "October 01, 2025",
            },
        )
    ] == issues


@freeze_time(MOCK_REQUEST_DATE)
def test_effective_date_before_anniversary_date():
    """Test effective date before anniversary date returns validation error."""
    employer_exemption_application = EmployerExemptionApplicationFactory.build(
        insurance_plan_effective_at=date(2025, 10, 1), insurance_plan_expires_at=date(2024, 10, 1)
    )
    issues = get_date_range_issues(employer_exemption_application)
    assert [
        ValidationErrorDetail(
            type=IssueType.minimum,
            message="insurance_plan_expires_at must be on or after 2025-10-01",
            field="insurance_plan_expires_at",
        )
    ] == issues
