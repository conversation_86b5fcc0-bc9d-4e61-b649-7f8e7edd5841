import copy
import datetime
from typing import Any, List, Optional
from unittest import mock
from unittest.mock import MagicMock, patch

import factory.random
import pytest
from connexion.testing import TestContext
from dateutil.relativedelta import relativedelta
from freezegun import freeze_time
from sqlalchemy import inspect

import massgov.pfml.fineos
import massgov.pfml.fineos.mock.field
import massgov.pfml.fineos.mock_client
import massgov.pfml.util.datetime as datetime_util
import tests.api
from massgov.pfml.api.applications import applications_complete
from massgov.pfml.api.models.applications.common import DurationBasis, FrequencyIntervalBasis
from massgov.pfml.api.models.applications.responses import ApplicationStatus
from massgov.pfml.api.services.applications import get_application_split, get_crossed_benefit_years
from massgov.pfml.api.services.fineos_actions import LeaveNotificationReason
from massgov.pfml.api.util.paginate.paginator import DEFAULT_PAGE_SIZE
from massgov.pfml.api.validation.exceptions import IssueRule, IssueType
from massgov.pfml.db.lookup_data.absences import AbsencePeriodType, AbsenceStatus
from massgov.pfml.db.lookup_data.applications import (
    EmploymentStatus,
    Ethnicity,
    IndustrySector,
    LeaveReason,
    LeaveReasonQualifier,
    MmgIdvStatus,
    Race,
    RelationshipQualifier,
    RelationshipToCaregiver,
    WorkPatternType,
)
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.lookup_data.employees import Gender, LeaveRequestDecision, PaymentMethod
from massgov.pfml.db.lookup_data.geo import GeoState
from massgov.pfml.db.lookup_data.language import Language
from massgov.pfml.db.lookup_data.payments import PrepaidRegistrationStatus
from massgov.pfml.db.models.applications import (
    Application,
    ApplicationPaymentPreference,
    CaringLeaveMetadata,
    ContinuousLeavePeriod,
    NoClaimTypeForAbsenceType,
    WorkPattern,
    WorkPatternDay,
)
from massgov.pfml.db.models.employees import Address, TaxIdentifier
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    AddressFactory,
    ApplicationFactory,
    ApplicationMmgProfileFactory,
    ApplicationUserNotFoundInfoFactory,
    BenefitYearFactory,
    CaringLeaveMetadataFactory,
    ClaimFactory,
    ContinuousLeavePeriodFactory,
    DocumentFactory,
    DuaEmployeeDemographicsFactory,
    DuaReportingUnitFactory,
    EmployeeFactory,
    EmployeeOccupationFactory,
    EmployeeWithFineosNumberFactory,
    EmployerBenefitFactory,
    EmployerFactory,
    IntermittentLeavePeriodFactory,
    LeaveReasonFactory,
    OrganizationUnitFactory,
    OtherIncomeFactory,
    PaymentPreferenceFactory,
    PreviousLeaveFactory,
    ReducedScheduleLeavePeriodFactory,
    SubmittableApplicationFactory,
    TaxIdentifierFactory,
    UserFactory,
    WagesAndContributionsFactory,
    WorkPatternFixedFactory,
)
from massgov.pfml.db.models.fineos_web_id import FINEOSWebIdExt
from massgov.pfml.db.models.payments import ClaimantPrepaidRegistration
from massgov.pfml.db.models.phone import Phone
from massgov.pfml.fineos.client import AbstractFINEOSClient
from massgov.pfml.fineos.exception import (
    FINEOSClientError,
    FINEOSEntityNotFound,
    FINEOSFatalResponseError,
    FINEOSFatalUnavailable,
)
from massgov.pfml.fineos.factory import FINEOSClientConfig
from massgov.pfml.fineos.models.customer_api import NotificationCaseSummary
from massgov.pfml.my_mass_gov.client.mock import build_response
from massgov.pfml.my_mass_gov.client.models import Gender as MmgGender
from massgov.pfml.my_mass_gov.client.models import ProfileResponseDto, RaceEthnicityEnum
from massgov.pfml.util.strings import format_fein
from tests.api import apply_custom_encoder
from tests.helpers.logging import assert_log_contains

# methods called when an application is submitted to FINEOS
SUBMIT_APPLICATION_TO_FINEOS_METHODS = [
    "add_week_based_work_pattern",
    "update_occupation",
    "start_absence",
    "complete_intake",
]


def sqlalchemy_object_as_dict(obj):
    return {c.key: getattr(obj, c.key) for c in inspect(obj).mapper.column_attrs}


def _get_captured_calls_for_method(capture: List[Any], method_name: str) -> List[Any]:
    """Given a list of captured calls to FINEOS and a method name, filter to the calls matching the method."""
    return [call for call in capture if call[0] == method_name]


def setup_env(self, feature_config):
    feature_config.universal_profile.enable_mmg_idv = False
    feature_config.universal_profile.enable_backend_invalidation = False


def test_applications_get_invalid_uuid(client, user, auth_token):
    response = client.get(
        "/v1/applications/undefined", headers={"Authorization": f"Bearer {auth_token}"}
    )

    response_body = response.json()
    assert response.status_code == 400
    assert (
        response_body.get("message")
        == "'undefined' is not a 'uuid'\n\nFailed validating 'format' in schema:\n    {'type': 'string', 'format': 'uuid'}\n\nOn instance:\n    'undefined'"
    )


# The UUID used in this test was generated online. Hopefully it will never match any of
# the IDs generated by our seed data generator. If it does the test will fail.
def test_applications_get_invalid(client, user, auth_token):
    response = client.get(
        "/v1/applications/{}".format("b26aa854-dd50-4aed-906b-c72b062f0275"),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    tests.api.validate_error_response(response, 404)


@freeze_time("2020-01-01")
def test_applications_get_incomplete(client, user, auth_token):
    application = ApplicationFactory.create(
        user=user,
        submitted_time=None,
        updated_at=datetime_util.utcnow(),
        # Cause at least one validation error to be present
        first_name=None,
    )

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    assert len(response.json().get("warnings")) > 0

    response_body = response.json().get("data")

    assert response_body.get("employer_fein") is not None
    assert response_body.get("application_id") == str(application.application_id)
    assert response_body.get("updated_at") == "2020-01-01T00:00:00+00:00"
    assert response_body.get("status") == ApplicationStatus.Started.value


def test_applications_get_incomplete_submitted(client, user, auth_token):
    application = ApplicationFactory.create(
        user=user,
        updated_at=datetime_util.utcnow(),
        # Put the application in a state where it is submitted
        submitted_time=datetime_util.utcnow(),
        # Simulate a validation error for pre-submission application
        first_name=None,
    )

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    # No warning about the first_name missing, because the application is submitted
    assert len(response.json().get("warnings")) == 0


def test_applications_unauthorized_get(client, user, auth_token):
    # create application not associated with user
    application = ApplicationFactory.create()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    tests.api.validate_error_response(response, 403)


def test_applications_unauthorized_get_with_user(client, user, auth_token):
    other_user = UserFactory.create()
    # create application not associated with user
    application = ApplicationFactory.create(user=other_user)

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    tests.api.validate_error_response(response, 403)


def test_applications_get_fineos_forbidden(client, fineos_user, fineos_user_token):
    # Fineos role cannot access this endpoint
    application = ApplicationFactory.create(user=fineos_user, updated_at=datetime.datetime.now())
    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {fineos_user_token}"},
    )
    tests.api.validate_error_response(response, 403)


def test_applications_get_partially_displays_fin_acct_num(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)
    application.payment_preference = ApplicationPaymentPreference(
        account_number="*********", routing_number="*********"
    )

    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")

    payment_preference = response_body.get("payment_preference")

    assert payment_preference["account_number"] == "*****6789"
    assert payment_preference["routing_number"] == "*********"


def test_applications_get_with_payment_preference(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    application.payment_preference = ApplicationPaymentPreference(
        payment_method_id=PaymentMethod.ACH.payment_method_id
    )

    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    assert response_body.get("application_id") == str(application.application_id)

    payment_preference = response_body.get("payment_preference")
    assert payment_preference

    assert payment_preference["payment_method"] == PaymentMethod.ACH.payment_method_description


def test_applications_get_split_from_application_id(client, user, auth_token, test_db_session):
    # split_from_application_id is an optional field and defaults to None
    application = ApplicationFactory.create(user=user)
    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200

    response_body = response.json().get("data")
    assert response_body.get("split_from_application_id") is None

    # when split_from_application_id is provided, it should also be returned
    split_application = ApplicationFactory.create(
        user=user, split_from_application_id=application.application_id
    )
    test_db_session.commit()
    test_db_session.refresh(application)

    response = client.get(
        "/v1/applications/{}".format(split_application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_body = response.json().get("data")
    assert response_body.get("split_from_application_id") == str(application.application_id)

    # the original request should now provide a split_into_application_id
    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_body = response.json().get("data")
    assert response_body.get("split_into_application_id") == str(split_application.application_id)


def test_applications_get_employee_id(client, user, auth_token, test_db_session):
    # No employee_id should be returned if there isn't an employee associated with the application
    application = ApplicationFactory.create(user=user)
    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200

    response_body = response.json().get("data")
    assert response_body.get("employee_id") is None

    # employee_id will be returned when the application is associated with an employee
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(user=user, tax_identifier=employee.tax_identifier)
    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200

    response_body = response.json().get("data")
    assert response_body.get("employee_id") == str(employee.employee_id)


@freeze_time("2021-10-25")
def test_applications_get_computed_application_split(
    client, user, auth_token, test_db_session, initialize_factories_session
):
    employee = EmployeeFactory.create()

    # note: BY end date will be 01/01/2022
    BenefitYearFactory.create(employee=employee)

    application = ApplicationFactory.create(tax_identifier=employee.tax_identifier, user=user)

    leave_period = IntermittentLeavePeriodFactory.create(
        start_date=datetime.date(2021, 12, 15),
        end_date=datetime.date(2022, 3, 10),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)
    test_db_session.commit()
    test_db_session.refresh(application)

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_body = response.json().get("data")
    application_split = response_body.get("computed_application_split")

    assert application_split is not None
    assert application_split["crossed_benefit_year"]["benefit_year_start_date"] == "2021-01-03"
    assert application_split["crossed_benefit_year"]["benefit_year_end_date"] == "2022-01-01"
    assert application_split["application_dates_in_benefit_year"]["start_date"] == "2021-12-15"
    assert application_split["application_dates_in_benefit_year"]["end_date"] == "2022-01-01"
    assert application_split["application_dates_outside_benefit_year"]["start_date"] == "2022-01-02"
    assert application_split["application_dates_outside_benefit_year"]["end_date"] == "2022-03-10"
    assert application_split["application_outside_benefit_year_submittable_on"] == "2021-11-03"


@freeze_time("2021-10-25")
def test_applications_get_computed_application_split_no_split(
    client, user, auth_token, test_db_session, initialize_factories_session
):
    employee = EmployeeFactory.create()

    application = ApplicationFactory.create(tax_identifier=employee.tax_identifier, user=user)

    leave_period = IntermittentLeavePeriodFactory.create(
        start_date=datetime.date(2021, 12, 15),
        end_date=datetime.date(2022, 3, 10),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)
    test_db_session.commit()
    test_db_session.refresh(application)

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_body = response.json().get("data")
    application_split = response_body.get("computed_application_split")

    assert application_split is None


def test_applications_get_organization_units(
    client, user, auth_token, test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()
    org_unit = OrganizationUnitFactory(employer_id=employer.employer_id)

    EmployeeOccupationFactory.create(
        employee=employee, employer=employer, organization_unit=org_unit
    )

    reporting_unit_with_org_unit = DuaReportingUnitFactory.create(
        organization_unit=org_unit, employer_id=employer.employer_id
    )

    DuaEmployeeDemographicsFactory.create(
        fineos_customer_number=employee.fineos_customer_number,
        employer_fein=employer.employer_fein,
        employer_reporting_unit_number=reporting_unit_with_org_unit.dua_id,
    )

    application = ApplicationFactory.create(
        tax_identifier=employee.tax_identifier, user=user, employer_fein=employer.employer_fein
    )

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    response_body = response.json().get("data")
    assert len(response_body["employee_organization_units"]) > 0
    assert len(response_body["employer_organization_units"]) > 0


TEST_FEIN = "*********"
DIFFERENT_FEIN = "*********"


@pytest.mark.parametrize(
    "employer_fein, application_fein, employer_dba",
    [
        pytest.param(
            TEST_FEIN, TEST_FEIN, "Test DBA", id="Application with FEIN and Employer with DBA"
        ),
        pytest.param(
            TEST_FEIN, TEST_FEIN, "", id="Application with FEIN and Employer with empty string DBA"
        ),
        pytest.param(
            TEST_FEIN, TEST_FEIN, None, id="Application with FEIN and Employer without DBA"
        ),
        pytest.param(
            TEST_FEIN, DIFFERENT_FEIN, "Test DBA", id="Application with FEIN and no Employer match"
        ),
        pytest.param(
            TEST_FEIN, None, "Test DBA", id="Application without FEIN and Employer with DBA"
        ),
        pytest.param(TEST_FEIN, None, None, id="Application without FEIN and Employer without DBA"),
    ],
)
def test_applications_get_employer_dba(
    client,
    user,
    auth_token,
    test_db_session,
    initialize_factories_session,
    employer_fein,
    application_fein,
    employer_dba,
):
    EmployerFactory.create(employer_fein=employer_fein, employer_dba=employer_dba)

    application = ApplicationFactory.create(user=user, employer_fein=application_fein)

    response = client.get(
        f"/v1/applications/{application.application_id}",
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    response_body = response.json().get("data")
    if application_fein == employer_fein:
        assert response_body.get("employer_dba") == employer_dba
    else:
        assert response_body.get("employer_dba") is None


def test_applications_get_all_for_user(client, user, auth_token):
    applications = sorted(
        [ApplicationFactory.create(user=user), ApplicationFactory.create(user=user)],
        key=lambda app: app.created_at,
        reverse=True,
    )
    unassociated_application = ApplicationFactory.create()

    response = client.get("/v1/applications", headers={"Authorization": f"Bearer {auth_token}"})
    assert response.status_code == 200

    response_data = response.json().get("data")
    assert len(response_data) == len(applications)
    for application, app_response in zip(applications, response_data):
        assert str(application.application_id) == app_response["application_id"]
        assert application.application_id != unassociated_application.application_id


def test_applications_get_all_pagination_default_limit(client, user, auth_token):
    applications = [ApplicationFactory.create(user=user) for _ in range(DEFAULT_PAGE_SIZE * 4)]
    applications = sorted(applications, key=lambda app: app.created_at, reverse=True)

    response = client.get("/v1/applications", headers={"Authorization": f"Bearer {auth_token}"})
    assert response.status_code == 200
    response_data = response.json().get("data")
    assert len(response_data) == DEFAULT_PAGE_SIZE
    for application, app_response in zip(applications, response_data):
        assert str(application.application_id) == app_response["application_id"]


def test_applications_get_all_pagination_asc(client, user, auth_token):
    applications = [ApplicationFactory.create(user=user) for _ in range(100)]
    applications = sorted(applications, key=lambda app: app.created_at, reverse=False)

    response = client.get(
        "/v1/applications?order_direction=ascending",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_data = response.json().get("data")
    assert len(response_data) == DEFAULT_PAGE_SIZE
    for application, app_response in zip(applications, response_data):
        assert str(application.application_id) == app_response["application_id"]


def test_applications_get_all_pagination_limit_double(client, user, auth_token):
    applications = [ApplicationFactory.create(user=user) for _ in range(DEFAULT_PAGE_SIZE * 4)]
    applications = sorted(applications, key=lambda app: app.created_at, reverse=True)

    response = client.get(
        f"/v1/applications?page_size={DEFAULT_PAGE_SIZE * 2}",
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    response_data = response.json().get("data")
    assert len(response_data) == DEFAULT_PAGE_SIZE * 2
    for application, app_response in zip(applications, response_data):
        assert str(application.application_id) == app_response["application_id"]


def test_applications_post_start_app(client, user, auth_token, test_db_session, caplog_info):
    token_info = mock.MagicMock(name="token_info")
    with TestContext(context=token_info):
        response = client.post(
            "/v1/applications", headers={"Authorization": f"Bearer {auth_token}"}
        )

        response_body = response.json().get("data")
        application_id = response_body.get("application_id")

        assert response.status_code == 201
        assert application_id

        application = test_db_session.get(Application, application_id)

        assert application.created_at
        assert application.updated_at == application.created_at
        assert application.user.user_id == user.user_id

        assert_log_contains(
            caplog_info,
            "applications_start success",
            {"application.application_id": application_id},
        )


def test_applications_post_start_app_invalid_benefit_years(
    client, user, auth_token, test_db_session
):
    employee = EmployeeFactory.create(
        tax_identifier=TaxIdentifierFactory.create(tax_identifier="*********"),
        invalid_benefit_years_since=datetime.date(2022, 1, 1),
    )
    ApplicationFactory.create(user=user, tax_identifier=employee.tax_identifier)

    response = client.post(
        "/v1/applications",
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-Disable-Overlapping-Benefit-Year-Claim-Creation": "true",
        },
    )

    assert response.status_code == 400
    assert (
        response.json()["errors"][0]["message"]
        == "The employee with matching tax identifier has an invalid benefit year."
    )


def test_applications_post_start_app_success_with_ff_on(client, user, auth_token, test_db_session):
    employee = EmployeeFactory.create(
        tax_identifier=TaxIdentifierFactory.create(tax_identifier="*********"),
    )
    ApplicationFactory.create(user=user, tax_identifier=employee.tax_identifier)

    response = client.post(
        "/v1/applications",
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-Disable-Overlapping-Benefit-Year-Claim-Creation": "true",
        },
    )

    assert response.status_code == 201


def test_applications_post_start_app_invalid_multiple_employee_ids(
    client, user, auth_token, test_db_session
):
    employee1 = EmployeeFactory.create(
        tax_identifier=TaxIdentifierFactory.create(tax_identifier="*********"),
        invalid_benefit_years_since=datetime.date(2022, 1, 1),
    )
    employee2 = EmployeeFactory.create(
        tax_identifier=TaxIdentifierFactory.create(tax_identifier="*********"),
    )
    ApplicationFactory.create(user=user, tax_identifier=employee1.tax_identifier)
    ApplicationFactory.create(user=user, tax_identifier=employee2.tax_identifier)

    response = client.post(
        "/v1/applications",
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-Disable-Overlapping-Benefit-Year-Claim-Creation": "true",
        },
    )

    assert response.status_code == 201


def test_applications_post_start_app_unauthenticated(client):
    response = client.post("/v1/applications", headers={"Authorization": f"Bearer {''}"})

    tests.api.validate_error_response(response, 401)


def test_applications_post_fineos_forbidden(client, fineos_user_token):
    # Fineos role cannot access this endpoint
    response = client.post(
        "/v1/applications", headers={"Authorization": f"Bearer {fineos_user_token}"}
    )
    assert response.status_code == 403


@freeze_time("2020-01-01")
def test_application_patch(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    update_request_body = sqlalchemy_object_as_dict(application)
    # Change last name
    update_request_body["last_name"] = "Perez"
    update_request_body["leave_details"] = {
        "employer_notification_method": "In Writing",
        "caring_leave_metadata": {
            "family_member_first_name": "Jane",
            "family_member_middle_name": "Alice",
            "family_member_last_name": "Doe",
            "family_member_date_of_birth": "1975-01-01",
            "relationship_to_caregiver": RelationshipToCaregiver.PARENT.relationship_to_caregiver_description,
        },
    }
    update_request_body["middle_name"] = "Mike"
    update_request_body["tax_identifier"] = "***********"
    update_request_body["employer_fein"] = "22-7777777"
    update_request_body["occupation"] = "Engineer"

    # Remove foreign keys as DB does not have all tables populated
    update_request_body.pop("employer_id", None)
    update_request_body.pop("employee_id", None)

    update_request_body["mailing_address"] = {
        "city": "Springfield",
        "state": "IL",
        "line_1": "123 Foo St.",
        "zip": "12345-1234",
    }

    update_request_body["phone"] = {
        "int_code": "1",
        "phone_number": "************",
        "phone_type": "Cell",
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(update_request_body),
    )

    response_body = response.json()

    assert response.status_code == 200

    test_db_session.refresh(application)

    # Formatted fields are saved as unformatted (i.e. no dashes)
    assert application.tax_identifier
    assert application.tax_identifier.tax_identifier == "*********"
    assert application.employer_fein == "*********"

    # CaringLeaveMetadata fields added
    assert application.caring_leave_metadata.family_member_first_name == "Jane"
    assert application.caring_leave_metadata.family_member_middle_name == "Alice"
    assert application.caring_leave_metadata.family_member_last_name == "Doe"
    assert application.caring_leave_metadata.family_member_date_of_birth.isoformat() == "1975-01-01"
    assert (
        application.caring_leave_metadata.relationship_to_caregiver_id
        == RelationshipToCaregiver.PARENT.relationship_to_caregiver_id
    )

    # Phone number is saved as E.164
    assert application.phone.phone_number == "+***********"

    assert response_body.get("data").get("last_name") == "Perez"
    assert response_body.get("data").get("updated_at") == "2020-01-01T00:00:00+00:00"
    assert response_body.get("data").get("middle_name") == "Mike"
    assert response_body.get("data").get("occupation") == "Engineer"
    assert response_body.get("data").get("mailing_address")["city"] == "Springfield"
    assert response_body.get("data").get("phone")["phone_type"] == "Cell"

    assert (
        response_body.get("data").get("leave_details").get("employer_notification_method")
        == "In Writing"
    )

    assert (
        response_body.get("data")
        .get("leave_details")
        .get("caring_leave_metadata")["family_member_first_name"]
        == "Jane"
    )
    assert (
        response_body.get("data")
        .get("leave_details")
        .get("caring_leave_metadata")["family_member_middle_name"]
        == "Alice"
    )
    assert (
        response_body.get("data")
        .get("leave_details")
        .get("caring_leave_metadata")["family_member_last_name"]
        == "Doe"
    )
    assert (
        response_body.get("data")
        .get("leave_details")
        .get("caring_leave_metadata")["relationship_to_caregiver"]
        == RelationshipToCaregiver.PARENT.relationship_to_caregiver_description
    )

    # Formatted / masked fields:
    assert response_body.get("data").get("employer_fein") == "22-7777777"
    assert response_body.get("data").get("tax_identifier") == "***-**-6789"
    assert response_body.get("data").get("phone")["phone_number"] == "***-***-9945"
    assert (
        response_body.get("data")
        .get("leave_details")
        .get("caring_leave_metadata")["family_member_date_of_birth"]
        == "****-01-01"
    )


def test_application_patch_masking(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    update_request_body = {
        "tax_identifier": "***********",
        "has_state_id": True,
        "mass_id": "*********",
        "date_of_birth": "1970-01-01",
        "mailing_address": {
            "city": "Chicago",
            "state": "IL",
            "line_1": "123 Foo St.",
            "line_2": "Apt #123",
            "zip": "12345-1234",
        },
        "leave_details": {
            "child_birth_date": "2021-09-21",
            "child_placement_date": "2021-05-13",
            "caring_leave_metadata": {"family_member_date_of_birth": "2021-01-01"},
        },
        "phone": {"int_code": "1", "phone_number": "************", "phone_type": "Cell"},
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    response_body = response.json()

    assert response.status_code == 200

    # Verify values in the DB are updated and not masked
    test_db_session.refresh(application)
    assert application.tax_identifier.tax_identifier == "*********"
    assert application.mass_id == "*********"
    assert application.date_of_birth.isoformat() == "1970-01-01"
    assert application.mailing_address.address_line_one == "123 Foo St."
    assert application.mailing_address.address_line_two == "Apt #123"
    assert application.mailing_address.zip_code == "12345-1234"
    assert application.child_placement_date.isoformat() == "2021-05-13"
    assert application.child_birth_date.isoformat() == "2021-09-21"
    assert application.phone.phone_number == "+***********"
    assert application.caring_leave_metadata.family_member_date_of_birth.isoformat() == "2021-01-01"

    # Verify values returned by the API are properly masked
    assert response_body.get("data").get("tax_identifier") == "***-**-6789"
    assert response_body.get("data").get("mass_id") == "*********"
    assert response_body.get("data").get("date_of_birth") == "****-01-01"

    assert (
        response_body.get("data").get("leave_details").get("child_placement_date") == "****-05-13"
    )
    assert response_body.get("data").get("leave_details").get("child_birth_date") == "****-09-21"
    assert (
        response_body.get("data")
        .get("leave_details")
        .get("caring_leave_metadata")["family_member_date_of_birth"]
        == "****-01-01"
    )
    assert response_body.get("data").get("phone")["phone_number"] == "***-***-9945"


def test_application_patch_masked_inputs_ignored(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    application.tax_identifier = TaxIdentifier(tax_identifier="*********")
    application.has_state_id = True
    application.mass_id = "*********"
    application.date_of_birth = datetime.date(1970, 1, 1)
    application.child_birth_date = datetime.date(2021, 9, 21)
    application.child_placement_date = datetime.date(2021, 5, 13)
    application.payment_preference = ApplicationPaymentPreference(
        routing_number="*********", account_number="*********"
    )
    application.mailing_address = Address(
        address_line_one="123 Foo St.",
        address_line_two="Apt #123",
        city="Chicago",
        geo_state_id=17,  # Illinois
        zip_code="12345-6789",
    )
    application.residential_address = Address(
        address_line_one="123 Foo St.",
        address_line_two="Apt #123",
        city="Chicago",
        geo_state_id=17,  # Illinois
        zip_code="12345-6789",
    )
    application.phone = Phone(phone_number="+***********", phone_type_id=1)  # Cell

    caring_leave_metadata = CaringLeaveMetadataFactory.create(
        family_member_date_of_birth=datetime.date(1975, 1, 1)
    )

    application.caring_leave_metadata = caring_leave_metadata

    test_db_session.add(caring_leave_metadata)

    test_db_session.commit()

    update_request_body = {
        "tax_identifier": "***-**-6789",
        "mass_id": "*********",
        "date_of_birth": "****-01-01",
        "leave_details": {
            "child_birth_date": "****-09-21",
            "child_placement_date": "****-05-13",
            "caring_leave_metadata": {"family_member_date_of_birth": "****-01-01"},
        },
        "mailing_address": {
            "city": "Chicago",
            "state": "IL",
            "line_1": "*******",
            "line_2": "*******",
            "zip": "12345-****",
        },
        "residential_address": {
            "city": "Chicago",
            "state": "IL",
            "line_1": "*******",
            "line_2": "*******",
            "zip": "12345-****",
        },
        "phone": {"int_code": "1", "phone_number": "***-***-9945", "phone_type": "Cell"},
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    assert response.status_code == 200

    # Nothing in the DB value was actually updated to the masked value
    test_db_session.refresh(application)
    assert application.tax_identifier.tax_identifier == "*********"
    assert application.mass_id == "*********"
    assert application.date_of_birth.isoformat() == "1970-01-01"
    assert application.mailing_address.address_line_one == "123 Foo St."
    assert application.mailing_address.address_line_two == "Apt #123"
    assert application.mailing_address.zip_code == "12345-6789"
    assert application.residential_address.address_line_one == "123 Foo St."
    assert application.residential_address.address_line_two == "Apt #123"
    assert application.residential_address.zip_code == "12345-6789"
    assert application.child_placement_date.isoformat() == "2021-05-13"
    assert application.child_birth_date.isoformat() == "2021-09-21"
    assert application.phone.phone_number == "+***********"
    assert application.caring_leave_metadata.family_member_date_of_birth.isoformat() == "1975-01-01"


def test_application_patch_masked_mismatch_fields(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    application.tax_identifier = TaxIdentifier(tax_identifier="*********")
    application.has_state_id = True
    application.mass_id = None
    application.date_of_birth = datetime.date(1970, 1, 1)
    application.child_birth_date = datetime.date(2021, 9, 21)
    application.child_placement_date = datetime.date(2021, 5, 13)

    application.mailing_address = Address(
        address_line_one=None,
        address_line_two=None,
        city="Chicago",
        geo_state_id=17,  # Illinois
        zip_code="12345-6789",
    )
    application.residential_address = Address(
        address_line_one=None,
        address_line_two=None,
        city="Chicago",
        geo_state_id=17,  # Illinois
        zip_code="12345-6789",
    )
    application.phone = Phone(phone_number="+2404879945", phone_type_id=1)  # Cell

    test_db_session.commit()

    update_request_body = {
        "tax_identifier": "***-**-0000",
        "mass_id": "*********",
        "date_of_birth": "****-12-31",
        "leave_details": {"child_birth_date": "****-12-31", "child_placement_date": "****-12-31"},
        "mailing_address": {
            "city": "Chicago",
            "state": "IL",
            "line_1": "*******",
            "line_2": "*******",
            "zip": "55555-****",
        },
        "residential_address": {
            "city": "Chicago",
            "state": "IL",
            "line_1": "*******",
            "line_2": "*******",
            "zip": "55555-****",
        },
        "phone": {"int_code": "1", "phone_number": "***-***-1234", "phone_type": "Cell"},
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    assert response.status_code == 400
    tests.api.validate_error_response(response, 400, message="Error validating masked fields")
    # Want to make sure every bad value errored above, but the error object is unwieldy, let's just look at the fields.
    fields = set(err["field"] for err in response.json()["errors"])
    partially_masked_errors = set(
        [
            "tax_identifier",
            "date_of_birth",
            "leave_details.child_birth_date",
            "leave_details.child_placement_date",
            "mailing_address.zip",
            "residential_address.zip",
            "phone.phone_number",
        ]
    )
    fully_masked_errors = set(
        [
            "mass_id",
            "mailing_address.line_1",
            "mailing_address.line_2",
            "residential_address.line_1",
            "residential_address.line_2",
        ]
    )
    assert fields == partially_masked_errors.union(fully_masked_errors)
    for err in response.json()["errors"]:
        assert err["type"] == IssueType.invalid_masked_field

        if err["field"] in partially_masked_errors:
            assert err["rule"] == IssueRule.disallow_mismatched_masked_field
        else:
            assert err["rule"] == IssueRule.disallow_fully_masked_no_existing


def test_application_patch_has_mailing_address(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user, has_mailing_address=None)
    assert application.has_mailing_address is None
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "has_mailing_address": True,
            "mailing_address": {
                "city": "Chicago",
                "state": "IL",
                "line_1": "123 Foo St.",
                "zip": "12345-1234",
            },
        },
    )
    assert response.status_code == 200
    response_body = response.json()
    assert response_body.get("data").get("has_mailing_address") is True
    # Activate and fix after enableAddressValidation is enabled
    # assert response_body.get("data").get("mailing_address")["line_1"] == "*******"

    test_db_session.refresh(application)
    assert application.has_mailing_address is True
    assert application.mailing_address.address_line_one == "123 Foo St."


def test_application_patch_fields_to_use_from_user_profile(
    client, auth_token, test_db_session, user
):
    application = ApplicationFactory.create(user=user)
    assert application.fields_to_use_from_user_profile is None

    update_fields_to_use_from_user_profile = {
        "fields_to_use_from_user_profile": ["raceEthnicity", "gender"]
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_fields_to_use_from_user_profile,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    fields_to_use_from_user_profile = response_body.get("data").get(
        "fields_to_use_from_user_profile"
    )
    assert "raceEthnicity" in fields_to_use_from_user_profile
    assert "gender" in fields_to_use_from_user_profile


def test_application_patch_mailing_address(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    assert application.mailing_address is None

    # adding residential address
    update_request_body = {
        "mailing_address": {
            "city": "Chicago",
            "state": "IL",
            "line_1": "123 Foo St.",
            "zip": "12345-1234",
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("mailing_address")["city"] == "Chicago"
    # Activate and fix after enableAddressValidation is enabled
    # assert response_body.get("data").get("mailing_address")["line_1"] == "*******"
    assert application.mailing_address.city == "Chicago"
    assert application.mailing_address.address_line_one == "123 Foo St."

    # updating mailing address
    update_request_body = {
        "mailing_address": {
            "city": "Chicago",
            "state": "IL",
            "line_1": "123 Bar St.",
            "line_2": None,
            "zip": "12345-1234",
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("mailing_address")["city"] == "Chicago"
    # Activate and fix after enableAddressValidation is enabled
    # assert response_body.get("data").get("mailing_address")["line_1"] == "*******"
    assert application.mailing_address.city == "Chicago"
    assert application.mailing_address.address_line_one == "123 Bar St."

    update_request_body_dob = {"date_of_birth": "1970-01-01"}

    # patching a partial update of the mailing address (zip code only)
    update_request_body = {"mailing_address": {"zip": "12345"}}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("mailing_address")["city"] == "Chicago"
    # Activate and fix after enableAddressValidation is enabled
    # assert response_body.get("data").get("mailing_address")["line_1"] == "*******"
    assert response_body.get("data").get("mailing_address")["zip"] == "12345"
    assert application.mailing_address.city == "Chicago"
    assert application.mailing_address.address_line_one == "123 Bar St."
    assert application.mailing_address.zip_code == "12345"

    # patching another field and confirming mailing address still persists
    response_new_update = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body_dob,
    )

    test_db_session.refresh(application)
    response_body_new_update = response_new_update.json()
    assert response_body_new_update.get("data").get("mailing_address")["city"] == "Chicago"
    # Activate and fix after enableAddressValidation is enabled
    # assert response_body_new_update.get("data").get("mailing_address")["line_1"] == "*******"

    # removing mailing address
    update_request_body = {"mailing_address": None}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("mailing_address") is None
    assert application.residential_address is None


def test_application_patch_residential_address(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    assert application.residential_address is None

    # adding residential address
    update_request_body = {
        "residential_address": {
            "city": "Chicago",
            "state": "IL",
            "line_1": "123 Foo St.",
            "zip": "12345-1234",
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("residential_address")["city"] == "Chicago"
    # Activate and fix after enableAddressValidation is enabled
    # assert response_body.get("data").get("residential_address")["line_1"] == "*******"
    assert application.residential_address.city == "Chicago"
    assert application.residential_address.address_line_one == "123 Foo St."

    # updating residential address
    update_request_body = {
        "residential_address": {
            "city": "Chicago",
            "state": "IL",
            "line_1": "123 Bar St.",
            "zip": "12345-1234",
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("residential_address")["city"] == "Chicago"
    # Activate and fix after enableAddressValidation is enabled
    # assert response_body.get("data").get("residential_address")["line_1"] == "*******"
    assert application.residential_address.city == "Chicago"
    assert application.residential_address.address_line_one == "123 Bar St."

    # patching a partial update of the residential address (zip code only)
    update_request_body = {"residential_address": {"zip": "12345"}}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("residential_address")["city"] == "Chicago"
    # Activate and fix after enableAddressValidation is enabled
    # assert response_body.get("data").get("residential_address")["line_1"] == "*******"
    assert response_body.get("data").get("residential_address")["zip"] == "12345"
    assert application.residential_address.city == "Chicago"
    assert application.residential_address.address_line_one == "123 Bar St."
    assert application.residential_address.zip_code == "12345"

    # removing residential address
    update_request_body = {"residential_address": None}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("residential_address") is None
    assert application.residential_address is None


def test_application_patch_residential_address_null_values(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)
    assert application.residential_address is None

    # Missing all values
    update_request_body = {
        "residential_address": {"city": None, "state": None, "line_1": None, "zip": None}
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("residential_address") == {}

    # Missing state value
    update_request_body = {
        "residential_address": {
            "city": "Chicago",
            "state": None,
            "line_1": "123 Bar St.",
            "zip": "12345-1234",
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data").get("residential_address").get("state") is None


def test_application_patch_residential_address_line_2_masked(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)
    application.residential_address = Address(
        address_line_one="Mt. Greylock",
        address_line_two="Taconic Mountains",
        city="Adams",
        geo_state_id=GeoState.MA.geo_state_id,
        zip_code="01220",
    )
    test_db_session.commit()

    assert application.residential_address
    assert application.residential_address.address_line_two

    # 1. Update the address by removing the second line. Perhaps the applicant realized they were being too specific.
    first_update_request_body = {
        "residential_address": {
            "line_1": "Mt. Greylock",
            "line_2": "",
            "city": "Adams",
            "state": "MA",
            "zip": "01220",
        }
    }

    first_update_response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=first_update_request_body,
    )
    assert first_update_response.status_code == 200

    # 2. Get the latest fields for the application before we update the address again.
    get_response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    get_response_body = get_response.json()
    addr = get_response_body["data"]["residential_address"]

    assert get_response.status_code == 200
    assert not addr["line_2"]

    # 3. Update the first address line. Use the second address line field from the response to emulate not changing it.
    second_update_request_body = {
        "residential_address": {
            "line_1": "Mount Greylock",
            "line_2": addr["line_2"],
            "city": "Adams",
            "state": "MA",
            "zip": "01220",
        }
    }
    second_update_response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=second_update_request_body,
    )
    assert second_update_response.status_code == 200

    errors = second_update_response.json().get("errors")
    assert not errors


def test_application_patch_phone(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user, phone=None)
    assert application.phone is None

    # adding phone
    update_request_body = {
        "phone": {"phone_number": "************", "phone_type": "Cell", "int_code": "1"}
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    response_phone = response_body.get("data").get("phone")
    assert application.phone.phone_number == "+***********"
    assert application.phone.phone_type_id == 1  # Cell
    assert response_phone["phone_number"] == "***-***-9945"
    assert response_phone["phone_type"] == update_request_body["phone"]["phone_type"]
    assert response_phone["int_code"] == update_request_body["phone"]["int_code"]

    # updating phone
    update_request_body = {
        "phone": {"phone_number": "************", "phone_type": "Phone", "int_code": "1"}
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    assert application.phone.phone_number == "+12404871234"
    assert application.phone.phone_type_id == 3  # Phone
    response_body = response.json()
    assert response.status_code == 200
    response_phone = response_body.get("data").get("phone")
    assert response_phone["phone_number"] == "***-***-1234"
    assert response_phone["phone_type"] == update_request_body["phone"]["phone_type"]

    update_request_body_dob = {"date_of_birth": "1970-01-01"}

    # patching another field and confirming phone still persists
    response_new_update = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body_dob,
    )

    test_db_session.refresh(application)
    assert application.phone.phone_number == "+12404871234"
    assert application.phone.phone_type_id == 3  # Phone
    response_body_new_update = response_new_update.json()
    response_phone = response_body_new_update.get("data").get("phone")
    assert response_phone["phone_number"] == "***-***-1234"
    assert response_phone["phone_type"] == update_request_body["phone"]["phone_type"]

    # patching with a masked value doesn't change the existing database value

    update_request_masked_phone_number = {
        "phone": {"phone_number": "***-***-1234", "int_code": "1", "phone_type": "Cell"}
    }

    response_new_update = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_masked_phone_number,
    )

    test_db_session.refresh(application)
    assert application.phone.phone_number == "+12404871234"
    assert application.phone.phone_type_id == 1  # Cell
    response_body_new_update = response_new_update.json()
    response_phone = response_body_new_update.get("data").get("phone")
    assert response_phone["phone_number"] == "***-***-1234"
    assert response_phone["phone_type"] == update_request_masked_phone_number["phone"]["phone_type"]


def test_application_patch_phone_validation(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user, phone=None)
    assert application.phone is None

    # adding an invalid phone number (fake area code)
    update_request_body = {
        "phone": {"phone_number": "************", "phone_type": "Cell", "int_code": "1"}
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    error = response_body.get("errors")[0]
    assert response.status_code == 400
    assert error["field"] == "phone.phone_number"
    assert error["message"] == "Phone number must be a valid number"


def test_application_unauthorized_patch(client, user, auth_token, test_db_session):
    # create application not associated with user
    application = ApplicationFactory.create(last_name="Smith")

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"last_name": "Perez"},
    )

    tests.api.validate_error_response(response, 403)

    test_db_session.refresh(application)
    assert application.last_name == "Smith"


def test_application_patch_fineos_forbidden(
    client, fineos_user, fineos_user_token, test_db_session
):
    # Fineos role cannot access this endpoint
    # A fineos user having an application doesn't make sense, but makes certain this fails due to the role.
    application = ApplicationFactory.create(user=fineos_user, last_name="Smith")

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {fineos_user_token}"},
        json={"last_name": "Perez"},
    )

    assert response.status_code == 403

    test_db_session.refresh(application)
    assert application.last_name == "Smith"


def test_application_patch_tax_identifier(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    assert application.tax_identifier
    assert application.tax_identifier.tax_identifier != "***********"

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"tax_identifier": "***********"},
    )

    assert response.status_code == 200
    assert response.json()["data"]["tax_identifier"] == "***-**-6789"

    test_db_session.refresh(application)
    assert application.tax_identifier
    assert application.tax_identifier.tax_identifier == "*********"


def test_application_patch_tax_identifier_with_invalid_benefit_year(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)
    assert application.tax_identifier
    assert application.tax_identifier.tax_identifier != "*********"

    tax_identifier = TaxIdentifierFactory.create(tax_identifier="*********")
    EmployeeFactory.create(
        tax_identifier=tax_identifier,
        invalid_benefit_years_since=datetime.date(2022, 1, 1),
    )

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-Disable-Overlapping-Benefit-Year-Claim-Creation": "true",
        },
        json={"tax_identifier": "***********"},
    )

    assert response.status_code == 400

    test_db_session.refresh(application)
    assert application.tax_identifier
    assert application.tax_identifier.tax_identifier == "*********"


def test_application_complete_with_invalid_benefit_year(client, user, auth_token, test_db_session):
    # application that is being updated
    tax_identifier = TaxIdentifierFactory.create(tax_identifier="*********")
    EmployeeFactory.create(
        tax_identifier=tax_identifier,
        invalid_benefit_years_since=datetime.date(2022, 1, 1),
    )
    application: Application = ApplicationFactory.create(user=user, tax_identifier=tax_identifier)

    # Other application where the claim was identified to have an invalid benefit year
    response = client.post(
        "/v1/applications/{}/complete-application".format(application.application_id),
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-Disable-Overlapping-Benefit-Year-Claim-Creation": "true",
        },
    )

    message = "The employee with matching tax identifier has an invalid benefit year."
    assert response.status_code == 400
    assert response.json()["message"] == message
    assert response.json()["errors"] == [
        {
            "type": IssueType.employee_has_invalid_benefit_year.value,
            "message": message,
        }
    ]


def test_application_patch_tax_identifer_associated_with_invalid_benefit_year(
    client, user, auth_token, test_db_session
):
    tax_identifier = TaxIdentifierFactory.create(tax_identifier="*********")
    # application that is being updated
    EmployeeFactory.create(
        tax_identifier=tax_identifier,
        invalid_benefit_years_since=datetime.date(2022, 1, 1),
    )
    application: Application = ApplicationFactory.create(user=user, tax_identifier=tax_identifier)

    # Other application where the claim was identified to have an invalid benefit year
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-Disable-Overlapping-Benefit-Year-Claim-Creation": "true",
        },
        json={"tax_identifier": "987-65-4321"},
    )

    message = "The employee with matching tax identifier has an invalid benefit year."
    assert response.status_code == 400
    assert response.json()["message"] == message
    assert response.json()["errors"] == [
        {
            "type": IssueType.employee_has_invalid_benefit_year.value,
            "message": message,
        }
    ]

    # The application should still be updated with the correct tax identifier
    test_db_session.refresh(application)
    assert application.tax_identifier
    assert application.tax_identifier.tax_identifier == "*********"

    assert (
        response.json()["data"]["tax_identifier"]
        == "***-**-" + application.tax_identifier.tax_identifier[-4:]
    )


def test_application_patch_limit_ssn_max_attempts(
    limit_ssn_fein_max_attempts, client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(
        user=user, tax_identifier=TaxIdentifierFactory.create(tax_identifier="*********")
    )
    # Make sure the fein doesn't change in between requests,
    # because it also counts as an attempt
    initial_fein = application.employer_fein

    # Perform a couple updates to the SSN
    for new_tax_identifier in ["***********", "***********"]:
        assert application.tax_identifier
        assert application.tax_identifier.tax_identifier != new_tax_identifier

        response = client.patch(
            "/v1/applications/{}".format(application.application_id),
            headers={"Authorization": f"Bearer {auth_token}"},
            json={"tax_identifier": new_tax_identifier},
        )

        assert response.status_code == 200
        assert response.json()["data"]["tax_identifier"] == "***-**-" + new_tax_identifier[-4:]

        test_db_session.refresh(application)
        assert application.tax_identifier
        assert application.tax_identifier.tax_identifier == new_tax_identifier.replace("-", "")
        assert application.employer_fein == initial_fein

    # Reached limit SSN max attempts
    new_tax_identifier = "***********"
    assert application.tax_identifier
    assert application.tax_identifier.tax_identifier != new_tax_identifier

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"tax_identifier": new_tax_identifier},
    )

    assert response.status_code == 400
    assert (
        response.json()["data"]["tax_identifier"]
        == "***-**-" + application.tax_identifier.tax_identifier[-4:]
    )
    assert response.json()["errors"][0]["rule"] == IssueRule.max_ssn_fein_update_attempts
    assert application.employer_fein == initial_fein


def test_application_patch_limit_fein_max_attempts(
    limit_ssn_fein_max_attempts, client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user, employer_fein="*********")
    # Make sure the ssn doesn't change in between requests,
    # because it also counts as an attempt
    initial_tax_identifier = application.tax_identifier.tax_identifier

    # Perform a couple updates to the FEIN
    for new_employer_fein in ["12-3456789", "12-3456788"]:
        assert application.employer_fein
        assert application.employer_fein != new_employer_fein

        response = client.patch(
            "/v1/applications/{}".format(application.application_id),
            headers={"Authorization": f"Bearer {auth_token}"},
            json={"employer_fein": new_employer_fein},
        )

        assert response.status_code == 200
        assert response.json()["data"]["employer_fein"] == new_employer_fein

        test_db_session.refresh(application)
        assert application.employer_fein
        assert application.employer_fein == new_employer_fein.replace("-", "")
        assert application.tax_identifier.tax_identifier == initial_tax_identifier

    # Reached limit FEIN max attempts
    new_employer_fein = "12-3456787"
    assert application.employer_fein
    assert application.employer_fein != new_employer_fein

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"employer_fein": new_employer_fein},
    )

    assert response.status_code == 400
    assert response.json()["data"]["employer_fein"] == format_fein(application.employer_fein)
    assert response.json()["errors"][0]["rule"] == IssueRule.max_ssn_fein_update_attempts
    assert application.tax_identifier.tax_identifier == initial_tax_identifier


def test_application_patch_masked_tax_id_has_no_effect(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    tax_identifier_before = application.tax_identifier.tax_identifier

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"tax_identifier": "***-**-****"},
    )

    tests.api.validate_error_response(response, 400)

    test_db_session.refresh(application)
    assert application.tax_identifier
    assert application.tax_identifier.tax_identifier == tax_identifier_before


def test_application_patch_employment_status(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"employment_status": "Employed"},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_employment_status = response_body.get("employment_status")
    assert updated_employment_status == "Employed"


def test_application_patch_ethnicity(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"ethnicity": Ethnicity.HISPANIC_OR_LATINO.ethnicity_description},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_ethnicity = response_body.get("ethnicity")
    assert updated_ethnicity == Ethnicity.HISPANIC_OR_LATINO.ethnicity_description


def test_application_patch_race(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "race": Race.ANOTHER_RACE_NOT_LISTED_ABOVE.race_description,
            "race_custom": "Some other race",
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_race = response_body.get("race")
    updated_race_custom = response_body.get("race_custom")
    assert updated_race == Race.ANOTHER_RACE_NOT_LISTED_ABOVE.race_description
    assert updated_race_custom == "Some other race"


def test_application_patch_race_mena(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "race": Race.MIDDLE_EASTERN_NORTH_AFRICAN.race_description,
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_race = response_body.get("race")
    test_db_session.refresh(application)
    assert application.race_id == Race.MIDDLE_EASTERN_NORTH_AFRICAN.id
    assert updated_race == Race.MIDDLE_EASTERN_NORTH_AFRICAN.race_description


def test_application_patch_language(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "language": Language.CHINESE_SIMPLIFIED.language_description,
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_language = response_body.get("language")
    assert updated_language == Language.CHINESE_SIMPLIFIED.language_description

    test_db_session.refresh(application)

    assert (
        application.language.language_description
        == Language.CHINESE_SIMPLIFIED.language_description
    )
    assert application.language.language_id == Language.CHINESE_SIMPLIFIED.language_id


def test_application_patch_hours_worked_per_week(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"hours_worked_per_week": 50.5},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_hours_worked_per_week = response_body.get("hours_worked_per_week")
    assert updated_hours_worked_per_week == 50.5


def test_application_patch_pregnant_or_recent_birth(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"leave_details": {"pregnant_or_recent_birth": True}},
    )

    assert response.status_code == 200

    response_body = response.json()
    updated_flag = response_body.get("data").get("leave_details").get("pregnant_or_recent_birth")
    assert updated_flag is True


def test_application_patch_child_birth_date(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"leave_details": {"child_birth_date": "2021-09-21"}},
    )

    assert response.status_code == 200

    response_body = response.json()
    child_dob = response_body.get("data").get("leave_details").get("child_birth_date")
    assert child_dob == "****-09-21"


def test_application_patch_child_placement_date(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"leave_details": {"child_placement_date": "2021-05-13"}},
    )

    assert response.status_code == 200

    response_body = response.json()
    child_dob = response_body.get("data").get("leave_details").get("child_placement_date")
    assert child_dob == "****-05-13"


def test_application_patch_has_future_child_date(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user, has_future_child_date=None)
    assert application.has_future_child_date is None
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"leave_details": {"has_future_child_date": True}},
    )
    assert response.status_code == 200
    response_body = response.json()
    response_leave_details = response_body.get("data").get("leave_details")
    assert response_leave_details
    assert response_leave_details.get("has_future_child_date") is True

    test_db_session.refresh(application)
    assert application.has_future_child_date is True


def test_application_patch_state_id_fields(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"has_state_id": True, "mass_id": "*********"},
    )

    assert response.status_code == 200

    response_body = response.json()
    updated_has_state_id = response_body.get("data").get("has_state_id")
    updated_state_id = response_body.get("data").get("mass_id")
    assert updated_has_state_id is True
    assert updated_state_id == "*********"


def test_application_patch_state_id_fields_good_format(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"has_state_id": True, "mass_id": "*********"},
    )

    assert response.status_code == 200

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"has_state_id": True, "mass_id": "SA1234567"},
    )

    assert response.status_code == 200


def test_application_patch_state_id_fields_bad_format(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"has_state_id": True, "mass_id": "*********000"},
    )

    assert response.status_code == 400

    response_body = response.json()
    error = response_body.get("errors")[0]
    assert error["field"] == "mass_id"
    assert (
        error["message"]
        == "'*********000' does not match '^(\\\\d{9}|S(\\\\d{8}|A\\\\d{7})|(\\\\*{9}))$'"
    )

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"has_state_id": True, "mass_id": "C12345678"},
    )

    assert response.status_code == 400

    response_body = response.json()
    error = response_body.get("errors")[0]
    assert error["field"] == "mass_id"
    assert (
        error["message"]
        == "'C12345678' does not match '^(\\\\d{9}|S(\\\\d{8}|A\\\\d{7})|(\\\\*{9}))$'"
    )


def test_application_patch_leave_reason(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    # set to empty value
    application.leave_reason = None
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"leave_details": {"reason": "Serious Health Condition - Employee"}},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_leave_details = response_body.get("leave_details")
    assert updated_leave_details
    updated_leave_reason = updated_leave_details.get("reason")
    assert updated_leave_reason == "Serious Health Condition - Employee"


def test_application_patch_leave_reason_qualifier(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    # set to empty value
    application.leave_reason = None
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"leave_details": {"reason": "Child Bonding", "reason_qualifier": "Foster Care"}},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_leave_details = response_body.get("leave_details")
    assert updated_leave_details
    updated_leave_reason = updated_leave_details.get("reason")
    assert updated_leave_reason == "Child Bonding"
    updated_leave_reason_qualifier = updated_leave_details.get("reason_qualifier")
    assert updated_leave_reason_qualifier == "Foster Care"


def test_application_patch_update_leave_reason_and_delete_leave_reason_qualifier(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    # set an initial value
    application.leave_reason_id = LeaveReason.CHILD_BONDING.leave_reason_id
    application.leave_reason_qualifier_id = LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "leave_details": {
                "reason": "Serious Health Condition - Employee",
                "reason_qualifier": None,
            }
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_leave_details = response_body.get("leave_details")
    assert updated_leave_details
    updated_leave_reason = updated_leave_details.get("reason")
    assert updated_leave_reason == "Serious Health Condition - Employee"
    updated_leave_reason_qualifier = updated_leave_details.get("reason_qualifier")
    assert updated_leave_reason_qualifier is None

    test_db_session.refresh(application)
    assert application.leave_reason_qualifier is None


def test_application_patch_add_leave_period(client, user, auth_token):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "has_continuous_leave_periods": True,
            "has_intermittent_leave_periods": False,
            "has_reduced_schedule_leave_periods": False,
            "leave_details": {"continuous_leave_periods": [{"start_date": "2021-01-01"}]},
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")

    assert response_body.get("has_continuous_leave_periods") is True
    assert response_body.get("has_intermittent_leave_periods") is False
    assert response_body.get("has_reduced_schedule_leave_periods") is False

    updated_leave_details = response_body.get("leave_details")
    assert updated_leave_details

    updated_leave_periods = updated_leave_details.get("continuous_leave_periods")
    assert updated_leave_periods
    assert len(updated_leave_periods) == 1

    updated_leave_period = updated_leave_periods[0]
    assert updated_leave_period["leave_period_id"]
    assert updated_leave_period["start_date"] == "2021-01-01"


def test_application_patch_update_leave_period(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 1, 1), application_id=application.application_id
    )
    test_db_session.add(leave_period)
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(
            {
                "leave_details": {
                    "continuous_leave_periods": [
                        {
                            "leave_period_id": leave_period.leave_period_id,
                            "start_date": "2021-01-03",
                        }
                    ]
                }
            }
        ),
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_leave_details = response_body.get("leave_details")
    assert updated_leave_details

    updated_leave_periods = updated_leave_details.get("continuous_leave_periods")
    assert updated_leave_periods
    assert len(updated_leave_periods) == 1

    updated_leave_period = updated_leave_periods[0]
    assert updated_leave_period["leave_period_id"]
    assert updated_leave_period["start_date"] == "2021-01-03"


@pytest.mark.parametrize("new_leave_period_value", [([]), (None)])
def test_application_patch_delete_all_leave_periods(
    client, user, auth_token, test_db_session, new_leave_period_value
):
    application = ApplicationFactory.create(user=user)

    leave_periods = [
        ContinuousLeavePeriod(
            start_date=datetime.date(2021, 1, 1),
            end_date=datetime.date(2021, 1, 5),
            application_id=application.application_id,
        ),
        ContinuousLeavePeriod(
            start_date=datetime.date(2021, 1, 8),
            end_date=datetime.date(2021, 1, 10),
            application_id=application.application_id,
        ),
        ContinuousLeavePeriod(
            start_date=datetime.date(2021, 1, 12),
            end_date=datetime.date(2021, 1, 15),
            application_id=application.application_id,
        ),
    ]
    for leave_period in leave_periods:
        test_db_session.add(leave_period)
    test_db_session.commit()

    assert len(application.continuous_leave_periods) == len(leave_periods)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"leave_details": {"continuous_leave_periods": new_leave_period_value}},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    updated_leave_details = response_body.get("leave_details")
    assert updated_leave_details

    updated_leave_periods = updated_leave_details.get("continuous_leave_periods")
    assert updated_leave_periods == []

    # Collect the IDs of these leave periods before refreshing the database connection.
    leave_period_ids = [leave_period.leave_period_id for leave_period in leave_periods]
    test_db_session.expire_all()
    for leave_period_id in leave_period_ids:
        assert test_db_session.get(ContinuousLeavePeriod, leave_period_id) is None


def test_application_patch_update_leave_period_belonging_to_other_application_blocked(
    client, user, auth_token, test_db_session
):
    application_1 = ApplicationFactory.create(user=user)
    application_2 = ApplicationFactory.create(user=user)

    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 6, 11), application_id=application_1.application_id
    )
    test_db_session.add(leave_period)
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application_2.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(
            {
                "leave_details": {
                    "continuous_leave_periods": [
                        {
                            "leave_period_id": leave_period.leave_period_id,
                            "start_date": "2021-06-12",
                        }
                    ]
                }
            }
        ),
    )

    tests.api.validate_error_response(response, 403)

    # assert existing leave period has not changed
    # test_db_session.refresh(leave_period)
    assert leave_period.application_id == application_1.application_id
    assert leave_period.start_date == datetime.date(2021, 6, 11)

    # assert other application does not have the leave period
    response = client.get(
        "/v1/applications/{}".format(application_2.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    response_body = response.json().get("data")
    updated_leave_details = response_body.get("leave_details")
    assert updated_leave_details

    updated_leave_periods = updated_leave_details.get("continuous_leave_periods")
    assert len(updated_leave_periods) == 0


def test_application_patch_add_work_pattern(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "work_pattern": {
                "work_pattern_type": "Fixed",
                "work_pattern_days": [
                    {"day_of_week": "Wednesday", "minutes": 60 * 8},
                    {"day_of_week": "Monday", "minutes": 60 * 8},
                    {"day_of_week": "Friday", "minutes": 60 * 8},
                    {"day_of_week": "Thursday", "minutes": 60 * 8},
                    {"day_of_week": "Saturday", "minutes": 60 * 8},
                    {"day_of_week": "Tuesday", "minutes": 60 * 8},
                    {"day_of_week": "Sunday", "minutes": 60 * 8},
                ],
            }
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    work_pattern = response_body.get("work_pattern")

    assert work_pattern.get("work_pattern_type") == "Fixed"
    assert len(work_pattern.get("work_pattern_days")) == 7
    work_pattern_days_of_week = [
        day.get("day_of_week") for day in work_pattern.get("work_pattern_days")
    ]
    assert work_pattern_days_of_week == [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
    ]


def test_application_patch_update_work_pattern(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    new_work_pattern = WorkPattern(
        work_pattern_days=[WorkPatternDay(day_of_week_id=i + 1) for i in range(7)],
        work_pattern_type_id=WorkPatternType.get_id("Fixed"),
    )
    application.work_pattern = new_work_pattern
    test_db_session.add(application)
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "work_pattern": {
                "work_pattern_type": "Fixed",
                "work_pattern_days": [
                    {"day_of_week": "Sunday", "minutes": 60 * 8},
                    {"day_of_week": "Monday", "minutes": 60 * 8},
                    {"day_of_week": "Tuesday", "minutes": 60 * 8},
                    {"day_of_week": "Wednesday", "minutes": 60 * 8},
                    {"day_of_week": "Thursday", "minutes": 60 * 8},
                    {"day_of_week": "Friday", "minutes": 60 * 8},
                    {"day_of_week": "Saturday", "minutes": 60 * 8},
                ],
            }
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    work_pattern = response_body.get("work_pattern")

    assert work_pattern.get("work_pattern_type") == "Fixed"
    assert len(work_pattern.get("work_pattern_days")) == 7


def test_application_patch_remove_work_pattern_days(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    new_work_pattern = WorkPattern(
        work_pattern_type_id=1,
        work_pattern_days=[WorkPatternDay(day_of_week_id=i + 1) for i in range(7)],
    )
    application.work_pattern = new_work_pattern
    test_db_session.add(application)
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"work_pattern": {"work_pattern_type": "Variable", "work_pattern_days": None}},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    work_pattern = response_body.get("work_pattern")

    assert work_pattern.get("work_pattern_type") == "Variable"
    assert len(work_pattern.get("work_pattern_days")) == 0


def test_application_patch_invalid_work_pattern(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    base_work_pattern = {
        "work_pattern_type": "Fixed",
        "work_pattern_days": [
            {"day_of_week": "Sunday", "minutes": 60 * 8},
            {"day_of_week": "Monday", "minutes": 60 * 8},
            {"day_of_week": "Tuesday", "minutes": 60 * 8},
            {"day_of_week": "Wednesday", "minutes": 60 * 8},
            {"day_of_week": "Thursday", "minutes": 60 * 8},
            {"day_of_week": "Friday", "minutes": 60 * 8},
            {"day_of_week": "Saturday", "minutes": 60 * 8},
        ],
    }

    work_pattern_with_additional_days = copy.deepcopy(base_work_pattern)
    work_pattern_with_additional_days["work_pattern_days"].append(
        {"day_of_week": "Sunday", "minutes": 60 * 8}
    )

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"work_pattern": work_pattern_with_additional_days},
    )

    assert response.status_code == 400
    assert response.json().get("errors") == [
        {
            "field": "work_pattern.work_pattern_days",
            "message": "[{'day_of_week': 'Sunday', 'minutes': 480}, {'day_of_week': 'Monday', 'minutes': 480}, {'day_of_week': 'Tuesday', 'minutes': 480}, {'day_of_week': 'Wednesday', 'minutes': 480}, {'day_of_week': 'Thursday', 'minutes': 480}, {'day_of_week': 'Friday', 'minutes': 480}, {'day_of_week': 'Saturday', 'minutes': 480}, {'day_of_week': 'Sunday', 'minutes': 480}] is too long",
            "rule": 7,
            "type": "maxItems",
        }
    ]

    work_pattern_with_missing_days = {
        "work_pattern_type": "Fixed",
        "work_pattern_days": [
            {"day_of_week": "Sunday", "minutes": 60 * 8},
            {"day_of_week": "Monday", "minutes": 60 * 8},
            {"day_of_week": "Wednesday", "minutes": 60 * 8},
        ],
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"work_pattern": work_pattern_with_missing_days},
    )

    assert response.status_code == 400
    assert response.json().get("errors") == [
        {
            "field": "work_pattern.work_pattern_days",
            "message": "Provided work_pattern_days is missing Friday, Saturday, Thursday, Tuesday.",
            "rule": "no_missing_days",
            "type": "required",
        }
    ]


def test_application_patch_has_employer_benefits(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"has_employer_benefits": True},
    )

    assert response.status_code == 200
    data = response.json().get("data")

    assert data.get("has_employer_benefits") is True


def test_application_patch_add_employer_benefits(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "employer_benefits": [
                {
                    "benefit_type": "Short-term disability insurance",
                    "benefit_start_date": "2021-01-10",
                    "benefit_end_date": "2021-01-20",
                    "benefit_amount_dollars": 400,
                    "benefit_amount_frequency": "Per Month",
                    "is_full_salary_continuous": True,
                }
            ]
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    employer_benefits = response_body.get("employer_benefits")

    assert len(employer_benefits) == 1
    employer_benefit = employer_benefits[0]
    assert employer_benefit.get("employer_benefit_id") is not None
    assert employer_benefit.get("benefit_type") == "Short-term disability insurance"
    assert employer_benefit.get("benefit_start_date") == "2021-01-10"
    assert employer_benefit.get("benefit_end_date") == "2021-01-20"
    assert employer_benefit.get("benefit_amount_dollars") == 400
    assert employer_benefit.get("benefit_amount_frequency") == "Per Month"
    assert employer_benefit.get("is_full_salary_continuous") is True


def test_application_patch_add_empty_array_for_employer_benefits(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    benefits = [EmployerBenefitFactory.create(application_id=application.application_id)]
    application.employer_benefits = benefits

    test_db_session.add(application)
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"employer_benefits": []},
    )
    test_db_session.refresh(application)
    assert response.status_code == 200
    assert len(application.employer_benefits) == 0


def test_application_patch_add_empty_employer_benefits(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    benefits = [EmployerBenefitFactory.create(application_id=application.application_id)]
    application.employer_benefits = benefits

    test_db_session.add(application)
    test_db_session.commit()

    employer_benefit_id = application.employer_benefits[0].employer_benefit_id

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "employer_benefits": [
                {
                    "benefit_type": None,
                    "benefit_end_date": None,
                    "benefit_start_date": None,
                    "benefit_amount_dollars": None,
                    "benefit_amount_frequency": None,
                }
            ]
        },
    )
    test_db_session.refresh(application)

    assert response.status_code == 200

    warnings = response.json().get("warnings")
    assert len([x for x in warnings if x.get("field") == "employer_benefits[0].benefit_type"]) == 1
    assert len(application.employer_benefits) == 1
    assert application.employer_benefits[0].employer_benefit_id != str(employer_benefit_id)
    assert application.employer_benefits[0].employer_benefit_id is not None
    assert application.employer_benefits[0].benefit_type is None
    assert application.employer_benefits[0].benefit_start_date is None
    assert application.employer_benefits[0].benefit_end_date is None
    assert application.employer_benefits[0].benefit_amount_dollars is None
    assert application.employer_benefits[0].benefit_amount_frequency is None


def test_application_patch_replace_existing_employer_benefits(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    benefits = EmployerBenefitFactory.create_batch(
        size=2, application_id=application.application_id
    )
    application.employer_benefits = benefits
    test_db_session.add(application)
    test_db_session.commit()
    employer_benefit_id = application.employer_benefits[0].employer_benefit_id

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(
            {
                "employer_benefits": [
                    {
                        "employer_benefit_id": employer_benefit_id,
                        "benefit_type": "Short-term disability insurance",
                        "benefit_end_date": "2021-01-20",
                        "benefit_start_date": "2021-01-10",
                        "benefit_amount_dollars": 400,
                        "benefit_amount_frequency": "Per Month",
                    }
                ]
            }
        ),
    )

    test_db_session.refresh(application)

    assert response.status_code == 200

    response_body = response.json().get("data")
    employer_benefits = response_body.get("employer_benefits")

    assert len(employer_benefits) == 1
    employer_benefit = employer_benefits[0]
    assert employer_benefit.get("employer_benefit_id") != str(employer_benefit_id)
    assert application.employer_benefits[0].employer_benefit_id != str(employer_benefit_id)
    assert employer_benefit.get("benefit_type") == "Short-term disability insurance"
    assert employer_benefit.get("benefit_start_date") == "2021-01-10"
    assert employer_benefit.get("benefit_end_date") == "2021-01-20"
    assert employer_benefit.get("benefit_amount_dollars") == 400
    assert employer_benefit.get("benefit_amount_frequency") == "Per Month"


def test_application_patch_employer_benefit_exceed_limit(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    limit = 6

    benefits = EmployerBenefitFactory.create_batch(
        size=5, application_id=application.application_id
    )
    application.employer_benefits = benefits
    test_db_session.add(application)
    test_db_session.commit()

    existing_benefits = application.employer_benefits

    new_benefits = [
        {
            "benefit_type": "Short-term disability insurance",
            "benefit_end_date": "2021-01-20",
            "benefit_start_date": "2021-01-10",
            "benefit_amount_dollars": 400,
            "benefit_amount_frequency": "Per Month",
        }
        for i in range(limit + 1)
    ]

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"employer_benefits": new_benefits},
    )

    assert response.status_code == 400
    test_db_session.refresh(application)
    assert application.employer_benefits == existing_benefits


def test_application_patch_has_other_incomes(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"has_other_incomes": True},
    )

    assert response.status_code == 200
    data = response.json().get("data")

    assert data.get("has_other_incomes") is True


def test_application_patch_add_other_incomes(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "other_incomes": [
                {
                    "income_type": "Unemployment Insurance",
                    "income_end_date": "2021-01-20",
                    "income_start_date": "2021-01-10",
                    "income_amount_dollars": 800,
                    "income_amount_frequency": "Per Month",
                }
            ]
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    other_incomes = response_body.get("other_incomes")

    assert len(other_incomes) == 1
    other_income = other_incomes[0]
    assert other_income.get("income_type") == "Unemployment Insurance"
    assert other_income.get("income_start_date") == "2021-01-10"
    assert other_income.get("income_end_date") == "2021-01-20"
    assert other_income.get("income_amount_dollars") == 800
    assert other_income.get("income_amount_frequency") == "Per Month"


def test_application_patch_add_empty_array_for_other_incomes(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    incomes = [OtherIncomeFactory.create(application_id=application.application_id)]
    application.other_incomes = incomes

    test_db_session.add(application)
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"other_incomes": []},
    )
    test_db_session.refresh(application)

    assert response.status_code == 200
    assert len(application.other_incomes) == 0


def test_application_patch_add_empty_other_income(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    incomes = [OtherIncomeFactory.create(application_id=application.application_id)]
    application.other_incomes = incomes

    test_db_session.add(application)
    test_db_session.commit()
    other_income_id = application.other_incomes[0].other_income_id

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "other_incomes": [
                {
                    "income_type": None,
                    "income_end_date": None,
                    "income_start_date": None,
                    "income_amount_dollars": None,
                    "income_amount_frequency": None,
                }
            ]
        },
    )

    test_db_session.refresh(application)

    assert response.status_code == 200
    warnings = response.json().get("warnings")
    assert len([x for x in warnings if x.get("field") == "other_incomes[0].income_type"]) == 1
    assert len(application.other_incomes) == 1
    assert application.other_incomes[0].other_income_id != str(other_income_id)
    assert application.other_incomes[0].other_income_id is not None
    assert application.other_incomes[0].income_type is None
    assert application.other_incomes[0].income_start_date is None
    assert application.other_incomes[0].income_end_date is None
    assert application.other_incomes[0].income_amount_dollars is None
    assert application.other_incomes[0].income_amount_frequency is None


def test_application_patch_replace_existing_other_incomes(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    incomes = OtherIncomeFactory.create_batch(size=2, application_id=application.application_id)

    application.other_incomes = incomes
    test_db_session.add(application)
    test_db_session.commit()
    other_income_id = application.other_incomes[0].other_income_id

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(
            {
                "other_incomes": [
                    {
                        "other_income_id": other_income_id,
                        "income_type": "Workers Compensation",
                        "income_end_date": "2021-01-20",
                        "income_start_date": "2021-01-10",
                        "income_amount_dollars": 400,
                        "income_amount_frequency": "Per Month",
                    }
                ]
            }
        ),
    )

    test_db_session.refresh(application)
    assert response.status_code == 200

    response_body = response.json().get("data")
    other_incomes = response_body.get("other_incomes")

    assert len(other_incomes) == 1
    other_income = other_incomes[0]
    assert other_income.get("other_income_id") != str(other_income_id)
    assert application.other_incomes[0].other_income_id != str(other_income_id)
    assert other_income.get("income_type") == "Workers Compensation"
    assert other_income.get("income_start_date") == "2021-01-10"
    assert other_income.get("income_end_date") == "2021-01-20"
    assert other_income.get("income_amount_dollars") == 400
    assert other_income.get("income_amount_frequency") == "Per Month"


def test_application_patch_other_income_exceed_limit(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    limit = 6

    incomes = OtherIncomeFactory.create_batch(size=5, application_id=application.application_id)
    application.other_incomes = incomes
    test_db_session.add(application)
    test_db_session.commit()

    existing_incomes = application.other_incomes

    new_incomes = [
        {
            "income_type": "Workers Compensation",
            "income_end_date": "2021-01-20",
            "income_start_date": "2021-01-10",
            "income_amount_dollars": 400,
            "income_amount_frequency": "Per Month",
        }
        for i in range(limit + 1)
    ]

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"other_incomes": new_incomes},
    )

    assert response.status_code == 400
    test_db_session.refresh(application)
    assert application.other_incomes == existing_incomes


def test_application_delete_user_not_found_info(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    application.additional_user_not_found_info = ApplicationUserNotFoundInfoFactory.build(
        application_id=application.application_id
    )

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"additional_user_not_found_info": None},
    )

    assert response.status_code == 200

    data = response.json().get("data")

    assert getattr(data, "additional_user_not_found_info", None) is None


def test_application_patch_user_not_found_info(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "additional_user_not_found_info": {
                "currently_employed": True,
                "date_of_hire": "2020-11-01",
                "date_of_separation": "2021-01-01",
                "employer_name": "Employer's name",
                "recently_acquired_or_merged": False,
            },
        },
    )

    assert response.status_code == 200
    info = response.json().get("data")["additional_user_not_found_info"]

    assert info["currently_employed"] is True
    assert info["date_of_hire"] == "2020-11-01"
    assert info["date_of_separation"] == "2021-01-01"
    assert info["employer_name"] == "Employer's name"
    assert info["recently_acquired_or_merged"] is False


def test_application_patch_user_not_found_conditional_validation(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "additional_user_not_found_info": {
                "currently_employed": True,
            },
        },
    )

    assert response.status_code == 200
    warnings = response.json().get("warnings")
    assert {
        "field": "additional_user_not_found_info.employer_name",
        "message": "employer_name is required if additional_user_not_found_info is set",
        "type": "required",
        "rule": "conditional",
    } in warnings

    assert {
        "field": "additional_user_not_found_info.date_of_hire",
        "message": "date_of_hire is required if additional_user_not_found_info is set",
        "rule": "conditional",
        "type": "required",
    } in warnings
    assert {
        "field": "is_withholding_tax",
        "message": "Tax withholding preference is required",
        "type": "required",
    }


def test_application_patch_user_not_found_info_replaces_existing(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)
    application.additional_user_not_found_info = ApplicationUserNotFoundInfoFactory.build(
        application_id=application.application_id,
        currently_employed=False,
        employer_name="Before",
        date_of_hire=datetime.date(2020, 1, 1),
        date_of_separation=datetime.date(2022, 1, 1),
        recently_acquired_or_merged=True,
    )

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "additional_user_not_found_info": {
                "currently_employed": True,
                "date_of_hire": "2020-12-31",
                "employer_name": "After",
                "recently_acquired_or_merged": False,
            },
        },
    )

    assert response.status_code == 200

    info = response.json().get("data")["additional_user_not_found_info"]

    assert info["currently_employed"] is True
    assert info["date_of_hire"] == "2020-12-31"
    assert info["employer_name"] == "After"
    assert info["recently_acquired_or_merged"] is False
    assert "date_of_separation" not in info


def test_application_patch_has_previous_leaves(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "has_previous_leaves": True,
        },
    )

    assert response.status_code == 200
    data = response.json().get("data")

    assert data.get("has_previous_leaves") is True


def test_application_patch_add_previous_leaves(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "previous_leaves": [
                {
                    "is_for_current_employer": True,
                    "leave_start_date": "2021-01-01",
                    "leave_end_date": "2021-05-01",
                    "leave_reason": "Pregnancy",
                    "worked_per_week_minutes": 20,
                    "leave_minutes": 10,
                },
                {
                    "is_for_current_employer": True,
                    "leave_start_date": "2021-01-01",
                    "leave_end_date": "2021-05-01",
                    "worked_per_week_minutes": 20,
                    "leave_minutes": 10,
                },
            ],
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    previous_leaves = response_body.get("previous_leaves")
    assert len(previous_leaves) == 2

    for previous_leave in [previous_leaves[0], previous_leaves[1]]:
        assert previous_leave.get("is_for_current_employer") is True
        assert previous_leave.get("leave_start_date") == "2021-01-01"
        assert previous_leave.get("leave_end_date") == "2021-05-01"
        assert previous_leave.get("worked_per_week_minutes") == 20
        assert previous_leave.get("leave_minutes") == 10

    assert previous_leaves[0].get("leave_reason") == "Pregnancy"
    assert previous_leaves[1].get("leave_reason") is None


def test_application_patch_add_any_reason_previous_leaves(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={
            "Authorization": f"Bearer {auth_token}",
        },
        json={
            "previous_leaves": [
                {
                    "is_for_current_employer": True,
                    "leave_start_date": "2021-01-01",
                    "leave_end_date": "2021-05-01",
                    "leave_reason": "Pregnancy",
                    "worked_per_week_minutes": 20,
                    "leave_minutes": 10,
                },
                {
                    "is_for_current_employer": True,
                    "leave_start_date": "2021-01-01",
                    "leave_end_date": "2021-05-01",
                    "leave_reason": "Pregnancy",
                    "worked_per_week_minutes": 20,
                    "leave_minutes": 10,
                    "is_continuous": True,
                },
                {
                    "is_for_current_employer": True,
                    "leave_start_date": "2021-01-01",
                    "leave_end_date": "2021-05-01",
                    "leave_reason": "An illness or injury that required hospitalization",
                    "worked_per_week_minutes": 20,
                    "leave_minutes": 10,
                    "type": "any_reason",
                    "is_continuous": False,
                },
                {
                    "is_for_current_employer": True,
                    "leave_start_date": "2021-01-01",
                    "leave_end_date": "2021-05-01",
                    "leave_reason": "A health condition during pregnancy",
                    "worked_per_week_minutes": 20,
                    "leave_minutes": 10,
                    "type": "any_reason",
                },
            ],
        },
    )

    assert response.status_code == 200

    response_body = response.json().get("data")

    assert len(response_body.get("previous_leaves")) == 4
    assert response_body.get("previous_leaves")[0]["type"] == "any_reason"
    assert response_body.get("previous_leaves")[1]["is_continuous"] is True
    assert response_body.get("previous_leaves")[2]["is_continuous"] is False
    assert response_body.get("previous_leaves")[3].get("is_continuous") is None

    assert response_body.get("previous_leaves")[2]["type"] == "any_reason"
    assert (
        response_body.get("previous_leaves")[2]["leave_reason"]
        == "An illness or injury that required hospitalization"
    )
    assert (
        response_body.get("previous_leaves")[3]["leave_reason"]
        == "A health condition during pregnancy"
    )


def test_application_patch_add_empty_array_for_previous_leaves(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    application.previous_leaves = [
        PreviousLeaveFactory.create(application_id=application.application_id),
        PreviousLeaveFactory.create(application_id=application.application_id),
    ]

    test_db_session.add(application)
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "previous_leaves": [],
        },
    )
    test_db_session.refresh(application)

    assert response.status_code == 200
    assert len(application.previous_leaves) == 0


def test_application_patch_add_empty_previous_leaves(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)

    application.previous_leaves = [
        PreviousLeaveFactory.create(application_id=application.application_id),
        PreviousLeaveFactory.create(application_id=application.application_id),
    ]

    test_db_session.add(application)
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "previous_leaves": [
                {
                    "is_for_current_employer": None,
                    "leave_end_date": None,
                    "leave_start_date": None,
                    "leave_reason": None,
                    "worked_per_week_minutes": None,
                    "leave_minutes": None,
                },
                {
                    "is_for_current_employer": None,
                    "leave_end_date": None,
                    "leave_start_date": None,
                    "leave_reason": None,
                    "worked_per_week_minutes": None,
                    "leave_minutes": None,
                },
            ],
        },
    )

    test_db_session.refresh(application)

    assert response.status_code == 200

    for leave in application.previous_leaves:
        assert leave.previous_leave_id is not None
        assert leave.is_for_current_employer is None
        assert leave.leave_start_date is None
        assert leave.leave_reason is None
        assert leave.worked_per_week_minutes is None
        assert leave.leave_minutes is None


def test_application_patch_null_date_of_birth(client, user, auth_token):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"date_of_birth": None},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    dob = response_body.get("date_of_birth")
    assert dob is None


def test_application_patch_date_of_birth_after_1900_over_14(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    now = datetime.datetime.now()
    test_date = now - relativedelta(years=20)
    test_date_str = test_date.strftime("%Y-%m-%d")

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"date_of_birth": test_date_str},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    dob = response_body.get("date_of_birth")
    assert dob == f"****{test_date_str[4:]}"

    test_db_session.refresh(application)
    assert application.date_of_birth.isoformat() == test_date_str


def test_application_patch_date_of_birth_under_14(client, user, auth_token):
    application = ApplicationFactory.create(user=user)

    now = datetime.datetime.now()
    test_date = now - relativedelta(years=14) + relativedelta(days=1)
    test_date_string = test_date.strftime("%Y-%m-%d")

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"date_of_birth": test_date_string},
    )

    assert response.status_code == 400

    response_body = response.json()
    errors = response_body.get("errors")
    assert len(errors) == 1

    error = errors[0]
    field = error.get("field")
    message = error.get("message")
    rule = error.get("rule")
    error_type = error.get("type")

    assert field == "date_of_birth"
    assert message == "The person taking leave must be at least 14 years old"
    assert rule == "older_than_14"
    assert error_type == "invalid_age"


def test_application_patch_date_of_birth_before_1850(client, user, auth_token):
    application = ApplicationFactory.create(user=user)

    test_date = datetime.datetime(1849, 12, 31)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"date_of_birth": test_date.strftime("%Y-%m-%d")},
    )

    assert response.status_code == 400

    response_body = response.json()
    errors = response_body.get("errors")
    assert len(errors) == 1

    error = errors[0]
    field = error.get("field")
    message = error.get("message")
    rule = error.get("rule")
    error_type = error.get("type")

    assert field == "date_of_birth"
    assert message == "Date of birth must be within the past 150 years"
    assert rule == "date_of_birth_within_past_150_years"
    assert error_type == "invalid_year_range"


@freeze_time("2020-01-01")
def test_application_patch_date_of_birth_in_future(client, user, auth_token):
    application = ApplicationFactory.create(user=user)
    date_of_birth = "2022-01-01"

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"date_of_birth": date_of_birth},
    )

    assert response.status_code == 400

    response_body = response.json()
    errors = response_body.get("errors")
    assert len(errors) == 1

    error = errors[0]
    field = error.get("field")
    message = error.get("message")
    rule = error.get("rule")
    error_type = error.get("type")

    assert field == "date_of_birth"
    assert message == "The person taking leave must be at least 14 years old"
    assert rule == "older_than_14"
    assert error_type == "invalid_age"


def test_application_patch_invalid_dates(client, user, auth_token):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "date_of_birth": "1970-13-42",
            "leave_details": {
                "employer_notification_date": "970-06-01",
                "reduced_schedule_leave_periods": [{"end_date": ""}],
            },
        },
    )

    tests.api.validate_error_response(
        response,
        400,
        errors=[
            {
                "field": "date_of_birth",
                "message": "'1970-13-42' is not a 'maskable_date'",
                "rule": "maskable_date",
                "type": "format",
                "value": "1970-13-42",
            },
            {
                "field": "leave_details.reduced_schedule_leave_periods.0.end_date",
                "message": "'' is not a 'date'",
                "rule": "date",
                "type": "format",
                "value": "",
            },
            {
                "field": "leave_details.employer_notification_date",
                "message": "'970-06-01' is not a 'date'",
                "rule": "date",
                "type": "format",
                "value": "970-06-01",
            },
        ],
    )


def test_application_patch_minimum_payload(client, user, auth_token):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    assert response_body is not None


def test_application_patch_null_values(client, user, auth_token):
    application = ApplicationFactory.create(user=user)
    null_request_body = {
        "application_id": application.application_id,
        "tax_identifier": None,
        "employer_fein": None,
        "hours_worked_per_week": None,
        "first_name": None,
        "last_name": None,
        "leave_details": {
            "continuous_leave_periods": [
                {
                    "end_date": None,
                    "end_date_full_day": None,
                    "end_date_off_hours": None,
                    "end_date_off_minutes": None,
                    "expected_return_to_work_date": None,
                    "last_day_worked": None,
                    "start_date": None,
                    "start_date_full_day": None,
                    "start_date_off_hours": None,
                    "start_date_off_minutes": None,
                    "status": None,
                }
            ],
            "child_birth_date": None,
            "child_placement_date": None,
            "employer_notification_date": None,
            "employer_notification_method": None,
            "employer_notified": None,
            "intermittent_leave_periods": [
                {
                    "duration": None,
                    "duration_basis": None,
                    "end_date": None,
                    "frequency": None,
                    "frequency_interval": None,
                    "frequency_interval_basis": None,
                    "start_date": None,
                }
            ],
            "reason": None,
            "reason_qualifier": None,
            "reduced_schedule_leave_periods": [
                {
                    "end_date": None,
                    "friday_off_minutes": None,
                    "monday_off_minutes": None,
                    "saturday_off_minutes": None,
                    "start_date": None,
                    "status": None,
                    "sunday_off_minutes": None,
                    "thursday_off_minutes": None,
                    "tuesday_off_minutes": None,
                    "wednesday_off_minutes": None,
                }
            ],
            "relationship_qualifier": None,
            "relationship_to_caregiver": None,
        },
        "occupation": None,
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(null_request_body),
    )
    assert response.json().get("warnings")
    assert response.status_code == 200


def test_application_patch_invalid_values(client, user, auth_token):
    # Assert that API returns a 400 error when the request body
    # doesn't conform to the OpenAPI spec
    application = ApplicationFactory.create(user=user)

    update_request_body = {
        # Leave periods should be an array, not an object
        "leave_details": {"reduced_schedule_leave_periods": {}},
        # mass_id should conform to the pattern (e.g *********),
        "mass_id": "*********0000",
        "residential_address": {
            # zip should conform to the pattern (e.g 12345-1234),
            "zip": "123456"
        },
        # tax_identifier should conform to the pattern (e.g ***********),
        "tax_identifier": "***********0000",
        "phone": {
            # phone number should conform to the pattern (e.g. ************)
            "phone_number": "(*************",
            "phone_type": "Cell",
            "int_code": "1",
        },
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    tests.api.validate_error_response(
        response,
        400,
        message="Request Validation Error",
        errors=[
            {
                "field": "tax_identifier",
                "message": "'***********0000' does not match '^[\\\\d|\\\\*]{3}-[\\\\d|\\\\*]{2}-\\\\d{4}$'",
                "rule": "^[\\d|\\*]{3}-[\\d|\\*]{2}-\\d{4}$",
                "type": "pattern",
            },
            {
                "field": "residential_address.zip",
                "message": "'123456' does not match '^[0-9]{5}((?:-[0-9]{4})?|(?:-\\\\*{4})?)$'",
                "rule": "^[0-9]{5}((?:-[0-9]{4})?|(?:-\\*{4})?)$",
                "type": "pattern",
            },
            {
                "field": "mass_id",
                "message": "'*********0000' does not match '^(\\\\d{9}|S(\\\\d{8}|A\\\\d{7})|(\\\\*{9}))$'",
                "rule": "^(\\d{9}|S(\\d{8}|A\\d{7})|(\\*{9}))$",
                "type": "pattern",
            },
            {
                "field": "leave_details.reduced_schedule_leave_periods",
                "message": "{} is not of type 'array'",
                "rule": "array",
                "type": "type",
                "value": "dict",
            },
            {
                "field": "phone.phone_number",
                "message": "'(*************' does not match '^([0-9]|\\\\*){3}\\\\-([0-9]|\\\\*){3}\\\\-[0-9]{4}$'",
                "rule": "^([0-9]|\\*){3}\\-([0-9]|\\*){3}\\-[0-9]{4}$",
                "type": "pattern",
            },
        ],
    )


@pytest.mark.parametrize("state_string", ("ZZ", "New York"))
def test_application_patch_state_invalid(client, user, auth_token, state_string):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"mailing_address": {"state": state_string}},
    )

    tests.api.validate_error_response(
        response,
        400,
        errors=[
            {
                "field": "mailing_address.state",
                "message": f"'{state_string}' is not a valid state",
                "type": "invalid",
            }
        ],
    )


def test_application_patch_state_valid(client, user, auth_token):
    application = ApplicationFactory.create(user=user)

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"mailing_address": {"state": "NY"}},
    )

    assert response.status_code == 200
    assert response.json()["data"]["mailing_address"]["state"] == "NY"


def test_application_patch_fein_not_found(client, user, auth_token):
    # Assert that API returns a validation warning when the request
    # includes an EIN that doesn't match an Employer record
    application = ApplicationFactory.create(user=user)

    update_request_body = {"employer_fein": "99-9999999"}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    response_body = response.json()
    warnings = response_body.get("warnings")

    assert {
        "rule": IssueRule.require_contributing_employer,
        "message": "Confirm that you have the correct EIN, and that the Employer is contributing to Paid Family and Medical Leave.",
        "type": IssueType.pfml,
    } in warnings


def test_application_patch_keys_not_in_body_retain_existing_value(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    # establish some existing value
    application.first_name = "Foo"
    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    response_body = response.json().get("data")

    assert response_body.get("first_name") == "Foo"

    # update some other field
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"last_name": "Bar"},
    )

    assert response.status_code == 200

    # ensure the existing field still has it's existing value
    test_db_session.refresh(application)
    assert application.first_name == "Foo"

    # for extra measure
    response_body = response.json().get("data")
    assert response_body.get("first_name") == "Foo"


def test_application_patch_key_set_to_null_does_null_field(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)

    # establish some existing value
    application.first_name = "Foo"
    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    response_body = response.json().get("data")
    assert response_body.get("first_name") == "Foo"

    # null the field
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"first_name": None},
    )

    assert response.status_code == 200

    # ensure it's null in the db
    test_db_session.refresh(application)
    assert application.first_name is None

    # for extra measure
    response_body = response.json().get("data")
    assert response_body.get("first_name") is None


def test_application_patch_failure_after_absence_case_creation(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )

    # Attach absence case information application so it appears as if this application has already been submitted.
    application.claim = claim
    application.submitted_time = datetime_util.utcnow()
    test_db_session.commit()

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={},
    )
    message = "Application {} could not be updated. Application already submitted on {}".format(
        application.application_id, datetime_util.utcnow().strftime("%x")
    )
    tests.api.validate_error_response(
        response,
        403,
        message=message,
        errors=[{"type": "exists", "field": "claim", "message": message}],
    )


def test_application_post_submit_app(client, user, auth_token, test_db_session):
    factory.random.reseed_random(1)
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()

    application = SubmittableApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    assert not application.submitted_time

    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    response_body = response.json()

    assert response.status_code == 201
    assert employee.date_of_birth
    assert employee.date_of_birth == application.date_of_birth
    assert employee.gender
    assert employee.gender_id == application.gender_id
    assert not response_body.get("errors")
    assert not response_body.get("warnings")
    # Simplified check to confirm Application was included in response:
    assert response_body.get("data").get("application_id") == str(application.application_id)
    assert response_body.get("data").get("fineos_absence_id") == "NTN-259-ABS-01"
    assert response_body.get("data").get("status") == ApplicationStatus.Submitted.value
    test_db_session.refresh(application)
    assert application.submitted_time


def test_application_language_preference_post_submit(
    client, user, auth_token, test_db_session, monkeypatch, caplog_info
):
    massgov.pfml.fineos.mock_client.start_capture()

    factory.random.reseed_random(1)
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    clp = ContinuousLeavePeriodFactory.create()
    application = SubmittableApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
        continuous_leave_periods=[clp],
        language_id=Language.HAITIAN_CREOLE.language_id,
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    capture = massgov.pfml.fineos.mock_client.get_capture()
    update_customer_details = [call for call in capture if call[0] == "update_customer_details"]
    assert len(update_customer_details) == 1

    assert (
        update_customer_details[0][2]["customer"].languages[0].languageEnum.domainName == "Language"
    )
    assert (
        update_customer_details[0][2]["customer"].languages[0].languageEnum.instanceValue
        == "Haitian Creole"
    )
    assert update_customer_details[0][2]["customer"].languages[0].preferredLanguage
    assert update_customer_details[0][2]["customer"].languages[0].written


def test_application_default_language_preference_post_submit(
    client, user, auth_token, test_db_session, monkeypatch, caplog_info
):
    massgov.pfml.fineos.mock_client.start_capture()

    factory.random.reseed_random(1)
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    clp = ContinuousLeavePeriodFactory.create()
    application = SubmittableApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
        continuous_leave_periods=[clp],
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    capture = massgov.pfml.fineos.mock_client.get_capture()
    update_customer_details = [call for call in capture if call[0] == "update_customer_details"]
    assert len(update_customer_details) == 1

    assert (
        update_customer_details[0][2]["customer"].languages[0].languageEnum.domainName == "Language"
    )
    assert (
        update_customer_details[0][2]["customer"].languages[0].languageEnum.instanceValue
        == "English"
    )
    assert update_customer_details[0][2]["customer"].languages[0].preferredLanguage
    assert update_customer_details[0][2]["customer"].languages[0].written


@pytest.fixture
def application_spanning_benefit_years(user):
    by_start_date = datetime.date(2021, 2, 7)
    by_end_date = by_start_date + datetime.timedelta(weeks=52)  # 2022-02-06
    leave_start_date = by_end_date - datetime.timedelta(days=4)  # 2022-02-02
    leave_end_date = by_end_date + datetime.timedelta(days=4)  # 2022-02-10

    factory.random.reseed_random(1)
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    BenefitYearFactory.create(start_date=by_start_date, end_date=by_end_date, employee=employee)
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=leave_start_date, end_date=leave_end_date)
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.first_name = "First"
    application.middle_name = "Middle"
    application.last_name = "Last"
    application.mass_id = "*********"
    application.gender_id = Gender.WOMAN.gender_id
    return application


@freeze_time("2022-02-01")
def test_application_post_submit_app_split_across_by_logs(
    client, auth_token, application_spanning_benefit_years, caplog_info, test_db_session
):
    application = application_spanning_benefit_years
    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={
            "Authorization": f"Bearer {auth_token}",
        },
    )

    assert response.status_code == 201
    assert_log_contains(caplog_info, "application would have been split: True")


@freeze_time("2022-02-01")
def test_application_post_submit_app_does_not_split_when_already_by_is_not_crossed(
    client, user, auth_token, test_db_session, caplog_info
):
    massgov.pfml.fineos.mock_client.start_capture()
    by_start_date = datetime.date(2021, 2, 7)
    by_end_date = by_start_date + datetime.timedelta(weeks=52)  # 2022-02-06
    leave_start_date = by_end_date - datetime.timedelta(days=8)  # 2022-02-02
    leave_end_date = by_end_date - datetime.timedelta(days=4)  # 2022-02-10

    factory.random.reseed_random(1)
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
    )
    BenefitYearFactory.create(start_date=by_start_date, end_date=by_end_date, employee=employee)
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=leave_start_date, end_date=leave_end_date)
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()

    assert not application.submitted_time

    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    response_body = response.json()

    assert response.status_code == 201
    assert not response_body.get("errors")
    assert not response_body.get("warnings")
    # Simplified check to confirm Application was included in response:
    assert response_body.get("data").get("application_id") == str(application.application_id)
    assert response_body.get("data").get("fineos_absence_id") == "NTN-259-ABS-01"
    assert response_body.get("data").get("status") == ApplicationStatus.Submitted.value
    assert response_body.get("data").get("split_into_application_id") is None
    # This is the submit call.  The full fineos API calls are asserted against in a different test
    # This asserts that the app was not split (and the split app submitted)
    submit_calls = [
        capture
        for capture in massgov.pfml.fineos.mock_client.get_capture()
        if capture[0] == "start_absence"
    ]
    assert len(submit_calls) == 1
    assert_log_contains(caplog_info, "application would have been split: False")


@freeze_time("2022-12-01")
def test_application_post_submit_app_to_manual_review(
    client, user, auth_token, test_db_session, application_additional_user_not_found_info
):
    assert not application_additional_user_not_found_info.submitted_time
    assert (
        not application_additional_user_not_found_info.additional_user_not_found_info.submitted_time
    )

    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(
            application_additional_user_not_found_info.application_id
        ),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    response_body = response.json()

    assert response.status_code == 201
    assert not response_body.get("errors")
    assert not response_body.get("warnings")
    # Simplified check to confirm Application was included in response:
    assert response_body.get("data").get("application_id") == str(
        application_additional_user_not_found_info.application_id
    )
    assert response_body.get("data").get("fineos_absence_id") is None
    assert response_body.get("data").get("status") == ApplicationStatus.InManualReview.value
    test_db_session.refresh(application_additional_user_not_found_info)
    assert application_additional_user_not_found_info.submitted_time is None
    assert application_additional_user_not_found_info.additional_user_not_found_info.submitted_time


def test_application_post_submit_app_does_not_persist_payment_preference_and_tax_withholding(
    client, user, auth_token, test_db_session
):
    employee = EmployeeFactory.create()
    employer = EmployerFactory.create()

    WagesAndContributionsFactory.create(
        employer=employer,
        employee=employee,
    )

    payment_preference = PaymentPreferenceFactory.create()

    application = ApplicationFactory.create(
        continuous_leave_periods=[
            ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
        ],
        date_of_birth=datetime.date(1997, 6, 6),
        employer_fein=employer.employer_fein,
        employment_status_id=EmploymentStatus.UNEMPLOYED.employment_status_id,
        has_continuous_leave_periods=True,
        hours_worked_per_week=70,
        is_withholding_tax=True,
        payment_preference=payment_preference,
        residential_address=AddressFactory.create(),
        tax_identifier=employee.tax_identifier,
        user=user,
        work_pattern=WorkPatternFixedFactory.create(),
    )

    response = client.post(
        f"/v1/applications/{application.application_id}/submit-application",
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    test_db_session.refresh(application)
    assert response.status_code == 201
    assert application.is_withholding_tax is None
    assert application.payment_preference is None

    persisted_payment_preference = (
        test_db_session.query(ApplicationPaymentPreference)
        .filter(ApplicationPaymentPreference.payment_pref_id == payment_preference.payment_pref_id)
        .first()
    )

    assert persisted_payment_preference is None


def create_mock_client(err: FINEOSClientError):
    class MockFINEOSTestClient(massgov.pfml.fineos.mock_client.MockFINEOSClient):
        def register_api_user(
            self, employee_registration: massgov.pfml.fineos.models.EmployeeRegistration
        ) -> None:
            raise err

    def inner(config: Optional[FINEOSClientConfig] = None) -> AbstractFINEOSClient:
        return MockFINEOSTestClient()

    return inner


@pytest.mark.parametrize(
    "expected_status,issue_type,err",
    [
        (
            400,
            IssueType.fineos_case_creation_issues,
            FINEOSEntityNotFound(
                "<ErrorDetails><faultcode>com.fineos.common.portalinfrastructure.exceptions.GenericUncheckedException</faultcode><faultstring>The employee does not have an occupation linked.</faultstring><detail></detail></ErrorDetails>"
            ),
        ),
        (
            500,
            None,
            FINEOSFatalResponseError(
                method_name="test_name",
                message="<ErrorDetails><faultcode>com.fineos.common.exceptions.WSException</faultcode><faultstring>More than One Employee Details Found for the input Search Criteria.</faultstring><detail></detail></ErrorDetails>",
            ),
        ),
        (
            503,
            IssueType.fineos_case_error,
            FINEOSFatalUnavailable(response_status=504, method_name="test_name"),
        ),
    ],
)
def test_application_post_submit_fineos_register_api_errors(
    client, user, auth_token, test_db_session, monkeypatch, err, expected_status, issue_type
):
    monkeypatch.setattr(massgov.pfml.fineos, "create_client", create_mock_client(err))

    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    test_db_session.commit()

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == expected_status

    response_body = response.json()
    expected_message = f"Application {str(application.application_id)} could not be submitted"

    if expected_status == 503:
        expected_message += ", try again later"

    assert response_body.get("message"), expected_message

    fineos_issues = response_body.get("errors") or []
    assert not response_body.get("warnings")

    num_issues = len(list(filter(lambda i: i["type"] == issue_type, fineos_issues)))

    if issue_type is None:
        assert num_issues == 0
    else:
        assert num_issues > 0
        test_db_session.refresh(application)
        assert application.submitted_time is None
        # Simplified check to confirm Application was included in response:
        assert response_body.get("data").get("application_id") == str(application.application_id)
        assert not response_body.get("data").get("fineos_absence_id")
        assert response_body.get("data").get("status") == ApplicationStatus.Started.value


def test_application_post_submit_complete_intake_fineos_api_errors(
    client, user, auth_token, test_db_session, monkeypatch
):
    class MockFINEOSTestClient(massgov.pfml.fineos.mock_client.MockFINEOSClient):
        def complete_intake(
            self, user_id: str, notification_case_id: str
        ) -> NotificationCaseSummary:
            raise FINEOSFatalUnavailable(response_status=504, method_name="complete_intake")

    monkeypatch.setattr(massgov.pfml.fineos, "create_client", MockFINEOSTestClient)

    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    test_db_session.commit()

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 503
    response_body = response.json()
    expected_message = (
        f"Application {str(application.application_id)} could not be submitted, try again later"
    )
    assert response_body.get("message"), expected_message
    assert response_body.get("data").get("fineos_absence_id") is not None

    test_db_session.refresh(application)
    assert application.claim is not None
    assert application.submitted_time is None
    assert len(application.continuous_leave_periods) == 1

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "has_continuous_leave_periods": True,
            "has_intermittent_leave_periods": False,
            "has_reduced_schedule_leave_periods": False,
            "leave_details": {"continuous_leave_periods": [{"start_date": "2021-01-01"}]},
        },
    )

    assert response.status_code == 200
    test_db_session.refresh(application)
    assert len(application.continuous_leave_periods) == 2


def test_application_post_submit_app_already_submitted(client, user, auth_token, test_db_session):
    # This test aims to test the scenario where the application was successfully sent to fineos,
    # but failed when trying to complete the intake. This would mean we have the fineos_absence_id,
    # but it isn't currently in submitted status. This verifies that it only calls methods in complete_intake.
    factory.random.reseed_random(1)
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )

    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()

    # Add fineos_absence_id so it behaves like it was submitted but failed to complete intake
    application.claim = claim

    assert not application.submitted_time

    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    response_body = response.json()

    assert response.status_code == 201
    assert not response_body.get("errors")
    assert not response_body.get("warnings")
    # Just verify that it was marked as submitted
    assert response_body.get("data").get("status") == ApplicationStatus.Submitted.value

    capture = massgov.pfml.fineos.mock_client.get_capture()
    # This is generated randomly and changes each time.
    fineos_user_id = capture[2][1]
    # Capture contains a find_employer call and the complete_intake call
    assert capture == [
        ("read_employer", None, {"employer_fein": application.employer_fein}),
        (
            "register_api_user",
            None,
            {
                "employee_registration": massgov.pfml.fineos.models.EmployeeRegistration(
                    user_id=fineos_user_id,
                    customer_number=None,
                    employer_id=str(
                        massgov.pfml.fineos.mock.field.fake_customer_no(application.employer_fein)
                    ),
                    date_of_birth=datetime.date(1753, 1, 1),
                    email=None,
                    first_name=None,
                    last_name=None,
                    national_insurance_no=application.tax_identifier.tax_identifier,
                )
            },
        ),
        ("complete_intake", fineos_user_id, {"notification_case_id": "NTN-1989"}),
    ]


def test_application_post_submit_caring_leave_app_before_july(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2021, 6, 30), end_date=datetime.date(2022, 1, 30)
        )
    ]
    application.has_continuous_leave_periods = True
    application.leave_reason_id = LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id

    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    response_body = response.json()

    assert response.status_code == 400

    errors = response_body.get("errors")

    assert {
        "message": "Caring leave start_date cannot be before 2021-07-01",
        "rule": "disallow_caring_leave_before_july",
        "type": "",
    } in errors


@freeze_time("2020-01-01")
def test_application_post_submit_app_more_than_60_days_ahead(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2022, 1, 1), end_date=datetime.date(2022, 1, 30)
        )
    ]
    application.has_continuous_leave_periods = True

    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    response_body = response.json()

    assert response.status_code == 400

    errors = response_body.get("errors")

    assert {
        "message": "Can't submit application more than 60 days in advance of the earliest leave period",
        "rule": "disallow_submit_over_60_days_before_start_date",
        "type": "",
    } in errors


@freeze_time("2021-07-20")
def test_application_post_submit_app_logs_issues(client, user, auth_token, test_db_session, caplog):
    import logging  # noqa: B1

    caplog.set_level(logging.INFO)  # noqa: B1

    by_start_date = datetime.date(2020, 9, 20)  # 2020-09-20
    by_end_date = by_start_date + datetime.timedelta(weeks=52)  # 2021-9-19
    leave_start_date = by_end_date - datetime.timedelta(days=20)  # 2021-8-30
    leave_end_date = leave_start_date + datetime.timedelta(days=61)  # 2022-12-20

    factory.random.reseed_random(1)
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    BenefitYearFactory.create(start_date=by_start_date, end_date=by_end_date, employee=employee)
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=leave_start_date, end_date=leave_end_date)
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()

    assert not application.submitted_time

    test_db_session.commit()

    client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    for record in caplog.records:
        if record.message == "split_application was submitted: False":
            assert "Can't submit application more than 60 days" in record.issue
            break


def test_application_post_submit_fineos_forbidden(client, fineos_user, fineos_user_token):
    application = ApplicationFactory.create(user=fineos_user)
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {fineos_user_token}"},
    )

    assert response.status_code == 403


def test_application_post_submit_ssn_fraud_error(
    enable_application_fraud_check,
    client,
    user,
    consented_user,
    employer_user,
    auth_token,
    test_db_session,
):
    # This tests the case where an application is submitted, but another application
    # with the same SSN but different user ids exists. These need to be handled
    # in the call center as they may be cases of fraud.

    # consented_user will have a different IDs, create another app with it
    assert user.auth_id != consented_user.auth_id
    assert user.auth_id != employer_user.auth_id

    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    ApplicationFactory.create(user=employer_user, tax_identifier=application.tax_identifier)
    ApplicationFactory.create(
        user=consented_user,
        tax_identifier=application.tax_identifier,
        submitted_time=datetime_util.utcnow(),
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()

    test_db_session.commit()

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    tests.api.validate_error_response(
        response,
        403,
        message="Application unable to be submitted by current user",
        errors=[
            {
                "message": "Request by current user not allowed",
                "rule": "disallow_attempts",
                "type": "",
            }
        ],
    )


def test_application_post_submit_ssn_fraud_not_submitted_okay(
    enable_application_fraud_check,
    client,
    user,
    consented_user,
    employer_user,
    auth_token,
    test_db_session,
):
    # This tests the case where an application is submitted, but another application
    # with the same SSN but different user ids exists. These need to be handled
    # in the call center as they may be cases of fraud.

    # consented_user will have a different IDs, create another app with it
    assert user.auth_id != consented_user.auth_id
    assert user.auth_id != employer_user.auth_id

    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    ApplicationFactory.create(user=employer_user, tax_identifier=application.tax_identifier)
    ApplicationFactory.create(user=consented_user, tax_identifier=application.tax_identifier)
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()

    test_db_session.commit()

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    response_body = response.json()
    assert response.status_code == 201
    assert not response_body.get("errors")
    assert not response_body.get("warnings")


def test_application_post_submit_ssn_second_app(
    enable_application_fraud_check, client, user, auth_token, test_db_session
):
    # This tests the case where an application is submitted, but another application
    # with the same SSN and same user exists.
    # This is in contrast to test_application_post_submit_ssn_fraud_error above
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    ApplicationFactory.create(user=user, tax_identifier=application.tax_identifier)
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()

    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    response_body = response.json()
    assert response.status_code == 201
    assert not response_body.get("errors")
    assert not response_body.get("warnings")


def test_application_post_submit_app_fein_not_found(client, user, auth_token):
    # Assert that API returns a validation error when the application
    # includes an EIN that doesn't match an Employer record
    application = ApplicationFactory.create(user=user, employer_fein="*********")

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    response_body = response.json()
    errors = response_body.get("errors")

    assert {
        "rule": IssueRule.require_contributing_employer,
        "message": "Confirm that you have the correct EIN, and that the Employer is contributing to Paid Family and Medical Leave.",
        "type": IssueType.pfml,
    } in errors


def test_application_post_submit_app_ssn_not_found(client, user, auth_token, test_db_session):
    # An FEIN of ********* is simulated as not found in MockFINEOSClient.
    employer = EmployerFactory.create()
    tax_identifier = TaxIdentifierFactory.create(tax_identifier="*********")
    application = ApplicationFactory.create(
        user=user, tax_identifier=tax_identifier, employer_fein=employer.employer_fein
    )

    test_db_session.commit()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    response_body = response.json()

    assert {
        "message": "Couldn't find Employee in our system. Confirm that you have the correct EIN.",
        "rule": "require_employee",
        "type": "",
    } in response_body.get("errors")
    assert not response_body.get("warnings")
    # Simplified check to confirm Application was included in response:
    assert response_body.get("data").get("application_id") == str(application.application_id)
    assert not response_body.get("data").get("fineos_absence_id")
    assert response_body.get("data").get("status") == ApplicationStatus.Started.value


@mock.patch(
    "massgov.pfml.fineos.mock_client.MockFINEOSClient.get_customer_occupations_customer_api"
)
@freeze_time("2022-12-01")
def test_application_post_submit_existing_work_pattern(
    mock_get_customer_occupations, client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.hours_worked_per_week = 70
    application.first_name = "First"
    application.middle_name = "Middle"
    application.last_name = "Last"
    application.date_of_birth = datetime.date(1977, 7, 27)
    application.employer_notified = True
    application.employer_notification_date = datetime.date(2021, 1, 7)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    application.has_continuous_leave_periods = True

    mock_get_customer_occupations.return_value = [
        massgov.pfml.fineos.models.customer_api.ReadCustomerOccupation(
            occupationId=12345, hoursWorkedPerWeek=37.5, workPatternBasis="Week Based"
        )
    ]

    # set fineos user id in DB to avoid autogenerated id
    fineos_user_id = "USER_WITH_EXISTING_WORK_PATTERN"
    fineos_web_id_ext = FINEOSWebIdExt()
    fineos_web_id_ext.employee_tax_identifier = application.tax_identifier.tax_identifier
    fineos_web_id_ext.employer_fein = application.employer_fein
    fineos_web_id_ext.fineos_web_id = fineos_user_id
    test_db_session.add(fineos_web_id_ext)

    test_db_session.commit()
    massgov.pfml.fineos.mock_client.start_capture()

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    response_body = response.json()
    status = response_body.get("data").get("status")
    assert status == ApplicationStatus.Submitted.value

    capture = massgov.pfml.fineos.mock_client.get_capture()

    assert (
        "update_week_based_work_pattern",
        fineos_user_id,
        {
            "week_based_work_pattern": massgov.pfml.fineos.models.customer_api.WeekBasedWorkPattern(
                workPatternType="Fixed",
                workWeekStarts="Sunday",
                patternStartDate=None,
                patternStatus=None,
                workPatternDays=[
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Sunday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Monday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Tuesday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Wednesday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Thursday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Friday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Saturday", weekNumber=1, hours=8, minutes=15
                    ),
                ],
            )
        },
    ) in capture

    assert capture[-1] == (
        "complete_intake",
        "USER_WITH_EXISTING_WORK_PATTERN",
        {"notification_case_id": "NTN-259"},
    )


@freeze_time("2022-12-01")
def test_application_post_submit_to_fineos(client, user, auth_token, test_db_session):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.first_name = "First"
    application.middle_name = "Middle"
    application.last_name = "Last"
    application.date_of_birth = datetime.date(1977, 7, 27)
    application.mass_id = "*********"
    application.hours_worked_per_week = 70
    application.employer_notified = True
    application.phone = Phone(phone_number="+***********", phone_type_id=1, fineos_phone_id=111)
    application.employer_notification_date = datetime.date(2021, 1, 7)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.gender_id = Gender.WOMAN.gender_id
    application.work_pattern = WorkPatternFixedFactory.create()
    application.has_continuous_leave_periods = True

    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2021, 2, 9),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)

    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()

    language_enum = massgov.pfml.fineos.models.customer_api.LanguageEnum(instanceValue="English")
    language = massgov.pfml.fineos.models.customer_api.Language(
        languageEnum=language_enum, preferredLanguage=True, written=True
    )

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    response_body = response.json()
    status = response_body.get("data").get("status")
    assert status == ApplicationStatus.Submitted.value

    capture = massgov.pfml.fineos.mock_client.get_capture()

    # This is generated randomly and changes each time.
    fineos_user_id = capture[2][1]
    assert ("read_employer", None, {"employer_fein": application.employer_fein}) in capture
    assert (
        "register_api_user",
        None,
        {
            "employee_registration": massgov.pfml.fineos.models.EmployeeRegistration(
                user_id=fineos_user_id,
                employer_id=str(
                    massgov.pfml.fineos.mock.field.fake_customer_no(application.employer_fein)
                ),
                date_of_birth=datetime.date(1753, 1, 1),
                national_insurance_no=application.tax_identifier.tax_identifier,
            )
        },
    ) in capture
    assert (
        "update_customer_details",
        fineos_user_id,
        {
            "customer": massgov.pfml.fineos.models.customer_api.Customer(
                firstName="First",
                lastName="Last",
                secondName="Middle",
                dateOfBirth=datetime.date(1977, 7, 27),
                idNumber=application.tax_identifier.tax_identifier,
                customerAddress=massgov.pfml.fineos.models.customer_api.CustomerAddress(
                    address=massgov.pfml.fineos.models.customer_api.Address(
                        addressLine1=application.residential_address.address_line_one,
                        addressLine2=application.residential_address.address_line_two,
                        addressLine4=application.residential_address.city,
                        addressLine6=application.residential_address.geo_state.geo_state_description,
                        postCode=application.residential_address.zip_code,
                        country="USA",
                    )
                ),
                gender="Female",
                classExtensionInformation=[
                    massgov.pfml.fineos.models.customer_api.ExtensionAttribute(
                        name="MassachusettsID", stringValue=application.mass_id
                    ),
                    massgov.pfml.fineos.models.customer_api.ExtensionAttribute(
                        name="Confirmed", booleanValue=True
                    ),
                    massgov.pfml.fineos.models.customer_api.ExtensionAttribute(
                        name="ConsenttoShareData", booleanValue=False
                    ),
                ],
                languages=[language],
            )
        },
    ) in capture
    assert (
        "get_customer_occupations_customer_api",
        fineos_user_id,
        {},
    ) in capture
    assert (
        "add_week_based_work_pattern",
        fineos_user_id,
        {
            "week_based_work_pattern": massgov.pfml.fineos.models.customer_api.WeekBasedWorkPattern(
                workPatternType="Fixed",
                workWeekStarts="Sunday",
                patternStartDate=None,
                patternStatus=None,
                workPatternDays=[
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Sunday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Monday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Tuesday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Wednesday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Thursday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Friday", weekNumber=1, hours=8, minutes=15
                    ),
                    massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                        dayOfWeek="Saturday", weekNumber=1, hours=8, minutes=15
                    ),
                ],
            )
        },
    ) in capture
    assert (
        "update_occupation",
        None,
        {
            "employment_status": "Terminated",
            "fineos_org_unit_id": None,
            "hours_worked_per_week": 70,
            "occupation_id": 12345,
            "worksite_id": None,
        },
    ) in capture
    assert (
        "update_customer_contact_details",
        fineos_user_id,
        {
            "contact_details": massgov.pfml.fineos.models.customer_api.ContactDetails(
                phoneNumbers=[
                    massgov.pfml.fineos.models.customer_api.PhoneNumber(
                        id=111,
                        preferred=None,
                        phoneNumberType="Cell",
                        intCode="1",
                        areaCode="240",
                        telephoneNo="4879945",
                        classExtensionInformation=None,
                    )
                ],
                emailAddresses=[
                    massgov.pfml.fineos.models.customer_api.EmailAddress(
                        emailAddress=application.user.email_address, emailAddressType="Email"
                    )
                ],
            )
        },
    ) in capture
    assert (
        "start_absence",
        fineos_user_id,
        {
            "absence_case": massgov.pfml.fineos.models.customer_api.AbsenceCase(
                additionalComments="PFML API " + str(application.application_id),
                intakeSource="Self-Service",
                notifiedBy="Employee",
                reason="Serious Health Condition - Employee",
                reasonQualifier1="Not Work Related",
                reasonQualifier2="Sickness",
                notificationReason="Accident or treatment required for an injury",
                primaryRelationship=None,
                primaryRelQualifier1=None,
                primaryRelQualifier2=None,
                timeOffLeavePeriods=[
                    massgov.pfml.fineos.models.customer_api.TimeOffLeavePeriod(
                        startDate=datetime.date(2021, 1, 1),
                        endDate=datetime.date(2021, 2, 9),
                        startDateFullDay=True,
                        endDateFullDay=True,
                        status="known",
                    )
                ],
                employerNotified=True,
                employerNotificationDate=datetime.date(2021, 1, 7),
                employerNotificationMethod=None,
            )
        },
    ) in capture
    assert ("complete_intake", fineos_user_id, {"notification_case_id": "NTN-259"}) in capture


@freeze_time("2022-12-01")
def test_application_post_submit_to_fineos_intermittent_leave(
    client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 70
    application.employer_notified = True
    application.employer_notification_date = datetime.date(2021, 1, 7)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.work_pattern = WorkPatternFixedFactory.create()
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()

    leave_period = IntermittentLeavePeriodFactory.create(
        application_id=application.application_id,
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2021, 3, 2),
        duration=3,
        duration_basis=DurationBasis.days.value,
        frequency=6,
        frequency_interval=2,
        frequency_interval_basis=FrequencyIntervalBasis.months.value,
    )

    application.intermittent_leave_periods = [leave_period]
    application.has_intermittent_leave_periods = True

    test_db_session.add(application)

    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    response_body = response.json()
    status = response_body.get("data").get("status")
    assert status == ApplicationStatus.Submitted.value

    capture = massgov.pfml.fineos.mock_client.get_capture()

    assert capture[8][2]["absence_case"].episodicLeavePeriods == [
        massgov.pfml.fineos.models.customer_api.EpisodicLeavePeriod(
            startDate=datetime.date(2021, 1, 1),
            endDate=datetime.date(2021, 3, 2),
            duration=3,
            durationBasis="Days",
            frequency=6,
            frequencyInterval=2,
            frequencyIntervalBasis="Months",
        )
    ]


@freeze_time("2022-12-01")
def test_application_post_submit_to_fineos_reduced_schedule_leave(
    client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    application.hours_worked_per_week = 70
    application.employer_notified = True
    application.employer_notification_date = datetime.date(2021, 1, 7)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.work_pattern = WorkPatternFixedFactory.create()
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()

    leave_period = ReducedScheduleLeavePeriodFactory.create(
        application_id=application.application_id,
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2021, 2, 9),
        thursday_off_minutes=240 + 45,
        friday_off_minutes=480,
        saturday_off_minutes=0,
        sunday_off_minutes=45,
        monday_off_minutes=240 + 15,
        tuesday_off_minutes=60,
        wednesday_off_minutes=240 + 30,
    )
    application.reduced_schedule_leave_periods = [leave_period]
    application.has_reduced_schedule_leave_periods = True

    test_db_session.add(application)
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    response_body = response.json()
    status = response_body.get("data").get("status")
    assert status == ApplicationStatus.Submitted.value

    capture = massgov.pfml.fineos.mock_client.get_capture()

    assert capture[8][2]["absence_case"].reducedScheduleLeavePeriods == [
        massgov.pfml.fineos.models.customer_api.ReducedScheduleLeavePeriod(
            startDate=datetime.date(2021, 1, 1),
            endDate=datetime.date(2021, 2, 9),
            status="known",
            mondayOffHours=4,
            mondayOffMinutes=15,
            tuesdayOffHours=1,
            tuesdayOffMinutes=0,
            wednesdayOffHours=4,
            wednesdayOffMinutes=30,
            thursdayOffHours=4,
            thursdayOffMinutes=45,
            fridayOffHours=8,
            fridayOffMinutes=0,
            saturdayOffHours=0,
            saturdayOffMinutes=0,
            sundayOffHours=0,
            sundayOffMinutes=45,
        )
    ]


def test_application_post_submit_to_fineos_bonding_adoption(
    client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 70
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2021, 2, 9),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)

    # Reason = Child Bonding
    # Reason Qualifier 1 = Adoption
    application.leave_reason_id = LeaveReason.CHILD_BONDING.leave_reason_id
    application.leave_reason_qualifier_id = LeaveReasonQualifier.ADOPTION.leave_reason_qualifier_id

    application.child_placement_date = datetime.date(2021, 1, 15)
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 201

    capture = massgov.pfml.fineos.mock_client.get_capture()
    captured_absence_case = capture[8][2]["absence_case"]

    assert captured_absence_case.reason == LeaveReason.CHILD_BONDING.leave_reason_description
    assert (
        captured_absence_case.reasonQualifier1
        == LeaveReasonQualifier.ADOPTION.leave_reason_qualifier_description
    )
    assert captured_absence_case.reasonQualifier2 is None
    assert (
        captured_absence_case.primaryRelationship
        == RelationshipToCaregiver.CHILD.relationship_to_caregiver_description
    )
    assert (
        captured_absence_case.primaryRelQualifier1
        == RelationshipQualifier.ADOPTED.relationship_qualifier_description
    )
    assert captured_absence_case.primaryRelQualifier2 is None


def test_application_post_submit_to_fineos_bonding_foster(
    client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 70
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2021, 2, 9),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)

    # Reason = Child Bonding
    # Reason Qualifier 1 = Foster Care
    application.leave_reason_id = LeaveReason.CHILD_BONDING.leave_reason_id
    application.leave_reason_qualifier_id = (
        LeaveReasonQualifier.FOSTER_CARE.leave_reason_qualifier_id
    )
    application.child_placement_date = datetime.date(2020, 2, 1)
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    capture = massgov.pfml.fineos.mock_client.get_capture()
    captured_absence_case = capture[8][2]["absence_case"]

    assert captured_absence_case.reason == LeaveReason.CHILD_BONDING.leave_reason_description
    assert (
        captured_absence_case.reasonQualifier1
        == LeaveReasonQualifier.FOSTER_CARE.leave_reason_qualifier_description
    )
    assert captured_absence_case.reasonQualifier2 is None
    assert (
        captured_absence_case.primaryRelationship
        == RelationshipToCaregiver.CHILD.relationship_to_caregiver_description
    )
    assert (
        captured_absence_case.primaryRelQualifier1
        == RelationshipQualifier.FOSTER.relationship_qualifier_description
    )
    assert captured_absence_case.primaryRelQualifier2 is None


def test_application_post_submit_to_fineos_bonding_newborn(
    client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 70
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2021, 2, 9),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)

    # Reason = Child Bonding
    # Reason Qualifier 1 = Newborn
    application.leave_reason_id = LeaveReason.CHILD_BONDING.leave_reason_id
    application.leave_reason_qualifier_id = LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id

    application.child_birth_date = datetime.date(2020, 2, 1)
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    capture = massgov.pfml.fineos.mock_client.get_capture()
    captured_absence_case = capture[8][2]["absence_case"]

    assert captured_absence_case.reason == LeaveReason.CHILD_BONDING.leave_reason_description
    assert (
        captured_absence_case.reasonQualifier1
        == LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_description
    )
    assert captured_absence_case.reasonQualifier2 is None
    assert (
        captured_absence_case.primaryRelationship
        == RelationshipToCaregiver.CHILD.relationship_to_caregiver_description
    )
    assert (
        captured_absence_case.primaryRelQualifier1
        == RelationshipQualifier.BIOLOGICAL.relationship_qualifier_description
    )
    assert captured_absence_case.primaryRelQualifier2 is None


def test_application_post_submit_to_fineos_medical(client, user, auth_token, test_db_session):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 70
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2021, 2, 9),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)

    # API input:
    # Reason = Serious Health Condition - Employee
    # Pregnant or Recent Birth = False
    application.leave_reason_id = LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id
    application.pregnant_or_recent_birth = False
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    capture = massgov.pfml.fineos.mock_client.get_capture()
    captured_absence_case = capture[8][2]["absence_case"]

    # Maps to FINEOS:
    # Reason = Serious Health Condition - Employee
    # Reason Qualifier 1 = Not Work Related
    # Reason Qualifier 2 = Sickness
    assert (
        captured_absence_case.reason
        == LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_description
    )
    assert (
        captured_absence_case.reasonQualifier1
        == LeaveReasonQualifier.NOT_WORK_RELATED.leave_reason_qualifier_description
    )
    assert (
        captured_absence_case.reasonQualifier2
        == LeaveReasonQualifier.SICKNESS.leave_reason_qualifier_description
    )
    assert captured_absence_case.primaryRelationship is None
    assert captured_absence_case.primaryRelQualifier1 is None
    assert captured_absence_case.primaryRelQualifier2 is None


def test_application_post_submit_to_fineos_pregnant_true(client, user, auth_token, test_db_session):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 70
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2021, 2, 9),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)

    # API input:
    # Reason = Pregnancy/Maternity
    # Pregnant or Recent Birth = True
    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    application.pregnant_or_recent_birth = True
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    capture = massgov.pfml.fineos.mock_client.get_capture()
    captured_absence_case = capture[8][2]["absence_case"]

    # Maps to FINEOS:
    # Reason = Pregnancy/Maternity
    # Reason Qualifier 1 = Postnatal Disability
    assert captured_absence_case.reason == LeaveReason.PREGNANCY_MATERNITY.leave_reason_description
    assert (
        captured_absence_case.reasonQualifier1
        == LeaveReasonQualifier.POSTNATAL_DISABILITY.leave_reason_qualifier_description
    )
    assert captured_absence_case.reasonQualifier2 is None
    assert captured_absence_case.primaryRelationship is None
    assert captured_absence_case.primaryRelQualifier1 is None
    assert captured_absence_case.primaryRelQualifier2 is None


def test_application_post_submit_to_fineos_pregnant(client, user, auth_token, test_db_session):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 70
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2021, 2, 9),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)

    # API input:
    # Reason = Pregnancy/Maternity
    # Pregnant or Recent Birth = False
    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    application.pregnant_or_recent_birth = False
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 201

    capture = massgov.pfml.fineos.mock_client.get_capture()
    captured_absence_case = capture[8][2]["absence_case"]

    # Maps to FINEOS:
    # Reason = Pregnancy/Maternity
    # Reason Qualifier 1 = Postnatal Disability
    assert captured_absence_case.reason == LeaveReason.PREGNANCY_MATERNITY.leave_reason_description
    assert (
        captured_absence_case.reasonQualifier1
        == LeaveReasonQualifier.POSTNATAL_DISABILITY.leave_reason_qualifier_description
    )
    assert captured_absence_case.reasonQualifier2 is None
    assert captured_absence_case.primaryRelationship is None
    assert captured_absence_case.primaryRelQualifier1 is None
    assert captured_absence_case.primaryRelQualifier2 is None


def test_application_post_submit_app_failure_after_absence_case_creation(
    client, user, auth_token, test_db_session
):
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    # Fill in required fields so this is an valid application apart from the fact it's already been submitted.
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()

    # Attach absence case information application so it appears as if this application has already been submitted.
    application.claim = claim
    application.submitted_time = datetime_util.utcnow()
    test_db_session.commit()

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 403


def test_application_post_submit_creates_other_leaves_v22_eform(client, user, auth_token):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 40
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    application.previous_leaves = [
        PreviousLeaveFactory.create(
            application_id=application.application_id,
            leave_start_date=datetime.date(2020, 1, 1),
            leave_end_date=datetime.date(2020, 2, 1),
        ),
        PreviousLeaveFactory.create(
            application_id=application.application_id,
            leave_start_date=datetime.date(2020, 3, 1),
            leave_end_date=datetime.date(2020, 4, 1),
        ),
    ]

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    captures = massgov.pfml.fineos.mock_client.get_capture()

    assert response.status_code == 201
    filtered = list(filter(lambda cap: cap[0] == "customer_create_eform", captures))
    assert len(filtered) == 1
    create_eform_capture = filtered[0]
    assert create_eform_capture[2]["eform"].eformType == "Other Leaves - current version v2"
    eform_attributes = create_eform_capture[2]["eform"].eformAttributes
    previous_leave_reason = next(
        (attr for attr in eform_attributes if attr["name"] == "V22QualifyingReason2"), None
    )
    assert previous_leave_reason["enumValue"]["instanceValue"] == "Pregnancy"


def test_application_post_submit_no_previous_leaves_does_not_create_other_leaves_eform(
    client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 40
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2021, 1, 1))
    ]
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    captures = massgov.pfml.fineos.mock_client.get_capture()

    assert response.status_code == 201
    assert "customer_create_eform" not in map(lambda capture: capture[0], captures)


def test_application_post_submit_creates_other_incomes_eform(
    client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 40
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2021, 1, 1), end_date=datetime.date(2021, 7, 1)
        )
    ]
    application.employer_benefits = [
        EmployerBenefitFactory.create(application_id=application.application_id)
    ]

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    captures = massgov.pfml.fineos.mock_client.get_capture()

    assert response.status_code == 201
    filtered = list(filter(lambda cap: cap[0] == "customer_create_eform", captures))
    assert len(filtered) == 1
    create_eform_capture = filtered[0]
    assert create_eform_capture[2]["eform"].eformType == "Other Income - current version v2"


def test_application_post_submit_creates_other_incomes_v22_eform(client, user, auth_token):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 40
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2021, 1, 1), end_date=datetime.date(2021, 4, 1)
        )
    ]
    application.employer_benefits = [
        EmployerBenefitFactory.create(application_id=application.application_id)
    ]

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    captures = massgov.pfml.fineos.mock_client.get_capture()

    assert response.status_code == 201
    filtered = list(filter(lambda cap: cap[0] == "customer_create_eform", captures))
    assert len(filtered) == 1
    create_eform_capture = filtered[0]
    assert create_eform_capture[2]["eform"].eformType == "Other Income - current version v2"

    eform_attributes = create_eform_capture[2]["eform"].eformAttributes
    assert {
        "name": "V22StartDate1",
        "dateValue": application.employer_benefits[0].benefit_start_date.isoformat(),
    } in eform_attributes


def test_application_post_submit_no_benefits_or_incomes_does_not_create_other_incomes_eform(
    client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 40
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2021, 1, 1), end_date=datetime.date(2021, 4, 1)
        )
    ]
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()
    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    captures = massgov.pfml.fineos.mock_client.get_capture()

    assert response.status_code == 201
    assert "customer_create_eform" not in map(lambda capture: capture[0], captures)


def test_application_post_submit_to_fineos_caring_leave(client, user, auth_token, test_db_session):
    caring_leave_metadata = CaringLeaveMetadataFactory.create(
        relationship_to_caregiver_id=RelationshipToCaregiver.SPOUSE.relationship_to_caregiver_id
    )
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
        caring_leave_metadata=caring_leave_metadata,
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)
    application.hours_worked_per_week = 40
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2021, 7, 1),
        end_date=datetime.date(2021, 8, 9),
        application_id=application.application_id,
    )
    test_db_session.add(leave_period)

    application.leave_reason_id = LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id
    test_db_session.commit()

    massgov.pfml.fineos.mock_client.start_capture()

    response = client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    capture = massgov.pfml.fineos.mock_client.get_capture()
    captured_absence_case = capture[8][2]["absence_case"]

    assert response.status_code == 201
    assert (
        captured_absence_case.reason
        == LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_description
    )
    assert (
        captured_absence_case.reasonQualifier1
        == LeaveReasonQualifier.SERIOUS_HEALTH_CONDITION.leave_reason_qualifier_description
    )
    assert (
        captured_absence_case.notificationReason
        == LeaveNotificationReason.CARING_FOR_A_FAMILY_MEMBER
    )
    assert (
        captured_absence_case.primaryRelationship
        == RelationshipToCaregiver.SPOUSE.relationship_to_caregiver_description
    )
    assert (
        captured_absence_case.primaryRelQualifier1
        == RelationshipQualifier.LEGALLY_MARRIED.relationship_qualifier_description
    )
    assert (
        captured_absence_case.primaryRelQualifier2
        == RelationshipQualifier.UNDISCLOSED.relationship_qualifier_description
    )


def test_application_previous_pfml_one_leave_period(client, user, auth_token, test_db_session):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()

    claim = ClaimFactory.create(
        employer=employer,
        fineos_notification_id="NTN-1989",
        fineos_absence_id="NTN-1989-ABS-01",
        employee=employee,
    )
    application = ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
        claim=claim,
    )
    leave_period = ContinuousLeavePeriodFactory.create(
        start_date=datetime.date(2023, 3, 1),
        end_date=datetime.date(2023, 3, 15),
        application_id=application.application_id,
    )

    application.leave_periods = leave_period

    previous_claim = ClaimFactory.create(
        employer=employer,
        employee=employee,
        fineos_absence_status_id=AbsenceStatus.APPROVED.absence_status_id,
    )
    absence_period = [
        AbsencePeriodFactory.create(
            modified_start_date=datetime.date(2023, 1, 20),
            modified_end_date=datetime.date(2023, 2, 20),
            absence_period_start_date=datetime.date(2023, 1, 2),
            absence_period_end_date=datetime.date(2023, 2, 20),
            claim=claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        )
    ]

    previous_claim.absence_periods = absence_period
    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200

    response_body = response.json().get("data")
    assert len(response_body.get("previous_pfml_leave_periods")) == 1


def test_application_previous_pfml_multiple_leave_periods(client, user, auth_token):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()

    claim = ClaimFactory.create(
        employer=employer,
        fineos_notification_id="NTN-1989",
        fineos_absence_id="NTN-1989-ABS-01",
        employee=employee,
    )
    application = ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
        claim=claim,
    )
    leave_period = ContinuousLeavePeriodFactory.create(
        start_date=datetime.date(2023, 3, 1),
        end_date=datetime.date(2023, 3, 15),
        application_id=application.application_id,
    )

    application.leave_periods = leave_period

    previous_claim = ClaimFactory.create(
        employer=employer,
        employee=employee,
        fineos_absence_status_id=AbsenceStatus.APPROVED.absence_status_id,
    )
    absence_periods = [
        AbsencePeriodFactory.create(
            modified_start_date=datetime.date(2023, 1, 20),
            modified_end_date=datetime.date(2023, 2, 20),
            absence_period_start_date=datetime.date(2023, 1, 2),
            absence_period_end_date=datetime.date(2023, 2, 20),
            claim=previous_claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        ),
        AbsencePeriodFactory.create(
            modified_start_date=datetime.date(2023, 1, 1),
            modified_end_date=datetime.date(2023, 1, 7),
            absence_period_start_date=datetime.date(2022, 12, 20),
            absence_period_end_date=datetime.date(2022, 12, 30),
            claim=previous_claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        ),
    ]

    previous_claim.absence_periods = absence_periods

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200

    response_body = response.json().get("data")
    assert len(response_body.get("previous_pfml_leave_periods")) == 2


def test_application_previous_pfml_multiple_leave_periods_different_employers(
    client, user, auth_token
):
    employer = EmployerFactory.create()
    employer2 = EmployerFactory.create()
    employee = EmployeeFactory.create()

    application = ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
    )
    leave_period = ContinuousLeavePeriodFactory.create(
        start_date=datetime.date(2023, 3, 1),
        end_date=datetime.date(2023, 3, 15),
        application_id=application.application_id,
    )
    previous_claim = ClaimFactory.create(
        employer=employer2,
        employee=employee,
        fineos_absence_status_id=AbsenceStatus.APPROVED.absence_status_id,
    )
    absence_periods = [
        AbsencePeriodFactory.create(
            modified_start_date=datetime.date(2023, 1, 20),
            modified_end_date=datetime.date(2023, 2, 20),
            absence_period_start_date=datetime.date(2023, 1, 2),
            absence_period_end_date=datetime.date(2023, 2, 20),
            claim=previous_claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        ),
        AbsencePeriodFactory.create(
            modified_start_date=datetime.date(2023, 1, 1),
            modified_end_date=datetime.date(2023, 1, 7),
            absence_period_start_date=datetime.date(2022, 12, 20),
            absence_period_end_date=datetime.date(2022, 12, 30),
            claim=previous_claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        ),
    ]

    application.continuous_leave_periods = [leave_period]
    previous_claim.absence_periods = absence_periods

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200

    response_body = response.json().get("data")
    assert len(response_body.get("previous_pfml_leave_periods")) == 2


@freeze_time("2023-11-01")
def test_application_get_previous_pfml_dates(client, user, auth_token):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()

    application = ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
    )
    leave_period = ContinuousLeavePeriodFactory.create(
        start_date=datetime.date(2023, 9, 1),
        end_date=datetime.date(2023, 9, 15),
        application_id=application.application_id,
    )

    application.leave_periods = leave_period

    previous_claim = ClaimFactory.create(
        employer=employer,
        employee=employee,
        fineos_absence_status_id=AbsenceStatus.APPROVED.absence_status_id,
    )
    absence_period = [
        AbsencePeriodFactory.create(
            modified_start_date=datetime.date(2023, 8, 1),
            modified_end_date=datetime.date(2023, 8, 15),
            absence_period_start_date=datetime.date(2023, 8, 1),
            absence_period_end_date=datetime.date(2023, 8, 15),
            claim=previous_claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        ),
    ]

    previous_claim.absence_periods = absence_period

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    assert len(response.json()["data"]["previous_pfml_leave_periods"]) == 1
    assert response.json()["data"]["previous_pfml_leave_periods"][0]["start_date"] == "2023-08-01"
    assert response.json()["data"]["previous_pfml_leave_periods"][0]["end_date"] == "2023-08-15"


@freeze_time("2023-11-01")
def test_application_get_previous_pfml_dates_within_year(client, user, auth_token, test_db_session):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()

    application = ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
    )

    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2023, 10, 1),
        end_date=datetime.date(2023, 10, 15),
        application_id=application.application_id,
    )

    previous_claim_within_year = ClaimFactory.create(
        employer=employer,
        employee=employee,
        fineos_absence_status_id=AbsenceStatus.APPROVED.absence_status_id,
    )

    previous_claim_not_within_year = ClaimFactory.create(
        employer=employer,
        employee=employee,
        fineos_absence_status_id=AbsenceStatus.APPROVED.absence_status_id,
    )

    absence_period_within_year = [
        AbsencePeriodFactory.create(
            modified_start_date=datetime.date(2023, 9, 1),
            modified_end_date=datetime.date(2023, 9, 15),
            absence_period_start_date=datetime.date(2023, 9, 1),
            absence_period_end_date=datetime.date(2023, 9, 15),
            claim=previous_claim_within_year,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        ),
    ]
    absence_period_not_within_year = [
        AbsencePeriodFactory.create(
            modified_start_date=datetime.date(2022, 9, 1),
            modified_end_date=datetime.date(2022, 9, 15),
            absence_period_start_date=datetime.date(2022, 9, 1),
            absence_period_end_date=datetime.date(2022, 9, 15),
            claim=previous_claim_not_within_year,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        ),
    ]

    test_db_session.add(leave_period)
    test_db_session.commit()

    previous_claim_within_year.absence_periods = absence_period_within_year
    previous_claim_not_within_year.absence_periods = absence_period_not_within_year

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    assert len(response.json()["data"]["previous_pfml_leave_periods"]) == 1
    assert response.json()["data"]["previous_pfml_leave_periods"][0]["start_date"] == "2023-09-01"
    assert response.json()["data"]["previous_pfml_leave_periods"][0]["end_date"] == "2023-09-15"


@freeze_time("2023-11-01")
def test_application_get_previous_pfml_dates_overlap_new_previous_leave(
    client, user, auth_token, test_db_session
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()

    application = ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
    )
    leave_period = ContinuousLeavePeriod(
        start_date=datetime.date(2023, 10, 1),
        end_date=datetime.date(2023, 10, 15),
        application_id=application.application_id,
    )
    previous_claim = ClaimFactory.create(
        employer=employer,
        employee=employee,
        fineos_absence_status_id=AbsenceStatus.APPROVED.absence_status_id,
    )
    absence_period = [
        AbsencePeriodFactory.create(
            modified_start_date=datetime.date(2023, 9, 1),
            modified_end_date=datetime.date(2023, 9, 15),
            absence_period_start_date=datetime.date(2023, 9, 1),
            absence_period_end_date=datetime.date(2023, 9, 15),
            claim=previous_claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        ),
    ]

    test_db_session.add(leave_period)
    test_db_session.commit()

    previous_claim.absence_periods = absence_period

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    assert len(response.json()["data"]["previous_pfml_leave_periods"]) == 1
    assert response.json()["data"]["previous_pfml_leave_periods"][0]["start_date"] == "2023-09-01"
    assert response.json()["data"]["previous_pfml_leave_periods"][0]["end_date"] == "2023-09-15"

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={
            "Authorization": f"Bearer {auth_token}",
        },
        json={
            "previous_leaves": [
                {
                    "is_for_current_employer": True,
                    "leave_start_date": "2023-09-02",
                    "leave_end_date": "2023-09-10",
                    "leave_reason": "Pregnancy",
                    "worked_per_week_minutes": 20,
                    "leave_minutes": 10,
                    "is_continuous": True,
                },
            ],
        },
    )

    assert response.status_code == 200

    conflicting_leave_warning = {
        "field": "previous_leaves[0].leave_start_date",
        "message": "Previous leaves cannot overlap with leave periods from other applications.",
        "rule": "disallow_overlapping_leave_period_from_other_applications",
        "type": "conflicting",
    }
    assert conflicting_leave_warning in response.json()["warnings"]


def test_application_get_all_previous_leave_types(client, user, auth_token, test_db_session):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()

    application = ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
    )

    application.previous_leaves = [
        PreviousLeaveFactory.create(application_id=application.application_id, type="any_reason"),
        PreviousLeaveFactory.create(application_id=application.application_id, type="other_reason"),
        PreviousLeaveFactory.create(application_id=application.application_id, type="other_reason"),
        PreviousLeaveFactory.create(application_id=application.application_id, type="same_reason"),
        PreviousLeaveFactory.create(application_id=application.application_id, type="same_reason"),
        PreviousLeaveFactory.create(application_id=application.application_id, type="same_reason"),
    ]

    test_db_session.add(application)
    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    assert len(response.json()["data"]["previous_leaves"]) == 6


@patch("massgov.pfml.api.applications.is_payment_preference_set_to_prepaid_card", return_value=True)
@patch(
    "massgov.pfml.api.applications.error_response_if_invalid_benefit_year_issue", return_value=None
)
@patch("massgov.pfml.api.applications.ensure", return_value=True)
@patch("massgov.pfml.api.app.get_features_config")
def test_application_with_prepaid_card_payment_preference(
    mock_get_features_config,
    mock_ensure,
    mock_benefit_year_issue_check,
    mock_payment_preference,
    client,
    user,
    auth_token,
    test_db_session,
):
    mock_get_features_config.return_value.mark_applications_ready_for_review.enable_mark_applications_ready_for_review = (
        False
    )

    employee = EmployeeFactory.create()

    application = ApplicationFactory.create(user=user)
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )
    application = ApplicationFactory.create(tax_identifier=employee.tax_identifier, user=user)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.claim = claim
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2021, 1, 1), end_date=datetime.date(2021, 4, 1)
        )
    ]
    application.has_continuous_leave_periods = True
    application.is_withholding_tax = True
    application.has_submitted_payment_preference = True
    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    DocumentFactory.create(
        user_id=user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
    )

    test_db_session.commit()

    mock_db_session = MagicMock()
    mock_db_session.__enter__.return_value = test_db_session
    mock_db_session.__exit__.return_value = None

    with patch("massgov.pfml.api.applications.app.db_session", return_value=mock_db_session):
        with patch(
            "massgov.pfml.api.applications.os.environ",
            {"ENABLE_PREPAID_IMPACT_PAYMENTS": "1", "ENVIRONMENT": "local"},
        ):
            applications_complete(application.application_id, None)

    prepaid_registration = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=application.employee_id,
        )
        .first()
    )

    assert prepaid_registration.employee_id == application.employee_id
    assert (
        prepaid_registration.prepaid_registration_status_id
        == PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id
    )


@patch("massgov.pfml.api.applications.is_payment_preference_set_to_prepaid_card", return_value=True)
@patch(
    "massgov.pfml.api.applications.error_response_if_invalid_benefit_year_issue", return_value=None
)
@patch("massgov.pfml.api.applications.ensure", return_value=True)
@patch("massgov.pfml.api.app.get_features_config")
def test_application_with_prepaid_card_payment_preference_with_existing_registration(
    mock_get_features_config,
    mock_ensure,
    mock_benefit_year_issue_check,
    mock_payment_preference,
    client,
    user,
    auth_token,
    test_db_session,
):
    mock_get_features_config.return_value.mark_applications_ready_for_review.enable_mark_applications_ready_for_review = (
        False
    )

    employee = EmployeeFactory.create()

    application = ApplicationFactory.create(user=user)
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )
    application = ApplicationFactory.create(tax_identifier=employee.tax_identifier, user=user)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.claim = claim
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2021, 1, 1), end_date=datetime.date(2021, 4, 1)
        )
    ]
    application.has_continuous_leave_periods = True
    application.is_withholding_tax = True
    application.has_submitted_payment_preference = True
    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    DocumentFactory.create(
        user_id=user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
    )

    test_db_session.commit()

    mock_db_session = MagicMock()
    mock_db_session.__enter__.return_value = test_db_session
    mock_db_session.__exit__.return_value = None

    claimant_prepaid_registration = ClaimantPrepaidRegistration(
        employee_id=application.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.ERROR.prepaid_registration_status_id,
    )
    test_db_session.add(claimant_prepaid_registration)
    test_db_session.commit()

    prepaid_registration = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=application.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration.prepaid_registration_status_id
        == PrepaidRegistrationStatus.ERROR.prepaid_registration_status_id
    )

    with patch("massgov.pfml.api.applications.app.db_session", return_value=mock_db_session):
        with patch(
            "massgov.pfml.api.applications.os.environ",
            {"ENABLE_PREPAID_IMPACT_PAYMENTS": "1", "ENVIRONMENT": "local"},
        ):
            applications_complete(application.application_id, None)

    prepaid_registration = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=application.employee_id,
        )
        .first()
    )

    assert prepaid_registration.employee_id == application.employee_id
    assert (
        prepaid_registration.prepaid_registration_status_id
        == PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id
    )


@patch("massgov.pfml.api.applications.is_payment_preference_set_to_prepaid_card", return_value=True)
@patch(
    "massgov.pfml.api.applications.error_response_if_invalid_benefit_year_issue", return_value=None
)
@patch("massgov.pfml.api.applications.ensure", return_value=True)
@patch("massgov.pfml.api.app.get_features_config")
def test_application_with_prepaid_card_payment_preference_with_existing_active_registration(
    mock_get_features_config,
    mock_ensure,
    mock_benefit_year_issue_check,
    mock_payment_preference,
    client,
    user,
    auth_token,
    test_db_session,
):
    mock_get_features_config.return_value.mark_applications_ready_for_review.enable_mark_applications_ready_for_review = (
        False
    )

    employee = EmployeeFactory.create()

    application = ApplicationFactory.create(user=user)
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )
    application = ApplicationFactory.create(tax_identifier=employee.tax_identifier, user=user)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.claim = claim
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2021, 1, 1), end_date=datetime.date(2021, 4, 1)
        )
    ]
    application.has_continuous_leave_periods = True
    application.is_withholding_tax = True
    application.has_submitted_payment_preference = True
    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    DocumentFactory.create(
        user_id=user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
    )

    test_db_session.commit()

    mock_db_session = MagicMock()
    mock_db_session.__enter__.return_value = test_db_session
    mock_db_session.__exit__.return_value = None

    claimant_prepaid_registration = ClaimantPrepaidRegistration(
        employee_id=application.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id,
        account_number="*********",
        routing_number="*********",
    )
    test_db_session.add(claimant_prepaid_registration)
    test_db_session.commit()

    prepaid_registration = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=application.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration.prepaid_registration_status_id
        == PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id
    )

    with patch("massgov.pfml.api.applications.app.db_session", return_value=mock_db_session):
        with patch(
            "massgov.pfml.api.applications.os.environ",
            {"ENABLE_PREPAID_IMPACT_PAYMENTS": "1", "ENVIRONMENT": "local"},
        ):
            applications_complete(application.application_id, None)

    prepaid_registration = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=application.employee_id,
        )
        .all()
    )

    assert len(prepaid_registration) == 2
    assert prepaid_registration[0].employee_id == application.employee_id
    assert prepaid_registration[1].employee_id == application.employee_id

    assert prepaid_registration[0].account_number == prepaid_registration[1].account_number
    assert prepaid_registration[0].routing_number == prepaid_registration[1].routing_number

    assert (
        prepaid_registration[0].prepaid_registration_status_id
        != prepaid_registration[1].prepaid_registration_status_id
    )

    assert [3, 7] == sorted(
        [
            prepaid_registration[0].prepaid_registration_status_id,
            prepaid_registration[1].prepaid_registration_status_id,
        ]
    )


def test_application_post_complete_app(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )
    application.tax_identifier = TaxIdentifier(tax_identifier="*********")
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.claim = claim
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2021, 1, 1), end_date=datetime.date(2021, 4, 1)
        )
    ]
    application.has_continuous_leave_periods = True
    application.is_withholding_tax = True
    application.has_submitted_payment_preference = True
    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    DocumentFactory.create(
        user_id=user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
    )

    test_db_session.commit()

    response = client.post(
        "/v1/applications/{}/complete-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    response_body = response.json()

    assert response.status_code == 200
    assert response_body.get("data").get("status") == ApplicationStatus.Completed.value


def test_application_post_complete_app_without_other_leave_fields(
    client, user, auth_token, test_db_session
):
    # TODO (CP-2455): Remove this test when we begin requiring these fields for complete-application
    application = ApplicationFactory.create(
        user=user,
        has_employer_benefits=None,
        has_other_incomes=None,
        has_previous_leaves=None,
        submitted_time=datetime.datetime.now(),
    )
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )
    application.tax_identifier = TaxIdentifier(tax_identifier="*********")
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.claim = claim
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=datetime.date(2021, 1, 1), end_date=datetime.date(2021, 4, 1)
        )
    ]
    application.has_continuous_leave_periods = True
    application.is_withholding_tax = True
    application.has_submitted_payment_preference = True
    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    DocumentFactory.create(
        user_id=user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
    )

    test_db_session.commit()

    response = client.post(
        "/v1/applications/{}/complete-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    response_body = response.json()

    assert response.status_code == 200
    assert response_body.get("data").get("status") == ApplicationStatus.Completed.value


def test_application_complete_mark_document_received_fineos(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(user=user)
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )
    application.tax_identifier = TaxIdentifier(tax_identifier="*********")
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.claim = claim
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            # Dates that pass validation criteria. 55 and 75 are not meaningful values outside
            # of the fact that they are valid. Would work with any valid leave period.
            start_date=(datetime.datetime.now() + relativedelta(days=55)).date(),
            end_date=(datetime.datetime.now() + relativedelta(days=75)).date(),
        )
    ]
    application.has_continuous_leave_periods = True
    application.is_withholding_tax = True
    application.has_submitted_payment_preference = True
    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    DocumentFactory.create(
        user_id=user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
    )

    test_db_session.add(application)

    id_proof = DocumentFactory.create(
        user_id=user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.IDENTIFICATION_PROOF.document_type_id,
        fineos_id="id-proof-dummy-doc-id",
    )
    test_db_session.add(id_proof)

    irrelevant_evidence = DocumentFactory.create(
        user_id=user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.PASSPORT.document_type_id,
        fineos_id="passport-dummy-doc-id",
    )
    test_db_session.add(irrelevant_evidence)

    test_db_session.commit()

    assert not application.completed_time

    massgov.pfml.fineos.mock_client.start_capture()

    with patch(
        "massgov.pfml.api.applications.os.environ",
        {"ENABLE_PREPAID_IMPACT_PAYMENTS": "0", "ENVIRONMENT": "local"},
    ):
        response = client.post(
            "/v1/applications/{}/complete-application".format(application.application_id),
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 200

    capture = massgov.pfml.fineos.mock_client.get_capture()

    # Refresh the db session because the application object was manipulated by another session
    # in the logic we executed as a result of the POST request above.
    test_db_session.refresh(application)
    assert application.completed_time

    client_function_calls = (
        "find_employer",
        "register_api_user",
        "mark_document_as_received",
        "get_outstanding_evidence",
    )
    for i in range(len(capture)):
        assert capture[i][0] == client_function_calls[i]

    assert (
        len(capture) == 4
    ), "Did not make 4 requests to FINEOS API. Confirm that we did not attempt to mark_document_as_received for irrelevant_evidence"

    # Function name
    assert capture[2][0] == "mark_document_as_received"

    # Arguments we pass to the function
    assert capture[2][2] == {
        "absence_id": application.claim.fineos_absence_id,
        "fineos_document_id": id_proof.fineos_id,
    }


def test_application_patch_null_benefits(
    client, user, auth_token, test_db_session, initialize_factories_session
):
    # employer_benefits
    application = ApplicationFactory.create(user=user, updated_at=datetime.datetime.now())
    EmployerBenefitFactory.create(application_id=application.application_id)

    update_request_body = {"employer_benefits": None}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    response_body = response.json().get("data")

    assert response.status_code == 200
    assert len(response_body.get("previous_pfml_leave_periods")) == 0
    assert len(response_body.get("employer_benefits")) == 0
    assert len(application.employer_benefits) == 0


def test_application_patch_null_other_incomes(
    client, user, auth_token, test_db_session, initialize_factories_session
):
    application = ApplicationFactory.create(user=user, updated_at=datetime.datetime.now())
    # other_incomes
    OtherIncomeFactory.create(application_id=application.application_id)

    update_request_body = {"other_incomes": None}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    response_body = response.json().get("data")

    assert response.status_code == 200
    assert len(response_body.get("other_incomes")) == 0
    assert len(application.other_incomes) == 0


def test_application_patch_null_previous_leaves(
    client, user, auth_token, test_db_session, initialize_factories_session
):
    application = ApplicationFactory.create(user=user, updated_at=datetime.datetime.now())
    # previous_leaves
    PreviousLeaveFactory.create(application_id=application.application_id)

    update_request_body = {"previous_leaves": None}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    response_body = response.json().get("data")

    assert response.status_code == 200
    assert len(response_body.get("previous_leaves")) == 0
    assert len(application.previous_leaves) == 0


def test_application_patch_benefits_empty_arrays(
    client, user, auth_token, test_db_session, initialize_factories_session
):
    # employer_benefits
    application = ApplicationFactory.create(user=user, updated_at=datetime.datetime.now())
    EmployerBenefitFactory.create(application_id=application.application_id)

    update_request_body = {"employer_benefits": []}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    response_body = response.json().get("data")
    test_db_session.refresh(application)

    assert response.status_code == 200
    assert len(response_body.get("employer_benefits")) == 0
    assert len(application.employer_benefits) == 0


def test_application_patch_other_incomes_empty_arrays(
    client, user, auth_token, test_db_session, initialize_factories_session
):
    application = ApplicationFactory.create(user=user, updated_at=datetime.datetime.now())
    # other_incomes
    OtherIncomeFactory.create(application_id=application.application_id)

    update_request_body = {"other_incomes": []}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    response_body = response.json().get("data")
    test_db_session.refresh(application)

    assert response.status_code == 200
    assert len(response_body.get("other_incomes")) == 0
    assert len(application.other_incomes) == 0


def test_application_patch_previous_leaves_empty_arrays(
    client, user, auth_token, test_db_session, initialize_factories_session
):
    application = ApplicationFactory.create(user=user, updated_at=datetime.datetime.now())
    # previous_leaves
    PreviousLeaveFactory.create(application_id=application.application_id)

    update_request_body = {"previous_leaves": []}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    response_body = response.json().get("data")
    test_db_session.refresh(application)

    assert response.status_code == 200
    assert len(response_body.get("previous_leaves")) == 0
    assert len(application.previous_leaves) == 0


# TODO: refactor existing update_application tests to use this class and fixtures
# https://lwd.atlassian.net/browse/CP-2331
class TestApplicationsUpdate:
    @pytest.fixture
    def application(self, user):
        application = ApplicationFactory.create(user=user, updated_at=datetime.datetime.now())
        EmployerBenefitFactory.create(application_id=application.application_id)

        return application

    @pytest.fixture
    def address(self):
        return massgov.pfml.api.models.applications.common.Address(
            line_1="123 Main St.", city="Boston", state="Massachusetts", zip="02111"
        )

    # Collects the params necessary for making a request with a valid application update
    # to the mock API client
    @pytest.fixture
    def request_params(self, application, auth_token):
        class UpdateApplicationRequestParams(object):
            __slots__ = ["application_id", "auth_token", "body"]

            def __init__(self, application_id, auth_token, body):
                self.application_id = application_id
                self.auth_token = auth_token
                self.body = body

        return UpdateApplicationRequestParams(application.application_id, auth_token, {})

    # Submits an application update request with the given params
    def perform_update(self, client, request_params):
        return client.patch(
            "/v1/applications/{}".format(request_params.application_id),
            headers={"Authorization": f"Bearer {request_params.auth_token}"},
            json=request_params.body,
        )

    def test_success(self, client, request_params):
        request_body = {}
        request_body["first_name"] = "Foo"
        request_params.body = request_body

        response = self.perform_update(client, request_params)
        assert response.status_code == 200

    @pytest.mark.parametrize("name_field", ["first_name", "middle_name", "last_name"])
    def test_name_field_too_long(self, client, request_params, name_field):
        name = "a" * 51

        request_body = {}
        request_body[name_field] = name
        request_params.body = request_body

        response = self.perform_update(client, request_params)
        assert response.status_code == 400

        errors = response.json().get("errors")
        assert len(errors) == 1

        error = errors[0]
        assert error.get("type") == "maxLength"
        assert error.get("field") == name_field

    @pytest.mark.parametrize("address_field", ["line_1", "line_2", "city", "state"])
    def test_address_field_too_long(self, client, request_params, address, address_field):
        address_dict = address.__dict__
        address_dict[address_field] = "a" * 41

        request_body = {}
        request_body["residential_address"] = address_dict
        request_params.body = request_body

        response = self.perform_update(client, request_params)
        assert response.status_code == 400

        errors = response.json().get("errors")
        assert len(errors) == 1

        error = errors[0]
        assert error.get("type") == "maxLength"
        assert error.get("field") == f"residential_address.{address_field}"


def test_application_post_submit_app_creates_claim(client, user, auth_token, test_db_session):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    application = ApplicationFactory.create(
        user=user, employer_fein=employer.employer_fein, tax_identifier=employee.tax_identifier
    )
    WagesAndContributionsFactory.create(employer=employer, employee=employee)

    startDate = datetime.date(2021, 1, 1)
    endDate = datetime.date(2021, 4, 1)

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=startDate, end_date=endDate)
    ]
    application.date_of_birth = datetime.date(1997, 6, 6)
    application.employment_status_id = EmploymentStatus.UNEMPLOYED.employment_status_id
    application.hours_worked_per_week = 70
    application.has_continuous_leave_periods = True
    application.residential_address = AddressFactory.create()
    application.work_pattern = WorkPatternFixedFactory.create()
    application.leave_reason_id = 4
    application.leave_reason_qualifier_id = 7

    test_db_session.commit()
    client.post(
        "/v1/applications/{}/submit-application".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    submitted_application = (
        test_db_session.query(Application)
        .filter(Application.application_id == application.application_id)
        .one_or_none()
    )

    assert submitted_application.claim is not None
    assert submitted_application.claim.employer is not None
    assert submitted_application.claim.claim_start_date == startDate
    assert submitted_application.claim.claim_end_date == endDate
    assert submitted_application.claim.claim_type_id == 2


def test_submit_app_with_leave_reason_id_not_in_map(client, user, auth_token, test_db_session):
    with pytest.raises(NoClaimTypeForAbsenceType):
        new_leave_reason = LeaveReasonFactory.create(
            leave_reason_id=999, leave_reason_description="New reason"
        )
        new_leave_reason.absence_to_claim_type


@pytest.mark.parametrize("leave_reason", LeaveReason.get_all())
def test_each_leave_reason_has_claim_type(leave_reason):
    leave_reason.absence_to_claim_type


def test_application_patch_caring_leave_metadata(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user, phone=None)
    assert (
        application.leave_reason_id == LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id
    )
    assert application.caring_leave_metadata is None

    caring_leave_metadata = CaringLeaveMetadataFactory.create()
    application.caring_leave_metadata = caring_leave_metadata
    test_db_session.add(caring_leave_metadata)
    test_db_session.add(application)
    test_db_session.commit()

    # change leave reason to caring leave
    update_request_body = {
        "leave_details": {"reason": LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_description}
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert application.leave_reason_id == LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id

    # updating caring leave data
    update_request_body = {
        "leave_details": {
            "caring_leave_metadata": {
                "family_member_first_name": "Jane",
                "family_member_middle_name": "Alice",
                "family_member_last_name": "Doe",
                "family_member_date_of_birth": "1975-01-01",
                "relationship_to_caregiver": RelationshipToCaregiver.PARENT.relationship_to_caregiver_description,
            }
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    assert response.status_code == 200

    test_db_session.refresh(application)
    assert application.caring_leave_metadata.family_member_first_name == "Jane"
    assert application.caring_leave_metadata.family_member_middle_name == "Alice"
    assert application.caring_leave_metadata.family_member_last_name == "Doe"
    assert application.caring_leave_metadata.family_member_date_of_birth.isoformat() == "1975-01-01"
    assert (
        application.caring_leave_metadata.relationship_to_caregiver_id
        == RelationshipToCaregiver.PARENT.relationship_to_caregiver_id
    )

    response_body = response.json()
    response_caring_leave_metadata = (
        response_body.get("data").get("leave_details").get("caring_leave_metadata")
    )
    assert (
        response_caring_leave_metadata["family_member_first_name"]
        == update_request_body["leave_details"]["caring_leave_metadata"]["family_member_first_name"]
    )
    assert (
        response_caring_leave_metadata["family_member_middle_name"]
        == update_request_body["leave_details"]["caring_leave_metadata"][
            "family_member_middle_name"
        ]
    )
    assert (
        response_caring_leave_metadata["family_member_last_name"]
        == update_request_body["leave_details"]["caring_leave_metadata"]["family_member_last_name"]
    )
    assert response_caring_leave_metadata["family_member_date_of_birth"] == "****-01-01"
    assert (
        response_caring_leave_metadata["relationship_to_caregiver"]
        == update_request_body["leave_details"]["caring_leave_metadata"][
            "relationship_to_caregiver"
        ]
    )


def test_application_patch_caring_leave_metadata_issues(client, user, auth_token, test_db_session):
    caring_leave_metadata_issues = [
        {
            "field": "leave_details.caring_leave_metadata.family_member_first_name",
            "message": "leave_details.caring_leave_metadata.family_member_first_name is required",
            "type": "required",
        },
        {
            "field": "leave_details.caring_leave_metadata.family_member_last_name",
            "message": "leave_details.caring_leave_metadata.family_member_last_name is required",
            "type": "required",
        },
        {
            "field": "leave_details.caring_leave_metadata.family_member_date_of_birth",
            "message": "leave_details.caring_leave_metadata.family_member_date_of_birth is required",
            "type": "required",
        },
        {
            "field": "leave_details.caring_leave_metadata.relationship_to_caregiver",
            "message": "leave_details.caring_leave_metadata.relationship_to_caregiver is required",
            "type": "required",
        },
    ]

    application = ApplicationFactory.create(
        user=user, phone=None, leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id
    )
    assert application.caring_leave_metadata is None

    # patch without data
    update_request_body = {"leave_details": {"caring_leave_metadata": {}}}

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_warnings = response.json().get("warnings")

    assert response.status_code == 200
    for issue in caring_leave_metadata_issues:
        assert issue in response_warnings

    # patch with null values
    update_request_body = {
        "leave_details": {
            "caring_leave_metadata": {
                "family_member_first_name": None,
                "family_member_middle_name": None,
                "family_member_last_name": None,
                "family_member_date_of_birth": None,
                "relationship_to_caregiver": None,
            }
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    response_warnings = response.json().get("warnings")

    assert response.status_code == 200
    for issue in caring_leave_metadata_issues:
        assert issue in response_warnings


def test_application_patch_caring_leave_metadata_change_leave_reason_and_provide_metadata(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(
        user=user, phone=None, leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id
    )
    assert application.caring_leave_metadata is None

    application.caring_leave_metadata = CaringLeaveMetadataFactory.create()
    test_db_session.add(application)
    test_db_session.commit()

    count = test_db_session.query(CaringLeaveMetadata).count()
    assert count == 1

    update_request_body = {
        "leave_details": {
            # change leave reason to medical leave
            "reason": LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_description,
            # while also providing caring leave metadata
            "caring_leave_metadata": {
                "family_member_first_name": "Jane",
                "family_member_middle_name": "Alice",
                "family_member_last_name": "Doe",
                "family_member_date_of_birth": "1975-01-01",
                "relationship_to_caregiver": RelationshipToCaregiver.PARENT.relationship_to_caregiver_description,
            },
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert (
        application.leave_reason_id == LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id
    )

    updated_application = (
        test_db_session.query(Application)
        .filter(Application.application_id == application.application_id)
        .one()
    )
    assert updated_application.caring_leave_metadata is None

    # ensure CaringLeaveMetadata rows are deleted
    count = test_db_session.query(CaringLeaveMetadata).count()
    assert count == 0

    assert response_body.get("data").get("leave_details").get("caring_leave_metadata") is None


def test_application_patch_caring_leave_metadata_change_leave_reason(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(
        user=user, phone=None, leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id
    )
    assert application.caring_leave_metadata is None

    application.caring_leave_metadata = CaringLeaveMetadataFactory.create()
    test_db_session.add(application)
    test_db_session.commit()

    # change leave reason to medical leave
    update_request_body = {
        "leave_details": {
            "reason": LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_description
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    test_db_session.refresh(application)
    response_body = response.json()
    assert response.status_code == 200
    assert (
        application.leave_reason_id == LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id
    )
    assert application.caring_leave_metadata is None
    assert response_body.get("data").get("leave_details").get("caring_leave_metadata") is None


def test_application_patch_caring_leave_metadata_family_member_date_of_birth_validation(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(
        user=user, phone=None, leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id
    )

    # use an invalid date of birth - more than 150 years in the past
    update_request_body = {
        "leave_details": {
            "caring_leave_metadata": {
                "family_member_date_of_birth": datetime.date(1849, 1, 1).isoformat()
            }
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    assert response.status_code == 400

    response_body = response.json()
    errors = response_body.get("errors")
    assert len(errors) == 1

    error = errors[0]
    field = error.get("field")
    message = error.get("message")
    rule = error.get("rule")
    error_type = error.get("type")

    assert field == "leave_details.caring_leave_metadata.family_member_date_of_birth"
    assert message == "Date of birth must be within the past 150 years"
    assert rule == "date_of_birth_within_past_150_years"
    assert error_type == "invalid_year_range"


def test_application_patch_caring_leave_metadata_family_member_future_date_of_birth_validation(
    client, user, auth_token, test_db_session
):
    application = ApplicationFactory.create(
        user=user, phone=None, leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id
    )

    # use an invalid date of birth - more than 7 months in the future
    update_request_body = {
        "leave_details": {
            "caring_leave_metadata": {
                "family_member_date_of_birth": (
                    datetime.date.today() + relativedelta(months=7, days=1)
                ).isoformat()
            }
        }
    }

    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )

    assert response.status_code == 400

    response_body = response.json()
    errors = response_body.get("errors")
    assert len(errors) == 1

    error = errors[0]
    field = error.get("field")
    message = error.get("message")
    rule = error.get("rule")
    error_type = error.get("type")

    assert field == "leave_details.caring_leave_metadata.family_member_date_of_birth"
    assert message == "Family member's date of birth must be less than 7 months from now"
    assert rule == "max_7_months_in_future"
    assert error_type == "future_birth_date"


def test_get_crossed_benefit_years(initialize_factories_session, test_db_session):
    by_end_date = datetime.date(2022, 3, 1)
    by_start_date = by_end_date - datetime.timedelta(weeks=52)
    employee = EmployeeFactory.create()
    by = BenefitYearFactory.create(
        start_date=by_start_date, end_date=by_end_date, employee=employee
    )

    start_date = by_end_date - datetime.timedelta(days=10)
    end_date = by_end_date + datetime.timedelta(days=10)

    assert (
        get_crossed_benefit_years(employee.employee_id, start_date, end_date, test_db_session)[0]
        == by
    )

    start_date = by_end_date - datetime.timedelta(days=20)
    end_date = by_end_date - datetime.timedelta(days=10)

    assert (
        get_crossed_benefit_years(employee.employee_id, start_date, end_date, test_db_session) == []
    )

    start_date = by_end_date - datetime.timedelta(days=10)
    end_date = by_end_date

    assert (
        get_crossed_benefit_years(employee.employee_id, start_date, end_date, test_db_session) == []
    )

    start_date = by_end_date
    end_date = by_end_date + datetime.timedelta(days=10)

    assert (
        get_crossed_benefit_years(employee.employee_id, start_date, end_date, test_db_session)[0]
        == by
    )


def test_get_crossed_benefit_years_no_benefit_year(initialize_factories_session, test_db_session):
    end_date = datetime.date(2022, 3, 1)
    start_date = end_date - datetime.timedelta(days=12)
    employee = EmployeeFactory.create()

    assert (
        get_crossed_benefit_years(employee.employee_id, start_date, end_date, test_db_session) == []
    )


def test_get_application_split(initialize_factories_session, test_db_session):
    by_end_date = datetime.date(2022, 3, 1)
    by_start_date = by_end_date - datetime.timedelta(weeks=52)
    employee = EmployeeFactory.create()
    by = BenefitYearFactory.create(
        start_date=by_start_date, end_date=by_end_date, employee=employee
    )
    application: Application = ApplicationFactory.create(tax_identifier=employee.tax_identifier)

    assert get_application_split(application, by) is None

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=by_end_date - datetime.timedelta(days=10),
            end_date=by_end_date + datetime.timedelta(days=10),
        )
    ]

    application.employer_benefits = [
        EmployerBenefitFactory.build(
            benefit_start_date=by_end_date - datetime.timedelta(days=20),
            benefit_end_date=by_end_date + datetime.timedelta(days=10),
        )
    ]

    split = get_application_split(application, test_db_session)
    assert split is not None
    assert split.crossed_benefit_year == by
    assert split.application_dates_in_benefit_year.start_date == by_end_date - datetime.timedelta(
        days=10
    )
    assert split.application_dates_in_benefit_year.end_date == by_end_date
    assert (
        split.application_dates_outside_benefit_year.start_date
        == by_end_date + datetime.timedelta(days=1)
    )
    assert (
        split.application_dates_outside_benefit_year.end_date
        == by_end_date + datetime.timedelta(days=10)
    )
    assert (
        split.application_outside_benefit_year_submittable_on
        == by_end_date - datetime.timedelta(days=59)
    )

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=by_end_date - datetime.timedelta(days=20),
            end_date=by_end_date - datetime.timedelta(days=10),
        )
    ]

    assert get_application_split(application, test_db_session) is None

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=by_end_date - datetime.timedelta(days=10), end_date=by_end_date
        )
    ]

    assert get_application_split(application, test_db_session) is None

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(
            start_date=by_end_date, end_date=by_end_date + datetime.timedelta(days=10)
        )
    ]

    split = get_application_split(application, test_db_session)
    assert split is not None
    assert split.crossed_benefit_year == by
    assert split.application_dates_in_benefit_year.start_date == by_end_date
    assert split.application_dates_in_benefit_year.end_date == by_end_date
    assert (
        split.application_dates_outside_benefit_year.start_date
        == by_end_date + datetime.timedelta(days=1)
    )
    assert (
        split.application_dates_outside_benefit_year.end_date
        == by_end_date + datetime.timedelta(days=10)
    )
    assert (
        split.application_outside_benefit_year_submittable_on
        == by_end_date - datetime.timedelta(days=59)
    )


def test_get_application_split_app_already_split():
    by_end_date = datetime.date(2022, 3, 1)
    by_start_date = by_end_date - datetime.timedelta(weeks=52)
    by = BenefitYearFactory.build(start_date=by_start_date, end_date=by_end_date)
    application: Application = ApplicationFactory.build()
    already_split_app: Application = ApplicationFactory.build()

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.build(
            start_date=by_end_date - datetime.timedelta(days=10),
            end_date=by_end_date + datetime.timedelta(days=10),
        )
    ]
    application.split_from_application_id = already_split_app.application_id
    assert get_application_split(application, by) is None


def test_get_application_split_app_already_submitted():
    by_end_date = datetime.date(2022, 3, 1)
    by_start_date = by_end_date - datetime.timedelta(weeks=52)
    by = BenefitYearFactory.build(start_date=by_start_date, end_date=by_end_date)
    application: Application = ApplicationFactory.build()

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.build(
            start_date=by_end_date - datetime.timedelta(days=10),
            end_date=by_end_date + datetime.timedelta(days=10),
        )
    ]

    # Applications with an assigned submitted_time should not go through the split process
    application.submitted_time = datetime_util.utcnow()
    assert get_application_split(application, by) is None


def test_get_application_split_app_with_claim_id():
    by_end_date = datetime.date(2022, 3, 1)
    by_start_date = by_end_date - datetime.timedelta(weeks=52)
    by = BenefitYearFactory.build(start_date=by_start_date, end_date=by_end_date)
    application: Application = ApplicationFactory.build()

    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.build(
            start_date=by_end_date - datetime.timedelta(days=10),
            end_date=by_end_date + datetime.timedelta(days=10),
        )
    ]

    claim = ClaimFactory.build(
        fineos_notification_id="NTN-1989", fineos_absence_id="NTN-1989-ABS-01"
    )

    application.claim = claim
    application.claim_id = claim.claim_id
    assert get_application_split(application, by) is None


def test_computed_earliest_submission_date(client, user, auth_token, test_db_session):
    application = ApplicationFactory.create(user=user)
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=datetime.date(2053, 10, 1))
    ]
    test_db_session.commit()

    response = client.get(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200

    response_body = response.json().get("data")
    assert response_body.get("computed_earliest_submission_date") == "2053-08-02"


@patch("massgov.pfml.my_mass_gov.client.mock.mock_responses")
def test_patch_fields_to_use_from_user_profile_existing_values(
    mock_mock_responses, client, user, auth_token, test_db_session
):
    mock_mock_responses.return_value = build_response(
        200,
        ProfileResponseDto(
            idpUserId="a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
            idpTenantId="12345678-90ab-cdef-1234-567890abcdef",
            createdAt="2024-02-20T12:00:00Z",
            updatedAt="2024-02-20T12:00:00Z",
            gender=MmgGender.MAN,
            raceEthnicity=[RaceEthnicityEnum.ASIAN, RaceEthnicityEnum.HISPANIC_OR_LATINO],
        ),
    )

    application = ApplicationFactory.create(
        user=user, gender_id=None, race_id=Race.WHITE.race_id, mmg_idv_status_id=1
    )

    update_request_body = {"fields_to_use_from_user_profile": ["gender", "raceEthnicity"]}

    test_db_session.commit()
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )
    assert response.status_code == 200

    updated_response_body = response.json().get("data")

    # Assert this was updated from MMG
    assert updated_response_body.get("gender") == "Man"

    # Since this was already provided in the application, assert this was not updated
    assert updated_response_body.get("race") == "White"


@patch("massgov.pfml.my_mass_gov.client.mock.mock_responses")
def test_patch_fields_to_use_from_user_profile_repeated_calls(
    mock_mock_responses, client, user, auth_token, test_db_session
):
    mock_mock_responses.return_value = build_response(
        200,
        ProfileResponseDto(
            idpUserId="a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
            idpTenantId="12345678-90ab-cdef-1234-567890abcdef",
            createdAt="2024-02-20T12:00:00Z",
            updatedAt="2024-02-20T12:00:00Z",
            gender=MmgGender.MAN,
            raceEthnicity=[RaceEthnicityEnum.ASIAN, RaceEthnicityEnum.HISPANIC_OR_LATINO],
        ),
    )

    application = ApplicationFactory.create(
        user=user, gender_id=None, race_id=None, mmg_idv_status_id=1
    )

    update_request_body = {"fields_to_use_from_user_profile": ["gender"]}

    test_db_session.commit()
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )
    assert response.status_code == 200

    updated_response_body = response.json().get("data")

    assert updated_response_body.get("gender") == "Man"
    assert updated_response_body.get("fields_to_use_from_user_profile") == ["gender"]

    update_request_body_next = {"fields_to_use_from_user_profile": ["gender", "raceEthnicity"]}
    response_next = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body_next,
    )
    assert response_next.status_code == 200

    updated_response_body_next = response_next.json().get("data")
    assert updated_response_body_next.get("gender") == "Man"
    assert "gender" in updated_response_body_next.get("fields_to_use_from_user_profile")
    assert "raceEthnicity" in updated_response_body_next.get("fields_to_use_from_user_profile")


def test_patch_mmg_idv_status_to_trigger_status_update(
    client, user, auth_token, test_db_session, feature_config
):
    """
    For the IDV Pilot, PATCHing the application's MMG IDV status with a null update
    must trigger an IDV status update if the status is not already set
    """
    feature_config.universal_profile.enable_mmg_idv = True
    application = ApplicationFactory.create(user=user)

    update_request_body = {"mmg_idv_status": None}

    test_db_session.commit()
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )
    assert response.status_code == 200

    updated_response_body = response.json().get("data")

    # Assert this was updated from MMG
    assert updated_response_body.get("mmg_idv_status") == "Verified"


def test_patch_application_update_to_invalidate_idv(
    client, user, auth_token, test_db_session, feature_config
):
    """
    For backend IDV invalidation, PATCHing autofilled fields should invalidate the MMG IDV status
    """
    feature_config.universal_profile.enable_mmg_idv = True
    feature_config.universal_profile.enable_backend_invalidation = True
    application = ApplicationFactory.create(user=user, mmg_idv_status_id=MmgIdvStatus.VERIFIED.id)
    application.mmg_profile = ApplicationMmgProfileFactory.create(
        application_id=application.application_id,
        idv_status_id=MmgIdvStatus.VERIFIED.id,
    )

    update_request_body = {"first_name": "Changed Name"}

    test_db_session.commit()
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )
    assert response.status_code == 200

    updated_response_body = response.json().get("data")

    # Assert this was updated from MMG
    assert updated_response_body.get("mmg_idv_status") == "Unverified"


def test_patch_application_update_does_not_invalidate_idv(
    client, user, auth_token, test_db_session, feature_config
):
    """
    For backend IDV invalidation, PATCHing non-autofilled fields should not invalidate the MMG IDV status
    """
    feature_config.universal_profile.enable_mmg_idv = True
    feature_config.universal_profile.enable_backend_invalidation = True
    application = ApplicationFactory.create(
        user=user, middle_name="Original Name", mmg_idv_status_id=MmgIdvStatus.VERIFIED.id
    )
    application.mmg_profile = ApplicationMmgProfileFactory.create(
        application_id=application.application_id,
        idv_status_id=MmgIdvStatus.VERIFIED.id,
    )

    update_request_body = {"middle_name": "Changed Name"}

    test_db_session.commit()
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )
    assert response.status_code == 200

    updated_response_body = response.json().get("data")

    # Assert this was updated from MMG
    assert updated_response_body.get("mmg_idv_status") == "Verified"


@pytest.mark.parametrize("industry_sector_row", IndustrySector.get_all())
def test_patch_industry_sector(client, user, auth_token, test_db_session, industry_sector_row):
    industry_sector = industry_sector_row.industry_sector_description
    application = ApplicationFactory.create(user=user)

    update_request_body = {"industry_sector": industry_sector}

    test_db_session.commit()
    response = client.patch(
        "/v1/applications/{}".format(application.application_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_body,
    )
    assert response.status_code == 200

    updated_response_body = response.json().get("data")

    # Assert that the application was updated with industry_sector
    assert updated_response_body.get("industry_sector") == industry_sector
