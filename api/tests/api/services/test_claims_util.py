from datetime import date
from decimal import Decimal
from unittest.mock import Mock, patch

import pytest

from massgov.pfml.api.services.claims_util import (
    ClaimWagesBenefits,
    get_weekly_benefit_amount_from_claim,
)
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.factories import ApplicationFactory, ClaimFactory, EmployeeFactory
from massgov.pfml.util.logging import logging
from tests.helpers.logging import assert_log_has_match


class TestClaimWagesBenefits:
    """Test the ClaimWagesBenefits Pydantic model"""

    def test_init_with_values(self):
        """Test creating a ClaimWagesBenefits instance with values"""
        model = ClaimWagesBenefits(
            individual_average_weekly_wage=Decimal("500.00"),
            weekly_benefit_amount=Decimal("300.00"),
        )
        assert model.individual_average_weekly_wage == Decimal("500.00")
        assert model.weekly_benefit_amount == Decimal("300.00")

    def test_init_with_none(self):
        """Test creating a ClaimWagesBenefits instance with None values"""
        model = ClaimWagesBenefits()
        assert model.individual_average_weekly_wage is None
        assert model.weekly_benefit_amount is None

    def test_json_serialization(self):
        """Test that the model can be converted to JSON"""
        model = ClaimWagesBenefits(
            individual_average_weekly_wage=Decimal("500.00"),
            weekly_benefit_amount=Decimal("300.00"),
        )

        json_data = model.json()
        assert '"individual_average_weekly_wage": 500.0' in json_data
        assert '"weekly_benefit_amount": 300.0' in json_data


class TestGetWeeklyBenefitAmountFromClaimId:
    """Test the get_weekly_benefit_amount_from_claim function"""

    @pytest.fixture
    def claim(self):
        return ClaimFactory.build(fineos_absence_id="ABS-123-456")

    @patch("massgov.pfml.api.services.claims_util.get_iaww_from_claim")
    def test_no_iaww(self, mock_get_iaww, test_db_session, claim):
        """Test when no IAWW is found for the claim"""

        mock_app = Mock(spec=Application)
        mock_app.start_date = date.today()
        mock_get_iaww.return_value = None

        result = get_weekly_benefit_amount_from_claim(test_db_session, claim)

        assert result is None
        mock_get_iaww.assert_called_once()

    @patch("massgov.pfml.api.services.claims_util.get_iaww_from_claim")
    def test_no_application(self, mock_get_iaww, test_db_session, claim, caplog):
        """Test when no application is found for the claim"""
        mock_get_iaww.return_value = Decimal("1000.00")
        claim.application = None

        result = get_weekly_benefit_amount_from_claim(test_db_session, claim)

        assert result is None
        mock_get_iaww.assert_called_once()

        assert_log_has_match(
            caplog, "No application found for claim", {"claim_id": str(claim.claim_id)}
        )

    @patch("massgov.pfml.api.services.claims_util.get_iaww_from_claim")
    def test_exception_handling(self, mock_get_iaww, test_db_session, claim, caplog):
        """Test that exceptions are properly caught and logged"""
        caplog.set_level(logging.INFO)

        mock_get_iaww.side_effect = Exception("Test exception")

        result = get_weekly_benefit_amount_from_claim(test_db_session, claim)

        assert result is None
        assert_log_has_match(
            caplog,
            "Error calculating wages and benefits for claim",
            {"claim_id": str(claim.claim_id), "error": "Test exception"},
        )


class TestGetWeeklyBenefitAmountFromApplication:
    """Test the get_weekly_benefit_amount_from_application function using factories"""

    @pytest.fixture
    def mock_db_session(self):
        return Mock()

    @pytest.fixture
    def employee(self):
        return EmployeeFactory.build()

    @pytest.fixture
    def claim(self, employee):
        return ClaimFactory.build(employee=employee)

    @pytest.fixture
    def mock_application(self):
        mock_app = Mock(spec=Application)
        mock_app.start_date = date.today()
        return mock_app

    @pytest.fixture
    def log_extra(self, claim):
        return {"claim_id": claim.claim_id, "function": "test_function"}

    @patch("massgov.pfml.api.services.claims_util.get_weekly_benefit_amount_from_application")
    @patch("massgov.pfml.api.services.claims_util.get_iaww_from_claim")
    def test_successful_calculation(self, mock_get_iaww, mock_get_wba, test_db_session, claim):
        """Test a successful calculation of weekly benefit amount from claim ID"""
        mock_get_iaww.return_value = Decimal("1000.00")
        mock_app = ApplicationFactory.build()
        claim.application = mock_app
        mock_get_wba.return_value = Decimal("500.00")

        result = get_weekly_benefit_amount_from_claim(test_db_session, claim)

        assert result is not None
        assert result.individual_average_weekly_wage == Decimal("1000.00")
        assert result.weekly_benefit_amount == Decimal("500.00")
