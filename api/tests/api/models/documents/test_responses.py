import uuid
from datetime import datetime
from unittest import mock

import pytest

from massgov.pfml.api.models.documents.common import ContentType as AllowedContentTypes
from massgov.pfml.api.models.documents.common import DocumentType as DocumentTypeModel
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.models.documents import Document, LkDocumentType


@pytest.fixture
def mock_db_document():
    mock_db_document = mock.MagicMock(spec=Document)
    mock_db_document.document_type_instance = None
    mock_db_document.document_id = 1
    mock_db_document.user_id = uuid.uuid4()
    mock_db_document.application_id = uuid.uuid4()
    mock_db_document.appeal_id = uuid.uuid4()
    mock_db_document.document_type_id = 1
    mock_db_document.pfml_document_type_id = 1
    mock_db_document.fineos_id = 3131
    mock_db_document.size_bytes = 47029
    mock_db_document.is_stored_in_s3 = True
    mock_db_document.name = "Mock Passport"
    mock_db_document.description = "Page 1"
    mock_db_document.created_at = datetime.now()
    mock_db_document.document_type_instance = LkDocumentType(
        document_type_id=1, document_type_description="Passport"
    )
    return mock_db_document


def test_document_response_from_orm(mock_db_document):

    # GIVEN
    valid_content_type = AllowedContentTypes.pdf

    # WHEN
    document_response = DocumentResponse.from_orm(mock_db_document, valid_content_type)

    # THEN
    assert document_response.is_legal_notice is False

    assert document_response.user_id == mock_db_document.user_id
    assert document_response.application_id == mock_db_document.application_id
    assert document_response.appeal_id == mock_db_document.appeal_id
    assert (
        document_response.created_at == mock_db_document.created_at.date()
    )  # DocumentResponse uses type date
    assert (
        document_response.document_type
        == mock_db_document.document_type_instance.document_type_description
    )
    assert document_response.appeal_id == mock_db_document.appeal_id
    assert document_response.content_type == valid_content_type
    assert document_response.appeal_id == mock_db_document.appeal_id
    assert document_response.fineos_document_id == str(mock_db_document.fineos_id)
    assert document_response.name == mock_db_document.name
    assert document_response.description == mock_db_document.description
    assert (
        document_response.pfml_document_type
        == mock_db_document.document_type_instance.document_type_description
    )


@pytest.fixture
def mock_db_document_preg():
    mock_db_document_preg = mock.MagicMock(spec=Document)  # Change variable name to avoid confusion
    mock_db_document_preg.document_type_instance = None
    mock_db_document_preg.document_id = 10
    mock_db_document_preg.user_id = uuid.uuid4()
    mock_db_document_preg.application_id = uuid.uuid4()
    mock_db_document_preg.appeal_id = uuid.uuid4()
    mock_db_document_preg.document_type_id = 10
    mock_db_document_preg.pfml_document_type_id = 10
    mock_db_document_preg.fineos_id = 3131
    mock_db_document_preg.size_bytes = 47029
    mock_db_document_preg.is_stored_in_s3 = True
    mock_db_document_preg.name = "Mock Pregnancy/Maternity Form"
    mock_db_document_preg.description = "Page 1"
    mock_db_document_preg.created_at = datetime.now()
    mock_db_document_preg.document_type_instance = LkDocumentType(
        document_type_id=10, document_type_description="Pregnancy/Maternity form"
    )
    return mock_db_document_preg


def test_document_response_converts_pregnancy_and_maternity_form():
    # GIVEN
    mock_doc = mock.MagicMock(spec=Document)
    mock_doc.document_id = 90
    mock_doc.user_id = uuid.uuid4()
    mock_doc.application_id = uuid.uuid4()
    mock_doc.appeal_id = uuid.uuid4()
    mock_doc.document_type_id = 90
    mock_doc.pfml_document_type_id = None
    mock_doc.fineos_id = 3131
    mock_doc.name = "Mock Pregnancy and Maternity Form"
    mock_doc.description = "Test document"
    mock_doc.created_at = datetime.now()

    # Mock the DocumentType.get_description to return the new format
    with mock.patch(
        "massgov.pfml.db.lookup_data.documents.DocumentType.get_description",
        return_value=DocumentTypeModel.pregnancy_and_maternity_form.value,
    ):
        valid_content_type = AllowedContentTypes.pdf

        # WHEN
        document_response = DocumentResponse.from_orm(mock_doc, valid_content_type)

        # THEN
        # Verify the document type was converted to the old format
        assert document_response.document_type == DocumentTypeModel.pregnancy_maternity_form.value
