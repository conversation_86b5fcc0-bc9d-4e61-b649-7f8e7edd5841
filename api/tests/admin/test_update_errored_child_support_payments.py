import pytest

from massgov.pfml.db.models.factories import EmployeeFactory, PaymentFactory, StateLogFactory
from massgov.pfml.db.models.payments import FineosWritebackDetails
from massgov.pfml.util.admin.update_errored_child_support_payments_util import (
    UpdateErroredChildSupport,
)


@pytest.fixture()
def employee_one(initialize_factories_session):
    return EmployeeFactory.create()


@pytest.fixture()
def payment_one(initialize_factories_session, employee_one):
    return PaymentFactory.create(employee=employee_one)


@pytest.fixture()
def state_log_one(initialize_factories_session, payment_one):
    return StateLogFactory.create(payment=payment_one)


def get_all_writeback_details(db_session):
    return db_session.query(FineosWritebackDetails).all()


def get_writeback_details(db_session, id):
    return (
        db_session.query(FineosWritebackDetails)
        .filter(FineosWritebackDetails.fineos_writeback_details_id == id)
        .first()
    )


class TestUpdateErroredChildSupport:
    def test_dry_run_update_errored_child_support_success(
        self, payment_one, state_log_one, test_db_session
    ):
        updater = UpdateErroredChildSupport(
            payment_one.payment_id,
            "2",
            state_log_one.state_log_id,
            "223",
            "Testing Outcome",
            True,
            "test",
            test_db_session,
        )
        updater.run()
        assert updater.should_commit_changes is False
        assert get_all_writeback_details(test_db_session) == []

    def test_update_errored_child_support_success(
        self, payment_one, state_log_one, test_db_session
    ):
        updater = UpdateErroredChildSupport(
            payment_one.payment_id,
            "2",
            state_log_one.state_log_id,
            "223",
            "Testing Outcome",
            False,
            "test",
            test_db_session,
        )
        updater.run()
        assert updater.should_commit_changes is True
        get_writeback_details = get_all_writeback_details(test_db_session)
        assert len(get_writeback_details) == 1
        writeback_details = get_writeback_details[0]
        assert writeback_details.payment_id == payment_one.payment_id
