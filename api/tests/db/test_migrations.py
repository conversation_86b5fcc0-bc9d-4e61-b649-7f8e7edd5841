import os
from unittest.mock import patch

import pytest
from alembic.script import ScriptDirectory
from alembic.script.revision import MultipleHeads
from alembic.util.exc import CommandError
from freezegun import freeze_time

import massgov.pfml.util.files as file_util
from massgov.pfml.db.migrations.run import (
    _upload_executed_statements_to_s3_and_send_email,
    alembic_cfg,
)


@pytest.fixture
def mock_send_email():
    with patch("massgov.pfml.db.migrations.run._send_email_to_edm_team") as mock:
        yield mock


def test_only_single_head_revision_in_migrations():
    script = ScriptDirectory.from_config(alembic_cfg)

    try:
        # This will raise if there are multiple heads
        script.get_current_head()
        multihead_situation = False
    except CommandError as e:
        # re-raise anything not expected
        if not isinstance(e.__cause__, MultipleHeads):
            raise

        multihead_situation = True

    # raising assertion error here instead of in `except` block to avoid pytest
    # printing the huge stacktrace of the multi-head exception, which in this
    # case we don't really care about the details, just using it as a flag
    if multihead_situation:
        raise AssertionError(
            "See 'Multi-head situations' section of README.md. TL;DR: run `make db-migrate-merge-heads`"
        )


def test_running_migrations_fixture(logging_fix, test_db_via_migrations):
    pass


@freeze_time("2025-01-01")
def test_upload_executed_statements_to_s3_and_send_email(
    mock_s3_bucket, monkeypatch, mock_send_email
):

    pfml_schema_changes_directory = f"s3://{mock_s3_bucket}/edm/pfml-schema-changes"

    monkeypatch.setenv("PFML_SCHEMA_CHANGES_DIRECTORY", pfml_schema_changes_directory)
    monkeypatch.setenv("ENABLE_NOTIFY_EDM_TEAM_OF_PFML_SCHEMA_CHANGES", "1")

    executed_statements = [
        "UPDATE alembic_version SET version_num='c57888c13fe8' WHERE alembic_version.version_num = 'ca62c6c6a047'",
        "COMMENT ON COLUMN employer_exemption_application.tpa_contact_title IS 'Title associated with employer exemption application third party administrator'",
        "ALTER TABLE employer_exemption_application ADD COLUMN tpa_contact_title TEXT",
        "COMMENT ON COLUMN employer_exemption_application.tpa_contact_last_name IS 'Contact last name associated with employer exemption application third party administrator pii:true'",
        "ALTER TABLE employer_exemption_application ADD COLUMN tpa_contact_last_name TEXT",
        "COMMENT ON COLUMN employer_exemption_application.tpa_contact_first_name IS 'Contact first name associated with employer exemption application third party administrator pii:true'",
        "ALTER TABLE employer_exemption_application ADD COLUMN tpa_contact_first_name TEXT",
        "UPDATE alembic_version SET version_num='ca62c6c6a047' WHERE alembic_version.version_num = '57570cd3eff2'",
        "ALTER TABLE wages_and_contributions ALTER COLUMN wages_and_contributions_datasource_id SET NOT NULL",
        "UPDATE alembic_version SET version_num='57570cd3eff2' WHERE alembic_version.version_num = 'b5d262b67495'",
        "ALTER TABLE insurance_plan ADD COLUMN deactivated BOOLEAN DEFAULT false NOT NULL",
        "UPDATE alembic_version SET version_num='b5d262b67495' WHERE alembic_version.version_num = 'fe78ea1ab515'",
        "DROP TABLE lk_user_action_type",
        "ALTER TABLE employer_exemption_application DROP COLUMN contact_name",
        "UPDATE alembic_version SET version_num='fe78ea1ab515' WHERE alembic_version.version_num = 'c3a792458e73'",
        "DROP TABLE user_action",
        "DROP INDEX ix_user_action_user_id",
        "DROP INDEX ix_user_action_user_action_type_id",
        "UPDATE alembic_version SET version_num='c3a792458e73' WHERE alembic_version.version_num = 'b5287840829e'",
        "ALTER TABLE insurance_provider ADD COLUMN deactivated BOOLEAN DEFAULT false NOT NULL",
        "UPDATE alembic_version SET version_num='b5287840829e' WHERE alembic_version.version_num = '0bb37e7cf756'",
        "ALTER TABLE mmars_event ADD CONSTRAINT mmars_event_mmars_phase_code_type_id_fkey FOREIGN KEY(mmars_phase_code_type_id) REFERENCES lk_mmars_phase_code_type (mmars_phase_code_type_id)",
        "ALTER TABLE mmars_event ADD COLUMN mmars_phase_code_type_id INTEGER",
        "COMMENT ON COLUMN mmars_event.original_vendor_customer_code IS 'original Vendor Customer Code in MMARS'",
        "ALTER TABLE mmars_event ADD COLUMN original_vendor_customer_code TEXT",
        "CREATE TABLE lk_mmars_phase_code_type (mmars_phase_code_type_id SERIAL NOT NULL, mmars_phase_code_type_description TEXT NOT NULL, PRIMARY KEY (mmars_phase_code_type_id))",
        "SELECT alembic_version.version_num FROM alembic_version",
        "SET ROLE pfml_owner",
    ]

    _upload_executed_statements_to_s3_and_send_email(executed_statements)

    dest_filepath = os.path.join(
        pfml_schema_changes_directory, "PFML_DB_Schema_Changes_20**********.txt"
    )

    file_lines = list(file_util.read_file_lines(dest_filepath))

    expected_file_lines = [
        "ALTER TABLE employer_exemption_application ADD COLUMN tpa_contact_title TEXT",
        "ALTER TABLE employer_exemption_application ADD COLUMN tpa_contact_last_name TEXT",
        "ALTER TABLE employer_exemption_application ADD COLUMN tpa_contact_first_name TEXT",
        "ALTER TABLE insurance_plan ADD COLUMN deactivated BOOLEAN DEFAULT false NOT NULL",
        "ALTER TABLE insurance_provider ADD COLUMN deactivated BOOLEAN DEFAULT false NOT NULL",
        "ALTER TABLE mmars_event ADD COLUMN mmars_phase_code_type_id INTEGER",
        "ALTER TABLE mmars_event ADD COLUMN original_vendor_customer_code TEXT",
        "ALTER TABLE employer_exemption_application DROP COLUMN contact_name",
        "CREATE TABLE lk_mmars_phase_code_type (mmars_phase_code_type_id SERIAL NOT NULL, mmars_phase_code_type_description TEXT NOT NULL, PRIMARY KEY (mmars_phase_code_type_id))",
        "DROP TABLE lk_user_action_type",
        "DROP TABLE user_action",
        "COMMENT ON COLUMN employer_exemption_application.tpa_contact_last_name IS 'Contact last name associated with employer exemption application third party administrator pii:true'",
        "COMMENT ON COLUMN employer_exemption_application.tpa_contact_first_name IS 'Contact first name associated with employer exemption application third party administrator pii:true'",
    ]

    assert expected_file_lines == file_lines

    mock_send_email.assert_called_once()
