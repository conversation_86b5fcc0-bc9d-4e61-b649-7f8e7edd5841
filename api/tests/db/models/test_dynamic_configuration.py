import pytest
import sqlalchemy.orm.exc

from massgov.pfml.db.models.dynamic_configuration import DynamicConfig


def test_all_blank(test_db_session):
    config_entry = DynamicConfig(config_key="", config_context="", config_value="")
    test_db_session.add(config_entry)
    test_db_session.commit()


def test_blank_context(test_db_session):
    config_entry = DynamicConfig(
        config_key="test-key", config_context="", config_value="test-value"
    )
    test_db_session.add(config_entry)
    test_db_session.commit()


def test_no_context_blank_default(test_db_session):
    config_entry = DynamicConfig(config_key="test-key", config_value="test-value")
    test_db_session.add(config_entry)
    test_db_session.commit()
    assert config_entry.config_context == ""


def test_multiple_context(test_db_session):

    test_db_session.add(
        DynamicConfig(
            config_key="test-key", config_context="test-context-1", config_value="test-value"
        )
    )
    test_db_session.add(
        DynamicConfig(
            config_key="test-key", config_context="test-context-2", config_value="test-value"
        )
    )
    test_db_session.commit()


def test_duplicate_key_no_context(test_db_session):
    # Test that we can't add the same key twice without a context (default context is empty string)
    with pytest.raises(sqlalchemy.exc.IntegrityError):
        test_db_session.add(DynamicConfig(config_key="test-key", config_value="test-value"))
        test_db_session.add(DynamicConfig(config_key="test-key", config_value="test-value"))
        test_db_session.commit()


def test_duplicate_context(test_db_session):
    # Test that we can't add 2 entries with the same key and non-blank context
    with pytest.raises(sqlalchemy.exc.IntegrityError):
        test_db_session.add(
            DynamicConfig(
                config_key="test-key", config_context="test-context-1", config_value="test-value"
            )
        )
        test_db_session.add(
            DynamicConfig(
                config_key="test-key", config_context="test-context-1", config_value="test-value"
            )
        )
        test_db_session.commit()
