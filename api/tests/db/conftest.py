import pytest

import massgov.pfml.db.config


@pytest.fixture
def delete_db_env_vars(monkeypatch):
    monkeypatch.delenv("DB_HOST", raising=False)
    monkeypatch.delenv("DB_NAME", raising=False)
    monkeypatch.delenv("DB_USERNAME", raising=False)
    monkeypatch.delenv("DB_PASSWORD", raising=False)
    monkeypatch.delenv("DB_SCHEMA", raising=False)
    monkeypatch.delenv("DB_PORT", raising=False)

    monkeypatch.delenv("DB_ADMIN_USERNAME", raising=False)
    monkeypatch.delenv("DB_ADMIN_PASSWORD", raising=False)


@pytest.fixture(scope="session")
def db_config_verbose(db_schema_name):
    return massgov.pfml.db.config.DbConfig(
        # Note: host is not set, because it is different when running natively vs in Docker.
        dbname="pfml",
        username="pfml",
        password="secret123",
        schema=db_schema_name,
        use_iam_auth=False,
        hide_sql_parameter_logs=False,
    )


@pytest.fixture
def verbose_test_db_session(monkeypatch, test_db, db_config_verbose):
    import massgov.pfml.db as db

    db_session = db.init(db_config_verbose, sync_lookups=True, check_migrations_current=False)

    yield db_session

    db_session.close()
    db_session.remove()
