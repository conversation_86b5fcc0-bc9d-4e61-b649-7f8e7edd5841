import massgov.pfml.db.queries.dynamic_config as dc_queries
from massgov.pfml.db.models.dynamic_configuration import DynamicConfig, DynamicConfigLog
from massgov.pfml.db.models.factories import DynamicConfigFactory
from massgov.pfml.db.queries.dynamic_config import ADMIN_UX_DYNAMIC_CONFIG_LOGS_LOAD_LIMIT_KEY
from tests.api.admin.test_dynamic_config import DynamicConfigLogFactory


class TestDynamicConfigQueries:

    def test_get_all_entries(self, mock_dynamic_config_entries, test_db_session):
        result = dc_queries.get_all_dynamic_config_entries(test_db_session)
        assert len(result) == len(mock_dynamic_config_entries)

    def test_insert_entry(self, test_db_session, mock_valid_admin_user):
        new_entry = dc_queries.create_dynamic_config_entry(
            test_db_session,
            admin_user_id=mock_valid_admin_user.admin_user_id,
            config_key="new_key",
            config_context="new_context",
            config_value="new_value",
        )
        assert new_entry.config_key == "new_key"
        assert new_entry.config_context == "new_context"
        assert new_entry.config_value == "new_value"

        # Verify it was added to the database
        entries = test_db_session.query(DynamicConfig).all()
        assert len(entries) == 1
        entry = entries[0]
        assert entry.config_key == "new_key"
        assert entry.config_context == "new_context"
        assert entry.config_value == "new_value"

    def test_update_entry(
        self, test_db_session, mock_valid_admin_user, mock_dynamic_config_entries
    ):
        entry_to_update = mock_dynamic_config_entries[0]
        updated_entry = dc_queries.update_dynamic_config_entry(
            test_db_session,
            admin_user_id=mock_valid_admin_user.admin_user_id,
            dynamic_config_id=entry_to_update.dynamic_config_id,
            config_value="new_value",
        )

        # verify the entry was returned as updated
        assert updated_entry.config_value == "new_value"

        # Verify it was added to the database
        db_entry = (
            test_db_session.query(DynamicConfig)
            .filter(DynamicConfig.dynamic_config_id == entry_to_update.dynamic_config_id)
            .one()
        )
        # verify db_entry is an instance of DynamicConfig
        assert isinstance(db_entry, DynamicConfig)
        assert db_entry.config_value == "new_value"

    def test_delete_entry(
        self, test_db_session, mock_valid_admin_user, mock_dynamic_config_entries
    ):
        entry_to_delete = mock_dynamic_config_entries[0]
        dc_queries.delete_dynamic_config_entry(
            test_db_session,
            admin_user_id=mock_valid_admin_user.admin_user_id,
            dynamic_config_id=entry_to_delete.dynamic_config_id,
        )

        # Verify it was deleted
        entries = test_db_session.query(DynamicConfig).all()
        assert len(entries) == len(mock_dynamic_config_entries) - 1
        assert entry_to_delete not in entries

    def test_new_entry_creates_audit_entry(self, test_db_session, mock_valid_admin_user):
        dc_queries.create_dynamic_config_entry(
            test_db_session,
            admin_user_id=mock_valid_admin_user.admin_user_id,
            config_key="foo_key",
            config_context="foo_context",
            config_value="foo_value",
        )

        # Verify an audit log entry was created
        logs = test_db_session.query(DynamicConfigLog).all()
        assert len(logs) == 1
        log_entry = logs[0]
        # Verify the log entry was created with the expected values
        assert log_entry.before_state == "[created]"
        assert log_entry.admin_user_id == mock_valid_admin_user.admin_user_id
        assert log_entry.config_key == "foo_key"
        assert log_entry.config_context == "foo_context"
        values_to_check = [
            "foo_key",
            "foo_context",
            "foo_value",
        ]
        for value in values_to_check:
            assert value in log_entry.after_state

    def test_update_entry_creates_audit_entry(
        self, test_db_session, mock_valid_admin_user, mock_dynamic_config_entries
    ):
        entry_to_update = mock_dynamic_config_entries[0]

        dc_queries.update_dynamic_config_entry(
            test_db_session,
            admin_user_id=mock_valid_admin_user.admin_user_id,
            dynamic_config_id=entry_to_update.dynamic_config_id,
            config_value="updated_value",
        )

        # Verify an audit log entry was created
        logs = test_db_session.query(DynamicConfigLog).all()
        assert len(logs) == 1
        log_entry = logs[0]

        # Verify the log entry was created with the expected values
        values_to_check = [
            entry_to_update.config_key,
            entry_to_update.config_context,
        ]
        before_values_to_check = [
            *values_to_check,
            entry_to_update.config_value,
        ]
        after_values_to_check = [
            *values_to_check,
            "updated_value",
        ]
        for value in before_values_to_check:
            assert value in log_entry.before_state
        for value in after_values_to_check:
            assert value in log_entry.after_state
        assert log_entry.admin_user_id == mock_valid_admin_user.admin_user_id
        assert log_entry.config_key == entry_to_update.config_key
        assert log_entry.config_context == entry_to_update.config_context

    def test_delete_creates_audit_entry(
        self, test_db_session, mock_valid_admin_user, mock_dynamic_config_entries
    ):
        entry_to_delete = mock_dynamic_config_entries[0]
        dc_queries.delete_dynamic_config_entry(
            test_db_session,
            admin_user_id=mock_valid_admin_user.admin_user_id,
            dynamic_config_id=entry_to_delete.dynamic_config_id,
        )

        # Verify an audit log entry was created
        logs = test_db_session.query(DynamicConfigLog).all()
        assert len(logs) == 1
        log_entry = logs[0]
        before_values_to_check = [
            entry_to_delete.config_key,
            entry_to_delete.config_context,
            entry_to_delete.config_value,
        ]
        # Verify the log entry was created with the expected values
        for value in before_values_to_check:
            assert value in log_entry.before_state
        assert log_entry.after_state == "[deleted]"
        assert log_entry.admin_user_id == mock_valid_admin_user.admin_user_id
        assert log_entry.config_key == entry_to_delete.config_key
        assert log_entry.config_context == entry_to_delete.config_context


class TestDynamicConfigLogQueries:

    def test_get_logs(self, test_db_session, initialize_factories_session):
        DynamicConfigLogFactory.create_batch(10)

        logs = dc_queries.get_dynamic_config_log_entries(test_db_session)

        # Verify that the logs are returned correctly
        assert isinstance(logs, list)
        assert len(logs) == 10

    def test_get_logs_with_arg_limit(self, test_db_session, initialize_factories_session):
        DynamicConfigLogFactory.create_batch(10)

        logs = dc_queries.get_dynamic_config_log_entries(test_db_session, limit=5)

        # Verify that the logs are returned correctly
        assert isinstance(logs, list)
        assert len(logs) == 5

    def test_get_logs_with_env_limit(
        self, test_db_session, initialize_factories_session, monkeypatch
    ):
        monkeypatch.setenv(ADMIN_UX_DYNAMIC_CONFIG_LOGS_LOAD_LIMIT_KEY, "5")

        DynamicConfigLogFactory.create_batch(10)

        logs = dc_queries.get_dynamic_config_log_entries(test_db_session)

        # Verify that the logs are returned correctly
        assert isinstance(logs, list)
        assert len(logs) == 5

    def test_get_logs_with_dc_limit(
        self, test_db_session, initialize_factories_session, monkeypatch
    ):
        DynamicConfigFactory.create(
            config_key=ADMIN_UX_DYNAMIC_CONFIG_LOGS_LOAD_LIMIT_KEY,
            config_context="",
            config_value="7",
        )

        DynamicConfigLogFactory.create_batch(10)

        logs = dc_queries.get_dynamic_config_log_entries(test_db_session)

        # Verify that the logs are returned correctly
        assert isinstance(logs, list)
        assert len(logs) == 7
