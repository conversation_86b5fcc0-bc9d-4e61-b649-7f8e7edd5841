import pytest

import massgov.pfml.util.dynamic_config as dynamic_config_util
from massgov.pfml.db.models.factories import DynamicConfigFactory


@pytest.fixture(autouse=True)
def reset_dynamic_config_cache():
    """Reset the dynamic config cache for testing purposes."""
    dynamic_config_util._reset_cache()


@pytest.fixture
def mock_dynamic_config_entries(initialize_factories_session, test_db_session):
    entries = [
        DynamicConfigFactory.create(
            config_key="foo_key", config_context="", config_value="foo_context_blank_val"
        ),
        DynamicConfigFactory.create(
            config_key="foo_key", config_context="foo_context_1", config_value="foo_val_1"
        ),
        DynamicConfigFactory.create(
            config_key="foo_key", config_context="foo_context_2", config_value="foo_val_2"
        ),
        DynamicConfigFactory.create(
            config_key="bar_key", config_context="", config_value="bar_context_blank_val"
        ),
    ]
    # Detach each of the entries from ORM state to avoid side effects in test actions
    # Don't use expunge_all(), that could affect other fixtures
    for entry in entries:
        test_db_session.expunge(entry)

    return entries
