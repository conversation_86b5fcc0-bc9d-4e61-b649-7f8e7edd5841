from datetime import datetime, timedelta

import pytest

from massgov.pfml.db.models.factories import AdminUserFactory


@pytest.fixture(scope="session")
def azure_auth_claims_unit():
    claims = {"exp": datetime.now() + timedelta(days=1), "sub": "foo", "aud": "client_id"}
    return claims


@pytest.fixture
def mock_valid_admin_user(azure_auth_claims_unit, initialize_factories_session):
    return AdminUserFactory.create(oauth_id=azure_auth_claims_unit["sub"])
