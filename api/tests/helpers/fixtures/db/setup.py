#
# Fixtures for setting up a PostgreSQL database schema for testing.
#
# The session scoped fixtures are the most efficient to use, as they initialize the database once per test session:
#
# - `db_schema_scope_session`: Creates a unique schema for the entire test session.
# - `test_db`: Initializes the database with all tables for the session.
# - `test_db_session`: A function-scoped fixture based on `test_db` that wraps the test's database operations in a
#    transaction, and rolls back after the test completes, for a clean state for each test.
#
# The other fixtures are for tests that are incompatible with the rollback method, but are less efficient.
#

import io
import os
import sys
import traceback
import uuid

import pytest
import sqlalchemy
from sqlalchemy.orm import sessionmaker

import massgov.pfml.util.logging
import tests.helpers.db

logger = massgov.pfml.util.logging.get_logger(f"massgov.{__name__}")


@pytest.fixture
def mock_db_session(mocker):
    return mocker.patch("sqlalchemy.orm.Session", autospec=True)


@pytest.fixture(scope="session")
def db_schema_name():
    """Generate a unique PostgreSQL schema name for this test session."""
    return f"api_test_session_{uuid.uuid4().int}"


@pytest.fixture(scope="module")
def db_schema_name_scope_module():
    """Generate a unique PostgreSQL schema name for a single test module."""
    return f"api_test_module_{uuid.uuid4().int}"


@pytest.fixture(scope="function")
def db_schema_name_scope_function():
    """Generate a unique PostgreSQL schema name for a single test function."""
    return f"api_test_function_{uuid.uuid4().int}"


@pytest.fixture(scope="session")
def db_config(db_schema_name):
    return massgov.pfml.db.config.DbConfig(
        # Note: host is not set, because it is different when running natively vs in Docker.
        dbname="pfml",
        username="pfml",
        password="secret123",
        schema=db_schema_name,
        use_iam_auth=False,
    )


@pytest.fixture(scope="module")
def db_config_scope_module(db_schema_name_scope_module):
    return massgov.pfml.db.config.DbConfig(
        dbname="pfml",
        username="pfml",
        password="secret123",
        schema=db_schema_name_scope_module,
        use_iam_auth=False,
    )


@pytest.fixture(scope="function")
def db_config_scope_function(db_schema_name_scope_function):
    return massgov.pfml.db.config.DbConfig(
        dbname="pfml",
        username="pfml",
        password="secret123",
        schema=db_schema_name_scope_function,
        use_iam_auth=False,
    )


@pytest.fixture(scope="session")
def db_admin_config(db_schema_name):
    return massgov.pfml.db.config.DbConfig(
        # Note: host is not set, because it is different when running natively vs in Docker.
        dbname="pfml",
        username="pfml",
        password="secret123",
        schema=db_schema_name,
        use_iam_auth=False,
    )


@pytest.fixture(scope="session", autouse=True)
def db_global_initialize_for_tests(db_admin_config):
    """Do one-off db initialization for tests, that would otherwise happen in migrations or RDS configuration."""
    # From 2022_02_03_23_08_12_1c912d1c655c_create_more_index_for_employees.py
    _exec_sql_admin(
        'CREATE EXTENSION IF NOT EXISTS "pg_trgm" WITH SCHEMA public; '
        'CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA public',
        db_admin_config,
    )


def _db_schema_create(db_config, db_admin_config):
    """Create a database schema."""
    db_test_user = db_config.username

    _exec_sql_admin(
        f"CREATE SCHEMA IF NOT EXISTS {db_config.schema_name} AUTHORIZATION {db_test_user};",
        db_admin_config,
    )
    logger.info("create schema %s", db_config.schema_name)


def _db_schema_drop(db_config, db_admin_config):
    """Drop a database schema."""
    _exec_sql_admin(f"DROP SCHEMA {db_config.schema_name} CASCADE;", db_admin_config)
    logger.info("drop schema %s", db_config.schema_name)


def _exec_sql_admin(sql, db_admin_config):
    engine = massgov.pfml.db.create_engine(config=db_admin_config)
    with engine.begin() as connection:
        connection.execute(sqlalchemy.text(sql))


@pytest.fixture(scope="session")
def db_schema_scope_session(
    has_external_dependencies, db_config, db_admin_config, monkeypatch_session
):
    _db_schema_create(db_config, db_admin_config)

    yield db_config

    _db_schema_drop(db_config, db_admin_config)


@pytest.fixture(scope="session")
def test_db(db_schema_scope_session):
    """Initialize the session scoped test database schema with all tables."""

    # not used directly, but loads models into Base
    import massgov.pfml.db as db
    import massgov.pfml.db.models.applications as applications  # noqa: F401
    import massgov.pfml.db.models.employees as employees  # noqa: F401
    from massgov.pfml.db.models.base import Base

    engine = db.create_engine(config=db_schema_scope_session)
    Base.metadata.create_all(bind=engine)

    db_session = db.init(config=db_schema_scope_session, sync_lookups=True)
    db_session.close()
    # db.init() does return a scoped_session, but is typed as a plain Session
    # for simplicity elsewhere, so ignore type issue
    db_session.remove()  # type: ignore

    return engine


@pytest.fixture(scope="session")
def _db_session_maker(test_db):
    return sessionmaker(autocommit=False, expire_on_commit=False)


@pytest.fixture
def test_db_session(test_db, _db_session_maker, request):
    # Based on https://docs.sqlalchemy.org/en/20/orm/session_transaction.html#joining-a-session-into-an-external-transaction-such-as-for-test-suites
    connection = test_db.connect()
    trans = connection.begin()
    session = _db_session_maker(bind=connection, join_transaction_mode="create_savepoint")

    yield session

    if request.session.testsfailed:
        try:
            stream = io.StringIO()
            tests.helpers.db.dump_tables(session, stream)
            request.node.add_report_section(
                "teardown", "test_db_session table dump", stream.getvalue()
            )
        except Exception as ex:
            print(f"caught exception when dumping database tables: {ex!r}", file=sys.stderr)
            traceback.print_exc()

    session.close()
    trans.rollback()
    connection.close()


@pytest.fixture
def test_db_other_session(test_db, _db_session_maker):
    # Based on https://docs.sqlalchemy.org/en/20/orm/session_transaction.html#joining-a-session-into-an-external-transaction-such-as-for-test-suites
    connection = test_db.connect()
    trans = connection.begin()
    session = _db_session_maker(bind=connection, join_transaction_mode="create_savepoint")

    yield session

    session.close()
    trans.rollback()
    connection.close()


@pytest.fixture
def initialize_factories_session(monkeypatch, test_db_session):
    monkeypatch.delenv("DB_FACTORIES_DISABLE_DB_ACCESS")

    import massgov.pfml.db.models.factories as factories

    logger.info("set factories db_session to %s", test_db_session)
    factories.db_session = test_db_session


@pytest.fixture(scope="function")
def db_schema_scope_function(
    has_external_dependencies, monkeypatch, db_config_scope_function, db_admin_config
):
    _db_schema_create(db_config_scope_function, db_admin_config)
    yield db_config_scope_function
    _db_schema_drop(db_config_scope_function, db_admin_config)


@pytest.fixture
def local_test_db(db_schema_scope_function):
    """
    Creates a test schema, directly creating all tables with SQLAlchemy. Schema
    is dropped after the test completes.
    """

    # not used directly, but loads models into Base
    import massgov.pfml.db as db
    import massgov.pfml.db.models.applications as applications  # noqa: F401
    import massgov.pfml.db.models.employees as employees  # noqa: F401
    import massgov.pfml.db.models.payments as payments  # noqa: F401
    from massgov.pfml.db.models.base import Base

    engine = db.create_engine(config=db_schema_scope_function)
    Base.metadata.create_all(bind=engine)

    db_session = db.init(
        config=db_schema_scope_function, sync_lookups=True, check_migrations_current=False
    )
    db_session.close()
    db_session.remove()

    return db_schema_scope_function


@pytest.fixture
def local_test_db_session(local_test_db):
    import massgov.pfml.db as db

    db_session = db.init(config=local_test_db, sync_lookups=False, check_migrations_current=False)

    yield db_session

    db_session.close()
    db_session.remove()


@pytest.fixture
def local_test_db_other_session(local_test_db):
    import massgov.pfml.db as db

    db_session = db.init(config=local_test_db, sync_lookups=False, check_migrations_current=False)

    yield db_session

    db_session.close()
    db_session.remove()


@pytest.fixture
def local_initialize_factories_session(monkeypatch, local_test_db_session):
    monkeypatch.delenv("DB_FACTORIES_DISABLE_DB_ACCESS")
    import massgov.pfml.db.models.factories as factories

    logger.info("set factories db_session to %s", local_test_db_session)
    factories.db_session = local_test_db_session


@pytest.fixture
def test_db_via_migrations(has_external_dependencies, db_schema_scope_function, logging_fix):
    """
    Creates a test schema, runs migrations through Alembic. Schema is dropped
    after the test completes.
    """
    from pathlib import Path

    from alembic import command
    from alembic.config import Config

    alembic_cfg = Config(
        os.path.join(
            os.path.dirname(__file__), "../../../../massgov/pfml/db/migrations/alembic.ini"
        )
    )
    # Change directory location so the relative script_location in alembic config works.
    os.chdir(Path(__file__).parent.parent.parent.parent.parent)
    alembic_cfg.set_main_option(
        "sqlalchemy.url",
        massgov.pfml.db.make_connection_uri(db_schema_scope_function).replace("%", "%%"),
    )

    command.upgrade(alembic_cfg, "head")

    return db_schema_scope_function


@pytest.fixture(scope="module")
def db_schema_scope_module(
    has_external_dependencies, monkeypatch, db_config_scope_module, db_admin_config
):
    _db_schema_create(db_config_scope_module, db_admin_config)
    yield db_config_scope_module
    _db_schema_drop(db_config_scope_module, db_admin_config)


@pytest.fixture(scope="module")
def module_persistent_db(
    has_external_dependencies, monkeypatch_module, request, db_config_scope_module, db_admin_config
):
    # not used directly, but loads models into Base
    import massgov.pfml.db as db
    import massgov.pfml.db.models.applications as applications  # noqa: F401
    import massgov.pfml.db.models.employees as employees  # noqa: F401
    from massgov.pfml.db.models.base import Base

    logger.info("use persistent test db for module %s", request.module.__name__)

    # monkeypatch_module.setenv("DB_SCHEMA", schema_name)
    _db_schema_create(db_config_scope_module, db_admin_config)

    engine = db.create_engine(config=db_config_scope_module)
    Base.metadata.create_all(bind=engine)

    db_session = db.init(config=db_config_scope_module, sync_lookups=True)

    try:
        yield db_config_scope_module
    finally:
        db_session.close()
        db_session.remove()
        _db_schema_drop(db_config_scope_module, db_admin_config)


@pytest.fixture
def module_persistent_db_session(module_persistent_db, monkeypatch):
    import massgov.pfml.db as db
    import massgov.pfml.db.models.factories as factories

    db_session = db.init(config=module_persistent_db, sync_lookups=False)

    monkeypatch.delenv("DB_FACTORIES_DISABLE_DB_ACCESS")

    logger.info("set factories db_session to %s", db_session)
    factories.db_session = db_session

    yield db_session

    db_session.close()
    db_session.remove()
