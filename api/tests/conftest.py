"""
conftest.py files are automatically loaded by pytest, they are an easy place to
put shared test fixtures as well as define other pytest configuration (define
hooks, load plugins, define new/override assert behavior, etc.).

More info:
https://docs.pytest.org/en/latest/fixture.html#conftest-py-sharing-fixture-functions
"""

import os
from pathlib import Path

import pytest

import massgov.pfml.api.app
import massgov.pfml.api.authentication as authentication

####################################################################################
# Autouse fixtures                                                                 #
#                                                                                  #
# Since these are defined here in the top-level conftest file, they apply globally #
# to all tests.                                                                    #
####################################################################################


@pytest.fixture(autouse=True, scope="session")
def set_no_db_factories_alert():
    """By default, ensure factories do not attempt to access the database.

    The tests that need generated models to actually hit the database can pull
    in the `initialize_factories_session` fixture to their test case to enable
    factory writes to the database.
    """
    os.environ["DB_FACTORIES_DISABLE_DB_ACCESS"] = "1"


@pytest.fixture(autouse=True, scope="session")
def ensure_mock_fineos_client():
    """When creating FINEOS client in test environment, we call use real FINEOS URL.
    We set FINEOS_CLIENT_CUSTOMER_API_URL to none, to force use of mock_fineos_client, in test environment.
    """
    os.environ["FINEOS_CLIENT_CUSTOMER_API_URL"] = ""


@pytest.fixture(autouse=True, scope="session")
def ensure_mock_pdf_api(monkeypatch_session):
    monkeypatch_session.setenv("PDF_API_HOST", "")


@pytest.fixture(autouse=True, scope="session")
def ensure_mock_my_mass_gov_api(monkeypatch_session):
    monkeypatch_session.setenv("MY_MASS_GOV_API_CLIENT_MOCK_CALLS", "true")


@pytest.fixture(autouse=True, scope="session")
def disable_settings_env_file(monkeypatch_session: pytest.MonkeyPatch) -> None:
    import massgov.pfml.util.pydantic

    monkeypatch_session.setattr(
        massgov.pfml.util.pydantic.PydanticBaseSettings.Config, "env_file", ""
    )


@pytest.fixture
def enable_settings_env_file(monkeypatch: pytest.MonkeyPatch) -> None:
    import massgov.pfml.util.pydantic
    from massgov.pfml.util.config import get_env_files

    monkeypatch.setattr(
        massgov.pfml.util.pydantic.PydanticBaseSettings.Config, "env_file", get_env_files()
    )


####################################################################################
# Regular fixtures                                                                 #
#                                                                                  #
# A large amount of fixtures are grouped in their own modules under                #
# tests/helpers/fixtures/ and imported at the bottom of this file as plugins       #
# (which is effectively like defining them here in this file). But general/misc.   #
# fixtures can be defined here.                                                    #
####################################################################################


@pytest.fixture(scope="session")
def initialize_feature_config():
    import massgov.pfml.features

    # code under test may also call this, but to make using the feature_config
    # more flexible/less dependent on the order fixture are declared, explicitly
    # initialize things for the test session
    massgov.pfml.features.initialize()


@pytest.fixture
def feature_config(initialize_feature_config, monkeypatch):
    import massgov.pfml.features

    base_config = massgov.pfml.features._config

    test_config = base_config.copy()

    monkeypatch.setattr(massgov.pfml.features, "_config", test_config)

    return test_config


@pytest.fixture
def test_fs_path(tmp_path):
    file_name = "test.txt"
    content = "line 1 text\nline 2 text\nline 3 text"

    test_folder = tmp_path / "test_folder"
    test_folder.mkdir()
    test_file = test_folder / file_name
    test_file.write_text(content)
    return test_folder


@pytest.fixture
def mock_sftp_paths(monkeypatch):
    source_directory_path = "dua/pending"
    archive_directory_path = "dua/archive"
    moveit_pickup_path = "upload/DFML/DUA/Inbound"

    monkeypatch.setenv("DUA_TRANSFER_BASE_PATH", "local_s3/agency_transfer")
    monkeypatch.setenv("OUTBOUND_DIRECTORY_PATH", source_directory_path)
    monkeypatch.setenv("ARCHIVE_DIRECTORY_PATH", archive_directory_path)
    monkeypatch.setenv("MOVEIT_SFTP_URI", "sftp://<EMAIL>")
    monkeypatch.setenv("MOVEIT_SSH_KEY", "foo")

    pending_directory = f"local_s3/agency_transfer/{source_directory_path}"
    archive_directory = f"local_s3/agency_transfer/{archive_directory_path}"

    paths = {
        "pending_directory": pending_directory,
        "archive_directory": archive_directory,
        "moveit_pickup_path": moveit_pickup_path,
    }

    return paths


@pytest.fixture
def mock_sftp_client():
    from tests.helpers.sftp import MockSftpClient

    return MockSftpClient()


@pytest.fixture
def setup_mock_sftp_client(monkeypatch, mock_sftp_client):
    import massgov.pfml.util.files as file_util

    # Mock SFTP client so we can inspect the method calls we make later in the test.
    monkeypatch.setattr(
        file_util, "get_sftp_client", lambda uri, ssh_key_password, ssh_key: mock_sftp_client
    )


@pytest.fixture
def set_env_to_local(monkeypatch):
    # this should always be the case for the tests, but the when testing
    # behavior that depends on the ENVIRONMENT value, best set it explicitly, to
    # be sure we test the correct behavior
    monkeypatch.setenv("ENVIRONMENT", "local")


@pytest.fixture
def mock_pdf_api_client():
    import massgov.pfml.pdf_api as pdf_api

    return pdf_api.mock_client.MockPDFClient()


@pytest.fixture
def mock_nr_api_client():
    import massgov.pfml.new_relic_dashboard.nr_api.mock_client as mock_client

    return mock_client.MockNewRelicClient()


####################################################################################
# App config fixtures                                                              #
####################################################################################


@pytest.fixture(scope="session")
def auth_key():
    hmac_key = {
        "alg": "RS256",
        "e": "AQAB",
        "kid": "c7f7e776-bd29-4d00-b110-d4d4a8652815",
        "kty": "RSA",
        "n": "iWBm-DQbycUqrPBSD5yk73zxyIr66hBUCyPCShW-btQ-nyBk1E-h4AvtqHpl4Y1aghQDTnn2gLHiRtV_XJtCpK1PEJ3SCqw6wGOEw5bbG7Q88KDvTMUF5k6gzRMHMBTD7lMNPIY-oCuh_Rwvg19hGBD2O6rA2sMHyTB-O2ZwL6M",
        "use": "sig",
    }

    return hmac_key


@pytest.fixture
def set_auth_public_keys(monkeypatch, auth_key):
    monkeypatch.setattr(authentication, "public_keys", auth_key)


@pytest.fixture(scope="session")
def app_config_scope_session(db_config):
    """Create an app config for this test session."""
    return massgov.pfml.api.config.AppConfig(
        environment="local",
        db=db_config,
    )


@pytest.fixture(scope="session")
def base_app(app_config_scope_session):
    return massgov.pfml.api.app.create_app(
        config=app_config_scope_session,
        check_migrations_current=False,
        db_session_factory=None,
        do_close_db=False,
        use_db_factories_session=True,
    )


@pytest.fixture
def app(test_db_session, initialize_factories_session, set_auth_public_keys, base_app):
    return base_app


#################################################################################
# Utilities                                                                     #
#                                                                               #
# More internal test things, stuff primarily used for setting up other fixtures #
# rather than used directly, etc.                                               #
#################################################################################


@pytest.fixture(scope="session")
def has_external_dependencies():
    """
    Use this fixture to automatically mark all tests that are in the downline
    of fixtures or tests that request this fixture.
    """
    pass


# From https://github.com/pytest-dev/pytest/issues/363
@pytest.fixture(scope="session")
def monkeypatch_session(request):
    with pytest.MonkeyPatch.context() as mp:
        yield mp


# From https://github.com/pytest-dev/pytest/issues/363
@pytest.fixture(scope="module")
def monkeypatch_module(request):
    with pytest.MonkeyPatch.context() as mp:
        yield mp


######################
# Pytest setup stuff #
######################


@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """Format output for GitHub Actions.

    See https://docs.github.com/en/free-pro-team@latest/actions/reference/workflow-commands-for-github-actions
    """
    yield

    if "CI" not in os.environ:
        return

    for report in terminalreporter.stats.get("failed", []):
        print(
            "::error file=api/%s,line=%s::%s %s\n"
            % (
                report.location[0],
                report.location[1],
                report.location[2],
                report.longrepr.reprcrash.message,
            )
        )


@pytest.hookimpl(hookwrapper=True, tryfirst=True)
def pytest_collection_modifyitems(items: list[pytest.Item]):
    """Automatically mark integration tests.

    Automatically marks any test with the has_external_dependencies fixture in its
    fixture request graph as an integration test.

    Other markers could be established here as well.
    """

    for item in items:
        if "has_external_dependencies" in item.fixturenames:
            item.add_marker(pytest.mark.integration)

    yield


@pytest.fixture(autouse=True)
def mock_usbank(monkeypatch):
    """
    Automatically set required USBankClientConfig environment variables for all tests.
    """
    monkeypatch.setenv("USBANK_CLIENT_BASE_URL", "https://3.ghi.test/oauth2/token")
    monkeypatch.setenv("USBANK_CLIENT_SOAP_BASE_URL", "https://1.ghi.test/oauth2/token")
    monkeypatch.setenv("USBANK_CLIENT_CERTIFICATE_BINARY_ARN", "1234567890abcdefghij")
    monkeypatch.setenv("USBANK_CLIENT_CERTIFICATE_PASSWORD", "abcdefghijklmnopqrstuvwxyz")


pytest.register_assert_rewrite("tests.helpers")

pytest_plugins = (
    "tests.helpers.fixtures.aws",
    "tests.helpers.fixtures.logging",
    "tests.helpers.fixtures.oauth",
    "tests.helpers.fixtures.db.check_order_by",
    "tests.helpers.fixtures.db.models",
    "tests.helpers.fixtures.db.setup",
    "tests.helpers.fixtures.db.util",
    "tests.helpers.fixtures.experian",
    "tests.helpers.fixtures.dynamic_config",
    "tests.helpers.fixtures.admin",
)


@pytest.fixture
def snapshot_file(request):
    file_name = request.module.__file__
    test_name = request.node.name
    return os.path.join(
        os.path.dirname(file_name), f"./snapshots/{Path(file_name).stem}__{test_name}.sql"
    )


@pytest.fixture
def fineos_pre_v24_feature_config(feature_config):
    conf = feature_config
    conf.fineos.is_running_v24 = False
    return conf


@pytest.fixture
def fineos_v24_feature_config(feature_config):
    conf = feature_config
    conf.fineos.is_running_v24 = True
    return conf
