import argparse
import sys
from typing import List

from massgov.pfml.util.admin.update_errored_child_support_payments_util import (
    UpdateErroredChildSupport,
)
from massgov.pfml.util.bg import background_task


def _parse_script_args(args: List[str]) -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Sets child support payments to errored state")

    parser.add_argument(
        "--payment_id", type=str, required=True, help="(Required) The payment ID of the request"
    )

    parser.add_argument(
        "--writeback_status_id",
        type=str,
        required=False,
        default="2",
        help="The writeback status ID.",
    )
    parser.add_argument(
        "--state_log_id",
        type=str,
        required=True,
        help="The state log ID.",
    )
    parser.add_argument(
        "--end_state_id",
        type=str,
        required=False,
        default="223",
        help="The end state ID to set the payment to in the state log.",
    )

    parser.add_argument(
        "--outcome",
        type=str,
        required=False,
        help="The outcome string to add to the state log.",
    )

    parser.add_argument(
        "--dry_run",
        type=str,
        default="True",
        help="(Optional Defaults to 'True'. \
            Set this to 'False' to allow the script to commit changes to the PFML db",
    )

    parser.add_argument(
        "--env",
        type=str,
        default="test",
        help="(Optional Defaults to 'test'. \
            Set this to 'test' to allow updates on all tables except two that are only in prod with new employer id",
    )

    return parser.parse_args(args)


@background_task("update-errored-child-support-payments")
def main() -> None:

    args = sys.argv[1:]
    parsed_args = _parse_script_args(args)

    payment_id = parsed_args.payment_id
    writeback_status_id = parsed_args.writeback_status_id
    state_log_id = parsed_args.state_log_id
    end_state_id = parsed_args.end_state_id
    outcome = parsed_args.outcome
    dry_run = not (parsed_args.dry_run.lower() == "false")
    env = parsed_args.env

    if None not in [payment_id, writeback_status_id, state_log_id, end_state_id, outcome]:
        update_payment = UpdateErroredChildSupport(
            payment_id, writeback_status_id, state_log_id, end_state_id, outcome, dry_run, env
        )
        update_payment.run()

    else:
        print(
            "Missing arguments. Please provide all required arguments: '--payment_id, --state_log_id, --outcome'."
        )
        return
