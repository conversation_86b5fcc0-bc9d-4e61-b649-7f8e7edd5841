import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.models.employees import StateLog
from massgov.pfml.db.models.payments import FineosWritebackDetails

logger = logging.get_logger(__name__)


class UpdateErroredChildSupport:
    log_attr: dict
    payment_id: str
    writeback_status_id: str
    state_log_id: str
    end_state_id: str
    outcome: str
    should_commit_changes: bool
    env: str

    def __init__(
        self,
        payment_id,
        writeback_status_id,
        state_log_id,
        end_state_id,
        outcome,
        dry_run,
        env,
        db_session=None,
    ):

        self.log_attr = {
            "dry_run": dry_run,
            "payment_id": payment_id,
            "writeback_status_id": writeback_status_id,
            "state_log_id": state_log_id,
            "end_state_id": end_state_id,
            "outcome": outcome,
            "env": env,
        }

        if dry_run:
            logger.info("***DRY RUN MODE ENABLED***")
        else:
            logger.info("***DRY RUN MODE DISABLED***")
        self.payment_id = payment_id
        self.writeback_status_id = writeback_status_id
        self.state_log_id = state_log_id
        self.end_state_id = end_state_id
        self.outcome = outcome
        self.should_commit_changes = not dry_run
        self.env = env
        self.db_session = db.init(sync_lookups=True) if not db_session else db_session

    def run(self):

        logger.info("Starting ECS Task: Update Errored Child Support Payments", extra=self.log_attr)

        writeback_details = FineosWritebackDetails(
            payment_id=self.payment_id, transaction_status_id=self.writeback_status_id
        )
        state_log = (
            self.db_session.query(StateLog)
            .filter(
                StateLog.state_log_id == self.state_log_id,
            )
            .first()
        )
        if not state_log:
            logger.error(
                f"StateLog with ID {self.state_log_id} not found.",
                extra=self.log_attr,
            )
            return

        state_log.end_state_id = self.end_state_id
        state_log.outcome = self.outcome

        if self.should_commit_changes:
            self.db_session.add(writeback_details)
            self.db_session.add(state_log)
            self.db_session.commit()
            logger.info(
                f"StateLog {state_log.state_log_id} updated with end state {self.end_state_id} and outcome '{self.outcome}'",
                extra=self.log_attr,
            )
            logger.info(
                f"FineosWritebackDetails for payment {self.payment_id} created with status {self.writeback_status_id}",
                extra=self.log_attr,
            )
        else:
            logger.info(
                f"DRY RUN: StateLog {state_log.state_log_id} would be updated with end state {self.end_state_id} and outcome '{self.outcome}'",
                extra=self.log_attr,
            )
            logger.info(
                f"DRY RUN: FineosWritebackDetails for payment {self.payment_id} would be created with status {self.writeback_status_id}",
                extra=self.log_attr,
            )

        logger.info(
            "Completed ECS Task: Update Errored Child Support Payments", extra=self.log_attr
        )
