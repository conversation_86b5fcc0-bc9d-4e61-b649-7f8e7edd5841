import uuid
from datetime import datetime
from typing import Tuple

from sqlalchemy import exc, text

import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.models.base import uuid_gen
from massgov.pfml.db.models.dor import EmployerDORExemption, FineosServiceAgreement
from massgov.pfml.db.models.employees import (
    Address,
    BenefitYearContribution,
    Claim,
    DuaReportingUnit,
    EmployeeAddress,
    EmployeeOccupation,
    EmployeePushToFineosQueue,
    Employer,
    EmployerAddress,
    EmployerPushToFineosQueue,
    EmployerQuarterlyContribution,
    HealthCareProviderAddress,
    LeaveAdminVerificationAttempt,
    OrganizationUnit,
    UserLeaveAdministrator,
    WagesAndContributions,
)
from massgov.pfml.db.models.payments import MmarsPaymentData

logger = logging.get_logger(__name__)


class UpdateUUIDResolver:

    log_attr: dict
    should_commit_changes: bool
    commit_exception_occurred: bool
    employer_fein: str
    env: str

    def __init__(
        self,
        psd_number,
        employer_fein,
        dry_run,
        env,
        db_session=None,
    ):

        self.log_attr = {
            "dry_run": dry_run,
            "psd_ticket_number": psd_number,
            "employer_fein": employer_fein,
        }

        if dry_run:
            self._log_info("***DRY RUN MODE ENABLED***")
        else:
            self._log_info("***DRY RUN MODE DISABLED***")

        self.db_session = db.init(sync_lookups=True) if not db_session else db_session

        self.should_commit_changes = not dry_run

        self.commit_exception_occurred = False

        self.employer_fein = employer_fein

        self.env = env

    def run(self):

        self._log_info("Task initiated.")
        (
            existing_employer_record,
            existing_employer_address_record,
        ) = self._get_existing_records_for_the_employer_fein(self.employer_fein)

        self._process_update_uuid(existing_employer_record, existing_employer_address_record)

        self._log_info("Completed")

    def _process_update_uuid(
        self, existing_employer_record: Employer, existing_employer_address_record: Address
    ) -> None:

        self._log_info("Starting the process")
        self._log_info("Creating new employer and address uuids.")

        new_employer_id = uuid_gen()
        new_address_id = uuid_gen()

        temp_fineos_employer_id = "88888888"
        temp_mtc_number = "88888888"

        if existing_employer_address_record.address_line_two is None:
            existing_employer_address_record.address_line_two = "null"

        fineos_emp_id = existing_employer_record.fineos_employer_id
        mtc_number = existing_employer_record.mtc_number

        self.db_session.begin_nested()

        self.update_fineos_employer_id(temp_fineos_employer_id, temp_mtc_number)

        # Copy existing employer and address info and add new uuids
        new_employer_record = existing_employer_record.copy()
        new_employer_record.employer_id = new_employer_id
        new_employer_record.fineos_employer_id = fineos_emp_id
        new_employer_record.mtc_number = mtc_number

        self.db_session.add(new_employer_record)

        new_address_record = existing_employer_address_record.copy()
        new_address_record.address_id = new_address_id

        self.db_session.add(new_address_record)

        new_employer_address_record = EmployerAddress(
            employer_id=new_employer_id, address_id=new_address_id
        )
        self.db_session.add(new_employer_address_record)

        self.db_session.commit()
        self._log_info("Adding 3 rows to database")

        if not self.should_commit_changes:
            self._log_info("dry run")
            self.db_session.autoflush = False

        self._update_employer_uuid(existing_employer_record.employer_id, new_employer_id)
        self._update_employer_address_uuid(
            existing_employer_address_record.address_id, new_address_id
        )

        if self.should_commit_changes:
            try:
                self.db_session.commit()
                self._log_info("Now deleting old records")
                self._delete_employer_and_address_record(
                    existing_employer_record.employer_id,
                    existing_employer_address_record.address_id,
                )
                self.db_session.commit()
                logger.info("Successfully updated with new UUIDs", extra=self.log_attr)

            except exc.SQLAlchemyError as ex:
                self._log_info("In exception")
                self.db_session.rollback()

                self._delete_employer_and_address_record(new_employer_id, new_address_id)
                self.update_fineos_employer_id(fineos_emp_id, mtc_number)
                self.db_session.commit()
                self._log_info("Deleted new records created because of error!")
                logger.warning("Unable to update UUIDs to database", exc_info=ex)

            self._log_info("...Done!")
        else:
            self._log_info("Dry run")
            self._log_info(str(new_employer_id))
            self.db_session.rollback()
            self._delete_employer_and_address_record(new_employer_id, new_address_id)
            self.update_fineos_employer_id(fineos_emp_id, mtc_number)
            self._log_info("Completed running process in dry run mode, nothing committed to db")
        self.db_session.close()

    def _get_existing_records_for_the_employer_fein(
        self, employer_fein: str
    ) -> Tuple[Employer, Address]:

        self._log_info("Querying to find employer")

        employer = (
            self.db_session.query(Employer)
            .filter(Employer.employer_fein == employer_fein)
            .one_or_none()
        )

        if employer:
            self._log_info("Employer record found")
            employer_link_address = (
                self.db_session.query(EmployerAddress)
                .filter(EmployerAddress.employer_id == employer.employer_id)
                .one()
            )

            self._log_info("Existing employer_link_address found")

            employer_address = (
                self.db_session.query(Address)
                .filter(Address.address_id == employer_link_address.address_id)
                .one()
            )

            self._log_info("Address found for employer")
        else:
            e = Exception("Unable to find employer with the given employer fein")
            self._log_error("Error finding employer in PFML database", e)
            raise e

        return employer, employer_address

    def _get_records_for_the_fineos_employer_id(
        self, fineos_employer_id: str
    ) -> Tuple[Employer, Address]:

        self._log_info("Querying to find employer using employer id")

        employer = (
            self.db_session.query(Employer)
            .filter(Employer.fineos_employer_id == fineos_employer_id)
            .one_or_none()
        )

        if employer:
            self._log_info("Employer record found")
            employer_link_address = (
                self.db_session.query(EmployerAddress)
                .filter(EmployerAddress.employer_id == employer.employer_id)
                .one()
            )

            self._log_info("Existing employer_link_address found")

            employer_address = (
                self.db_session.query(Address)
                .filter(Address.address_id == employer_link_address.address_id)
                .one()
            )
            self._log_info("Address found for employer")
        else:
            e = Exception(
                "Unable to find employer with the given employer fein and fineos employer id"
            )
            self._log_error("Error finding employer in PFML database", e)
            raise e

        return employer, employer_address

    # Doing this to bypass the unique constraint 'ix_employer_fineos_employer_id' on fineos_employer_
    # id and mtc_uq_employer_mtc_number on mtc_number in Employer table.
    # Alternate is to drop the constraint as shown in the commented code below
    #
    def update_fineos_employer_id(self, fineos_emp_id, mtc_number):

        self._log_info("Updating Employer fineos employer id")
        self._log_info(fineos_emp_id)
        self.db_session.query(Employer).filter(Employer.employer_fein == self.employer_fein).update(
            {
                Employer.fineos_employer_id: fineos_emp_id,
                Employer.mtc_number: mtc_number,
            }
        )
        self.db_session.commit()
        self._log_info("Updated Employer fineos id before inserting a new employer record")

    def _update_employer_address_uuid(
        self, existing_employer_address_id: uuid.UUID, new_address_id: uuid.UUID
    ) -> None:

        self._log_info("Updating related foreign key with new address id")

        self.db_session.query(EmployeeAddress).filter(
            EmployeeAddress.address_id == existing_employer_address_id
        ).update(
            {
                EmployeeAddress.address_id: new_address_id,
            }
        )

        self.db_session.query(HealthCareProviderAddress).filter(
            HealthCareProviderAddress.address_id == existing_employer_address_id
        ).update(
            {
                HealthCareProviderAddress.address_id: new_address_id,
            }
        )
        self.db_session.query(MmarsPaymentData).filter(
            MmarsPaymentData.address_id == str(existing_employer_address_id)
        ).update(
            {
                MmarsPaymentData.address_id: str(new_address_id),
            }
        )
        # mmars_payment_data_stage - table not found in lower regions
        # add prod only tables here
        if self.env == "prod":
            # Use text() with bound parameters for security - table name is validated constant
            query_object = text(
                "UPDATE mmars_payment_data_stage SET address_id = :new_address_id WHERE address_id = :existing_employer_address_id"
            )
            self.db_session.execute(
                query_object,
                {
                    "new_address_id": str(new_address_id),
                    "existing_employer_address_id": str(existing_employer_address_id),
                },
            )

        self._log_info("Updated address UUID of related tables")

    def _update_employer_uuid(
        self, existing_employer_id: uuid.UUID, new_employer_id: uuid.UUID
    ) -> None:

        self._log_info("Starting foreign key updates of employer uuid of related  tables")

        self.db_session.query(BenefitYearContribution).filter(
            BenefitYearContribution.employer_id == existing_employer_id
        ).update(
            {
                BenefitYearContribution.employer_id: new_employer_id,
            }
        )
        self._log_info("updated BenefitYearContribution table")
        self.db_session.query(Claim).filter(Claim.employer_id == existing_employer_id).update(
            {
                Claim.employer_id: new_employer_id,
            }
        )
        self._log_info("updated Claim table")
        self.db_session.query(DuaReportingUnit).filter(
            DuaReportingUnit.employer_id == existing_employer_id
        ).update(
            {
                DuaReportingUnit.employer_id: new_employer_id,
            }
        )
        self._log_info("updated DuaReortingUnit table")
        self.db_session.query(EmployeeOccupation).filter(
            EmployeeOccupation.employer_id == existing_employer_id
        ).update(
            {
                EmployeeOccupation.employer_id: new_employer_id,
            }
        )
        self._log_info("updated EmployeeOccupation table")
        self.db_session.query(EmployeePushToFineosQueue).filter(
            EmployeePushToFineosQueue.employer_id == existing_employer_id
        ).update(
            {
                EmployeePushToFineosQueue.employer_id: new_employer_id,
            }
        )
        self._log_info("updated EmployeePushToFineosQueue table")
        self.db_session.query(EmployerPushToFineosQueue).filter(
            EmployerPushToFineosQueue.employer_id == existing_employer_id
        ).update(
            {
                EmployerPushToFineosQueue.employer_id: new_employer_id,
            }
        )
        self._log_info("updated EmployerPushToFineosQueue table")
        self.db_session.query(EmployerQuarterlyContribution).filter(
            EmployerQuarterlyContribution.employer_id == existing_employer_id
        ).update(
            {
                EmployerQuarterlyContribution.employer_id: new_employer_id,
            }
        )
        self._log_info("updated EmployerQuarterlyContribution table")
        self.db_session.query(OrganizationUnit).filter(
            OrganizationUnit.employer_id == existing_employer_id
        ).update(
            {
                OrganizationUnit.employer_id: new_employer_id,
            }
        )
        self._log_info("updated OrganizationUnit table")
        self.db_session.query(WagesAndContributions).filter(
            WagesAndContributions.employer_id == existing_employer_id
        ).update(
            {
                WagesAndContributions.employer_id: new_employer_id,
            }
        )
        self._log_info("updated WagesAndContributions table")
        self.db_session.query(LeaveAdminVerificationAttempt).filter(
            LeaveAdminVerificationAttempt.employer_id == existing_employer_id
        ).update(
            {
                LeaveAdminVerificationAttempt.employer_id: new_employer_id,
            }
        )
        self._log_info("updated LeaveAdminVerificationAttempt table")
        self.db_session.query(UserLeaveAdministrator).filter(
            UserLeaveAdministrator.employer_id == existing_employer_id
        ).update(
            {
                UserLeaveAdministrator.employer_id: new_employer_id,
            }
        )
        self._log_info("updated UserLeaveAdministrator table")
        self.db_session.query(EmployerDORExemption).filter(
            EmployerDORExemption.employer_id == existing_employer_id
        ).update(
            {
                EmployerDORExemption.employer_id: new_employer_id,
            }
        )
        self._log_info("updated EmployerDORExemption table")
        self.db_session.query(FineosServiceAgreement).filter(
            FineosServiceAgreement.employer_id == existing_employer_id
        ).update(
            {
                FineosServiceAgreement.employer_id: new_employer_id,
            }
        )
        self._log_info("updated FineosServiceAgreement table")

        # employer_log_backup, employee_occupation_20211006 - table not found in lower regions
        # add prod only tables here and then in the loop below
        prod_only_tables = [
            "employer_log_backup",
            "employee_occupation_20211006",
        ]

        if self.env == "prod":
            # Use individual queries for security - table names are validated constants
            for table_name in prod_only_tables:
                if table_name == "employer_log_backup":
                    query_object = text(
                        "UPDATE employer_log_backup SET employer_id = :new_employer_id WHERE employer_id = :existing_employer_id"
                    )
                elif table_name == "employee_occupation_20211006":
                    query_object = text(
                        "UPDATE employee_occupation_20211006 SET employer_id = :new_employer_id WHERE employer_id = :existing_employer_id"
                    )
                else:
                    # Skip unknown table names for security
                    continue

                self.db_session.execute(
                    query_object,
                    {
                        "new_employer_id": str(new_employer_id),
                        "existing_employer_id": str(existing_employer_id),
                    },
                )
            self._log_info("Updated employer UUID in prod only tables")

        self._log_info("Updated employer UUID of related tables")

    def _delete_employer_and_address_record(self, employer_id, employer_address_id):

        self._log_info(
            "Deleting employer and address records after adding new records with new uuids"
        )
        self._log_info(employer_id)

        self.db_session.query(EmployerAddress).filter(
            EmployerAddress.employer_id == employer_id,
            EmployerAddress.address_id == employer_address_id,
        ).delete()

        self.db_session.query(Address).filter(Address.address_id == employer_address_id).delete()

        self.db_session.query(Employer).filter(Employer.employer_id == employer_id).delete()

        self._log_info("Deleting employer and address records for employer_id")
        self._log_info(employer_id)

    def _log_info(self, message: str) -> None:
        """Helper for adding metadata to each log statement"""
        logger.info(message, extra=self.log_attr)

    def _log_error(self, message: str, error: Exception) -> None:
        """Helper for adding metadata to each log statement"""
        logger.error(message, exc_info=error, extra=self.log_attr)

    def _update_employer_record(self, existing_employer_id):
        # keeping this for future reference
        """removing updates
        self._log_info(
            f"Updating employer {existing_employer_address_id} and address records after adding new records with new uuids {new_employer_id}"
        )
        self._log_info(existing_employer_id)

        self.db_session.query(EmployerAddress).filter(
            EmployerAddress.employer_id == existing_employer_id
            and EmployerAddress.address_id == existing_employer_address_id,
        ).update(
            {
                EmployerAddress.employer_id: new_employer_id,
            }
        )

        self.db_session.query(Address).filter(
            Address.address_id == existing_employer_address_id
        ).update(
            {
                Address.address_id: new_address_id,
            }
        """

        # though this if condition is not required, keeping it for future reference

        if self.should_commit_changes and not self.commit_exception_occurred:
            self.db_session.query(Employer).filter(
                Employer.employer_id == existing_employer_id
            ).update({Employer.account_key: None, Employer.updated_at: datetime.now()})
