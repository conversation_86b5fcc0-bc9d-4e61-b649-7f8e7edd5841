"""
Dynamic Configuration Utility functions.
For spec and more, see https://lwd.atlassian.net/wiki/spaces/API/pages/3962372104
"""

from datetime import datetime, timedelta
from threading import Lock

import massgov.pfml.db as db
import massgov.pfml.util.logging as logging
from massgov.pfml.db.models.dynamic_configuration import DynamicConfig
from massgov.pfml.db.queries.dynamic_config import get_all_dynamic_config_entries

logger = logging.get_logger(__name__)

DYNAMIC_CONFIG_CACHE_EXPIRY_MINUTES = 5

_cache_lock = Lock()


def _reset_cache() -> tuple[dict[tuple[str, str], DynamicConfig], datetime]:
    """Reset the dynamic configuration cache."""
    global _cached_config, _cache_expiry_date
    _cached_config = dict[tuple[str, str], DynamicConfig]()
    _cache_expiry_date = datetime.min
    return _cached_config, _cache_expiry_date


# Initialize the global values for caching
_cached_config, _cache_expiry_date = _reset_cache()


def _need_to_load_config() -> bool:
    """Check if the dynamic configuration needs to be loaded."""
    return datetime.now() >= _cache_expiry_date


def _load_config_from_db(db_session: db.Session) -> dict[tuple[str, str], DynamicConfig]:
    logger.info("Loading dynamic config from database")
    return {
        (config.config_key, config.config_context): config
        for config in get_all_dynamic_config_entries(db_session)
    }


def _get_dynamic_config_list(db_session: db.Session) -> dict[tuple[str, str], DynamicConfig]:
    """Get dynamic configuration values from cache or database."""

    global _cached_config, _cache_expiry_date

    if _need_to_load_config():
        # If cache is empty or expired, fetch from database
        with _cache_lock:
            if _need_to_load_config():  # re-check after acquiring the lock
                _cached_config = _load_config_from_db(db_session)
                _cache_expiry_date = datetime.now() + timedelta(
                    minutes=DYNAMIC_CONFIG_CACHE_EXPIRY_MINUTES
                )

    return _cached_config


def get_dynamic_config_value(
    db_session: db.Session,
    config_key: str,
    config_context: str | None = None,
    default: str | None = None,
) -> str | None:
    """
    Get a dynamic configuration value.
    Values from the database are cached to minimize database load.

    :param config_key: The key of the configuration value to retrieve.
    :param config_context: Optional context for the configuration key.
    :param default: Default value to return if the key/context is not found.
    :return: The configuration value or the default if not found.
    """

    adjusted_context = config_context or ""

    # Get cached dynamic configuration list
    dynamic_config_list = _get_dynamic_config_list(db_session)

    config_key_tuple = (config_key, adjusted_context)

    # Check if the key exists in the cached dynamic configuration
    if config_key_tuple in dynamic_config_list:
        return dynamic_config_list[config_key_tuple].config_value

    # If no specific context match, check for a match with a blank context
    config_key_tuple = (config_key, "")
    if config_key_tuple in dynamic_config_list:
        return dynamic_config_list[config_key_tuple].config_value

    # If the key is not found in the dynamic configuration, return the default
    return default
