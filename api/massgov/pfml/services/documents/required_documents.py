from typing import cast

from massgov.pfml.api import app
from massgov.pfml.api.constants.documents import ABSENCE_REASON_TO_CERTIFICATION_DOC_TYPE_MAPPING
from massgov.pfml.api.models.documents.common import (
    DocumentRequirement,
    DocumentRequirementCategory,
    DocumentType,
    DocumentTypeRequirement,
)
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.api.services import fineos_actions
from massgov.pfml.db.lookup_data.absences import AbsenceReason
from massgov.pfml.db.lookup_data.applications import LeaveReason
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.services.documents.get_document_service import (
    BaseDocumentService,
    GetDocumentService,
)


class DocumentRequirementService(BaseDocumentService):
    def __init__(self, db_session=None, fineos=None):
        super().__init__(db_session=db_session, fineos=fineos)
        self._get_document_service = None

    @property
    def get_document_service(self) -> GetDocumentService:
        if self._get_document_service is None:
            self._get_document_service = GetDocumentService(db_session=self.db_session)
        return self._get_document_service

    def are_all_required_documents_received(
        self, application: Application, fineos_web_id: str | None = None
    ) -> bool:
        # An application without a claim is not in a state to have documents.
        if application.claim is None:
            return False

        document_types = self.get_unreceived_required_document_types(application, fineos_web_id)
        return len(document_types) == 0

    def get_unreceived_required_document_types(
        self, application: Application, fineos_web_id: str | None = None
    ) -> list[DocumentTypeRequirement]:
        """
        Fetches a list of outstanding evidence and searches the list of required document types for
        the application to match FINEOS missing evidence types to DocumentTypeRequirement instances.
        """
        if application.claim is None or application.fineos_absence_id is None:
            raise ValueError(
                "Cannot get unreceived required document types for application "
                f"{application.application_id}. The application does not have a claim."
            )

        if not fineos_web_id:
            fineos_web_id = fineos_actions.get_or_register_employee_fineos_web_id(
                self.fineos, application, self.db_session
            )

        fineos_absence_id = application.fineos_absence_id
        fineos_outstanding_evidence: set[DocumentType] = {
            cast(DocumentType, evidence.name)
            for evidence in self.fineos.get_outstanding_evidence(fineos_web_id, fineos_absence_id)
            if evidence.uploadCaseNumber == fineos_absence_id
            and not evidence.docReceived
            and evidence.name is not None
        }

        required_document_types = self.get_required_document_types_for_application(application)

        unreceived_evidence = [
            doc_type_req
            for doc_type_req in required_document_types
            if len(fineos_outstanding_evidence.intersection(set(doc_type_req.document_types))) > 0
        ]

        return unreceived_evidence

    def get_document_requirements_for_claim_via_absence_periods(
        self, claim: Claim
    ) -> list[DocumentRequirement]:
        """Get document requirements for a claim based on its absence periods.

        Claim object must have related absence periods loaded
        """

        if not claim.absence_periods:
            raise ValueError(
                "Cannot get document requirements for claim "
                f"{claim.claim_id}. The claim must have associated absence periods."
            )

        document_type_requirements = self.get_required_document_types_for_absence_periods(
            claim.absence_periods
        )
        if not document_type_requirements:
            return []

        documents_from_fineos = self.get_document_service.get_documents_from_fineos_for_claim(claim)
        documents_with_pfml_types = self.get_document_service.load_pfml_types_for_fineos_documents(
            documents_from_fineos
        )
        return self.determine_document_requirements(
            document_type_requirements, documents_with_pfml_types
        )

    def get_document_requirements_for_application(
        self, application: Application
    ) -> list[DocumentRequirement]:

        document_type_requirements = self.get_required_document_types_for_application(application)
        if not document_type_requirements:
            return []

        documents_from_fineos = self.get_document_service.get_documents_from_fineos_for_application(
            application
        )
        documents_with_pfml_types = self.get_document_service.load_pfml_types_for_fineos_documents(
            documents_from_fineos
        )

        return self.determine_document_requirements(
            document_type_requirements, documents_with_pfml_types
        )

    @staticmethod
    def determine_document_requirements(
        document_type_requirements: list[DocumentTypeRequirement],
        uploaded_documents: list[DocumentResponse],
    ) -> list[DocumentRequirement]:
        """Based on list of documents and document type requirements, return list of DocumentRequirements"""
        document_requirements: list[DocumentRequirement] = []
        # For each requirement, gets the latest upload of any document that matches the list of
        # acceptable document types.
        for doc_type_req in document_type_requirements:
            upload_dates_and_types = [
                (
                    doc.created_at,
                    doc.document_type if doc.document_type else "",
                    doc.pfml_document_type if doc.pfml_document_type else "",
                )
                for doc in uploaded_documents
                if doc.created_at is not None and doc.document_type in doc_type_req.document_types
            ]

            if len(upload_dates_and_types) > 0:
                (max_upload_date, max_fineos_doc_type, max_pfml_doc_type) = max(
                    upload_dates_and_types
                )
            else:
                (max_upload_date, max_fineos_doc_type, max_pfml_doc_type) = (None, None, None)
            document_requirements.append(
                DocumentRequirement(
                    document_requirement_category=doc_type_req.document_requirement_category,
                    allowed_document_types=doc_type_req.document_types,
                    document_type=(
                        cast(DocumentType, max_fineos_doc_type) if max_fineos_doc_type else None
                    ),  # TODO (PFMLPB-25359): remove document_type
                    fineos_document_type=(
                        cast(DocumentType, max_fineos_doc_type) if max_fineos_doc_type else None
                    ),
                    pfml_document_type=(
                        cast(DocumentType, max_pfml_doc_type) if max_pfml_doc_type else None
                    ),
                    upload_date=max_upload_date,
                )
            )

        # sort to get deterministic ordering
        document_requirements.sort(
            key=lambda x: (x.document_requirement_category, x.allowed_document_types)
        )

        return document_requirements

    def get_required_document_types_for_application(
        self, application: Application
    ) -> list[DocumentTypeRequirement]:
        """
        Returns a list of all document types required for the given application based on leave reason
        """
        try:
            leave_reason_description = LeaveReason.get_description(application.leave_reason_id)
        except KeyError:
            leave_reason_description = None

        return self.get_required_document_types_by_absence_reason(leave_reason_description)

    def get_required_document_types_for_absence_periods(
        self,
        absence_periods: list[AbsencePeriod],
    ) -> list[DocumentTypeRequirement]:
        """
        Returns a list of all document types required for given absence periods based on absence reason
        """
        required_document_types: list[DocumentTypeRequirement] = []
        for absence_period in absence_periods:
            if absence_period.absence_reason:
                # absence_reason relationship object is loaded
                absence_reason_description = (
                    absence_period.absence_reason.absence_reason_description
                )
            else:
                absence_reason_description = AbsenceReason.get_description(
                    absence_period.absence_reason_id
                )

            if not absence_reason_description:
                continue

            absence_period_required_docs_types = self.get_required_document_types_by_absence_reason(
                absence_reason_description
            )

            for req_doc_type in absence_period_required_docs_types:
                # Avoid duplication by checking whether an entry already exists before adding
                if req_doc_type not in required_document_types:
                    required_document_types.append(req_doc_type)

        return required_document_types

    @staticmethod
    def get_required_document_types_by_absence_reason(
        absence_reason: str | None,
    ) -> list[DocumentTypeRequirement]:
        """
        Returns a list of all document types required for a given absence period based on absence reason
        """
        if (
            not absence_reason
            or absence_reason not in ABSENCE_REASON_TO_CERTIFICATION_DOC_TYPE_MAPPING
        ):
            return []

        required_types = [
            # All applications have the same ID document requirements, so we just hard-code it here
            DocumentTypeRequirement(
                document_requirement_category=DocumentRequirementCategory.IDENTIFICATION,
                document_types=(DocumentType.identification_proof,),
            ),
            DocumentTypeRequirement(
                document_requirement_category=DocumentRequirementCategory.CERTIFICATION,
                document_types=ABSENCE_REASON_TO_CERTIFICATION_DOC_TYPE_MAPPING.get(
                    absence_reason, ()
                ),
            ),
        ]

        # TODO (PFMLPB-25436): This is a workaround to accomodate the change in Pragnancy/Maternity
        # document type from FINEOS 22 to 24. The value in the constant is the v24 one, but while
        # we're in transition we need:
        # 1) the right type based on the FINEOS environment to be listed first, to ensure that
        # the IDP type ends up correct
        # 2) both types to be listed, to ensure that 'syncDocumentRequirements' in the front end
        # identifies the requirement to update when a new form is uploaded. (the front end has no
        # FINEOS version flag, so it will have the old value until we change it to the new, and
        # the back end has to work around that)
        #
        # Once there are no v22 environments, just remove this whole override.
        if absence_reason == AbsenceReason.PREGNANCY_MATERNITY.absence_reason_description:
            if app.get_features_config().fineos.is_running_v24:
                required_types[1].document_types = (
                    DocumentType.pregnancy_and_maternity_form,
                    DocumentType.pregnancy_maternity_form,
                )
            else:
                required_types[1].document_types = (
                    DocumentType.pregnancy_maternity_form,
                    DocumentType.pregnancy_and_maternity_form,
                )

        return required_types
