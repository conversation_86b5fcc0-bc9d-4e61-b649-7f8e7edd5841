from typing import Dict, List, Optional

import newrelic.agent

from massgov.pfml import db
from massgov.pfml.api.models.claims.responses import AbsencePeriodDetailResponse
from massgov.pfml.api.services.fineos_actions import (
    get_absence_period_decisions_from_claim,
    get_absence_periods_from_claim,
)
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.db.queries.absence_periods import (
    convert_fineos_absence_period_to_claim_response_absence_period,
    find_db_absence_period_for_fineos_absence_period,
    sync_customer_api_absence_periods_to_db,
)
from massgov.pfml.fineos.exception import FINEOSForbidden
from massgov.pfml.fineos.models.customer_api import AbsencePeriod as FineosAbsencePeriod
from massgov.pfml.fineos.models.customer_api import AbsencePeriodDecision
from massgov.pfml.services.absence_periods import determine_approved_dates
from massgov.pfml.services.claims import ClaimWithdrawnError
from massgov.pfml.services.documents import DocumentRequirementService
from massgov.pfml.util.logging import get_logger

logger = get_logger(__name__)


def get_absence_period_details(
    claim: Claim, db_session: db.Session
) -> List[AbsencePeriodDetailResponse]:
    """
    Construct the AbsencePeriodDetailResponses for a Claim.
    These detailed responses include extra data that is not stored as part of an AP in the database,
    for example:
    - calculations like required document types and approved start and end dates
    - information fetched from FINEOS directly

    This method also syncs FINEOS data for an absence period into our database.
    """

    absence_periods = []
    absence_period_decisions = None

    log_attributes = {"absence_case_id": claim.fineos_absence_id}

    try:
        absence_periods = get_absence_periods_from_claim(claim, db_session)
    except FINEOSForbidden as error:
        if _is_withdrawn_claim_error(error):
            raise ClaimWithdrawnError
        raise error

    try:
        # Also get absence period decisions to look for any modifications on periods
        absence_period_decisions = get_absence_period_decisions_from_claim(claim, db_session)
    except Exception as error:
        logger.error(
            "Failed to fetch_absence period decisions.",
            extra=log_attributes,
            exc_info=error,
        )

    if len(absence_periods) == 0:
        raise Exception("No absence periods found for claim")
    try:
        sync_customer_api_absence_periods_to_db(
            absence_periods, claim, db_session, log_attributes, absence_period_decisions
        )
    except Exception as error:  # catch all exception handler
        logger.error(
            "Failed to handle update of absence period table.",
            extra=log_attributes,
            exc_info=error,
        )
        newrelic.agent.notice_error(attributes=log_attributes)
        db_session.rollback()  # handle insert errors

    # absence periods from both FINEOS and our db are needed to get the full picture
    fineos_absence_periods = absence_periods
    db_absence_periods = []

    if claim.absence_periods:
        db_absence_periods = claim.absence_periods
    else:
        logger.warning("No existing Absence Period found.", extra=log_attributes)

    detail_responses = [
        _convert_fineos_absence_period_to_absence_period_detail_response(
            fineos_absence_period, db_absence_periods, log_attributes, absence_period_decisions
        )
        for fineos_absence_period in fineos_absence_periods
    ]

    for detail_response in detail_responses:
        detail_response.document_type_requirements = (
            DocumentRequirementService().get_required_document_types_by_absence_reason(
                detail_response.reason
            )
            if detail_response.reason
            else None
        )

    return detail_responses


def _is_withdrawn_claim_error(error: FINEOSForbidden) -> bool:
    # TODO: PFMLPB-21724
    # This seems to be a generic forbidden message -
    # we may be mischaracterizing this as a "withdrawn claim" scenario
    withdrawn_msg = "User does not have permission to access the absence case."
    return withdrawn_msg in error.message


def _convert_fineos_absence_period_to_absence_period_detail_response(
    fineos_absence_period: FineosAbsencePeriod,
    db_absence_periods: List[AbsencePeriod],
    log_attributes: Dict,
    absence_period_decisions: Optional[List[AbsencePeriodDecision]] = None,
) -> AbsencePeriodDetailResponse:
    absence_period_response = convert_fineos_absence_period_to_claim_response_absence_period(
        fineos_absence_period, log_attributes, absence_period_decisions
    )
    response = AbsencePeriodDetailResponse.from_absence_period_response(absence_period_response)

    db_absence_period = find_db_absence_period_for_fineos_absence_period(
        fineos_absence_period, db_absence_periods
    )

    if db_absence_period:
        approved_dates = determine_approved_dates(db_absence_period)

        if approved_dates:
            response.approved_start_date = approved_dates.start_date
            response.approved_end_date = approved_dates.end_date

    if (
        response.approved_start_date == response.absence_period_start_date
        and response.approved_end_date == response.absence_period_end_date
    ):
        # approved start and end dates match the absence start and end dates
        # this absence period is fully approved
        response.is_fully_approved = True
    else:
        response.is_fully_approved = False

    return response
