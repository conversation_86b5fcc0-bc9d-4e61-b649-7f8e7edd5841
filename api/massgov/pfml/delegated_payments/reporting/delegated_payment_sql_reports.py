import os
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional


class ReportName(str, Enum):
    CLAIMANT_EXTRACT_ERROR_REPORT = "claimant-extract-error-report"
    PAYMENT_EXTRACT_ERROR_REPORT = "payment-extract-error-report"
    ADDRESS_ERROR_REPORT = "address-error-report"
    MAX_WEEKLY_BENEFIT_AMOUNT_ERROR_REPORT = "max-weekly-benefit-amount-error-report"
    ZERO_DOLLAR_PAYMENT_REPORT = "zero-dollar-payment-report"
    CANCELLATION_REPORT = "cancellation-report"
    EMPLOYER_REIMBURSEMENT_REPORT = "employer-reimbursement-report"
    ACH_PAYMENT_REPORT = "ach-payment-report"
    CHECK_PAYMENT_REPORT = "check-payment-report"
    DAILY_CASH_REPORT = "daily-cash-report"
    PUB_ERROR_REPORT = "pub-error-report"
    PAYMENT_REJECT_REPORT = "payment-reject-report"
    PAYMENT_FULL_SNAPSHOT_RECONCILIATION_SUMMARY_REPORT = (
        "payment-full-snapshot-reconciliation-summary-report"
    )
    PAYMENT_FULL_SNAPSHOT_RECONCILIATION_DETAIL_REPORT = (
        "payment-full-snapshot-reconciliation-detail-report"
    )
    IRS_1099_REPORT = "irs-1099-report"
    IRS_1099_RECONCILIATION_REPORT = "irs-1099-reconciliation-report"
    FEDERAL_WITHHOLDING_PROCESSED_REPORT = "federal-withholding-processed-report"
    STATE_WITHHOLDING_PROCESSED_REPORT = "state-withholding-processed-report"
    TAX_WITHHOLDING_CUMULATIVE_REPORT = "tax-withholding-cumulative-report"
    WEEKLY_PAYMENT_SUMMARY_REPORT = "weekly-payment-summary-report"
    OVERPAYMENT_SUMMARY_REPORT = "overpayment-summary-report"
    OVERPAYMENT_DETAIL_REPORT = "overpayment-detail-report"
    OUTSTANDING_OVERPAYMENT_REPORT = "outstanding-overpayment-report"
    OVERPAYMENT_REPAYMENT_SUMMARY_REPORT = "overpayment-repayment-summary-report"
    OVERPAYMENT_REPAYMENT_DETAIL_REPORT = "overpayment-repayment-detail-report"
    TASK_CREATION_REPORT = "task-creation-report"
    MAX_WEEKLY_BENEFIT_AMOUNT_ADJUSTMENTS_REPORT = "max-weekly-benefit-amount-adjustments-report"
    CLAIMS_STARTING_IN_CY23 = "claims-starting-in-cy-23"
    CLAIMS_STARTING_IN_CY23_NEW = "claims-starting-in-cy-23-new"
    TWO_INVALID_ENTITLEMENT_PERIODS_REPORT = "two-invalid-entitlement-periods-report"
    MORE_THAN_TWO_INVALID_ENTITLEMENT_PERIODS_REPORT = (
        "more-than-two-invalid-entitlement-periods-report"
    )
    CHILD_SUPPORT_PROCESSED_REPORT = "child-support-processed-report"
    CHILD_SUPPORT_CUMULATIVE_REPORT = "child-support-cumulative-report"
    RECENTLY_APPROVED_WEEKLY_CLAIM_REPORT = "recently-approved-weekly-claim-report"
    CLAIMANT_ADDRESS_VALIDATION_EXTRACT = "claimant-address-validation-extract"
    DUPLICATE_EMPLOYEES_REPORT = "duplicate-employees-report"


REPORT_NAMES = [x for x in ReportName]

# Reports grouped by processing tasks
PROCESS_FINEOS_EXTRACT_REPORTS: List[ReportName] = [
    ReportName.CLAIMANT_EXTRACT_ERROR_REPORT,
    ReportName.PAYMENT_EXTRACT_ERROR_REPORT,
    ReportName.TASK_CREATION_REPORT,
    ReportName.RECENTLY_APPROVED_WEEKLY_CLAIM_REPORT,
]
CREATE_PUB_FILES_REPORTS: List[ReportName] = [
    ReportName.ACH_PAYMENT_REPORT,
    ReportName.CHECK_PAYMENT_REPORT,
    ReportName.DAILY_CASH_REPORT,
    ReportName.PAYMENT_REJECT_REPORT,
    ReportName.TASK_CREATION_REPORT,
    ReportName.FEDERAL_WITHHOLDING_PROCESSED_REPORT,
    ReportName.STATE_WITHHOLDING_PROCESSED_REPORT,
    ReportName.TAX_WITHHOLDING_CUMULATIVE_REPORT,
    ReportName.CHILD_SUPPORT_PROCESSED_REPORT,
    ReportName.CHILD_SUPPORT_CUMULATIVE_REPORT,
]
PROCESS_PUB_RESPONSES_REPORTS: List[ReportName] = [
    ReportName.TASK_CREATION_REPORT,
]
PROCESS_FINEOS_RECONCILIATION_REPORTS: List[ReportName] = [
    ReportName.PAYMENT_FULL_SNAPSHOT_RECONCILIATION_DETAIL_REPORT,
]

IRS_1099_REPORTS: List[ReportName] = []

EMPLOYEE_UPDATE_REPORTS: List[ReportName] = [
    ReportName.DUPLICATE_EMPLOYEES_REPORT,
]

PROCESS_WEEKLY_REPORTS: List[ReportName] = [
    ReportName.OUTSTANDING_OVERPAYMENT_REPORT,
    ReportName.WEEKLY_PAYMENT_SUMMARY_REPORT,
]


@dataclass
class Report:
    sql_command: str
    report_name: ReportName


def _get_report_sql_command_from_file(sql_file_name: str) -> str:
    expected_output = open(
        os.path.join(os.path.dirname(__file__), "sql", f"{sql_file_name}.sql"), "r"
    ).read()
    return expected_output


REPORTS: List[Report] = [
    Report(
        sql_command=_get_report_sql_command_from_file(name.value),
        report_name=name,
    )
    for name in ReportName
]

REPORTS_BY_NAME: Dict[ReportName, Report] = {r.report_name: r for r in REPORTS}


def get_report_by_name(report_name: ReportName) -> Optional[Report]:
    return REPORTS_BY_NAME[report_name]
