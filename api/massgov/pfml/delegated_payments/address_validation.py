import enum
import os
from typing import List, Optional, cast

import massgov.pfml.api.util.state_log_util as state_log_util
import massgov.pfml.db as db
import massgov.pfml.delegated_payments.util.address_validation_util as address_validation_util
import massgov.pfml.experian.address_validate_soap.client as soap_api
import massgov.pfml.experian.address_validate_soap.models as sm
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import PaymentMethod, PaymentTransactionType, State
from massgov.pfml.db.lookup_data.payments import (
    FineosWritebackTransactionStatus,
    PrepaidRegistrationStatus,
)
from massgov.pfml.db.models.employees import Address, ExperianAddressPair, LkState, Payment
from massgov.pfml.db.models.payments import ClaimantPrepaidRegistration
from massgov.pfml.delegated_payments.delegated_payments_util import (
    get_traceable_payment_details,
    is_prepaid_impact_payments_enabled,
)
from massgov.pfml.delegated_payments.issue_resolution.scenarios import ADDRESS_VALIDATION_RESOLUTION
from massgov.pfml.delegated_payments.util.fineos_writeback_util import (
    stage_payment_fineos_writeback,
)
from massgov.pfml.services.payments.issue_resolution import create_issue_resolution
from massgov.pfml.util.batch.log import LogEntry
from massgov.pfml.util.batch.step import Step

logger = logging.get_logger(__name__)


class Constants:

    ERROR_STATE = State.PAYMENT_FAILED_ADDRESS_VALIDATION

    MESSAGE_ALREADY_VALIDATED = "Address has already been validated"
    MESSAGE_INVALID_EXPERIAN_RESPONSE = "Invalid response from Experian search API"
    MESSAGE_INVALID_EXPERIAN_FORMAT_RESPONSE = "Invalid response from Experian format API"
    MESSAGE_VALID_ADDRESS = "Address validated by Experian"
    MESSAGE_VALID_MATCHING_ADDRESS = "Matching address validated by Experian"
    MESSAGE_INVALID_ADDRESS = "Address not valid in Experian"
    MESSAGE_EXPERIAN_EXCEPTION_FORMAT = "An exception was thrown by Experian: {}"
    MESSAGE_ADDRESS_MISSING_PART = (
        "The address is missing a required component and cannot be validated"
    )


class AddressValidationStep(Step):
    """
    https://lwd.atlassian.net/wiki/spaces/API/pages/2365947907/Address+Validation+Step
    """

    class Metrics(str, enum.Enum):
        EXPERIAN_SEARCH_EXCEPTION_COUNT = "experian_search_exception_count"
        INVALID_EXPERIAN_FORMAT = "invalid_experian_format"
        INVALID_EXPERIAN_RESPONSE = "invalid_experian_response"
        MULTIPLE_EXPERIAN_MATCHES = "multiple_experian_matches"
        NO_EXPERIAN_MATCH_COUNT = "no_experian_match_count"
        ADDRESS_ERROR_STATE_COUNT = "address_error_state_count"
        PREVIOUSLY_VALIDATED_MATCH_COUNT = "previously_validated_match_count"
        VALID_EXPERIAN_FORMAT = "valid_experian_format"
        VALIDATED_ADDRESS_COUNT = "validated_address_count"
        VERIFIED_EXPERIAN_MATCH = "verified_experian_match"
        ADDRESS_MISSING_COMPONENT_COUNT = "address_missing_component_count"
        IMPORT_LOG_STATUS = "import_log_status"

    def run_step(self) -> None:

        self.set_metrics({self.Metrics.IMPORT_LOG_STATUS: "in-progress"})

        experian_soap_client = _get_experian_soap_client()

        payments = _get_payments_awaiting_address_validation(self.db_session)
        for payment in payments:
            logger.info(
                "Doing address validation for payment", extra=get_traceable_payment_details(payment)
            )
            self._validate_address_for_payment(payment, experian_soap_client)
            self.increment(self.Metrics.VALIDATED_ADDRESS_COUNT)

        self.validate_failed_address_count(self.get_log_entry())
        return None

    def _validate_address_for_payment(
        self, payment: Payment, experian_soap_client: soap_api.Client
    ) -> None:
        if not payment.experian_address_pair:
            raise ValueError("payment.experian_address_pair is required")

        address_pair = payment.experian_address_pair

        # already validated
        if address_validation_util.address_has_been_validated(address_pair):
            state_log_util.create_finished_state_log(
                associated_model=payment,
                end_state=self._get_next_state(payment),
                outcome=address_validation_util.build_experian_outcome(
                    Constants.MESSAGE_ALREADY_VALIDATED,
                    cast(Address, address_pair.experian_address),
                    address_validation_util.Constants.PREVIOUSLY_VERIFIED,
                ),
                db_session=self.db_session,
            )
            self.increment(self.Metrics.PREVIOUSLY_VALIDATED_MATCH_COUNT)
            logger.info(
                "Address previously validated for payment, skipping Experian call",
                extra=get_traceable_payment_details(payment, self._get_next_state(payment)),
            )

            return None

        if address_pair.fineos_address and not address_validation_util.does_address_have_all_parts(
            address_pair.fineos_address
        ):
            self._create_end_state_by_payment_type(
                payment=payment,
                address=address_pair.fineos_address,
                address_validation_result=None,
                end_state=Constants.ERROR_STATE,
                message=Constants.MESSAGE_ADDRESS_MISSING_PART,
            )
            self.increment(self.Metrics.ADDRESS_MISSING_COMPONENT_COUNT)
            logger.info(
                "Address missing components, skipping Experian call",
                extra=get_traceable_payment_details(payment, Constants.ERROR_STATE),
            )

            return None

        # When we fully switch over to using the SOAP API,
        # we can remove this check and just make it the normal behavior
        self._process_address_via_soap_api(experian_soap_client, payment, address_pair)

        return None

    def _process_address_via_soap_api(
        self,
        experian_soap_client: soap_api.Client,
        payment: Payment,
        address_pair: ExperianAddressPair,
    ) -> None:
        address = address_pair.fineos_address

        try:
            response = address_validation_util.experian_soap_response_for_address(
                experian_soap_client, address
            )

        except Exception as e:
            logger.exception(
                "An exception occurred when querying the address for payment ID %s: %s"
                % (payment.payment_id, type(e).__name__)
            )

            self._create_end_state_by_payment_type(
                payment=payment,
                address=address,
                address_validation_result=None,
                end_state=Constants.ERROR_STATE,
                message=Constants.MESSAGE_EXPERIAN_EXCEPTION_FORMAT.format(type(e).__name__),
            )
            self.increment(self.Metrics.EXPERIAN_SEARCH_EXCEPTION_COUNT)
            return None

        # Address was verified
        if response.verify_level == sm.VerifyLevel.VERIFIED:
            self.increment(self.Metrics.VERIFIED_EXPERIAN_MATCH)
            formatted_address = address_validation_util.experian_verification_response_to_address(
                response
            )

            if not address_validation_util.does_address_have_all_parts(address):
                end_state = Constants.ERROR_STATE
                message = Constants.MESSAGE_INVALID_EXPERIAN_FORMAT_RESPONSE
                self.increment(self.Metrics.INVALID_EXPERIAN_FORMAT)

            else:
                address_pair.experian_address = formatted_address

                end_state = self._get_next_state(payment)
                message = Constants.MESSAGE_VALID_ADDRESS
                self.increment(self.Metrics.VALID_EXPERIAN_FORMAT)

            self._create_end_state_by_payment_type(
                payment=payment,
                address=address,
                address_validation_result=response,
                end_state=end_state,
                message=message,
            )
            return None

        # Experian returned a non-verified scenario, all of these
        # are cases that are considered errors
        self.increment(self.Metrics.NO_EXPERIAN_MATCH_COUNT)
        self._create_end_state_by_payment_type(
            payment=payment,
            address=address,
            address_validation_result=response,
            end_state=Constants.ERROR_STATE,
            message=Constants.MESSAGE_INVALID_ADDRESS,
        )

    def _create_end_state_by_payment_type(
        self,
        payment: Payment,
        address: Address,
        address_validation_result: Optional[sm.SearchResponse],
        end_state: LkState,
        message: str,
    ) -> None:
        # We don't need to block ACH payments for bad addresses, but
        # still want to put it in the log so it can end up in a report
        if payment.disb_method_id == PaymentMethod.ACH.payment_method_id:
            if end_state.state_id == Constants.ERROR_STATE.state_id:
                # Update the message to mention that for EFT we do not
                # require the address to be valid.
                message += " but not required for EFT payment"

                logger.info(
                    "EFT Payment has an invalid address, but address is not required for EFT",
                    extra=get_traceable_payment_details(payment),
                )
            end_state = self._get_next_state(payment)

        # We conditionally need to block PREPAID_CARD payments for bad addresses
        if (
            is_prepaid_impact_payments_enabled()
            and payment.disb_method_id == PaymentMethod.PREPAID_CARD.payment_method_id
        ):
            if end_state.state_id == Constants.ERROR_STATE.state_id:
                # Check if there is a valid Prepaid Card already on record.
                # If yes, then we do not need to block the payment, otherwise we do.
                prepaid_registration = (
                    self.db_session.query(ClaimantPrepaidRegistration)
                    .filter(
                        ClaimantPrepaidRegistration.employee_id == payment.employee_id,
                        ClaimantPrepaidRegistration.prepaid_registration_status_id.in_(
                            [
                                PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id,
                                PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
                            ]
                        ),
                    )
                    .order_by(ClaimantPrepaidRegistration.updated_at.desc())
                    .first()
                )
                if prepaid_registration:
                    # Update the message to mention that for PREPAID_CARD we do not
                    # require the address to be valid if a valid card exists.
                    message += " but not required for PREPAID_CARD payment given a valid card already exists"

                    logger.info(
                        "PREPAID_CARD Payment has an invalid address, but address is not required for Prepaid Card when a valid card exists",
                        extra=get_traceable_payment_details(payment),
                    )
                    end_state = self._get_next_state(payment)

        outcome = address_validation_util.outcome_for_search_result(
            address_validation_result, message, address
        )
        state_log_util.create_finished_state_log(
            associated_model=payment,
            end_state=end_state,
            outcome=outcome,
            db_session=self.db_session,
        )

        if end_state.state_id == Constants.ERROR_STATE.state_id:
            # Any addresses that fail and are required for payment go into the database
            # in an error state and create writeback records to FINEOS
            self.increment(self.Metrics.ADDRESS_ERROR_STATE_COUNT)
            stage_payment_fineos_writeback(
                payment=payment,
                writeback_transaction_status=FineosWritebackTransactionStatus.ADDRESS_VALIDATION_ERROR,
                db_session=self.db_session,
                import_log_id=self.get_import_log_id(),
            )
            create_issue_resolution(
                payment,
                ADDRESS_VALIDATION_RESOLUTION,
                self.get_import_log(),
                self.db_session,
                ADDRESS_VALIDATION_RESOLUTION.get_description(),
            )
            logger.info(
                "Payment failed address validation",
                extra=get_traceable_payment_details(payment, Constants.ERROR_STATE),
            )
        else:
            logger.info(
                "Payment passed address validation",
                extra=get_traceable_payment_details(payment, self._get_next_state(payment)),
            )

    def _get_next_state(self, payment: Payment) -> LkState:
        # Employer reimbursements go to a separate end state
        # as they need to be processed in the RelatedPaymentsProcessingStep
        return (
            State.EMPLOYER_REIMBURSEMENT_READY_FOR_PROCESSING
            if (
                payment.payment_transaction_type_id
                == PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id
            )
            else State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_AUDIT_REPORT
        )

    def validate_failed_address_count(self, log_entry: LogEntry) -> None:
        failed_address_error_threshold_pct = get_failed_address_error_threshold_pct()
        failed_address_warning_threshold_pct = get_failed_address_warning_threshold_pct()
        address_validation_status_change_threshold_count = (
            get_address_validation_status_change_threshold_count()
        )
        total_address_count = (
            int(str(self.get_metric("validated_address_count")))
            if self.get_metric("validated_address_count")
            else 0
        )
        previously_validated_address_count = (
            int(str(self.get_metric("previously_validated_match_count")))
            if self.get_metric("previously_validated_match_count")
            else 0
        )
        experian_search_exception_address_count = (
            int(str(self.get_metric("experian_search_exception_count")))
            if self.get_metric("experian_search_exception_count")
            else 0
        )
        invalid_experian_format_address_count = (
            int(str(self.get_metric("invalid_experian_format")))
            if self.get_metric("invalid_experian_format")
            else 0
        )
        experian_mismatch_address_count = (
            int(str(self.get_metric("no_experian_match_count")))
            if self.get_metric("no_experian_match_count")
            else 0
        )
        failed_address_count = (
            experian_search_exception_address_count
            + invalid_experian_format_address_count
            + experian_mismatch_address_count
        )
        newly_validated_address_count = total_address_count - previously_validated_address_count
        if newly_validated_address_count:
            failed_pct = 100 * float(failed_address_count) / float(newly_validated_address_count)
            if (
                failed_pct > failed_address_error_threshold_pct
                and total_address_count > address_validation_status_change_threshold_count
            ):
                logger.warning(
                    f"Address Validation failed records count is greater than threshold value {failed_address_error_threshold_pct}%"
                )
                log_entry.set_metrics({self.Metrics.IMPORT_LOG_STATUS: "error"})
                return None
            elif failed_pct > failed_address_warning_threshold_pct:
                logger.warning(
                    f"Address Validation encountered partial failure greater than threshold value {failed_address_warning_threshold_pct}%"
                )
                log_entry.set_metrics({self.Metrics.IMPORT_LOG_STATUS: "warning"})
                return None
        log_entry.set_metrics({self.Metrics.IMPORT_LOG_STATUS: "success"})


def _get_experian_soap_client() -> soap_api.Client:
    return soap_api.Client()


def _get_payments_awaiting_address_validation(db_session: db.Session) -> List[Payment]:
    state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
        associated_class=state_log_util.AssociatedClass.PAYMENT,
        end_state=State.PAYMENT_READY_FOR_ADDRESS_VALIDATION,
        db_session=db_session,
    )

    return [state_log.payment for state_log in state_logs if state_log.payment]


def get_failed_address_error_threshold_pct() -> int:
    return int(os.environ.get("FAILED_ADDRESS_ERROR_THRESHOLD_PCT", 50))


def get_failed_address_warning_threshold_pct() -> int:
    return int(os.environ.get("FAILED_ADDRESS_WARNING_THRESHOLD_PCT", 20))


def get_address_validation_status_change_threshold_count() -> int:
    return int(os.environ.get("ADDRESS_VALIDATION_STATUS_CHANGE_THRESHOLD_COUNT", 50))
