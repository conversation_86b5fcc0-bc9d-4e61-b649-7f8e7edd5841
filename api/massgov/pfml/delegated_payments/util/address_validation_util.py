from typing import Any, Dict, Optional

import massgov.pfml.experian.address_validate_soap.client as soap_api
import massgov.pfml.experian.address_validate_soap.models as sm
import massgov.pfml.experian.address_validate_soap.service as address_validation_service
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.geo import GeoState
from massgov.pfml.db.models.employees import Address
from massgov.pfml.db.models.link_experian_address_pair import ExperianAddressPair
from massgov.pfml.experian.physical_address.service import (
    address_to_experian_suggestion_text_format,
)

logger = logging.get_logger(__name__)


class Constants:
    MESSAGE_KEY = "message"
    CONFIDENCE_KEY = "confidence"
    INPUT_ADDRESS_KEY = "input_address"
    OUTPUT_ADDRESS_KEY_PREFIX = "output_address_"
    EXPERIAN_RESULT_KEY = "experian_result"
    PREVIOUSLY_VERIFIED = "Previously verified"
    UNKNOWN = "Unknown"
    STATE_MA_LINE_1 = "Address Line 1"
    STATE_MA_LINE_2 = "Address Line 2"
    STATE_MA_CITY = "City"
    STATE_MA_STATE = "State"
    STATE_MA_ZIP = "Zip+4"


def experian_soap_response_for_address(
    experian_soap_client: soap_api.Client, address: Address
) -> sm.SearchResponse:
    request = address_validation_service.address_to_experian_verification_search(address)
    return experian_soap_client.search(request)


def experian_verification_response_to_address(
    response: Optional[sm.SearchResponse],
) -> Optional[Address]:
    """
    Create PFML Address from Experian SOAP formatted address info

    Throws Exception if the state or country codes are not in PFML DB.
    """
    if not response or not response.address:
        return None

    search_address = response.address

    address = Address()
    for address_line in search_address.address_lines:
        label = address_line.label
        line_value = address_line.line

        if label == Constants.STATE_MA_LINE_1:
            address.address_line_one = line_value
        elif label == Constants.STATE_MA_LINE_2:
            address.address_line_two = line_value
        elif label == Constants.STATE_MA_CITY:
            address.city = line_value
        elif label == Constants.STATE_MA_STATE:
            address.geo_state_id = GeoState.get_id(line_value) if line_value else None
        elif label == Constants.STATE_MA_ZIP:
            address.zip_code = line_value

    return address


def does_address_have_all_parts(address: Address) -> bool:
    if (
        not address.address_line_one
        or not address.city
        or not address.zip_code
        or not address.geo_state_id
    ):
        return False

    return True


def address_has_been_validated(address_pair: ExperianAddressPair) -> bool:
    return address_pair.experian_address is not None


def outcome_for_search_result(
    result: Optional[sm.SearchResponse], msg: str, address: Address
) -> Dict[str, Any]:

    verify_level = (
        result.verify_level.value if result and result.verify_level else Constants.UNKNOWN
    )

    # The address passed into this is the incoming address validated.
    outcome = build_experian_outcome(msg, address, verify_level)

    # Right now we only have the one result.
    response_address = experian_verification_response_to_address(result)
    if response_address:
        label = Constants.OUTPUT_ADDRESS_KEY_PREFIX + "1"
        outcome[Constants.EXPERIAN_RESULT_KEY][label] = address_to_experian_suggestion_text_format(
            response_address
        )

    return outcome


def build_experian_outcome(msg: str, address: Address, confidence: str) -> Dict[str, Any]:
    outcome: Dict[str, Any] = {
        Constants.MESSAGE_KEY: msg,
        Constants.EXPERIAN_RESULT_KEY: {
            Constants.INPUT_ADDRESS_KEY: address_to_experian_suggestion_text_format(address),
            Constants.CONFIDENCE_KEY: confidence,
        },
    }
    return outcome
