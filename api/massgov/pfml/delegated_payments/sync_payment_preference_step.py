from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union

from pydantic import Field
from sqlalchemy import and_, cast, func, or_
from sqlalchemy.orm import Query, aliased
from sqlalchemy.types import String

import massgov.pfml.db as db
import massgov.pfml.delegated_payments.util.address_validation_util as address_validation_util
import massgov.pfml.experian.address_validate_soap.client as soap_api
import massgov.pfml.experian.address_validate_soap.models as sm
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import BankAccountType, PaymentMethod
from massgov.pfml.db.lookup_data.payments import PrepaidRegistrationStatus
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.employees import Employee, LkPaymentMethod, TaxIdentifier
from massgov.pfml.db.models.payments import (
    ClaimantPrepaidRegistration,
    FineosExtractEmployeeFeed,
    FineosExtractEmployeeFeedAggregate,
    PaymentPreference,
)
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.delegated_payments.delegated_payments_util import (
    ValidationContainer,
    get_latest_reference_file_or_raise,
    handle_account_detail,
    lookup_validator,
    routing_number_validator,
    validate_db_input,
)
from massgov.pfml.delegated_payments.util import payment_preference_util, prepaid_debit_address_util
from massgov.pfml.fineos import create_client
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.compare_queries import compare_query_result_sets
from massgov.pfml.util.pydantic import PydanticBaseSettings
from massgov.pfml.util.strings import none_if_empty

logger = logging.get_logger(__name__)

NUM_RECORDS_TO_STREAM = 1_000


class Config(PydanticBaseSettings):
    enable_sync_payment_preferences_using_aggregate_table: bool = Field(
        False, description="Enable to use feed aggregate table"
    )


ExtractTableType = Union[
    FineosExtractEmployeeFeed,
    FineosExtractEmployeeFeedAggregate,
]


class SyncPaymentPreferenceStep(Step):
    extra: Dict[str, Any] = {}  # used for logging
    config = Config()

    class Metrics(str, Enum):
        EXTRACT_PATH = "extract_path"
        PAYMENT_PREFERENCE_PROCESSED = "payment_preference_processed"
        PAYMENT_PREFERENCE_UPDATED = "payment_preference_updated"
        PAYMENT_PREFERENCE_CREATED = "payment_preference_created"
        FINEOS_ROUTING_NUMBER_FAILED_VALIDATION = "fineos_routing_number_failed_validation"
        FINEOS_ACCOUNT_NUMBER_FAILED_VALIDATION = "fineos_account_number_failed_validation"
        FINEOS_ACCOUNT_TYPE_FAILED_VALIDATION = "fineos_account_type_failed_validation"
        AGGREGATE_MISSING = "aggregate_missing"
        AGGREGATE_EXTRA = "aggregate_extra"
        AGGREGATE_MATCHED = "aggregate_matched"
        ENABLE_SYNC_PAYMENT_PREFERENCES_USING_AGGREGATE_TABLE = (
            "enable_sync_payment_preferences_using_aggregate_table"
        )

    def __init__(self, db_session: db.Session, log_entry_db_session: db.Session):
        super().__init__(db_session, log_entry_db_session)
        self.config = Config()

        self.fineos_client = create_client()

    def validate_payment_preference_fields(
        self,
        fineos_employee_record: ExtractTableType,
        validation_container: ValidationContainer,
    ) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """Validate and return the routing number, account number, and account type from the FINEOS data"""
        routing_number = validate_db_input(
            "SORTCODE",
            fineos_employee_record,
            validation_container,
            True,
            min_length=9,
            max_length=9,
            custom_validator_func=routing_number_validator,
        )
        if not routing_number:
            self.increment(self.Metrics.FINEOS_ROUTING_NUMBER_FAILED_VALIDATION)

        account_number = validate_db_input(
            "ACCOUNTNO", fineos_employee_record, validation_container, True, max_length=17
        )
        if not account_number:
            self.increment(self.Metrics.FINEOS_ACCOUNT_NUMBER_FAILED_VALIDATION)

        account_type = validate_db_input(
            "ACCOUNTTYPE",
            fineos_employee_record,
            validation_container,
            True,
            custom_validator_func=lookup_validator(BankAccountType),
        )
        if not account_type:
            self.increment(self.Metrics.FINEOS_ACCOUNT_TYPE_FAILED_VALIDATION)

        return routing_number, account_number, account_type

    def _full_extract_query(self, reference_file: ReferenceFile) -> Query:
        ranked_full_extract = (
            self.db_session.query(
                func.rank()
                .over(
                    order_by=[
                        FineosExtractEmployeeFeed.effectivefrom.desc(),
                        FineosExtractEmployeeFeed.effectiveto.desc(),
                        FineosExtractEmployeeFeed.employee_feed_serial_id.desc(),
                    ],
                    partition_by=FineosExtractEmployeeFeed.customerno,
                )
                .label("rank"),
                FineosExtractEmployeeFeed,
            ).where(
                and_(
                    # Get only records from the specified reference file
                    FineosExtractEmployeeFeed.reference_file_id == reference_file.reference_file_id,
                    # Where the default payment preference is Y
                    FineosExtractEmployeeFeed.defpaymentpref == "Y",
                )
            )
        ).subquery("ranked_full_extract")

        return self.db_session.query(ranked_full_extract).where(ranked_full_extract.c.rank == 1)

    def _agg_extract_query(self):
        ranked_agg_extract = (
            self.db_session.query(
                func.rank()
                .over(
                    order_by=[
                        FineosExtractEmployeeFeedAggregate.effectivefrom.desc(),
                        FineosExtractEmployeeFeedAggregate.effectiveto.desc(),
                        FineosExtractEmployeeFeedAggregate.employee_feed_aggregate_id.desc(),
                    ],
                    partition_by=FineosExtractEmployeeFeedAggregate.customerno,
                )
                .label("rank"),
                FineosExtractEmployeeFeedAggregate,
            ).where(
                and_(
                    # Where the default payment preference is Y
                    FineosExtractEmployeeFeedAggregate.defpaymentpref
                    == "Y",
                )
            )
        ).subquery("ranked_agg_extract")

        return self.db_session.query(ranked_agg_extract).where(ranked_agg_extract.c.rank == 1)

    def _compare_query_result_sets_query(self, reference_file):
        def filter_extract_fields(qry):
            return self.db_session.query(
                qry.c.customerno,
                qry.c.natinsno,
                qry.c.sortcode,
                qry.c.accountno,
                qry.c.accountname,
                qry.c.paymentmethod,
                qry.c.defpaymentpref,
                qry.c.effectivefrom,
                qry.c.effectiveto,
            )

        full_extract_result = self._full_extract_query(reference_file).cte("full")
        agg_extract_result = self._agg_extract_query().cte("agg")

        # Perform the comparison logic here
        return compare_query_result_sets(
            filter_extract_fields(full_extract_result).cte("filtered_full"),
            filter_extract_fields(agg_extract_result).cte("filtered_agg"),
        )

    def _compare_query_result_sets(self, reference_file):
        # Compare the result sets of the two queries
        comparison_results = self.db_session.execute(
            self._compare_query_result_sets_query(reference_file)
        ).fetchall()

        for scenario, count in comparison_results:
            if scenario == "missing":
                self.set_metrics({self.Metrics.AGGREGATE_MISSING: count})
            elif scenario == "extra":
                self.set_metrics({self.Metrics.AGGREGATE_EXTRA: count})
            elif scenario == "matched":
                self.set_metrics({self.Metrics.AGGREGATE_MATCHED: count})
            else:
                logger.warning("Unknown scenario: %s", scenario)

    def _payment_preference_query(self, reference_file: ReferenceFile) -> Query:
        agg_payment_preference_feed = self._agg_extract_query().cte(
            name="agg_payment_preference_feed"
        )
        full_payment_preference_feed = self._full_extract_query(reference_file).cte(
            name="payment_preference_feed"
        )

        payment_preference_feed = (
            agg_payment_preference_feed
            if self.config.enable_sync_payment_preferences_using_aggregate_table
            else full_payment_preference_feed
        )

        employee_feed = (
            aliased(FineosExtractEmployeeFeedAggregate, agg_payment_preference_feed)
            if self.config.enable_sync_payment_preferences_using_aggregate_table
            else aliased(FineosExtractEmployeeFeed, full_payment_preference_feed)  # type: ignore
        )

        return (
            self.db_session.query(employee_feed, Employee, PaymentPreference)
            .join(TaxIdentifier, payment_preference_feed.c.natinsno == TaxIdentifier.tax_identifier)
            .join(Employee, TaxIdentifier.tax_identifier_id == Employee.tax_identifier_id)
            .outerjoin(PaymentPreference, Employee.employee_id == PaymentPreference.employee_id)
            .outerjoin(
                LkPaymentMethod,
                PaymentPreference.payment_method_id == LkPaymentMethod.payment_method_id,
            )
            .where(
                or_(
                    PaymentPreference.payment_preference_id.is_(None),
                    PaymentPreference.routing_number != payment_preference_feed.c.sortcode,
                    PaymentPreference.account_number != payment_preference_feed.c.accountno,
                    PaymentPreference.account_name != payment_preference_feed.c.accountname,
                    cast(PaymentPreference.bank_customer_id, String)
                    != payment_preference_feed.c.customerno,
                    LkPaymentMethod.payment_method_description
                    != payment_preference_feed.c.paymentmethod,
                ),
            )
        )

    def get_payment_preferences_to_sync(
        self, reference_file: ReferenceFile
    ) -> List[Tuple[ExtractTableType, Employee, PaymentPreference]]:
        self._compare_query_result_sets(reference_file)

        rows = self._payment_preference_query(reference_file).all()

        return [tuple(row) for row in rows]

    def upsert_payment_preference(
        self,
        fineos_employee_record: ExtractTableType,
        employee: Employee,
        existing_preference: PaymentPreference,
    ) -> PaymentPreference | None:

        log_attributes = self.extra

        try:
            bank_account_type_id = BankAccountType.CHECKING.bank_account_type_id

            if fineos_employee_record.accountno == "0" and fineos_employee_record.sortcode == "0":
                log_attributes["account_details_workaround"] = True

            # Check for existing payment preference and update it
            # PFMLPB-23643: There is functionality in FINEOS which does not allow a Payment Preference account details to be saved unless
            # account and routing number are populated. There are valid business scenarios when the account and routing number are not
            # known, but we want the Payment Preference to exist with account details.
            # As a workaround, We are advising the business to enter 0 in the fields.  We will treat 0s as empty strings.
            # This allows the payment preference to be saved in FINEOS with no downstream impact to payment processing or payment preference sync.
            # This workaround can be removed when we address this via updates back to FINEOS. (PFMLPB-23863)
            if existing_preference:
                log_attributes["payment_preference.payment_preference_id"] = (
                    existing_preference.payment_preference_id
                )
                log_attributes["updated_payment_preference"] = True
                if fineos_employee_record.accountno != existing_preference.account_number:
                    log_attributes["updated_account_number"] = True
                if fineos_employee_record.sortcode != existing_preference.routing_number:
                    log_attributes["updated_routing_number"] = True
                if fineos_employee_record.accountname != existing_preference.account_name:
                    log_attributes["updated_account_name"] = True

                logger.info("Processing payment preference", extra=log_attributes)

                existing_preference.payment_method_id = PaymentMethod.get_id(
                    fineos_employee_record.paymentmethod
                )
                existing_preference.routing_number = handle_account_detail(
                    fineos_employee_record.sortcode
                )
                existing_preference.account_number = handle_account_detail(
                    fineos_employee_record.accountno
                )
                existing_preference.account_name = none_if_empty(fineos_employee_record.accountname)
                existing_preference.bank_account_type_id = bank_account_type_id
                existing_preference.bank_customer_id = (
                    int(fineos_employee_record.customerno)
                    if fineos_employee_record.customerno
                    else None
                )
                existing_preference.default = True
                self.increment(self.Metrics.PAYMENT_PREFERENCE_UPDATED)
                return existing_preference
            else:
                log_attributes["created_payment_preference"] = True
                logger.info("Processing payment preference", extra=log_attributes)

                # Create new payment preference if it does not exist
                new_payment_preference = PaymentPreference(
                    payment_method_id=PaymentMethod.get_id(fineos_employee_record.paymentmethod),
                    employee_id=employee.employee_id,
                    routing_number=handle_account_detail(fineos_employee_record.sortcode),
                    account_number=handle_account_detail(fineos_employee_record.accountno),
                    account_name=none_if_empty(fineos_employee_record.accountname),
                    bank_account_type_id=bank_account_type_id,
                    bank_customer_id=(
                        int(fineos_employee_record.customerno)
                        if fineos_employee_record.customerno
                        else None
                    ),
                    default=True,
                )
                self.db_session.add(new_payment_preference)
                self.increment(self.Metrics.PAYMENT_PREFERENCE_CREATED)
                return new_payment_preference
        except Exception as e:
            logger.exception(
                "Unexpected error %s while upserting the payment preference for employee: %s",
                type(e),
                employee.employee_id,
            )
            return None

    def _process_account_details(
        self, fineos_employee: ExtractTableType, employee: Employee
    ) -> None:
        if (
            none_if_empty(fineos_employee.accountname) is None
            and none_if_empty(fineos_employee.sortcode) is None
            and none_if_empty(fineos_employee.accountno) is None
        ):

            try:
                # Set up account details in FINEOS
                payment_preference_util.create_payment_preference_account_details(
                    self.db_session, self.fineos_client, employee
                )
                self.extra["created_payment_preference_account_details"] = True
            except Exception as error:
                # This is not a critical update to FINEOS, so log at INFO instead.
                # This is largely here so that FINEOS API failures do not stop the batch from processing.
                logger.info(
                    "Error creating account details in FINEOS", extra=self.extra, exc_info=error
                )

    def _process_prepaid_registration(self, employee: Employee) -> None:
        registration = (
            self.db_session.query(ClaimantPrepaidRegistration)
            .filter(
                or_(
                    ClaimantPrepaidRegistration.prepaid_registration_status_id
                    == PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id,
                    ClaimantPrepaidRegistration.prepaid_registration_status_id
                    == PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id,
                    ClaimantPrepaidRegistration.prepaid_registration_status_id
                    == PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
                ),
                ClaimantPrepaidRegistration.employee_id == employee.employee_id,
            )
            .distinct()
            .first()
        )

        if registration:
            self.extra["claimant_prepaid_registration.claimant_prepaid_registration_id"] = (
                registration.claimant_prepaid_registration_id
            )
            self.extra["claimant_prepaid_registration.prepaid_registration_status_description"] = (
                registration.prepaid_registration_status.prepaid_registration_status_description
            )
            logger.info("Claimant Prepaid Registration", extra=self.extra)
        else:
            # TODO: Determing most recent FINEOS Absence Id, and set it on the prepaid registration
            # This is leveraged when creating a FINEOS Task (if necessary).

            claimant_prepaid_registration = ClaimantPrepaidRegistration(
                employee_id=employee.employee_id,
                prepaid_registration_status_id=PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id,
            )
            self.db_session.add(claimant_prepaid_registration)
            self.extra["created_claimant_prepaid_registration"] = True
            self.extra["claimant_prepaid_registration.prepaid_registration_status_description"] = (
                PrepaidRegistrationStatus.PENDING.prepaid_registration_status_description
            )
            logger.info("Claimant Prepaid Registration", extra=self.extra)

    def _process_address(
        self,
        fineos_employee: ExtractTableType,
        employee: Employee,
        payment_preference: Optional[PaymentPreference] = None,
    ) -> None:
        if fineos_employee and fineos_employee.address1 is None:
            logger.error("Address to verify is not provided", extra=self.extra)
            return None

        address = prepaid_debit_address_util.load_address(
            fineos_employee.address1,
            fineos_employee.address2,
            fineos_employee.address4,
            fineos_employee.address6,
            fineos_employee.postcode,
        )
        experian_address_pair = prepaid_debit_address_util.upsert_address(
            self.db_session, employee, address
        )
        if payment_preference:
            payment_preference.experian_address_pair = experian_address_pair

            if experian_address_pair.experian_address_id is None:
                # Validate the Address
                experian_soap_client = soap_api.Client()
                try:
                    response = address_validation_util.experian_soap_response_for_address(
                        experian_soap_client, address
                    )

                    # Address was verified
                    if response.verify_level == sm.VerifyLevel.VERIFIED:
                        formatted_address = (
                            address_validation_util.experian_verification_response_to_address(
                                response
                            )
                        )
                        experian_address_pair.experian_address = formatted_address

                except Exception as error:
                    self.extra["payment_preference_id"] = payment_preference.payment_preference_id
                    self.extra["experian_address_pair.fineos_address_id"] = (
                        experian_address_pair.fineos_address_id
                    )
                    # This is not an error that requires immediate action
                    logger.info("Address Validation Error", extra=self.extra, exc_info=error)

    def process_payment_preference(self, reference_file: ReferenceFile) -> None:
        """Process Payment Preference from FINEOS

        Creates the Payment Preference for an employee if the employee was included
        in the latest Employee Feed Extract

        The Payment Preference information is from the row for the employee in Employee Feed Extract
        """

        employee_feed_records = self.get_payment_preferences_to_sync(reference_file)

        for fineos_employee, employee, existing_payment_preference in employee_feed_records:
            # Process Payment Preference
            self.extra = {
                "employee.employee_id": employee.employee_id,
                "employee.fineos_customer_number": employee.fineos_customer_number,
            }

            payment_preference = self.upsert_payment_preference(
                fineos_employee, employee, existing_payment_preference
            )

            if (
                payment_preference
                and payment_preference.payment_method_id
                == PaymentMethod.PREPAID_CARD.payment_method_id
            ):
                # If FINEOS does not have account details, then update FINEOS
                self._process_account_details(fineos_employee, employee)

                # If there is no Prepaid Registration for this Claimant, then queue one
                self._process_prepaid_registration(employee)

            # Process Payment Preference Address
            self._process_address(fineos_employee, employee, payment_preference)

            self.increment(self.Metrics.PAYMENT_PREFERENCE_PROCESSED)

    def run_step(self) -> None:
        self.set_metrics(
            {
                self.Metrics.ENABLE_SYNC_PAYMENT_PREFERENCES_USING_AGGREGATE_TABLE: self.config.enable_sync_payment_preferences_using_aggregate_table
            }
        )
        logger.info("Processing Payment Preference data from FINEOS")
        latest_reference_file = get_latest_reference_file_or_raise(
            self.db_session, ReferenceFileType.FINEOS_CLAIMANT_EXTRACT
        )

        if latest_reference_file.processed_import_log_id:
            logger.warning(
                "Already processed the most recent extracts for %s in import run %s",
                latest_reference_file.file_location,
                latest_reference_file.processed_import_log_id,
            )
        else:
            self.set_metrics({self.Metrics.EXTRACT_PATH: latest_reference_file.file_location})
            self.process_payment_preference(latest_reference_file)
            self.db_session.commit()

        logger.info("Done processing Payment Preference data from FINEOS")
