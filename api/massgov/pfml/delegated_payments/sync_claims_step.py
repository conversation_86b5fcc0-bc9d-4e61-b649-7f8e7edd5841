import uuid
from datetime import datetime
from typing import Iterable, List, Optional, Tuple

from sqlalchemy import String, and_, cast
from sqlalchemy.sql.functions import coalesce

import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.employees import <PERSON><PERSON><PERSON>, Employee, Employer
from massgov.pfml.db.models.organization_unit import OrganizationUnit
from massgov.pfml.db.models.payments import (
    FineosExtractVbiRequestedAbsence,
    FineosExtractVbiRequestedAbsenceSom,
)
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.delegated_payments.claimant_extract_metrics import ClaimantExtractMetrics
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.pydantic import PydanticBaseModel

logger = logging.get_logger(__name__)

NUM_ROWS_TO_YIELD = 1_000


class RequestedAbsenceForClaim(PydanticBaseModel):
    """
    These fields are used to create and update Claims in the PFML database.
    We pull these fields from two FINEOS extracts, RequestedAbsence and
    RequestedAbsenceSom, because the RequestedAbsenceSom is missing some
    claims that are needed for the User Not Found flow.
    """

    absenceperiod_classid: str
    absenceperiod_indexid: str

    # Primary keys for rows in each table
    vbi_requested_absence_som_serial_id: Optional[int]
    vbi_requested_absence_serial_id: Optional[int]

    employer_customerno: str
    employee_customerno: str
    absence_casenumber: str
    absencereason_coverage: str

    # This is only present in the FineosExtractVbiRequestedAbsenceSom
    orgunit_name: Optional[str]

    fineos_notification_id: Optional[str]
    absence_casestatus: Optional[str]


class SyncClaimsStep(Step):
    def get_claim_records(self, reference_file: ReferenceFile) -> Iterable[
        Tuple[
            str,  # absenceperiod_classid
            str,  # absenceperiod_indexid
            Optional[int],  # vbi_requested_absence_som_serial_id
            Optional[int],  # vbi_requested_absence_serial_id
            str,  # absence_casenumber
            str,  # employee_customerno
            str,  # employer_customerno
            Optional[str],  # fineos_notification_id
            Optional[str],  # absence_casestatus
            Optional[str],  # orgunit_name
            str,  # absencereason_coverage
            Optional[Claim],
            Optional[uuid.UUID],  # Employee.employee_id
            Optional[uuid.UUID],  # Employer.employer_id
            Optional[uuid.UUID],  # OrganizationUnit.organization_unit_id
        ]
    ]:
        reference_file_id = reference_file.reference_file_id

        # Query the 'out of the box' extract of requested absences
        requested_absence_table = self.db_session.query(
            FineosExtractVbiRequestedAbsence.absenceperiod_classid.label("absenceperiod_classid"),
            FineosExtractVbiRequestedAbsence.absenceperiod_indexid.label("absenceperiod_indexid"),
        ).filter(
            FineosExtractVbiRequestedAbsence.reference_file_id == reference_file_id,
            FineosExtractVbiRequestedAbsence.absence_casestatus
            == AbsenceStatus.CLOSED.absence_status_description,
            FineosExtractVbiRequestedAbsence.absence_intakesource == "Self-Service",
        )

        # Query the custom extract of requested absences
        requested_absence_som_table = self.db_session.query(
            FineosExtractVbiRequestedAbsenceSom.absenceperiod_classid.label(
                "absenceperiod_classid"
            ),
            FineosExtractVbiRequestedAbsenceSom.absenceperiod_indexid.label(
                "absenceperiod_indexid"
            ),
        ).filter(FineosExtractVbiRequestedAbsenceSom.reference_file_id == reference_file_id)

        requested_absence_union_table = requested_absence_som_table.union(
            requested_absence_table
        ).subquery()

        absence_casenumber_label = coalesce(
            FineosExtractVbiRequestedAbsenceSom.absence_casenumber,
            FineosExtractVbiRequestedAbsence.absence_casenumber,
        ).label("absence_casenumber")
        employee_customerno_label = coalesce(
            FineosExtractVbiRequestedAbsenceSom.employee_customerno,
            FineosExtractVbiRequestedAbsence.employee_customerno,
        ).label("employee_customerno")
        employer_customerno_label = coalesce(
            FineosExtractVbiRequestedAbsenceSom.employer_customerno,
            FineosExtractVbiRequestedAbsence.employer_customerno,
        ).label("employer_customerno")
        notification_casenumber_label = coalesce(
            FineosExtractVbiRequestedAbsenceSom.notification_casenumber,
            FineosExtractVbiRequestedAbsence.notification_casenumber,
        ).label("notification_casenumber")
        absence_casestatus_label = coalesce(
            FineosExtractVbiRequestedAbsenceSom.absence_casestatus,
            FineosExtractVbiRequestedAbsence.absence_casestatus,
        ).label("absence_casestatus")

        orgunit_name_label = FineosExtractVbiRequestedAbsenceSom.orgunit_name.label("orgunit_name")

        requested_absence_for_claim = (
            self.db_session.query(
                # Find the original union identifier data
                requested_absence_union_table.c.absenceperiod_classid.label(
                    "absenceperiod_classid"
                ),
                requested_absence_union_table.c.absenceperiod_indexid.label(
                    "absenceperiod_indexid"
                ),
                # Get both relevant table identifiers
                FineosExtractVbiRequestedAbsenceSom.vbi_requested_absence_som_serial_id.label(
                    "vbi_requested_absence_som_serial_id"
                ),
                FineosExtractVbiRequestedAbsence.vbi_requested_absence_serial_id.label(
                    "vbi_requested_absence_serial_id"
                ),
                # Default to using the data coming from the SOM extract,
                # and fallback to the data from the 'out of the box' extract,
                # in the few cases where the SOM extract is missing
                absence_casenumber_label,
                employee_customerno_label,
                employer_customerno_label,
                notification_casenumber_label,
                absence_casestatus_label,
                # Get the fields we can only find in the SOM extract
                orgunit_name_label,
                coalesce(
                    FineosExtractVbiRequestedAbsenceSom.absencereason_coverage,
                    FineosExtractVbiRequestedAbsence.absencereason_coverage,
                ).label("absencereason_coverage"),
            )
            .outerjoin(
                FineosExtractVbiRequestedAbsenceSom,
                and_(
                    requested_absence_union_table.c.absenceperiod_classid
                    == FineosExtractVbiRequestedAbsenceSom.absenceperiod_classid,
                    requested_absence_union_table.c.absenceperiod_indexid
                    == FineosExtractVbiRequestedAbsenceSom.absenceperiod_indexid,
                    FineosExtractVbiRequestedAbsenceSom.reference_file_id == reference_file_id,
                ),
            )
            .outerjoin(
                FineosExtractVbiRequestedAbsence,
                and_(
                    requested_absence_union_table.c.absenceperiod_classid
                    == FineosExtractVbiRequestedAbsence.absenceperiod_classid,
                    requested_absence_union_table.c.absenceperiod_indexid
                    == FineosExtractVbiRequestedAbsence.absenceperiod_indexid,
                    FineosExtractVbiRequestedAbsence.reference_file_id == reference_file_id,
                ),
            )
            .distinct(absence_casenumber_label)
        ).subquery()
        # Join these separately, after the distinct on absence_casenumber, to speed up the query
        # We also join just the fields we need (generally, the primary keys) -- loading
        # the entire row resulted in a significant slowdown of the query.
        return (
            self.db_session.query(
                requested_absence_for_claim,
                Claim,
                Employee.employee_id,
                Employer.employer_id,
                OrganizationUnit.organization_unit_id,
            )
            .outerjoin(
                Claim, Claim.fineos_absence_id == requested_absence_for_claim.c.absence_casenumber
            )
            .outerjoin(
                Employee,
                Employee.fineos_customer_number
                == requested_absence_for_claim.c.employee_customerno,
            )
            .outerjoin(
                Employer,
                cast(Employer.fineos_employer_id, String)
                == requested_absence_for_claim.c.employer_customerno,
            )
            .outerjoin(
                OrganizationUnit,
                and_(
                    OrganizationUnit.name == requested_absence_for_claim.c.orgunit_name,
                    OrganizationUnit.employer_id == Employer.employer_id,
                ),
            )
        ).yield_per(NUM_ROWS_TO_YIELD)

    def process_claim(
        self,
        fineos_data: RequestedAbsenceForClaim,
        claim: Optional[Claim],
        employee_id: Optional[uuid.UUID],
        employer_customerno: str,
        employer_id: Optional[uuid.UUID],
        org_unit_id: Optional[uuid.UUID],
        default_org_unit_employer_ids: List[str],
    ) -> None:
        start_time = datetime.now()

        absence_casenumber = fineos_data.absence_casenumber

        logger.info(
            "Processing absence_case_id %s",
            absence_casenumber,
        )
        validation_container = payments_util.ValidationContainer(absence_casenumber)

        # If the claim didn't exist at the time of the initial query, re-query in case
        # it was created afterward by some separate process (e.g., notifications or an
        # application being submitted).
        if not claim:
            logger.info(
                "Retreiving claim for absence_case_id: %s, creating claim",
                absence_casenumber,
            )

            claim = (
                self.db_session.query(Claim)
                .filter(Claim.fineos_absence_id == absence_casenumber)
                .one_or_none()
            )

        # Add claim table if it does not exist
        if not claim:
            claim = Claim()
            claim.claim_id = uuid.uuid4()
            claim.fineos_absence_id = absence_casenumber

            logger.info(
                "No existing claim found for absence_case_id: %s, creating claim",
                absence_casenumber,
            )
            self.increment(ClaimantExtractMetrics.CLAIM_NOT_FOUND_COUNT)
        else:
            logger.info(
                "Found existing claim for absence_case_id: %s",
                absence_casenumber,
            )

        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(
            "Finished claim setup",
            extra={
                "absence_case_id": absence_casenumber,
                "duration": duration.total_seconds(),
            },
        )

        start_time = datetime.now()

        if fineos_notification_id := payments_util.validate_db_input(
            "fineos_notification_id", fineos_data, validation_container, False
        ):
            claim.fineos_notification_id = fineos_notification_id
        else:
            logger.warning(
                "FINEOS claim notification ID failed validation",
                extra={
                    "absence_case_id": absence_casenumber,
                    "validation_issues": validation_container.get_issues_as_string(),
                },
            )

        # Get claim type.
        # This field is deprecated, but the SQL that generates various reports still relies on
        # this column being set in the database. https://lwd.atlassian.net/browse/PFMLPB-12510
        # will update these reports to not rely on this column being set, at which point
        # this code can be removed.
        claim_type_raw = str(fineos_data.absencereason_coverage)

        if claim_type_raw:
            try:
                claim_type_mapped = payments_util.get_mapped_claim_type(claim_type_raw)
                if claim.claim_type_id != claim_type_mapped.claim_type_id:
                    if claim.claim_type_id:  # Only log a change if the value is populated.
                        logger.info(
                            "Claim type %s changed to %s in absence case id %s",
                            claim.claim_type_id,
                            claim_type_mapped.claim_type_id,
                            absence_casenumber,
                        )
                    claim.claim_type_id = claim_type_mapped.claim_type_id
            except ValueError:
                self.increment(ClaimantExtractMetrics.ERRORED_CLAIMANT_COUNT)
                extra = {
                    "validation_reason": str(payments_util.ValidationReason.INVALID_VALUE),
                    "absencereason_coverage": str(fineos_data.absencereason_coverage),
                }
                logger.info(
                    "Claim validation failed due to invalid absence reason value", extra=extra
                )
                raise

        if employer_id:
            self.increment(ClaimantExtractMetrics.EMPLOYER_FOUND_COUNT)
            if claim.employer_id != employer_id:
                if claim.employer_id:  # Only log a change if the value is populated.
                    self.increment(ClaimantExtractMetrics.EMPLOYER_CHANGED_COUNT)
                    logger.warning(
                        "Employer for claim %s is changing from %s to %s",
                        absence_casenumber,
                        claim.employer_id,
                        employer_id,
                    )
                claim.employer_id = employer_id
        elif not employer_customerno:
            self.increment(ClaimantExtractMetrics.EMPLOYER_CUSTOMERNO_NOT_FOUND_COUNT)
        elif not employer_id:
            logger.warning(
                "Employer customerno %s not found in Employer table for claim %s",
                employer_customerno,
                claim.claim_id,
            )
            validation_container.add_validation_issue(
                payments_util.ValidationReason.MISSING_IN_DB,
                str(employer_id),
                "employer_id",
            )
            self.increment(ClaimantExtractMetrics.EMPLOYER_NOT_FOUND_COUNT)

        if employee_id:
            if claim.employee_id != employee_id:
                if claim.employee_id:  # Only log a change if the value is populated.
                    self.increment(ClaimantExtractMetrics.EMPLOYEE_CHANGED_COUNT)
                    logger.warning(
                        "Employee for claim %s is changing from %s to %s",
                        absence_casenumber,
                        claim.employee_id,
                        employee_id,
                    )
                claim.employee_id = employee_id
        else:
            self.increment(ClaimantExtractMetrics.EMPLOYEE_NOT_FOUND_IN_DATABASE_COUNT)

        if fineos_data.orgunit_name and org_unit_id:
            self.increment(ClaimantExtractMetrics.ORG_UNIT_FOUND_COUNT)
            if claim.organization_unit_id != org_unit_id:
                claim.organization_unit_id = org_unit_id
                logger.info(
                    "Attached organization unit %s to claim %s",
                    fineos_data.orgunit_name,
                    absence_casenumber,
                )
        elif fineos_data.orgunit_name:
            self.increment(ClaimantExtractMetrics.ORG_UNIT_NOT_FOUND_COUNT)
            extra = {
                "validation_reason": str(payments_util.ValidationReason.MISSING_IN_DB),
                "organization_unit_name": str(fineos_data.orgunit_name),
            }
            logger.warning(
                "Claim validation failed due to organization unit missing in db", extra=extra
            )

        if absence_casestatus := payments_util.validate_db_input(
            "ABSENCE_CASESTATUS",
            fineos_data,
            validation_container,
            True,
            custom_validator_func=payments_util.lookup_validator(AbsenceStatus),
        ):
            claim.fineos_absence_status_id = AbsenceStatus.get_id(absence_casestatus)
        else:
            logger.warning(
                "FINEOS claim absence case status failed validation",
                extra={
                    "absence_case_id": absence_casenumber,
                    "validation_issues": validation_container.get_issues_as_string(),
                },
            )

        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(
            "Finished validating claim",
            extra={
                "absence_case_id": absence_casenumber,
                "duration": duration.total_seconds(),
            },
        )

        start_time = datetime.now()

        if (
            claim.organization_unit_id is None
            and str(claim.employer_id) in default_org_unit_employer_ids
        ):
            logger.info(
                "Claim is missing an organization unit.",
                extra={
                    "absence_case_id": absence_casenumber,
                },
            )
            default_organization_unit_row = (
                self.db_session.query(OrganizationUnit)
                .filter(OrganizationUnit.employer_id == claim.employer_id)
                .filter(OrganizationUnit.is_default_organization_unit.is_(True))
                .first()
            )

            if default_organization_unit_row:
                claim.organization_unit_id = str(default_organization_unit_row.organization_unit_id)
                logger.info(
                    "Assigned default organization unit to claim.",
                    extra={
                        "absence_case_id": absence_casenumber,
                        "organization_unit_id": claim.organization_unit_id,
                    },
                )
            else:
                logger.info(
                    "Claim was not assigned an organization unit because a default organization unit doesn't exist.",
                    extra={
                        "absence_case_id": absence_casenumber,
                    },
                )

        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(
            "Finished default organization unit check",
            extra={
                "absence_case_id": absence_casenumber,
                "duration": duration.total_seconds(),
            },
        )

        start_time = datetime.now()

        self.db_session.add(claim)
        self.increment(ClaimantExtractMetrics.CLAIM_PROCESSED_COUNT)

        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(
            "Finished adding claim to DB session",
            extra={
                "absence_case_id": absence_casenumber,
                "duration": duration.total_seconds(),
            },
        )

    def run_step(self) -> None:
        logger.info("Processing claims from FINEOS")
        latest_reference_file = payments_util.get_latest_reference_file_or_raise(
            self.db_session, ReferenceFileType.FINEOS_CLAIMANT_EXTRACT
        )
        self.set_metrics({ClaimantExtractMetrics.EXTRACT_PATH: latest_reference_file.file_location})

        default_org_units = (
            self.db_session.query(OrganizationUnit)
            .filter(OrganizationUnit.is_default_organization_unit.is_(True))
            .all()
        )

        default_org_unit_employer_ids = [
            str(default_org_unit.employer_id) for default_org_unit in default_org_units
        ]

        if latest_reference_file.processed_import_log_id:
            logger.warning(
                "Already processed the most recent extracts for %s in import run %s",
                latest_reference_file.file_location,
                latest_reference_file.processed_import_log_id,
            )
        else:

            record_count = 0

            for (
                absenceperiod_classid,
                absenceperiod_indexid,
                vbi_requested_absence_som_serial_id,
                vbi_requested_absence_serial_id,
                absence_casenumber,
                employee_customerno,
                employer_customerno,
                notification_casenumber,
                absence_casestatus,
                orgunit_name,
                absencereason_coverage,
                claim,
                employee_id,
                employer_id,
                org_unit_id,
            ) in self.get_claim_records(latest_reference_file):
                record_count = record_count + 1
                fineos_data = RequestedAbsenceForClaim(
                    absenceperiod_classid=absenceperiod_classid,
                    absenceperiod_indexid=absenceperiod_indexid,
                    vbi_requested_absence_som_serial_id=vbi_requested_absence_som_serial_id,
                    vbi_requested_absence_serial_id=vbi_requested_absence_serial_id,
                    employer_customerno=employer_customerno,
                    employee_customerno=employee_customerno,
                    absence_casenumber=absence_casenumber,
                    absencereason_coverage=absencereason_coverage,
                    orgunit_name=orgunit_name,
                    fineos_notification_id=notification_casenumber,
                    absence_casestatus=absence_casestatus,
                )
                try:
                    start_time = datetime.now()

                    self.increment(ClaimantExtractMetrics.REQUESTED_ABSENCE_RECORD_COUNT)
                    self.process_claim(
                        fineos_data,
                        claim,
                        employee_id,
                        employer_customerno,
                        employer_id,
                        org_unit_id,
                        default_org_unit_employer_ids,
                    )

                    end_time = datetime.now()
                    duration = end_time - start_time
                    logger.info(
                        "Finished process_claim",
                        extra={
                            "record_count": record_count,
                            "fineos_data.absence_casenumber": fineos_data.absence_casenumber,
                            "duration": duration.total_seconds(),
                        },
                    )
                except Exception as e:
                    logger.exception(
                        "Unexpected error %s while processing claim: %s",
                        type(e),
                        absence_casenumber,
                    )
                    self.increment(ClaimantExtractMetrics.CLAIM_UPDATE_EXCEPTION_COUNT)
        logger.info("Done processing claims from FINEOS")
