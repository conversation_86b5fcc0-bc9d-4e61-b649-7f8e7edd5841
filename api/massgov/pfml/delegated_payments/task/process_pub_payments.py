from enum import Enum
from typing import List, Optional

import massgov.pfml.db as db
import massgov.pfml.delegated_payments.delegated_config as payments_config
import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.logging as logging
from massgov.pfml.delegated_payments.audit.delegated_payment_rejects import PaymentRejectsStep
from massgov.pfml.delegated_payments.delegated_fineos_payment_extract import PaymentExtractStep
from massgov.pfml.delegated_payments.delegated_fineos_payment_marker import PaymentReportMarkerStep
from massgov.pfml.delegated_payments.delegated_fineos_pei_writeback import FineosPeiWritebackStep
from massgov.pfml.delegated_payments.delegated_fineos_related_payment_post_processing import (
    RelatedPaymentsPostProcessingStep,
)
from massgov.pfml.delegated_payments.fineos_task_automation_step import FineosTaskAutomationStep
from massgov.pfml.delegated_payments.payment_methods_split_step import PaymentMethodsSplitStep
from massgov.pfml.delegated_payments.pickup_response_files_step import PickupResponseFilesStep
from massgov.pfml.delegated_payments.pub.transaction_file_creator import TransactionFileCreatorStep
from massgov.pfml.delegated_payments.reporting.delegated_payment_sql_report_step import ReportStep
from massgov.pfml.delegated_payments.reporting.delegated_payment_sql_reports import (
    CREATE_PUB_FILES_REPORTS,
)
from massgov.pfml.util.batch.task_runner import TaskRunner
from massgov.pfml.util.bg import background_task
from massgov.pfml.util.datetime import get_now_us_eastern

logger = logging.get_logger(__name__)


class CreatePubFileTaskRunner(TaskRunner):
    """
    https://lwd.atlassian.net/wiki/spaces/API/pages/1746337949/Create+PUB+Files+Task
    """

    class StepMapping(str, Enum):
        PICKUP_FILES = "pickup"
        PROCESS_AUDIT_REJECT = "audit-reject"
        SPLIT_PAYMENT_METHODS = "split-payment-methods"
        PUB_TRANSACTION = "pub-transaction"
        RELATED_PAYMENT_POST_PROCESSING = "related-payment-post-processing"
        CREATE_PEI_WRITEBACK = "initial-writeback"
        CREATE_FINEOS_TASKS = "create-fineos-tasks"
        REPORT = "report"
        PAYMENT_REPORT_MARKER = "payment-report-marker"

    def __init__(
        self, s3_config: payments_config.PaymentsS3Config, input_args: Optional[List[str]] = None
    ):
        self.s3_config = s3_config
        super().__init__(input_args)

    def run_steps(self, db_session: db.Session, log_entry_db_session: db.Session) -> None:
        """Process PUB Payments"""
        logger.info("Start - PUB Payments ECS Task")
        start_time = get_now_us_eastern()
        s3_config = self.s3_config

        if self.is_enabled(self.StepMapping.PICKUP_FILES):
            PickupResponseFilesStep(
                db_session=db_session,
                log_entry_db_session=log_entry_db_session,
                s3_config=s3_config,
            ).run()

        if self.is_enabled(self.StepMapping.PROCESS_AUDIT_REJECT):
            PaymentRejectsStep(
                db_session=db_session,
                log_entry_db_session=log_entry_db_session,
                payment_rejects_folder_path=s3_config.pfml_payment_rejects_archive_path,
            ).run()

        if self.is_enabled(self.StepMapping.SPLIT_PAYMENT_METHODS):
            PaymentMethodsSplitStep(
                db_session=db_session, log_entry_db_session=log_entry_db_session
            ).run()

        if self.is_enabled(self.StepMapping.PUB_TRANSACTION):
            TransactionFileCreatorStep(
                db_session=db_session,
                log_entry_db_session=log_entry_db_session,
                s3_config=s3_config,
            ).run()

        if self.is_enabled(self.StepMapping.RELATED_PAYMENT_POST_PROCESSING):
            RelatedPaymentsPostProcessingStep(
                db_session=db_session, log_entry_db_session=log_entry_db_session
            ).run()

        if self.is_enabled(self.StepMapping.CREATE_PEI_WRITEBACK):
            FineosPeiWritebackStep(
                db_session=db_session,
                log_entry_db_session=log_entry_db_session,
                s3_config=s3_config,
            ).run()

        if self.is_enabled(self.StepMapping.CREATE_FINEOS_TASKS):
            FineosTaskAutomationStep(
                db_session=db_session,
                log_entry_db_session=log_entry_db_session,
                should_add_to_report_queue=True,
            ).run()

        if self.is_enabled(self.StepMapping.REPORT):
            ReportStep(
                db_session=db_session,
                log_entry_db_session=log_entry_db_session,
                s3_config=s3_config,
                report_names=CREATE_PUB_FILES_REPORTS,
                sources_to_clear_from_report_queue=[PaymentExtractStep],
            ).run()

        if self.is_enabled(self.StepMapping.PAYMENT_REPORT_MARKER):
            PaymentReportMarkerStep(
                db_session=db_session,
                log_entry_db_session=log_entry_db_session,
            ).run()

        payments_util.create_success_file(start_time, "pub-payments-create-pub-files", s3_config)
        logger.info("Done - PUB Payments ECS Task")


@background_task("pub-payments-create-pub-files")
def main():
    """
    Entry point for PUB Payment Processing for processing
    the reject report and creating PUB payment files
    """
    CreatePubFileTaskRunner(payments_config.get_s3_config()).run()
