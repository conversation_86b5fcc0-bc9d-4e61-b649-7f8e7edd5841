p"""add has_previous_leaves to application

Revision ID: 0b58ecc31567
Revises: 159e80bb438a
Create Date: 2023-02-22 23:52:47.031773

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0b58ecc31567"
down_revision = "159e80bb438a"


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "application",
        sa.Column(
            "has_previous_leaves",
            sa.<PERSON>(),
            nullable=True,
            comment="Whether or not the claimant indicates they have any previous leaves to report",
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("application", "has_previous_leaves")
    # ### end Alembic commands ###
