"""Create dynamic config tables

Revision ID: 6c22a94401e3
Revises: ab2dea393fb0
Create Date: 2025-07-07 20:03:07.998815

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "6c22a94401e3"
down_revision = "ab2dea393fb0"


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "dynamic_config",
        sa.Column(
            "dynamic_config_id",
            sa.UUID(),
            nullable=False,
            comment="The internal id for the associated dynamic config entry",
        ),
        sa.Column(
            "config_key", sa.Text(), nullable=False, comment="The configuration key\npii:false"
        ),
        sa.Column(
            "config_context",
            sa.Text(),
            nullable=False,
            comment="The optional configuration context (blank default to satisfy uniqueness constraint)\npii:false",
        ),
        sa.Column(
            "config_value", sa.Text(), nullable=False, comment="The configuration value\npii:false"
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("dynamic_config_id"),
        sa.UniqueConstraint("config_key", "config_context", name="uix_config_key_config_context"),
    )
    op.create_table(
        "dynamic_config_log",
        sa.Column(
            "dynamic_config_log_id",
            sa.UUID(),
            nullable=False,
            comment="The internal id for the associated dynamic config log entry",
        ),
        sa.Column("admin_user_id", sa.UUID(), nullable=True),
        sa.Column(
            "config_key", sa.Text(), nullable=False, comment="The configuration key\npii:false"
        ),
        sa.Column(
            "config_context",
            sa.Text(),
            nullable=False,
            comment="The configuration context\npii:false",
        ),
        sa.Column(
            "before_state",
            sa.Text(),
            nullable=True,
            comment="The configuration value before the change\npii:false",
        ),
        sa.Column(
            "after_state",
            sa.Text(),
            nullable=True,
            comment="The configuration value after the change\npii:false",
        ),
        sa.Column("created_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["admin_user_id"],
            ["admin_user.admin_user_id"],
        ),
        sa.PrimaryKeyConstraint("dynamic_config_log_id"),
        comment="Single log entry of dynamic runtime configuration setting change.\nThis does not use the standard TimestampMixin because it is not intended to be updated.\nTable must not have foreign key constraints on the key or context to allow for deletion\nof dynamic_config entries.\nstatus:active",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("dynamic_config_log")
    op.drop_table("dynamic_config")
    # ### end Alembic commands ###
