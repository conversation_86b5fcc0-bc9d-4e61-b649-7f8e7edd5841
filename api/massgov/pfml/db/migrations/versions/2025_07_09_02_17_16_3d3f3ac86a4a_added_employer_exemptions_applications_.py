"""Added Employer Exemptions Applications ID to documents table with a back populate

Revision ID: 3d3f3ac86a4a
Revises: 6c22a94401e3
Create Date: 2025-07-09 02:17:16.837184

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "3d3f3ac86a4a"
down_revision = "6c22a94401e3"


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "document", sa.Column("employer_exemption_application_id", sa.UUID(), nullable=True)
    )
    op.create_index(
        op.f("ix_document_employer_exemption_application_id"),
        "document",
        ["employer_exemption_application_id"],
        unique=False,
    )
    op.create_foreign_key(
        "document_employer_exemption_application_id_fkey",
        "document",
        "employer_exemption_application",
        ["employer_exemption_application_id"],
        ["employer_exemption_application_id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "document_employer_exemption_application_id_fkey", "document", type_="foreignkey"
    )
    op.drop_index(op.f("ix_document_employer_exemption_application_id"), table_name="document")
    op.drop_column("document", "employer_exemption_application_id")
    # ### end Alembic commands ###
