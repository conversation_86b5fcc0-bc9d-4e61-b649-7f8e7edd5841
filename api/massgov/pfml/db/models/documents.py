"""Grouping for tables related to Documents"""

# ORM models for documents
#
# A model's ORM representation should always match the database so we can
# properly read and write data. If you make a change, follow the instructions
# in the API README to generate an associated table migration.
#
# Generally, a model factory should be provided in the associated factories.py file.
# This allows us to build mock data and insert them easily in the database for tests
# and seeding.

from typing import Optional
from uuid import UUID

from sqlalchemy import Boolean, ForeignKey, Integer, Text
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from massgov.pfml.db.models.appeal import Appeal
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication

from .base import Base, TimestampMixin, uuid_gen

"""
These models were originally in db/models/applications.py
To follow the git history prior to this split, see this PR:
https://github.com/EOLWD/pfml/pull/8226
"""


class LkDocumentType(Base):
    __tablename__ = "lk_document_type"
    document_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    document_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, document_type_id: int, document_type_description: str) -> None:
        self.document_type_id = document_type_id
        self.document_type_description = document_type_description


class Document(Base, TimestampMixin):
    __tablename__ = "document"
    document_id: Mapped[UUID] = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)
    user_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("user.user_id"), nullable=False, index=True
    )
    application_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey("application.application_id"), nullable=True, index=True
    )
    appeal_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey("appeal.appeal_id"), nullable=True, index=True
    )
    employer_exemption_application_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey(
            "employer_exemption_application.employer_exemption_application_id",
            name="document_employer_exemption_application_id_fkey",
        ),
        nullable=True,
        index=True,
    )
    # document_type_id is the document type uploaded to FINEOS
    document_type_id = Column(
        Integer, ForeignKey("lk_document_type.document_type_id"), nullable=False
    )
    # pfml_document_type_id is the user-selected document type internal to PFML
    pfml_document_type_id = Column(
        Integer, ForeignKey("lk_document_type.document_type_id"), nullable=True
    )
    size_bytes: Mapped[int] = Column(Integer, nullable=False)
    fineos_id: Mapped[str | None] = Column(Text, nullable=True)
    is_stored_in_s3: Mapped[bool] = Column(Boolean, nullable=False)
    name: Mapped[str] = Column(Text, nullable=False)
    description: Mapped[str] = Column(Text, nullable=False)

    document_type_instance: Mapped[LkDocumentType] = relationship(
        "LkDocumentType", foreign_keys=[document_type_id], backref="document"
    )

    pfml_document_type_instance = relationship(
        "LkDocumentType", foreign_keys=[pfml_document_type_id], backref="pfml_document"
    )

    application: Mapped[Optional[Application]] = relationship(back_populates="documents")
    appeal: Mapped[Optional[Appeal]] = relationship(back_populates="documents")
    employer_exemption_application: Mapped[Optional[EmployerExemptionApplication]] = relationship(
        back_populates="documents"
    )
