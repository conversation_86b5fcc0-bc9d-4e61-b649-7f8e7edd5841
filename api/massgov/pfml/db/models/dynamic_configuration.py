from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>TAMP, ForeignKey, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from massgov.pfml.db.models.admin import AdminUser

from .base import Base, TimestampMixin, column_doc, utc_timestamp_gen, uuid_gen

"""
This modules defines the object used to store and log dynamic configuration settings that can be
applied to targeted entities in the system for selective runtime behavior.
Use of dynamic configuration must be implemented explicitly in the feature code.
https://lwd.atlassian.net/wiki/spaces/API/pages/3962372104
"""


class DynamicConfig(Base, TimestampMixin):
    """A dynamic runtime configuration setting."""

    __tablename__ = "dynamic_config"
    __table_args__ = (
        UniqueConstraint(
            "config_key",
            "config_context",
            name="uix_config_key_config_context",
        ),
    )

    dynamic_config_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="The internal id for the associated dynamic config entry",
    )

    config_key: Mapped[str] = Column(
        Text,
        comment=column_doc("The configuration key"),
    )

    config_context: Mapped[str] = Column(
        Text,
        default="",
        comment=column_doc(
            "The optional configuration context (blank default to satisfy uniqueness constraint)"
        ),
    )

    config_value: Mapped[str] = Column(
        Text,
        comment=column_doc("The configuration value"),
    )


class DynamicConfigLog(Base):
    """
    Single log entry of dynamic runtime configuration setting change.
    This does not use the standard TimestampMixin because it is not intended to be updated.
    Table must not have foreign key constraints on the key or context to allow for deletion
    of dynamic_config entries.
    """

    __tablename__ = "dynamic_config_log"

    dynamic_config_log_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="The internal id for the associated dynamic config log entry",
    )

    admin_user_id: Mapped[UUID | None] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("admin_user.admin_user_id")
    )

    config_key: Mapped[str] = Column(
        Text,
        comment=column_doc("The configuration key"),
    )

    config_context: Mapped[str] = Column(
        Text,
        comment=column_doc("The configuration context"),
    )

    before_state: Mapped[str] = Column(
        Text,
        nullable=True,
        comment=column_doc("The configuration value before the change"),
    )

    after_state: Mapped[str] = Column(
        Text,
        nullable=True,
        comment=column_doc("The configuration value after the change"),
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        default=utc_timestamp_gen,
    )

    admin_user: Mapped["AdminUser"] = relationship(AdminUser)
