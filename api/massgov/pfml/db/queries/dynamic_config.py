import os
from uuid import UUID

import massgov.pfml.db as db
import massgov.pfml.util.logging
from massgov.pfml.api.exceptions import ObjectNotFound
from massgov.pfml.db.models.dynamic_configuration import DynamicConfig, DynamicConfigLog
from massgov.pfml.util import json

logger = massgov.pfml.util.logging.get_logger(__name__)

ADMIN_UX_DYNAMIC_CONFIG_LOGS_LOAD_LIMIT_KEY = "ADMINUX_DYNAMIC_CONFIG_LOGS_LOAD_LIMIT"


def get_all_dynamic_config_entries(db_session: db.Session) -> list[DynamicConfig]:
    """
    Retrieves all dynamic configuration entries.
    """
    return (
        db_session.query(DynamicConfig)
        .order_by(DynamicConfig.config_key, DynamicConfig.config_context)
        .all()
    )


def create_dynamic_config_entry(
    db_session: db.Session,
    admin_user_id: UUID,
    config_key: str,
    config_context: str,
    config_value: str,
) -> DynamicConfig:
    """
    Inserts a dynamic configuration entry.
    """

    logger.info(f"Creating dynamic config entry: {config_key} / {config_context}")

    entry = DynamicConfig(
        config_key=config_key,
        config_context=config_context,
        config_value=config_value,
    )
    db_session.add(entry)

    add_dynamic_config_log_entry(
        db_session,
        admin_user_id,
        config_key=config_key,
        config_context=config_context,
        state_before="[created]",
        state_after=entry.for_json(),
    )
    return entry


def update_dynamic_config_entry(
    db_session: db.Session,
    admin_user_id: UUID,
    dynamic_config_id: UUID,
    config_value: str,
) -> DynamicConfig:
    """
    Updates a dynamic configuration entry.
    """
    entry = (
        db_session.query(DynamicConfig)
        .filter(DynamicConfig.dynamic_config_id == dynamic_config_id)
        .one_or_none()
    )

    if entry is None:
        raise ObjectNotFound(
            f"Dynamic configuration entry with key '{dynamic_config_id}' not found."
        )

    logger.info(f"Updating dynamic config entry: {dynamic_config_id}")

    before_state = entry.for_json()
    entry.config_value = config_value

    add_dynamic_config_log_entry(
        db_session,
        admin_user_id,
        config_key=entry.config_key,
        config_context=entry.config_context,
        state_before=before_state,
        state_after=entry.for_json(),
    )

    return entry


def delete_dynamic_config_entry(
    db_session: db.Session, admin_user_id: UUID, dynamic_config_id: UUID
) -> None:
    """
    Deletes a dynamic configuration entry.
    """
    entry = (
        db_session.query(DynamicConfig)
        .filter(DynamicConfig.dynamic_config_id == dynamic_config_id)
        .one_or_none()
    )

    if entry is None:
        raise ObjectNotFound(
            f"Dynamic configuration entry with key '{dynamic_config_id}' not found."
        )

    logger.info(f"Creating dynamic config entry: {dynamic_config_id}")

    db_session.delete(entry)
    add_dynamic_config_log_entry(
        db_session,
        admin_user_id,
        config_key=entry.config_key,
        config_context=entry.config_context,
        state_before=entry.for_json(),
        state_after="[deleted]",
    )


def add_dynamic_config_log_entry(
    db_session: db.Session,
    admin_user_id: UUID,
    config_key: str,
    config_context: str | None,
    state_before: str | dict,
    state_after: str | dict,
) -> DynamicConfigLog:
    """
    Adds a log entry for a dynamic configuration change.
    """

    state_before_str = (
        json.dumps(state_before, sort_keys=True, indent=4)
        if isinstance(state_before, dict)
        else state_before
    )
    state_after_str = (
        json.dumps(state_after, sort_keys=True, indent=4)
        if isinstance(state_after, dict)
        else state_after
    )

    config_log_entry = DynamicConfigLog(
        config_key=config_key,
        config_context=config_context,
        before_state=state_before_str,
        after_state=state_after_str,
        admin_user_id=admin_user_id,
    )

    db_session.add(config_log_entry)

    return config_log_entry


def get_dynamic_config_log_entries(
    db_session: db.Session, limit: int | None = None
) -> list[DynamicConfigLog]:
    from massgov.pfml.util.dynamic_config import get_dynamic_config_value

    """
    Retrieves all dynamic configuration log entries.
    """

    # use the limit provided, or fall back to dynamic config, environment variable, or default
    resolved_limit = limit or int(
        str(
            get_dynamic_config_value(
                db_session,
                ADMIN_UX_DYNAMIC_CONFIG_LOGS_LOAD_LIMIT_KEY,
                default=os.getenv(ADMIN_UX_DYNAMIC_CONFIG_LOGS_LOAD_LIMIT_KEY, "50"),
            )
        )
    )

    return (
        db_session.query(DynamicConfigLog)
        .order_by(DynamicConfigLog.created_at.desc())
        .limit(resolved_limit)
        .all()
    )
