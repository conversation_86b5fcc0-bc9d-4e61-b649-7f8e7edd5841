import massgov.pfml.experian.address_validate_soap.models as sm
from massgov.pfml.db.models.employees import Address
from massgov.pfml.experian.address_validate_soap.layouts import Layout
from massgov.pfml.experian.experian_util import address_to_experian_search_text


def address_to_experian_verification_search(address: Address) -> sm.SearchRequest:
    return sm.SearchRequest(
        engine=sm.EngineEnum.VERIFICATION,
        search=address_to_experian_search_text(address),
        layout=Layout.StateMA,
    )
