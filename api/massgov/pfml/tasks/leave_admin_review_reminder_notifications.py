from massgov.pfml import db
from massgov.pfml.util.batch.task_runner import TaskRunner
from massgov.pfml.util.bg import background_task
from massgov.pfml.util.logging import get_logger

logger = get_logger(__name__)


class LeaveAdminReviewNotificationTaskRunner(TaskRunner):
    def run_steps(self, db_session: db.Session, log_entry_db_session: db.Session) -> None:
        log_attributes = {"job_name": "Leave Admin Review Reminder Notifications"}
        logger.info("Start - Leave Admin Review Reminder Notifications job", extra=log_attributes)

        # Reminders are sent 5 days before, 2 days before, and 1 day after the leave admin review date
        # Review date is ManageRequirement.follow_up_date
        offsets = [-5, -2, 1]

        for offset in offsets:
            try:
                logger.info(
                    f"Processing leave admin review notification step for offset: {offset} day",
                    extra=log_attributes,
                )
                # Placeholder for actual processing logic for this offset

                # TODO (PFMLPB-24707): create step for sending reminder email notifications via SNOW
                # TODO (PFMLPB-24708): create step for sending claim auto-submit notification via SNOW

            except Exception as e:
                logger.warning(
                    f"Processing failed for interval {offset} day. Continuing with next interval.",
                    extra=log_attributes,
                    exc_info=e,
                )

        logger.info("End - Leave Admin Review Reminder Notifications job", extra=log_attributes)


@background_task("leave-admin-review-reminder-notifications")
def main():
    LeaveAdminReviewNotificationTaskRunner().run()
