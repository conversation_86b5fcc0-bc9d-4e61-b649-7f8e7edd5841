from massgov.pfml import db
from massgov.pfml.util.batch.task_runner import TaskRunner
from massgov.pfml.util.bg import background_task
from massgov.pfml.util.logging import get_logger

logger = get_logger(__name__)


class LeaveAdminCsvExportTaskRunner(TaskRunner):
    def run_steps(self, db_session: db.Session, log_entry_db_session: db.Session) -> None:
        log_attributes = {"job_name": "Leave Admin CSV Export"}
        logger.info("Start - Leave Admin CSV Export job", extra=log_attributes)

        try:
            # Placeholder: actual export logic goes here
            logger.info("Processing leave admin CSV export", extra=log_attributes)

        except Exception as e:
            logger.error("Leave Admin CSV export failed", extra=log_attributes, exc_info=e)
            raise

        logger.info("End - Leave Admin CSV Export job", extra=log_attributes)


@background_task("leave-admin-csv-export")
def main():
    LeaveAdminCsvExportTaskRunner().run()
