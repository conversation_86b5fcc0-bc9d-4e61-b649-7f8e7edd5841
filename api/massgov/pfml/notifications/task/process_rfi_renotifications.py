from enum import Enum

import massgov.pfml.db as db
import massgov.pfml.util.logging as logging
from massgov.pfml.notifications.query_applications_for_rfi_renotification import (
    QueryApplicationsForRFIReNotificationStep,
)
from massgov.pfml.util.batch.task_runner import TaskRunner
from massgov.pfml.util.bg import background_task

logger = logging.get_logger(__name__)


class RFIReNotificationTaskRunner(TaskRunner):
    """
    Task Runner To Process Request for Information (RFI) Re-Notifications
    https://lwd.atlassian.net/wiki/x/XoDH_g
    """

    class StepMapping(str, Enum):
        QUERY_APPLICATIONS_READY_FOR_RFI_RENOTIFICATION = (
            "query-applications-for-rfi-renotification"
        )

    def run_steps(self, db_session: db.Session, log_entry_db_session: db.Session) -> None:
        logger.info("Start - RFI ReNotification ECS Task")

        if self.is_enabled(self.StepMapping.QUERY_APPLICATIONS_READY_FOR_RFI_RENOTIFICATION):
            logger.info("Running: query-applications-for-rfi-renotification")
            QueryApplicationsForRFIReNotificationStep(
                db_session=db_session, log_entry_db_session=log_entry_db_session
            ).run()


@background_task("process-rfi-renotifications")
def main():
    """Entry point for the task runner"""
    RFIReNotificationTaskRunner().run()


if __name__ == "__main__":
    main()
