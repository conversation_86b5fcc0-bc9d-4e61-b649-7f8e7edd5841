import datetime
from enum import Enum
from typing import Sequence

from sqlalchemy import asc, func

import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.db.models.notifications import Notification
from massgov.pfml.util.batch.step import Step

logger = logging.get_logger(__name__)


class QueryApplicationsForRFIReNotificationStep(Step):

    class Metrics(str, Enum):
        TOTAL_RFI_NOTIFICATION_COUNT_IN_LAST_30_DAYS = (
            "total_rfi_notification_count_in_last_30_days"
        )
        TOTAL_NOTIFICATIONS_DUE_FOR_RENOTIFICATION = "total_notifications_due_for_renotification"
        TOTAL_CLAIMS_BY_ABSENCE_ID = "total_claims_by_absence_id"
        TOTAL_NOTIFICATIONS_WITH_ACTIVE_CLAIMS_READY_FOR_RENOTIFICATION = (
            "total_notifications_with_active_claims_ready_for_renotification"
        )
        TOTAL_NOTIFICATIONS_WITH_CALCULATED_DEADLINES = (
            "total_notifications_with_calculated_deadlines"
        )

    def run_step(self) -> None:
        logger.info("Filtering for RFI notifications in the last thirty days")
        notifications = self.get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days()
        self.set_metrics(
            {self.Metrics.TOTAL_RFI_NOTIFICATION_COUNT_IN_LAST_30_DAYS: len(notifications)}
        )
        if not notifications:
            logger.info(
                "No RFI records found in the last 30 days",
                extra={
                    "status": "Completed",
                },
            )
            return

        logger.info("Filtering out recently notified claims")
        notifications_due_for_renotification = self.filter_out_recently_notified_claims(
            notifications
        )
        self.set_metrics(
            {
                self.Metrics.TOTAL_NOTIFICATIONS_DUE_FOR_RENOTIFICATION: len(
                    notifications_due_for_renotification
                )
            }
        )
        if not notifications_due_for_renotification:
            logger.info(
                "No records found for renotifications after filtering out recently notified claims",
                extra={
                    "status": "Completed",
                },
            )
            return

        logger.info("Filtering for claims by fineos absence ids")
        fineos_absence_ids = [
            notification.fineos_absence_id for notification in notifications_due_for_renotification
        ]
        claims = self.get_claims_by_fineos_absence_ids(fineos_absence_ids)
        self.set_metrics({self.Metrics.TOTAL_CLAIMS_BY_ABSENCE_ID: len(claims)})
        if not claims:
            logger.info(
                "No claims found for the provided fineos absence ids",
                extra={
                    "status": "Completed",
                },
            )
            return

        logger.info("Filtering for notifications with an active claim status")
        notifications_with_an_active_claim_status = (
            self.filter_for_notifications_with_an_active_claim_status(
                notifications_due_for_renotification, claims
            )
        )
        self.set_metrics(
            {
                self.Metrics.TOTAL_NOTIFICATIONS_WITH_ACTIVE_CLAIMS_READY_FOR_RENOTIFICATION: len(
                    notifications_with_an_active_claim_status
                )
            }
        )
        if not notifications_with_an_active_claim_status:
            logger.info(
                "No records found for notifications with an active claim status",
                extra={
                    "status": "Completed",
                },
            )
            return

        logger.info("Calculating deadlines for notifications with an active claim status")
        notifications_with_calculated_deadlines = self.calculate_notification_deadlines(
            notifications_with_an_active_claim_status
        )
        self.set_metrics(
            {
                self.Metrics.TOTAL_NOTIFICATIONS_WITH_CALCULATED_DEADLINES: len(
                    notifications_with_calculated_deadlines
                )
            }
        )

    def get_earliest_rfis_by_fineos_absence_id_in_the_last_30_days(self) -> Sequence[Notification]:
        """
        Queries for all rfi notifications created in the last 30 days
        """
        try:
            return (
                self.db_session.query(Notification)
                .distinct(Notification.fineos_absence_id)
                .order_by(Notification.fineos_absence_id, asc(Notification.created_at))
                .filter(
                    Notification.created_at >= func.now() - datetime.timedelta(days=30),
                    Notification.trigger == "Additional Information Request",
                )
                .all()
            )
        except Exception as e:
            logger.error(
                f"Unable to get notifications: {e}",
                extra={
                    "status": "Failed",
                },
            )
            raise

    def filter_out_recently_notified_claims(
        self, notifications: Sequence[Notification]
    ) -> list[Notification]:
        """
        Filters out notification that have been recently notified, as set by the renotification cadence
        """
        try:
            today = datetime.datetime.now().date()
            filtered_notifications = []

            for notification in notifications:
                created_at_days_ago = (today - notification.created_at.date()).days
                last_notified_at_days_ago = (
                    None
                    if notification.last_notified_at is None
                    else (today - notification.last_notified_at.date()).days
                )

                schedule_days = [9, 14, 24]

                for scheduled_day in schedule_days:
                    window_start = scheduled_day
                    window_end = scheduled_day + 2

                    if window_start <= created_at_days_ago <= window_end and (
                        last_notified_at_days_ago is None
                        or (
                            notification.last_notified_at.date()
                            < (
                                notification.created_at.date()
                                + datetime.timedelta(days=window_start)
                            )
                        )
                    ):
                        filtered_notifications.append(notification)
                        break

            return filtered_notifications
        except Exception as e:
            logger.error(
                f"Unable to remove recently notified claims: {e}",
                extra={
                    "status": "Failed",
                },
            )
            raise

    def get_claims_by_fineos_absence_ids(
        self, fineos_absence_ids: Sequence[str]
    ) -> dict[str, Claim]:
        try:
            claims = (
                self.db_session.query(Claim)
                .filter(Claim.fineos_absence_id.in_(fineos_absence_ids))
                .all()
            )
            return {claim.fineos_absence_id: claim for claim in claims}
        except Exception as e:
            logger.error(
                f"Unable to retrieve claims associated to a notification: {e}",
                extra={
                    "status": "Failed",
                },
            )
            raise

    def filter_for_notifications_with_an_active_claim_status(
        self, notifications: Sequence[Notification], claims: dict[str, Claim]
    ) -> Sequence[Notification]:
        try:
            filtered_notifications_with_active_claim_status = []
            for notification in notifications:
                if notification.fineos_absence_id not in claims:
                    logger.warning(
                        f"No claim found for notification: {notification.notification_id}",
                        extra={"status": "Flagged"},
                    )
                    continue
                if not claims[notification.fineos_absence_id].fineos_absence_status_id in {
                    AbsenceStatus.ADJUDICATION.absence_status_id,
                    AbsenceStatus.IN_REVIEW.absence_status_id,
                    AbsenceStatus.INTAKE_IN_PROGRESS.absence_status_id,
                }:
                    logger.warning(
                        f"Inactive claim found for notification: {notification.notification_id}",
                        extra={"status": "Flagged"},
                    )
                    continue
                filtered_notifications_with_active_claim_status.append(notification)
            return filtered_notifications_with_active_claim_status
        except Exception as e:
            logger.error(
                f"Unable to parse notifications for open application status: {e}",
                extra={
                    "status": "Failed",
                },
            )
            raise

    def calculate_notification_deadlines(
        self, notifications: Sequence[Notification]
    ) -> Sequence[tuple[Notification, int]]:
        try:
            return [
                (
                    notification,
                    (
                        notification.created_at.date()
                        + datetime.timedelta(days=30)
                        - datetime.datetime.now().date()
                    ).days,
                )
                for notification in notifications
            ]
        except Exception as e:
            logger.error(
                f"Unable to calculate the notification deadlines: {e}",
                extra={
                    "status": "Failed",
                },
            )
            raise
