from massgov.pfml.api.models.documents.common import DocumentType
from massgov.pfml.db.lookup_data.absences import AbsenceReason
from massgov.pfml.db.lookup_data.documents import DocumentType as DocumentTypeDetails

ID_DOC_TYPES = [
    DocumentTypeDetails.PASSPORT,
    DocumentTypeDetails.DRIVERS_LICENSE_MASS,
    DocumentTypeDetails.DRIVERS_LICENSE_OTHER_STATE,
    DocumentTypeDetails.IDENTIFICATION_PROOF,
]

SERIOUS_HEALTH_CONDITION_CERTIFICATION_DOC_TYPES = [
    DocumentTypeDetails.OWN_SERIOUS_HEALTH_CONDITION_FORM,
    DocumentTypeDetails.CHILD_BONDING_EVIDENCE_FORM,
    DocumentTypeDetails.HEALTHCARE_PROVIDER_FORM,
]

ABSENCE_REASON_TO_CERTIFICATION_DOC_TYPE_MAPPING = {
    AbsenceReason.CHILD_BONDING.absence_reason_description: (
        DocumentType.child_bonding_evidence_form,
    ),
    AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_description: (
        DocumentType.own_serious_health_condition_form,
        DocumentType.healthcare_provider_form,
    ),
    AbsenceReason.CARE_OF_A_FAMILY_MEMBER.absence_reason_description: (
        DocumentType.care_for_a_family_member_form,
        DocumentType.healthcare_provider_form,
    ),
    AbsenceReason.PREGNANCY_MATERNITY.absence_reason_description: (
        # This was updated from 'pregnancy_maternity_form' to 'pregnancy_and_maternity_form' for
        # FINEOS v24. The special handling to still use the v22 version when necessary during
        # the transition is in the DocumentRequirementService method.
        DocumentType.pregnancy_and_maternity_form,
    ),
}

EMPLOYER_LEGAL_DOC_TYPES = [
    DocumentTypeDetails.APPEAL_ACKNOWLEDGMENT,
    DocumentTypeDetails.APPROVAL_NOTICE,
    DocumentTypeDetails.APPROVAL_OF_APPLICATION_CHANGE,
    DocumentTypeDetails.DENIAL_NOTICE,
    DocumentTypeDetails.DENIAL_OF_APPLICATION_CHANGE,
    DocumentTypeDetails.REQUEST_FOR_MORE_INFORMATION,
    DocumentTypeDetails.WITHDRAWAL_NOTICE,
    DocumentTypeDetails.MAXIMUM_WEEKLY_BENEFIT_CHANGE_NOTICE,
    DocumentTypeDetails.BENEFIT_AMOUNT_CHANGE_NOTICE,
    DocumentTypeDetails.LEAVE_ALLOTMENT_CHANGE_NOTICE,
    DocumentTypeDetails.APPROVED_TIME_CANCELLED,
    DocumentTypeDetails.CHANGE_REQUEST_APPROVED,
    DocumentTypeDetails.CHANGE_REQUEST_DENIED,
    DocumentTypeDetails.OVERPAYMENT_NOTICE_FULL_BALANCE_DEMAND,
    DocumentTypeDetails.OVERPAYMENT_NOTICE_FULL_BALANCE_RECOVERY,
    DocumentTypeDetails.OVERPAYMENT_NOTICE_FULL_BALANCE_RECOVERY_MANUAL,
    DocumentTypeDetails.OVERPAYMENT_NOTICE_PARTIAL_BALANCE_RECOVERY,
    DocumentTypeDetails.OVERPAYMENT_PAYOFF_NOTICE,
    DocumentTypeDetails.INTERMITTENT_TIME_APPROVED_NOTICE,
    DocumentTypeDetails.APPEAL_APPROVED,
    DocumentTypeDetails.APPEAL_DISMISSED_EXEMPT_EMPLOYER,
    DocumentTypeDetails.APPEAL_DISMISSED_OTHER,
    DocumentTypeDetails.APPEAL_HEARING_VIRTUAL_FILLABLE,
    DocumentTypeDetails.MODIFY_DECISION,
    DocumentTypeDetails.APPEAL_RFI,
    DocumentTypeDetails.APPEAL_RETURNED_TO_ADJUDICATION,
    DocumentTypeDetails.APPEAL_WITHDRAWN,
    DocumentTypeDetails.PAYMENT_RECEIVED_UPDATED_OVERPAYMENT_BALANCE,
    DocumentTypeDetails.APPROVED_LEAVE_DATES_CANCELLED,
    DocumentTypeDetails.DENIAL_OF_APPLICATION,
    DocumentTypeDetails.INTERMITTENT_TIME_REPORTED,
]

EMPLOYEE_LEGAL_DOC_TYPES = list(
    set(
        EMPLOYER_LEGAL_DOC_TYPES
        + [
            DocumentTypeDetails.APPEAL_POSTPONEMENT_AGENCY,
            DocumentTypeDetails.APPEAL_POSTPONEMENT_APPROVED,
            DocumentTypeDetails.APPEAL_POSTPONEMENT_DENIED,
            DocumentTypeDetails.APPEAL_REINSTATEMENT_DENIED,
            DocumentTypeDetails.APPEAL_REINSTATEMENT_GRANTED,
            DocumentTypeDetails.DENIAL_NOTICE_EXPLANATION_OF_WAGES,
            DocumentTypeDetails.EXPLANATION_OF_WAGES,
            DocumentTypeDetails.APPROVAL_NOTICE_EXPLANATION_OF_WAGES,
            DocumentTypeDetails.OVERPAYMENT_FULL_DEMAND_ER_BENEFITS,
            DocumentTypeDetails.OVERPAYMENT_FULL_DEMAND_INTERMITTENT,
            DocumentTypeDetails.OVERPAYMENT_FULL_DEMAND_LEAVE_CHANGE,
            DocumentTypeDetails.OVERPAYMENT_FULL_DEMAND_PAID_TIME_OFF,
            DocumentTypeDetails.OVERPAYMENT_FULL_DEMAND_UI,
            DocumentTypeDetails.OVERPAYMENT_FULL_DEMAND_WORKERS_COMP,
            DocumentTypeDetails.OVERPAYMENT_FULL_RECOVERY_ER_BENEFITS,
            DocumentTypeDetails.OVERPAYMENT_FULL_RECOVERY_INTERMITTENT,
            DocumentTypeDetails.OVERPAYMENT_FULL_RECOVERY_LEAVE_CHANGE,
            DocumentTypeDetails.OVERPAYMENT_FULL_RECOVERY_PAID_TIME_OFF,
            DocumentTypeDetails.OVERPAYMENT_FULL_RECOVERY_UI,
            DocumentTypeDetails.OVERPAYMENT_FULL_RECOVERY_WORKERS_COMP,
            DocumentTypeDetails.OVERPAYMENT_NOTICE_PARTIAL_BALANCE_RECOVERY,
            DocumentTypeDetails.OVERPAYMENT_PARTIAL_DEMAND_ER_BENEFITS,
            DocumentTypeDetails.OVERPAYMENT_PARTIAL_DEMAND_INTERMITTENT,
            DocumentTypeDetails.OVERPAYMENT_PARTIAL_DEMAND_UI,
            DocumentTypeDetails.OVERPAYMENT_PARTIAL_DEMAND_WORKERS_COMP,
            DocumentTypeDetails.OVERPAYMENT_PARTIAL_LEAVE_CHANGE,
            DocumentTypeDetails.OVERPAYMENT_PARTIAL_PAID_TIME_OFF,
            DocumentTypeDetails.OVERPAYMENT_PAYMENT_RECEIVED_NEW_BALANCE,
            DocumentTypeDetails.OVERPAYMENT_PAYOFF,
            DocumentTypeDetails.DISMISSAL_FOR_FAILURE_TO_ATTEND_HEARING,
            DocumentTypeDetails.NOTICE_OF_DEFAULT,
            DocumentTypeDetails.EFT_CHANGE_REQUEST,
            DocumentTypeDetails.NOTICE_OF_CHILD_SUPPORT_WITHHOLDING,
            DocumentTypeDetails.W9_TAX_FORM,
        ]
    )
)

# The list of overpayment notice document types
OVERPAYMENT_DOCUMENT_TYPES = [
    DocumentType.overpayment_notice_partial_balance_recovery,
    DocumentType.overpayment_payoff_notice,
    DocumentType.overpayment_notice_full_balance_recovery,
    DocumentType.overpayment_notice_full_balance_recovery_manual,
    DocumentType.overpayment_notice_full_balance_demand,
    DocumentType.payment_received_updated_overpayment_balance,
    DocumentType.overpayment_full_demand_er_benefits,
    DocumentType.overpayment_full_demand_intermittent,
    DocumentType.overpayment_full_demand_leave_change,
    DocumentType.overpayment_full_demand_paid_time_off,
    DocumentType.overpayment_full_demand_ui,
    DocumentType.overpayment_full_demand_workers_comp,
    DocumentType.overpayment_full_recovery_er_benefits,
    DocumentType.overpayment_full_recovery_intermittent,
    DocumentType.overpayment_full_recovery_leave_change,
    DocumentType.overpayment_full_recovery_paid_time_off,
    DocumentType.overpayment_full_recovery_ui,
    DocumentType.overpayment_full_recovery_workers_comp,
    DocumentType.overpayment_partial_demand_er_benefits,
    DocumentType.overpayment_partial_demand_intermittent,
    DocumentType.overpayment_partial_leave_change,
    DocumentType.overpayment_partial_paid_time_off,
    DocumentType.overpayment_partial_demand_ui,
    DocumentType.overpayment_partial_demand_workers_comp,
    DocumentType.overpayment_payment_received_new_balance,
    DocumentType.overpayment_payoff,
]

DOCUMENT_TYPES_ASSOCIATED_WITH_EVIDENCE = (
    DocumentType.identification_proof,
    DocumentType.state_managed_paid_leave_confirmation,
    DocumentType.own_serious_health_condition_form,
    # TODO (PFMLPB-25436): remove this pregnancy_maternity_form document_type
    DocumentType.pregnancy_maternity_form,
    # FINEOS version 24 and later uses DocumentType.pregnancy_and_maternity_form. Earlier versions use DocumentType.pregnancy_maternity_form
    DocumentType.pregnancy_and_maternity_form,
    DocumentType.child_bonding_evidence_form,
    DocumentType.care_for_a_family_member_form,
    DocumentType.military_exigency_form,
    DocumentType.covered_service_member_identification_proof,
    DocumentType.family_member_active_duty_service_proof,
)
