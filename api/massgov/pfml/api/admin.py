from typing import Any, Dict, List, Optional
from uuid import UUID

import flask
from connexion.lifecycle import ConnexionResponse
from sqlalchemy import String, Text, cast, exists, or_, select
from sqlalchemy.orm import joinedload
from sqlalchemy.sql.expression import desc
from werkzeug.exceptions import BadRequest, NotFound, ServiceUnavailable, Unauthorized

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
import massgov.pfml.db.queries.dynamic_config as dynamic_config
import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.fineos
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.api.authentication import (
    build_access_token,
    build_auth_code_flow,
    build_logout_flow,
)
from massgov.pfml.api.authentication.azure import AzureUser
from massgov.pfml.api.authorization.flask import EDIT, READ, ensure, skip_authorization
from massgov.pfml.api.models.admin.audit_log.responses import AuditLogResponse
from massgov.pfml.api.models.admin.users.requests import (
    AdminUpdateBaseRequest,
    AdminUserUpdateRequest,
)
from massgov.pfml.api.models.flags.requests import FlagRequest
from massgov.pfml.api.models.flags.responses import FlagLogResponse
from massgov.pfml.api.models.oauth.requests import OmniSearchRequest
from massgov.pfml.api.models.payments.requests import (
    OverpaymentHoldTransactionRequest,
    OverpaymentMarkVcmReviewedRequest,
    OverpaymentReferRequest,
    OverpaymentRetryTransactionRequest,
    OverpaymentSearchRequest,
)
from massgov.pfml.api.models.payments.responses import (
    MmarsEventResponse,
    OverpaymentCaseResponse,
    OverpaymentSearchResponse,
    VCMComparisonResponse,
)
from massgov.pfml.api.models.users.requests import (
    AdminTokenRequest,
    ApplicationReassignmentPreviewRequest,
    ApplicationReassignmentRequest,
)
from massgov.pfml.api.models.users.responses import (
    AdminTokenResponse,
    AdminUserResponse,
    ApplicationReassignmentPreviewResponse,
    AuthURIResponse,
    CheckAddressValidationOverrideResponse,
    UserAuthLogResponse,
    UserResponse,
)
from massgov.pfml.api.util.paginate.paginator import PaginationAPIContext, page_for_api_context
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationErrorList,
)
from massgov.pfml.api.validation.experian_address_validator import call_experian_address
from massgov.pfml.api.validation.user_rules import (
    get_users_convert_claimant_issues,
    get_users_convert_employer_issues,
)
from massgov.pfml.db.lookup_data.azure import AzurePermission
from massgov.pfml.db.lookup_data.flags import FeatureFlag
from massgov.pfml.db.lookup_data.payments import (
    MmarsEventActionType,
    MmarsEventStatusType,
    MmarsEventType,
    PaymentEventType,
)
from massgov.pfml.db.models.admin import AdminAuditLog, AdminUser
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.azure import LkAzurePermission
from massgov.pfml.db.models.employees import Employee, LkRole, Overpayment, User, UserRole
from massgov.pfml.db.models.flags import FeatureFlagValue, LkFeatureFlag
from massgov.pfml.db.models.oauth import UserAuthLog
from massgov.pfml.db.models.payments import (
    LkMmarsEventActionType,
    MmarsCustomerDetail,
    MmarsEvent,
    MmarsEventActionLog,
)
from massgov.pfml.delegated_payments.delegated_payments_util import (
    get_address_pair_for_experian_override,
    override_experian_address,
)
from massgov.pfml.dor.importer.lib.dor_persistence_util import get_address
from massgov.pfml.util.admin_audit import create_audit_entry, get_admin_user_id
from massgov.pfml.util.logging.instrumentation import LogConstants
from massgov.pfml.util.sql_utils import escape_like
from massgov.pfml.util.sqlalchemy import get_or_404
from massgov.pfml.util.users import remove_leave_admins_and_role

logger = massgov.pfml.util.logging.get_logger(__name__)

SERVICE_UNAVAILABLE_MESSAGE = "Azure AD is not configured."

ERROR_TO_PENDING_STATUS_MAP = {
    MmarsEventStatusType.VCC_FAILED.mmars_event_status_type_id: MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id,
    MmarsEventStatusType.VCC_ERROR.mmars_event_status_type_id: MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id,
    MmarsEventStatusType.RE_FAILED.mmars_event_status_type_id: MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id,
    MmarsEventStatusType.RE_ERROR.mmars_event_status_type_id: MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id,
    MmarsEventStatusType.REM_FAILED.mmars_event_status_type_id: MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
    MmarsEventStatusType.REM_ERROR.mmars_event_status_type_id: MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
}


@skip_authorization
def admin_authorization_url():
    auth_code_params = build_auth_code_flow()
    if auth_code_params is None:
        raise ServiceUnavailable(description=SERVICE_UNAVAILABLE_MESSAGE)
    return response_util.success_response(
        data=AuthURIResponse.parse_obj(auth_code_params).__dict__,
        message="Retrieved authorization url!",
    ).to_api_response()


@skip_authorization
def admin_token(body: dict) -> ConnexionResponse:
    request = parse_request_body(AdminTokenRequest, body)
    try:
        tokens = build_access_token(request.auth_uri_res.__dict__, request.auth_code_res.__dict__)

    except ValueError:  # Usually caused by CSRF, simply ignore them
        logger.error("admins_token value error.")
        return response_util.error_response(
            status_code=BadRequest,
            message="Invalid code while attempting to acquire a token",
            errors=[
                ValidationErrorDetail(
                    field="auth_uri_res", message="Value error", type=IssueType.invalid
                )
            ],
        ).to_api_response()

    if tokens is None:
        raise ServiceUnavailable(description=SERVICE_UNAVAILABLE_MESSAGE)

    if "error" in tokens:
        logger.error("admins_token error in tokens.", extra={"error": tokens["error"]})
        return response_util.error_response(
            status_code=BadRequest,
            message="Unknown error while attempting to acquire a token",
            errors=[
                ValidationErrorDetail(
                    field="auth_uri_res", message=tokens["error"], type=IssueType.invalid
                )
            ],
        ).to_api_response()

    claims = tokens["id_token_claims"]

    user_name = claims.get("name")
    oauth_id = claims.get("sub")
    email_address = claims.get("preferred_username")

    with app.db_session() as db_session:
        try:
            admin_user = db_session.query(AdminUser).filter_by(oauth_id=oauth_id).one_or_none()
            log_extra = {"admin_oauth_id": oauth_id}
            log_message = "Ensuring admin user exists in DB"
            logger.info(log_message, extra=log_extra)
            if not admin_user:
                admin_user = AdminUser()
                admin_user.name = user_name
                admin_user.oauth_id = oauth_id
                admin_user.email_address = email_address
                db_session.add(admin_user)
                db_session.commit()

                log_message = f"Added admin user for oauth_id: {oauth_id}"
            else:
                log_message = f"Admin user already exists for oauth_id: {oauth_id}"

            log_extra["admin_user_id"] = admin_user.admin_user_id
            logger.info(log_message, extra=log_extra)

        except Exception as e:
            error_message = "Error occurred trying to add the authenticated Admin user"
            logger.error(error_message, exc_info=e, extra={"admin_auth_sub": oauth_id})
            raise e

    return response_util.success_response(
        data=AdminTokenResponse.parse_obj(tokens).__dict__, message="Successfully logged in!"
    ).to_api_response()


def admin_login():
    # decode_jwt is automatically called and will validate the token.
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise NotFound
    ensure(READ, azure_user)
    return response_util.success_response(
        data=azure_user_response(azure_user), message="Successfully logged in!"
    ).to_api_response()


@skip_authorization
def admin_logout():
    logout_uri = build_logout_flow()
    if logout_uri is None:
        raise ServiceUnavailable(description=SERVICE_UNAVAILABLE_MESSAGE)
    return response_util.success_response(
        data={"logout_uri": logout_uri}, message="Retrieved logout url!"
    ).to_api_response()


def admin_get_flag_logs(name):
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)
    # There's a single API endpoint to set feature flags and maintenance is
    # the only feature flag right now. This will need to change if more feature
    # flags are set from the Admin Portal. This will disallow other feature
    # flags from being unset until the decision is made.
    if name == FeatureFlag.MAINTENANCE.name:
        ensure(READ, LkFeatureFlag)
    else:
        raise Unauthorized
    with app.db_session() as db_session:
        logs = FeatureFlag.get_instance(db_session, description=name).logs()
        response = response_util.success_response(
            data=[
                FlagLogResponse(
                    first_name=log.given_name,
                    last_name=log.family_name,
                    updated_at=log.updated_at,
                    enabled=log.enabled,
                    name=log.name,
                    start=log.start,
                    end=log.end,
                    options=log.options,
                ).__dict__
                for log in logs
            ],
            message="Successfully retrieved flag",
        ).to_api_response()
        return response


def admin_flags_patch(name, body):
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    # There's a single API endpoint to set feature flags and maintenance is
    # the only feature flag right now. This will need to change if more feature
    # flags are set from the Admin Portal. This will disallow other feature
    # flags from being unset until the decision is made.
    if name == FeatureFlag.MAINTENANCE.name:
        ensure(EDIT, LkFeatureFlag)
    else:
        raise Unauthorized
    request_body = parse_request_body(FlagRequest, body)

    with app.db_session() as db_session:
        try:
            flag = FeatureFlag.get_instance(db_session, description=name)
        except KeyError:
            raise NotFound(
                description="Could not find {} with name {}".format(LkFeatureFlag.__name__, name)
            )
        feature_flag_value = FeatureFlagValue()
        feature_flag_value.feature_flag = flag
        feature_flag_value.email_address = azure_user.email_address
        feature_flag_value.sub_id = azure_user.sub_id
        feature_flag_value.given_name = azure_user.first_name
        feature_flag_value.family_name = azure_user.last_name
        feature_flag_value.action = "INSERT"

        for key in request_body.__fields_set__:
            value = getattr(request_body, key)
            setattr(feature_flag_value, key, value)
        db_session.add(feature_flag_value)
        db_session.commit()

    return response_util.success_response(
        message="Successfully updated feature flag",
        data=FlagRequest.from_orm(flag).dict(),
    ).to_api_response()


def admin_users_get(email_address: Optional[str] = "") -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)
    # Not implemented. If so, this should be converted to a POST
    # /resource/search to follow API standards on
    # https://lwd.atlassian.net/wiki/spaces/API/pages/195002417/API+Standards
    ensure(READ, AzurePermission.MAINTENANCE_READ)
    with PaginationAPIContext(User, request=flask.request) as pagination_context:
        with app.db_session() as db_session:
            query = db_session.query(User)
            if email_address:
                query = query.filter(
                    User.email_address.ilike("%" + escape_like(email_address) + "%")
                )
            page = page_for_api_context(pagination_context, query)
    return response_util.paginated_success_response(
        message="Successfully retrieved users",
        model=UserResponse,
        page=page,
        context=pagination_context,
        status_code=200,
    ).to_api_response()


def azure_user_response(user: AzureUser) -> Dict[str, Any]:
    response = user.__dict__.copy()

    with app.db_session() as db_session:
        permissions = (
            db_session.query(LkAzurePermission)
            .filter(LkAzurePermission.azure_permission_id.in_(user.permissions))
            .all()
        )

    response["permissions"] = [
        f"{permission.azure_permission_description}" for permission in permissions
    ]
    return AdminUserResponse.parse_obj(response).__dict__


def validate_address(address: str) -> flask.Response:
    rtn_data = call_experian_address(address)
    response = response_util.success_response(
        data=[rtn_data],
        message="",
    ).to_api_response()
    return response


def check_address_validation_override_get(absence_case_id: str) -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    with app.db_session() as db_session:

        address_pair = get_address_pair_for_experian_override(db_session, absence_case_id)

        if address_pair is None:
            return response_util.error_response(
                status_code=NotFound, message=NotFound.description, errors=[]
            ).to_api_response()

        address = get_address(db_session, address_pair.fineos_address_id)

        response_data = CheckAddressValidationOverrideResponse()
        response_data.address_line_one = address.address_line_one
        response_data.address_line_two = address.address_line_two
        response_data.city = address.city
        response_data.state = address.geo_state.geo_state_description if address.geo_state else None
        response_data.zip_code = address.zip_code

        response = response_util.success_response(
            data=response_data.__dict__,
            message="",
        ).to_api_response()

        return response


def confirm_address_validation_override_patch(absence_case_id: str) -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    with app.db_session() as db_session:

        address_pair = get_address_pair_for_experian_override(db_session, absence_case_id)

        try:

            if address_pair is not None:

                override_experian_address(db_session, address_pair)

                response = response_util.success_response(
                    data={},
                    message="Success",
                ).to_api_response()

        except Exception as error:
            logger.error(
                "Failed to update the address pair table.",
                extra={"absence_case_id": absence_case_id},
                exc_info=error,
            )

            response = response_util.error_response(
                data={},
                message="Failed to update the address pair table.",
                status_code=BadRequest,
                errors=[],
            ).to_api_response()

        return response


def smoke_test_fineos_customer_api_get() -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    fineos = massgov.pfml.fineos.create_client()

    is_api_available = fineos.is_customer_api_available()

    if is_api_available:
        response = response_util.success_response(
            data={},
            message="The FINEOS Customer API is available.",
        ).to_api_response()
    else:
        response = response_util.error_response(
            data={},
            message="The FINEOS Customer API is not available.",
            status_code=ServiceUnavailable,
            errors=[],
        ).to_api_response()

    return response


def smoke_test_fineos_integration_services_api_get() -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    fineos = massgov.pfml.fineos.create_client()

    is_api_available = fineos.is_integration_services_api_available()

    if is_api_available:
        response = response_util.success_response(
            data={},
            message="The FINEOS Integration Services API is available.",
        ).to_api_response()
    else:
        response = response_util.error_response(
            data={},
            message="The FINEOS Integration Services API is not available.",
            status_code=ServiceUnavailable,
            errors=[],
        ).to_api_response()

    return response


def smoke_test_fineos_group_client_api_get() -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    fineos = massgov.pfml.fineos.create_client()

    is_api_available = fineos.is_group_client_api_available()

    if is_api_available:
        response = response_util.success_response(
            data={},
            message="The FINEOS Group Client API is available.",
        ).to_api_response()
    else:
        response = response_util.error_response(
            data={},
            message="The FINEOS Group Client API is not available.",
            status_code=ServiceUnavailable,
            errors=[],
        ).to_api_response()

    return response


def overpayment_search(body: dict) -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)
    logger.info("Searching for overpayments")
    request_body = parse_request_body(OverpaymentSearchRequest, body)
    fineos_customer_number = request_body.fineos_customer_number

    # Query the database
    with app.db_session() as db_session:
        employee = (
            db_session.query(Employee)
            .filter(Employee.fineos_customer_number == fineos_customer_number)
            .first()
        )

        if not employee:

            logger.info(f"No employee found for fineos_customer_number: {fineos_customer_number}")
            return response_util.success_response(
                data=None,
                message="No employee found.",
            ).to_api_response()

        # Map the result to OverpaymentCollectionResponse
        overpayment_collection_response = OverpaymentSearchResponse.from_orm(employee)

        response = response_util.success_response(
            data=overpayment_collection_response.dict(),
            message="Success",
        ).to_api_response()

        return response


def refer_overpayment(body: dict) -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(EDIT, azure_user)
    request_body = parse_request_body(OverpaymentReferRequest, body)
    overpayment_id = request_body.overpayment_id

    with app.db_session() as db_session:
        overpayment: Overpayment = get_or_404(db_session, Overpayment, overpayment_id)

        # Check if a Mmars event already exists
        existing_transaction = (
            db_session.query(MmarsEvent)
            .filter_by(overpayment_id=overpayment.overpayment_id)
            .first()
        )

        if (
            existing_transaction
        ):  # this only happens if the overpayment has already been referred in another browser/tab and the user is trying to refer it again
            return response_util.error_response(
                data={},
                status_code=BadRequest,
                message="A transaction for this overpayment already exists, indicating it has been previously referred",
                errors=[
                    ValidationErrorDetail(
                        field="overpayment_id",
                        message="A transaction already exists",
                        value=str(overpayment_id),
                        type=IssueType.invalid,
                    )
                ],
            ).to_api_response()

        if overpayment.claim is None:
            error_message = f"Claim information is missing for the overpayment with ID {overpayment.overpayment_id}"
            logger.error(error_message)
            raise ValueError(
                error_message
            )  # this is not an expected case. Therefore raise an exception

        # Ensure the payment type of overpayment is the correct type
        # This is not an expected case. Only the overpayments with PaymentTransactionType.OVERPAYMENT are displayed to the user
        if overpayment.payment_event_type_id != PaymentEventType.OVERPAYMENT.payment_event_type_id:
            raise ValueError(
                f"Invalid payment type: expected PaymentEventType.OVERPAYMENT: {PaymentEventType.OVERPAYMENT.payment_event_type_id} got {overpayment.payment_event_type_id}"
            )

        if overpayment.outstanding_amount == 0:

            logger.error(
                "Overpayment cannot be referred because it has already been fully recovered",
                extra={"overpayment_id": overpayment_id},
            )
            return response_util.error_response(
                data={},
                status_code=BadRequest,
                message="Overpayment has already been fully recovered",
                errors=[
                    ValidationErrorDetail(
                        field="overpayment_id",
                        message="Overpayment has already been fully recovered",
                        value=str(overpayment_id),
                        type=IssueType.invalid,
                    )
                ],
            ).to_api_response()
        # Create a new transaction
        new_transaction = MmarsEvent(
            overpayment_id=overpayment.overpayment_id,
            employee_id=overpayment.claim.employee_id,
            mmars_event_type_id=MmarsEventType.RE_TRX.mmars_event_type_id,
            mmars_status_type_id=MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id,
        )

        db_session.add(new_transaction)
        db_session.flush()  # have new_transaction.mmars_event_id populated

        add_mmars_event_action_log(
            db_session,
            new_transaction.mmars_event_id,
            MmarsEventActionType.REFERRED,
            azure_user.sub_id,
        )
        db_session.commit()

        # Fetch the updated overpayment with transactions
        updated_overpayment = (
            db_session.query(Overpayment).filter(Overpayment.overpayment_id == overpayment_id).one()
        )

        overpayment_case = OverpaymentCaseResponse.from_orm(updated_overpayment)

        return response_util.success_response(
            data=overpayment_case.dict(), message="Successfully referred overpayment"
        ).to_api_response()


def retry_overpayment_transaction(body: dict) -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(EDIT, azure_user)

    request_body = parse_request_body(OverpaymentRetryTransactionRequest, body)

    with app.db_session() as db_session:
        mmars_event: MmarsEvent = get_or_404(db_session, MmarsEvent, request_body.mmars_event_id)

        if (
            mmars_event.mmars_status_type_id not in ERROR_TO_PENDING_STATUS_MAP
        ):  # if the status is not an error status, then it cannot be retried
            logger.error(
                "Cannot retry overpayment transaction, status is not in an error stage.",
                extra={
                    "mmars_event_id": request_body.mmars_event_id,
                    "current_status": mmars_event.mmars_status_type_id,
                },
            )
            return response_util.error_response(
                data={},
                message="Cannot retry overpayment transaction, status is not in an error stage.",
                status_code=BadRequest,
                errors=[
                    ValidationErrorDetail(
                        field="mmars_event_id",
                        message="Invalid status for retry",
                        value=str(request_body.mmars_event_id),
                        type=IssueType.invalid,
                    )
                ],
            ).to_api_response()

        # Update the status to the corresponding pending status
        update_mmars_event_status(mmars_event)

        add_mmars_event_action_log(
            db_session, mmars_event.mmars_event_id, MmarsEventActionType.RETRIED, azure_user.sub_id
        )

        db_session.commit()

        mmars_event_response = MmarsEventResponse.from_orm(mmars_event)

        return response_util.success_response(
            data=mmars_event_response.dict(), message="Successfully retried overpayment transaction"
        ).to_api_response()


def hold_overpayment_transaction(body: dict) -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(EDIT, azure_user)

    request_body = parse_request_body(OverpaymentHoldTransactionRequest, body)
    with app.db_session() as db_session:
        mmars_event: MmarsEvent = get_or_404(db_session, MmarsEvent, request_body.mmars_event_id)
        if (
            mmars_event.mmars_status_type_id
            in payments_util.MMARS_Constants.NON_HOLDABLE_MMARS_EVENTS
        ):
            return response_util.error_response(
                data={},
                message="Cannot hold overpayment transaction, status is not in a holdable stage.",
                status_code=BadRequest,
                errors=[
                    ValidationErrorDetail(
                        field="mmars_event_id",
                        message="Invalid status for hold",
                        value=str(request_body.mmars_event_id),
                        type=IssueType.invalid,
                    )
                ],
            ).to_api_response()

        mmars_event.mmars_status_type_id = MmarsEventStatusType.ON_HOLD.mmars_event_status_type_id

        add_mmars_event_action_log(
            db_session,
            mmars_event.mmars_event_id,
            MmarsEventActionType.HELD,
            azure_user.sub_id,
            request_body.reason,
        )

        db_session.commit()
        mmars_event_response = MmarsEventResponse.from_orm(mmars_event)
        return response_util.success_response(
            data=mmars_event_response.dict(), message="Successfully held overpayment transaction"
        ).to_api_response()


def release_overpayment_transaction(body: dict) -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(EDIT, azure_user)

    request_body = parse_request_body(OverpaymentHoldTransactionRequest, body)
    with app.db_session() as db_session:
        mmars_event: MmarsEvent = get_or_404(db_session, MmarsEvent, request_body.mmars_event_id)
        if (
            mmars_event.mmars_status_type_id
            != MmarsEventStatusType.ON_HOLD.mmars_event_status_type_id
        ):
            return response_util.error_response(
                data={},
                message="Cannot remove hold on overpayment transaction, status is not on hold.",
                status_code=BadRequest,
                errors=[
                    ValidationErrorDetail(
                        field="mmars_event_id",
                        message="Invalid status for removing hold",
                        value=str(request_body.mmars_event_id),
                        type=IssueType.invalid,
                    )
                ],
            ).to_api_response()
        if mmars_event.mmars_event_type_id == MmarsEventType.RE_TRX.mmars_event_type_id:
            mmars_event.mmars_status_type_id = (
                MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
            )
        elif mmars_event.mmars_event_type_id == MmarsEventType.REM_TRX.mmars_event_type_id:
            mmars_event.mmars_status_type_id = (
                MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id
            )
        else:
            logger.error(
                "Cannot remove hold on overpayment transaction, status is not in a holdable stage.",
                extra={
                    "mmars_event_id": request_body.mmars_event_id,
                    "mmars_event_type": mmars_event.mmars_event_type_id,
                },
            )
            return response_util.error_response(
                data={},
                message="Cannot remove hold on overpayment transaction, status is not in a holdable stage.",
                status_code=BadRequest,
                errors=[
                    ValidationErrorDetail(
                        field="mmars_event_id",
                        message="Invalid status for removing hold",
                        value=str(request_body.mmars_event_id),
                        type=IssueType.invalid,
                    )
                ],
            ).to_api_response()

        add_mmars_event_action_log(
            db_session,
            mmars_event.mmars_event_id,
            MmarsEventActionType.RELEASED,
            azure_user.sub_id,
            request_body.reason,
        )

        db_session.commit()
        mmars_event_response = MmarsEventResponse.from_orm(mmars_event)
        return response_util.success_response(
            data=mmars_event_response.dict(),
            message="Successfully released an overpayment transaction",
        ).to_api_response()


def overpayment_vcm_report() -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    with app.db_session() as db_session:
        vcm_required_events = (
            db_session.query(MmarsEvent, MmarsCustomerDetail)
            .outerjoin(
                MmarsCustomerDetail, MmarsEvent.mmars_event_id == MmarsCustomerDetail.mmars_event_id
            )
            .filter(
                MmarsEvent.mmars_status_type_id
                == MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id
            )
            .options(joinedload(MmarsEvent.employee))
            .order_by(MmarsEvent.employee_id, MmarsCustomerDetail.created_at.desc())
            .all()
        )
        comparison_list = []
        previous_employee_id = None

        if vcm_required_events:
            for mmars_event, mmars_customer_detail in vcm_required_events:
                # do not include multiple mmars_event for the same employee
                if previous_employee_id == mmars_event.employee_id:
                    continue

                try:
                    # if mmars_customer_detail is not exist for this mmars_event, then the mmars_event may be switched to VCM_REQUIRE through manual SQL execution
                    # or the status has changed before MmarsCustomerDetail logic is added
                    if mmars_customer_detail is None:
                        # this will only contain employee's address information from PFML database not from MMARS
                        comparison_list.append(
                            VCMComparisonResponse.from_mmars_event(mmars_event).dict()
                        )
                    else:
                        comparison_list.append(
                            VCMComparisonResponse.from_mmars_customer_detail(
                                mmars_customer_detail,
                                mmars_event.employee.fineos_customer_number,
                                str(mmars_event.employee_id),
                            ).dict()
                        )
                except Exception as e:
                    logger.error(
                        "Failed to create VCMComparisonResponse",
                        exc_info=e,
                        extra={"mmars_event_id": mmars_event.mmars_event_id},
                    )

                previous_employee_id = mmars_event.employee_id

        response = response_util.success_response(
            data=comparison_list,
            message="Successfully retrieved VCM report",
        ).to_api_response()

        return response


def mark_overpayment_vcm_reviewed(body: dict) -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(EDIT, azure_user)

    request_body = parse_request_body(OverpaymentMarkVcmReviewedRequest, body)
    with app.db_session() as db_session:
        employee: Employee = get_or_404(db_session, Employee, request_body.employee_id)
        mmars_events = (
            db_session.query(MmarsEvent)
            .filter(
                MmarsEvent.employee_id == employee.employee_id,
                MmarsEvent.mmars_status_type_id
                == MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
            )
            .all()
        )

        if not mmars_events:
            return response_util.error_response(
                data={},
                message="No overpayment transactions found for the employee",
                status_code=BadRequest,
                errors=[
                    ValidationErrorDetail(
                        field="employee_id",
                        message="No overpayment transactions found for the employee",
                        value=str(request_body.employee_id),
                        type=IssueType.invalid,
                    )
                ],
            ).to_api_response()

        for mmars_event in mmars_events:
            mmars_event.mmars_status_type_id = (
                MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
            )
            add_mmars_event_action_log(
                db_session,
                mmars_event.mmars_event_id,
                MmarsEventActionType.VCM_REVIEWED,
                azure_user.sub_id,
            )

        db_session.commit()

    return response_util.success_response(
        message="Successfully marked overpayment transactions as reviewed",
    ).to_api_response()


def add_mmars_event_action_log(
    db_session: db.Session,
    mmars_event_id: UUID,
    action_type: LkMmarsEventActionType,
    admin_user_id: str,
    reason: Optional[str] = None,
) -> None:

    current_admin_user = None

    try:
        current_admin_user = get_admin_user_id(db_session, admin_user_id)
    except Exception as e:
        # We don't want to fail the hold if we can't get the admin user
        logger.error(
            "Failed to get admin user for oauth sub id",
            exc_info=e,
            extra={"sub_id": admin_user_id},
        )

    mmars_event_action = MmarsEventActionLog(
        mmars_event_id=mmars_event_id,
        mmars_event_action_type_id=action_type.mmars_event_action_type_id,
        reason=reason,
        admin_user_id=current_admin_user,
    )
    db_session.add(mmars_event_action)


def update_mmars_event_status(mmars_event: MmarsEvent) -> None:
    if not mmars_event.mmars_status_type_id:
        raise ValueError("mmars_event.mmars_status_type_id is required")
    mmars_event.mmars_status_type_id = ERROR_TO_PENDING_STATUS_MAP[mmars_event.mmars_status_type_id]


def omni_search_users(body: dict) -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)
    request_body = parse_request_body(OmniSearchRequest, body)
    search = request_body.search
    data = list()
    logger.info("Searching for users")
    with app.db_session() as db_session:
        filter_params_list = [
            cast(User.email_address, String),
            cast(User.first_name, String),
            cast(User.last_name, String),
            cast(User.auth_id, String),
            cast(User.user_id, String),
        ]
        filter_conds = list()
        filter_conds = [
            filter_params.ilike("%" + escape_like(search) + "%")
            for filter_params in filter_params_list
        ]

        users = db_session.query(User).filter(or_(*filter_conds)).limit(20)
        for user in users:
            data.append(UserResponse.from_orm(user).dict())

    response = response_util.success_response(
        data=data,
        message="The user omni search is complete.",
    ).to_api_response()

    return response


def omni_search_get_user_detail(user_id: str) -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)
    data = dict()
    logger.info(f"Getting user detail for user: {user_id}", extra={"user_id": user_id})
    with app.db_session() as db_session:
        user = db_session.query(User).filter(User.user_id == user_id).one()
        if user:
            data = UserResponse.from_orm(user).dict()
        response = response_util.success_response(
            data=data,
            message="Get user detail completed.",
        ).to_api_response()

        return response


def omni_search_get_user_auth_logs(user_id: str) -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)
    data = list()
    logger.info(f"Getting Oauth log data for user: {user_id}", extra={"user_id": user_id})
    with app.db_session() as db_session:
        user_auth_logs = (
            db_session.query(UserAuthLog)
            .filter(UserAuthLog.user_id == user_id)
            .order_by(UserAuthLog.started_at.desc())
            .all()
        )
        for user_auth_log in user_auth_logs:
            data.append(UserAuthLogResponse.from_orm(user_auth_log).dict())
        response = response_util.success_response(
            data=data,
            message="Get user detail completed.",
        ).to_api_response()

        return response


def require_ticket_num(func):
    def wrapper(*args, **kwargs):
        request_vals = kwargs["body"]

        if "ticket_num" not in request_vals or request_vals["ticket_num"].strip() == "":
            issues = ValidationErrorList()
            issues.add_validation_error(
                field="ticket_num",
                type=IssueType.required,
                message="Ticket number is required.",
            )
            return response_util.error_response(
                status_code=BadRequest,
                message="Request has issues.",
                errors=issues.get_errors(),
                data={},
            ).to_api_response()
        return func(*args, **kwargs)

    return wrapper


def get_admin_update_request_ticket_num(json: Dict[str, Any]) -> str:
    body: AdminUpdateBaseRequest = parse_request_body(AdminUpdateBaseRequest, json)
    return body.ticket_num


def get_admin_update_log_props(user, body):
    azure_user = app.azure_user()

    return {
        "admin_auth_id": azure_user.sub_id if azure_user else "[unknown]",
        "user_id": user.user_id,
        "ticket_num": get_admin_update_request_ticket_num(body),
    }


@require_ticket_num
def admin_users_patch(user_id: UUID, body: dict, **kwargs: dict[str, Any]) -> ConnexionResponse:
    """This endpoint modifies the user specified by the user_id"""

    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    with app.db_session() as db_session:
        user: User = get_or_404(db_session, User, user_id)

        log_extra = get_admin_update_log_props(user, body)
        logger.info("admin updating user", extra=log_extra)

        state_before = user.for_json()

        request_body = parse_request_body(AdminUserUpdateRequest, body)

        # sanitize request data
        request_body.email_address = request_body.email_address.lower().strip()
        request_body.auth_id = request_body.auth_id.lower().strip()

        issues = get_admin_users_patch_issues(db_session, request_body, user)
        if issues:
            logger.info("admin_users_patch failure - request has issues")
            return response_util.error_response(
                status_code=BadRequest,
                message="Request has issues.",
                errors=issues,
                data={},
            ).to_api_response()

        updated_user = admin_update_user(user, request_body)

        state_after = updated_user.for_json()

        create_audit_entry(
            db_session,
            get_admin_user_id(db_session, azure_user.sub_id),
            request_body.ticket_num,
            "user",
            user.user_id,
            state_before,
            state_after,
            log_extra,
        )

        data = UserResponse.from_orm(updated_user).dict()

    logger.info(
        "admin user update successful",
        extra={
            **log_extra,
            LogConstants.LOG_METRIC_ID: "admin_user_patch",
        },
    )

    return response_util.success_response(
        message="successfully updated user",
        data=data,
    ).to_api_response()


def admin_update_user(
    user: User,
    update_request: AdminUserUpdateRequest,
) -> User:

    for key in update_request.__fields_set__:
        value = getattr(update_request, key)

        if key == "auth_id" and value.strip() == "":
            # need to 'null' an empty auth_id to satisfy api validation
            setattr(user, key, None)
            continue

        setattr(user, key, value)
    return user


def get_admin_users_patch_issues(
    db_session: db.Session, user_patch_request: AdminUserUpdateRequest, user: User
) -> List[ValidationErrorDetail]:
    """Validate that the patch request is issue free"""
    issues = ValidationErrorList()

    patch_email: str = user_patch_request.email_address
    patch_auth_id: str = user_patch_request.auth_id

    if len(patch_email) == 0:
        issues.add_validation_error(
            field="email_address",
            type=IssueType.required,
            message="Email address is required",
        )

    if len(user_patch_request.ticket_num) == 0:
        issues.add_validation_error(
            field="ticket_num",
            type=IssueType.required,
            message="Ticket number is required",
        )

    query = db_session.query(User).filter(User.user_id != user.user_id)

    if patch_auth_id != "":
        query = query.filter(
            or_(User.email_address.ilike(patch_email), User.auth_id.ilike(patch_auth_id))
        )
    else:
        query = query.filter(User.email_address.ilike(patch_email))

    conflicting_users = query.order_by(User.email_address).all()
    for conflicting_user in conflicting_users:
        if conflicting_user.email_address.lower() == patch_email:
            issues.add_validation_error(
                field="email_address",
                type=IssueType.exists,
                message=f"User already exists with email address: '{patch_email}'",
            )
        if patch_auth_id != "" and conflicting_user.auth_id == patch_auth_id:
            issues.add_validation_error(
                field="auth_id",
                type=IssueType.exists,
                message=f"User already exists with auth id: {patch_auth_id}",
            )

    return issues.get_errors()


@require_ticket_num
def users_role_add(
    user_id: UUID, role_id: UUID, body: dict, **kwargs: dict[str, Any]
) -> ConnexionResponse:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    with app.db_session() as db_session:
        user = get_or_404(db_session, User, user_id)
        get_or_404(db_session, LkRole, role_id)

        log_extra = get_admin_update_log_props(user, body)
        logger.info("admin adding user role", extra=log_extra)

        user_role = (
            db_session.query(UserRole).filter(
                UserRole.user_id == user_id and UserRole.role_id == role_id
            )
        ).one_or_none()

        if user_role is not None:
            logger.info(
                "user role already present, nothing to do",
                extra=log_extra,
            )

            return response_util.success_response(
                message="Role was already assigned to user.",
                status_code=200,
                data=user_role.for_json(),
            ).to_api_response()

        ticket_num = get_admin_update_request_ticket_num(body)

        convert_employer_issues = get_users_convert_employer_issues(user, db_session)
        if convert_employer_issues:
            logger.info("users_role_add failure - Couldn't convert user to employer account")
            return response_util.error_response(
                status_code=BadRequest,
                message="Couldn't convert user to employer account",
                errors=convert_employer_issues,
                data={},
            ).to_api_response()

        log_extra["role_id"] = role_id

        user_role = UserRole(user_id=user_id, role_id=int(role_id))
        db_session.add(user_role)
        state_after = user_role.for_json()

        create_audit_entry(
            db_session,
            get_admin_user_id(db_session, azure_user.sub_id),
            ticket_num,
            "link_user_role",
            user.user_id,
            "[created]",
            state_after,
            log_extra,
        )

        logger.info(
            "admin added user role",
            extra={
                **log_extra,
                LogConstants.LOG_METRIC_ID: "admin_user_role_add",
            },
        )

        return response_util.success_response(
            message="Role was added to user successfully",
            status_code=200,
            data=user_role.for_json(),
        ).to_api_response()


@require_ticket_num
def users_role_delete(user_id, role_id, body, **kwargs):
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    with app.db_session() as db_session:
        user = get_or_404(db_session, User, user_id)

        log_extra = {
            **get_admin_update_log_props(user, body),
            "role_id": role_id,
        }

        logger.info("admin removing user role", extra=log_extra)

        user_role = (
            db_session.query(UserRole).filter(
                UserRole.user_id == user_id and UserRole.role_id == role_id
            )
        ).one_or_none()

        # no role present, do nothing
        if user_role is None:  # no role present, do nothing
            logger.info(
                "user role not present, nothing to do",
                extra=log_extra,
            )

            return response_util.success_response(
                message="Role was not assigned to user.", status_code=200, data={}
            ).to_api_response()

        request_body: AdminUpdateBaseRequest = parse_request_body(AdminUpdateBaseRequest, body)
        ticket_num = request_body.ticket_num

        convert_claimant_issues = get_users_convert_claimant_issues(user)
        if convert_claimant_issues:
            logger.info("users_role_delete failure - Couldn't convert user to claimant account")
            return response_util.error_response(
                status_code=BadRequest,
                message="Couldn't convert user to claimant account",
                errors=convert_claimant_issues,
                data={},
            ).to_api_response()

        state_before = user_role.for_json()

        remove_leave_admins_and_role(db_session, user)

        create_audit_entry(
            db_session,
            get_admin_user_id(db_session, azure_user.sub_id),
            ticket_num,
            "link_user_role",
            user.user_id,
            state_before,
            "[deleted]",
            log_extra,
        )

        logger.info(
            "admin removed user role",
            extra={
                **log_extra,
                LogConstants.LOG_METRIC_ID: "admin_user_role_delete",
            },
        )

        return response_util.success_response(
            message="Role was deleted from user successfully", status_code=200, data={}
        ).to_api_response()


def audit_logs(filter_id):
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    data = list()

    with app.db_session() as db_session:
        logs = (
            db_session.query(AdminAuditLog)
            .filter(
                or_(AdminAuditLog.admin_user_id == filter_id, AdminAuditLog.record_id == filter_id)
            )
            .order_by(AdminAuditLog.created_at)
        )
        for log in logs:
            data.append(AuditLogResponse.from_orm(log).dict())

    return response_util.success_response(
        message="Successfully retrieved audit logs",
        data=data,
    ).to_api_response()


def omni_search_user_auth_logs(body: dict) -> flask.Response:
    azure_user = app.azure_user()
    # This should never be the case.
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)
    request_body = parse_request_body(OmniSearchRequest, body)
    search = request_body.search
    data = list()
    logger.info("Searching user auth logs")
    with app.db_session() as db_session:
        user_auth_logs = (
            db_session.query(UserAuthLog)
            .filter(cast(UserAuthLog.meta_data, Text).ilike("%" + escape_like(search) + "%"))
            .order_by(desc(UserAuthLog.completed_at), desc(UserAuthLog.started_at))
            .limit(20)
        )
        for user_auth_log in user_auth_logs:
            data.append(UserAuthLogResponse.from_orm(user_auth_log).dict())

    response = response_util.success_response(
        data=data,
        message="The user omni search is complete.",
    ).to_api_response()

    return response


def get_reassign_applications_issues(
    db_session: db.Session,
    from_user_id: UUID,
    to_user_id: UUID,
    application_ids_to_transfer: List[UUID],
    applications: List[Application],
) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()
    from_user = db_session.get(User, from_user_id)
    to_user = db_session.get(User, to_user_id)

    if from_user_id == to_user_id:
        issues.add_validation_error(
            field="from_user_id",
            type=IssueType.invalid,
            message="Source and target users cannot be the same.",
        )

    if not from_user:
        issues.add_validation_error(
            field="from_user_id",
            type=IssueType.object_not_found,
            message=f"User with ID {from_user_id} does not exist.",
        )

    if not to_user:
        issues.add_validation_error(
            field="to_user_id",
            type=IssueType.object_not_found,
            message=f"User with ID {to_user_id} does not exist.",
        )
    else:
        # Check if the target user is not an employee user
        if to_user.roles and len(to_user.roles) > 0:
            issues.add_validation_error(
                field="to_user_id",
                type=IssueType.invalid,
                message=f"Target user {to_user_id} is not an employee user",
            )

    if not applications:
        issues.add_validation_error(
            field="application_ids",
            type=IssueType.object_not_found,
            message="No applications found for the specified user.",
        )
    else:
        # all application ids owned by the source user
        source_user_application_ids = set(app.application_id for app in applications)

        # find the application ids that are requested to transfer but do not belong to source user
        invalid_application_ids = list(
            set(application_ids_to_transfer) - source_user_application_ids
        )

        if invalid_application_ids:
            issues.add_validation_error(
                field="application_ids",
                type=IssueType.object_not_found,
                message=f"Some application IDs: {invalid_application_ids} are not present for source user: {from_user_id}",
            )

    return issues.get_errors()


@require_ticket_num
def reassign_applications(body: dict, **kwargs: dict[str, Any]) -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(EDIT, azure_user)
    request_body = parse_request_body(ApplicationReassignmentRequest, body)
    from_user_id = request_body.from_user_id
    to_user_id = request_body.to_user_id
    application_ids_to_transfer = request_body.application_ids

    with app.db_session() as db_session:
        applications = (
            db_session.query(Application).filter(Application.user_id == from_user_id).all()
        )

        validation_errors = get_reassign_applications_issues(
            db_session, from_user_id, to_user_id, application_ids_to_transfer, applications
        )
        if validation_errors:
            validation_errors_string = ", ".join(
                [validation_error.message for validation_error in validation_errors]
            )
            logger.warning(
                f"Application reassignment request has validation errors: {validation_errors_string}"
            )

            return response_util.error_response(
                status_code=BadRequest,
                message="Request is invalid",
                data={},
                errors=validation_errors,
            ).to_api_response()
        applications_to_transfer = (
            db_session.query(Application)
            .filter(Application.application_id.in_(application_ids_to_transfer))
            .all()
        )
        # Assign target user's ID to the applications
        for application in applications_to_transfer:

            # if application is attached to a claim and claim does not have a user_id then we log this as a warning
            if application.claim is not None and application.claim.employee_id is None:
                logger.warning(
                    f"Application {application.application_id} is attached to a claim that has no employee attached.",
                    extra={"claim_id": application.claim.claim_id},
                )

            # capture the application snapshot before the change
            state_before = application.for_json()

            # reassign the application to the target user
            application.user_id = to_user_id

            # capture the application snapshot after the change
            state_after = application.for_json()

            log_extra = {
                "from_user_id": from_user_id,
                "to_user_id": to_user_id,
                "application_id": application.application_id,
            }
            create_audit_entry(
                db_session=db_session,
                admin_user_id=get_admin_user_id(db_session, azure_user.sub_id),
                ticket_number=request_body.ticket_num,
                record_type="application",
                record_id=application.application_id,
                state_before=state_before,
                state_after=state_after,
                log_extra=log_extra,
            )

        db_session.commit()
        logger.info(
            f"Reassigned {len(applications_to_transfer)} applications tranferred from user {from_user_id} to user {to_user_id}",
        )

        # Refresh the users to get the latest state with the updated applications
        from_user = db_session.get(User, from_user_id)
        to_user = db_session.get(User, to_user_id)

        # create the review response that contains users' application details to reflect the changes
        # the arg-type is ignored because the validation function ensures that the inputs are not None
        review_response = ApplicationReassignmentPreviewResponse(
            from_user=from_user, to_user=to_user  # type: ignore[arg-type]
        )

        response = response_util.success_response(
            data=review_response.dict(),
            message=f"Successfully reassigned {len(applications)} applications.",
            status_code=200,
        ).to_api_response()

        return response


def get_user_applications(user_id: str) -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    with app.db_session() as db_session:
        user = get_or_404(db_session, User, UUID(user_id))

    applications = (
        db_session.query(Application)
        .filter(Application.user_id == user.user_id)
        .options(joinedload(Application.claim))  # Eager load to retreive fineos absence id
        .order_by(Application.created_at.desc())
        .all()
    )
    if not applications:
        return response_util.success_response(
            message="No applications found for the user.",
            data=[],
        ).to_api_response()

    # fineos_absence_id field is not included in the Application model's for_json method,
    # so we need to manually add it to the response data
    application_data = []
    for application in applications:
        app_json = application.for_json()
        app_json["fineos_absence_id"] = application.fineos_absence_id
        application_data.append(app_json)

    response = response_util.success_response(
        data=application_data,
        message=f"Successfully retrieved {len(applications)} applications for user {user.email_address}.",
    ).to_api_response()

    return response


def get_application_reassignment_preview_issues(
    db_session: db.Session,
    from_user_email: str,
    to_user_email: str,
    from_user: Optional[User] = None,
    to_user: Optional[User] = None,
) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()

    if not from_user:
        issues.add_validation_error(
            field="from_user_email",
            type=IssueType.object_not_found,
            message=f"User with email {from_user_email} does not exist.",
        )
    else:
        query_application_exist = select(exists().where(Application.user_id == from_user.user_id))
        application_exist = bool(db_session.execute(query_application_exist).scalar())
        if not application_exist:
            issues.add_validation_error(
                type=IssueType.object_not_found,
                message=f"No applications found for the user with email {from_user_email}.",
            )

    if not to_user:
        issues.add_validation_error(
            field="to_user_email",
            type=IssueType.object_not_found,
            message=f"User with email {to_user_email} does not exist.",
        )
    else:
        # Check if the target user is not an employee user
        if to_user.roles and len(to_user.roles) > 0:
            issues.add_validation_error(
                field="to_user_email",
                type=IssueType.invalid,
                message=f"Target user {to_user_email} is not an employee user.",
            )

    return issues.get_errors()


def application_reassignment_preview(body: dict) -> flask.Response:
    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    request_body = parse_request_body(ApplicationReassignmentPreviewRequest, body)

    with app.db_session() as db_session:

        from_user = (
            db_session.query(User)
            .filter(User.email_address == request_body.from_user_email)
            .one_or_none()
        )
        to_user = (
            db_session.query(User)
            .filter(User.email_address == request_body.to_user_email)
            .one_or_none()
        )
        validation_errors = get_application_reassignment_preview_issues(
            db_session, request_body.from_user_email, request_body.to_user_email, from_user, to_user
        )

        if validation_errors:
            validation_errors_string = ", ".join(
                [validation_error.message for validation_error in validation_errors]
            )
            logger.info(
                f"Application reassignment preview request has validation errors: {validation_errors_string}"
            )

            return response_util.error_response(
                status_code=BadRequest,
                message="Request is invalid",
                data={},
                errors=validation_errors,
            ).to_api_response()

        # create the review response that contains users' application details
        # this is used to preview the applications that will be reassigned
        # the arg-type is ignored because the validation function ensures that the inputs are not None
        review_response = ApplicationReassignmentPreviewResponse(
            from_user=from_user, to_user=to_user  # type: ignore[arg-type]
        )

        response = response_util.success_response(
            data=review_response.dict(),
            message="Successfully retrieved applications for preview.",
        ).to_api_response()

        return response


def get_dynamic_configs() -> flask.Response:

    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    logger.info("Retrieving dynamic config entries")

    with app.db_session() as db_session:
        entries = [
            entry.for_json() for entry in dynamic_config.get_all_dynamic_config_entries(db_session)
        ]

    response = response_util.success_response(
        data=entries,
        message="Got dynamic config entries.",
    ).to_api_response()

    return response


def post_dynamic_config(body: dict) -> flask.Response:

    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(EDIT, azure_user)

    config_key: str = body["config_key"]
    config_context: str = body["config_context"]

    logger.info(f"Creating dynamic config entry: {config_key} / {config_context}")

    with app.db_session() as db_session:
        result = dynamic_config.create_dynamic_config_entry(
            db_session,
            admin_user_id=get_admin_user_id(db_session, azure_user.sub_id),
            config_key=config_key,
            config_context=config_context,
            config_value=body.get("config_value", None),
        )

    response = response_util.success_response(
        data=result.for_json(),
        status_code=201,
        message="Created dynamic config entry.",
    ).to_api_response()

    return response


def patch_dynamic_config(dynamic_config_id: UUID, body: dict) -> flask.Response:

    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(EDIT, azure_user)

    with app.db_session() as db_session:
        result = dynamic_config.update_dynamic_config_entry(
            db_session,
            admin_user_id=get_admin_user_id(db_session, azure_user.sub_id),
            dynamic_config_id=dynamic_config_id,
            config_value=body.get("config_value", None),
        )

    response = response_util.success_response(
        data=result.for_json(),
        status_code=200,
        message="Updated dynamic config entry.",
    ).to_api_response()

    return response


def delete_dynamic_config(dynamic_config_id: UUID) -> flask.Response:

    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(EDIT, azure_user)

    with app.db_session() as db_session:
        dynamic_config.delete_dynamic_config_entry(
            db_session,
            admin_user_id=get_admin_user_id(db_session, azure_user.sub_id),
            dynamic_config_id=dynamic_config_id,
        )

    response = response_util.success_response(
        status_code=204,
        message="Deleted dynamic config entry.",
    ).to_api_response()

    return response


def get_dynamic_config_logs() -> flask.Response:

    azure_user = app.azure_user()
    if azure_user is None:
        raise Unauthorized
    ensure(READ, azure_user)

    logger.info("Retrieving dynamic config logs")

    with app.db_session() as db_session:
        logs = dynamic_config.get_dynamic_config_log_entries(db_session)

    response = response_util.success_response(
        data=[log.for_json() for log in logs],
        message="Got dynamic config logs.",
    ).to_api_response()

    return response
