from datetime import date

from pydantic.types import UUID4

from massgov.pfml.api.constants.documents import EMPLOYEE_LEGAL_DOC_TYPES
from massgov.pfml.api.models.documents.common import DocumentType as DocumentTypeModel
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.models.documents import Document
from massgov.pfml.util.documents import validate_content_type
from massgov.pfml.util.pydantic import PydanticBaseModel


class DocumentResponse(PydanticBaseModel):
    name: str
    description: str
    is_legal_notice: bool
    user_id: UUID4 | None
    application_id: UUID4 | None
    appeal_id: UUID4 | None
    created_at: date | None
    document_type: str | None
    content_type: str | None
    fineos_document_id: str | None
    pfml_document_type: str | None

    @classmethod
    def from_orm(cls, document: Document, content_type: str | None = None) -> "DocumentResponse":

        if content_type:
            # validates based on massgov.pfml.api.models.applications.common.ContentType
            validate_content_type(content_type)

        document_type_description = DocumentType.get_description(document.document_type_id)
        # TODO (PFMLPB-25436): remove this pregnancy_maternity_form document_type
        if document_type_description == DocumentTypeModel.pregnancy_and_maternity_form.value:
            document_type_description = DocumentTypeModel.pregnancy_maternity_form.value

        pfml_document_type_description = (
            DocumentType.get_description(document.pfml_document_type_id)
            if document.pfml_document_type_id
            else None
        )
        # TODO (PFMLPB-25436): remove this pregnancy_maternity_form document_type
        if pfml_document_type_description == DocumentTypeModel.pregnancy_and_maternity_form.value:
            pfml_document_type_description = DocumentTypeModel.pregnancy_maternity_form.value

        is_legal_notice = document_type_description in [
            doc_type.document_type_description for doc_type in EMPLOYEE_LEGAL_DOC_TYPES
        ]

        document_response = DocumentResponse(
            user_id=document.user_id,
            application_id=document.application_id,
            appeal_id=document.appeal_id,
            created_at=document.created_at,
            content_type=content_type,
            document_type=document_type_description,
            fineos_document_id=str(document.fineos_id),
            name=document.name,
            description=document.description,
            is_legal_notice=is_legal_notice,
            pfml_document_type=pfml_document_type_description,
        )
        return document_response
