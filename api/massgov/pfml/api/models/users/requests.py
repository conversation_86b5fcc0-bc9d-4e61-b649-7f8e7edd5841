from typing import List, Optional
from uuid import UUID

from massgov.pfml.api.models.admin.users.requests import AdminUpdateBaseRequest
from massgov.pfml.api.models.applications.common import Language
from massgov.pfml.api.models.phones.common import Phone
from massgov.pfml.api.models.users.responses import AuthCodeResponse, AuthURIResponse
from massgov.pfml.db.models.employees import UserLeaveAdministrator
from massgov.pfml.util.pydantic import PydanticBaseModel
from massgov.pfml.util.pydantic.types import FEINUnformattedStr


class RoleRequest(PydanticBaseModel):
    role_description: Optional[str]


class UserLeaveAdminRequest(PydanticBaseModel):
    employer_fein: Optional[FEINUnformattedStr]
    user_id: Optional[UUID]
    employer_id: Optional[UUID]

    def to_db_model(self) -> UserLeaveAdministrator:
        user_administrator = UserLeaveAdministrator()
        if self.user_id:
            user_administrator.user_id = self.user_id

        if self.employer_id:
            user_administrator.employer_id = self.employer_id
        return user_administrator


class UserLeaveAdminAddRequest(PydanticBaseModel):
    email_address: str
    employer_id: UUID

    def to_db_model(self) -> UserLeaveAdministrator:
        return UserLeaveAdministrator(employer_id=self.employer_id)


class UserCreateRequest(PydanticBaseModel):
    # Enforcement of these fields' presence is via user_rules.py
    email_address: Optional[str]
    password: Optional[str]
    role: Optional[RoleRequest]
    user_leave_administrator: Optional[UserLeaveAdminRequest]


class UserUpdateRequest(PydanticBaseModel):
    consented_to_data_sharing: Optional[bool]
    consented_to_view_tax_documents: Optional[bool]
    first_name: Optional[str]
    last_name: Optional[str]
    phone: Optional[Phone]
    language_preference: Optional[Language]


class UserConvertEmployerRequest(PydanticBaseModel):
    employer_fein: FEINUnformattedStr


class AdminTokenRequest(PydanticBaseModel):
    auth_uri_res: AuthURIResponse
    auth_code_res: AuthCodeResponse


class UserProfileUpdateRequest(PydanticBaseModel):
    from_application: UUID
    profile_fields_include: set[str]


class UserProfileCheckForUpdatesRequest(PydanticBaseModel):
    from_application: UUID


class ApplicationReassignmentRequest(AdminUpdateBaseRequest):
    from_user_id: UUID
    to_user_id: UUID
    application_ids: List[UUID]


class ApplicationReassignmentPreviewRequest(PydanticBaseModel):
    from_user_email: str
    to_user_email: str
