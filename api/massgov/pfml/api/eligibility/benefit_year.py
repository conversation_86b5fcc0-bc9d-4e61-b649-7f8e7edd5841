from datetime import date
from decimal import Decimal
from typing import Optional

from pydantic.types import UUID4
from sqlalchemy import and_, or_

import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.api.eligibility.benefit_year_dates import (
    BenefitYearDateRange,
    calculate_benefit_year_dates,
)
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.models.employees import BenefitYear, BenefitYearContribution, Claim, Employee

logger = massgov.pfml.util.logging.get_logger(__name__)


def get_benefit_year_for_leave_start_date(
    db_session: db.Session, employee_id: UUID4, leave_start_date: date
) -> Optional[BenefitYear]:
    dates = calculate_benefit_year_dates(leave_start_date)
    found_benefit_year: "Optional[BenefitYear]" = (
        db_session.query(BenefitYear)
        .join(Employee, BenefitYear.employee_id == Employee.employee_id)
        .filter(
            Employee.employee_id == employee_id,
            BenefitYear.start_date <= dates.start_date,
            dates.start_date <= BenefitYear.end_date,
        )
        .one_or_none()
    )

    return found_benefit_year


def get_benefit_year_from_claim(db_session: db.Session, claim: Claim) -> Optional[BenefitYear]:
    """
    Find the BenefitYear that overlaps with any of the claim's key dates
    (claim start, application start, or approval date).
    """

    claim_start_date = claim.claim_start_date
    application_start_date = claim.application.start_date if claim.application else None
    claim_approval_date = claim.approval_date
    employee_id = claim.employee_id

    log_extra = {
        "claim_id": str(claim.claim_id),
        "employee_id": str(employee_id),
        "claim_start_date": claim_start_date,
        "application_start_date": application_start_date,
        "claim_approval_date": claim_approval_date,
    }

    if not all([claim_start_date, application_start_date, claim_approval_date]):
        logger.warning("Claim is missing one or more dates", extra=log_extra)

    claim_start_benefit_year = (
        calculate_benefit_year_dates(claim_start_date) if claim_start_date else None
    )
    application_start_benefit_year = (
        calculate_benefit_year_dates(application_start_date) if application_start_date else None
    )
    claim_approval_benefit_year = (
        calculate_benefit_year_dates(claim_approval_date) if claim_approval_date else None
    )

    benefit_years = [
        claim_start_benefit_year,
        application_start_benefit_year,
        claim_approval_benefit_year,
    ]

    or_conditions = [
        and_(BenefitYear.start_date <= by.start_date, by.start_date <= BenefitYear.end_date)
        for by in benefit_years
        if by is not None
    ]

    if not or_conditions:
        logger.warning("No valid benefit year conditions found for claim", extra=log_extra)
        return None
    else:
        logger.info("Searching for benefit year with conditions", extra=log_extra)

    found_benefit_year: "Optional[BenefitYear]" = (
        db_session.query(BenefitYear)
        .join(Employee, BenefitYear.employee_id == Employee.employee_id)
        .filter(Employee.employee_id == employee_id, or_(*or_conditions))
        .one_or_none()
    )

    if found_benefit_year is not None:
        logger.info(
            f"Found benefit year {found_benefit_year.start_date}",
            extra=log_extra | {"benefit_year_id": str(found_benefit_year.benefit_year_id)},
        )

    return found_benefit_year


def get_benefit_year_by_benefit_year_start_date(
    db_session: db.Session, employee_id: UUID4, benefit_year_start_date: date
) -> Optional[BenefitYear]:
    found_benefit_year: "Optional[BenefitYear]" = (
        db_session.query(BenefitYear)
        .join(Employee, BenefitYear.employee_id == Employee.employee_id)
        .filter(
            Employee.employee_id == employee_id, BenefitYear.start_date == benefit_year_start_date
        )
        .one_or_none()
    )

    return found_benefit_year


def retrieve_eaww(
    db_session: db.Session,
    benefit_year: BenefitYear,
    employer_id: UUID4,
) -> Optional[Decimal]:
    """
    Retrieve the previously stored Employer Average Weekly Wage (EAWW) for the given
    employer in the given benefit year, without recomputing.
    """
    benefit_year_contribution = (
        db_session.query(BenefitYearContribution)
        .filter(
            BenefitYearContribution.benefit_year_id == benefit_year.benefit_year_id,
            BenefitYearContribution.employer_id == employer_id,
        )
        .one_or_none()
    )

    employer_average_weekly_wage = (
        benefit_year_contribution.average_weekly_wage if benefit_year_contribution else None
    )

    return employer_average_weekly_wage


def get_claims_within_benefit_year_count(
    db_session: db.Session, employee_id: UUID4, benefit_year_dates: BenefitYearDateRange
) -> int:
    return (
        db_session.query(Claim)
        .filter(
            Claim.employee_id == employee_id,
            Claim.claim_start_date.isnot(None),
            Claim.claim_start_date.between(
                benefit_year_dates.start_date, benefit_year_dates.end_date
            ),
            Claim.fineos_absence_status_id == AbsenceStatus.APPROVED.absence_status_id,
        )
        .count()
    )
