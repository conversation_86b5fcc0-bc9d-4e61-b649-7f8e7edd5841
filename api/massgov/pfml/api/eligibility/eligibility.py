#
# Financial eligibility - main algorithm.
#
import math
from datetime import date
from decimal import Decimal
from typing import Op<PERSON>, <PERSON><PERSON>, TypedDict
from uuid import UUID

from pydantic.types import UUID4

import massgov.pfml.api.eligibility.eligibility_util as eligibility_util
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.api.eligibility.benefit_year import (
    BenefitYear,
    get_benefit_year_by_benefit_year_start_date,
    get_claims_within_benefit_year_count,
    retrieve_eaww,
)
from massgov.pfml.api.eligibility.benefit_year_dates import (
    BenefitYearDateRange,
    calculate_benefit_year_dates,
)
from massgov.pfml.api.eligibility.eligibility_date import eligibility_date
from massgov.pfml.api.eligibility.eligibility_wage_scenario_data_logging import (
    wage_scenario_logging_for_base_period,
    wage_scenario_logging_for_benefit_year,
)
from massgov.pfml.api.eligibility.response import EligibilityResult
from massgov.pfml.api.models.applications.common import EligibilityEmploymentStatus
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import Claim, LeaveRequest, WagesAndContributions
from massgov.pfml.services.wages import get_base_period_quarters
from massgov.pfml.services.wages.create_financial_eligibility_calculation import (
    create_financial_eligibility_calculation_record_from_base_period_wages,
    create_financial_eligibility_calculation_record_from_benefit_year,
)
from massgov.pfml.services.wages.employee_wage_data import (
    has_duplicated_employer_quarter_combinations,
)
from massgov.pfml.services.wages.get_average_weekly_wage import (
    get_employer_average_weekly_wage,
    get_individual_average_weekly_wage,
)
from massgov.pfml.services.wages.get_employee_wage_data import get_employee_wage_data
from massgov.pfml.services.wages.get_employee_wage_data_for_eligibility import (
    get_employee_wage_data_for_eligibility,
)
from massgov.pfml.util.datetime.quarter import Quarter
from massgov.pfml.util.decimals import round_nearest_hundredth

logger = massgov.pfml.util.logging.get_logger(__name__)


class EligibilityLogExtra(TypedDict):
    absence_case_id: str
    employee_id: UUID4
    employer_id: UUID4
    benefit_year_id: Optional[UUID4]
    request_decision: Optional[str]
    quarterly_wages: str
    num_quarterly_wages: Optional[int]
    employee_employer_key: str  # for deduplicating FE calls


class ClaimsLogExtra(TypedDict):
    total_employee_claims: int
    is_first_claim: bool
    total_employee_claims_in_benefit_year: int
    is_first_claim_in_new_benefit_year: bool
    is_benefit_year_missing_for_claims: bool
    is_entitlement_period_not_on_sunday: bool
    benefit_year_start_date: date
    calculated_benefit_year_start_date: date
    calculated_benefit_year_end_date: date


def _compute_financial_eligibility(
    employment_status: str,
    state_average_weekly_wage: Decimal,
    maximum_weekly_benefit_amount: Decimal,
    unemployment_minimum_earnings: Decimal,
    total_wages: Decimal,
    individual_average_weekly_wage: Decimal,
    average_weekly_wage: Decimal,
    num_quarters_with_wages: int,
) -> EligibilityResult:
    unemployment_min_met = eligibility_util.wages_gte_unemployment_min(
        total_wages, unemployment_minimum_earnings
    )

    gte_thirty_times_wba = eligibility_util.wages_gte_thirty_times_wba(
        total_wages,
        individual_average_weekly_wage,
        state_average_weekly_wage,
        maximum_weekly_benefit_amount,
    )

    # Check various financial eligibility thresholds, set the description accordingly
    financially_eligible = False
    if not unemployment_min_met:
        description = "Claimant wages under minimum"

    elif not gte_thirty_times_wba:
        description = "Claimant wages failed 30x rule"

    elif (
        employment_status == EligibilityEmploymentStatus.self_employed
        and num_quarters_with_wages < 2
    ):
        description = "Opt-in quarterly contributions not met"

    else:
        financially_eligible = True
        description = "Financially eligible"

    return EligibilityResult(
        financially_eligible=financially_eligible,
        description=description,
        total_wages=total_wages,
        state_average_weekly_wage=state_average_weekly_wage,
        unemployment_minimum=unemployment_minimum_earnings,
        average_weekly_wage=average_weekly_wage,
    )


# TODO:
# Remove use of absence_period.leave_request_decision when FF goes live
def get_leave_request_decision(
    db_session: db.Session, claim: Claim, start_date: date
) -> Optional[str]:
    absence_period: Optional[AbsencePeriod] = (
        db_session.query(AbsencePeriod)
        .filter(
            AbsencePeriod.claim_id == claim.claim_id,
            AbsencePeriod.absence_period_start_date == start_date,
        )
        .one_or_none()
    )
    if absence_period:
        leave_request: Optional[LeaveRequest] = (
            db_session.query(LeaveRequest)
            .filter(
                LeaveRequest.claim_id == claim.claim_id,
                LeaveRequest.fineos_leave_request_id == absence_period.fineos_leave_request_id,
            )
            .one_or_none()
        )
        if leave_request:
            return leave_request.leave_approval_decision.leave_request_decision_description

        if absence_period.leave_request_decision:
            return absence_period.leave_request_decision.leave_request_decision_description

    return None


def recalculate_0_dollar_aww(
    claim: Claim, iaww: Decimal, meta: EligibilityLogExtra
) -> Tuple[Decimal, bool]:
    """
    $0 AWW is a special case,
    see https://lwd.atlassian.net/wiki/spaces/DD/pages/3670966482/0+AWW+Tech+Spec
    """

    recalculated_aww = Decimal(0)
    is_recalculated = False
    match claim.has_concurrent_employers():
        case True:
            recalculated_aww = round_nearest_hundredth(
                Decimal(math.ceil(iaww * claim.percentage_hours_with_employer()))
            )
            is_recalculated = True
            logger.info(
                "Prorated AWW for concurrent employment, "
                f"previous AWW was {iaww} new AWW is {recalculated_aww}",
                extra=meta,
            )
        case False:
            recalculated_aww = iaww
            is_recalculated = True
            logger.info(
                f"No concurrent employment, AWW is set to IAWW of {recalculated_aww}",
                extra=meta,
            )
        # This Claim was created before we enabled this feature
        case None:
            logger.info(
                "While prorating IAWW, did not find has_concurrent_employers info. "
                f"IAWW is {iaww} but AWW was not recalculated.",
                extra=meta,
            )
            return (Decimal(0), False)
    logger.info(
        "Employer Average Weekly Wage (EAWW) with $0 has "
        f"recalculated AWW of {recalculated_aww}",
        extra=meta,
    )
    return (recalculated_aww, is_recalculated)


def process_0_dollar_aww(
    db_session: db.Session,
    benefit_year: BenefitYear,
    effective_date: date,
    meta: EligibilityLogExtra,
    claim: Claim | None,
    metrics: eligibility_util.StateMetrics,
    absence_case_id: str,
) -> EligibilityResult:
    """
    $0 AWW is a special case

    This function generates and returns an EligibilityResult with a recalculated AWW based on the IAWW
    see: https://lwd.atlassian.net/wiki/spaces/DD/pages/3670966482/0+AWW+Tech+Spec
    """
    base_period_wages = get_employee_wage_data(
        db_session,
        benefit_year.employee.employee_id,
        benefit_year.base_period_start_quarter,
        benefit_year.base_period_end_quarter,
        effective_date,
    )
    iaww = get_individual_average_weekly_wage(base_period_wages)
    if claim is None:
        return EligibilityResult(
            financially_eligible=True,
            description="Financially eligible",
            total_wages=benefit_year.total_wages,
            state_average_weekly_wage=metrics[0].average_weekly_wage,
            unemployment_minimum=metrics[1].unemployment_minimum_earnings,
            average_weekly_wage=Decimal(0),
        )
    used_aww, is_recalculated = recalculate_0_dollar_aww(claim, iaww, meta)
    if is_recalculated:
        logger.info("Recalculated $0 AWW to be non-zero and passed FE", extra=meta)
    eligibility_result = EligibilityResult(
        financially_eligible=True,
        description="Financially eligible",
        total_wages=benefit_year.total_wages,
        state_average_weekly_wage=metrics[0].average_weekly_wage,
        unemployment_minimum=metrics[1].unemployment_minimum_earnings,
        average_weekly_wage=used_aww,
    )
    wage_scenario_logging_for_base_period(
        db_session,
        base_period_wages,
        absence_case_id,
        eligibility_result,
    )
    return eligibility_result


def retrieve_financial_eligibility(
    db_session: db.Session,
    employee_id: UUID4,
    employer_id: UUID4,
    leave_start_date: date,
    application_submitted_date: date,
    employment_status: EligibilityEmploymentStatus,
    expected_benefit_year_start_date: date,
    absence_case_id: str,
) -> EligibilityResult:
    """Checks to see if a Benefit Year exists for the given employee's leave start date,
    If one exists, use persisted values for the employee's total wages and employer IAWW
    to serve financial eligibility request.
    Otherwise, Compute financial eligibility to serve the request and store on a new
    Benefit Year values for the employee's total wages and employer IAWW.

    see https://lwd.atlassian.net/wiki/spaces/API/pages/2681077805/Evaluating+financial+eligibility
    """
    meta: EligibilityLogExtra = {
        "absence_case_id": absence_case_id,
        "employee_id": employee_id,
        "employer_id": employer_id,
        "benefit_year_id": None,
        "request_decision": None,
        "quarterly_wages": "",
        "num_quarterly_wages": None,
        "employee_employer_key": f"{employee_id}-{employer_id}",
    }

    claim = db_session.query(Claim).filter(Claim.fineos_absence_id == absence_case_id).one_or_none()

    sunday_on_or_before_leave_start_date = calculate_benefit_year_dates(leave_start_date).start_date
    effective_date = eligibility_date(
        sunday_on_or_before_leave_start_date, application_submitted_date
    )
    metrics = eligibility_util.fetch_state_metric(db_session, sunday_on_or_before_leave_start_date)

    found_benefit_year = get_benefit_year_by_benefit_year_start_date(
        db_session, employee_id, expected_benefit_year_start_date
    )

    if found_benefit_year:
        return retrieve_financial_eligibility_for_existing_benefit_year(
            db_session,
            found_benefit_year,
            employer_id,
            absence_case_id,
            claim,
            effective_date,
            metrics,
            meta,
        )

    else:
        benefit_year_dates = calculate_benefit_year_dates(expected_benefit_year_start_date)

        claims_meta = log_claims_metrics(
            db_session,
            employee_id,
            benefit_year_dates,
            expected_benefit_year_start_date,
            meta,
        )

        return retrieve_financial_eligibility_for_new_benefit_year(
            db_session,
            employee_id,
            employer_id,
            absence_case_id,
            claim,
            metrics,
            employment_status,
            benefit_year_dates,
            leave_start_date,
            effective_date,
            meta,
            claims_meta,
        )


def log_claims_metrics(
    db_session: db.Session,
    employee_id: UUID,
    benefit_year_dates: BenefitYearDateRange,
    expected_benefit_year_start_date: date,
    meta: EligibilityLogExtra,
) -> ClaimsLogExtra:

    # Visibilty into when the passed entitlement period start date from FINEOS does
    # not line up with an existing benefit year.
    total_employee_claims = db_session.query(Claim).filter(Claim.employee_id == employee_id).count()

    total_employee_claims_in_benefit_year = get_claims_within_benefit_year_count(
        db_session, employee_id, benefit_year_dates
    )

    is_benefit_year_missing_for_claims = total_employee_claims_in_benefit_year > 0

    claims_meta: ClaimsLogExtra = {
        "total_employee_claims": total_employee_claims,
        "is_first_claim": total_employee_claims == 0,
        "total_employee_claims_in_benefit_year": total_employee_claims_in_benefit_year,
        "is_first_claim_in_new_benefit_year": total_employee_claims_in_benefit_year == 0,
        "is_benefit_year_missing_for_claims": is_benefit_year_missing_for_claims,
        "is_entitlement_period_not_on_sunday": expected_benefit_year_start_date.weekday() != 6,
        "benefit_year_start_date": expected_benefit_year_start_date,
        "calculated_benefit_year_start_date": benefit_year_dates.start_date,
        "calculated_benefit_year_end_date": benefit_year_dates.end_date,
    }
    logger.info(
        "The given benefit year start date does not match a benefit year.",
        extra={**meta, **claims_meta},
    )

    if is_benefit_year_missing_for_claims:
        logger.warning(
            "No benefit year exists corresponding to given benefit year start date but claims were found to exist in that period.",
            extra={**meta, **claims_meta},
        )

    return claims_meta


def retrieve_financial_eligibility_for_existing_benefit_year(
    db_session: db.Session,
    benefit_year: BenefitYear,
    employer_id: UUID,
    absence_case_id: str,
    claim: Claim | None,
    effective_date: date,
    metrics: eligibility_util.StateMetrics,
    meta: EligibilityLogExtra,
) -> EligibilityResult:

    meta["benefit_year_id"] = benefit_year.benefit_year_id

    missing_employer_wages: list[WagesAndContributions] = []
    employer_average_weekly_wage = retrieve_eaww(db_session, benefit_year, employer_id)

    # If employee has *no* wage records for this employer in the existing benefit year,
    # then check for new wage data from that year now.
    # (Check for is None, because 0 is a valid EAWW.)
    if employer_average_weekly_wage is None:

        logger.info(
            "No existing employer average weekly wage was found for this benefit year.",
            extra=meta,
        )
        effective_date = benefit_year.base_period_end_quarter.as_date()
        base_period_wages = get_employee_wage_data(
            db_session,
            benefit_year.employee.employee_id,
            benefit_year.base_period_start_quarter,
            benefit_year.base_period_end_quarter,
            effective_date,
        )
        missing_employer_wages = [
            wage for wage in base_period_wages.non_0_wages if wage.employer_id == employer_id
        ]
        employer_average_weekly_wage = get_employer_average_weekly_wage(
            base_period_wages, employer_id
        )

    if employer_average_weekly_wage == 0:
        eligibility_response = process_0_dollar_aww(
            db_session,
            benefit_year,
            effective_date,
            meta,
            claim,
            metrics,
            absence_case_id,
        )

    else:
        (benefits_metrics, unemployment_metric) = metrics
        eligibility_response = EligibilityResult(
            financially_eligible=True,
            description="Financially eligible",
            total_wages=benefit_year.total_wages,
            state_average_weekly_wage=benefits_metrics.average_weekly_wage,
            unemployment_minimum=unemployment_metric.unemployment_minimum_earnings,
            average_weekly_wage=employer_average_weekly_wage,
        )

    logger.info(
        "Financial eligibility was loaded from a Benefit Year.",
        extra={**eligibility_response.dict(), **meta},
    )

    create_financial_eligibility_calculation_record_from_benefit_year(
        benefit_year,
        absence_case_id,
        employer_id,
        eligibility_response,
        missing_employer_wages,
        db_session,
    )

    wage_scenario_logging_for_benefit_year(benefit_year, absence_case_id, eligibility_response)

    return eligibility_response


def retrieve_financial_eligibility_for_new_benefit_year(
    db_session: db.Session,
    employee_id: UUID,
    employer_id: UUID,
    absence_case_id: str,
    claim: Claim | None,
    metrics: eligibility_util.StateMetrics,
    employment_status: EligibilityEmploymentStatus,
    benefit_year_dates: BenefitYearDateRange,
    leave_start_date: date,
    effective_date: date,
    meta: EligibilityLogExtra,
    claims_meta: ClaimsLogExtra,
) -> EligibilityResult:

    effective_quarter = Quarter.from_date(effective_date)
    last_potential_quarter = effective_quarter
    first_potential_quarter = last_potential_quarter.subtract_quarters(5)
    potential_base_period_wages = get_employee_wage_data(
        db_session, employee_id, first_potential_quarter, last_potential_quarter, effective_date
    )
    meta["quarterly_wages"] = str(potential_base_period_wages.employer_quarter_wages())
    meta["num_quarterly_wages"] = len(potential_base_period_wages.employer_quarter_wages())

    # If a claim exists, try to get the request decision for the logs
    if claim:
        meta["request_decision"] = get_leave_request_decision(db_session, claim, leave_start_date)

    base_period_wages = get_employee_wage_data_for_eligibility(
        db_session, employee_id, effective_date
    )

    if has_duplicated_employer_quarter_combinations(base_period_wages.all_wages):
        # PFMLPB-18536: create_financial_eligibility_calculation_record_from_base_period_wages
        #   was correctly de-duplicating wages when storing a calculation, but
        #   _compute_financial_eligibility and thus the EligibilityResult returned was
        #   incorrectly returning IAWW & if claimant was eligible or not
        # WARNING: Thus any FinancialEligibilityCalculation stored prior to this fix
        #   may have incorrect is_eligible without wages appearing duplicated on
        #   FinancialEligibilityCalculation
        logger.warning(
            "Detected duplicated employer-quarter combinations",
            extra={**meta, **claims_meta},
        )

    employer_average_weekly_wage = get_employer_average_weekly_wage(base_period_wages, employer_id)
    individual_average_weekly_wage = get_individual_average_weekly_wage(base_period_wages)

    used_average_weekly_wage = employer_average_weekly_wage
    is_0_dollar_aww_recalculated = False
    if employer_average_weekly_wage == 0 and claim:
        # Entry point is not process_0_dollar_aww due to uncertain BenefitYear
        used_average_weekly_wage, is_0_dollar_aww_recalculated = recalculate_0_dollar_aww(
            claim, individual_average_weekly_wage, meta
        )
        # Logging that would be in process_0_dollar_aww is added here
        if is_0_dollar_aww_recalculated:
            logger.info("Recalculated $0 AWW to be non-zero", extra=meta)

    logger.info(
        "Difference between individual and employer average weekly wage",
        extra={"Difference": float(individual_average_weekly_wage - employer_average_weekly_wage)},
    )

    eligibility_response = _compute_financial_eligibility(
        employment_status=employment_status,
        state_average_weekly_wage=metrics.for_benefits.average_weekly_wage,
        maximum_weekly_benefit_amount=metrics.for_benefits.maximum_weekly_benefit_amount,
        unemployment_minimum_earnings=metrics.for_unemployment.unemployment_minimum_earnings,
        total_wages=base_period_wages.total_wages,
        individual_average_weekly_wage=individual_average_weekly_wage,
        average_weekly_wage=used_average_weekly_wage,
        num_quarters_with_wages=len(base_period_wages.quarters),
    )

    if is_0_dollar_aww_recalculated and eligibility_response.financially_eligible:
        logger.info(
            "Recalculated $0 AWW to be non-zero and passed FE",
            extra=meta,
        )

    base_period_quarters = get_base_period_quarters(db_session, employee_id, effective_date)
    base_period_start_date = base_period_quarters[-1].start_date()
    base_period_end_date: date = base_period_quarters[0].as_date()
    create_financial_eligibility_calculation_record_from_base_period_wages(
        base_period_wages,
        absence_case_id,
        base_period_start_date,
        base_period_end_date,
        benefit_year_dates.start_date,
        benefit_year_dates.end_date,
        employee_id,
        eligibility_response,
        db_session,
    )

    wage_scenario_logging_for_base_period(
        db_session,
        base_period_wages,
        absence_case_id,
        eligibility_response,
    )

    logger.info(
        "Financial eligibility was computed without a Benefit Year.",
        extra={**eligibility_response.dict(), **meta},
    )

    return eligibility_response
