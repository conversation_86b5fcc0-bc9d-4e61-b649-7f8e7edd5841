from concurrent.futures import Future, ThreadPoolExecutor, as_completed
from decimal import Decimal
from typing import Dict, List, Optional

import massgov
import massgov.pfml.api.models.applications.common as apps_common_io
import massgov.pfml.api.models.common as common_io
import massgov.pfml.api.models.phones.common as phone_common_io
import massgov.pfml.util.logging
import massgov.pfml.util.newrelic.events as newrelic_util
from massgov.pfml import db
from massgov.pfml.api.models.applications.common import Address as ApiAddress
from massgov.pfml.api.models.applications.common import PaymentMethod as CommonPaymentMethod
from massgov.pfml.api.models.applications.common import PaymentPreference
from massgov.pfml.api.services.applications import (
    add_or_update_address,
    add_or_update_payment_preference,
    add_or_update_phone,
    convert_customer_preferences,
    set_employer_benefits,
    set_other_incomes,
    set_previous_leaves,
)
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationException,
)
from massgov.pfml.db.lookup_data.absences import AbsencePeriodType
from massgov.pfml.db.lookup_data.applications import DayOfWeek, EmploymentStatus
from massgov.pfml.db.lookup_data.applications import LeaveReason as DBLeaveReason
from massgov.pfml.db.lookup_data.applications import LeaveReasonQualifier, WorkPatternType
from massgov.pfml.db.lookup_data.employees import AddressType, LeaveRequestDecision, PaymentMethod
from massgov.pfml.db.models.applications import (
    Application,
    ContinuousLeavePeriod,
    IntermittentLeavePeriod,
    LkEmploymentStatus,
    ReducedScheduleLeavePeriod,
    WorkPattern,
    WorkPatternDay,
)
from massgov.pfml.db.models.employees import Claim, LkGender
from massgov.pfml.db.models.phone import LkPhoneType
from massgov.pfml.fineos import AbstractFINEOSClient, exception
from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import (
    PFML_ContactDetails,
    PFML_EForm,
    PFML_EFormSummary,
    PFML_PhoneNumber,
)
from massgov.pfml.fineos.models.customer_api import (
    AbsenceDay,
    AbsenceDetails,
    AbsencePeriod,
    AbsencePeriodStatus,
    Customer,
    PaymentPreferenceCustomerResources,
    PaymentPreferenceResponse,
    ReadCustomerOccupation,
)
from massgov.pfml.fineos.transforms.from_fineos.eforms import (
    EformTypes,
    TransformEmployerBenefitsFromOtherIncomeEform,
    TransformOtherIncomeEform,
    TransformOtherIncomeNonEmployerEform,
    TransformPreviousLeaveFromOtherLeaveEform,
    TransformPreviousLeaveFromOtherLeaveEformV22,
)
from massgov.pfml.util.datetime import is_date_contained, utcnow
from massgov.pfml.util.logging.applications import get_absence_period_log_attributes
from massgov.pfml.util.pydantic.types import Regexes

logger = massgov.pfml.util.logging.get_logger(__name__)

EFORM_CACHE = Dict[str, PFML_EForm]
BENEFITS_EFORM_TYPES = [
    EformTypes.OTHER_INCOME,
    EformTypes.OTHER_INCOME_V2,
    EformTypes.OTHER_INCOME_V3,
]

OPEN_STATUSES = [
    LeaveRequestDecision.PENDING.leave_request_decision_description,
    LeaveRequestDecision.IN_REVIEW.leave_request_decision_description,
    LeaveRequestDecision.PROJECTED.leave_request_decision_description,
]


def minutes_from_hours_minutes(hours: int, minutes: int) -> int:
    return hours * 60 + minutes


class ApplicationImportService:
    def __init__(
        self,
        fineos_client: AbstractFINEOSClient,
        fineos_web_id: str,
        application: Application,
        db_session: db.Session,
        claim: Claim,
        absence_case_id: str,
        channel_switch_unsupported_claims: bool,
    ):
        self.fineos_client = fineos_client
        self.fineos_web_id = fineos_web_id
        self.application = application
        self.db_session = db_session
        self.claim = claim
        self.absence_case_id = absence_case_id
        self.channel_switch_unsupported_claims = channel_switch_unsupported_claims

        self.eform_cache: EFORM_CACHE = {}

    def import_data(self):
        """
        Top level handler for executing application import
        """
        self._set_application_fields_from_db_claim()

        # FINEOS requests run in parallel (the "fetch" methods), and their results
        # are handled by the "save" methods.
        parallelizable_tasks = [
            {
                "fetch": self._fetch_absence,
                "save": self._set_application_absence_and_leave_period,
            },
            {"fetch": self._fetch_customer_details, "save": self._set_customer_detail_fields},
            {
                "fetch": self._fetch_employment_status_and_occupations,
                "save": self._set_employment_status_and_occupations,
            },
            {
                "fetch": self._fetch_payment_preference_fields,
                "save": self._set_payment_preference_fields,
            },
            {
                "fetch": self._fetch_tax_withholding_preference,
                "save": self._set_tax_withholding_preference,
            },
        ]

        # PFMLPB-17435 If user is not convrted to MMG, perform MFA verification
        if not self.application.user.auth_id:
            parallelizable_tasks.append(
                {
                    "fetch": self._fetch_customer_contact_details,
                    "save": self._set_customer_contact_detail_fields,
                }
            )

        with ThreadPoolExecutor(max_workers=6) as executor:
            fetch_futures: dict[Future, dict] = {executor.submit(task["fetch"]): task for task in parallelizable_tasks}  # type: ignore
            for future in as_completed(fetch_futures):
                data = future.result()
                fetch_futures[future]["save"](data)

        eform_summaries = self.fineos_client.customer_get_eform_summary(
            self.fineos_web_id, self.absence_case_id
        )
        self._set_previous_leaves(eform_summaries)
        self._set_employer_benefits_from_fineos(eform_summaries)
        self._set_other_incomes_from_fineos(eform_summaries)

    def _set_application_fields_from_db_claim(self) -> None:
        self.application.claim_id = self.claim.claim_id
        if self.claim.employee:
            self.application.tax_identifier_id = self.claim.employee.tax_identifier_id
            self.application.tax_identifier = self.claim.employee.tax_identifier
        self.application.employer_fein = self.claim.employer_fein
        self.application.imported_from_fineos_at = utcnow()

    def _fetch_absence(self) -> AbsenceDetails:
        return self.fineos_client.get_absence(self.fineos_web_id, self.absence_case_id)

    def _set_application_absence_and_leave_period(self, absence_details: AbsenceDetails) -> None:
        absence_period = self._get_absence_period_from_absence_details(absence_details)
        if absence_period is not None:
            if absence_period.reason is not None:
                try:
                    # TODO (PFMLPB-7411): Remove feature flag and military check
                    if not self.channel_switch_unsupported_claims and absence_period.reason in (
                        DBLeaveReason.MILITARY_CAREGIVER.leave_reason_description,
                        DBLeaveReason.MILITARY_EXIGENCY_FAMILY.leave_reason_description,
                    ):
                        # Don't allow military claims to be imported if feature flag is disabled
                        raise KeyError()

                    leave_reason_id = DBLeaveReason.get_id(absence_period.reason)
                except KeyError:
                    logger.warning(
                        "Unsupported leave reason on absence period from FINEOS",
                        extra={
                            "fineos_web_id": self.fineos_web_id,
                            "reason": absence_period.reason,
                            "absence_case_id": (
                                self.application.claim.fineos_absence_id
                                if self.application.claim
                                else None
                            ),
                        },
                        exc_info=True,
                    )
                    raise ValidationException(
                        errors=[
                            ValidationErrorDetail(
                                type=IssueType.invalid,
                                message="Absence period reason is not supported.",
                                field="leave_details.reason",
                            )
                        ]
                    )
                self.application.leave_reason_id = leave_reason_id
            if absence_period.reasonQualifier1 is not None:
                self.application.leave_reason_qualifier_id = LeaveReasonQualifier.get_id(
                    absence_period.reasonQualifier1
                )
            self.application.pregnant_or_recent_birth = (
                self.application.leave_reason_id
                == DBLeaveReason.PREGNANCY_MATERNITY.leave_reason_id
            )
            self._set_has_future_child_date(absence_period)

        # When a claim has multiple leaves with different reasons and a mix of open and completed leaves,
        # we only need to set the Continuous, Reduced, Intermittent leave related to the open leaves,
        # which includes PENDING, IN_REVIEW, and PROJECTED leave request decisions.
        # See https://lwd.atlassian.net/wiki/spaces/DD/pages/2211512350/Claim+status+mappings for more details.
        # Check the absence period selected above to see if it is open, and set flag accordingly.
        import_all_statuses = (
            absence_period is None or absence_period.requestStatus not in OPEN_STATUSES
        )
        self._set_continuous_leave_periods(absence_details, import_all_statuses)
        self._set_intermittent_leave_periods(absence_details, import_all_statuses)
        self._set_reduced_leave_periods(absence_details, import_all_statuses)
        self.application.submitted_time = absence_details.creationDate

    def _get_absence_period_from_absence_details(
        self, absence_details: AbsenceDetails
    ) -> Optional[AbsencePeriod]:
        """
        return Open Absence Period if there is one
        if there isn't an open absence period the application is considered
        completed and the function returns the latest closed absence period
        if there is one
        """
        if absence_details.absencePeriods is None:
            return None
        absence_period = self._get_open_absence_period(absence_details)
        if absence_period is None:
            self.application.mark_complete()
            absence_period = self._get_latest_absence_period(absence_details)

        if len(absence_details.absencePeriods) > 1:
            logger.info(
                "multiple absence periods found during application import",
                extra={
                    "application_id": self.application.application_id,
                    "absence_case_id": (
                        self.application.claim.fineos_absence_id if self.application.claim else None
                    ),
                    "absence_period_attributes": get_absence_period_log_attributes(
                        absence_details.absencePeriods, absence_period
                    ),
                },
            )

        return absence_period

    def _set_has_future_child_date(self, imported_absence_period: AbsencePeriod) -> None:
        if (
            self.application.leave_reason_id == DBLeaveReason.CHILD_BONDING.leave_reason_id
            and imported_absence_period.status == AbsencePeriodStatus.ESTIMATED.value
        ):
            self.application.has_future_child_date = True
        else:
            self.application.has_future_child_date = False

    def _set_continuous_leave_periods(
        self, absence_details: AbsenceDetails, import_all_statuses: bool
    ) -> None:

        continuous_leave_periods: List[ContinuousLeavePeriod] = []

        if absence_details.absencePeriods:
            for absence_period in absence_details.absencePeriods:
                if (
                    absence_period.absenceType
                    == AbsencePeriodType.CONTINUOUS.absence_period_type_description
                ):
                    perform_import = (
                        import_all_statuses or absence_period.requestStatus in OPEN_STATUSES
                    )
                    if perform_import:
                        continuous_leave = self._parse_continuous_leave_period(absence_period)
                        continuous_leave_periods.append(continuous_leave)

        self.application.continuous_leave_periods = continuous_leave_periods
        self.application.has_continuous_leave_periods = len(continuous_leave_periods) > 0

    def _parse_intermittent_leave_period(
        self, absence_period: AbsencePeriod
    ) -> IntermittentLeavePeriod:
        leave_period = IntermittentLeavePeriod()
        if absence_period.episodicLeavePeriodDetail is None:
            error = ValueError("Episodic absence period is missing episodicLeavePeriodDetail")
            raise error
        leave_period.application_id = self.application.application_id
        leave_period.start_date = absence_period.startDate
        leave_period.end_date = absence_period.endDate

        episodic_detail = absence_period.episodicLeavePeriodDetail
        leave_period.frequency = episodic_detail.frequency
        leave_period.frequency_interval = episodic_detail.frequencyInterval
        leave_period.frequency_interval_basis = episodic_detail.frequencyIntervalBasis
        leave_period.duration = episodic_detail.duration
        leave_period.duration_basis = episodic_detail.durationBasis

        return leave_period

    def _set_intermittent_leave_periods(
        self, absence_details: AbsenceDetails, import_all_statuses: bool
    ) -> None:
        intermittent_leave_periods: List[IntermittentLeavePeriod] = []

        if absence_details.absencePeriods:
            for absence_period in absence_details.absencePeriods:
                if (
                    absence_period.absenceType
                    == AbsencePeriodType.INTERMITTENT.absence_period_type_description
                    or absence_period.absenceType
                    == AbsencePeriodType.EPISODIC.absence_period_type_description
                ):
                    perform_import = (
                        import_all_statuses or absence_period.requestStatus in OPEN_STATUSES
                    )
                    if perform_import:
                        intermittent_leave = self._parse_intermittent_leave_period(absence_period)
                        intermittent_leave_periods.append(intermittent_leave)

        self.application.intermittent_leave_periods = intermittent_leave_periods
        self.application.has_intermittent_leave_periods = len(intermittent_leave_periods) > 0

    def _set_reduced_leave_periods(
        self, absence_details: AbsenceDetails, import_all_statuses: bool
    ) -> None:
        reduced_schedule_leave_periods: List[ReducedScheduleLeavePeriod] = []

        if absence_details.absencePeriods:
            for absence_period in absence_details.absencePeriods:
                if (
                    absence_period.absenceType
                    == AbsencePeriodType.REDUCED_SCHEDULE.absence_period_type_description
                    and absence_details.absenceDays
                ):
                    perform_import = (
                        import_all_statuses or absence_period.requestStatus in OPEN_STATUSES
                    )
                    if perform_import:
                        reduced_leave = self._parse_reduced_leave_period(
                            absence_period,
                            absence_details.absenceDays,
                            bool(len(absence_details.absencePeriods)),
                        )
                        reduced_schedule_leave_periods.append(reduced_leave)

        self.application.has_reduced_schedule_leave_periods = (
            len(reduced_schedule_leave_periods) > 0
        )
        self.application.reduced_schedule_leave_periods = reduced_schedule_leave_periods

    def _get_open_absence_period(self, absence_details: AbsenceDetails) -> Optional[AbsencePeriod]:
        # First try to retrieve an absence period with a PENDING leave request decision.
        # If none are found, look for any with OPEN_STATUSES (includes IN_REVIEW and PROJECTED).
        if absence_details.absencePeriods:
            for absence_period in absence_details.absencePeriods:
                if (
                    absence_period.requestStatus
                    == LeaveRequestDecision.PENDING.leave_request_decision_description
                ):
                    return absence_period
            for absence_period in absence_details.absencePeriods:
                if absence_period.requestStatus in OPEN_STATUSES:
                    return absence_period
        return None

    def _get_latest_absence_period(
        self, absence_details: AbsenceDetails
    ) -> Optional[AbsencePeriod]:
        if absence_details.absencePeriods is None:
            return None
        absence_periods = sorted(absence_details.absencePeriods, key=lambda x: x.startDate)  # type: ignore
        if len(absence_periods) > 0:
            return absence_periods[-1]
        return None

    def _parse_continuous_leave_period(
        self, absence_period: AbsencePeriod
    ) -> ContinuousLeavePeriod:
        return ContinuousLeavePeriod(
            application_id=self.application.application_id,
            start_date=absence_period.startDate,
            end_date=absence_period.endDate,
        )

    def _parse_reduced_leave_period(
        self,
        absence_period: AbsencePeriod,
        absenceDays: List[AbsenceDay],
        is_multiple_leave: bool,
    ) -> ReducedScheduleLeavePeriod:
        # set default to 0
        off_minutes: Dict[str, int] = {
            "Sunday": 0,
            "Monday": 0,
            "Tuesday": 0,
            "Wednesday": 0,
            "Thursday": 0,
            "Friday": 0,
            "Saturday": 0,
        }
        start_date = absence_period.startDate
        end_date = absence_period.endDate
        week_list = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]

        for absence_day in absenceDays:
            if not week_list:
                break
            if not absence_day.date or not start_date or not end_date:
                break

            target_date = absence_day.date
            if is_date_contained((start_date, end_date), target_date):
                weekday = target_date.strftime("%A")
                if absence_day.timeRequested:
                    off_minutes[weekday] = round(float(absence_day.timeRequested) * 60)
                    if weekday in week_list:
                        week_list.remove(weekday)
            elif not is_multiple_leave:
                newrelic_util.log_and_capture_exception(
                    "Parse reduced leave: absence_day.date is outside the date range of the leave",
                    extra={"application_id": self.application.application_id},
                )

        return ReducedScheduleLeavePeriod(
            application_id=self.application.application_id,
            start_date=absence_period.startDate,
            end_date=absence_period.endDate,
            sunday_off_minutes=off_minutes["Sunday"],
            monday_off_minutes=off_minutes["Monday"],
            tuesday_off_minutes=off_minutes["Tuesday"],
            wednesday_off_minutes=off_minutes["Wednesday"],
            thursday_off_minutes=off_minutes["Thursday"],
            friday_off_minutes=off_minutes["Friday"],
            saturday_off_minutes=off_minutes["Saturday"],
        )

    def _fetch_customer_details(self) -> Customer:
        return self.fineos_client.read_customer_details(self.fineos_web_id)

    def _set_customer_detail_fields(self, details: Customer) -> None:
        """
        Use customer details from FINEOS to set application fields
        """
        self.application.first_name = details.firstName
        self.application.middle_name = details.secondName
        self.application.last_name = details.lastName

        if details.dateOfBirth is None:
            raise ValueError("Customer date of birth is missing")

        self.application.date_of_birth = details.dateOfBirth

        if details.gender is not None:
            db_gender = (
                self.db_session.query(LkGender)
                .filter(LkGender.fineos_gender_description == details.gender)
                .one_or_none()
            )
            if db_gender is not None:
                self.application.gender_id = db_gender.gender_id

        has_state_id = False
        if details.classExtensionInformation is not None:
            mass_id = next(
                (
                    info
                    for info in details.classExtensionInformation
                    if info.name == "MassachusettsID"
                ),
                None,
            )

            if (
                mass_id is not None
                and mass_id.stringValue is not None
                and mass_id.stringValue != ""
            ):
                mass_id_formatted = str(mass_id.stringValue).upper()
                if Regexes.MASS_ID.match(mass_id_formatted):
                    self.application.mass_id = mass_id_formatted
                    has_state_id = True
                else:
                    logger.warning(
                        "Not setting mass_id because it doesn't conform to expected format/length",
                        extra={
                            "application_id": self.application.application_id,
                            "absence_case_id": self.absence_case_id,
                            "mass_id_length": len(mass_id_formatted),
                        },
                    )

        self.application.has_state_id = has_state_id

        if isinstance(
            details.customerAddress, massgov.pfml.fineos.models.customer_api.CustomerAddress
        ):
            # Convert CustomerAddress to ApiAddress, in order to use add_or_update_address
            address_to_create = ApiAddress(
                line_1=details.customerAddress.address.addressLine1,
                line_2=details.customerAddress.address.addressLine2,
                city=details.customerAddress.address.addressLine4,
                state=details.customerAddress.address.addressLine6,
                zip=details.customerAddress.address.postCode,
            )
            add_or_update_address(
                self.db_session, address_to_create, AddressType.RESIDENTIAL, self.application
            )

    def _fetch_customer_contact_details(self) -> PFML_ContactDetails:
        return self.fineos_client.read_customer_contact_details(self.fineos_web_id)

    def _set_customer_contact_detail_fields(self, contact_details: PFML_ContactDetails) -> None:
        """
        Uses customer contact details from FINEOS to create a new phone record,
        and associate the phone record with the application being imported
        """
        if not contact_details or not contact_details.phoneNumbers:
            logger.info("No contact details returned from FINEOS")
            return

        preferred_phone_number = next(
            (phone_num for phone_num in contact_details.phoneNumbers if phone_num.preferred),
            contact_details.phoneNumbers[0],
        )

        # Handles the potential case of a phone number list existing, but phone fields are null
        if not (
            preferred_phone_number.intCode
            or preferred_phone_number.areaCode
            or preferred_phone_number.telephoneNo
        ):
            logger.info(
                "Field missing from FINEOS phoneNumber list",
                extra={"phoneNumberType": str(preferred_phone_number.phoneNumberType)},
            )
            return

        phone_to_create = self._create_common_io_phone_from_fineos(preferred_phone_number)
        add_or_update_phone(self.db_session, phone_to_create, self.application)

    def _create_common_io_phone_from_fineos(
        self, phone: PFML_PhoneNumber
    ) -> Optional[phone_common_io.Phone]:
        """
        Creates common.io Phone object from FINEOS PhoneNumber object
        """
        db_phone = (
            self.db_session.query(LkPhoneType)
            .filter(LkPhoneType.phone_type_description == phone.phoneNumberType)
            .one_or_none()
        )

        if not db_phone:
            newrelic_util.log_and_capture_exception(
                f"Unable to find phone_type: {phone.phoneNumberType}",
                extra={"phone_type": phone.phoneNumberType},
            )
            return None

        phone_to_create = phone_common_io.Phone(
            int_code=phone.intCode,
            phone_number=f"{phone.areaCode}{phone.telephoneNo}",
            phone_type=phone_common_io.PhoneType(db_phone.phone_type_description),
        )
        return phone_to_create

    def _fetch_employment_status_and_occupations(self) -> List[ReadCustomerOccupation]:
        return self.fineos_client.get_customer_occupations_customer_api(self.fineos_web_id)

    def _set_employment_status_and_occupations(
        self, occupations: List[ReadCustomerOccupation]
    ) -> None:
        if len(occupations) == 0:
            return
        occupation = occupations[0]
        # TODO (PFMLPB-7411): Remove feature flag check and all code within the else statement below
        if self.channel_switch_unsupported_claims:
            employment_status = (
                self.db_session.query(LkEmploymentStatus)
                .filter(LkEmploymentStatus.fineos_label == occupation.employmentStatus)
                .one_or_none()
            )
            if not employment_status:
                logger.warning(
                    "Did not import due to unsupported employment status",
                    extra={
                        "fineos_web_id": self.fineos_web_id,
                        "status": occupation.employmentStatus,
                        "absence_case_id": (
                            self.application.claim.fineos_absence_id
                            if self.application.claim
                            else None
                        ),
                    },
                )
                raise Exception("Employment status cannot be determined")
            self.application.employment_status_id = employment_status.employment_status_id
        else:
            if occupation.employmentStatus is not None:
                if occupation.employmentStatus != EmploymentStatus.EMPLOYED.fineos_label:
                    logger.info(
                        "Did not import unsupported employment status from FINEOS",
                        extra={
                            "fineos_web_id": self.fineos_web_id,
                            "status": occupation.employmentStatus,
                            "absence_case_id": (
                                self.application.claim.fineos_absence_id
                                if self.application.claim
                                else None
                            ),
                        },
                    )
                    raise ValidationException(
                        errors=[
                            ValidationErrorDetail(
                                type=IssueType.invalid,
                                message="Employment Status must be Active",
                                field="employment_status",
                            )
                        ]
                    )
                else:
                    self.application.employment_status_id = (
                        EmploymentStatus.EMPLOYED.employment_status_id
                    )
        if occupation.hoursWorkedPerWeek is not None:
            self.application.hours_worked_per_week = Decimal(occupation.hoursWorkedPerWeek)
        if occupation.occupationId is None:
            return

        fineos_work_patterns = None
        try:
            fineos_work_patterns = self.fineos_client.get_week_based_work_pattern(
                self.fineos_web_id, occupation.occupationId
            )
        except exception.FINEOSForbidden:
            # Known FINEOS limitation, where it responds with a 403 for Variable work pattern types
            logger.info(
                "Received FINEOS forbidden response when getting week-based work pattern.",
                extra={
                    "absence_case_id": (
                        self.application.claim.fineos_absence_id if self.application.claim else None
                    ),
                    "occupationId": occupation.occupationId,
                },
            )

        if fineos_work_patterns is None:
            return

        if (
            fineos_work_patterns.workPatternType
            != WorkPatternType.FIXED.work_pattern_type_description
        ):
            logger.warning(
                f"Application work pattern type is not {WorkPatternType.FIXED.work_pattern_type_description}",
                extra={"fineos_work_pattern_type": fineos_work_patterns.workPatternType},
            )
            return
        db_work_pattern_days = []
        work_pattern = WorkPattern(work_pattern_type_id=WorkPatternType.FIXED.work_pattern_type_id)
        for pattern in fineos_work_patterns.workPatternDays:
            db_work_pattern_days.append(
                WorkPatternDay(
                    day_of_week_id=DayOfWeek.get_id(pattern.dayOfWeek),
                    minutes=minutes_from_hours_minutes(pattern.hours, pattern.minutes),
                )
            )
        work_pattern.work_pattern_days = db_work_pattern_days
        self.application.work_pattern = work_pattern

    def _fetch_payment_preference_fields(
        self,
    ) -> List[PaymentPreferenceResponse] | PaymentPreferenceCustomerResources:
        return self.fineos_client.get_customer_payment_preference(self.fineos_web_id)

    def _set_payment_preference_fields(
        self, preferences: List[PaymentPreferenceResponse] | PaymentPreferenceCustomerResources
    ) -> None:
        """
        Use payment preferences from FINEOS to set imported application fields
        """
        self.application.has_submitted_payment_preference = False

        # the data is in new format
        if isinstance(preferences, PaymentPreferenceCustomerResources):
            preference = convert_customer_preferences(preferences)
            if not preference:
                return
        else:
            if not preferences:
                return
            # Take the one with isDefault=True, otherwise take first one
            preference = next(
                (pref for pref in preferences if pref.isDefault and pref.paymentMethod),
                preferences[0],
            )
        # breakpoint()
        payment_preference = None
        try:
            if preference.accountDetails is not None:
                payment_preference = PaymentPreference(
                    account_number=preference.accountDetails.accountNo,
                    routing_number=preference.accountDetails.routingNumber,  # type: ignore
                    bank_account_type=preference.accountDetails.accountType,  # type: ignore
                    payment_method=preference.paymentMethod,  # type: ignore
                )
            elif preference.paymentMethod == PaymentMethod.CHECK.payment_method_description:
                payment_preference = PaymentPreference(
                    payment_method=CommonPaymentMethod(preference.paymentMethod),
                )
        except Exception:
            logger.warning(
                "Unable to import payment preference from FINEOS",
                extra={
                    "fineos_web_id": self.fineos_web_id,
                    "absence_case_id": (
                        self.application.claim.fineos_absence_id if self.application.claim else None
                    ),
                },
                exc_info=True,
            )
        if payment_preference is not None:
            add_or_update_payment_preference(self.db_session, payment_preference, self.application)
            self.application.has_submitted_payment_preference = True

        has_mailing_address = False
        if isinstance(
            preference.customerAddress, massgov.pfml.fineos.models.customer_api.CustomerAddress
        ):
            # Convert CustomerAddress to ApiAddress, in order to use add_or_update_address
            address_to_create = ApiAddress(
                line_1=preference.customerAddress.address.addressLine1,
                line_2=preference.customerAddress.address.addressLine2,
                city=preference.customerAddress.address.addressLine4,
                state=preference.customerAddress.address.addressLine6,
                zip=preference.customerAddress.address.postCode,
            )
            add_or_update_address(
                self.db_session, address_to_create, AddressType.MAILING, self.application
            )
            has_mailing_address = True

        if not preference.paymentMethod:
            self.application.has_submitted_payment_preference = False

        self.application.has_mailing_address = has_mailing_address

    def _set_previous_leaves(
        self,
        eform_summaries: Optional[List[PFML_EFormSummary]] = None,
    ) -> None:
        """
        Retrieve other leaves from FINEOS and set for imported application fields
        """
        if eform_summaries is None:
            eform_summaries = self.fineos_client.customer_get_eform_summary(
                self.fineos_web_id, self.absence_case_id
            )

        for summary in eform_summaries:
            if summary.eformType not in [EformTypes.OTHER_LEAVES, EformTypes.OTHER_LEAVES_V3]:
                continue

            previous_leaves: List[common_io.PreviousLeave] = []

            eform = self._customer_get_eform(summary.eformId)

            if summary.eformType == EformTypes.OTHER_LEAVES:
                previous_leaves = TransformPreviousLeaveFromOtherLeaveEform.from_fineos(eform)
            elif summary.eformType == EformTypes.OTHER_LEAVES_V3:
                previous_leaves = TransformPreviousLeaveFromOtherLeaveEformV22.from_fineos(eform)

            # make backward compatible checking other/same leaves, and then check if there's any newer generic previous leaves
            self.application.has_previous_leaves = len(previous_leaves) > 0

            set_previous_leaves(
                self.db_session,
                previous_leaves,
                self.application,
            )

    def _customer_get_eform(
        self,
        eform_id: str,
    ) -> PFML_EForm:
        if eform_id in self.eform_cache:
            return self.eform_cache[eform_id]
        self.eform = self.fineos_client.customer_get_eform(
            self.fineos_web_id, self.absence_case_id, eform_id
        )
        self.eform_cache[eform_id] = self.eform
        return self.eform

    def _set_employer_benefits_from_fineos(
        self,
        eform_summaries: Optional[List[PFML_EFormSummary]] = None,
    ) -> None:
        employer_benefits: List[common_io.EmployerBenefit] = []
        if eform_summaries is None:
            eform_summaries = self.fineos_client.customer_get_eform_summary(
                self.fineos_web_id, self.absence_case_id
            )

        for eform_summary in eform_summaries:
            if eform_summary.eformType not in BENEFITS_EFORM_TYPES:
                continue

            eform = self._customer_get_eform(eform_summary.eformId)

            if eform_summary.eformType == EformTypes.OTHER_INCOME:
                employer_benefits.extend(
                    other_income
                    for other_income in TransformOtherIncomeEform.from_fineos(eform)
                    if other_income.program_type == "Employer"
                )

            elif eform_summary.eformType in [
                EformTypes.OTHER_INCOME_V2,
                EformTypes.OTHER_INCOME_V3,
            ]:
                employer_benefits.extend(
                    TransformEmployerBenefitsFromOtherIncomeEform.from_fineos(eform)
                )
        self.application.has_employer_benefits = len(employer_benefits) > 0
        set_employer_benefits(self.db_session, employer_benefits, self.application)

    def _set_other_incomes_from_fineos(
        self,
        eform_summaries: Optional[List[PFML_EFormSummary]] = None,
    ) -> None:
        other_incomes: List[apps_common_io.OtherIncome] = []
        if eform_summaries is None:
            eform_summaries = self.fineos_client.customer_get_eform_summary(
                self.fineos_web_id, self.absence_case_id
            )

        for eform_summary in eform_summaries:
            if eform_summary.eformType not in [
                EformTypes.OTHER_INCOME_V2,
                EformTypes.OTHER_INCOME_V3,
            ]:
                continue

            eform = self._customer_get_eform(eform_summary.eformId)
            other_incomes.extend(
                [
                    income
                    for income in TransformOtherIncomeNonEmployerEform.from_fineos(eform)
                    if income.income_type in set(apps_common_io.OtherIncomeType)
                ]
            )
        self.application.has_other_incomes = len(other_incomes) > 0
        set_other_incomes(self.db_session, other_incomes, self.application)

    def _fetch_tax_withholding_preference(self) -> Optional[bool]:
        assert self.claim.fineos_absence_id
        return self.fineos_client.read_tax_withholding_preference(self.claim.fineos_absence_id)

    def _set_tax_withholding_preference(self, preference: Optional[bool]) -> None:
        self.application.is_withholding_tax = preference
