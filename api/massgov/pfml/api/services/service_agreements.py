import copy
import dataclasses
import datetime
import enum
import re
import time
import uuid
from collections import defaultdict
from dataclasses import dataclass
from datetime import date
from typing import Any, DefaultDict, Dict, List, Optional, Sequence

import massgov.pfml.db as db
import massgov.pfml.util.batch.log as batch_log
import massgov.pfml.util.logging as logging
from massgov.pfml.db import Session
from massgov.pfml.db.lookup_data.dor import FineosServiceAgreementStatus
from massgov.pfml.db.models.dor import (
    DOR_EXEMPTION_SENTINEL_DATE,
    PFML_EXEMPTION_DEFAULT_START_DATE,
    EmployerDORExemption,
    FineosServiceAgreement,
)
from massgov.pfml.db.models.employees import Employer
from massgov.pfml.fineos import AbstractFINEOSClient
from massgov.pfml.fineos.constants import Metrics
from massgov.pfml.fineos.models import CreateOrUpdateServiceAgreement, ServiceAgreementLeavePlans
from massgov.pfml.util.collections import daterangedict
from massgov.pfml.util.datetime import utcnow

logger = logging.get_logger(__name__)

EmployerMap = Dict[uuid.UUID, Employer]
EmployerDORExemptionMap = DefaultDict[uuid.UUID, List[EmployerDORExemption]]
EmployerFineosServiceAgreementMap = DefaultDict[uuid.UUID, List[FineosServiceAgreement]]


@dataclass
class ServiceAgreementInput:
    create_or_update: CreateOrUpdateServiceAgreement
    family_exemption: bool
    medical_exemption: bool
    dor_exemption: Optional[EmployerDORExemption] = None


class ActionType(enum.Enum):
    EDIT = 1
    CREATE_VERSION = 2
    SET_SERVICE_AGREEMENT_START_DATE = 3


@dataclasses.dataclass
class SyncExemptionAction:
    """An action to synchronize exemptions to FINEOS."""

    type: ActionType
    start_date: date
    family_exemption: bool
    medical_exemption: bool

    def __repr__(self):
        exemptions = ""
        if self.family_exemption:
            exemptions += "f"
        if self.medical_exemption:
            exemptions += "m"
        if self.family_exemption is False and self.medical_exemption is False:
            exemptions = "-"
        return f"<{self.type.name} {self.start_date} {exemptions}>"


DOR_EXEMPTION_SENTINEL = EmployerDORExemption(
    employer_id=None,
    dor_activity_key=0,
    decision_commence_date=DOR_EXEMPTION_SENTINEL_DATE,
    decision_cease_date=DOR_EXEMPTION_SENTINEL_DATE,
    family_exemption=False,
    medical_exemption=False,
    dor_updated_date=None,
)


def create_exemption_record_for_dor_gap(
    current_dor_exemption: EmployerDORExemption,
    prev_dor_exemption: Optional[EmployerDORExemption] = None,
) -> Optional[EmployerDORExemption]:
    """
    If there is a gap between the end of the previous DOR exemption record and the start of the current one,
    create a new DOR exemption record to represent the gap.
    """

    if (
        prev_dor_exemption is None
        or prev_dor_exemption.decision_cease_date is None
        or prev_dor_exemption.decision_cease_date == DOR_EXEMPTION_SENTINEL_DATE
    ):
        return None

    next_dor_start_date = prev_dor_exemption.decision_cease_date + datetime.timedelta(days=1)

    # Check if the leave plans are identical between the previous and current DOR exemption records.
    has_identical_leave_plans = (
        prev_dor_exemption.family_exemption,
        prev_dor_exemption.medical_exemption,
    ) == (current_dor_exemption.family_exemption, current_dor_exemption.medical_exemption)

    # Check if the DOR exemption records overlap.
    is_overlapping = (
        prev_dor_exemption.start_date <= current_dor_exemption.decision_cease_date
        and current_dor_exemption.start_date <= prev_dor_exemption.decision_cease_date
    )

    if current_dor_exemption.start_date > next_dor_start_date > PFML_EXEMPTION_DEFAULT_START_DATE:
        # There is a gap between the end of the previous DOR exemption record and the start of the current one.
        # so we need to create a new DOR exemption record to represent the gap.

        decision_cease_date = current_dor_exemption.start_date - datetime.timedelta(days=1)

        logger.info(
            "Creating DOR exemption record for gap",
            extra={
                "employer_id": current_dor_exemption.employer_id,
                "decision_commence_date": next_dor_start_date,
                "decision_cease_date": decision_cease_date,
            },
        )

        return EmployerDORExemption(
            decision_commence_date=next_dor_start_date,
            decision_cease_date=decision_cease_date,
            employer_id=prev_dor_exemption.employer_id,
            family_exemption=False,
            medical_exemption=False,
        )
    elif not has_identical_leave_plans and is_overlapping:
        # The current DOR exemption record overlaps with the previous one and the leave plans are not identical.
        logger.warning(
            "DOR exemption record overlaps with previous record",
            extra={
                "employer_id": current_dor_exemption.employer_id,
                "previous_decision_commence_date": prev_dor_exemption.start_date.isoformat(),
                "previous_decision_cease_date": prev_dor_exemption.decision_cease_date.isoformat(),
                "current_decision_commence_date": current_dor_exemption.start_date.isoformat(),
                "current_decision_cease_date": current_dor_exemption.decision_cease_date.isoformat(),
            },
        )

    return None


def compute_exemption_sync_actions(
    dor_exemptions: Sequence[EmployerDORExemption],
    fineos_service_agreements: Sequence[FineosServiceAgreement],
    today: date,
    log_extra: dict[str, Any] | None = None,
) -> List[SyncExemptionAction]:
    """Compute the actions that must be done to FINEOS service agreements to sync them to match the DOR exemptions.

    Date ranges ("versions") on the FINEOS side can't be deleted or moved, so we have to turn everything into editing
    an existing version or creating a new one.

    The dates in the data sets may not match. We need to consider each date range that is in the intersection of both
    data sets. For example, as a timeline view:

      DOR exemptions: |2020-01-01........................|2022-01-01........|2023-04-01....|
      FINEOS:             |2020-04-01...|2020-10-15............|2022-10-15..|2023-04-01....|
      All dates:      |...|.............|................|.....|............|..............|
    """

    # Build mapping from date range to DOR exemption.
    date_to_dor_exemption = daterangedict.DateRangeDict()

    # Sort the dor exemptions by start date, so we can iterate through them in order
    dor_exemptions = sorted(dor_exemptions, key=lambda e: e.start_date)

    # Keep track of the previous DOR exemption record we've seen.
    prev_dor_exemption: Optional[EmployerDORExemption] = None

    for dor_exemption in dor_exemptions:
        if dor_exemption.decision_cease_date < PFML_EXEMPTION_DEFAULT_START_DATE:
            prev_dor_exemption = dor_exemption
            # The whole DOR exemption record is before PFML started, so we can ignore it.
            continue
        elif dor_exemption.start_date < PFML_EXEMPTION_DEFAULT_START_DATE:
            # The DOR exemption record starts before PFML, consider it starting on the start date
            date_to_dor_exemption.add(PFML_EXEMPTION_DEFAULT_START_DATE, dor_exemption)
        else:
            date_to_dor_exemption.add(dor_exemption.start_date, dor_exemption)

        # Create a new DOR exemption record to represent the gap between the end of the previous DOR exemption record
        # and the start of the current one.
        if gap_dor_exemption := create_exemption_record_for_dor_gap(
            dor_exemption, prev_dor_exemption
        ):
            date_to_dor_exemption.add(gap_dor_exemption.start_date, gap_dor_exemption)

        prev_dor_exemption = dor_exemption

    dor_exemption_at_default_start_date: EmployerDORExemption | None = (
        date_to_dor_exemption.get_value_at_date(PFML_EXEMPTION_DEFAULT_START_DATE)
    )
    if dor_exemption_at_default_start_date is None:
        # If we never saw a DOR exemption record that includes the start date (often the sentinel), add one to represent
        # that the employer was implicitly non-exempt.
        dor_exemption_at_default_start_date = DOR_EXEMPTION_SENTINEL
        date_to_dor_exemption.add(
            PFML_EXEMPTION_DEFAULT_START_DATE, dor_exemption_at_default_start_date
        )

    # If the latest DOR exemption record has a decision cease date that is in the past, add a record to represent
    # that the employer is now implicitly non-exempt.
    latest_dor_exemption = max(dor_exemptions, default=None, key=lambda e: e.start_date)
    if (
        latest_dor_exemption is not None
        and latest_dor_exemption.decision_cease_date <= today
        and (latest_dor_exemption.family_exemption or latest_dor_exemption.medical_exemption)
    ):
        next_decision_commence_date = latest_dor_exemption.decision_cease_date + datetime.timedelta(
            days=1
        )
        date_to_dor_exemption.add(
            next_decision_commence_date,
            EmployerDORExemption(
                employer_id=None,
                dor_activity_key=0,
                decision_commence_date=next_decision_commence_date,
                decision_cease_date=DOR_EXEMPTION_SENTINEL_DATE,
                family_exemption=False,
                medical_exemption=False,
                dor_updated_date=None,
            ),
        )

    # Compute sync action for initial version of FINEOS service agreement.
    actions, initial_sa = compute_sync_actions_for_initial_version(
        dor_exemption_at_default_start_date,
        fineos_service_agreements,
    )

    if not initial_sa.start_date:
        raise ValueError("Service Agreement must have a start date")

    # Build mapping from date range to FINEOS service agreement.
    date_to_fineos_service_agreement = daterangedict.DateRangeDict()
    date_to_fineos_service_agreement[initial_sa.start_date] = initial_sa
    for fineos_sa in fineos_service_agreements:
        if fineos_sa.version_date is None:
            continue  # Skip initial version, already handled by compute_sync_actions_for_initial_version().
        date_to_fineos_service_agreement.add(fineos_sa.period_start_date, fineos_sa)
    # Keep a copy of the input state for logging later.
    date_to_fineos_service_agreement_original = copy.deepcopy(date_to_fineos_service_agreement)

    # The union of all start dates across both data sets (DOR and FINEOS), in order.
    all_dates = sorted(date_to_dor_exemption.keys() | date_to_fineos_service_agreement.keys())

    # Compare each date range and determine the sync action.
    for start_date in all_dates:
        if start_date < PFML_EXEMPTION_DEFAULT_START_DATE:
            continue

        dor_exemption_at_date: EmployerDORExemption | None = (
            date_to_dor_exemption.get_value_at_date(start_date)
        )
        fineos_sa_at_date: FineosServiceAgreement | None = (
            date_to_fineos_service_agreement.get_value_at_date(start_date)
        )

        if dor_exemption_at_date is None:
            raise ValueError("bug: dor_exemption_at_date is None")

        actions += compute_sync_actions(start_date, dor_exemption_at_date, fineos_sa_at_date)

        # Update the FINEOS service agreement map to what it would be after the edit/create action
        # so that checks on later dates will operate on the updated service agreement map
        date_to_fineos_service_agreement[start_date] = FineosServiceAgreement(
            start_date=PFML_EXEMPTION_DEFAULT_START_DATE,
            version_date=start_date,
            family_exemption=dor_exemption_at_date.family_exemption,
            medical_exemption=dor_exemption_at_date.medical_exemption,
        )

    # Log all relevant fields in one log line for easier analysis.
    extra = {
        "date_to_dor_exemption": date_to_dor_exemption,
        "date_to_fineos_service_agreement": date_to_fineos_service_agreement_original,
        "all_dates": " ".join(map(str, all_dates)),
        "actions": actions,
    }
    if log_extra:
        extra |= log_extra
    if actions:
        logger.info("computed", extra=extra)
    else:
        logger.debug("computed", extra=extra)

    return actions


def compute_sync_actions_for_initial_version(
    dor_exemption_at_default_start_date: EmployerDORExemption,
    fineos_service_agreements: Sequence[FineosServiceAgreement],
) -> tuple[List[SyncExemptionAction], FineosServiceAgreement]:
    """Compute actions for the initial version specially. It can be moved in time (unlike all other versions) and can
    have updates to the leave plans."""

    fully_exempt_on_default_start_date = (
        dor_exemption_at_default_start_date.family_exemption
        and dor_exemption_at_default_start_date.medical_exemption
    )

    # Compute the fields that the initial version should have.
    calculated_sa_initial_version = FineosServiceAgreement(
        start_date=PFML_EXEMPTION_DEFAULT_START_DATE,
        version_date=None,
        family_exemption=dor_exemption_at_default_start_date.family_exemption,
        medical_exemption=dor_exemption_at_default_start_date.medical_exemption,
    )

    if not calculated_sa_initial_version.start_date:
        raise ValueError("Service Agreement must have a start date")

    if fully_exempt_on_default_start_date:
        # FINEOS doesn't allow a fully exempt initial version. Make it non-exempt and a day earlier. Other code
        # will add a version a day later that is fully exempt.
        calculated_sa_initial_version.start_date -= datetime.timedelta(days=1)
        calculated_sa_initial_version.family_exemption = False
        calculated_sa_initial_version.medical_exemption = False

    # Get the initial service agreement, the initial version will have `version_date` as None.
    sa_initial_version = next(
        (sa for sa in fineos_service_agreements if sa.version_date is None), None
    )

    if sa_initial_version == calculated_sa_initial_version:
        # The initial version already exists and has the correct attributes in FINEOS.
        return [], sa_initial_version

    return (
        [
            SyncExemptionAction(
                ActionType.SET_SERVICE_AGREEMENT_START_DATE,
                calculated_sa_initial_version.start_date,
                bool(calculated_sa_initial_version.family_exemption),
                bool(calculated_sa_initial_version.medical_exemption),
            )
        ],
        calculated_sa_initial_version,
    )


def compute_sync_actions(
    start_date: date,
    dor_exemption: EmployerDORExemption,
    fineos_sa: FineosServiceAgreement | None,
) -> List[SyncExemptionAction]:
    """Compute the actions that must be done to the given FINEOS service agreement to match the DOR exemption."""

    if _dor_fineos_data_are_identical(dor_exemption, fineos_sa):
        logger.debug("data equal for start date %s", start_date)
        return []

    if fineos_sa and fineos_sa.version_date and start_date == fineos_sa.version_date:
        # Existing FINEOS service agreement at this date. Can't use the API, manual edit needed.
        return [
            SyncExemptionAction(
                ActionType.EDIT,
                start_date,
                dor_exemption.family_exemption,
                dor_exemption.medical_exemption,
            )
        ]

    # The start date of this period does not match exactly to a FINEOS service agreement start date or version date.
    #
    # That means that this is the start of a DOR date range, but falls into the middle of a FINEOS date range.
    #
    # We can "split" the date range into two by creating a new version with the required attributes.
    return [
        SyncExemptionAction(
            ActionType.CREATE_VERSION,
            start_date,
            dor_exemption.family_exemption,
            dor_exemption.medical_exemption,
        )
    ]


def resolve_leave_plans(
    family_exemption: bool, medical_exemption: bool, use_dummy_plan: Optional[bool] = False
) -> str:
    leave_plans = set()
    if not family_exemption:
        leave_plans.update(
            {ServiceAgreementLeavePlans.FAMILY.value, ServiceAgreementLeavePlans.MILITARY.value}
        )
    if not medical_exemption:
        leave_plans.add(ServiceAgreementLeavePlans.EMPLOYEE.value)
    if len(leave_plans) == 0 and use_dummy_plan:
        leave_plans.add(ServiceAgreementLeavePlans.FAM_MED_EXEMPT.value)
    return ", ".join(sorted(leave_plans))


def resolve_version_case_number(revision_number: Optional[str]) -> Optional[str]:
    # This return the version number from a given revision_number usually formatted as SA-\d{4}-SAR-{2}
    # e.g. this function returns SA-1234 for a given revision_number = SA-1234-SAR-01
    if not revision_number:
        return revision_number

    match = re.search(r"(SA-\d+).*", revision_number)

    return match.group(1) if match else revision_number


def get_employers_to_dor_exemptions_map(db_session: Session) -> EmployerDORExemptionMap:
    all_dor_exemptions = (
        db_session.query(EmployerDORExemption)
        .distinct(EmployerDORExemption.employer_id, EmployerDORExemption.dor_activity_key)
        .order_by(
            EmployerDORExemption.employer_id.desc(),
            EmployerDORExemption.dor_activity_key.desc(),
            EmployerDORExemption.import_log_id.desc(),
            EmployerDORExemption.decision_commence_date.asc(),
        )
    )
    employers_to_dor_exemptions_map: DefaultDict[uuid.UUID, List[EmployerDORExemption]] = (
        defaultdict(list)
    )
    for result in all_dor_exemptions:
        employers_to_dor_exemptions_map[result.employer_id].append(result)  # type: ignore

    return employers_to_dor_exemptions_map


def get_employers_to_fineos_service_agreements_map(
    db_session: Session,
) -> EmployerFineosServiceAgreementMap:
    employers_to_fineos_service_agreements_map: DefaultDict[
        uuid.UUID, List[FineosServiceAgreement]
    ] = defaultdict(list)

    all_fineos_service_agreements = db_session.query(FineosServiceAgreement).order_by(
        FineosServiceAgreement.employer_id.desc(),
        # Docs: https://docs.sqlalchemy.org/en/20/core/sqlelement.html#sqlalchemy.sql.expression.nulls_first
        # The initial version will have version_date as null
        FineosServiceAgreement.version_date.desc().nulls_first(),
    )

    for result in all_fineos_service_agreements:
        employers_to_fineos_service_agreements_map[result.employer_id].append(result)

    return employers_to_fineos_service_agreements_map


def _dor_fineos_data_are_identical(
    dor_row: Optional[EmployerDORExemption], fineos_sa: Optional[FineosServiceAgreement]
) -> bool:
    if fineos_sa is None:
        return False

    if dor_row is None:
        return False

    return (fineos_sa.family_exemption, fineos_sa.medical_exemption) == (
        dor_row.family_exemption,
        dor_row.medical_exemption,
    )


def insert_fineos_sa(
    db_session: db.Session,
    employer: Employer,
    fineos_revision_case_number: str,
    start_date: date,
    version_date: date | None,
    family_exemption: bool,
    medical_exemption: bool,
    import_log_id: int,
) -> None:
    case_number = resolve_version_case_number(fineos_revision_case_number)
    log_extra = {
        "fineos_revision_case_number": fineos_revision_case_number,
        "employer_id": employer.employer_id,
    }
    if not case_number:
        logger.warning(
            "Couldn't resolve version case number",
            extra=log_extra,
        )
        return

    log_extra |= {"fineos_service_agreement_case_number": case_number}

    fineos_service_agreement = None
    if version_date is None:
        # If the initial version is already in the table, update it instead of inserting.
        #
        # Dated versions are always inserted, since FINEOS does not support updates.
        fineos_service_agreement = (
            db_session.query(FineosServiceAgreement)
            .filter(
                FineosServiceAgreement.employer_id == employer.employer_id,
                FineosServiceAgreement.fineos_service_agreement_case_number == case_number,
                FineosServiceAgreement.version_date.is_(None),
            )
            .one_or_none()
        )

    if fineos_service_agreement is None:
        fineos_service_agreement = FineosServiceAgreement()
        fineos_service_agreement.employer_id = employer.employer_id
        fineos_service_agreement.fineos_service_agreement_case_number = case_number
        fineos_service_agreement.version_date = version_date

    fineos_service_agreement.start_date = start_date
    fineos_service_agreement.family_exemption = family_exemption
    fineos_service_agreement.medical_exemption = medical_exemption
    fineos_service_agreement.status_id = FineosServiceAgreementStatus.ACTIVE.status_id
    fineos_service_agreement.import_log_id = import_log_id

    db_session.add(fineos_service_agreement)
    db_session.commit()
    db_session.refresh(fineos_service_agreement)

    log_extra |= {
        "fineos_service_agreement_id": fineos_service_agreement.fineos_service_agreement_id,
    }

    if start_date == date(9999, 12, 31):
        logger.warning(
            "Storing a FINEOS service agreement with a start date of 9999-12-31", extra=log_extra
        )

    logger.info("inserted fineos_service_agreement", extra=log_extra)


def load_service_agreements(
    db_session: db.Session,
    log_entry: batch_log.LogEntry,
    fineos: AbstractFINEOSClient,
    employer_update_limit: int | None = None,
    enable_fineos_version_write: bool = True,
    seconds_between_initial_sa_calls: float | None = None,
    seconds_between_sa_calls: float | None = None,
    preselected_employers: list[str] | None = None,
) -> None:
    logger.info(
        "Starting load service agreements to FINEOS",
        extra={
            "employer_update_limit": employer_update_limit,
            "enable_fineos_version_write": enable_fineos_version_write,
            "seconds_between_initial_sa_calls": seconds_between_initial_sa_calls,
            "seconds_between_sa_calls": seconds_between_sa_calls,
        },
    )

    employers = db_session.query(Employer).filter(Employer.fineos_employer_id.isnot(None))

    # Limit the scope of load_service_agreements task to a list of preselected employers for testing purposes.
    if preselected_employers is not None:
        logger.info(
            "Running for preselected employers.",
            extra={"preselected_employers": ",".join(map(str, preselected_employers))},
        )
        employers = employers.filter(Employer.employer_id.in_(preselected_employers))

    # Filter out any admin marked employers to skip
    skip_employer_count = employers.filter(
        Employer.skip_load_service_agreement_to_fineos.is_(True)
    ).count()

    logger.info(
        f"Skipping {skip_employer_count} employers marked by admin.",
    )
    log_entry.set_metrics({Metrics.SKIPPED_EMPLOYER_COUNT: skip_employer_count})
    employers = employers.filter(Employer.skip_load_service_agreement_to_fineos.is_(False))

    # Loading additional data for employers once for time efficiency
    employers_to_dor_exemptions_map = get_employers_to_dor_exemptions_map(db_session)
    employers_to_fineos_service_agreements_map = get_employers_to_fineos_service_agreements_map(
        db_session
    )

    # Timestamp representing the last time that an initial service agreement was created during this
    # execution. Initializing to 0.0 means that one has not yet been created.
    last_initial_sa_time = 0.0

    for employer_index, employer in enumerate(employers):
        if employer_update_limit and employer_index >= employer_update_limit:
            logger.info(
                "Update employer limit was surpassed. Finishing task.",
                extra=log_entry.metrics,
            )
            break

        log_entry.increment(Metrics.TOTAL_EMPLOYER_ROW_COUNT)

        log_extra = {
            "fineos_employer_id": employer.fineos_employer_id,
            "employer_id": employer.employer_id,
        }

        try:
            sync_actions = compute_exemption_sync_actions(
                employers_to_dor_exemptions_map[employer.employer_id],
                employers_to_fineos_service_agreements_map[employer.employer_id],
                utcnow().date(),
                log_extra,
            )

            last_initial_sa_time = sync_employer(
                db_session,
                employer,
                sync_actions,
                enable_fineos_version_write,
                fineos,
                log_entry,
                last_initial_sa_time,
                seconds_between_initial_sa_calls,
                seconds_between_sa_calls,
            )
        except Exception as ex:
            logger.warning(
                "Encountered an error while computing sync actions",
                extra=log_extra | {"exception_type": type(ex), "exception_msg": str(ex)},
            )
            continue


def sync_employer(
    db_session: db.Session,
    employer: Employer,
    sync_actions: Sequence[SyncExemptionAction],
    enable_fineos_version_write: bool,
    fineos: AbstractFINEOSClient,
    log_entry: batch_log.LogEntry,
    last_initial_sa_time: float,
    seconds_between_initial_sa_calls: float | None = None,
    seconds_between_sa_calls: float | None = None,
) -> float:
    """
    This function makes use of two different optional delays between service agreement calls to
    mitigate known issues with FINEOS.

    `seconds_between_initial_sa_calls` configures a delay between initial service agreement
    creations among all employers. Expression of the delay is based on the `last_initial_sa_time`.
    If a new initial service agreement was created during execution, a new `last_initial_sa_time` is
    returned from this function. Otherwise, the function returns the argument provided for
    `last_initial_sa_time` on call.

    `seconds_between_sa_calls` configures a delay between service agreement calls for the same
    individual employer.
    """
    log_extra: dict[str, Any] = {
        "fineos_employer_id": employer.fineos_employer_id,
        "employer_id": employer.employer_id,
        "enable_fineos_version_write": enable_fineos_version_write,
    }

    if len(sync_actions) == 0:
        log_entry.increment(Metrics.NO_ACTIONS_EMPLOYER_COUNT)
        logger.debug("no actions", extra=log_extra)
        return last_initial_sa_time

    for i, action in enumerate(sync_actions):
        if i > 0 and seconds_between_sa_calls is not None:
            time.sleep(seconds_between_sa_calls)

        create_or_update_input = CreateOrUpdateServiceAgreement(
            absence_management_flag=True,
            start_date=action.start_date,
            leave_plans=resolve_leave_plans(
                action.family_exemption, action.medical_exemption, use_dummy_plan=True
            ),
        )

        log_extra |= {
            "action.type": action.type.name,
            "action.start_date": action.start_date,
            "action.family_exemption": action.family_exemption,
            "action.medical_exemption": action.medical_exemption,
            "end_date": create_or_update_input.end_date,
            "leave_plans": create_or_update_input.leave_plans,
            "version": None,
            "fineos_revision_case_number": None,
        }

        if action.type == ActionType.EDIT:
            # TODO: May also want the action to return a description of what needs to be done or details on the DOR record
            log_entry.increment(Metrics.SERVICE_AGREEMENTS_NEEDING_MANUAL_EDIT_COUNT)
            logger.info(
                "manual action required",
                extra=log_extra,
            )
            continue

        if action.type == ActionType.CREATE_VERSION:
            create_or_update_input.version = True
            log_entry.increment(Metrics.TOTAL_EMPLOYER_CREATE_VERSION_COUNT)

        if action.type == ActionType.SET_SERVICE_AGREEMENT_START_DATE:
            create_or_update_input.version = False
            log_entry.increment(Metrics.TOTAL_EMPLOYER_SET_SERVICE_AGREEMENT_START_DATE_COUNT)

            # The introduction of FINEOS 24.8.1 (FR25-1) saw an issue where FINEOS can not handle
            # too many initial service agreement creations in short succession. A delay between
            # initial service agreement creations can be used to mitigate this issue.
            # See https://lwd.atlassian.net/browse/EDM-1639 to learn more.
            if seconds_between_initial_sa_calls:
                now = time.time()
                next_initial_sa_time = last_initial_sa_time + seconds_between_initial_sa_calls

                if now < next_initial_sa_time:
                    seconds_until_next_initial_sa_call = next_initial_sa_time - now
                    time.sleep(seconds_until_next_initial_sa_call)

        log_extra |= {"version": create_or_update_input.version}

        try:
            if enable_fineos_version_write:
                if employer.fineos_employer_id is None:
                    raise ValueError("employer must have a fineos_employer_id")

                try:
                    fineos_revision_case_number = fineos.create_service_agreement_for_employer(
                        employer.fineos_employer_id, create_or_update_input
                    )
                finally:
                    if (
                        action.type == ActionType.SET_SERVICE_AGREEMENT_START_DATE
                        and seconds_between_initial_sa_calls
                    ):
                        last_initial_sa_time = time.time()

                insert_fineos_sa(
                    db_session,
                    employer,
                    fineos_revision_case_number,
                    PFML_EXEMPTION_DEFAULT_START_DATE,
                    action.start_date if action.type == ActionType.CREATE_VERSION else None,
                    action.family_exemption,
                    action.medical_exemption,
                    log_entry.import_log.import_log_id,
                )
                log_extra |= {"fineos_revision_case_number": fineos_revision_case_number}
                log_entry.increment(Metrics.UPDATED_SERVICE_AGREEMENTS_COUNT)

            logger.info("action done", extra=log_extra)

        except Exception as ex:
            logger.warning(
                "Error creating service agreement for employer",
                extra=log_extra
                | {
                    "exception_type": type(ex),
                    "exception_msg": str(ex),
                },
            )
            log_entry.increment(Metrics.ERRORED_SERVICE_AGREEMENTS_COUNT)
            break

    return last_initial_sa_time
