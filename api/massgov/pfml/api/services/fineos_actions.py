###
# This file houses all FINEOS actions currently defined. We may have
# to split the code into multiple files down the road. As of this
# writing we have the following actions:
#
# 1 - Register user with FINEOS using employee <PERSON><PERSON> and employer FEIN
# 2 - Submit an application
#
# The FINEOS API calls are defined in the FINEOSClient class. Here we
# stitch together the various API calls we have to do to complete the
# two actions described above.
#
###
import collections
import datetime
import tempfile
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import date
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Tuple, Union

import phonenumbers
from requests.models import Response

import massgov.pfml.db
import massgov.pfml.fineos.models
import massgov.pfml.util.logging as logging
import massgov.pfml.util.pdf as pdf_util
from massgov.pfml.api.models.applications.common import Language, OtherIncome
from massgov.pfml.api.models.common import EmployerBenefit, PreviousLeave
from massgov.pfml.api.models.IntermittentLeaveEpisodes.common import IntermittentLeaveEpisode
from massgov.pfml.api.models.notifications.requests import FineosNotificationLanguage
from massgov.pfml.db.lookup_data.applications import (
    LeaveReason,
    LeaveReasonQualifier,
    RelationshipQualifier,
    RelationshipToCaregiver,
)
from massgov.pfml.db.lookup_data.dor import ClaimantChildSupportStatusType
from massgov.pfml.db.lookup_data.employees import PaymentMethod
from massgov.pfml.db.lookup_data.geo import Country
from massgov.pfml.db.lookup_data.phone import PhoneType
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.change_request import ChangeRequest
from massgov.pfml.db.models.dor import ClaimantChildSupportObligation
from massgov.pfml.db.models.employees import Address, Claim, Employer, User
from massgov.pfml.db.models.fineos_web_id import FINEOSWebIdExt
from massgov.pfml.db.models.language import LkLanguage
from massgov.pfml.db.models.payments import LkFineosTaskType
from massgov.pfml.features import FeaturesConfig
from massgov.pfml.features.config.fineos import BondingDateReflexiveFieldNamesStrategy
from massgov.pfml.fineos.activity_services.models import (
    ActivitySubjectIdentifier,
    CaseIdentifier,
    CreateTaskResponse,
    WorkTypeIdentifier,
)
from massgov.pfml.fineos.common import (
    FINEOS_MAX_TASK_DESCRIPTION_LENGTH,
    SUB_CASE_DOC_TYPES,
    FineosRecipientType,
)
from massgov.pfml.fineos.exception import FINEOSClientError, FINEOSEntityNotFound
from massgov.pfml.fineos.models import CreateOrUpdateServiceAgreement
from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import (
    PFML_ContactDetails,
    PFML_EmailAddress,
    PFML_PhoneNumber,
)
from massgov.pfml.fineos.models.customer_api import AbsenceDetails
from massgov.pfml.fineos.models.customer_api import AbsencePeriod as FineosAbsencePeriod
from massgov.pfml.fineos.models.customer_api import (
    AbsencePeriodDecision,
    AbsencePeriodDecisions,
    ActualAbsencePeriodResource,
    ActualAbsencePeriodResources,
    Base64EncodedFileData,
    BenefitSummary,
    ChangeRequestPeriodCommand,
    CreateAbsencePeriodTypeRequest,
    CreateActualAbsencePeriodCommand,
    CreateActualAbsencePeriodCommandElements,
    CreateLeavePeriodsChangeRequestCommand,
    EpisodePeriodDurationBasisRequest,
    ReadDisabilityBenefitResult,
    ReasonRequest,
    ReflexiveQuestionType,
)
from massgov.pfml.fineos.models.wscomposer import OverpaymentRecovery, OverpaymentRecoveryMethod
from massgov.pfml.fineos.transforms.to_fineos.base import EFormBody
from massgov.pfml.fineos.transforms.to_fineos.eforms.childsupport import (
    DorChildSupportEFormBuilder,
    DorEformAttribute,
)
from massgov.pfml.fineos.transforms.to_fineos.eforms.employee import (
    OtherIncomesV22EFormBuilder,
    OtherLeavesV22EFormBuilder,
)
from massgov.pfml.util.datetime import convert_minutes_to_hours_minutes, utcnow
from massgov.pfml.util.logging.applications import get_application_log_attributes

logger = logging.get_logger(__name__)

# Due to Fineos requirements, when creating new exempt employers
# we create one service agreement valid for only one day (the day below)
# with an active leave plan and another service agreement starting the
# day after the date below with a retired leave plan
# this date value was specified by DFML
EXEMPT_SA_START_DATE = datetime.date(2020, 11, 30)

RELATIONSHIP_REFLEXIVE_FIELD_MAPPING = {
    RelationshipToCaregiver.CHILD.relationship_to_caregiver_description: "AgeCapacityFamilyMemberQuestionGroup.familyMemberDetailsQuestions",
    RelationshipToCaregiver.GRANDCHILD.relationship_to_caregiver_description: "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions",
    RelationshipToCaregiver.GRANDPARENT.relationship_to_caregiver_description: "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions",
    RelationshipToCaregiver.INLAW.relationship_to_caregiver_description: "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions",
    RelationshipToCaregiver.PARENT.relationship_to_caregiver_description: "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions",
    RelationshipToCaregiver.SPOUSE.relationship_to_caregiver_description: "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions",
    RelationshipToCaregiver.SIBLING.relationship_to_caregiver_description: "FamilyMemberSiblingDetailsQuestionGroup.familyMemberDetailsQuestions",
}


class LeaveNotificationReason(str, Enum):
    PREGNANCY_BIRTH_OR_RELATED_MEDICAL_TREATMENT = "Pregnancy, birth or related medical treatment"
    BONDING_WITH_A_NEW_CHILD = "Bonding with a new child (adoption/ foster care/ newborn)"
    CARING_FOR_A_FAMILY_MEMBER = "Caring for a family member"
    ACCIDENT_OR_TREATMENT_REQUIRED = "Accident or treatment required for an injury"
    SICKNESS_TREATMENT_REQUIRED_FOR_MEDICAL_CONDITION = (
        "Sickness, treatment required for a medical condition or any other medical procedure"
    )
    OUT_OF_WORK_FOR_ANOTHER_REASON = "Out of work for another reason"


def register_employee_with_claim(
    fineos: massgov.pfml.fineos.AbstractFINEOSClient,
    db_session: massgov.pfml.db.Session,
    claim: Claim,
) -> str:
    """Helper method for getting a FINEOS auth id for a user with a Claim"""
    employee_tax_id = claim.employee_tax_identifier
    if not employee_tax_id:
        raise Exception("Unable to register employee with FINEOS - No employee tax ID for claim")

    employer_fein = claim.employer_fein
    if not employer_fein:
        raise Exception("Unable to register employee with FINEOS - No employer FEIN for claim")

    fineos_employer_id = str(claim.employer.fineos_employer_id) if claim.employer else None
    return find_or_register_employee(
        fineos, employee_tax_id, employer_fein, db_session, fineos_employer_id
    )


def find_or_register_employee(
    fineos: massgov.pfml.fineos.AbstractFINEOSClient,
    employee_ssn: str,
    employer_fein: str,
    db_session: massgov.pfml.db.Session,
    employer_id: Optional[str] = None,
) -> str:
    """
    Returns an existing FINEOS web ID if it exists.

    Otherwise registers the employee, stages the resultant `FINEOSWebIdExt`, and
    returns the new FINEOS web ID.
    """

    # If a FINEOS Id exists for SSN/FEIN return it.
    fineos_web_id_ext = (
        db_session.query(FINEOSWebIdExt)
        .filter(
            FINEOSWebIdExt.employee_tax_identifier == str(employee_ssn),
            FINEOSWebIdExt.employer_fein == str(employer_fein),
        )
        .one_or_none()
    )

    if fineos_web_id_ext is None:
        fineos_web_id_ext = register_employee(fineos, employee_ssn, employer_fein, employer_id)
        # If successful save ExternalIdentifier in the database
        db_session.add(fineos_web_id_ext)
    elif fineos_web_id_ext.fineos_web_id is None:
        # This should never happen and we should have a db constraint,
        # but keep mypy happy for now.
        #
        # We don't have a non-sensitive identifier for fineos_web_id_ext so just log
        # that something's wrong and have someone take a look at it.
        raise ValueError("fineos_web_id_ext is missing a fineos_web_id")

    return fineos_web_id_ext.fineos_web_id


def register_employee(
    fineos: massgov.pfml.fineos.AbstractFINEOSClient,
    employee_ssn: str,
    employer_fein: str,
    employer_id: str | None = None,
) -> FINEOSWebIdExt:
    """
    Registers the employee as a FINEOS web user with the related employer and
    returns the resultant `FINEOSWebIdExt`.
    """

    # Find FINEOS employer id using employer FEIN
    if employer_id is None:
        employer_id = fineos.find_employer(employer_fein)

    logger.info("found employer_id %s", employer_id)

    # Generate external id
    employee_external_id = "pfml_api_{}".format(str(uuid.uuid4()))

    employee_registration = massgov.pfml.fineos.models.EmployeeRegistration(
        user_id=employee_external_id,
        customer_number=None,
        date_of_birth=datetime.date(1753, 1, 1),
        email=None,
        employer_id=employer_id,
        first_name=None,
        last_name=None,
        national_insurance_no=employee_ssn,
    )

    fineos.register_api_user(employee_registration)
    logger.info("registered as %s", employee_external_id)

    fineos_web_id_ext = FINEOSWebIdExt()
    fineos_web_id_ext.employee_tax_identifier = employee_ssn
    fineos_web_id_ext.employer_fein = employer_fein
    fineos_web_id_ext.fineos_web_id = employee_external_id
    return fineos_web_id_ext


def complete_intake(application: Application, db_session: massgov.pfml.db.Session) -> None:
    """Send an application to FINEOS for completion."""

    if application.claim is None:
        raise ValueError("application.claim is None")

    if application.claim.fineos_notification_id is None:
        raise ValueError("application.claim.fineos_notification_id is None")

    fineos = massgov.pfml.fineos.create_client()

    fineos_web_id = get_or_register_employee_fineos_web_id(fineos, application, db_session)

    fineos_web_id = str(fineos_web_id)
    db_session.commit()
    fineos.complete_intake(fineos_web_id, str(application.claim.fineos_notification_id))


def language_pref_to_fineos(language: LkLanguage | None) -> FineosNotificationLanguage:
    if not language or language.is_not_listed:
        return FineosNotificationLanguage.english
    if language.is_chinese:
        return FineosNotificationLanguage.chinese_simplified
    else:
        return FineosNotificationLanguage(language.language_description)


def language_pref_from_fineos(fineos_request):
    if (
        fineos_request.recipient_type == FineosRecipientType.LEAVE_ADMINISTRATOR
        or fineos_request.claimant_info.preferred_language is None
    ):
        return Language.english
    elif (
        fineos_request.claimant_info.preferred_language
        == FineosNotificationLanguage.chinese_simplified
    ):
        return Language.chinese_simplified
    else:
        return Language[
            FineosNotificationLanguage(fineos_request.claimant_info.preferred_language).name
        ]


def build_customer_model(application, current_user):
    """Convert an application to a FINEOS API Customer model."""
    tax_identifier = application.tax_identifier.tax_identifier
    confirmed = massgov.pfml.fineos.models.customer_api.ExtensionAttribute(
        name="Confirmed", booleanValue=True
    )
    class_ext_info = [confirmed]
    if current_user is not None:
        consented_to_data_sharing = massgov.pfml.fineos.models.customer_api.ExtensionAttribute(
            name="ConsenttoShareData", booleanValue=current_user.consented_to_data_sharing
        )
        class_ext_info.append(consented_to_data_sharing)

    if application.mass_id is not None:
        mass_id = massgov.pfml.fineos.models.customer_api.ExtensionAttribute(
            name="MassachusettsID", stringValue=application.mass_id or ""
        )
        # tests assume MassId is the first ExtensionAttribute, so insert at 0 index.
        class_ext_info.insert(0, mass_id)

    language_enum = massgov.pfml.fineos.models.customer_api.LanguageEnum(
        instanceValue=language_pref_to_fineos(application.language)
    )
    language = massgov.pfml.fineos.models.customer_api.Language(
        languageEnum=language_enum, preferredLanguage=True, written=True
    )

    customer = massgov.pfml.fineos.models.customer_api.Customer(
        firstName=application.first_name,
        secondName=application.middle_name,
        lastName=application.last_name,
        dateOfBirth=application.date_of_birth,
        # We have to send back the SSN as FINEOS wipes it from the Customer otherwise.
        idNumber=tax_identifier,
        classExtensionInformation=class_ext_info,
        languages=[language],
    )

    # API-394: Residential address is added using updateCustomerDetails endpoint,
    # but mailing address (if different from residential address) is added using
    # the addPaymentPreference or updatePaymentPreference endpoints
    if application.residential_address is not None:
        customer.customerAddress = build_customer_address(application.residential_address)
    if application.gender is not None:
        customer.gender = application.gender.fineos_gender_description

    return customer


def build_contact_details(
    application: Application,
) -> PFML_ContactDetails:
    """Convert an application's email and phone number to PFML ContactDetails model."""

    email_address = PFML_EmailAddress(
        emailAddress=application.user.email_address, emailAddressType="Email"
    )

    contact_details = PFML_ContactDetails(emailAddresses=[email_address])

    if application.phone is not None and application.phone.phone_number is not None:
        phone_number = phonenumbers.parse(application.phone.phone_number)

        phone_number_type = PhoneType.get_description(application.phone.phone_type_id)
        int_code = phone_number.country_code
        if phone_number.national_number is not None:
            telephone_no = str(phone_number.national_number)
            area_code = None

            # For US numbers, set the area code separately
            if int_code == 1:
                area_code = str(telephone_no)[:3]
                telephone_no = str(telephone_no)[-7:]

            # If the Fineos customer has existing phone number(s), this overwrites them
            # with the phone number they entered. Although you can add multiple phone
            # numbers in the Fineos UI, the Fineos API was preventing multiple phones
            # with the same phoneNumberType.
            contact_details.phoneNumbers = [
                PFML_PhoneNumber(
                    areaCode=area_code,
                    id=application.phone.fineos_phone_id,
                    intCode=str(int_code),
                    telephoneNo=telephone_no,
                    phoneNumberType=phone_number_type,
                )
            ]

    return contact_details


def build_customer_address(
    application_address: Address,
) -> massgov.pfml.fineos.models.customer_api.CustomerAddress:
    """Convert an application's address into a FINEOS API CustomerAddress model."""

    if not application_address.geo_state:
        raise ValueError("Address has no state info")

    # Note: In the FINEOS model:
    # - addressLine1 = Address Line 1
    # - addressLine2 = Address Line 2
    # - addressLine3 = Unknown. Do not use
    # - addressLine4 = City
    # - addressLine5 = Unknown. Do not use
    # - addressLine6 = State
    address = massgov.pfml.fineos.models.customer_api.Address(
        addressLine1=application_address.address_line_one,
        addressLine2=application_address.address_line_two,
        addressLine4=application_address.city,
        addressLine6=application_address.geo_state.geo_state_description,
        postCode=application_address.zip_code,
        country=Country.USA.country_description,
    )
    customer_address = massgov.pfml.fineos.models.customer_api.CustomerAddress(address=address)
    return customer_address


def build_fineos_customer_address(
    application_address: Address,
) -> massgov.pfml.fineos.models.customer_api.CreateAddressCommand:
    """Convert an application's address into a FINEOS API CustomerAddress model."""

    if not application_address.geo_state:
        raise ValueError("Address has no state info")

    address = massgov.pfml.fineos.models.customer_api.CreateAddressCommand(
        addressLine1=application_address.address_line_one,
        addressLine2=application_address.address_line_two,
        addressLine4=application_address.city,
        addressLine6=application_address.geo_state.geo_state_description,
        postCode=application_address.zip_code,
        country=massgov.pfml.fineos.models.customer_api.CountryRequest(
            name=Country.USA.country_description
        ),
    )
    return address


def determine_absence_period_status(application: Application) -> str:
    absence_period_status = ""
    known = massgov.pfml.fineos.models.customer_api.AbsencePeriodStatus.KNOWN.value
    estimated = massgov.pfml.fineos.models.customer_api.AbsencePeriodStatus.ESTIMATED.value

    if not application.leave_reason:
        raise ValueError("application.leave_reason is None")

    if application.leave_reason.leave_reason_id in [
        LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id,
        LeaveReason.PREGNANCY_MATERNITY.leave_reason_id,
        LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id,
    ]:
        return known

    elif application.leave_reason.leave_reason_id == LeaveReason.CHILD_BONDING.leave_reason_id:
        if application.has_future_child_date:
            absence_period_status = estimated
        else:
            absence_period_status = known

    return absence_period_status


def build_leave_periods(
    application: Application,
) -> Tuple[
    List[massgov.pfml.fineos.models.customer_api.ReducedScheduleLeavePeriod],
    List[massgov.pfml.fineos.models.customer_api.EpisodicLeavePeriod],
]:
    reduced_schedule_leave_periods = []
    for reduced_leave_period in application.reduced_schedule_leave_periods:
        if not reduced_leave_period.start_date or not reduced_leave_period.end_date:
            raise ValueError("Leave periods must have a start and end date.")

        [
            monday_hours_minutes,
            tuesday_hours_minutes,
            wednesday_hours_minutes,
            thursday_hours_minutes,
            friday_hours_minutes,
            saturday_hours_minutes,
            sunday_hours_minutes,
        ] = map(
            convert_minutes_to_hours_minutes,
            [
                reduced_leave_period.monday_off_minutes,
                reduced_leave_period.tuesday_off_minutes,
                reduced_leave_period.wednesday_off_minutes,
                reduced_leave_period.thursday_off_minutes,
                reduced_leave_period.friday_off_minutes,
                reduced_leave_period.saturday_off_minutes,
                reduced_leave_period.sunday_off_minutes,
            ],
        )

        # determine the status of the absence period
        absence_period_status = determine_absence_period_status(application)

        reduced_schedule_leave_periods.append(
            massgov.pfml.fineos.models.customer_api.ReducedScheduleLeavePeriod(
                startDate=reduced_leave_period.start_date,
                endDate=reduced_leave_period.end_date,
                status=absence_period_status,
                mondayOffHours=monday_hours_minutes.hours,
                mondayOffMinutes=monday_hours_minutes.minutes,
                tuesdayOffHours=tuesday_hours_minutes.hours,
                tuesdayOffMinutes=tuesday_hours_minutes.minutes,
                wednesdayOffHours=wednesday_hours_minutes.hours,
                wednesdayOffMinutes=wednesday_hours_minutes.minutes,
                thursdayOffHours=thursday_hours_minutes.hours,
                thursdayOffMinutes=thursday_hours_minutes.minutes,
                fridayOffHours=friday_hours_minutes.hours,
                fridayOffMinutes=friday_hours_minutes.minutes,
                saturdayOffHours=saturday_hours_minutes.hours,
                saturdayOffMinutes=saturday_hours_minutes.minutes,
                sundayOffHours=sunday_hours_minutes.hours,
                sundayOffMinutes=sunday_hours_minutes.minutes,
            )
        )

    intermittent_leave_periods = []
    for int_leave_period in application.intermittent_leave_periods:
        if not int_leave_period.start_date or not int_leave_period.end_date:
            raise ValueError("Leave periods must have a start and end date.")
        intermittent_leave_periods.append(
            massgov.pfml.fineos.models.customer_api.EpisodicLeavePeriod(
                startDate=int_leave_period.start_date,
                endDate=int_leave_period.end_date,
                frequency=int_leave_period.frequency,
                frequencyInterval=int_leave_period.frequency_interval,
                frequencyIntervalBasis=int_leave_period.frequency_interval_basis,
                duration=int_leave_period.duration,
                durationBasis=int_leave_period.duration_basis,
            )
        )

    return reduced_schedule_leave_periods, intermittent_leave_periods


def application_reason_to_claim_reason(
    application: Application,
) -> Tuple[
    str,
    Optional[str],
    Optional[str],
    Optional[str],
    Optional[str],
    Optional[str],
    LeaveNotificationReason,
]:
    """Calculate a claim reason, reason qualifiers, relationship qualifiers, and notification reason
    from an application's reason and related fields. For example, an application may have have a medical
    leave reason and also have pregnant_or_recent_birth set to true which would get saved to FINEOS as a
    claim with reason set to pregnancy.
    """
    # Leave Reason and Leave Reason Qualifier mapping.
    # Relationship and Relationship Qualifier mapping.
    reason = reason_qualifier_1 = reason_qualifier_2 = None
    primary_relationship = primary_rel_qualifier_1 = primary_rel_qualifier_2 = None
    notification_reason = None

    if not application.leave_reason:
        raise ValueError("application.leave_reason is None")

    if application.leave_reason_id == LeaveReason.PREGNANCY_MATERNITY.leave_reason_id:
        reason = LeaveReason.PREGNANCY_MATERNITY.leave_reason_description
        reason_qualifier_1 = (
            LeaveReasonQualifier.POSTNATAL_DISABILITY.leave_reason_qualifier_description
        )
        notification_reason = LeaveNotificationReason.PREGNANCY_BIRTH_OR_RELATED_MEDICAL_TREATMENT

    elif (
        application.leave_reason_id == LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id
    ):
        reason = LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_description
        reason_qualifier_1 = (
            LeaveReasonQualifier.NOT_WORK_RELATED.leave_reason_qualifier_description
        )
        reason_qualifier_2 = LeaveReasonQualifier.SICKNESS.leave_reason_qualifier_description
        notification_reason = LeaveNotificationReason.ACCIDENT_OR_TREATMENT_REQUIRED

    elif application.leave_reason_id == LeaveReason.CHILD_BONDING.leave_reason_id:
        reason = application.leave_reason.leave_reason_description
        reason_qualifier = application.leave_reason_qualifier

        if not reason_qualifier:
            raise ValueError("application.leave_reason_qualifier is None")

        reason_qualifier_1 = reason_qualifier.leave_reason_qualifier_description
        notification_reason = LeaveNotificationReason.BONDING_WITH_A_NEW_CHILD
        primary_relationship = RelationshipToCaregiver.CHILD.relationship_to_caregiver_description

        if (
            reason_qualifier.leave_reason_qualifier_id
            == LeaveReasonQualifier.ADOPTION.leave_reason_qualifier_id
        ):
            primary_rel_qualifier_1 = (
                RelationshipQualifier.ADOPTED.relationship_qualifier_description
            )
        elif (
            reason_qualifier.leave_reason_qualifier_id
            == LeaveReasonQualifier.FOSTER_CARE.leave_reason_qualifier_id
        ):
            primary_rel_qualifier_1 = (
                RelationshipQualifier.FOSTER.relationship_qualifier_description
            )
        else:
            primary_rel_qualifier_1 = (
                RelationshipQualifier.BIOLOGICAL.relationship_qualifier_description
            )

    elif application.leave_reason_id == LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id:
        if not application.caring_leave_metadata:
            raise ValueError("Caring leave requests must have caring leave metadata")

        reason = application.leave_reason.leave_reason_description
        reason_qualifier_1 = (
            LeaveReasonQualifier.SERIOUS_HEALTH_CONDITION.leave_reason_qualifier_description
        )
        notification_reason = LeaveNotificationReason.CARING_FOR_A_FAMILY_MEMBER

        primary_relationship = None

        primary_relationship = (
            application.caring_leave_metadata.relationship_to_caregiver.relationship_to_caregiver_description  # type: ignore
        )

        # Map relationship to qualifiers
        # All relationships use BIOLOGICAL as qualifier_1 and have no qualifier_2 qualifier except:
        # INLAW - uses PARENT_IN_LAW as qualifier_1 and has no qualifier_2
        # SPOUSE - uses LEGALLY_MARRIED as qualifier_1 and UNDISCLOSED as qualifier_2 (this is the only relationship with a qualifier_2)
        if (
            application.caring_leave_metadata.relationship_to_caregiver_id
            == RelationshipToCaregiver.INLAW.relationship_to_caregiver_id
        ):
            primary_rel_qualifier_1 = (
                RelationshipQualifier.PARENT_IN_LAW.relationship_qualifier_description
            )

        elif (
            application.caring_leave_metadata.relationship_to_caregiver_id
            == RelationshipToCaregiver.SPOUSE.relationship_to_caregiver_id
        ):
            primary_rel_qualifier_1 = (
                RelationshipQualifier.LEGALLY_MARRIED.relationship_qualifier_description
            )
            primary_rel_qualifier_2 = (
                RelationshipQualifier.UNDISCLOSED.relationship_qualifier_description
            )

        else:
            primary_rel_qualifier_1 = (
                RelationshipQualifier.BIOLOGICAL.relationship_qualifier_description
            )

    else:
        raise ValueError("Invalid application.leave_reason")

    assert reason

    return (
        reason,
        reason_qualifier_1,
        reason_qualifier_2,
        primary_relationship,
        primary_rel_qualifier_1,
        primary_rel_qualifier_2,
        notification_reason,
    )


def build_absence_case(
    application: Application,
) -> massgov.pfml.fineos.models.customer_api.AbsenceCase:
    """Convert an Application to a FINEOS API AbsenceCase model."""
    continuous_leave_periods = []

    for leave_period in application.continuous_leave_periods:
        if not leave_period.start_date or not leave_period.end_date:
            raise ValueError("Leave periods must have a start and end date.")

        # determine the status of the absence period
        absence_period_status = determine_absence_period_status(application)

        continuous_leave_periods.append(
            massgov.pfml.fineos.models.customer_api.TimeOffLeavePeriod(
                startDate=leave_period.start_date,
                endDate=leave_period.end_date,
                startDateFullDay=True,
                endDateFullDay=True,
                status=absence_period_status,
            )
        )

    reduced_schedule_leave_periods, intermittent_leave_periods = build_leave_periods(application)

    # Leave Reason and Leave Reason Qualifier mapping.
    # Relationship and Relationship Qualifier mapping.
    (
        reason,
        reason_qualifier_1,
        reason_qualifier_2,
        primary_relationship,
        primary_rel_qualifier_1,
        primary_rel_qualifier_2,
        notification_reason,
    ) = application_reason_to_claim_reason(application)

    absence_case = massgov.pfml.fineos.models.customer_api.AbsenceCase(
        additionalComments="PFML API " + str(application.application_id),
        intakeSource="Self-Service",
        notifiedBy="Employee",
        reason=reason,
        reasonQualifier1=reason_qualifier_1,
        reasonQualifier2=reason_qualifier_2,
        notificationReason=notification_reason,
        timeOffLeavePeriods=continuous_leave_periods if continuous_leave_periods else None,
        reducedScheduleLeavePeriods=(
            reduced_schedule_leave_periods if reduced_schedule_leave_periods else None
        ),
        episodicLeavePeriods=intermittent_leave_periods if intermittent_leave_periods else None,
        employerNotified=application.employer_notified,
        employerNotificationDate=application.employer_notification_date,
        primaryRelationship=primary_relationship,
        primaryRelQualifier1=primary_rel_qualifier_1,
        primaryRelQualifier2=primary_rel_qualifier_2,
    )
    return absence_case


def get_or_register_employee_fineos_web_id(
    fineos: massgov.pfml.fineos.AbstractFINEOSClient,
    application: Application,
    db_session: massgov.pfml.db.Session,
) -> str:

    if not application.tax_identifier:
        raise ValueError("application.tax_identifier is None")

    tax_identifier = application.tax_identifier.tax_identifier

    employer_fein = application.employer_fein_for_fineos
    if employer_fein is None:
        raise ValueError("Missing employer fein")

    return find_or_register_employee(fineos, tax_identifier, employer_fein, db_session)


def build_bonding_date_reflexive_question(
    application: Application,
) -> massgov.pfml.fineos.models.customer_api.AdditionalInformation:
    from massgov.pfml.api import app

    field_names: BondingDateReflexiveFieldNamesStrategy = (
        app.get_features_config().fineos.bonding_date_field_names
    )

    if (
        application.leave_reason_qualifier_id
        == LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id
    ):
        field_name = field_names.date_of_birth
        date_value = application.child_birth_date
    else:
        field_name = field_names.adoption_date
        date_value = application.child_placement_date

    reflexive_details = massgov.pfml.fineos.models.customer_api.Attribute(
        fieldName=field_name, dateValue=date_value
    )
    reflexive_question = massgov.pfml.fineos.models.customer_api.AdditionalInformation(
        reflexiveQuestionLevel="reason", reflexiveQuestionDetails=[reflexive_details]
    )
    return reflexive_question


def build_caring_leave_reflexive_question(
    application: Application,
) -> massgov.pfml.fineos.models.customer_api.AdditionalInformation:
    if not application.caring_leave_metadata:
        raise ValueError("Caring leave requests must have caring leave metadata")

    reflexive_question_field_name = RELATIONSHIP_REFLEXIVE_FIELD_MAPPING[
        application.caring_leave_metadata.relationship_to_caregiver.relationship_to_caregiver_description  # type: ignore
    ]

    caring_leave_metadata = application.caring_leave_metadata

    reflexive_question_details: List[ReflexiveQuestionType] = []
    # first name
    first_name_details = massgov.pfml.fineos.models.customer_api.Attribute(
        fieldName=f"{reflexive_question_field_name}.firstName",
        stringValue=caring_leave_metadata.family_member_first_name,
    )
    reflexive_question_details.append(first_name_details)

    # middle name
    if caring_leave_metadata.family_member_middle_name:
        middle_name_field = "middleInitial"

        middle_name_details = massgov.pfml.fineos.models.customer_api.Attribute(
            fieldName=f"{reflexive_question_field_name}.{middle_name_field}",
            # FINEOS API calls this field "middle initial", though it will accept a full middle name
            stringValue=caring_leave_metadata.family_member_middle_name,
        )
        reflexive_question_details.append(middle_name_details)

    # last name
    last_name_name_details = massgov.pfml.fineos.models.customer_api.Attribute(
        fieldName=f"{reflexive_question_field_name}.lastName",
        stringValue=caring_leave_metadata.family_member_last_name,
    )
    reflexive_question_details.append(last_name_name_details)

    # family member date of birth
    if caring_leave_metadata.family_member_date_of_birth:
        date_of_birth_details = massgov.pfml.fineos.models.customer_api.Attribute(
            fieldName=f"{reflexive_question_field_name}.dateOfBirth",
            dateValue=caring_leave_metadata.family_member_date_of_birth,
        )
        reflexive_question_details.append(date_of_birth_details)

    reflexive_question = massgov.pfml.fineos.models.customer_api.AdditionalInformation(
        reflexiveQuestionLevel="primary relationship",
        reflexiveQuestionDetails=reflexive_question_details,
    )

    return reflexive_question


def get_customer_occupation(
    fineos_client: massgov.pfml.fineos.AbstractFINEOSClient, fineos_web_id: str, customer_id: str
) -> Optional[massgov.pfml.fineos.models.customer_api.ReadCustomerOccupation]:
    logger.info("getting occupation for customer %s", customer_id)
    try:
        # fineos_web_id is associated with a specific employee and employer, so this will only return the occupations for the employee from the associated employer
        occupations = fineos_client.get_customer_occupations_customer_api(fineos_web_id)
        if not occupations:
            logger.warning(
                "No occupations found for customer",
                extra={"customer_id": customer_id, "fineos_web_id": fineos_web_id},
            )
            return None
        else:
            return occupations[0]
    except Exception as error:
        logger.warning(
            "get_customer_occuption failure",
            extra={"customer_id": customer_id, "status": getattr(error, "response_status", None)},
            exc_info=True,
        )
        raise error


def get_occupation(
    fineos_client: massgov.pfml.fineos.AbstractFINEOSClient, user_id: str, application: Application
) -> massgov.pfml.fineos.models.customer_api.ReadCustomerOccupation:

    if application.claim is None:
        raise ValueError("appliation.claim is None")

    logger.info("getting occupation for absence_case %s", application.claim.fineos_absence_id)
    try:
        return fineos_client.get_case_occupations(
            user_id, str(application.claim.fineos_notification_id)
        )[0]
    except Exception as error:
        logger.warning(
            "get_occuption failure",
            extra={
                "absence_case_id": application.claim.fineos_absence_id,
                "application.absence_case_id": application.claim.fineos_absence_id,
                "application.application_id": application.application_id,
                "status": getattr(error, "response_status", None),
            },
            exc_info=True,
        )
        raise error


def upsert_week_based_work_pattern(
    fineos_client, user_id, application, occupation_id, existing_work_pattern
):
    """Add or update work pattern on an in progress absence case"""
    if occupation_id is None:
        raise ValueError("occupation_id is None")
    week_based_work_pattern = build_week_based_work_pattern(application)
    log_attributes = {
        "application.application_id": application.application_id,
        "occupation_id": occupation_id,
        "work_pattern_basis": existing_work_pattern,
    }

    if application.claim is not None:
        log_attributes["application.absence_case_id"] = application.claim.fineos_absence_id
        log_attributes["absence_case_id"] = application.claim.fineos_absence_id
        logger.info(
            "upserting work_pattern for absence case %s",
            application.claim.fineos_absence_id,
            extra=log_attributes,
        )
    else:
        logger.info("upserting work_pattern for empty claim", extra=log_attributes)

    # if we already have the pattern, just update
    if existing_work_pattern == "Week Based":
        update_week_based_work_pattern(
            fineos_client, user_id, occupation_id, week_based_work_pattern, log_attributes
        )
        if application.claim is not None:
            logger.info(
                "updated work_pattern successfully for absence case %s",
                application.claim.fineos_absence_id,
                extra=log_attributes,
            )
        else:
            logger.info("updated work_pattern successfully", extra=log_attributes)
    # if unknown, add it
    elif existing_work_pattern == "Unknown":
        add_week_based_work_pattern(
            fineos_client, user_id, occupation_id, week_based_work_pattern, log_attributes
        )
        logger.info(
            "added work_pattern successfully for customer occupation %s",
            occupation_id,
            extra=log_attributes,
        )
    # otherwise we don't do anything
    else:
        logger.info(
            "No work_pattern request for customer occupation %s",
            occupation_id,
            extra=log_attributes,
        )


def add_week_based_work_pattern(
    fineos_client: massgov.pfml.fineos.AbstractFINEOSClient,
    user_id: str,
    occupation_id: str,
    week_based_work_pattern: massgov.pfml.fineos.models.customer_api.WeekBasedWorkPattern,
    log_attributes: Optional[Dict] = None,
) -> None:
    try:
        fineos_client.add_week_based_work_pattern(user_id, occupation_id, week_based_work_pattern)
    except Exception as error:
        extra = {} if log_attributes is None else log_attributes
        extra.update({"status": getattr(error, "response_status", None)})
        logger.warning("add_week_based_work_pattern failure", extra=extra, exc_info=True)
        raise error


def update_week_based_work_pattern(
    fineos_client: massgov.pfml.fineos.AbstractFINEOSClient,
    user_id: str,
    occupation_id: str,
    week_based_work_pattern: massgov.pfml.fineos.models.customer_api.WeekBasedWorkPattern,
    log_attributes: Optional[Dict] = None,
) -> None:
    try:
        fineos_client.update_week_based_work_pattern(
            user_id, occupation_id, week_based_work_pattern
        )
    except Exception as error:
        extra = {} if log_attributes is None else log_attributes
        extra.update({"status": getattr(error, "response_status", None)})
        logger.warning("update_week_based_work_pattern failure", extra=extra, exc_info=True)
        raise error


def update_employer_structure(
    application: Application, fineos_employer: massgov.pfml.fineos.models.OCOrganisation
) -> Optional[str]:
    # (PFMLPB-3944) FINEOS doesn't perform validation for
    # When a claimant previously selected an organization unit
    # but ended up changing employers afterwards
    if application.organization_unit is not None and application.employer is not None:
        if application.employer.employer_id != application.organization_unit.employer_id:
            # so, automatically remove organization unit from application
            application.organization_unit_id = None

    # Only pick a worksite if an organization unit was picked
    if application.organization_unit_id:
        return fineos_employer.get_worksite_id()

    return None


def update_occupation_details(
    fineos_client: massgov.pfml.fineos.AbstractFINEOSClient,
    application: Application,
    occupation_id: Optional[int],
    worksite_id: Optional[str],
) -> None:
    if occupation_id is None:
        raise ValueError("occupation_id is None")

    employment_status_label = None
    if application.employment_status:
        employment_status_label = application.employment_status.fineos_label

    fineos_org_unit_id = None
    if application.organization_unit:
        fineos_org_unit_id = application.organization_unit.fineos_id

    fineos_client.update_occupation(
        occupation_id,
        employment_status_label,
        application.hours_worked_per_week,
        fineos_org_unit_id,
        worksite_id,
    )


def build_week_based_work_pattern(
    application: Application,
) -> massgov.pfml.fineos.models.customer_api.WeekBasedWorkPattern:
    """Construct FINEOS customer api work pattern models from application"""

    if application.work_pattern is None:
        raise ValueError("application.work_pattern is None")

    fineos_work_pattern_days = []

    for day in application.work_pattern.work_pattern_days:
        if not day.day_of_week or not day.day_of_week.day_of_week_description:
            raise ValueError("Work pattern days must include the day of the week.")

        (hours, minutes) = convert_minutes_to_hours_minutes(day.minutes or 0)

        fineos_work_pattern_days.append(
            massgov.pfml.fineos.models.customer_api.WorkPatternDay(
                dayOfWeek=day.day_of_week.day_of_week_description,
                weekNumber=1,
                hours=hours or 0,
                minutes=minutes or 0,
            )
        )

    return massgov.pfml.fineos.models.customer_api.WeekBasedWorkPattern(
        # FINEOS does not support Variable work patterns.
        # We capture variable work patterns as average hours worked per week,
        # split the hours evenly across 7 work pattern days, and send the days to
        # FINEOS as a Fixed pattern
        # TODO (CP-1377): Record variable work pattern somewhere in FINEOS
        workPatternType="Fixed",
        workWeekStarts="Sunday",
        patternStartDate=None,
        patternStatus=None,
        workPatternDays=fineos_work_pattern_days,
    )


def upload_document(
    application: Application,
    document_type: str,
    file_content: bytes,
    file_name: str,
    content_type: str,
    description: str,
    db_session: massgov.pfml.db.Session,
    fineos_appeal_id: Optional[str] = None,
    with_multipart: bool = False,
) -> massgov.pfml.fineos.models.customer_api.Document:
    fineos = massgov.pfml.fineos.create_client()

    fineos_web_id = get_or_register_employee_fineos_web_id(fineos, application, db_session)

    if not fineos_appeal_id:
        id_to_associate = application.fineos_absence_id
        if id_to_associate is None:
            raise ValueError("Missing absence id")
    else:
        id_to_associate = fineos_appeal_id

    if with_multipart:
        upload_fn = fineos.upload_document_multipart
    else:
        upload_fn = fineos.upload_document

    fineos_document = upload_fn(
        fineos_web_id,
        id_to_associate,
        document_type,
        file_content,
        file_name,
        content_type,
        description,
    )
    return fineos_document


def upload_document_with_claim(
    claim: Claim,
    document_type: str,
    file_content: bytes,
    file_name: str,
    content_type: str,
    description: str,
    db_session: massgov.pfml.db.Session,
    with_multipart: bool = False,
) -> massgov.pfml.fineos.models.customer_api.Document:
    fineos = massgov.pfml.fineos.create_client()

    fineos_web_id = register_employee_with_claim(fineos, db_session, claim)

    absence_id = claim.fineos_absence_id
    if absence_id is None:
        raise ValueError("Missing absence id")

    if with_multipart:
        upload_fn = fineos.upload_document_multipart
    else:
        upload_fn = fineos.upload_document

    if len(file_content) > 3_000_000:
        with (
            tempfile.SpooledTemporaryFile(mode="wb+") as file,
            tempfile.SpooledTemporaryFile(mode="wb+") as compressed_file,
        ):
            file.write(file_content)
            try:
                file_size = pdf_util.compress_pdf(file, compressed_file)

                file_name = f"Compressed_{file_name}"

                compressed_file.seek(0)
                file_content = compressed_file.read()
                logger.info(f"upload_document_with_claim - compressed file size: {file_size}")

            except pdf_util.PDFSizeError:
                logger.warning("upload_document_with_claim - file too large")

            except pdf_util.PDFCompressionError:
                logger.warning(
                    "upload_document_with_claim - file too large and failed to compress.",
                )

    fineos_document = upload_fn(
        fineos_web_id, absence_id, document_type, file_content, file_name, content_type, description
    )
    return fineos_document


def upload_document_to_dms(
    file_name: str,
    file: bytes,
    document_type: str,
    class_id: Optional[str] = None,
    index_id: Optional[str] = None,
    current_retry: int = 0,
    max_retries: int = 2,
    fineos: Optional[massgov.pfml.fineos.AbstractFINEOSClient] = None,
    exception: Optional[Exception] = None,
) -> Optional[Response]:
    if fineos is None:
        fineos = massgov.pfml.fineos.create_client()

    doc_properties = {
        "title": file_name,
        "description": file_name,
        "receivedDate": str(datetime.datetime.now().isoformat()),
        "managedReqId": 0,
        "status": "Completed",
        "fineosDocType": document_type,
        "dmsDocType": document_type,
    }

    if class_id is not None:
        doc_properties["partyClassId"] = class_id

    if index_id is not None:
        doc_properties["partyIndexId"] = index_id

    data = {
        "dmsDocId": file_name,
        "fileExtension": "." + file_name.split(".")[1],
        "docProperties": doc_properties,
    }

    if current_retry < max_retries:
        logger.info(f"RETRY ({current_retry}) to upload document {file_name} to Fineos Api.")
        try:
            response = fineos.upload_document_to_dms(file_name, file, data)
            logger.info(f"File {file_name} was successfully uploaded to Fineos Api.")
            return response
        except Exception as e:
            # TODO: does this change any current dashboards, NR queries?
            logger.exception(f"Upload {document_type} exception.", exc_info=e)
            return upload_document_to_dms(
                file_name,
                file,
                document_type,
                class_id=class_id,
                index_id=index_id,
                current_retry=current_retry + 1,
                max_retries=max_retries,
                fineos=fineos,
                exception=e,
            )

    # Returns None when there are no more retries allowed
    logger.warning("No more RETRY allowed to Fineos Api.")
    # TODO: verify
    # previously this exception did not exist
    # and even if the document failed all retries,
    # it would be counted as a successful upload in the 1099 metrics
    raise Exception("No more RETRY allowed to Fineos Api.")


def build_payment_preference(
    application: Application,
    payment_address: Optional[massgov.pfml.fineos.models.customer_api.CustomerAddress],
) -> massgov.pfml.fineos.models.customer_api.NewPaymentPreference:
    payment_preference = application.payment_preference
    if not payment_preference:
        raise ValueError("application.payment_preference is None")

    override_postal_addr = True if payment_address else None

    if payment_preference.payment_method_id == PaymentMethod.ACH.payment_method_id:
        if not payment_preference.account_number or not payment_preference.routing_number:
            raise ValueError(
                "ACH payment preference must include an account number and routing number."
            )

        account_details = massgov.pfml.fineos.models.customer_api.AccountDetails(
            accountName=f"{application.first_name} {application.last_name}",
            accountNo=payment_preference.account_number,
            routingNumber=payment_preference.routing_number,
            accountType=(
                payment_preference.bank_account_type.bank_account_type_description
                if payment_preference.bank_account_type
                else ""
            ),
        )
        fineos_payment_preference = massgov.pfml.fineos.models.customer_api.NewPaymentPreference(
            paymentMethod=PaymentMethod.ACH.payment_method_description,
            isDefault=True,
            customerAddress=payment_address,
            accountDetails=account_details,
            overridePostalAddress=override_postal_addr,
        )
    elif payment_preference.payment_method_id == PaymentMethod.CHECK.payment_method_id:
        fineos_payment_preference = massgov.pfml.fineos.models.customer_api.NewPaymentPreference(
            paymentMethod=PaymentMethod.CHECK.payment_method_description,
            isDefault=True,
            customerAddress=payment_address,
            chequeDetails=massgov.pfml.fineos.models.customer_api.ChequeDetails(),
            overridePostalAddress=override_postal_addr,
        )
    else:
        raise ValueError("Invalid application.payment_preference.payment_method")
    return fineos_payment_preference


def build_customer_payment_preference(
    application: Application,
    payment_address: Optional[massgov.pfml.fineos.models.customer_api.CreateAddressCommand],
) -> massgov.pfml.fineos.models.customer_api.CreatePaymentPreferenceCommand:
    payment_preference = application.payment_preference

    if not payment_preference or not payment_preference.payment_method:
        raise ValueError("application.payment_preference is None")

    payment_method = massgov.pfml.fineos.models.customer_api.PaymentMethodRequest(
        name=payment_preference.payment_method.payment_method_description
    )

    override_postal_addr = True if payment_address else None

    if payment_preference.payment_method_id == PaymentMethod.ACH.payment_method_id:
        if not payment_preference.account_number or not payment_preference.routing_number:
            raise ValueError(
                "ACH / Preapid Card payment preference must include an account number and routing number."
            )

        account_type = massgov.pfml.fineos.models.customer_api.AccountTypeRequest(
            name=(
                payment_preference.bank_account_type.bank_account_type_description
                if payment_preference.bank_account_type
                else "Checking"
            )
        )

        account_detail = massgov.pfml.fineos.models.customer_api.CreateAccountDetailCommand(
            accountName=f"{application.first_name} {application.last_name}",
            accountNo=str(payment_preference.account_number),
            routingNumber=str(payment_preference.routing_number),
            accountType=account_type,
        )

        fineos_payment_preference = (
            massgov.pfml.fineos.models.customer_api.CreatePaymentPreferenceCommand(
                paymentMethod=payment_method,
                accountDetail=account_detail,
                address=payment_address,
                isDefault=True,
                overridePostalAddress=override_postal_addr,
            )
        )
    elif payment_preference.payment_method_id == PaymentMethod.PREPAID_CARD.payment_method_id:
        account_type = massgov.pfml.fineos.models.customer_api.AccountTypeRequest(
            name=(
                payment_preference.bank_account_type.bank_account_type_description
                if payment_preference.bank_account_type
                else "Checking"
            )
        )

        # PFMLPB-19529 : Fineos always expects values for account number and routing number
        # however, for prepaid card, we don't have these values until we register the claimant with US Bank.
        # Due to this we need to pass blank values to the API.
        account_detail = massgov.pfml.fineos.models.customer_api.CreateAccountDetailCommand(
            accountName=f"{application.first_name} {application.last_name}",
            accountNo=" ",
            routingNumber=" ",
            accountType=account_type,
        )

        fineos_payment_preference = (
            massgov.pfml.fineos.models.customer_api.CreatePaymentPreferenceCommand(
                paymentMethod=payment_method,
                accountDetail=account_detail,
                address=payment_address,
                isDefault=True,
                overridePostalAddress=override_postal_addr,
            )
        )

    elif payment_preference.payment_method_id == PaymentMethod.CHECK.payment_method_id:
        fineos_payment_preference = (
            massgov.pfml.fineos.models.customer_api.CreatePaymentPreferenceCommand(
                paymentMethod=payment_method,
                chequeDetail=massgov.pfml.fineos.models.customer_api.CreateChequeDetailCommand(),
                address=payment_address,
                isDefault=True,
                overridePostalAddress=override_postal_addr,
            )
        )
    else:
        raise ValueError("Invalid application.payment_preference.payment_method")
    return fineos_payment_preference


def submit_customer_payment_preference(
    application: Application, db_session: massgov.pfml.db.Session
) -> massgov.pfml.fineos.models.customer_api.PaymentPreferenceResource:
    fineos = massgov.pfml.fineos.create_client()
    fineos_web_id = get_or_register_employee_fineos_web_id(fineos, application, db_session)

    if application.has_mailing_address and application.mailing_address:
        fineos_payment_addr: Optional[
            massgov.pfml.fineos.models.customer_api.CreateAddressCommand
        ] = build_fineos_customer_address(application.mailing_address)
    else:
        fineos_payment_addr = None

    fineos_payment_preference = build_customer_payment_preference(application, fineos_payment_addr)
    return fineos.create_customer_payment_preference(fineos_web_id, fineos_payment_preference)


def get_customer_payment_preference(
    application: Application, db_session: massgov.pfml.db.Session
) -> massgov.pfml.fineos.models.customer_api.PaymentPreferenceCustomerResources:
    fineos = massgov.pfml.fineos.create_client()
    fineos_web_id = get_or_register_employee_fineos_web_id(fineos, application, db_session)
    return fineos.get_customer_payment_preference(fineos_web_id)


def update_customer_payment_preference(
    application: Application, db_session: massgov.pfml.db.Session
) -> massgov.pfml.fineos.models.customer_api.PaymentPreferenceResource:
    fineos = massgov.pfml.fineos.create_client()
    fineos_web_id = get_or_register_employee_fineos_web_id(fineos, application, db_session)

    # It was decided to use the update payment preference solely for updating the
    # Fineos account and routing numbers after receiving them from US Bank, and therefore
    # the address is not being updated.

    # if application.has_mailing_address and application.mailing_address:
    #     fineos_payment_addr: Optional[massgov.pfml.fineos.models.customer_api.CustomerAddress] = (
    #         build_customer_address(application.mailing_address)
    #     )
    # else:
    #     fineos_payment_addr = None
    payment_preference = application.payment_preference

    if (
        not payment_preference
        or not payment_preference.account_number
        or not payment_preference.routing_number
    ):
        raise ValueError(
            "Prepaid card payment preference must include an account number and routing number."
        )

    payment_preference_id = None
    fineos_payment_preference = None
    fineos_payment_preferences = fineos.get_customer_payment_preference(fineos_web_id)

    if not fineos_payment_preferences or not fineos_payment_preferences.elements:
        raise ValueError(f"No payment preferences found for fineos web id {fineos_web_id}")

    for payment_pref in fineos_payment_preferences.elements:
        if (
            payment_pref.paymentMethod
            and payment_pref.paymentMethod.name
            == PaymentMethod.PREPAID_CARD.payment_method_description
            and payment_pref.default
        ):
            payment_preference_id = payment_pref.id

            payment_method = massgov.pfml.fineos.models.customer_api.PaymentMethodRequest(
                name=PaymentMethod.PREPAID_CARD.payment_method_description
            )

            account_type = massgov.pfml.fineos.models.customer_api.AccountTypeRequest(
                name=(
                    payment_preference.bank_account_type.bank_account_type_description
                    if payment_preference.bank_account_type
                    else "Checking"
                )
            )

            account_details = massgov.pfml.fineos.models.customer_api.EditAccountDetailCommand(
                accountName=f"{application.first_name} {application.last_name}",
                accountNo=payment_preference.account_number,
                routingNumber=payment_preference.routing_number,
                accountType=account_type,
            )
            fineos_payment_preference = (
                massgov.pfml.fineos.models.customer_api.EditPaymentPreferenceCommand(
                    paymentMethod=payment_method,
                    accountDetail=account_details,
                )
            )

            break

    if not payment_preference_id or not fineos_payment_preference:
        raise ValueError("No prepaid card payment preference found for update")

    return fineos.update_customer_payment_preference(
        fineos_web_id, payment_preference_id, fineos_payment_preference
    )


def download_document(
    application: Application,
    fineos_document_id: str,
    db_session: massgov.pfml.db.Session,
    document_type: Union[str, None],
) -> Base64EncodedFileData:
    fineos = massgov.pfml.fineos.create_client()

    fineos_web_id = get_or_register_employee_fineos_web_id(fineos, application, db_session)

    absence_id = application.fineos_absence_id
    if absence_id is None:
        raise ValueError("Missing absence id")

    if not document_type or document_type.lower() in SUB_CASE_DOC_TYPES:
        fineos_documents = fineos.get_documents(fineos_web_id, absence_id)
        for doc in fineos_documents:
            if fineos_document_id == str(doc.documentId):
                absence_case = doc.caseId
                break
    else:
        absence_case = absence_id
    if not absence_case:
        logger.warning(
            "Document with that fineos_document_id could not be found",
            extra={
                "absence_id": absence_id,
                "absence_case_id": absence_id,
                "fineos_document_id": fineos_document_id,
            },
        )
        raise Exception("Document with that fineos_document_id could not be found")

    response = fineos.download_document(fineos_web_id, fineos_document_id, absence_case)
    return response


def download_customer_document(
    user: User,
    fineos_document_id: str,
    db_session: massgov.pfml.db.Session,
) -> Base64EncodedFileData:
    fineos = massgov.pfml.fineos.create_client()

    if not user.submitted_applications:
        raise Exception(
            "User does not have any submitted applications, therefore could not establish a session"
        )

    application = user.submitted_applications[0]

    fineos_web_id = get_or_register_employee_fineos_web_id(fineos, application, db_session)

    document = fineos.download_document(fineos_web_id, fineos_document_id)

    return document


def create_or_update_employer(
    fineos: massgov.pfml.fineos.AbstractFINEOSClient,
    employer: Employer,
) -> int:
    # Determine if operation is create or update by seeing if the API Employer
    # record has a fineos_employer_id already.
    is_create = employer.fineos_employer_id is None

    existing_fineos_record = None
    if not is_create:
        try:
            read_employer_response = fineos.read_employer(employer.employer_fein)
            existing_fineos_record = read_employer_response.OCOrganisation[0]
        except FINEOSEntityNotFound:
            logger.warning(
                "Did not find employer in FINEOS as expected. Continuing with update as create.",
                extra={
                    "internal_employer_id": employer.employer_id,
                    "fineos_employer_id": employer.fineos_employer_id,
                },
            )
            is_create = True
            pass

    if not employer.employer_name:
        raise ValueError(
            "An Employer must have a employer_name in order to create or update an employer."
        )

    if not employer.employer_dba:
        raise ValueError(
            "An Employer must have a employer_dba in order to create or update an employer."
        )

    # The FINEOS employer DBA cannot be larger than 100 characters.
    employer_dba = employer.employer_dba[:100]

    employer_request_body = massgov.pfml.fineos.models.CreateOrUpdateEmployer(
        # `fineos_customer_nbr` is used as the Organization's CustomerNo
        # attribute for the request, which FINEOS uses to determine if this is a
        # create or update on their end.
        fineos_customer_nbr=str(employer.employer_id),
        employer_fein=employer.employer_fein,
        employer_legal_name=employer.employer_name,
        employer_dba=employer_dba,
    )

    fineos_customer_nbr, fineos_employer_id = fineos.create_or_update_employer(
        employer_request_body, existing_fineos_record
    )

    logger.debug(
        f"{'Created' if is_create else 'Updated'} employer in FINEOS",
        extra={
            "is_create": is_create,
            "internal_employer_id": employer.employer_id,
            "fineos_customer_nbr": fineos_customer_nbr,
            "fineos_employer_id": fineos_employer_id,
        },
    )

    if employer.fineos_employer_id is None:
        logger.info(
            "update fineos_employer_id",
            extra=dict(employer_id=employer.employer_id, fineos_employer_id=fineos_employer_id),
        )
        employer.fineos_employer_id = fineos_employer_id
    elif fineos_employer_id != employer.fineos_employer_id:
        logger.error(
            "unexpected fineos_employer_id",
            extra={
                "internal_employer_id": employer.employer_id,
                "fineos_employer_id": employer.fineos_employer_id,
                "new_fineos_employer_id": fineos_employer_id,
            },
        )
        raise RuntimeError("FINEOS returned different fineos_employer_id than before")
    return fineos_employer_id


def log_create_service_agreement_for_employer(
    employer: Employer, service_agreement_inputs: CreateOrUpdateServiceAgreement
) -> str:
    log_extra = {
        "unlink_leave_plans": service_agreement_inputs.unlink_leave_plans,
        "start_date": service_agreement_inputs.start_date,
        "end_date": service_agreement_inputs.end_date,
        "leave_plans": service_agreement_inputs.leave_plans,
        "absence_management_flag": service_agreement_inputs.absence_management_flag,
        "version": service_agreement_inputs.version,
        "fineos_employer_id": employer.fineos_employer_id,
        "employer_id": employer.employer_id,
    }

    logger.info("Create service agreement for employer attributes", extra=log_extra)

    return ""


def format_previous_leaves_data(
    application: Application, features: Optional[FeaturesConfig] = None
) -> Optional[EFormBody]:
    # Convert from DB models to API models because the API enum models are easier to serialize to strings
    previous_leave_items = list(
        map(
            PreviousLeave.from_orm,
            application.previous_leaves,
        )
    )

    return OtherLeavesV22EFormBuilder.build(previous_leave_items)


def create_eform(
    application: Application, db_session: massgov.pfml.db.Session, eform: EFormBody
) -> None:
    fineos = massgov.pfml.fineos.create_client()
    fineos_web_id = get_or_register_employee_fineos_web_id(fineos, application, db_session)

    fineos_absence_id = application.fineos_absence_id
    if fineos_absence_id is None:
        raise ValueError("Missing absence id")

    fineos.customer_create_eform(fineos_web_id, fineos_absence_id, eform)


def create_other_leaves_and_other_incomes_eforms(
    application: Application,
    db_session: massgov.pfml.db.Session,
    features: Optional[FeaturesConfig] = None,
) -> None:
    log_attributes = get_application_log_attributes(application)

    # Send Other Leaves to FINEOS - this eForm contains previous leaves for the same reason, some other reason, and concurrent leaves.
    if application.previous_leaves:
        eform = format_previous_leaves_data(application, features)
        if eform:
            create_eform(application, db_session, eform)
            logger.info("Created Other Leaves eform", extra=log_attributes)
        else:
            raise ValueError("expected an Other Leaves eform but got None")

    # Send employer benefits and other incomes to fineos
    if application.employer_benefits or application.other_incomes:
        # Convert from DB models to API models because the API enum models are easier to serialize to strings
        other_incomes = map(lambda income: OtherIncome.from_orm(income), application.other_incomes)
        employer_benefits = map(
            lambda benefit: EmployerBenefit.from_orm(benefit), application.employer_benefits
        )

        eform = OtherIncomesV22EFormBuilder.build(employer_benefits, other_incomes)

        create_eform(application, db_session, eform)
        logger.info("Created Other Incomes eform", extra=log_attributes)


def get_absence_periods_from_claim(
    claim: Claim, db_session: massgov.pfml.db.Session
) -> List[FineosAbsencePeriod]:
    absence_id = claim.fineos_absence_id
    if absence_id is None:
        raise Exception("Can't get absence periods from FINEOS - No absence_id for claim")

    fineos = massgov.pfml.fineos.create_client()

    try:
        # Get FINEOS web admin id
        web_id = register_employee_with_claim(fineos, db_session, claim)

        # Get absence periods
        response: AbsenceDetails = fineos.get_absence(web_id, absence_id)
    except FINEOSClientError as ex:
        logger.warning(
            "Unable to get absence periods",
            exc_info=ex,
            extra={"absence_id": absence_id, "absence_case_id": absence_id},
        )
        raise
    return response.absencePeriods or []


def get_absence_period_decisions_from_claim(
    claim: Claim, db_session: massgov.pfml.db.Session
) -> List[AbsencePeriodDecision]:
    absence_id = claim.fineos_absence_id
    if absence_id is None:
        raise Exception("Can't get absence period decisions from FINEOS - No absence_id for claim")

    fineos = massgov.pfml.fineos.create_client()

    try:
        # Get FINEOS web admin id
        web_id = register_employee_with_claim(fineos, db_session, claim)

        # Get absence period decisions
        response: AbsencePeriodDecisions = fineos.get_customer_absence_period_decisions(
            web_id, absence_id
        )

    except FINEOSClientError as ex:
        logger.warning(
            "Unable to get absence period decisions",
            exc_info=ex,
            extra={"absence_id": absence_id, "absence_case_id": absence_id},
        )
        raise

    return response.absencePeriodDecisions or []


def get_absence_periods(
    absence_id: str,
    employee_tax_id: str,
    employer_fein: str,
    db_session: massgov.pfml.db.Session,
    fineos_employer_id: Optional[str] = None,
) -> List[FineosAbsencePeriod]:
    fineos = massgov.pfml.fineos.create_client()

    try:
        web_id = find_or_register_employee(
            fineos, employee_tax_id, employer_fein, db_session, fineos_employer_id
        )
        response: AbsenceDetails = fineos.get_absence(web_id, absence_id)
    except FINEOSClientError as ex:
        logger.warning(
            "Unable to get absence periods",
            exc_info=ex,
            extra={"absence_id": absence_id, "absence_case_id": absence_id},
        )
        raise
    return response.absencePeriods or []


def get_claim_benefits(
    claim: Claim,
    absence_paid_leave_case_number: str,
    web_id: str,
    fineos: massgov.pfml.fineos.AbstractFINEOSClient,
) -> List[BenefitSummary]:
    """
    This retrieves a list of benefits associated with the absence paid leave cases (aplcs)
    through a call to FINEOS (fineos.read_claim_benefit)
    """

    try:
        # benefit details are returned in an array format
        response: List[BenefitSummary] = fineos.read_claim_benefit(
            web_id, absence_paid_leave_case_number
        )
    except FINEOSClientError as ex:
        logger.warning(
            "Unable to get benefit information",
            exc_info=ex,
            extra={
                "absence_id": claim.fineos_absence_id,
                "absence_paid_leave_case_number": absence_paid_leave_case_number,
            },
        )
        raise
    return response


def get_disability_benefit_details(
    absence_paid_leave_case_number: str,
    benefit_id: str,
    web_id: str,
    fineos: massgov.pfml.fineos.AbstractFINEOSClient,
) -> ReadDisabilityBenefitResult:
    """
    This retrieves a list of disability benefit information associated with the
    absence paid leave cases (aplcs) and benefit ids from a call to FINEOS (fineos.read_disability_benefit)
    """

    try:
        response: ReadDisabilityBenefitResult = fineos.read_disability_benefit(
            web_id, absence_paid_leave_case_number, benefit_id
        )
    except FINEOSClientError as ex:
        logger.warning(
            "Unable to get disability benefit information",
            exc_info=ex,
            extra={
                "absence_paid_leave_case_number": absence_paid_leave_case_number,
                "benefit_id": benefit_id,
            },
        )
        raise
    return response


def send_tax_withholding_preference(
    application: Application,
    is_withholding_tax: bool,
    fineos_client: Optional[massgov.pfml.fineos.AbstractFINEOSClient] = None,
) -> None:
    if not fineos_client:
        fineos_client = massgov.pfml.fineos.create_client()

    absence_id = application.fineos_absence_id
    if absence_id is None:
        raise ValueError("Missing absence id")

    absence_id = absence_id.rstrip()
    fineos_client.send_tax_withholding_preference(absence_id, is_withholding_tax)


def submit_change_request(
    change_request: ChangeRequest, claim: Claim, db_session: massgov.pfml.db.Session
) -> ChangeRequest:
    fineos = massgov.pfml.fineos.create_client()

    fineos_web_id = register_employee_with_claim(fineos, db_session, claim)

    absence_id = claim.fineos_absence_id
    if absence_id is None:
        raise Exception("Can't get absence periods from FINEOS - No absence_id for claim")

    fineos_change_request = convert_change_request_to_fineos_model(change_request, claim)
    fineos.create_or_update_leave_period_change_request(
        fineos_web_id, absence_id, fineos_change_request
    )

    change_request.submitted_time = utcnow()

    return change_request


# Throws a pydantic.error_wrappers.ValidationError if startDate or endDate are None
def convert_change_request_to_fineos_model(
    change_request: ChangeRequest, claim: Claim
) -> CreateLeavePeriodsChangeRequestCommand:
    change_request_type = change_request.type

    if change_request.is_withdrawal:
        if claim.leave_requests or claim.absence_periods:
            start_date = claim.claim_start_date
            end_date = claim.claim_end_date
        # claim might not have absence periods immediately after being submitted, use the dates from the application
        else:
            if claim.application:
                start_date = claim.application.start_date
                end_date = claim.application.end_date
            else:
                # Should hopefully never reach this case
                raise ValueError("Claim does not have absence periods or an associated application")

        # A withdrawal removes all dates from a claim
        return CreateLeavePeriodsChangeRequestCommand(
            reason=ReasonRequest(name="Employee Request"),
            additionalNotes="Withdrawal",
            changeRequestPeriods=[
                ChangeRequestPeriodCommand(startDate=start_date, endDate=end_date)
            ],
        )

    if change_request.is_medical_to_bonding:
        dob_str = (
            change_request.date_of_birth.strftime("%m/%d/%Y")
            if change_request.date_of_birth
            else ""
        )
        additional_notes = f"Medical to bonding transition. DOB - {dob_str}"
        return CreateLeavePeriodsChangeRequestCommand(
            reason=ReasonRequest(name="Add time for different Absence Reason"),
            additionalNotes=additional_notes,
            changeRequestPeriods=[
                ChangeRequestPeriodCommand(
                    startDate=change_request.start_date, endDate=change_request.end_date
                )
            ],
        )

    if change_request.is_extension:
        assert claim.claim_end_date

        # extension starts after prior claim ends
        start_date = claim.claim_end_date + datetime.timedelta(days=1)

        return CreateLeavePeriodsChangeRequestCommand(
            reason=ReasonRequest(name="Add time for identical Absence Reason"),
            additionalNotes="Extension",
            changeRequestPeriods=[
                ChangeRequestPeriodCommand(startDate=start_date, endDate=change_request.end_date)
            ],
        )

    if change_request.is_cancellation:
        # In FINEOS, a cancellation means you are removing time.
        # So the date range represents the dates that will be removed from leave
        return CreateLeavePeriodsChangeRequestCommand(
            reason=ReasonRequest(name="Employee Request"),
            additionalNotes="Cancellation",
            changeRequestPeriods=[
                ChangeRequestPeriodCommand(
                    startDate=(
                        change_request.end_date + datetime.timedelta(days=1)
                        if change_request.end_date
                        else None
                    ),
                    endDate=claim.claim_end_date,
                )
            ],
        )
    else:
        raise ValueError(
            f"Unable to convert ChangeRequest to FINEOS model - Unknown type: {change_request_type}"
        )


def create_appeal_case(absence_case_id: str) -> Optional[str]:
    fineos_client = massgov.pfml.fineos.create_client()
    absence_case_id = absence_case_id.rstrip()
    response = fineos_client.create_appeal_case(absence_case_id)
    return response


def create_task(
    fineos_client: massgov.pfml.fineos.AbstractFINEOSClient,
    absence_case_id: str,
    task_type: LkFineosTaskType,
    subject: Optional[str] = None,
    description: Optional[str] = None,
) -> CreateTaskResponse:
    """Create a task for an absence case in FINEOS. Returns a CreateTaskResponse,
    or raises an exception if task creation failed."""

    work_type = WorkTypeIdentifier(Name=task_type.fineos_task_type_description)
    case = CaseIdentifier(CaseNumber=absence_case_id)
    activity_subject = ActivitySubjectIdentifier(GenericSubject=subject) if subject else None

    if type(description) is str and len(description) > FINEOS_MAX_TASK_DESCRIPTION_LENGTH:
        logger.warning(
            "Description exceeded 2000 character max length and has been truncated",
            extra={
                "absence_case_id": absence_case_id,
                "description": description,
                "fineos_task_type": task_type.fineos_task_type_description,
            },
        )
        description = description[:FINEOS_MAX_TASK_DESCRIPTION_LENGTH]

    return fineos_client.create_task(work_type, case, activity_subject, description)


def update_claimant_email_in_fineos(
    db_session: massgov.pfml.db.Session,
    user: User,
    log_extra: Dict,
) -> None:
    """
    This updates a user email address for all applications related to the user on FINEOS

    We make call to fineos.update_customer_contact_details for all fineos_web_id associated with the user.

    Steps:
    - Get all applications related to user
    - Query FINEOSWebIdExt to get contact fineos_web_id
    - Make call to FINEOS in threads to update all applications contact details

    :param db_session: Database session
    :param user: A User object
    :param: log_extra: A Dict object

    :return: None
    """

    logger.info("Updating emails for claimant")
    user_applications: List[Application] = (
        db_session.query(Application)
        .filter(
            Application.user_id == user.user_id,
            Application.submitted_time.isnot(None),
        )
        .order_by(Application.submitted_time.desc())
        .all()
    )
    if not user_applications:
        logger.info("User does not have any submitted applications")
        return None

    # Group submitted applications by unique identifier
    unique_tax_ids: Dict[str, List[Application]] = collections.defaultdict(list)
    for application in user_applications:
        if not application.tax_identifier:
            continue

        unique_tax_ids[application.tax_identifier.tax_identifier].append(application)

    logger.info(f"Updating emails for {len(unique_tax_ids)} unique tax identifier found")
    # Create fineos client
    fineos = massgov.pfml.fineos.create_client()
    try:
        # Maximum number of workers is capped at 200, to avoid too many threads
        max_workers = max_workers = min(len(unique_tax_ids), 200)
        logger.info(f"Executing claimant thread with {max_workers} workers")
        with ThreadPoolExecutor(
            max_workers=max_workers, thread_name_prefix="update_email_claimant"
        ) as executor:
            futures = []
            for tax_identifier, applications in unique_tax_ids.items():
                application = applications[0]
                fineos_web_id = find_or_register_employee(
                    fineos, tax_identifier, application.employer_fein, db_session
                )
                logger.info(
                    "Updating contact details ...",
                    extra={"application_id": application.application_id},
                )

                # This also re-syncs the application's phone number,
                # calling fineos.update_customer_contact_details without the phone number
                # wipes out the existing phone number on the application
                contact_details = build_contact_details(application)

                futures.append(
                    executor.submit(
                        fineos.update_customer_contact_details,
                        fineos_web_id,
                        contact_details,
                    )
                )
            for future in as_completed(futures):
                # Call result() to make sure exceptions bubble up
                try:
                    future.result()
                except Exception as error:
                    logger.warning(
                        "Failed to update claimant email address in FINEOS",
                        extra=log_extra,
                        exc_info=error,
                    )
    except Exception as error:
        logger.warning(
            "Failed to update FINEOS, user applications contact details might not be in-sync",
            extra=log_extra,
            exc_info=error,
        )


def create_child_support_fineos_eform(
    user_id: str,
    ncp_name: str,
    ncp_date_of_birth: date,
    fineos_absence_id: str,
    claimant_child_obligation: ClaimantChildSupportObligation,
    fineos: Optional[massgov.pfml.fineos.AbstractFINEOSClient] = None,
) -> None:
    if fineos is None:
        fineos = massgov.pfml.fineos.create_client()
    if (
        claimant_child_obligation.status_type_id
        == ClaimantChildSupportStatusType.EXPIRED.status_type_id
    ):
        max_weekly_obligation = Decimal(0)
    else:
        max_weekly_obligation = claimant_child_obligation.max_weekly_obligation

    dor_eform_attributes = DorEformAttribute(
        max_weekly_obligation=max_weekly_obligation,
        effective_from=claimant_child_obligation.effective_from,
        effective_to=claimant_child_obligation.effective_to,
        case_open_date=claimant_child_obligation.case_open_date,
        ncp_name=ncp_name,
        ncp_date_of_birth=ncp_date_of_birth,
    )
    dor_child_support_eform = DorChildSupportEFormBuilder.build([dor_eform_attributes])
    fineos.customer_create_eform(user_id, fineos_absence_id, dor_child_support_eform)


def submit_intermittent_leave_episode(
    claim: Claim,
    fineos_absence_id: str,
    intermittent_leave_episode: IntermittentLeaveEpisode,
    db_session: massgov.pfml.db.Session,
) -> List[ActualAbsencePeriodResource]:
    if not intermittent_leave_episode:
        raise ValueError("Episode is none")
    if not intermittent_leave_episode.requested_date:
        raise ValueError("Episode does not have a requested date")
    if not intermittent_leave_episode.episodic_period:
        raise ValueError("Episode period duration is invalid")
    if not intermittent_leave_episode.episodic_period > 0:
        raise ValueError("Episode period duration is invalid")
    if not intermittent_leave_episode.episodic_period_basis:
        raise ValueError("Episode does not have a period basis")

    fineos = massgov.pfml.fineos.create_client()

    element = CreateActualAbsencePeriodCommand(
        actualDate=intermittent_leave_episode.requested_date,
        episodePeriodBasis=EpisodePeriodDurationBasisRequest(
            name=intermittent_leave_episode.episodic_period_basis
        ),
        episodePeriodDuration=intermittent_leave_episode.episodic_period,
        additionalNotes="submitted via portal",
        type=CreateAbsencePeriodTypeRequest(
            name="Incapacity",
        ),
    )

    web_id = register_employee_with_claim(fineos, db_session, claim)

    elements = CreateActualAbsencePeriodCommandElements(elements=[element])
    return fineos.submit_intermittent_leave_episode(web_id, fineos_absence_id, elements)


def get_actual_absence_period_resources_for_claim(
    claim: Claim,
    db_session: massgov.pfml.db.Session,
) -> ActualAbsencePeriodResources:
    fineos = massgov.pfml.fineos.create_client()

    if not claim.fineos_absence_id:
        raise ValueError(f"No fineos_absence_id for claim: {claim.claim_id}")

    web_id = register_employee_with_claim(fineos, db_session, claim)
    absence_id = claim.fineos_absence_id

    return fineos.get_actual_absence_period_resources(web_id, absence_id)


def create_overpayment_recovery(
    overpayment_case_number: str,
    amount_of_recovery: Decimal,
    date_of_recovery: datetime.date,
    recovery_method: OverpaymentRecoveryMethod,
    check_name: Optional[str] = None,
    check_number: Optional[str] = None,
    fineos_client: Optional[massgov.pfml.fineos.AbstractFINEOSClient] = None,
) -> str:
    if not fineos_client:
        fineos_client = massgov.pfml.fineos.create_client()

    overpayment_recovery = OverpaymentRecovery(
        overpayment_case_number=overpayment_case_number,
        amount_of_recovery=amount_of_recovery,
        date_of_recovery=date_of_recovery,
        recovery_method=recovery_method,
        check_name=check_name,
        check_number=check_number,
    )

    return fineos_client.create_overpayment_recovery(overpayment_recovery)
