from datetime import date
from typing import List

import massgov.pfml.db as db
from massgov.pfml.api.services.insurance_providers import (
    get_insurance_provider_from_db,
    get_insurance_providers_plan_from_db,
)
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationErrorList,
)
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication
from massgov.pfml.util.datetime.quarter import Quarter


def get_column_required_issues(
    employer_exemption_application: EmployerExemptionApplication,
) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()
    previously_encountered_error_fields = set()

    for col in employer_exemption_application.get_columns_required_for_presubmit_validation():

        if EmployerExemptionApplication.is_phone_db_model_foreign_key(col):
            related_phone_object_name = EmployerExemptionApplication.get_related_phone_object_name(
                col
            )

            if related_phone_object_name is None:
                raise ValueError(f"'{col}' related_phone_object_name is 'None' expected str")

            phone = getattr(
                employer_exemption_application,
                related_phone_object_name,
            )
            val = phone.phone_number if phone is not None else None
        else:
            val = getattr(employer_exemption_application, col)

        if val is None:
            field = EmployerExemptionApplication.get_mapped_to_api_field_name(col)

            # columns may map to the same api field (eg.
            #   has_family_exemption column maps to exemption_type
            #   has_medical_exemption column maps to exemption_type
            #
            # if duplicate mapped-to-api-field names exist, exclude all but the
            # first error. including duplicates will result in duplicate error
            # messages displayed in the front end
            if field not in previously_encountered_error_fields:
                issues.add_validation_error(
                    type=IssueType.required,
                    message=f"{field} is required",
                    field=field,
                )

                previously_encountered_error_fields.add(field)

    return issues.get_errors()


def get_purchased_private_plan_issues(
    employer_exemption_application: EmployerExemptionApplication,
    db_session: db.Session,
) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()

    if not employer_exemption_application.is_self_insured_plan:
        insurance_provider_id = employer_exemption_application.insurance_provider_id
        insurance_plan_id = employer_exemption_application.insurance_plan_id

        if insurance_provider_id:
            db_insurance_provider = get_insurance_provider_from_db(
                db_session, insurance_provider_id
            )

            if db_insurance_provider is None:
                field = EmployerExemptionApplication.get_mapped_to_api_field_name(
                    "insurance_provider_id"
                )
                issues.add_validation_error(
                    type=IssueType.invalid,
                    message=f"{field} is invalid",
                    field=field,
                )

        if insurance_plan_id:
            db_insurance_plan = get_insurance_providers_plan_from_db(
                db_session, insurance_provider_id, insurance_plan_id
            )

            if db_insurance_plan is None:
                field = EmployerExemptionApplication.get_mapped_to_api_field_name("insurance_plan")
                issues.add_validation_error(
                    type=IssueType.invalid,
                    message=f"{field} is invalid",
                    field=field,
                )

    return issues.get_errors()


def get_date_range_issues(
    employer_exemption_application: EmployerExemptionApplication,
) -> List[ValidationErrorDetail]:
    """Takes in an employer exemption application and outputs any date range validation issues."""
    issues = ValidationErrorList()

    # The PFML program began on Jan 1st, 2021 which is the earliest possible start_date.
    min_plan_start_date = date(2019, 10, 1)

    # Employers may not file exemption requests later than the first day of the next quarter.
    max_plan_start_date = Quarter.first_day_of_next_quarter(date.today())

    if employer_exemption_application.insurance_plan_effective_at:
        plan_start_date = employer_exemption_application.insurance_plan_effective_at
        field = EmployerExemptionApplication.get_mapped_to_api_field_name(
            "insurance_plan_effective_at"
        )
        if plan_start_date > max_plan_start_date:
            issues.add_validation_error(
                type=IssueType.maximum,
                message=f"{field} must be on or before {max_plan_start_date.isoformat()}",
                field=field,
                extra={
                    "max_plan_start_date": max_plan_start_date.strftime("%B %d, %Y"),
                },
            )

        elif plan_start_date < min_plan_start_date:
            issues.add_validation_error(
                type=IssueType.minimum,
                message=f"{field} must be on or after {min_plan_start_date.isoformat()}",
                field=field,
                extra={
                    "max_plan_start_date": max_plan_start_date.strftime("%B %d, %Y"),
                },
            )

        if employer_exemption_application.insurance_plan_expires_at:
            anniversary_date = employer_exemption_application.insurance_plan_expires_at
            anniversary_field = EmployerExemptionApplication.get_mapped_to_api_field_name(
                "insurance_plan_expires_at"
            )
            if anniversary_date <= plan_start_date:
                issues.add_validation_error(
                    type=IssueType.minimum,
                    message=f"{anniversary_field} must be on or after {plan_start_date.isoformat()}",
                    field=anniversary_field,
                )

    return issues.get_errors()


def get_application_submit_issues(
    employer_exemption_application: EmployerExemptionApplication,
    db_session: db.Session,
) -> List[ValidationErrorDetail]:
    """Takes in an employer exemption application and outputs any validation issues.
    These issues are either fields that are always required for an exemption or fields that are conditionally required based on previous input.
    """

    # Only run these validations if the application hasn't already been submitted. This
    # prevents warnings from showing in the response for rules added after the application
    # was submitted, which would cause a Portal user's Checklist to revert back to showing
    # steps as incomplete, and they wouldn't be able to fix this.
    if employer_exemption_application.submitted_at:
        return []

    issues = ValidationErrorList()
    issues += get_column_required_issues(employer_exemption_application)
    issues += get_purchased_private_plan_issues(employer_exemption_application, db_session)
    issues += get_date_range_issues(employer_exemption_application)

    return issues.get_errors()
