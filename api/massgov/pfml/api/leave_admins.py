from io import String<PERSON>
from typing import Dict

import flask
import newrelic.agent
from werkzeug.exceptions import BadRequest, ServiceUnavailable

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
import massgov.pfml.util.logging
from massgov.pfml.api.authorization.flask import EDIT, READ, ensure, requires
from massgov.pfml.api.models.phones.common import Phone
from massgov.pfml.api.models.users.requests import (
    UserLeaveAdminAddRequest,
    UserLeaveAdminRequest,
    UserUpdateRequest,
)
from massgov.pfml.api.models.users.responses import (
    UserLeaveAdminResponseWithUser,
    UserLeaveAdminSearchResult,
)
from massgov.pfml.api.services.administrator_fineos_actions import update_leave_admin_with_fineos
from massgov.pfml.api.services.claim_export import ClaimExport
from massgov.pfml.api.services.leave_admins import (
    add_new_verified_leave_admin,
    get_leave_admin_by_leave_admin_id,
    get_leave_admins_by_employer_id,
    get_leave_admins_by_user_id,
    get_pending_leave_admins_by_employer_id,
    handle_add_emails,
    handle_deactivation_emails,
    record_leave_admin_action,
    record_leave_admin_action_add,
)
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.api.validation.leave_admins_rules import validate_not_in_organization
from massgov.pfml.api.validation.user_rules import validate_user_employer_role
from massgov.pfml.db.lookup_data.employees import UserLeaveAdministratorActionType
from massgov.pfml.db.models.employees import Employer, User
from massgov.pfml.util.duration import Timer
from massgov.pfml.util.http.headers import StandardMediaType
from massgov.pfml.util.logging.leave_admins import get_leave_admin_search_metrics
from massgov.pfml.util.sqlalchemy import get_or_404

logger = massgov.pfml.util.logging.get_logger(__name__)


def leave_admins_search(body):
    """
    POST /v1/leave-admins/search
    """
    request_body = parse_request_body(UserLeaveAdminRequest, body)
    ensure(READ, request_body.to_db_model())

    user_id = request_body.user_id
    employer_id = request_body.employer_id
    current_user = app.current_user()

    with app.db_session() as db_session:
        leave_admins: UserLeaveAdminSearchResult = []
        pending_leave_admins = []

        if user_id:
            leave_admins = get_leave_admins_by_user_id(db_session, user_id)
        elif employer_id:
            active_leave_admins = get_leave_admins_by_employer_id(
                db_session, employer_id, current_user
            )

            pending_leave_admins = get_pending_leave_admins_by_employer_id(db_session, employer_id)
            leave_admins = [
                *active_leave_admins,
                *pending_leave_admins,
            ]

    leave_admins_data = [la.normalized_dict() for la in leave_admins]

    logger.info(
        "leave_admins_search - success",
        extra={
            "request_user_id": user_id,
            "request_employer_id": employer_id,
            **get_leave_admin_search_metrics(leave_admins, pending_leave_admins),
        },
    )
    return response_util.success_response(
        data=leave_admins_data, message="Successful leave_admins_search", status_code=200
    ).to_api_response()


def leave_admins_add(body):
    """
    POST /v1/leave-admins/add

    Allows the current user to invite a new leave administrator into an organization. The recipient does not need to have an account at the time of invite.
    """
    request_body = parse_request_body(UserLeaveAdminAddRequest, body)
    email_address = request_body.email_address
    employer_id = request_body.employer_id

    current_user = app.current_user()
    log_attributes: Dict = {
        "employer_id": employer_id,
    }

    with app.db_session() as db_session:
        existing_employer = get_or_404(db_session, Employer, employer_id)
        ensure(EDIT, request_body.to_db_model())

        # Ensure the recipient is allowed to be added to the organization
        recipient = db_session.query(User).filter(User.email_address == email_address).one_or_none()
        validate_not_in_organization(db_session, email_address, employer_id)
        validate_user_employer_role(recipient)

        user_leave_admin_action = record_leave_admin_action_add(
            db_session, email_address, employer_id, current_user
        )

        if recipient:
            log_attributes |= {"recipient.user_id": recipient.user_id}
            """
            If the recipient does not have a Portal account at the time of invite, this method will be called during signup.
            """
            logger.info(
                "Recipient already has an account — adding new leave administrator",
                extra=log_attributes,
            )
            add_new_verified_leave_admin(
                db_session, recipient, existing_employer, user_leave_admin_action
            )
        else:
            logger.warning(
                "Manual verification warning - Recipient does not have an account - skipping registration",
                extra=log_attributes,
            )

        try:
            handle_add_emails(db_session, email_address, existing_employer, current_user)
        except Exception as e:
            # Catch all errors here since we've already technically recorded the LA addition.
            log_attributes |= {"error.class": type(e).__name__}
            logger.exception(str(e), extra=log_attributes)
            newrelic.agent.notice_error(attributes=log_attributes)

    logger.info(
        "Leave admin add - success",
        extra=log_attributes,
    )
    return response_util.success_response(
        message="Successfully added leave admin user",
        data={},
        status_code=201,
    ).to_api_response()


def deactivate_leave_admin(user_leave_administrator_id):
    """
    POST /v1/leave-admins/{user_leave_administrator_id}/deactivate
    """
    current_user = app.current_user()
    log_attributes = {"user_leave_administrator_id": user_leave_administrator_id}

    with app.db_session() as db_session:
        leave_admin_to_deactivate = get_leave_admin_by_leave_admin_id(
            db_session, user_leave_administrator_id
        )

        if not leave_admin_to_deactivate:
            logger.warning(
                f"deactivate_leave_admin - leave admin was not found for ID {user_leave_administrator_id}",
                extra=log_attributes,
            )
            return response_util.error_response(
                BadRequest, "User Leave Admin Id not found", errors=[]
            ).to_api_response()

        ensure(EDIT, leave_admin_to_deactivate)

    if leave_admin_to_deactivate.deactivated:
        logger.info(
            "deactivate_leave_admin - Leave administrator is already deactivated",
            extra=log_attributes,
        )
    else:
        user = leave_admin_to_deactivate.user

        if user.user_id == current_user.user_id:
            logger.info(
                "Leave administrator is removing self from organization", extra=log_attributes
            )
        else:
            logger.info(
                "Leave administrator is removing other individual from organization",
                extra=log_attributes,
            )

        leave_admin_to_deactivate_phone: Phone = Phone(
            phone_number=leave_admin_to_deactivate.user.phone_number,
            extension=leave_admin_to_deactivate.user.phone_extension,
        )
        user_update_request = UserUpdateRequest(
            first_name=leave_admin_to_deactivate.user.first_name,
            last_name=leave_admin_to_deactivate.user.last_name,
            phone=leave_admin_to_deactivate_phone,
        )
        fineos_error_code, fineos_error_message = update_leave_admin_with_fineos(
            user, user_update_request, leave_admin_to_deactivate, True
        )

        if fineos_error_code:
            logger.error(
                "deactivate_leave_admin - Failed to deactivate leave admin user, fineos error.",
                extra={
                    **log_attributes,
                    "fineos_error_code": fineos_error_code,
                    "fineos_error_message": fineos_error_message,
                    "employer_id": leave_admin_to_deactivate.employer_id,
                    "user_id": leave_admin_to_deactivate.user_id,
                },
            )
            return response_util.error_response(
                status_code=ServiceUnavailable,
                errors=[],
                message="Fineos Leave admin deactivation failed.",
            ).to_api_response()

        with app.db_session() as db_session:
            record_leave_admin_action(
                db_session,
                current_user.user_id,
                leave_admin_to_deactivate,
                UserLeaveAdministratorActionType.DEACTIVATE.user_leave_administrator_action_type_id,
            )
            logger.info(
                "deactivate_leave_admin - Recorded leave admin deactivation", extra=log_attributes
            )

            try:
                handle_deactivation_emails(db_session, leave_admin_to_deactivate, current_user)
            except Exception as e:
                # Catch all errors here since we've already technically recorded the LA deactivation.
                logger.exception(str(e), extra={**log_attributes, "error.class": type(e).__name__})
                newrelic.agent.notice_error(
                    attributes={**log_attributes, "error.class": type(e).__name__}
                )

    logger.info(
        "Deactivate leave admin - success",
        extra={
            "employer_id": leave_admin_to_deactivate.employer_id,
        },
    )
    return response_util.success_response(
        message="Successfully deactivated leave administrator",
        data=UserLeaveAdminResponseWithUser.from_orm(leave_admin_to_deactivate).normalized_dict(),
        status_code=200,
    ).to_api_response()


@requires(READ, "EMPLOYER_API")
def download_csv():
    current_user = app.current_user()

    ski_max_csv_rows = app.get_app_config().leave_admin_csv_max_rows

    log_attributes = {"user_id": current_user.user_id, "max_ski_csv_rows": ski_max_csv_rows}

    # The number of applications displayed on the portal is passed as a query parameter.
    # Used for logging to compare against the number of applications in the CSV.
    portal_applications_count = flask.request.args.get("portal_applications_count", "N/A")

    with Timer() as timer:
        logger.info("Leave admin CSV download request starting", extra=log_attributes)
        with app.db_session() as db_session:
            claim_export = ClaimExport(db_session, current_user.user_id, max_rows=ski_max_csv_rows)

        with StringIO() as output:
            output = StringIO(output.read())
            claim_export.create_csv_content(output)
            logger.info("Leave admin CSV download retrieved data", extra=log_attributes)
            csv_string = output.getvalue()
            file_size_bytes = len(csv_string.encode("utf-8"))

        claim_export.finalize_log()

    logger.info(
        "Leave admin CSV download completed",
        extra={
            **log_attributes,
            "elapsed_seconds": timer.elapsed_s,
            "elapsed_ms": timer.elapsed_ms,
            "csv_file_size_bytes": file_size_bytes,
            "portal_applications_count": portal_applications_count,
            "csv_applications_count": claim_export.metrics.get("application_count", "N/A"),
            "csv_absence_period_count": claim_export.metrics.get("absence_period_count", "N/A"),
        },
    )

    return flask.Response(
        csv_string,
        content_type=StandardMediaType.CSV,
        headers={"Content-Disposition": "attachment; filename=leave_admin_applications.csv"},
    )
