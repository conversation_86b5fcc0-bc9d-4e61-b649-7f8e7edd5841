import base64
import os
from typing import Any, Dict, Optional
from uuid import UUID

from connexion.lifecycle import ConnexionResponse
from flask import Response, request
from sqlalchemy import select
from sqlalchemy.orm import joinedload
from werkzeug.exceptions import BadRequest, Forbidden, NotFound, ServiceUnavailable

import massgov.pfml.api.app as app
import massgov.pfml.api.services.applications as applications_service
import massgov.pfml.api.util.response as response_util
import massgov.pfml.api.validation.application_rules as application_rules
import massgov.pfml.db as db
import massgov.pfml.services.documents as documents_service
import massgov.pfml.util.concurrent
import massgov.pfml.util.logging
from massgov.pfml.api.authentication import get_bearer_token_from_header
from massgov.pfml.api.authorization.flask import CREATE, EDIT, READ, ensure, valid_access_query
from massgov.pfml.api.exceptions import ClaimWithdrawn, UploadDocumentError
from massgov.pfml.api.models.applications.common import MmgIdvStatus as MmgIdvStatusEnum
from massgov.pfml.api.models.applications.common import PaymentMethod
from massgov.pfml.api.models.applications.requests import (
    ApplicationRequestBody,
    PaymentPreferenceRequestBody,
    TaxWithholdingPreferenceRequestBody,
)
from massgov.pfml.api.models.applications.responses import (
    ApplicationResponse,
    get_fineos_submit_issues_response,
)
from massgov.pfml.api.models.documents.requests import DocumentRequestBody
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.api.services.applications import ApplicationSplit, get_application_split
from massgov.pfml.api.services.fineos_actions import (
    download_document,
    get_customer_payment_preference,
    send_tax_withholding_preference,
    submit_customer_payment_preference,
    update_customer_payment_preference,
)
from massgov.pfml.api.util.paginate.paginator import (
    PaginationAPIContext,
    ordered_query_for_api_context,
    page_for_api_context,
)
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.api.validation.employment_validator import (
    get_contributing_employer_or_employee_issue,
)
from massgov.pfml.api.validation.exceptions import (
    IssueRule,
    IssueType,
    ValidationErrorDetail,
    ValidationException,
)
from massgov.pfml.db.lookup_data.applications import MmgIdvStatus
from massgov.pfml.db.lookup_data.payments import PrepaidRegistrationStatus
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import Employee
from massgov.pfml.db.models.payments import ClaimantPrepaidRegistration
from massgov.pfml.fineos.exception import FINEOSClientError
from massgov.pfml.fineos.models.customer_api import Base64EncodedFileData
from massgov.pfml.pdf_api.exception import PDFClientError
from massgov.pfml.services import applications as application_service
from massgov.pfml.services.documents import DocumentRequirementService
from massgov.pfml.services.documents.get_document_service import GetDocumentService
from massgov.pfml.util.logging.applications import (
    get_app_progress_attributes,
    get_application_log_attributes,
)
from massgov.pfml.util.sqlalchemy import get_or_404

logger = massgov.pfml.util.logging.get_logger(__name__)


def application_get(application_id):
    with app.db_session() as db_session:
        existing_application = get_or_404(db_session, Application, application_id)
        ensure(READ, existing_application)
        application_response = ApplicationResponse.from_orm(existing_application)
        issues = application_rules.get_application_submit_issues(
            existing_application,
            db_session,
            {"application.application_id": existing_application.application_id},
            app.get_features_config(),
        )

    return response_util.success_response(
        message="Successfully retrieved application",
        data=application_response.dict(),
        warnings=issues,
    ).to_api_response()


def applications_get():
    with PaginationAPIContext(Application, request=request) as pagination_context:
        with app.db_session() as db_session:
            application_query = valid_access_query(db_session, READ, Application).options(
                joinedload(Application.employee),
                joinedload(Application.employer),
            )
            ordered_query = ordered_query_for_api_context(pagination_context, application_query)
            page = page_for_api_context(pagination_context, ordered_query)

    return response_util.paginated_success_response(
        message="Successfully retrieved applications",
        model=ApplicationResponse,
        page=page,
        context=pagination_context,
        status_code=200,
    ).to_api_response()


def applications_start():
    application = Application()

    ensure(CREATE, application)

    current_user = app.current_user()

    application.user = current_user

    with app.db_session() as db_session:
        employee_ids = (
            db_session.execute(
                select(Employee.employee_id)
                .join(Application.employee)
                .where(Application.user_id == current_user.user_id)
                .distinct()
            )
            .scalars()
            .all()
        )

        employees_with_invalid_benefit_years = (
            db_session.query(Employee.employee_id)
            .filter(Employee.employee_id.in_(employee_ids))
            .filter(
                Employee.invalid_benefit_years_since.isnot(None),
            )
            .distinct()
            .count()
        )

        disable_overlapping_benefit_year_claim_creation = (
            app.get_features_config().application_intake.disable_overlapping_benefit_year_claim_creation
        )

        # Do not let the user proceed if user has only ever applied for a single EE and that EE has an invalid benefit year
        should_block_due_to_invalid_benefit_year = (
            len(employee_ids) == 1 and employees_with_invalid_benefit_years == 1
        )

        if (
            disable_overlapping_benefit_year_claim_creation
            and should_block_due_to_invalid_benefit_year
        ):
            invalid_benefit_year_issue = ValidationErrorDetail(
                type=IssueType.employee_has_invalid_benefit_year,
                message="The employee with matching tax identifier has an invalid benefit year.",
            )

            logger.info(invalid_benefit_year_issue.message)
            return response_util.error_response(
                status_code=BadRequest,
                message=invalid_benefit_year_issue.message,
                errors=[invalid_benefit_year_issue],
                data={},
            ).to_api_response()
        else:
            db_session.add(application)

    log_attributes = get_application_log_attributes(application)
    logger.info("applications_start success", extra=log_attributes)

    return response_util.success_response(
        message="Successfully created application",
        data=ApplicationResponse.from_orm(application).dict(),
        status_code=201,
    ).to_api_response()


def applications_update(application_id, body):
    with app.db_session() as db_session:
        existing_application = get_or_404(db_session, Application, application_id)

    ensure(EDIT, existing_application)

    # The presence of a submitted_time indicates that the application has already been submitted.
    if existing_application.submitted_time:
        log_attributes = get_application_log_attributes(existing_application)
        logger.info(
            "applications_update failure - application already submitted", extra=log_attributes
        )
        message = "Application {} could not be updated. Application already submitted on {}".format(
            existing_application.application_id, existing_application.submitted_time.strftime("%x")
        )
        return response_util.error_response(
            status_code=Forbidden,
            message=message,
            errors=[ValidationErrorDetail(type=IssueType.exists, field="claim", message=message)],
            data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
        ).to_api_response()

    if existing_application.nbr_of_retries >= app.get_app_config().limit_ssn_fein_max_attempts:
        message = "Application {} could not be updated. Maximum number of attempts reached.".format(
            existing_application.application_id
        )
        return response_util.error_response(
            status_code=BadRequest,
            message=message,
            errors=[
                ValidationErrorDetail(
                    type=IssueType.maximum,
                    rule=IssueRule.max_ssn_fein_update_attempts,
                    message=message,
                )
            ],
            data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
        ).to_api_response()

    # Set log attributes to the updated attributes rather than the previous attributes
    log_attributes = get_application_log_attributes(existing_application)

    error_response = error_response_if_invalid_benefit_year_issue(
        existing_application, log_attributes
    )
    if error_response:
        return error_response

    updated_body = applications_service.remove_masked_fields_from_request(
        body, existing_application
    )

    application_request = parse_request_body(ApplicationRequestBody, updated_body)

    _log_app_progress(existing_application, application_request)

    previous_fein = existing_application.employer_fein
    previous_tax_identifier = None
    if existing_application.tax_identifier:
        previous_tax_identifier = existing_application.tax_identifier.tax_identifier

    has_no_existing_user_profile_fields = (
        existing_application.fields_to_use_from_user_profile is None
    )

    # If both MMG IDV and backend invalidation are enabled, check if the request invalidates the application's IDV status.
    # If so, update the application's IDV status to "unverified" to complete the invalidation.
    idv_flags = app.get_features_config().universal_profile
    if idv_flags.enable_mmg_idv and idv_flags.enable_backend_invalidation:
        if applications_service.is_request_idv_invalidating(updated_body, existing_application):
            applications_service.update_from_application_idv_status(
                db_session, MmgIdvStatusEnum.unverified, existing_application
            )

    with app.db_session() as db_session:
        applications_service.update_from_request(
            db_session,
            application_request,
            existing_application,
        )

    if has_no_existing_user_profile_fields and application_request.fields_to_use_from_user_profile:
        with app.db_session() as db_session:
            mmg_auth_token = get_bearer_token_from_header()
            client = massgov.pfml.my_mass_gov.client.get_client()
            applications_service.update_from_user_profile(
                db_session,
                client,
                mmg_auth_token,
                existing_application,
                application_request.fields_to_use_from_user_profile,
            )

    # When returning from the IDV flow, set the application's IDV status
    is_idv_return = (
        existing_application.mmg_idv_status is None and body.get("mmg_idv_status", "") is None
    )
    if idv_flags.enable_mmg_idv and is_idv_return:
        with app.db_session() as db_session:
            mmg_auth_token = get_bearer_token_from_header()
            client = massgov.pfml.my_mass_gov.client.get_client()
            applications_service.update_from_user_idv_status(
                db_session,
                client,
                mmg_auth_token,
                existing_application,
            )

    # refresh log attributes, as the application has changed after update
    log_attributes = get_application_log_attributes(existing_application)

    # Check for an error response again
    error_response = error_response_if_invalid_benefit_year_issue(
        existing_application, log_attributes
    )
    if error_response:
        return error_response

    issues = application_rules.get_application_submit_issues(
        existing_application,
        db_session,
        log_attributes,
        app.get_features_config(),
    )

    if application_rules.contains_employer_issue(issues):
        # If either SSN or FEIN have been recently updated
        # and an "employer_issue" occurred, it counts as an attempt
        if (
            application_request.tax_identifier is not None
            and previous_tax_identifier != application_request.tax_identifier
        ) or (
            application_request.employer_fein is not None
            and previous_fein != application_request.employer_fein
        ):
            existing_application.nbr_of_retries += 1
            with app.db_session() as db_session:
                db_session.add(existing_application)
                db_session.commit()
                db_session.refresh(existing_application)

            log_attributes = get_application_log_attributes(existing_application)
            logger.info("User attempted new combination of SSN/FEIN", extra=log_attributes)

    logger.info("applications_update success", extra=log_attributes)

    return response_util.success_response(
        message="Application updated without errors.",
        data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
        warnings=issues,
    ).to_api_response()


def _log_app_progress(existing_app: Application, app_updates: ApplicationRequestBody) -> None:
    """Log application progress, eg starting or completing a section"""
    app_progress_attributes = get_app_progress_attributes(existing_app, app_updates)

    if app_progress_attributes:
        # the user has made progress on their application - log it
        log_attributes = get_application_log_attributes(existing_app)
        log_attributes.update(app_progress_attributes)
        logger.info("Application progress", extra=log_attributes)

    return


def get_pdf_submit_issues_response(existing_application):
    # These errors are usually caught in our error handler and raised with a "pdf_client" issue type.
    # Once the Portal behavior is changed to handle that type, we can remove this special case.
    logger.error(
        "applications_submit failure - pdf submit",
        extra=get_application_log_attributes(existing_application),
    )

    return response_util.error_response(
        status_code=ServiceUnavailable,
        message="Application {} could not be submitted, try again later".format(
            existing_application.application_id
        ),
        errors=[],
        data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
    ).to_api_response()


def _will_submit_to_manual_review(
    application: Application, issues: list[ValidationErrorDetail]
) -> bool:
    has_employer_issue = application_rules.contains_employer_issue(issues)
    return (
        len(issues) == 1
        and has_employer_issue
        and application.additional_user_not_found_info is not None
    )


def _log_if_app_spans_benefit_years(
    existing_application: Application,
    db_session: db.Session,
    log_attributes: Dict[str, Optional[str]],
) -> Optional[ApplicationSplit]:
    """Helper for getting ApplicationSplit"""

    application_split = get_application_split(existing_application, db_session)
    logger.info(
        f"application would have been split: {application_split is not None}",
        extra={
            **log_attributes,
        },
    )

    return application_split


def applications_submit(application_id):
    with app.db_session() as db_session:
        current_user = app.current_user()
        existing_application = get_or_404(db_session, Application, application_id)

        ensure(EDIT, existing_application)

        log_attributes = get_application_log_attributes(existing_application)

        app_config_enable_application_fraud_check = (
            app.get_app_config().enable_application_fraud_check
        )
        features_config_enable_application_fraud_check = (
            app.get_features_config().application_intake.enable_application_fraud_check
        )

        logger.info(
            "Application fraud check",
            extra={
                "app_config_enable_application_fraud_check": app_config_enable_application_fraud_check,
                "features_config_enable_application_fraud_check": features_config_enable_application_fraud_check,
            },
        )
        if (
            app_config_enable_application_fraud_check
            or features_config_enable_application_fraud_check
        ):
            # Meta issues are problems with the application beyond just the model itself
            # Includes checks for a potentially fraudulent application.
            meta_issues = application_rules.validate_application_state(
                existing_application, db_session
            )
            if meta_issues:
                logger.info(
                    "applications_submit failure - application flagged for fraud",
                    extra=log_attributes,
                )
                return response_util.error_response(
                    status_code=Forbidden,
                    message="Application unable to be submitted by current user",
                    errors=meta_issues,
                    data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
                ).to_api_response()

        # The presence of a submitted_time indicates that the application has already been submitted.
        submitted_time = existing_application.submitted_time or (
            existing_application.additional_user_not_found_info is not None
            and existing_application.additional_user_not_found_info.submitted_time
        )
        if submitted_time:
            logger.info(
                "applications_submit failure - application already submitted",
                extra=log_attributes,
            )
            message = (
                "Application {} could not be submitted. Application already submitted on {}".format(
                    existing_application.application_id,
                    submitted_time.strftime("%x"),
                )
            )
            return response_util.error_response(
                status_code=Forbidden,
                message=message,
                errors=[
                    ValidationErrorDetail(type=IssueType.exists, field="claim", message=message)
                ],
                data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
            ).to_api_response()

        issues = application_rules.get_application_submit_issues(
            existing_application,
            db_session,
            log_attributes,
            app.get_features_config(),
        )

        if _will_submit_to_manual_review(existing_application, issues):
            try:
                application_service.submit_user_not_found(
                    db_session,
                    existing_application,
                    issues,
                )
            except Exception as e:
                if isinstance(e, FINEOSClientError):
                    return get_fineos_submit_issues_response(e, existing_application)
                if isinstance(e, PDFClientError):
                    return get_pdf_submit_issues_response(existing_application)
                raise e
        else:
            if issues:
                logger.info(
                    "applications_submit failure - application failed validation",
                    extra=log_attributes,
                )
                return response_util.error_response(
                    status_code=BadRequest,
                    message="Application is not valid for submission",
                    errors=issues,
                    data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
                ).to_api_response()

            # Addresses an issue where an application requiring manual review
            # to match employer and employee has payment preference and
            # tax withholding set prior to submitting the application,
            # as these fields are asked for to complete the manual review
            # process. A claimant could correct their FEIN and SSN to submit
            # the application with these fields set before they have been
            # properly handled in part two of the claimant flow.
            existing_application.is_withholding_tax = None

            if existing_application.payment_preference:
                db_session.delete(existing_application.payment_preference)
                db_session.commit()
                db_session.refresh(existing_application)

            _log_if_app_spans_benefit_years(existing_application, db_session, log_attributes)

            with massgov.pfml.util.concurrent.ThreadPoolFlaskContextExecutor(
                max_workers=8, thread_name_prefix="submit"
            ) as executor:
                log_attributes["application.split"] = "not split"
                try:
                    application_service.submit(
                        db_session,
                        [existing_application],
                        current_user,
                        executor,
                        features_config=app.get_features_config(),
                    )
                except Exception as e:
                    logger.error(
                        "submit exception %s: %s",
                        e.__class__.__name__,
                        e,
                        extra=log_attributes,
                    )
                    if isinstance(e, FINEOSClientError):
                        return get_fineos_submit_issues_response(e, existing_application)
                    raise e

    db_session.refresh(existing_application)
    log_attributes = get_application_log_attributes(existing_application)
    logger.info("applications_submit success", extra=log_attributes)

    if (
        app.get_features_config().universal_profile.enable_mmg_idv
        and existing_application.mmg_idv_status_id is MmgIdvStatus.UNVERIFIED.id
    ):
        applications_service.log_mmg_idv_to_application_changes(
            existing_application,
            client=massgov.pfml.my_mass_gov.client.get_client(),
            mmg_auth_token=get_bearer_token_from_header(),
        )

    return response_util.success_response(
        message="Application {} submitted without errors".format(
            existing_application.application_id
        ),
        data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
        status_code=201,
    ).to_api_response()


def is_payment_preference_set_to_prepaid_card(
    application: Application, db_session: db.Session
) -> bool:
    payment_preferences = get_customer_payment_preference(application, db_session)

    if payment_preferences and payment_preferences.elements:
        for payment_preference in payment_preferences.elements:
            if (
                payment_preference.default
                and payment_preference.paymentMethod
                and payment_preference.paymentMethod.name == PaymentMethod.prepaid_card
            ):
                return True
    return False


def applications_complete(application_id: UUID, body: dict | None) -> ConnexionResponse:
    with app.db_session() as db_session:
        existing_application = get_or_404(db_session, Application, application_id)

        # For the IDV Pilot, verified applicants with a State ID have not uploaded ID documents
        # We need to mock an upload of the ID documents so that they can be marked as received and satisfied in FINEOS
        is_idv_enabled = app.get_features_config().universal_profile.enable_mmg_idv
        if is_idv_enabled:
            applications_service.upload_placeholder_idv_id(existing_application)

        # if no request body for /complete-application, assume doc deferral is False
        certificate_document_deferred = (
            body.get("certificate_document_deferred", False) if body else False
        )

        ensure(EDIT, existing_application)

        log_attributes = get_application_log_attributes(existing_application)
        log_attributes["certificate_document_deferred"] = str(certificate_document_deferred)

        error_response = error_response_if_invalid_benefit_year_issue(
            existing_application, log_attributes
        )
        if error_response:
            return error_response

        issues = application_rules.get_application_complete_issues(
            existing_application, db_session, certificate_document_deferred
        )
        if issues:
            logger.info(
                "applications_complete failure - application failed validation",
                extra=log_attributes,
            )
            return response_util.error_response(
                status_code=BadRequest,
                message="Application is not valid for completion",
                errors=issues,
                data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
            ).to_api_response()

        enable_prepaid_impact_payments = (
            os.environ.get("ENABLE_PREPAID_IMPACT_PAYMENTS", "0") == "1"
        )
        if enable_prepaid_impact_payments:
            # PFMLPB-19551: If payment preference is set to prepaid card, we need to add the applicant
            # to prepaid card registration queue
            prepaid_card_flag = is_payment_preference_set_to_prepaid_card(
                existing_application, db_session
            )
            if prepaid_card_flag:
                try:
                    existing_record = (
                        db_session.query(ClaimantPrepaidRegistration)
                        .filter(
                            ClaimantPrepaidRegistration.employee_id
                            == existing_application.employee_id,
                            ClaimantPrepaidRegistration.prepaid_registration_status_id
                            != PrepaidRegistrationStatus.INACTIVE.prepaid_registration_status_id,
                        )
                        .first()
                    )

                    if existing_record:
                        # If there is an existing record for employee, create a new record with status UPDATE_FROM_PRIOR
                        # This record will later marked as Inactive after updating the account details to Fineos
                        if (
                            existing_record.prepaid_registration_status_id
                            == PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id
                        ):
                            claimant_prepaid_registration = ClaimantPrepaidRegistration(
                                employee_id=existing_application.employee_id,
                                prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE_FROM_PRIOR.prepaid_registration_status_id,
                                fineos_absence_id=existing_application.fineos_absence_id,
                                account_number=existing_record.account_number,
                                routing_number=existing_record.routing_number,
                            )
                            db_session.add(claimant_prepaid_registration)
                            db_session.commit()

                        if existing_record.prepaid_registration_status_id not in (
                            PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id,
                            PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
                            PrepaidRegistrationStatus.UPDATE_FROM_PRIOR.prepaid_registration_status_id,
                        ):
                            existing_record.prepaid_registration_status_id = (
                                PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id
                            )
                            existing_record.fineos_absence_id = (
                                existing_application.fineos_absence_id
                            )
                            db_session.commit()
                    else:
                        claimant_prepaid_registration = ClaimantPrepaidRegistration(
                            employee_id=existing_application.employee_id,
                            prepaid_registration_status_id=PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id,
                            fineos_absence_id=existing_application.fineos_absence_id,
                        )
                        db_session.add(claimant_prepaid_registration)
                        db_session.commit()

                    logger.info(
                        "applications_complete - successfully added claimant to prepaid card registration queue",
                        extra=log_attributes,
                    )
                except Exception as err:
                    logger.warning(
                        f"applications_complete failure - {err}",
                        extra=log_attributes,
                    )

        # TODO (PFMLPB-22886): Remove receipt of documents as part of application completion.
        try:
            documents_service.mark_documents_as_received(existing_application, db_session)
        except Exception as err:
            logger.warning(
                "applications_complete failure - application documents failed to be marked as received",
                extra=log_attributes,
                exc_info=err,
            )

        existing_application.mark_complete()
        completed_time = existing_application.completed_time
        log_attributes["application.completed_time"] = str(completed_time)
        log_attributes["application.completed_time.timestamp"] = str(completed_time.timestamp())

    mark_applications_ready_for_review_is_enabled = (
        app.get_features_config().mark_applications_ready_for_review.enable_mark_applications_ready_for_review
    )

    if DocumentRequirementService(db_session).are_all_required_documents_received(
        existing_application
    ):
        logger.info(
            "applications_complete - all required documents are received",
            extra=log_attributes,
        )
        if mark_applications_ready_for_review_is_enabled:
            existing_application.ready_for_review_time = completed_time
            log_attributes["application.ready_for_review_time"] = str(completed_time)
            log_attributes["application.ready_for_review_time.timestamp"] = str(
                completed_time.timestamp()
            )
            logger.info(
                "applications_complete - application ready for review", extra=log_attributes
            )
    else:
        logger.info(
            "applications_complete - some required documents are unreceived",
            extra=log_attributes,
        )

    logger.info("applications_complete success", extra=log_attributes)

    return response_util.success_response(
        message="Application {} completed without errors".format(
            existing_application.application_id
        ),
        data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
        status_code=200,
    ).to_api_response()


def document_upload(application_id, body, file):
    with app.db_session() as db_session:
        # Get the referenced application or return 404
        application = get_or_404(db_session, Application, application_id)

    # Parse the document details from the form body
    document_details: DocumentRequestBody = parse_request_body(DocumentRequestBody, body)

    log_attributes = get_application_log_attributes(application)

    error_response = error_response_if_invalid_benefit_year_issue(application, log_attributes)
    if error_response:
        return error_response

    try:
        document_response = documents_service.upload_document_to_fineos(
            application, document_details, file
        )

    except UploadDocumentError as e:
        return e.to_api_response()
    except ClaimWithdrawn as err:
        return err.to_api_response()

    # Return response
    return response_util.success_response(
        message="Successfully uploaded document", data=document_response.dict(), status_code=200
    ).to_api_response()


def documents_get(application_id):
    with app.db_session() as db_session:
        # Get the referenced application or return 404
        existing_application = get_or_404(db_session, Application, application_id)

        # Check if user can read application
        ensure(READ, existing_application)

        documents = GetDocumentService(
            db_session=db_session
        ).get_documents_from_fineos_for_application(existing_application)
        documents_list = [doc.dict() for doc in documents]

        return response_util.success_response(
            message="Successfully retrieved documents", data=documents_list, status_code=200
        ).to_api_response()


def document_download(application_id: UUID, document_id: str) -> Response:
    with app.db_session() as db_session:
        # Get the referenced application or return 404
        existing_application = get_or_404(db_session, Application, application_id)

        # Check if user can read application
        ensure(READ, existing_application)

        document = GetDocumentService(db_session=db_session).get_document_by_fineos_id(
            fineos_document_id=document_id, application=existing_application
        )
        if not document:
            raise NotFound(description=f"Could not find Document with ID {document_id}")

        ensure(READ, document)
        if isinstance(document, DocumentResponse):
            document_type = document.document_type
        else:
            document_type = None
        document_data: Base64EncodedFileData = download_document(
            existing_application, document_id, db_session, document_type
        )
        file_bytes = base64.b64decode(document_data.base64EncodedFileContents.encode("ascii"))

        content_type = document_data.contentType or "application/octet-stream"

        return Response(
            file_bytes,
            content_type=content_type,
            headers={"Content-Disposition": f"attachment; filename={document_data.fileName}"},
        )


def customer_payment_preference_get(application_id: UUID) -> Response:
    with app.db_session() as db_session:
        existing_application = get_or_404(db_session, Application, application_id)

        ensure(EDIT, existing_application)

        response = get_customer_payment_preference(existing_application, db_session)

        if response.error:
            return response_util.error_response(
                status_code=ServiceUnavailable,
                message=f"Request get-customer-payment-preference failed with error {response.error}.",
                data={},
                errors=[],
            ).to_api_response()

        log_attributes = get_application_log_attributes(existing_application)
        logger.info("customer_payment_preference_get success", extra=log_attributes)

        return response_util.success_response(
            message="Payment Preference for application {} fetched without errors".format(
                existing_application.application_id
            ),
            data=response.dict(exclude_none=True),
            status_code=200,
        ).to_api_response()


def customer_payment_preference_submit(application_id: UUID, body: Any) -> Response:

    with app.db_session() as db_session:
        existing_application = get_or_404(db_session, Application, application_id)

        ensure(EDIT, existing_application)

        log_attributes = get_application_log_attributes(existing_application)

        error_response = error_response_if_invalid_benefit_year_issue(
            existing_application, log_attributes
        )
        if error_response:
            return error_response

        updated_body = applications_service.remove_masked_fields_from_request(
            body, existing_application
        )

        payment_pref_request = parse_request_body(PaymentPreferenceRequestBody, updated_body)

        if not payment_pref_request.payment_preference:
            logger.info(
                "payment_preference_submit failure - payment preference is null",
                extra=log_attributes,
            )
            return response_util.error_response(
                status_code=BadRequest,
                message="Payment Preference cannot be null",
                data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
                errors=[],
            ).to_api_response()

        if not existing_application.has_submitted_payment_preference:
            applications_service.add_or_update_payment_preference(
                db_session, payment_pref_request.payment_preference, existing_application
            )

            db_session.add(existing_application)
            db_session.commit()
            db_session.refresh(existing_application)
            log_attributes = get_application_log_attributes(existing_application)

            payment_issues = application_rules.get_payments_issues(existing_application)
            if payment_issues:
                logger.info(
                    "payment_preference_submit failure - payment preference failed validation",
                    extra=log_attributes,
                )
                return response_util.error_response(
                    status_code=BadRequest,
                    message="Payment info is not valid for submission",
                    errors=payment_issues,
                    data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
                ).to_api_response()

            issues = application_rules.get_application_submit_issues(
                existing_application,
                db_session,
                log_attributes,
                app.get_features_config(),
            )

            if existing_application.payment_preference:
                # If there are no employer issues, send it straight to FINEOS
                # Otherwise claimant is filling additional "user not found" information
                if not application_rules.contains_employer_issue(issues):
                    try:
                        submit_customer_payment_preference(existing_application, db_session)
                    except Exception:
                        logger.warning(
                            "payment_preference_submit failure - failure submitting payment preference to claims processing system",
                            extra=log_attributes,
                            exc_info=True,
                        )
                        raise

                    existing_application.has_submitted_payment_preference = True
                    db_session.add(existing_application)
                    db_session.commit()
                    db_session.refresh(existing_application)

                log_attributes = get_application_log_attributes(existing_application)
                logger.info("payment_preference_submit success", extra=log_attributes)

                return response_util.success_response(
                    message="Payment Preference for application {} submitted without errors".format(
                        existing_application.application_id
                    ),
                    data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
                    status_code=201,
                    warnings=issues,
                ).to_api_response()
            else:
                logger.warning(
                    "payment_preference_submit failure - failure saving payment preference to database",
                    extra=log_attributes,
                )
                return response_util.error_response(
                    status_code=BadRequest,
                    message="Failed due to existing application missing payment preference. Please try again.",
                    data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
                    errors=[],
                ).to_api_response()

        logger.info(
            "payment_preference_submit failure - payment preference already submitted",
            extra=log_attributes,
        )
        message = (
            "Application {} could not be updated. Payment preference already submitted".format(
                existing_application.application_id
            )
        )
        return response_util.error_response(
            status_code=Forbidden,
            message=message,
            data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
            errors=[
                ValidationErrorDetail(
                    type=IssueType.exists,
                    field="payment_preference.payment_method",
                    message=message,
                )
            ],
        ).to_api_response()


def customer_payment_preference_update(application_id: UUID, body: Any) -> Response:
    """
    Currently this API is designed to update only below details on customers payment preference
    - Routing number
    - Address
    """

    with app.db_session() as db_session:
        existing_application = get_or_404(db_session, Application, application_id)

        ensure(EDIT, existing_application)

        log_attributes = get_application_log_attributes(existing_application)

        updated_body = applications_service.remove_masked_fields_from_request(
            body, existing_application
        )

        payment_pref_request = parse_request_body(PaymentPreferenceRequestBody, updated_body)

        # Verify that the request body has payment_preference
        if not payment_pref_request.payment_preference:
            logger.info(
                "payment_preference_submit failure - payment preference is null",
                extra=log_attributes,
            )
            return response_util.error_response(
                status_code=BadRequest,
                message="Payment Preference cannot be null",
                data={},
                errors=[],
            ).to_api_response()

        applications_service.add_or_update_payment_preference(
            db_session, payment_pref_request.payment_preference, existing_application
        )
        db_session.add(existing_application)
        db_session.commit()
        db_session.refresh(existing_application)

        try:
            update_customer_payment_preference(existing_application, db_session)
        except ValueError as error:
            return response_util.error_response(
                status_code=BadRequest,
                message=str(error),
                data={},
                errors=[],
            ).to_api_response()

        return response_util.success_response(
            message="Payment Preference for application {} submitted without errors".format(
                existing_application.application_id
            ),
            data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
            status_code=200,
            warnings=[],
        ).to_api_response()


def validate_tax_withholding_request(db_session, application_id, tax_preference_body):
    """
    Helper to handle validation for tax withholding requests
        1. Must be an existing application
        2. Requesting user must have authorization to edit application
        3. Preference must not already be set
    """
    existing_application = get_or_404(db_session, Application, application_id)
    ensure(EDIT, existing_application)

    if not isinstance(tax_preference_body.is_withholding_tax, bool):
        raise ValidationException(
            errors=[
                ValidationErrorDetail(
                    type=IssueType.required,
                    message="Tax withholding preference is required",
                    field="is_withholding_tax",
                )
            ]
        )

    # Allow claimant to update tax withholding preference if they haven't submitted part 1 yet
    if existing_application.submitted_time and existing_application.is_withholding_tax is not None:
        # otherwise, block further updates to tax withholding
        logger.info(
            "submit_tax_withholding_preference failure - preference already set",
            extra=get_application_log_attributes(existing_application),
        )
        raise ValidationException(
            errors=[
                ValidationErrorDetail(
                    type=IssueType.duplicate,
                    message="Tax withholding preference is already set",
                    field="is_withholding_tax",
                )
            ]
        )

    return existing_application


def save_tax_preference(db_session, existing_application, tax_preference_body):
    existing_application.is_withholding_tax = tax_preference_body.is_withholding_tax
    db_session.commit()
    db_session.refresh(existing_application)


def send_tax_selection_to_fineos(existing_application, tax_preference_body):
    try:
        send_tax_withholding_preference(
            existing_application, tax_preference_body.is_withholding_tax
        )
    except Exception:
        logger.warning(
            "submit-tax-withholding-preference failure - failure submitting tax withholding preference to claims processing system",
            extra=get_application_log_attributes(existing_application),
            exc_info=True,
        )
        raise


def submit_tax_withholding_preference(application_id: UUID, body: Any) -> Response:
    tax_preference_body = parse_request_body(TaxWithholdingPreferenceRequestBody, body)

    with app.db_session() as db_session:
        existing_application = validate_tax_withholding_request(
            db_session, application_id, tax_preference_body
        )

        log_attributes = get_application_log_attributes(existing_application)
        error_response = error_response_if_invalid_benefit_year_issue(
            existing_application, log_attributes
        )
        if error_response:
            return error_response

        employer_issue = get_contributing_employer_or_employee_issue(
            db_session,
            existing_application.employer_fein,
            existing_application.tax_identifier,
            log_attributes,
        )

        # If there are no employer issues, send it straight to FINEOS
        # Otherwise claimant is filling additional "user not found" information
        if not employer_issue:
            send_tax_selection_to_fineos(existing_application, tax_preference_body)
        save_tax_preference(db_session, existing_application, tax_preference_body)

        log_attributes = get_application_log_attributes(existing_application)
        logger.info(
            "tax_withholding_preference_submit success",
            extra=log_attributes,
        )
        # Grab the issues array after setting tax withholding, otherwise the
        # first tax withholding submission will have a warning.
        issues = application_rules.get_application_submit_issues(
            existing_application,
            db_session,
            log_attributes,
            app.get_features_config(),
        )

        return response_util.success_response(
            message="Tax Withholding Preference for application {} submitted without errors".format(
                existing_application.application_id
            ),
            data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
            warnings=issues,
            status_code=201,
        ).to_api_response()


def error_response_if_invalid_benefit_year_issue(
    existing_application: Application, log_attributes: Dict[str, Optional[str]]
) -> Optional[Response]:
    disable_overlapping_benefit_year_claim_creation = (
        app.get_features_config().application_intake.disable_overlapping_benefit_year_claim_creation
    )
    invalid_benefit_year_issue = application_rules.get_invalid_benefit_year_issue(
        existing_application
    )
    if disable_overlapping_benefit_year_claim_creation and invalid_benefit_year_issue:
        logger.info(invalid_benefit_year_issue.message, extra=log_attributes)
        return response_util.error_response(
            status_code=BadRequest,
            message=invalid_benefit_year_issue.message,
            errors=[invalid_benefit_year_issue],
            data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
        ).to_api_response()

    return None
