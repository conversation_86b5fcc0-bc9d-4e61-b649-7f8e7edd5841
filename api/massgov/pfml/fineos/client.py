#
# FINEOS client - abstract base class.
#

import abc
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple, Union

from requests.models import Response

from massgov.pfml.fineos.activity_services.models import (
    ActivitySubjectIdentifier,
    CaseIdentifier,
    CreateTaskResponse,
    WorkTypeIdentifier,
)
from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import (
    PFML_ContactDetails,
    PFML_EForm,
    PFML_EFormSummary,
)
from massgov.pfml.fineos.transforms.to_fineos.base import EFormBody

from . import models


class AbstractFINEOSClient(abc.ABC):
    """Abstract base class for a FINEOS API client."""

    @abc.abstractmethod
    def read_employer(self, employer_fein: str) -> models.OCOrganisation:
        pass

    @abc.abstractmethod
    def find_employer(self, employer_fein: str) -> str:
        """Create the employee account registration."""
        pass

    @abc.abstractmethod
    def register_api_user(self, employee_registration: models.EmployeeRegistration) -> None:
        """Create the employee account registration."""
        pass

    @abc.abstractmethod
    def health_check(self, user_id: str) -> bool:
        """Health check API."""
        pass

    @abc.abstractmethod
    def read_customer_details(self, user_id: str) -> models.customer_api.Customer:
        """Read customer details."""
        pass

    @abc.abstractmethod
    def update_customer_details(self, user_id: str, customer: models.customer_api.Customer) -> None:
        """Update customer details."""
        pass

    @abc.abstractmethod
    def read_customer_contact_details(self, user_id: str) -> PFML_ContactDetails:
        """Read customer contact details."""
        pass

    @abc.abstractmethod
    def update_customer_contact_details(
        self, user_id: str, contact_details: PFML_ContactDetails
    ) -> PFML_ContactDetails:
        """Update customer contact details."""
        pass

    @abc.abstractmethod
    def start_absence(
        self, user_id: str, absence_case: models.customer_api.AbsenceCase
    ) -> models.customer_api.AbsenceCaseSummary:
        pass

    @abc.abstractmethod
    def complete_intake(
        self, user_id: str, notification_case_id: str
    ) -> models.customer_api.NotificationCaseSummary:
        pass

    @abc.abstractmethod
    def get_absences(self, user_id: str) -> List[models.customer_api.AbsenceCaseSummary]:
        pass

    @abc.abstractmethod
    def get_absence(self, user_id: str, absence_id: str) -> models.customer_api.AbsenceDetails:
        pass

    @abc.abstractmethod
    def get_leave_plans(self, user_id: str) -> List[models.customer_api.LeavePlan]:
        pass

    @abc.abstractmethod
    def get_leave_availability(
        self, user_id: str, leave_plan_id: str
    ) -> models.customer_api.EmployeeLeaveBalance:
        pass

    @abc.abstractmethod
    def get_group_client_notification(
        self, employer_user_id: str, notification_id: str
    ) -> models.group_client_api.Notification:
        pass

    @abc.abstractmethod
    def get_group_client_absence_period_decisions(
        self, user_id: str, absence_id: str
    ) -> models.group_client_api.PeriodDecisions:
        pass

    @abc.abstractmethod
    def get_customer_absence_period_decisions(
        self, user_id: str, absence_id: str
    ) -> models.customer_api.AbsencePeriodDecisions:
        pass

    @abc.abstractmethod
    def get_customer_info(
        self, user_id: str, customer_id: str
    ) -> models.group_client_api.CustomerInfo:
        pass

    @abc.abstractmethod
    def get_customer_occupations(
        self, user_id: str, customer_id: str
    ) -> models.group_client_api.CustomerOccupations:
        pass

    @abc.abstractmethod
    def get_customer_occupations_customer_api(
        self, user_id: str
    ) -> List[models.customer_api.ReadCustomerOccupation]:
        pass

    @abc.abstractmethod
    def get_outstanding_information(
        self, user_id: str, case_id: str
    ) -> List[models.group_client_api.OutstandingInformationItem]:
        """Get outstanding information"""
        pass

    @abc.abstractmethod
    def update_outstanding_information_as_received(
        self,
        user_id: str,
        case_id: str,
        outstanding_information: models.group_client_api.OutstandingInformationData,
    ) -> None:
        """Update outstanding information received"""
        pass

    @abc.abstractmethod
    def get_eform_summary(
        self, user_id: str, absence_id: str
    ) -> List[models.group_client_api.EFormSummary]:
        pass

    @abc.abstractmethod
    def customer_get_eform_summary(self, user_id: str, absence_id: str) -> List[PFML_EFormSummary]:
        pass

    @abc.abstractmethod
    def get_eform(
        self, user_id: str, absence_id: str, eform_id: int
    ) -> models.group_client_api.EForm:
        pass

    @abc.abstractmethod
    def customer_get_eform(self, user_id: str, absence_id: str, eform_id: str) -> PFML_EForm:
        pass

    @abc.abstractmethod
    def create_eform(
        self, user_id: str, absence_id: str, eform: EFormBody
    ) -> models.group_client_api.EForm:
        pass

    @abc.abstractmethod
    def customer_create_eform(self, user_id: str, absence_id: str, eform: EFormBody) -> None:
        pass

    @abc.abstractmethod
    def get_case_occupations(
        self, user_id: str, case_id: str
    ) -> List[models.customer_api.ReadCustomerOccupation]:
        pass

    @abc.abstractmethod
    def get_customer_payment_preference(
        self, user_id: str
    ) -> models.customer_api.PaymentPreferenceCustomerResources:
        pass

    @abc.abstractmethod
    def create_overpayment_recovery(self, overpayment_recovery: models.OverpaymentRecovery) -> str:
        pass

    @abc.abstractmethod
    def create_customer_payment_preference(
        self, user_id: str, payment_preference: models.customer_api.CreatePaymentPreferenceCommand
    ) -> models.customer_api.PaymentPreferenceResource:
        pass

    @abc.abstractmethod
    def update_customer_payment_preference(
        self,
        user_id: str,
        payment_preference_id: str,
        payment_preference: models.customer_api.EditPaymentPreferenceCommand,
    ) -> models.customer_api.PaymentPreferenceResource:
        pass

    @abc.abstractmethod
    def update_occupation(
        self,
        occupation_id: int,
        employment_status: Optional[str],
        hours_worked_per_week: Optional[Decimal],
        fineos_org_unit_id: Optional[str],
        worksite_id: Optional[str],
    ) -> None:
        pass

    @abc.abstractmethod
    def upload_document(
        self,
        user_id: str,
        absence_id: str,
        document_type: str,
        file_content: bytes,
        file_name: str,
        content_type: str,
        description: str,
    ) -> models.customer_api.Document:
        pass

    @abc.abstractmethod
    def upload_document_multipart(
        self,
        user_id: str,
        absence_id: str,
        document_type: str,
        file_content: bytes,
        file_name: str,
        content_type: str,
        description: str,
    ) -> models.customer_api.Document:
        pass

    @abc.abstractmethod
    def group_client_get_documents(
        self, user_id: str, absence_id: str
    ) -> List[models.group_client_api.GroupClientDocument]:
        pass

    @abc.abstractmethod
    def get_managed_requirements(
        self, user_id: str, absence_id: str
    ) -> List[models.group_client_api.ManagedRequirementDetails]:
        pass

    @abc.abstractmethod
    def get_documents(
        self, user_id: str, absence_id: Optional[str] = None, api_params: Optional[Dict] = None
    ) -> List[models.customer_api.Document]:
        pass

    @abc.abstractmethod
    def download_document_as_leave_admin(
        self, user_id: str, absence_id: str, fineos_document_id: str
    ) -> models.group_client_api.Base64EncodedFileData:
        pass

    @abc.abstractmethod
    def download_document(
        self, user_id: str, fineos_document_id: str, absence_id: Optional[str] = None
    ) -> models.customer_api.Base64EncodedFileData:
        pass

    @abc.abstractmethod
    def mark_document_as_received(
        self, user_id: str, absence_id: str, fineos_document_id: str
    ) -> None:
        pass

    @abc.abstractmethod
    def get_outstanding_evidence(
        self, user_id: str, case_id: str
    ) -> list[models.customer_api.OutstandingSupportingEvidence]:
        pass

    @abc.abstractmethod
    def get_week_based_work_pattern(
        self, user_id: str, occupation_id: Union[str, int]
    ) -> models.customer_api.WeekBasedWorkPattern:
        pass

    @abc.abstractmethod
    def add_week_based_work_pattern(
        self,
        user_id: str,
        occupation_id: Union[str, int],
        week_based_work_pattern: models.customer_api.WeekBasedWorkPattern,
    ) -> models.customer_api.WeekBasedWorkPattern:
        pass

    @abc.abstractmethod
    def update_week_based_work_pattern(
        self,
        user_id: str,
        occupation_id: Union[str, int],
        week_based_work_pattern: models.customer_api.WeekBasedWorkPattern,
    ) -> models.customer_api.WeekBasedWorkPattern:
        pass

    @abc.abstractmethod
    def update_reflexive_questions(
        self,
        user_id: str,
        absence_id: Optional[str],
        additional_information: models.customer_api.AdditionalInformation,
    ) -> None:
        pass

    @abc.abstractmethod
    def create_or_update_employer(
        self,
        employer_creation: models.CreateOrUpdateEmployer,
        existing_organization: Optional[models.OCOrganisationItem] = None,
    ) -> Tuple[str, int]:
        """Create or update an employer in FINEOS."""
        pass

    @abc.abstractmethod
    def create_or_update_leave_period_change_request(
        self,
        fineos_web_id: str,
        absence_id: str,
        change_request: models.customer_api.CreateLeavePeriodsChangeRequestCommand,
    ) -> models.customer_api.LeavePeriodsChangeRequestResource:
        """Create or update a leave period change request in FINEOS."""
        pass

    @abc.abstractmethod
    def create_or_update_leave_admin(
        self, leave_admin_create_or_update: models.CreateOrUpdateLeaveAdmin
    ) -> Tuple[Optional[str], Optional[str]]:
        """Create or update a leave admin in FINEOS."""
        pass

    @abc.abstractmethod
    def create_service_agreement_for_employer(
        self,
        fineos_employer_id: int,
        service_agreement_inputs: models.CreateOrUpdateServiceAgreement,
    ) -> str:
        """Create Service Agreement For An Employer in FINEOS"""
        pass

    @abc.abstractmethod
    def send_tax_withholding_preference(self, absence_id: str, is_withholding_tax: bool) -> None:
        """Send tax withholding preference to FINEOS"""
        pass

    @abc.abstractmethod
    def upload_document_to_dms(self, file_name: str, file: bytes, data: Any) -> Response:
        """Upload a 1099G document to fineos Api"""
        pass

    @abc.abstractmethod
    def create_appeal_case(self, absence_case_id: str) -> Optional[str]:
        """Create an Appeal in FINEOS"""
        pass

    @abc.abstractmethod
    def read_tax_withholding_preference(self, absence_id: str) -> Optional[bool]:
        """Read tax withholding preference from FINEOS"""
        pass

    @abc.abstractmethod
    def read_claim_benefit(
        self, user_id: str, absence_paid_leave_case_id: str
    ) -> List[models.customer_api.BenefitSummary]:
        """Read claim benefit summary from FINEOS"""
        pass

    @abc.abstractmethod
    def read_disability_benefit(
        self, user_id: str, absence_paid_leave_case_id: str, benefit_id: str
    ) -> models.customer_api.ReadDisabilityBenefitResult:
        """Read disability benefits from FINEOS"""
        pass

    @abc.abstractmethod
    def submit_intermittent_leave_episode(
        self,
        fineos_web_id: str,
        absence_id: str,
        elements: models.customer_api.CreateActualAbsencePeriodCommandElements,
    ) -> List[models.customer_api.ActualAbsencePeriodResource]:
        pass

    @abc.abstractmethod
    def get_actual_absence_period_resources(
        self,
        user_id: str,
        absence_id: str,
    ) -> models.customer_api.ActualAbsencePeriodResources:
        pass

    @abc.abstractmethod
    def create_task(
        self,
        work_type: WorkTypeIdentifier,
        case: CaseIdentifier,
        activity_subject: Optional[ActivitySubjectIdentifier],
        description: Optional[str],
    ) -> CreateTaskResponse:
        """Create a task for an absence case in FINEOS. Returns CreateTaskResponse
        or raises an exception if task creation failed."""
        pass

    @abc.abstractmethod
    def is_customer_api_available(self) -> bool:
        """Checks if the Customer API is available."""
        pass

    @abc.abstractmethod
    def is_integration_services_api_available(self) -> bool:
        """Checks if the Integration Services API is available."""
        pass

    @abc.abstractmethod
    def is_group_client_api_available(self) -> bool:
        """Checks if the Group Client API is available."""
        pass

    @abc.abstractmethod
    def get_absence_reason(
        self, user_id: str, absence_reason_id: str
    ) -> models.customer_api.AbsenceReasonResource:
        """Retrieves the details of a specific absence period, including allowed transitions"""
        pass

    @abc.abstractmethod
    def add_absence_period_to_absence_case(
        self,
        user_id: str,
        absence_id: str,
        absence_period: models.customer_api.CreateRequestedAbsencePeriodCommandCustomer,
    ) -> models.customer_api.RequestedAbsencePeriodResource:
        """Adds a requested absence period to an absence case"""
        pass

    @abc.abstractmethod
    def update_requested_absence_period(
        self,
        user_id: str,
        absence_id: str,
        requested_absence_period_id: str,
        edit_absence_period_command: models.customer_api.EditRequestedAbsencePeriodCommand,
    ) -> models.customer_api.RequestedAbsencePeriodResource:
        """Updates the requested absence period"""
        pass
