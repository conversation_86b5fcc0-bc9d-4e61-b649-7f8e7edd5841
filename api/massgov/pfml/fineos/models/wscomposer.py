#
# Models for Web Services Composer.
#
from __future__ import annotations

import datetime
import re
from decimal import Decimal
from enum import Enum
from typing import Any, List, Optional

import pydantic

import massgov.pfml.util.logging

logger = massgov.pfml.util.logging.get_logger(__name__)


class BaseModel(pydantic.BaseModel):
    class Config:
        allow_population_by_field_name = True


class OverpaymentRecoveryMethod(str, Enum):
    EFT = "EFT"
    CREDIT_CARD = "Credit Card"
    INVOICE_RECOVERY = "Invoice Recovery"
    CHECK = "Check"


class OverpaymentRecovery(BaseModel):
    overpayment_case_number: str
    amount_of_recovery: Decimal
    date_of_recovery: datetime.date
    recovery_method: OverpaymentRecoveryMethod
    check_name: Optional[str] = None
    check_number: Optional[str] = None


class EmployeeRegistration(BaseModel):
    user_id: str
    customer_number: Optional[int]
    employer_id: str
    date_of_birth: datetime.date
    email: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    national_insurance_no: str


class CreateOrUpdateEmployer(BaseModel):
    fineos_customer_nbr: str
    employer_fein: str
    employer_legal_name: str
    employer_dba: str


class ServiceAgreementLeavePlans(str, Enum):
    FAM_MED_EXEMPT = "MA PFML Fam/Med Exempt"
    FAMILY = "MA PFML - Family"
    MILITARY = "MA PFML - Military Care"
    EMPLOYEE = "MA PFML - Employee"


class CreateOrUpdateServiceAgreement(BaseModel):
    unlink_leave_plans: Optional[bool]
    start_date: Optional[datetime.date]
    end_date: Optional[datetime.date]
    leave_plans: Optional[str]
    absence_management_flag: Optional[bool]
    version: Optional[bool]

    @pydantic.root_validator
    def version_or_unlink_leave_plans(cls, values):  # noqa: B902
        if values.get("unlink_leave_plans") is not None and values.get("version") is True:
            raise ValueError(
                "cannot send the unlink_leave_plans parameter when sending versions=True"
            )
        return values


# Classes for UpdateOrCreateParty request. Most attributes
# we are defaulting. The classes are used to help construct
# the XML request body. Not all elements of the request have
# been modeled, only those required to create or update a
# basic organisation with no points of contact or addresses.
class InstanceDomainAndFullIdItem(BaseModel):
    InstanceName: str
    DomainName: str
    FullId: int


class InstanceDomainAndFullId(BaseModel):
    InstanceDomainAndFullId: InstanceDomainAndFullIdItem


class OCPartyAliasItem(BaseModel):
    Type: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Unknown", DomainName="Party Alias Type", FullId=8928000
        )
    )
    Value: Any = None


class OCPartyAlias(BaseModel):
    OCPartyAlias: List[OCPartyAliasItem]


class OCPartyLocationAssociationItem(BaseModel):
    OID: str = "PE:11797:0000000001"
    BOEVersion: int = 0
    LastUpdateDate: str = "1753-01-01T00:00:00"
    UserUpdatedBy: str = "<EMAIL>"
    Description: Optional[str] = None
    EffectiveFrom: Optional[str] = "1753-01-01T00:00:00"
    EffectiveTo: Optional[str] = "1754-01-01T00:00:00"
    ExternalReference: Any = None
    Name: str = "HQ"
    PartyLocationAssociationType: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Unknown", DomainName="PartyLocationAssociationType", FullId=6624000
        )
    )
    SourceSystem: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Internal", DomainName="PartySourceSystem", FullId=8032000
        )
    )
    UpperName: Any = None
    ReferenceNumberValidated: bool = False


class OCPartyLocationAssociation(BaseModel):
    OCPartyLocationAssociation: List[OCPartyLocationAssociationItem]


class OCOrganisationUnitLocationLinkItem(BaseModel):
    OID: str = "PE:11797:0000000001"
    BOEVersion: int = 0
    LastUpdateDate: str = "1753-01-01T00:00:00"
    UserUpdatedBy: str = "<EMAIL>"
    partyLocationAssociation: OCPartyLocationAssociation


class OCOrganisationUnitLocationLinks(BaseModel):
    OrgUnitLocationLink: List[OCOrganisationUnitLocationLinkItem]


class OCOrganisationUnitItem(BaseModel):
    OID: str = "PE:11797:0000000001"
    BOEVersion: int = 0
    LastUpdateDate: str = "1753-01-01T00:00:00"
    UserUpdatedBy: str = "<EMAIL>"
    EffectiveFrom: Optional[str] = "1753-01-01T00:00:00"
    EffectiveTo: Optional[str] = "1754-01-01T00:00:00"
    ExternalReference: Any = None
    Name: str = "HRD"
    SourceSystem: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Internal", DomainName="PartySourceSystem", FullId=8032000
        )
    )
    orgUnitLocationLinks: Optional[OCOrganisationUnitLocationLinks]


class OCOrganisationUnit(BaseModel):
    OrganisationUnit: Optional[List[OCOrganisationUnitItem]]


class OCOrganisationDefaultItem(BaseModel):
    LastUpdateDate: str = "1753-01-01T00:00:00"
    UserUpdatedBy: str = "PFML_API"
    persistent_boeversion: int = pydantic.Field(0, alias="persistent-boeversion")
    C_CORRESP_PRIVHOLDER: int = 0
    C_OSGROUP_OWP: int = 0
    CulturalConsiderations: Any = None
    CustomerNo: str
    Disabled: bool = False
    ExcludePartyFromSearch: bool = False
    FailedLogonAttempts: int = 0
    GroupClient: bool = False
    I_CORRESP_PRIVHOLDER: int = 0
    I_OSGROUP_OWP: int = 0
    IdentificationNumberType: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Tax Identification Number",
            DomainName="Identification Number Type",
            FullId=8736002,
        )
    )
    LastSuccessfulLogon: Optional[str] = "1753-01-01T00:00:00"
    NotificationIssued: bool = False
    PartyType: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Employer", DomainName="Party Type", FullId=3290008
        )
    )
    Password: Any = None
    Position1: int = 0
    Position2: int = 0
    PronouncedAs: Any = None
    ReferenceGloballyUnique: bool = False
    ReferenceNo: Any = None
    SecuredClient: bool = False
    SelfServiceEnabled: bool = False
    SourceSystem: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Internal", DomainName="PartySourceSystem", FullId=8032000
        )
    )
    SuppressMktg: bool = False
    TenureStart: Optional[str] = "1753-01-01T00:00:00"
    AccountingDate: Optional[str] = "1753-01-01T00:00:00"
    BusinessType: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Unknown", DomainName="Organisation Business Type", FullId=3328000
        )
    )
    CompanyNumber: Any = None
    CorporateTaxDistrict: Any = None
    CorporateTaxNumber: str
    DateBusinessCommenced: Optional[str] = "1753-01-01T00:00:00"
    DateOfIncorporation: Optional[str] = "1753-01-01T00:00:00"
    DoingBusinessAs: str
    EndOfTrading: Optional[str] = "1753-01-01T00:00:00"
    EOTReasonCode: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Unknown", DomainName="EndOfTradingReasonCode", FullId=6592000
        )
    )
    EOTReasonInd: bool = False
    FinancialYearEnd: Optional[str] = "1753-01-01T00:00:00"
    LegalBusinessName: str
    LegalStatus: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Unknown", DomainName="Legal Status", FullId=1408000
        )
    )
    Name: str
    PayeTaxDistrict: Any = None
    PayeTaxNumber: Any = None
    RegisteredNumber: Any = None
    ShortName: Any = None
    UpperName: Any = None
    UpperRegisteredNumber: Any = None
    UpperShortName: Any = None
    VatNumber: Any = None
    organisationUnits: Optional[OCOrganisationUnit]


class OCOrganisationWithDefault(BaseModel):
    OCOrganisation: List[OCOrganisationDefaultItem]


class OCOrganisationNameItem(BaseModel):
    LastUpdateDate: str = "1753-01-01T00:00:00"
    UserUpdatedBy: str = "PFML_API"
    persistent_boeversion: int = pydantic.Field(0, alias="persistent-boeversion")
    Description: Any = None
    DoingBusinessAs: str
    LegalBusinessName: str
    Name: str
    NameType: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Unknown", DomainName="OrganisationNameType", FullId=6240000
        )
    )
    PronouncedAs: Any = None
    ShortName: Any = None
    UpperName: Any = None
    UpperShortName: Any = None
    ValidFrom: Optional[str] = "1753-01-01T00:00:00"
    ValidTo: Optional[str] = "1753-01-01T00:00:00"
    DefaultName: bool = True
    RunBOValidations: bool = True
    organisationWithDefault: OCOrganisationWithDefault


class OCOrganisationName(BaseModel):
    OCOrganisationName: List[OCOrganisationNameItem]


class OCOrganisationItem(BaseModel):
    OID: Optional[str] = None
    C_CORRESP_PRIVHOLDER: int = 0
    C_OSGROUP_OWP: int = 0
    CulturalConsiderations: Any = None
    CustomerNo: str
    Disabled: bool = False
    ExcludePartyFromSearch: bool = False
    FailedLogonAttempts: int = 0
    GroupClient: bool = False
    I_CORRESP_PRIVHOLDER: int = 0
    I_OSGROUP_OWP: int = 0
    IdentificationNumberType: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Tax Identification Number",
            DomainName="Identification Number Type",
            FullId=8736002,
        )
    )
    LastSuccessfulLogon: Optional[str] = "1753-01-01T00:00:00"
    NotificationIssued: bool = False
    PartyType: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Employer", DomainName="Party Type", FullId=3290008
        )
    )
    Password: Any = None
    Position1: int = 0
    Position2: int = 0
    PronouncedAs: Any = None
    ReferenceGloballyUnique: bool = False
    ReferenceNo: Any = None
    SecuredClient: bool = False
    SelfServiceEnabled: bool = False
    SourceSystem: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Internal", DomainName="PartySourceSystem", FullId=8032000
        )
    )
    SuppressMktg: bool = False
    TenureStart: Optional[str] = "1753-01-01T00:00:00"
    personalContactMediums: Any = None
    partyContactPreferences: Any = None
    partyAliases: OCPartyAlias = OCPartyAlias(OCPartyAlias=[OCPartyAliasItem()])
    pointsOfContact: Any = None
    AccountingDate: Optional[str] = "1753-01-01T00:00:00"
    BusinessType: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Unknown", DomainName="Organisation Business Type", FullId=3328000
        )
    )
    CompanyNumber: Any = None
    CorporateTaxDistrict: Any = None
    CorporateTaxNumber: str
    DateBusinessCommenced: Optional[str] = "1753-01-01T00:00:00"
    DateOfIncorporation: Optional[str] = "1753-01-01T00:00:00"
    DoingBusinessAs: Optional[str]
    EndOfTrading: Optional[str] = "1753-01-01T00:00:00"
    EOTReasonCode: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Unknown", DomainName="EndOfTradingReasonCode", FullId=6592000
        )
    )
    EOTReasonInd: bool = False
    FinancialYearEnd: Optional[str] = "1753-01-01T00:00:00"
    LegalBusinessName: Optional[str]
    LegalStatus: InstanceDomainAndFullId = InstanceDomainAndFullId(
        InstanceDomainAndFullId=InstanceDomainAndFullIdItem(
            InstanceName="Unknown", DomainName="Legal Status", FullId=1408000
        )
    )
    Name: str
    PayeTaxDistrict: Any = None
    PayeTaxNumber: Any = None
    RegisteredNumber: Any = None
    ShortName: Any = None
    UpperName: Any = None
    UpperRegisteredNumber: Any = None
    UpperShortName: Any = None
    VatNumber: Any = None
    organisationUnits: Optional[OCOrganisationUnit]
    names: Optional[OCOrganisationName]


# See FINEOS documentation for OID format: https://documentation.fineos.com/support/documentation/25.3_apiswebservicescomposer.html#tag/UpdateOrCreateParty/operation/UpdateOrCreateParty
# See ReadEmployer.Response.xsd here: https://github.com/EOLWD/pfml/blob/e66600d972641e67403d9d03653f9200c8d6177c/api/massgov/pfml/fineos/wscomposer/ReadEmployer.Response.xsd#L648
FINEOS_DEFAULT_OID_PATTERN = re.compile(r"^[pPtT][eElL]:\d{1,5}:\d{1,10}$")


class OCOrganisation(BaseModel):
    OCOrganisation: List[OCOrganisationItem]

    def is_valid_oid(self, oid: Optional[str]) -> bool:
        """Validate OID against the FINEOS default pattern."""
        if oid and FINEOS_DEFAULT_OID_PATTERN.match(oid):
            return True
        logger.error("Invalid OID format: %s", oid)
        return False

    def parse_oid(self, oid: Optional[str]) -> tuple[str, str, str]:
        """Parse OID into components."""
        validated_oid = self.is_valid_oid(oid)
        if oid and validated_oid:
            parts = oid.split(":")
            return (parts[0], parts[1], parts[2])
        raise ValueError(f"Invalid OID format: {oid}")

    @property
    def oid_parts(self) -> tuple[str, str, str]:
        """Convert OID into components."""
        oid = self.OCOrganisation[0].OID
        return self.parse_oid(oid)

    @property
    def class_id(self) -> str:
        """Get the organisation class ID from the OID."""
        return self.oid_parts[1]

    @property
    def index_id(self) -> str:
        """Get the organisation index ID from the OID."""
        return self.oid_parts[2]

    def get_customer_number(self):
        return str(self.OCOrganisation[0].CustomerNo)

    def get_worksite_id(self):
        units = self.OCOrganisation[0].organisationUnits
        try:
            worksite_id = (
                units.OrganisationUnit[0]  # type: ignore
                .orgUnitLocationLinks.OrgUnitLocationLink[0]
                .partyLocationAssociation.OCPartyLocationAssociation[0]
                .OID
            )
        except AttributeError:
            worksite_id = None
        return worksite_id

    def get_organization_units(self) -> Optional[List[OCOrganisationUnitItem]]:
        has_org_units = self.OCOrganisation[0].organisationUnits
        if has_org_units is not None:
            return has_org_units.OrganisationUnit
        return None


class PartyIntegrationDTOItem(BaseModel):
    EffectiveDate: Optional[str] = "1753-01-01T00:00:00"
    EndDate: Optional[str] = "1753-01-01T00:00:00"
    PartyTypeFlag: str = "Employer"
    SourceSystemName: str = "Unknown"
    person: Any = None
    organisation: OCOrganisation


class UpdateData(BaseModel):
    PartyIntegrationDTO: List[PartyIntegrationDTOItem]


class UpdateOrCreatePartyRequest(BaseModel):
    xmlns_p: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/UpdateOrCreateParty", alias="@xmlns:p"
    )
    xmlns_xsi: str = pydantic.Field("http://www.w3.org/2001/XMLSchema-instance", alias="@xmlns:xsi")
    xsi_schemaLocation: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/UpdateOrCreateParty updateOrcreateparty.xsd",
        alias="@xsi:schemaLocation",
    )
    config_name: str = pydantic.Field("UpdateOrCreateParty", alias="config-name")
    update_data: UpdateData = pydantic.Field(..., alias="update-data")


# Classes common to FINEOS XML interfaces
class AdditionalData(BaseModel):
    name: str
    value: str


class AdditionalDataSet(BaseModel):
    additional_data: List[AdditionalData] = pydantic.Field([], alias="additional-data")


# Classes to model OccupationDetailUpdateService request XML
class OccupationDetailUpdateData(BaseModel):
    additional_data_set: Optional[AdditionalDataSet] = pydantic.Field(
        None, alias="additional-data-set"
    )


class OccupationDetailUpdateRequest(BaseModel):
    xmlns_p: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/OccupationDetailUpdateService", alias="@xmlns:p"
    )
    xmlns_xsi: str = pydantic.Field("http://www.w3.org/2001/XMLSchema-instance", alias="@xmlns:xsi")
    xsi_schemaLocation: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/OccupationDetailUpdateService OccupationDetailUpdateService.xsd",
        alias="@xsi:schemaLocation",
    )
    config_name: str = pydantic.Field("OccupationDetailUpdateService", alias="config-name")
    update_data: OccupationDetailUpdateData = pydantic.Field(..., alias="update-data")


# Classes to model ServiceAgreementService request XML
class ServiceAgreementData(BaseModel):
    additional_data_set: Optional[AdditionalDataSet] = pydantic.Field(
        None, alias="additional-data-set"
    )


class ServiceAgreementServiceRequest(BaseModel):
    xmlns_p: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/ServiceAgreementService", alias="@xmlns:p"
    )
    xmlns_xsi: str = pydantic.Field("http://www.w3.org/2001/XMLSchema-instance", alias="@xmlns:xsi")
    xsi_schemaLocation: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/ServiceAgreementService serviceagreement.xsd",
        alias="@xsi:schemaLocation",
    )
    config_name: str = pydantic.Field("ServiceAgreementService", alias="config-name")
    update_data: ServiceAgreementData = pydantic.Field(..., alias="update-data")


class TaxWithholdingUpdateData(BaseModel):
    additional_data_set: Optional[AdditionalDataSet] = pydantic.Field(
        None, alias="additional-data-set"
    )


class TaxWithholdingUpdateRequest(BaseModel):
    xmlns_p: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/OptInSITFITService", alias="@xmlns:p"
    )
    xmlns_xsi: str = pydantic.Field("http://www.w3.org/2001/XMLSchema-instance", alias="@xmlns:xsi")
    xsi_schemaLocation: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/OptInSITFITService  optinsitfitservice.xsd",
        alias="@xsi:schemaLocation",
    )
    config_name: str = pydantic.Field("OptInSITFITService", alias="config-name")
    update_data: TaxWithholdingUpdateData = pydantic.Field(..., alias="update-data")


class AddOverpaymentAdjustmentData(BaseModel):
    additional_data_set: Optional[AdditionalDataSet] = pydantic.Field(
        None, alias="additional-data-set"
    )


class AddOverpaymentAdjustmentRequest(BaseModel):
    xmlns_p: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/COMAddOverpaymentActualRecovery", alias="@xmlns:p"
    )
    xmlns_xsi: str = pydantic.Field("http://www.w3.org/2001/XMLSchema-instance", alias="@xmlns:xsi")
    xsi_schemaLocation: str = pydantic.Field(
        "http://www.fineos.com/wscomposer/COMAddOverpaymentActualRecovery comAddOverpaymentActualRecovery.xsd",
        alias="@xsi:schemaLocation",
    )
    config_name: str = pydantic.Field("COMAddOverpaymentActualRecovery", alias="config-name")
    update_data: AddOverpaymentAdjustmentData = pydantic.Field(..., alias="update-data")
