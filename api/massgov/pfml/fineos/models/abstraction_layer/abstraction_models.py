from datetime import date
from typing import List, Optional

from pydantic import Field

from massgov.pfml.util.pydantic import PydanticBaseModelEmptyStrIsNone

"""
abstract_models.py

This module contains generic models used to form the model abstraction layer to insulate the
PFML codebase from the FINEOS API models, which are version specific and change with new releases.
These models are based on the FINEOS v22_5_1 spec models (as a starting place).

The intention is to have the rest of the PFML codebase rely on these stable models,
which will (hopefully) rarely change. As new versions of FINEOS are introduced,
new mappings can be created (in model_mapper.py) to convert version-specific models to these PFML-specific ones.
This approach ensures that the rest of the codebase continues to work with minimal changes, promoting maintainability
and reducing the risk of introducing errors.
"""


class PFML_ModelEnum(PydanticBaseModelEmptyStrIsNone):
    domainName: str = Field(..., description="Domain name.", max_length=100, min_length=0)
    instanceValue: str = Field(..., description="Enum instance name.", max_length=100, min_length=0)


### E F O R M S #################################################


class PFML_EFormAttribute(PydanticBaseModelEmptyStrIsNone):
    booleanValue: Optional[bool] = Field(
        None,
        description="Boolean value of an EForm attribute, which must be populated if the type attribute is set to boolean.",
    )
    dateValue: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    decimalValue: Optional[float] = Field(
        None,
        description="Decimal value of an EForm attribute, which must be populated if the type attribute is set to decimal.",
    )
    enumValue: Optional[PFML_ModelEnum] = None
    integerValue: Optional[int] = Field(
        None,
        description="Integer value of an EForm attribute, which must be populated if the type attribute is set to integer.",
    )
    name: str = Field(..., description="The name of an EForm attibute.")
    stringValue: Optional[str] = Field(
        None,
        description="String value of an EForm attribute, which must be populated if the type attribute is set to string.",
    )


class PFML_EForm(PydanticBaseModelEmptyStrIsNone):
    eformAttributes: Optional[List[PFML_EFormAttribute]] = Field(
        None, description="An array of EForm attributes."
    )
    eformId: str = Field(..., description="Unique automatically generated Id of an EForm document.")
    eformType: Optional[str] = Field(
        None, description="Name of the EForm document type", max_length=200, min_length=0
    )


class PFML_EFormSummary(PydanticBaseModelEmptyStrIsNone):
    effectiveDateFrom: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveDateTo: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    eformId: str = Field(..., description="Unique automatically generated Id of an EForm document.")
    eformType: str = Field(
        ...,
        description="The short business description of the document type.",
        max_length=200,
        min_length=0,
    )
    eformTypeId: Optional[str] = Field(
        None, description="Business Entity OID", example="PE-00012-0000001234"
    )


### C U S T O M E R ### D E T A I L S #################################################


class PFML_ExtensionAttribute(PydanticBaseModelEmptyStrIsNone):
    booleanValue: Optional[bool] = Field(
        None, description="Value of the class extension attribute of a boolean type."
    )
    dateOnlyValue: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    decimalValue: Optional[float] = Field(
        None, description="Value of the class extension attribute of a Decimal type."
    )
    enumValue: Optional[PFML_ModelEnum] = None
    integerValue: Optional[int] = Field(
        None, description="Value of the class extension attribute of a string type."
    )
    moneyValue: Optional[str] = Field(
        None, description="Value of the class extension attribute of a Money type."
    )
    name: str = Field(
        ..., description="The name of the attribute which extends standard claim set of attributes."
    )
    stringValue: Optional[str] = Field(
        None, description="Value of the class extension attribute of a string type."
    )


class PFML_EmailAddress(PydanticBaseModelEmptyStrIsNone):
    classExtensionInformation: Optional[List[PFML_ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain email Address extension information.",
    )
    emailAddress: Optional[str] = Field(
        None, description="Customers email address.", max_length=120, min_length=0
    )
    emailAddressType: str = Field(
        ..., description="Identifies the type of mail which is returned (Enum Domain=51)"
    )
    id: Optional[int] = Field(
        None, description="The id of the contact method (e.g. phone / mobile / emailAddress) ", ge=0
    )
    preferred: Optional[bool] = Field(
        None, description="Specify if it is the first person to try to contact when it is required."
    )


class PFML_PhoneNumber(PydanticBaseModelEmptyStrIsNone):
    areaCode: Optional[str] = Field(
        None, description="area code value", max_length=20, min_length=0
    )
    classExtensionInformation: Optional[List[PFML_ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain phone number (OCPhone) extension information.",
    )
    id: Optional[int] = Field(
        None, description="The id of the contact method (e.g. phone / mobile / emailAddress) ", ge=0
    )
    intCode: Optional[str] = Field(
        None, description="international code value", max_length=10, min_length=0
    )
    phoneNumberType: str = Field(
        ...,
        description="The type of phone number (e.g. landline / mobile)",
        max_length=10,
        min_length=0,
    )
    preferred: Optional[bool] = Field(
        None, description="Specify if it is the first person to try to contact when it is required."
    )
    telephoneNo: Optional[str] = Field(
        None, description="telephone No. value", max_length=10, min_length=0
    )


class PFML_ContactDetails(PydanticBaseModelEmptyStrIsNone):
    emailAddresses: Optional[List[PFML_EmailAddress]] = Field(
        None,
        description="Return list of email addresses, specifying the type: Email or Work Email.",
        max_items=100,
        min_items=0,
    )
    phoneNumbers: Optional[List[PFML_PhoneNumber]] = Field(
        None, description="An array with Customer Phone number details elements."
    )
    preferredContactMethod: Optional[int] = Field(
        None,
        description="Return the Id of the preferred contact method (it corresponds to one of the phones or emails specified in the previous lists and it means the first person to be contacted when it is needed).",
    )
