from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import (
    PFML_ContactDetails,
    PFML_EForm,
    PFML_EFormAttribute,
    PFML_EFormSummary,
    PFML_EmailAddress,
    PFML_ExtensionAttribute,
    PFML_ModelEnum,
    PFML_PhoneNumber,
)
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import ContactDetails
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import EForm as EForm_v22_5_1
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import (
    EFormAttribute as EFormAttribute_v22_5_1,
)
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import EFormSummary as EFormSummary_v22_5_1
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import EmailAddress, ExtensionAttribute
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import ModelEnum as ModelEnum_v22_5_1
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import PhoneNumber

"""
model_mapper.py

This module contains functions for mapping between version-specific FINEOS models and the generic PFML models.
The purpose of these mappings is to ensure that the PFML codebase can work with different versions of the FINEOS API
with minimal changes, promoting maintainability and reducing the risk of introducing errors.

As new versions of FINEOS are introduced, new mapping functions can be added to this module to handle the conversion
between the new version-specific models and the stable PFML models. This approach allows the rest of the PFML codebase
to remain stable and insulated from changes in the FINEOS API.

Note: we should also be removing old mapping functions as we retire support for specific versions of the FINEOS API/models.
"""


class ModelMapper:

    ### E F O R M S #################################################

    @staticmethod
    def map_eform_v22_5_1_to_pfml_eform(eform_v22_5_1: EForm_v22_5_1) -> PFML_EForm:
        def map_attribute(attr: EFormAttribute_v22_5_1) -> PFML_EFormAttribute:
            return PFML_EFormAttribute(
                booleanValue=attr.booleanValue,
                dateValue=attr.dateValue,
                decimalValue=attr.decimalValue,
                enumValue=(
                    PFML_ModelEnum(
                        domainName=attr.enumValue.domainName,
                        instanceValue=attr.enumValue.instanceValue,
                    )
                    if attr.enumValue
                    else None
                ),
                integerValue=attr.integerValue,
                name=attr.name,
                stringValue=attr.stringValue,
            )

        return PFML_EForm(
            eformAttributes=(
                [map_attribute(attr) for attr in eform_v22_5_1.eformAttributes]
                if eform_v22_5_1.eformAttributes
                else None
            ),
            eformId=str(eform_v22_5_1.eformId),
            eformType=eform_v22_5_1.eformType,
        )

    @staticmethod
    def map_pfml_eform_to_eform_v22_5_1(pfml_eform: PFML_EForm) -> EForm_v22_5_1:
        def map_attribute(attr: PFML_EFormAttribute) -> EFormAttribute_v22_5_1:
            return EFormAttribute_v22_5_1(
                booleanValue=attr.booleanValue,
                dateValue=attr.dateValue,
                decimalValue=attr.decimalValue,
                enumValue=(
                    ModelEnum_v22_5_1(
                        domainName=attr.enumValue.domainName,
                        instanceValue=attr.enumValue.instanceValue,
                    )
                    if attr.enumValue
                    else None
                ),
                integerValue=attr.integerValue,
                name=attr.name,
                stringValue=attr.stringValue,
            )

        return EForm_v22_5_1(
            eformAttributes=(
                [map_attribute(attr) for attr in pfml_eform.eformAttributes]
                if pfml_eform.eformAttributes
                else None
            ),
            eformId=int(pfml_eform.eformId),
            eformType=pfml_eform.eformType,
        )

    @staticmethod
    def map_eform_summary_v22_5_1_to_pfml_eform_summary(
        eform_summary_v22_5_1: EFormSummary_v22_5_1,
    ) -> PFML_EFormSummary:
        return PFML_EFormSummary(
            effectiveDateFrom=eform_summary_v22_5_1.effectiveDateFrom,
            effectiveDateTo=eform_summary_v22_5_1.effectiveDateTo,
            eformId=str(eform_summary_v22_5_1.eformId),
            eformType=eform_summary_v22_5_1.eformType,
            eformTypeId=eform_summary_v22_5_1.eformTypeId,
        )

    @staticmethod
    def map_pfml_eform_summary_to_eform_summary_v22_5_1(
        pfml_eform_summary: PFML_EFormSummary,
    ) -> EFormSummary_v22_5_1:
        return EFormSummary_v22_5_1(
            effectiveDateFrom=pfml_eform_summary.effectiveDateFrom,
            effectiveDateTo=pfml_eform_summary.effectiveDateTo,
            eformId=int(pfml_eform_summary.eformId),
            eformType=pfml_eform_summary.eformType,
            eformTypeId=pfml_eform_summary.eformTypeId,
        )

    ### C U S T O M E R ### D E T A I L S #################################################

    @staticmethod
    def map_contact_details_v22_5_1_to_pfml_contact_details(
        contact_details_v22_5_1: ContactDetails,
    ) -> PFML_ContactDetails:
        return PFML_ContactDetails(
            emailAddresses=(
                [
                    ModelMapper.map_email_address_v22_5_1_to_pfml_email_address(email)
                    for email in contact_details_v22_5_1.emailAddresses
                ]
                if contact_details_v22_5_1.emailAddresses
                else None
            ),
            phoneNumbers=(
                [
                    ModelMapper.map_phone_number_v22_5_1_to_pfml_phone_number(phone)
                    for phone in contact_details_v22_5_1.phoneNumbers
                ]
                if contact_details_v22_5_1.phoneNumbers
                else None
            ),
            preferredContactMethod=contact_details_v22_5_1.preferredContactMethod,
        )

    @staticmethod
    def map_pfml_contact_details_to_contact_details_v22_5_1(
        pfml_contact_details: PFML_ContactDetails,
    ) -> ContactDetails:
        return ContactDetails(
            emailAddresses=(
                [
                    ModelMapper.map_pfml_email_address_to_email_address_v22_5_1(email)
                    for email in pfml_contact_details.emailAddresses
                ]
                if pfml_contact_details.emailAddresses
                else None
            ),
            phoneNumbers=(
                [
                    ModelMapper.map_pfml_phone_number_to_phone_number_v22_5_1(phone)
                    for phone in pfml_contact_details.phoneNumbers
                ]
                if pfml_contact_details.phoneNumbers
                else None
            ),
            preferredContactMethod=pfml_contact_details.preferredContactMethod,
        )

    @staticmethod
    def map_email_address_v22_5_1_to_pfml_email_address(
        email_address_v22_5_1: EmailAddress,
    ) -> PFML_EmailAddress:
        return PFML_EmailAddress(
            classExtensionInformation=(
                [
                    ModelMapper.map_extension_attribute_v22_5_1_to_pfml_extension_attribute(attr)
                    for attr in email_address_v22_5_1.classExtensionInformation
                ]
                if email_address_v22_5_1.classExtensionInformation
                else None
            ),
            emailAddress=email_address_v22_5_1.emailAddress,
            emailAddressType=email_address_v22_5_1.emailAddressType,
            id=email_address_v22_5_1.id,
            preferred=email_address_v22_5_1.preferred,
        )

    @staticmethod
    def map_pfml_email_address_to_email_address_v22_5_1(
        pfml_email_address: PFML_EmailAddress,
    ) -> EmailAddress:
        return EmailAddress(
            classExtensionInformation=(
                [
                    ModelMapper.map_pfml_extension_attribute_to_extension_attribute_v22_5_1(attr)
                    for attr in pfml_email_address.classExtensionInformation
                ]
                if pfml_email_address.classExtensionInformation
                else None
            ),
            emailAddress=pfml_email_address.emailAddress,
            emailAddressType=pfml_email_address.emailAddressType,
            id=pfml_email_address.id,
            preferred=pfml_email_address.preferred,
        )

    @staticmethod
    def map_phone_number_v22_5_1_to_pfml_phone_number(
        phone_number_v22_5_1: PhoneNumber,
    ) -> PFML_PhoneNumber:
        return PFML_PhoneNumber(
            areaCode=phone_number_v22_5_1.areaCode,
            classExtensionInformation=(
                [
                    ModelMapper.map_extension_attribute_v22_5_1_to_pfml_extension_attribute(attr)
                    for attr in phone_number_v22_5_1.classExtensionInformation
                ]
                if phone_number_v22_5_1.classExtensionInformation
                else None
            ),
            id=phone_number_v22_5_1.id,
            intCode=phone_number_v22_5_1.intCode,
            phoneNumberType=phone_number_v22_5_1.phoneNumberType,
            preferred=phone_number_v22_5_1.preferred,
            telephoneNo=phone_number_v22_5_1.telephoneNo,
        )

    @staticmethod
    def map_pfml_phone_number_to_phone_number_v22_5_1(
        pfml_phone_number: PFML_PhoneNumber,
    ) -> PhoneNumber:
        return PhoneNumber(
            areaCode=pfml_phone_number.areaCode,
            classExtensionInformation=(
                [
                    ModelMapper.map_pfml_extension_attribute_to_extension_attribute_v22_5_1(attr)
                    for attr in pfml_phone_number.classExtensionInformation
                ]
                if pfml_phone_number.classExtensionInformation
                else None
            ),
            id=pfml_phone_number.id,
            intCode=pfml_phone_number.intCode,
            phoneNumberType=pfml_phone_number.phoneNumberType,
            preferred=pfml_phone_number.preferred,
            telephoneNo=pfml_phone_number.telephoneNo,
        )

    @staticmethod
    def map_extension_attribute_v22_5_1_to_pfml_extension_attribute(
        extension_attribute_v22_5_1: ExtensionAttribute,
    ) -> PFML_ExtensionAttribute:
        return PFML_ExtensionAttribute(
            booleanValue=extension_attribute_v22_5_1.booleanValue,
            dateOnlyValue=extension_attribute_v22_5_1.dateOnlyValue,
            decimalValue=extension_attribute_v22_5_1.decimalValue,
            enumValue=(
                ModelMapper.map_model_enum_v22_5_1_to_pfml_model_enum(
                    extension_attribute_v22_5_1.enumValue
                )
                if extension_attribute_v22_5_1.enumValue
                else None
            ),
            integerValue=extension_attribute_v22_5_1.integerValue,
            moneyValue=extension_attribute_v22_5_1.moneyValue,
            name=extension_attribute_v22_5_1.name,
            stringValue=extension_attribute_v22_5_1.stringValue,
        )

    @staticmethod
    def map_pfml_extension_attribute_to_extension_attribute_v22_5_1(
        pfml_extension_attribute: PFML_ExtensionAttribute,
    ) -> ExtensionAttribute:
        return ExtensionAttribute(
            booleanValue=pfml_extension_attribute.booleanValue,
            dateOnlyValue=pfml_extension_attribute.dateOnlyValue,
            decimalValue=pfml_extension_attribute.decimalValue,
            enumValue=(
                ModelMapper.map_pfml_model_enum_to_model_enum_v22_5_1(
                    pfml_extension_attribute.enumValue
                )
                if pfml_extension_attribute.enumValue
                else None
            ),
            integerValue=pfml_extension_attribute.integerValue,
            moneyValue=pfml_extension_attribute.moneyValue,
            name=pfml_extension_attribute.name,
            stringValue=pfml_extension_attribute.stringValue,
        )

    @staticmethod
    def map_model_enum_v22_5_1_to_pfml_model_enum(
        model_enum_v22_5_1: ModelEnum_v22_5_1,
    ) -> PFML_ModelEnum:
        return PFML_ModelEnum(
            domainName=model_enum_v22_5_1.domainName, instanceValue=model_enum_v22_5_1.instanceValue
        )

    @staticmethod
    def map_pfml_model_enum_to_model_enum_v22_5_1(
        pfml_model_enum: PFML_ModelEnum,
    ) -> ModelEnum_v22_5_1:
        return ModelEnum_v22_5_1(
            domainName=pfml_model_enum.domainName, instanceValue=pfml_model_enum.instanceValue
        )
