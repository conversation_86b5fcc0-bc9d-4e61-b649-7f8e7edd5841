#
# Executable functionality to export Eligibility Feed to FINEOS.
#
import dataclasses
import enum
import os
from datetime import datetime
from enum import Enum

import boto3

import massgov.pfml.fineos.eligibility_feed as eligibility_feed
import massgov.pfml.util.aws.sts as aws_sts
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging as logging
from massgov.pfml import db, fineos
from massgov.pfml.features.config.new_eligibility_feed import NewEligibilityFeedConfig
from massgov.pfml.fineos.eligibility_feed_export.update_customer_names_step import (
    UpdateCustomerNamesStep,
)
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.batch.task_runner import TaskRunner
from massgov.pfml.util.bg import background_task

logger = logging.get_logger(__name__)


class CreateEligibiltyExportTaskRunner(TaskRunner):
    class StepMapping(str, Enum):
        ELIGIBILITY_FEED = "eligibility-feed"
        ARCHIVE_FILES = "archive"
        UPDATE_CUSTOMER_NAMES = "update-customer-names"

    def run_steps(self, db_session: db.Session, log_entry_db_session: db.Session) -> None:
        logger.info("Start - Eligibility Export Task")
        eligibility_feed_export_config = eligibility_feed.EligibilityFeedExportConfig()
        new_eligibility_feed_config = NewEligibilityFeedConfig()

        if self.is_enabled(self.StepMapping.ELIGIBILITY_FEED):
            EligibilityFeedExportStep(
                db_session=db_session, log_entry_db_session=log_entry_db_session
            ).run()

        if self.is_enabled(self.StepMapping.ARCHIVE_FILES):
            ArchiveEligibilityFeedsStep(
                db_session=db_session,
                log_entry_db_session=log_entry_db_session,
            ).run()

        if (
            self.is_enabled(self.StepMapping.UPDATE_CUSTOMER_NAMES)
            and new_eligibility_feed_config.enabled_new_fineos_eligibility_feed_export
        ):
            UpdateCustomerNamesStep(
                db_session=db_session,
                log_entry_db_session=log_entry_db_session,
                max_concurrent_thread_count=eligibility_feed_export_config.update_customer_names_max_concurrent_thread_count,
                record_process_limit=eligibility_feed_export_config.update_customer_names_record_process_limit,
            ).run()


class EligibilityFeedExportStep(Step):
    """
    https://lwd.atlassian.net/wiki/spaces/API/pages/2365980675/Related+Payment+Post+Processing+Step
    """

    class Metrics(str, enum.Enum):
        EMPLOYERS_TOTAL_COUNT = "employers_total_count"
        EMPLOYERS_SUCCESS_COUNT = "employers_success_count"
        EMPLOYERS_SKIPPED_COUNT = "employers_skipped_count"
        EMPLOYERS_ERROR_COUNT = "employers_error_count"
        EMPLOYEE_AND_EMPLOYER_PAIRS_TOTAL_COUNT = "employee_and_employer_pairs_total_count"
        EMPLOYEES_PENDING_EXPORT_TOTAL_COUNT = "employees_pending_export_total_count"
        IMPORT_LOG_STATUS = "import_log_status"

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
    ):
        super().__init__(db_session, log_entry_db_session)

    def make_fineos_boto_session(
        self, config: eligibility_feed.EligibilityFeedExportConfig
    ) -> boto3.Session:
        return aws_sts.assume_session(
            role_arn=config.fineos_aws_iam_role_arn,
            external_id=config.fineos_aws_iam_role_external_id,
            role_session_name="eligibility_feed",
            region_name="us-east-1",
        )

    def make_fineos_client(self) -> fineos.AbstractFINEOSClient:
        return fineos.create_client()

    def make_db_session(self) -> db.Session:
        return db.init()

    def run_step(self) -> None:
        logger.info("Starting FINEOS eligibility feed export run")
        config = eligibility_feed.EligibilityFeedExportConfig()

        output_transport_params = None
        output_directory_path = f"{config.output_directory_path}/absence-eligibility/upload"
        temp_directory_path = config.temp_directory_path

        # Note that the IAM role for the Eligibility Feed Export Lambda/ECS Task
        # does not have access to any S3 bucket in the PFML account by default. So
        # in order to test the functionality by writing to a non-FINEOS S3 location,
        # the IAM role needs updated for the test as well.
        if eligibility_feed.is_fineos_output_location(output_directory_path):
            session = self.make_fineos_boto_session(config)
            output_transport_params = dict(client=session.client("s3"))

        fineos_client = self.make_fineos_client()

        if config.mode is eligibility_feed.EligibilityFeedExportMode.UPDATES:
            process_result = eligibility_feed.process_employee_updates(
                self.db_session,
                fineos_client,
                temp_directory_path,
                output_directory_path,
                output_transport_params,
                export_total_employee_limit=config.export_total_employee_limit,
                export_file_number_limit=config.export_file_number_limit,
            )

        elif config.mode is eligibility_feed.EligibilityFeedExportMode.LIST:
            employer_ids = config.employer_ids.split(",") if config.employer_ids else []
            if len(employer_ids) > 0:
                process_result = eligibility_feed.process_a_list_of_employers(
                    employer_ids,
                    self.db_session,
                    fineos_client,
                    temp_directory_path,
                    output_directory_path,
                    output_transport_params,
                )
            else:
                logger.info(
                    "Task started in 'LIST' mode but no list of employers provided. "
                    "If you intended to start task in this mode please provide a list of "
                    "employers to process in the ELIGIBILITY_FEED_LIST_OF_EMPLOYER_IDS environment variable."
                )
        else:
            process_result = eligibility_feed.process_all_employers(
                self.make_db_session,
                self.make_fineos_client,
                self.make_fineos_boto_session,
                config,
            )

        logger.info(
            "Finished writing all eligibility feeds",
            extra={"report": dataclasses.asdict(process_result)},
        )

        self.set_metrics(
            {
                self.Metrics.EMPLOYERS_TOTAL_COUNT: process_result.employers_total_count,
                self.Metrics.EMPLOYERS_SUCCESS_COUNT: process_result.employers_success_count,
                self.Metrics.EMPLOYERS_SKIPPED_COUNT: process_result.employers_skipped_count,
                self.Metrics.EMPLOYERS_ERROR_COUNT: process_result.employers_error_count,
                self.Metrics.EMPLOYEE_AND_EMPLOYER_PAIRS_TOTAL_COUNT: process_result.employee_and_employer_pairs_total_count,
                self.Metrics.EMPLOYEES_PENDING_EXPORT_TOTAL_COUNT: process_result.employees_pending_export_total_count,
                self.Metrics.IMPORT_LOG_STATUS: (
                    "warning" if process_result.warning_message else "success"
                ),
            }
        )


class ArchiveEligibilityFeedsStep(Step):
    class Metrics(str, enum.Enum):
        ARCHIVE_DIRECTORY_PATH = "archive_directory_path"
        ARCHIVED_FILES_TOTAL_COUNT = "archived_files_total_count"
        CONFIG_MODE = "config_mode"

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
    ):
        super().__init__(db_session, log_entry_db_session)

    def run_step(self) -> None:
        logger.info("Archiving eligibility feeds")
        config = eligibility_feed.EligibilityFeedExportConfig()

        archive_directory_path = (
            f"{config.archive_directory_path}/{datetime.now().strftime('%Y%m%dT%H%M%S')}"
        )
        temp_directory_path = config.temp_directory_path

        archived_files_total_count = 0
        files_to_copy = os.listdir(temp_directory_path)

        logger.info(f"Found {len(files_to_copy)} files to archive in the FINEOS bucket.")

        for file in files_to_copy:
            source = f"{temp_directory_path}/{file}"
            destination = f"{archive_directory_path}/{file}"
            logger.info(f"Uploading file from {source} to {destination}")
            file_util.upload_file(source, destination)
            archived_files_total_count += 1

        self.set_metrics(
            {
                self.Metrics.ARCHIVE_DIRECTORY_PATH: archive_directory_path,
                self.Metrics.ARCHIVED_FILES_TOTAL_COUNT: archived_files_total_count,
                self.Metrics.CONFIG_MODE: config.mode,
            }
        )

        logger.info(
            "Finished archiving eligibility feeds",
        )


@background_task("fineos-eligibility-feed-export")
def main():
    """Entry point for ECS task

    For an ECS task, the return value is taken as a status code, so should not
    return anything (or anything non-zero) from this function.
    """
    CreateEligibiltyExportTaskRunner().run()
