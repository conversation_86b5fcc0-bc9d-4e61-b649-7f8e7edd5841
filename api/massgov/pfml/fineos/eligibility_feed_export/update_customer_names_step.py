from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass
from datetime import date
from enum import auto
from typing import TypeAlias
from uuid import UUID

from sqlalchemy import delete, select
from sqlalchemy.orm.query import RowReturningQuery

import massgov.pfml.fineos as fineos
import massgov.pfml.util.logging as logging
from massgov.pfml.api.services import fineos_actions
from massgov.pfml.db import Session
from massgov.pfml.db.models.employees import (
    Employee,
    EmployeeOccupation,
    EmployeePushToFineosQueue,
    Employer,
    TaxIdentifier,
)
from massgov.pfml.db.models.fineos_web_id import FINEOSWebIdExt
from massgov.pfml.fineos.models.customer_api import Customer
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.str_enum import StrEnum

logger = logging.get_logger(__name__)


EmployeeId: TypeAlias = UUID
EmployeeSsn: TypeAlias = str
EmployerCustomerNumber: TypeAlias = int
EmployerFein: TypeAlias = str
FineosWebId: TypeAlias = str
FirstName: TypeAlias = str
LastName: TypeAlias = str
WasSuccessful: TypeAlias = bool

UpdateCustomerNameArguments: TypeAlias = tuple[
    EmployeeId,
    FirstName,
    LastName,
    EmployeeSsn,
    EmployerFein,
    EmployerCustomerNumber,
    FineosWebId | None,
]


@dataclass
class UpdateCustomerNameResult:
    employee_id: EmployeeId
    new_fineos_web_id_ext: FINEOSWebIdExt | None
    is_success: WasSuccessful


class UpdateCustomerNamesStep(Step):
    """
    Updates customer names in FINEOS by processing "UPDATE"s in the EmployeePushToFineosQueue.

    Uses multithreading to reduce I/O bounds from calling the FINEOS API.
    """

    BATCH_SIZE = 1_000
    """Count of records for processing yielded in memory at a time."""

    class Metrics(StrEnum):
        CUSTOMER_NAMES_UPDATED_COUNT = auto()
        """Count of FINEOS customer names updated."""

        NEWLY_REGISTERED_EMPLOYEE_COUNT = auto()
        """Count of FINEOSWebIdExt records created via employee registration."""

        RECORDS_ERRORED_COUNT = auto()
        """Count of processed records that encountered an error."""

        RECORDS_PROCESSED_COUNT = auto()
        """Count of records processed."""

    def __init__(
        self,
        db_session: Session,
        log_entry_db_session: Session,
        max_concurrent_thread_count: int = 5,
        record_process_limit: int | None = None,
        should_add_to_report_queue: bool = False,
    ) -> None:
        super().__init__(db_session, log_entry_db_session, should_add_to_report_queue)
        self._max_concurrent_thread_count = max_concurrent_thread_count
        self._record_process_limit = record_process_limit

    def cleanup_on_failure(self):
        logger.error(
            f"{self.__class__.__name__} failed. Some employees may have been registered with "
            "FINEOS, and some FINEOS customer names may have been updated successfully."
        )

    def run_step(self) -> None:
        logger.info("Updating FINEOS customer names…")

        customer_name_updates = self._query_pending_customer_name_updates()

        with ThreadPoolExecutor(
            self._max_concurrent_thread_count, "update_fineos_customer_names"
        ) as executor:
            results = list(
                executor.map(
                    lambda app_with_web_id: self._update_customer_name(app_with_web_id),
                    customer_name_updates.tuples(),
                )
            )

        self.set_metrics(
            {
                self.Metrics.RECORDS_ERRORED_COUNT: sum(
                    1 for result in results if not result.is_success
                ),
                self.Metrics.RECORDS_PROCESSED_COUNT: len(results),
            }
        )

        new_fineos_web_id_exts = [
            result.new_fineos_web_id_ext
            for result in results
            if result.new_fineos_web_id_ext is not None
        ]
        self.db_session.add_all(new_fineos_web_id_exts)
        self.db_session.commit()
        self.set_metrics(
            {self.Metrics.NEWLY_REGISTERED_EMPLOYEE_COUNT: len(new_fineos_web_id_exts)}
        )

        successful_employee_ids = [result.employee_id for result in results if result.is_success]

        if len(successful_employee_ids) > 0:
            stmt = delete(EmployeePushToFineosQueue).where(
                EmployeePushToFineosQueue.action == "UPDATE",
                # Deleting by employee_id instead of employee_push_to_fineos_queue_id removes any
                # redundant UPDATE actions for the same Employee from the queue.
                EmployeePushToFineosQueue.employee_id.in_(successful_employee_ids),
            )
            self.db_session.execute(stmt)
            self.db_session.commit()

        self.set_metrics({self.Metrics.CUSTOMER_NAMES_UPDATED_COUNT: len(successful_employee_ids)})

        logger.info("Successfully updated FINEOS customer names.")

    def _query_pending_customer_name_updates(
        self,
    ) -> RowReturningQuery[UpdateCustomerNameArguments]:
        # An employee with a pending employer update may not exist in FINEOS yet, so attempting to
        # process a name change could fail. Additionally, processing an employer update will also
        # perform a name update if there is a change.
        employees_with_pending_employer_updates = (
            self.db_session.query(EmployeePushToFineosQueue.employee_id)
            .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
            .distinct()
            .cte("employees_with_pending_employer_updates")
        )

        employee_selection = (
            self.db_session.query(
                EmployeePushToFineosQueue.employee_id,
            )
            .filter(
                EmployeePushToFineosQueue.action == "UPDATE",
                EmployeePushToFineosQueue.employee_id.is_not(None),
                EmployeePushToFineosQueue.employee_id.not_in(
                    select(employees_with_pending_employer_updates.c.employee_id)
                ),
            )
            .order_by(
                EmployeePushToFineosQueue.priority.desc(),
                EmployeePushToFineosQueue.created_at.asc(),
            )
            .limit(self._record_process_limit if self._record_process_limit is not None else None)
            .cte("employee_selection")
        )

        # EmployeeOccupation is used to link to employer_id instead of WagesAndContributions because
        # FINEOS will only allow registration for an employee/employer pair that has an occupation
        # record in FINEOS. EmployeeOccupations are created by processing FINEOS data in the
        # ImportFineosEmployeeUpdatesStep.
        employee_employer_selection = (
            self.db_session.query(
                employee_selection.c.employee_id,
                EmployeeOccupation.employer_id,
            )
            .distinct(
                employee_selection.c.employee_id,
            )
            .join(
                EmployeeOccupation,
                employee_selection.c.employee_id == EmployeeOccupation.employee_id,
            )
            .cte("employee_employer_selection")
        )

        return (
            self.db_session.query(
                Employee.employee_id,
                Employee.first_name,
                Employee.last_name,
                TaxIdentifier.tax_identifier,
                Employer.employer_fein,
                Employer.fineos_employer_id,
                FINEOSWebIdExt.fineos_web_id,
            )
            .select_from(employee_employer_selection)
            .join(Employee, Employee.employee_id == employee_employer_selection.c.employee_id)
            .join(TaxIdentifier, Employee.tax_identifier_id == TaxIdentifier.tax_identifier_id)
            .join(Employer, Employer.employer_id == employee_employer_selection.c.employer_id)
            .outerjoin(
                FINEOSWebIdExt,
                (FINEOSWebIdExt.employee_tax_identifier == TaxIdentifier.tax_identifier)
                & (FINEOSWebIdExt.employer_fein == Employer.employer_fein)
                & (FINEOSWebIdExt.fineos_web_id.is_not(None)),
            )
            .yield_per(self.BATCH_SIZE)
        )

    def _update_customer_name(
        self, arguments: UpdateCustomerNameArguments
    ) -> UpdateCustomerNameResult:
        """
        Processes a customer name update.

        Returns the affected employee_id, whether the update was successful, and a FINEOSWebIdExt if
        the customer was registered with FINEOS during the process.
        """

        (
            employee_id,
            first_name,
            last_name,
            employee_ssn,
            employer_fein,
            employer_customer_number,
            fineos_web_id,
        ) = arguments

        new_fineos_web_id_ext = None

        try:
            fineos_client = fineos.get_thread_singleton_client()

            if fineos_web_id is None:
                new_fineos_web_id_ext = fineos_actions.register_employee(
                    fineos_client,
                    employee_ssn,
                    employer_fein,
                    str(employer_customer_number),
                )
                fineos_web_id = new_fineos_web_id_ext.fineos_web_id

            customer = fineos_client.read_customer_details(fineos_web_id)
            customer_name_requires_change = (
                customer.firstName != first_name or customer.lastName != last_name
            )

            if customer_name_requires_change:
                customer.firstName = first_name
                customer.lastName = last_name
                _adjust_customer_for_fineos_validation(customer)
                fineos_client.update_customer_details(fineos_web_id, customer)

            was_successful = True
        except Exception as exception:
            was_successful = False
            logger.error(
                "Error occurred while processing customer name change for Employee %s: %s",
                employee_id,
                exception,
            )

        return UpdateCustomerNameResult(
            employee_id,
            new_fineos_web_id_ext,
            was_successful,
        )


def _adjust_customer_for_fineos_validation(customer: Customer) -> None:
    """
    Ensures that empty fields are populated to pass FINEOS validation for the update customer
    details endpoint.

    The `Customer` model is configured to transform and drop empty fields that FINEOS returns from
    the read customer details endpoint in a way that may cause it to fail validation if sent back to
    FINEOS in update customer details. Additionally, FINEOS can return an invalid representation of
    an address if it has not yet been entered.

    This function will check for any of these situations in `customer` and make the necessary
    adjustments to satisfy FINEOS validation.
    """

    if customer.classExtensionInformation is not None:
        for item in customer.classExtensionInformation:
            # These fields default to empty strings from FINEOS, which are dropped by the `Customer`
            # model when parsing from JSON. A missing value in an `ExtensionAttribute` will fail
            # validation.
            if item.name in ["MassachusettsID", "OutOfStateID"] and item.stringValue is None:
                item.stringValue = ""

    if customer.customerAddress is not None and not customer.customerAddress.address.is_valid():
        # FINEOS returns an invalid representation of its own address model when the address has not
        # yet been created in FINEOS. Sending the invalid address back to FINEOS will fail
        # validation.
        customer.customerAddress = None

    if customer.dateOfBirth is None:
        # dateOfBirth is documented as a required field for update customer details. January 1, 1753
        # represents a "null" date in FINEOS.
        customer.dateOfBirth = date(1753, 1, 1)
