#
# FINEOS client - FINEOS implementation.
#

import base64
import datetime
import os.path
import re
import reprlib
import urllib.parse
import xml.etree.ElementTree
from decimal import Decimal
from typing import Any, Dict, List, Mapping, NoReturn, Optional, Tuple, Union, cast
from urllib.parse import urlencode
from xml.etree.ElementTree import Element

import defusedxml.ElementTree
import oauthlib.oauth2
import pydantic
import requests
import xmlschema
from requests.models import Response

import massgov.pfml.features as features
import massgov.pfml.util.json as json
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.api.util.deepget import deepget
from massgov.pfml.db.models.fineos_api_log import FINEOSApiLog
from massgov.pfml.fineos.activity_services.models import (
    ActivitySubjectIdentifier,
    CaseIdentifier,
    CreateTaskResponse,
    WorkTypeIdentifier,
    create_task_request,
)
from massgov.pfml.fineos.activity_services.requests import (
    ACTIVITY_SERVICES_PATH,
    ActivityServiceMethod,
)
from massgov.pfml.fineos.activity_services.schemas import (
    fineos_activity_services_request_schema,
    fineos_activity_services_response_schema,
)
from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import (
    PFML_ContactDetails,
    PFML_EForm,
    PFML_EFormSummary,
)
from massgov.pfml.fineos.models.abstraction_layer.model_mapper import ModelMapper
from massgov.pfml.fineos.models.customer_api import (
    AbsenceReasonResource,
    ActualAbsencePeriodResource,
    ActualAbsencePeriodResources,
    CreateActualAbsencePeriodCommandElements,
    CreateLeavePeriodsChangeRequestCommand,
    CreateRequestedAbsencePeriodCommandCustomer,
    EditRequestedAbsencePeriodCommand,
    LeavePeriodsChangeRequestResource,
    OutstandingSupportingEvidence,
    RequestedAbsencePeriodResource,
)
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import (
    ContactDetails as ContactDetails_v22_5_1,
)
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import EForm as EForm_v22_5_1
from massgov.pfml.fineos.models.customer_api.spec_22_5_1 import EFormSummary as EFormSummary_v22_5_1
from massgov.pfml.fineos.transforms.from_fineos.enums import (
    update_translatable_absence_period_decision_values,
    update_translatable_absence_period_values,
)
from massgov.pfml.fineos.transforms.to_fineos.base import EFormBody
from massgov.pfml.fineos.util.response import (
    fineos_document_empty_dates_to_none,
    get_errors_meta_data,
    log_validation_error,
    parse_fineos_document_for_customer,
)
from massgov.pfml.fineos.wscomposer.schemas import fineos_wscomposer_schema
from massgov.pfml.util.collections.list import find_in_list
from massgov.pfml.util.newrelic.events import record_custom_event

from . import client, exception, models

logger = massgov.pfml.util.logging.get_logger(__name__)
MILLISECOND = datetime.timedelta(milliseconds=1)
RE_NUMBER = re.compile(r"[0-9]{2,}")

# Failure messages that are expected and don't need to be logged or tracked as errors.
EXPECTED_UNPROCESSABLE_ENTITY_FAILURES = {
    "encoded file data is mandatory",
    "file size is mandatory",
    "is not a valid file",
}

employee_register_request_schema = fineos_wscomposer_schema("EmployeeRegisterService.Request.xsd")

update_or_create_party_request_schema = fineos_wscomposer_schema("UpdateOrCreateParty.Request.xsd")

update_or_create_party_response_schema = fineos_wscomposer_schema(
    "UpdateOrCreateParty.Response.xsd"
)

create_or_update_leave_admin_request_schema = xmlschema.XMLSchema(
    os.path.join(
        os.path.dirname(__file__), "leave_admin_creation", "CreateOrUpdateLeaveAdmin.Request.xsd"
    )
)

create_or_update_leave_admin_response_schema = xmlschema.XMLSchema(
    os.path.join(
        os.path.dirname(__file__), "leave_admin_creation", "CreateOrUpdateLeaveAdmin.Response.xsd"
    )
)

service_agreement_service_request_schema = fineos_wscomposer_schema(
    "ServiceAgreementService.Request.xsd"
)

service_agreement_service_response_schema = fineos_wscomposer_schema(
    "ServiceAgreementService.Response.xsd"
)

occupation_detail_update_service_request_schema = fineos_wscomposer_schema(
    "OccupationDetailUpdateService.Request.xsd"
)

read_employer_response_schema = fineos_wscomposer_schema("ReadEmployer.Response.xsd")

update_tax_withholding_pref_request_schema = fineos_wscomposer_schema(
    "OptInSITFITService.Request.xsd"
)

update_tax_withholding_pref_response_schema = fineos_wscomposer_schema(
    "OptInSITFITService.Response.xsd"
)

read_tax_withholding_pref_response_schema = fineos_wscomposer_schema(
    "COMReadPaidLeaveInstruction.Response.xsd"
)

create_appeal_template_path = os.path.join(
    os.path.dirname(__file__), "models", "create_appeal_request_template.xml"
)

add_overpayment_recovery_request_schema = fineos_wscomposer_schema(
    "COMAddOverpaymentActualRecovery.Request.xsd"
)

add_overpayment_recovery_response_schema = fineos_wscomposer_schema(
    "COMAddOverpaymentActualRecovery.Response.xsd"
)

with open(create_appeal_template_path) as f:
    create_appeal_template = f.read()

# Instance of reprlib.Repr (repr with limits of the size of resulting string) for debug logging.
debug_repr = reprlib.Repr()
debug_repr.maxdict = debug_repr.maxlist = debug_repr.maxtuple = 100
debug_repr.maxstring = debug_repr.maxother = 4000


class FINEOSClient(client.AbstractFINEOSClient):
    """FINEOS API client."""

    db_session_raw: Any = None

    integration_services_api_url: str
    group_client_api_url: str
    customer_api_url: str
    wscomposer_url: str
    wscomposer_user_id: str
    oauth2_url: str
    client_id: str
    client_secret: str
    request_count: int
    oauth_session: Any
    soap_user_id: str
    soap_password: str

    def __init__(
        self,
        integration_services_api_url,
        group_client_api_url,
        customer_api_url,
        wscomposer_url,
        wscomposer_user_id,
        oauth2_url,
        client_id,
        client_secret,
        oauth_session,
        soap_user_id,
        soap_password,
    ):
        self.integration_services_api_url = integration_services_api_url
        self.group_client_api_url = group_client_api_url
        self.customer_api_url = customer_api_url
        self.wscomposer_url = wscomposer_url
        self.wscomposer_user_id = wscomposer_user_id
        self.oauth2_url = oauth2_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.oauth_session = oauth_session
        self.soap_user_id = soap_user_id
        self.soap_password = soap_password
        self.request_count = 0
        logger.info(
            "customer_api_url %s, wscomposer_url %s, group_client_api_url %s, "
            "integration_services_api_url %s",
            customer_api_url,
            wscomposer_url,
            group_client_api_url,
            integration_services_api_url,
        )
        self._init_oauth_session()

    def __repr__(self):
        return "<FINEOSClient %s>" % urllib.parse.urlparse(self.customer_api_url).hostname

    def _init_oauth_session(self):
        """Set up an OAuth session and get a token."""
        try:
            token = self.oauth_session.fetch_token(
                token_url=self.oauth2_url,
                client_id=self.client_id,
                client_secret=self.client_secret,
                timeout=5,
            )
        except (
            oauthlib.oauth2.OAuth2Error,
            requests.exceptions.RequestException,
            requests.exceptions.Timeout,
        ) as ex:
            self._handle_client_side_exception("POST", self.oauth2_url, ex, "init_oauth_session")

        logger.info(
            "POST %s => type %s, expires %is (at %s)",
            self.oauth2_url,
            token["token_type"],
            token["expires_in"],
            datetime.datetime.utcfromtimestamp(token["expires_at"]),
        )

    def _handle_client_side_exception(
        self, method: str, url: str, ex: Exception, method_name: str
    ) -> None:
        # Make sure New Relic records errors from FINEOS, even if the API does not ultimately
        # return an error.
        record_custom_event(
            "FineosError",
            {
                "fineos.error.class": type(ex).__name__,
                "fineos.error.message": str(ex),
                "fineos.request.method": method,
                "fineos.request.uri": url,
            },
        )

        if isinstance(
            ex,
            (
                requests.exceptions.Timeout,
                oauthlib.oauth2.TemporarilyUnavailableError,
                requests.exceptions.ConnectionError,
            ),
        ):
            logger.warning("%s %s => %r", method, url, ex)
            raise exception.FINEOSFatalUnavailable(method_name=method_name, cause=ex)
        else:
            logger.exception("%s %s => %r", method, url, ex)
            raise exception.FINEOSFatalClientSideError(method_name=method_name, cause=ex)

    def _request(
        self, method: str, url: str, method_name: str, headers: Dict[str, str], **args: Any
    ) -> requests.Response:
        """Make a request and handle errors."""
        self.request_count += 1

        log_extra: dict[str, Any] = {
            "fineos.request.method": method,
            "fineos.request.url": url,
            "fineos.request.url_rule": RE_NUMBER.sub("%", url),
        }
        if logger.isEnabledFor(massgov.pfml.util.logging.DEBUG):
            log_extra_debug: dict[str, Any] = {
                "fineos.request.headers": headers,
                "fineos.request.args": debug_repr.repr(args),
            }
            logger.debug("%s %s start", method, url, extra=log_extra | log_extra_debug)

        # Increase the timeout drastically. Past 29 seconds, FINEOS returns a 504
        # timeout, so the request client is really just waiting until the last second.
        #
        # Note that this is longer than our API Gateway timeout (also 29 seconds),
        # so it's very plausible that a request times out on the API Gateway side,
        # especially for application submission since /complete-intake is long and
        # there are other calls to the claims processing system (CPS) included in submission.
        #
        # However, we're allowing the request to complete on the API (as long as it's under 29s)
        # even if the response never makes it to the claimant, to try and ensure a consistent
        # CPS <--> Paid Leave API state and prevent additional long calls when the claimant retries.
        #
        request_timeout = 29

        try:
            try:
                response = self.oauth_session.request(
                    method, url, timeout=(6.1, request_timeout), headers=headers, **args
                )
            except oauthlib.oauth2.TokenExpiredError:
                logger.info("token expired, starting new OAuth session")
                self._init_oauth_session()
                response = self.oauth_session.request(
                    method, url, timeout=(6.1, request_timeout), headers=headers, **args
                )
        except (requests.exceptions.RequestException, requests.exceptions.Timeout) as ex:
            self._handle_client_side_exception(method, url, ex, method_name)
        else:
            if is_fineos_api_logging_to_db_enabled():
                try:
                    request_headers = json.dumps(headers)
                    request_args: str
                    try:
                        request_args = json.dumps(args)
                    except Exception as exc:
                        logger.info(
                            "Failed to serialize request args.",
                            exc_info=exc,
                        )
                        request_args = str(args)
                    response_headers = str(response.headers)
                    response_text = str(response.text)

                    with db.session_scope(self.get_db_session_raw()) as db_session:
                        fineos_api_log = FINEOSApiLog(
                            request_method=method,
                            request_url=url,
                            request_method_name=method_name,
                            request_headers=request_headers,
                            request_args=request_args,
                            response_status_code=response.status_code,
                            response_headers=response_headers,
                            response_text=response_text,
                        )
                        db_session.add(fineos_api_log)
                        db_session.commit()
                except Exception as exc:
                    logger.error(
                        "Failed to log FINEOS API request and response.",
                        exc_info=exc,
                    )

        response_time_ms = int(response.elapsed / MILLISECOND)
        log_extra |= {
            "fineos.response.status_code": response.status_code,
            "fineos.response.response_time_ms": response_time_ms,
            "fineos.response.length": len(response.content),
        }
        not_ok = (
            response.status_code != requests.codes.ok
            and response.status_code != requests.codes.created
        )
        if logger.isEnabledFor(massgov.pfml.util.logging.DEBUG) or not_ok:
            log_extra |= {
                "fineos.response.headers": response.headers,
                "fineos.response.text": response.text,
            }

        # Add FINEOS* properties for backward compatibility with NR Rule/Dashboards
        legacy_attributes_for_only_one_message_per_request = {
            "FINEOSMethod": method,
            "FINEOSUrl": url,
            "FINEOSResponseCode": response.status_code,
            "FINEOSResponseTime": response_time_ms,
        }

        if not_ok:
            # Try to parse metadata from the response.
            error_meta_data = get_errors_meta_data(response)

            # FINEOS returned an error. Record it in New Relic before raising the exception.

            base_fineos_error_payload = {
                "fineos.error.class": "FINEOSClientBadResponse",
                "fineos.error.message": response.text,
                "fineos.response.status": response.status_code,
                "fineos.request.method": method,
                "fineos.request.uri": url,
                "fineos.request.response_millis": response.elapsed / MILLISECOND,
            }

            if error_meta_data:
                for meta_data in error_meta_data:
                    record_custom_event(
                        "FineosError",
                        base_fineos_error_payload
                        | {
                            "fineos.error.id": meta_data.get("id"),
                            "fineos.error.X-Correlation-ID": meta_data.get("X-Correlation-ID"),
                            "fineos.error.X-Client-ID": meta_data.get("X-Client-ID"),
                            "fineos.error.X-Client-Version": meta_data.get("X-Client-Version"),
                            "fineos.error.nonDisplayableMessage": meta_data.get(
                                "nonDisplayableMessage"
                            ),
                        },
                    )
                else:
                    record_custom_event(
                        "FineosError",
                        base_fineos_error_payload,
                    )

            err: exception.FINEOSClientError

            if (
                response.status_code
                in (requests.codes.SERVICE_UNAVAILABLE, requests.codes.GATEWAY_TIMEOUT)
                or "ESOCKETTIMEDOUT" in response.text
            ):
                # The service is unavailable for some reason. Log a warning. There should be a
                # percentage-based alarm for when there are too many of these.
                #
                # Ideally we would never get GATEWAY_TIMEOUT and would instead always keep our
                # client-side timeout lower than the FINEOS 29s timeout; however, the program has
                # requested that we keep them as high as possible. We should still manage them
                # the same as a client-side timeout exception.
                err = exception.FINEOSFatalUnavailable(
                    response_status=response.status_code,
                    message=response.text,
                    method_name=method_name,
                )
                log_fn = logger.warning
            elif response.status_code == requests.codes.UNPROCESSABLE_ENTITY:
                err = exception.FINEOSUnprocessableEntity(
                    method_name, requests.codes.ok, response.status_code, message=response.text
                )
                log_fn = logger.warning
                log_validation_error(err, EXPECTED_UNPROCESSABLE_ENTITY_FAILURES)
            elif response.status_code == requests.codes.NOT_FOUND:
                err = exception.FINEOSNotFound(
                    method_name, requests.codes.ok, response.status_code, message=response.text
                )
                log_fn = logger.warning
            elif response.status_code == requests.codes.FORBIDDEN:
                err = exception.FINEOSForbidden(
                    method_name, requests.codes.ok, response.status_code, message=response.text
                )
                log_fn = logger.warning
            else:
                # We should never see anything other than these. Log an error if we do. These include issues
                # like 400 BAD REQUEST (misformatted request), 500 INTERNAL SERVER ERROR, and 413 SIZE TOO LARGE.

                # For the create_appeal_case() request, we may get XML back from the SOAP API.
                # If so, parse the error message out from the raw XML.
                message = response.text
                xml_error = re.search(r"(?<=<errorMessage>)(.*)(?=<\/errorMessage>)", message)
                if xml_error:
                    message = xml_error.group()
                err = exception.FINEOSFatalResponseError(
                    response_status=response.status_code,
                    message=message,
                    method_name=method_name,
                )
                log_fn = logger.error
                log_extra |= {
                    "fineos.response.internalerror.message": self._get_internal_error(
                        "nonDisplayableMessage", message
                    ),
                }

            log_fn(
                "FINEOS request complete",
                extra=log_extra | legacy_attributes_for_only_one_message_per_request,
            )

            raise err

        logger.info(
            "FINEOS request complete",
            extra=log_extra | legacy_attributes_for_only_one_message_per_request,
        )

        return response

    def _customer_api(
        self,
        method: str,
        path: str,
        user_id: str,
        method_name: str,
        header_content_type: Optional[str] = "application/json",
        **args: Any,
    ) -> requests.Response:
        """Make a request to the Customer API."""
        url = urllib.parse.urljoin(self.customer_api_url, path)
        content_type_header = {"Content-Type": header_content_type} if header_content_type else {}
        headers = dict({"userid": user_id}, **content_type_header)

        response = self._request(method, url, method_name, headers, **args)
        return response

    def _group_client_api(
        self,
        method: str,
        path: str,
        user_id: str,
        method_name: str,
        header_content_type: Optional[str] = "application/json",
        **args: Any,
    ) -> requests.Response:
        """Make a request to the Group Client API."""
        url = urllib.parse.urljoin(self.group_client_api_url, path)
        content_type_header = {"Content-Type": header_content_type} if header_content_type else {}
        headers = dict({"userid": user_id}, **content_type_header)
        response = self._request(method, url, method_name, headers, **args)
        return response

    def _integration_services_api(
        self,
        method: str,
        path: str,
        user_id: str,
        method_name: str,
        header_content_type: Optional[str] = "application/xml; charset=utf-8",
        **args: Any,
    ) -> requests.Response:
        """Make a request to the Integration Services API."""
        url = urllib.parse.urljoin(self.integration_services_api_url, path)
        content_type_header = {"Content-Type": header_content_type} if header_content_type else {}
        headers = dict({"userid": user_id}, **content_type_header)
        response = self._request(method, url, method_name, headers, **args)
        return response

    def _integration_services_soap_api(
        self,
        method: str,
        path: str,
        user_id: str,
        method_name: str,
        soap_action: str,
        **args: Any,
    ) -> requests.Response:
        """Make a request to the Integration Services SOAP API."""
        url = urllib.parse.urljoin(self.integration_services_api_url, path)
        headers = {"userid": user_id, "SOAPAction": soap_action, "Content-Type": "text/xml"}
        return self._request(method, url, method_name, headers, **args)

    def _wscomposer_request(
        self, method: str, path: str, method_name: str, query: Mapping[str, str], xml_data: str
    ) -> requests.Response:
        """Make a request to the Web Services Composer API."""
        query_with_user_id = dict(query)
        query_with_user_id["userid"] = self.wscomposer_user_id
        path_with_query = path + "?" + urllib.parse.urlencode(query_with_user_id)
        url = urllib.parse.urljoin(self.wscomposer_url, path_with_query)
        headers = {"Content-Type": "application/xml; charset=utf-8"}
        return self._request(method, url, method_name, headers, data=xml_data.encode("utf-8"))

    def _activity_services_request(
        self, method: ActivityServiceMethod, xml_data: str
    ) -> requests.Response:
        """Make a request to the Activity Services API."""

        url = urllib.parse.urljoin(self.wscomposer_url, ACTIVITY_SERVICES_PATH)
        headers = {
            "Content-Type": "text/xml; charset=utf-8",
            "SOAPAction": f"urn:{method.value}",
        }
        return self._request("POST", url, method.value, headers, data=xml_data.encode("utf-8"))

    def _prepare_document_id(self, fineos_document_id: str) -> str:
        """
        Prepares the Fineos document ID by checking if it is in the form of xxxxx-xxxxx.
        If it is, only the second part of the hyphenated value is used.
        """
        if "-" in fineos_document_id:
            parts = fineos_document_id.split("-")
            if len(parts) == 2 and all(part.isalnum() for part in parts):
                return parts[1]
        return fineos_document_id

    def read_employer(self, employer_fein: str) -> models.OCOrganisation:
        """Retrieves FINEOS employer info given an FEIN.

        Raises
        ------
        FINEOSEntityNotFound
            If no employer exists in FINEOS that matches the given FEIN.
        """
        response = self._wscomposer_request(
            "GET", "ReadEmployer", "read_employer", {"param_str_taxId": employer_fein}, ""
        )
        response_decoded = self._decode_xml_response(read_employer_response_schema, response.text)

        if response_decoded is not None and "OCOrganisation" not in response_decoded:
            raise exception.FINEOSEntityNotFound("Employer not found.")

        return models.OCOrganisation.parse_obj(response_decoded)

    def find_employer(self, employer_fein: str) -> str:
        """Retrieves the FINEOS customer number for an employer given an FEIN.

        Raises
        ------
        FINEOSEntityNotFound
            If no employer exists in FINEOS that matches the given FEIN.
        """
        return self.read_employer(employer_fein).get_customer_number()

    def register_api_user(self, employee_registration: models.EmployeeRegistration) -> None:
        """Creates the employee account registration.

        Raises
        ------
        FINEOSEntityNotFound
            If no employee-employer combination exists in FINEOS
            that matches the given SSN + employer FEIN.
        """
        xml_body = self._register_api_user_payload(employee_registration)

        try:
            self._wscomposer_request(
                "POST",
                "webservice",
                "register_api_user",
                {"config": "EmployeeRegisterService"},
                xml_body,
            )
        except exception.FINEOSFatalResponseError as err:
            # Expected 500 errors. See #3 and #7 here:
            # https://lwd.atlassian.net/wiki/spaces/DD/pages/*********/FINEOS+error+responses
            #
            # Although #2 (More than One Employee Details Found) is possible here,
            # we want to let it bubble up and raise so that it can be triaged.
            if err.response_status == 500 and (
                "The employee does not have an occupation linked" in err.message  # noqa: B306
                or "No Employee Details" in err.message  # noqa: B306
            ):
                raise exception.FINEOSEntityNotFound(err.message)  # noqa: B306

            # If not an expected error, bubble it up.
            raise

    @staticmethod
    def _register_api_user_payload(employee_registration: models.EmployeeRegistration) -> str:
        parameters = {
            "@xmlns:p": "http://www.fineos.com/wscomposer/EmployeeRegisterService",
            "config-name": "EmployeeRegisterService",
            "update-data": {
                "EmployeeRegistrationDTO": [
                    {
                        "CustomerNumber": str(employee_registration.customer_number or ""),
                        "DateOfBirth": str(employee_registration.date_of_birth),
                        "Email": employee_registration.email,
                        "EmployeeExternalId": employee_registration.user_id,
                        "EmployerId": employee_registration.employer_id,
                        "FirstName": employee_registration.first_name,
                        "LastName": employee_registration.last_name,
                        "NationalInsuranceNo": employee_registration.national_insurance_no,
                    }
                ]
            },
        }
        xml_element = cast(Element, employee_register_request_schema.encode(parameters))
        return xml.etree.ElementTree.tostring(xml_element, encoding="unicode", xml_declaration=True)

    def health_check(self, user_id: str) -> bool:
        """Health check API."""
        response = self._customer_api("GET", "healthcheck", user_id, "health_check")
        return response.text == "ALIVE"

    def read_customer_details(self, user_id: str) -> models.customer_api.Customer:
        """Read customer details."""
        response = self._customer_api(
            "GET", "customer/readCustomerDetails", user_id, "read_customer_details"
        )
        json = response.json()
        return models.customer_api.Customer.parse_obj(json)

    def update_customer_details(self, user_id: str, customer: models.customer_api.Customer) -> None:
        """Update customer details."""
        self._customer_api(
            "POST",
            "customer/updateCustomerDetails",
            user_id,
            "update_customer_details",
            data=customer.json(exclude_none=True),
        )

    # main implementation of read_customer_contact_details
    def read_customer_contact_details(self, user_id: str) -> PFML_ContactDetails:
        """Fetch customer contact details."""
        contact_details_v22_5_1 = self.read_customer_contact_details_v22_5_1(user_id)
        return ModelMapper.map_contact_details_v22_5_1_to_pfml_contact_details(
            contact_details_v22_5_1
        )

    # v22_5_1-specific implementation of read_customer_contact_details
    def read_customer_contact_details_v22_5_1(self, user_id: str) -> ContactDetails_v22_5_1:
        """Fetch customer contact details."""
        response = self._customer_api(
            "GET", "customer/readCustomerContactDetails", user_id, "read_customer_contact_details"
        )

        return ContactDetails_v22_5_1.parse_obj(response.json())

    # main implementation of update_customer_contact_details
    def update_customer_contact_details(
        self, user_id: str, pfml_contact_details: PFML_ContactDetails
    ) -> PFML_ContactDetails:
        """Update customer contact details."""

        # convert from our generic PFML model into v22_5_1 to send to FINEOS
        input_contact_details_v22_5_1 = (
            ModelMapper.map_pfml_contact_details_to_contact_details_v22_5_1(pfml_contact_details)
        )

        # post the update to FINEOS and convert the returned/updated v22_5_1 model back into our generic PFML model
        updated_contact_details_v22_5_1 = self.update_customer_contact_details_v22_5_1(
            user_id, input_contact_details_v22_5_1
        )
        return ModelMapper.map_contact_details_v22_5_1_to_pfml_contact_details(
            updated_contact_details_v22_5_1
        )

    # v22_5_1-specific implementation of update_customer_contact_details
    def update_customer_contact_details_v22_5_1(
        self, user_id: str, contact_details: ContactDetails_v22_5_1
    ) -> ContactDetails_v22_5_1:
        """Update customer contact details."""
        response = self._customer_api(
            "POST",
            "customer/updateCustomerContactDetails",
            user_id,
            "update_customer_contact_details",
            data=contact_details.json(exclude_none=True),
        )

        return ContactDetails_v22_5_1.parse_obj(response.json())

    def start_absence(
        self, user_id: str, absence_case: models.customer_api.AbsenceCase
    ) -> models.customer_api.AbsenceCaseSummary:
        logger.info("absence_case %s", absence_case.json(exclude_none=True))
        response = self._customer_api(
            "POST",
            "customer/absence/startAbsence",
            user_id,
            "start_absence",
            data=absence_case.json(exclude_none=True),
        )
        json = response.json()
        logger.debug("json %r", json)
        new_case = models.customer_api.AbsenceCaseSummary.parse_obj(json)
        logger.info(
            "absence %s, notification case %s", new_case.absenceId, new_case.notificationCaseId
        )
        return new_case

    def complete_intake(
        self, user_id: str, notification_case_id: str
    ) -> models.customer_api.NotificationCaseSummary:
        response = self._customer_api(
            "POST",
            f"customer/absence/notifications/{notification_case_id}/complete-intake",
            user_id,
            "complete_intake",
        )
        json = response.json()
        logger.debug("json %r", json)

        # Doesn't match OpenAPI file - API returns a single-item list instead of the object.
        return models.customer_api.NotificationCaseSummary.parse_obj(json[0])

    def get_absences(self, user_id: str) -> List[models.customer_api.AbsenceCaseSummary]:
        response = self._customer_api("GET", "customer/absence/absences", user_id, "get_absences")
        json = response.json()
        logger.debug("json %r", json)

        return pydantic.parse_obj_as(List[models.customer_api.AbsenceCaseSummary], json)

    def get_absence(self, user_id: str, absence_id: str) -> models.customer_api.AbsenceDetails:
        response = self._customer_api(
            "GET", f"customer/absence/absences/{absence_id}", user_id, "get_absence"
        )
        json = response.json()
        logger.debug("json %r", json)

        # Translate the absence period decision values for backward compatibility.
        absence_details = models.customer_api.AbsenceDetails.parse_obj(json)

        update_translatable_absence_period_values(absence_details.absencePeriods)

        return absence_details

    def get_leave_plans(self, user_id: str) -> List[models.customer_api.LeavePlan]:
        response = self._customer_api(
            "GET",
            "customer/absence/agreement/leavePlans",
            user_id,
            "get_leave_plans",
        )
        response_json = response.json()
        logger.debug("json %r", json)

        return pydantic.parse_obj_as(
            List[models.customer_api.LeavePlan], response_json.get("leavePlans")
        )

    def get_leave_availability(
        self, user_id: str, leave_plan_id: str
    ) -> models.customer_api.EmployeeLeaveBalance:
        url = f"customer/absence/absences/leave-plans/{leave_plan_id}/leave-availability"
        try:
            response = self._customer_api("GET", url, user_id, "get_leave_availability")
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: get_leave_availability",
                extra={"method_name": "get_leave_availability"},
                exc_info=error,
            )
            error.method_name = "get_leave_availability"
            raise error

        json_data = response.json()
        return models.customer_api.EmployeeLeaveBalance.parse_obj(json_data)

    def read_claim_benefit(
        self, user_id: str, absence_paid_leave_case_id: str
    ) -> List[models.customer_api.BenefitSummary]:
        response = self._customer_api(
            "GET",
            f"customer/claims/{absence_paid_leave_case_id}/benefits",
            user_id,
            "get_claim_benefit",
        )
        json = response.json()
        logger.debug("json %r", json)

        return pydantic.parse_obj_as(List[models.customer_api.BenefitSummary], json)

    def read_disability_benefit(
        self, user_id: str, absence_paid_leave_case_id: str, benefit_id: str
    ) -> models.customer_api.ReadDisabilityBenefitResult:
        response = self._customer_api(
            "GET",
            f"customer/claims/{absence_paid_leave_case_id}/benefits/{benefit_id}/readDisabilityBenefit",
            user_id,
            "get_claim_benefit",
        )
        json = response.json()
        logger.debug("json %r", json)

        return models.customer_api.ReadDisabilityBenefitResult.parse_obj(json)

    def get_group_client_notification(
        self, employer_user_id: str, notification_id: str
    ) -> models.group_client_api.Notification:
        response = self._group_client_api(
            "GET",
            f"groupClient/notifications/{notification_id}",
            employer_user_id,
            "get_notification",
        )

        notification = response.json()
        return models.group_client_api.Notification.parse_obj(notification)

    def get_group_client_absence_period_decisions(
        self, user_id: str, absence_id: str
    ) -> models.group_client_api.PeriodDecisions:
        try:
            querystring = urlencode({"absenceId": absence_id})
            response = self._group_client_api(
                "GET",
                f"groupClient/absences/absence-period-decisions?{querystring}",
                user_id,
                "get_group_client_absence_period_decisions",
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: get_group_client_absence_period_decisions",
                extra={"method_name": "get_group_client_absence_period_decisions"},
                exc_info=error,
            )
            error.method_name = "get_group_client_absence_period_decisions"
            raise error
        absence_periods = response.json()
        return models.group_client_api.PeriodDecisions.parse_obj(absence_periods)

    def get_customer_absence_period_decisions(
        self, user_id: str, absence_id: str
    ) -> models.customer_api.AbsencePeriodDecisions:
        try:
            response = self._customer_api(
                "GET",
                f"customer/absence/absences/{absence_id}/absence-period-decisions",
                user_id,
                "get_customer_absence_period_decisions",
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: get_customer_absence_period_decisions",
                extra={"method_name": "get_customer_absence_period_decisions"},
                exc_info=error,
            )
            error.method_name = "get_customer_absence_period_decisions"
            raise error
        absence_periods = response.json()

        absence_period_decisions = models.customer_api.AbsencePeriodDecisions.parse_obj(
            absence_periods
        )

        # need to translate decision(s) here for backward compatibility
        update_translatable_absence_period_decision_values(
            absence_period_decisions.absencePeriodDecisions
        )

        return absence_period_decisions

    def get_customer_info(
        self, user_id: str, customer_id: str
    ) -> models.group_client_api.CustomerInfo:
        try:
            response = self._group_client_api(
                "GET",
                f"groupClient/customers/{customer_id}/customer-info",
                user_id,
                "get_customer_info",
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: get_customer_info",
                extra={"method_name": "get_customer_info"},
                exc_info=error,
            )
            error.method_name = "get_customer_info"
            raise error
        json = response.json()
        return models.group_client_api.CustomerInfo.parse_obj(json)

    def get_customer_occupations(
        self, user_id: str, customer_id: str
    ) -> models.group_client_api.CustomerOccupations:
        try:
            response = self._group_client_api(
                "GET",
                f"groupClient/customers/{customer_id}/customer-occupations",
                user_id,
                "get_customer_occupations",
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: get_customer_occupations",
                extra={"method_name": "get_customer_occupations"},
                exc_info=error,
            )
            error.method_name = "get_customer_occupations"
            raise error
        json = response.json()
        return models.group_client_api.CustomerOccupations.parse_obj(json)

    def get_outstanding_information(
        self, user_id: str, case_id: str
    ) -> List[models.group_client_api.OutstandingInformationItem]:
        try:
            """Get outstanding information"""
            response = self._group_client_api(
                "GET",
                f"groupClient/cases/{case_id}/outstanding-information",
                user_id,
                "get_outstanding_information",
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: get_outstanding_information",
                extra={"method_name": "get_outstanding_information"},
                exc_info=error,
            )
            error.method_name = "get_outstanding_information"
            raise error
        return pydantic.parse_obj_as(
            List[models.group_client_api.OutstandingInformationItem], response.json()
        )

    def update_outstanding_information_as_received(
        self,
        user_id: str,
        case_id: str,
        outstanding_information: models.group_client_api.OutstandingInformationData,
    ) -> None:
        try:
            """Update outstanding information received"""
            self._group_client_api(
                "POST",
                f"groupClient/cases/{case_id}/outstanding-information-received",
                user_id,
                "update_outstanding_information_as_recieved",
                data=outstanding_information.json(exclude_none=True),
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: update_outstanding_information_as_received",
                extra={"method_name": "update_outstanding_information_as_received"},
                exc_info=error,
            )
            error.method_name = "update_outstanding_information_as_received"
            raise error

    def get_eform_summary(
        self, user_id: str, absence_id: str
    ) -> List[models.group_client_api.EFormSummary]:
        try:
            response = self._group_client_api(
                "GET", f"groupClient/cases/{absence_id}/eforms", user_id, "get_eform_summary"
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: get_eform_summary",
                extra={"method_name": "get_eform_summary"},
                exc_info=error,
            )
            error.method_name = "get_eform_summary"
            raise error
        json = response.json()
        # Workaround empty strings in response instead of null. These cause parse_obj to fail.
        return pydantic.parse_obj_as(List[models.group_client_api.EFormSummary], json)

    ## EXAMPLE
    def customer_get_eform_summary(self, user_id: str, absence_id: str) -> List[PFML_EFormSummary]:
        eform_summaries_v22_5_1 = self.customer_get_eform_summary_v22_5_1(user_id, absence_id)
        return [
            ModelMapper.map_eform_summary_v22_5_1_to_pfml_eform_summary(eform_summary)
            for eform_summary in eform_summaries_v22_5_1
        ]

    # v22_5_1-specific implementation of customer_get_eform_summary
    def customer_get_eform_summary_v22_5_1(
        self, user_id: str, absence_id: str
    ) -> List[EFormSummary_v22_5_1]:
        try:
            response = self._customer_api(
                "GET", f"customer/cases/{absence_id}/eForms", user_id, "customer_get_eform_summary"
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: customer_get_eform_summary",
                extra={"method_name": "customer_get_eform_summary"},
                exc_info=error,
            )
            error.method_name = "customer_get_eform_summary"
            raise error
        json = response.json()
        return pydantic.parse_obj_as(List[EFormSummary_v22_5_1], json)

    def get_eform(
        self, user_id: str, absence_id: str, eform_id: int
    ) -> models.group_client_api.EForm:
        try:
            response = self._group_client_api(
                "GET",
                f"groupClient/cases/{absence_id}/eforms/{eform_id}/readEform",
                user_id,
                "get_eform",
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: get_eform",
                extra={"method_name": "get_eform"},
                exc_info=error,
            )
            error.method_name = "get_eform"
            raise error
        json = response.json()
        return models.group_client_api.EForm.parse_obj(json)

    def customer_get_eform(self, user_id: str, absence_id: str, eform_id: str) -> PFML_EForm:
        eform_id_int = int(eform_id)
        eform_v22_5_1 = self.customer_get_eform_v22_5_1(user_id, absence_id, eform_id_int)
        pfml_eform = ModelMapper.map_eform_v22_5_1_to_pfml_eform(eform_v22_5_1)
        return pfml_eform

    # v22_5_1-specific implementation of customer_get_eform
    def customer_get_eform_v22_5_1(
        self, user_id: str, absence_id: str, eform_id: int
    ) -> EForm_v22_5_1:
        try:
            response = self._customer_api(
                "GET",
                f"customer/cases/{absence_id}/readEForm/{eform_id}",
                user_id,
                "customer_get_eform",
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: customer_get_eform",
                extra={"method_name": "customer_get_eform"},
                exc_info=error,
            )
            error.method_name = "customer_get_eform"
            raise error
        json = response.json()
        return EForm_v22_5_1.parse_obj(json)

    def create_eform(
        self, user_id: str, absence_id: str, eform: EFormBody
    ) -> models.group_client_api.EForm:
        encoded_eform_type = urllib.parse.quote(eform.eformType)
        try:
            response = self._group_client_api(
                "POST",
                f"groupClient/cases/{absence_id}/addEForm/{encoded_eform_type}",
                user_id,
                "create_eform",
                data=json.dumps(eform.eformAttributes),
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: create_eform",
                extra={"method_name": "create_eform"},
                exc_info=error,
            )
            error.method_name = "create_eform"
            raise error
        return models.group_client_api.EForm.parse_obj(response.json())

    def customer_create_eform(self, user_id: str, absence_id: str, eform: EFormBody) -> None:
        encoded_eform_type = urllib.parse.quote(eform.eformType)

        self._customer_api(
            "POST",
            f"customer/cases/{absence_id}/addEForm/{encoded_eform_type}",
            user_id,
            "customer_create_eform",
            data=json.dumps(eform.eformAttributes),
        )

    def get_customer_occupations_customer_api(
        self, user_id: str
    ) -> List[models.customer_api.ReadCustomerOccupation]:

        response = self._customer_api(
            "GET", "customer/occupations", user_id, "get_customer_occupations_customer_api"
        )

        json = response.json()

        return pydantic.parse_obj_as(List[models.customer_api.ReadCustomerOccupation], json)

    def get_case_occupations(
        self, user_id: str, case_id: str
    ) -> List[models.customer_api.ReadCustomerOccupation]:
        response = self._customer_api(
            "GET", f"customer/cases/{case_id}/occupations", user_id, "get_case_occupations"
        )

        json = response.json()

        return pydantic.parse_obj_as(List[models.customer_api.ReadCustomerOccupation], json)

    def get_customer_payment_preference(
        self, user_id: str
    ) -> models.customer_api.PaymentPreferenceCustomerResources:
        response = self._customer_api(
            "GET", "customer/payment-preferences", user_id, "get_customer_payment_preference"
        )
        json = response.json()
        return models.customer_api.PaymentPreferenceCustomerResources.parse_obj(json)

    def create_overpayment_recovery(self, overpayment_recovery: models.OverpaymentRecovery) -> str:
        xml_body = self._create_overpayment_recovery_payload(overpayment_recovery)

        response = self._wscomposer_request(
            "POST",
            "webservice",
            "create_overpayment_recovery",
            {"config": "COMAddOverpaymentActualRecovery"},
            xml_body,
        )

        response_decoded = self._decode_xml_response(
            add_overpayment_recovery_response_schema, response.text
        )

        # The service agreement service always returns a 200 status code,
        # even if it does not succeed. The presence of service errors
        # indicates failure.
        self._guard_against_service_errors(
            response_decoded,
            "create_overpayment_recovery",
            {"overpayment_case_number": overpayment_recovery.overpayment_case_number},
        )

        return response_decoded["oid-list"]["oid"][0]

    def create_customer_payment_preference(
        self, user_id: str, payment_preference: models.customer_api.CreatePaymentPreferenceCommand
    ) -> models.customer_api.PaymentPreferenceResource:
        response = self._customer_api(
            "POST",
            "customer/payment-preferences",
            user_id,
            "create_customer_payment_preference",
            data=payment_preference.json(exclude_none=True),
        )
        json = response.json()
        return models.customer_api.PaymentPreferenceResource.parse_obj(json)

    def update_customer_payment_preference(
        self,
        user_id: str,
        payment_preference_id: str,
        payment_preference: models.customer_api.EditPaymentPreferenceCommand,
    ) -> models.customer_api.PaymentPreferenceResource:
        response = self._customer_api(
            "POST",
            f"customer/payment-preferences/{payment_preference_id}/edit",
            user_id,
            "update_customer_payment_preference",
            data=payment_preference.json(exclude_none=True),
        )
        json = response.json()
        return models.customer_api.PaymentPreferenceResource.parse_obj(json)

    def update_occupation(
        self,
        occupation_id: int,
        employment_status: Optional[str],
        hours_worked_per_week: Optional[Decimal],
        fineos_org_unit_id: Optional[str],
        worksite_id: Optional[str],
    ) -> None:
        xml_body = self._create_update_occupation_payload(
            occupation_id, employment_status, hours_worked_per_week, fineos_org_unit_id, worksite_id
        )
        self._wscomposer_request(
            "POST",
            "webservice",
            "update_occupation",
            {"config": "OccupationDetailUpdateService"},
            xml_body,
        )

    @staticmethod
    def _create_overpayment_recovery_payload(
        overpayment_recovery: models.OverpaymentRecovery,
    ) -> str:

        additional_data_set = models.AdditionalDataSet()

        additional_data_set.additional_data.append(
            models.AdditionalData(
                name="OverPaymentCaseNumber", value=overpayment_recovery.overpayment_case_number
            )
        )

        additional_data_set.additional_data.append(
            models.AdditionalData(
                name="AmountOfRecovery", value=str(overpayment_recovery.amount_of_recovery)
            )
        )

        # Convert date_of_recovery to string in YYYY-MM-DD format
        date_of_recovery_str = overpayment_recovery.date_of_recovery.strftime("%Y-%m-%d")
        additional_data_set.additional_data.append(
            models.AdditionalData(name="DateOfRecovery", value=date_of_recovery_str)
        )

        additional_data_set.additional_data.append(
            models.AdditionalData(name="RecoveryMethod", value=overpayment_recovery.recovery_method)
        )

        # If recovery method is "Check", we need to include check_name and check_number
        if overpayment_recovery.check_name:
            additional_data_set.additional_data.append(
                models.AdditionalData(name="CheckName", value=overpayment_recovery.check_name)
            )

        if overpayment_recovery.check_number:
            additional_data_set.additional_data.append(
                models.AdditionalData(name="CheckNumber", value=overpayment_recovery.check_number)
            )

        overpayment_adjustment_data = models.AddOverpaymentAdjustmentData(
            additional_data_set=additional_data_set
        )

        service_request = models.AddOverpaymentAdjustmentRequest(
            update_data=overpayment_adjustment_data
        )

        payload = service_request.dict(by_alias=True)
        xml_element = cast(Element, add_overpayment_recovery_request_schema.encode(payload))

        return xml.etree.ElementTree.tostring(xml_element, encoding="unicode", xml_declaration=True)

    @staticmethod
    def _create_update_occupation_payload(
        occupation_id: int,
        employment_status: Optional[str],
        hours_worked_per_week: Optional[Decimal],
        fineos_org_unit_id: Optional[str],
        worksite_id: Optional[str],
    ) -> str:
        additional_data_set = models.AdditionalDataSet()

        # Occupation ID is the only identifier we use to specify which occupation record we want to update in FINEOS.
        additional_data_set.additional_data.append(
            models.AdditionalData(name="OccupationId", value=str(occupation_id))
        )

        # Application's hours_worked_per_week field is optional so we only update this value in FINEOS
        # if we've set it on the Application object in our database.
        if hours_worked_per_week:
            additional_data_set.additional_data.append(
                models.AdditionalData(name="HoursPerWeek", value=str(hours_worked_per_week))
            )

        if employment_status:
            additional_data_set.additional_data.append(
                models.AdditionalData(name="EmploymentStatus", value=employment_status)
            )

        if worksite_id:
            additional_data_set.additional_data.append(
                models.AdditionalData(name="workSiteId", value=worksite_id.split(":")[2])
            )

        if fineos_org_unit_id:
            additional_data_set.additional_data.append(
                models.AdditionalData(
                    name="OrganizationUnitId", value=fineos_org_unit_id.split(":")[2]
                )
            )

        # Put the XML object together properly.
        service_data = models.OccupationDetailUpdateData(additional_data_set=additional_data_set)

        service_request = models.OccupationDetailUpdateRequest(update_data=service_data)

        payload_as_dict = service_request.dict(by_alias=True)
        xml_element = cast(
            Element, occupation_detail_update_service_request_schema.encode(payload_as_dict)
        )

        return xml.etree.ElementTree.tostring(xml_element, encoding="unicode", xml_declaration=True)

    def upload_document(
        self,
        user_id: str,
        absence_id: str,
        document_type: str,
        file_content: bytes,
        file_name: str,
        content_type: str,
        description: str,
    ) -> models.customer_api.Document:
        """Upload a document to FINEOS using the Base64 endpoint, which accepts document content
        through a Base64-encoded string.

        FINEOS document uploads occur through an API Gateway --> Lambda function, so the max
        request size is 6MB. However, since base64 encoding can bloat the file size by up to
        33%, the effective file size is 4.5MB.

        The binary upload flag should be disabled on the FINEOS side when using this method;
        if it's enabled, the effective file size reduces even further to 3.4-3.6MB.
        """
        file_size = len(file_content)
        encoded_file_contents = base64.b64encode(file_content).decode("utf-8")
        file_name_root, file_extension = os.path.splitext(file_name)

        data = {
            "fileName": file_name_root,
            "fileExtension": file_extension.lower(),
            "base64EncodedFileContents": encoded_file_contents,
            "fileSizeInBytes": file_size,
            "description": description,
            "managedReqId": None,
        }

        # adding safe param also escapes '/'
        document_type = urllib.parse.quote(document_type.encode("utf-8"), safe="")

        response = self._customer_api(
            "POST",
            f"customer/cases/{absence_id}/documents/base64Upload/{document_type}",
            user_id,
            "upload_documents",
            json=data,
        )

        response_json = response.json()

        return models.customer_api.Document.parse_obj(
            fineos_document_empty_dates_to_none(response_json)
        )

    def upload_document_multipart(
        self,
        user_id: str,
        absence_id: str,
        document_type: str,
        file_content: bytes,
        file_name: str,
        content_type: str,
        description: str,
    ) -> models.customer_api.Document:
        """Upload a document through the multipart/form-data API endpoint.

        FINEOS document uploads occur through an API Gateway --> Lambda function.
        The binary upload flag must be enabled on the FINEOS side in order for this
        to function correctly; otherwise the documents will be blank when uploaded.

        The max request size is 6MB (limited by AWS Lambda); however, in practice,
        testing indicates that 4.5MB is the effective file size limit.
        """
        multipart_data = (
            ("documentContents", (file_name, file_content, content_type)),
            ("documentDescription", (None, description)),
        )

        # adding safe param also escapes '/'
        document_type = urllib.parse.quote(document_type.encode("utf-8"), safe="")

        response = self._customer_api(
            "POST",
            f"customer/cases/{absence_id}/documents/upload/{document_type}",
            user_id,
            "upload_document_multipart",
            # Ensure that the Content-Type is not set manually;
            # the requests library automatically adds the multipart/form-data header
            # and includes a generated boundary that separates the data parts specified above.
            header_content_type=None,
            files=multipart_data,
        )

        response_json = response.json()

        return models.customer_api.Document.parse_obj(
            fineos_document_empty_dates_to_none(response_json)
        )

    def group_client_get_documents(
        self, user_id: str, absence_id: str
    ) -> List[models.group_client_api.GroupClientDocument]:
        try:
            header_content_type = None

            fineos_is_running_v24 = features.get_config().fineos.is_running_v24

            if fineos_is_running_v24:
                response = self._group_client_api(
                    "GET",
                    f"groupClient/cases/{absence_id}/document-metas?_subcaseDocuments=true",
                    user_id,
                    "group_client_get_documents",
                    header_content_type=header_content_type,
                )
            else:
                response = self._group_client_api(
                    "GET",
                    f"groupClient/cases/{absence_id}/documents?_filter=includeChildCases",
                    user_id,
                    "group_client_get_documents",
                    header_content_type=header_content_type,
                )

        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: group_client_get_documents",
                extra={"method_name": "group_client_get_documents"},
                exc_info=error,
            )
            error.method_name = "group_client_get_documents"
            raise error

        documents = response.json()
        if "elements" in documents:
            documents = documents.get("elements", [])

        return [
            models.group_client_api.GroupClientDocument.parse_obj(
                parse_fineos_document_for_customer(doc)
                if fineos_is_running_v24
                else fineos_document_empty_dates_to_none(doc)
            )
            for doc in documents
        ]

    def get_managed_requirements(
        self, user_id: str, absence_id: str
    ) -> List[models.group_client_api.ManagedRequirementDetails]:
        try:
            header_content_type = None

            response = self._group_client_api(
                "GET",
                f"groupClient/cases/{absence_id}/managedRequirements",
                user_id,
                "get_managed_requirements",
                header_content_type=header_content_type,
            )

        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: get_managed_requirements",
                extra={"method_name": "get_managed_requirements"},
                exc_info=error,
            )
            error.method_name = "get_managed_requirements"
            raise error
        managed_reqs = response.json()

        return [
            models.group_client_api.ManagedRequirementDetails.parse_obj(req) for req in managed_reqs
        ]

    def get_documents(
        self, user_id: str, absence_id: Optional[str] = None, api_params: Optional[Dict] = None
    ) -> List[models.customer_api.Document]:
        header_content_type = None

        try:
            if absence_id is not None:
                response = self._customer_api(
                    "GET",
                    f"customer/cases/{absence_id}/documents?includeChildCases=True",
                    user_id,
                    "get_documents",
                    header_content_type=header_content_type,
                )
            else:
                # {"name": "1099-G documents","sort": "asc"} -> name=1099-G documents&sort=asc'
                query_string = urlencode(api_params) if api_params else ""

                fineos_is_running_v24 = features.get_config().fineos.is_running_v24
                if fineos_is_running_v24:
                    response = self._customer_api(
                        "GET",
                        f"customer/document-metas?{query_string}",
                        user_id,
                        "get_documents",
                        header_content_type=header_content_type,
                    )
                else:
                    response = self._customer_api(
                        "GET",
                        f"customer/documents?{query_string}",
                        user_id,
                        "get_documents",
                        header_content_type=header_content_type,
                    )
        except exception.FINEOSClientBadResponse as finres:
            # See API-1198
            if finres.response_status == requests.codes.FORBIDDEN:
                logger.warning(
                    "FINEOS API responded with 403. Returning empty documents list",
                    extra={
                        "absence_id": absence_id,
                        "absence_case_id": absence_id,
                        "user_id": user_id,
                    },
                )
                return []

        # customer/documents returns list of docs inside "elements"
        documents = response.json() if absence_id else response.json().get("elements", [])

        return [
            models.customer_api.Document.parse_obj(
                fineos_document_empty_dates_to_none(doc)
                if absence_id
                else parse_fineos_document_for_customer(doc)
            )
            for doc in documents
        ]

    def download_document_as_leave_admin(
        self, user_id: str, absence_id: str, fineos_document_id: str
    ) -> models.group_client_api.Base64EncodedFileData:
        try:
            header_content_type = None
            fineos_is_running_v24 = features.get_config().fineos.is_running_v24
            if fineos_is_running_v24:
                fineos_document_id = self._prepare_document_id(fineos_document_id)
            response = self._group_client_api(
                "GET",
                f"groupClient/cases/{absence_id}/documents/{fineos_document_id}/base64Download",
                user_id,
                "download_document_as_leave_admin",
                header_content_type=header_content_type,
            )
        except exception.FINEOSClientError as error:
            logger.error(
                "FINEOS Client Exception: download_document_as_leave_admin",
                extra={"method_name": "download_document_as_leave_admin"},
                exc_info=error,
            )
            error.method_name = "download_document_as_leave_admin"
            raise error
        response_json = response.json()
        # populate spec required field missing in fineos response
        if "fileSizeInBytes" not in response_json:
            response_json["fileSizeInBytes"] = len(
                base64.b64decode(response_json["base64EncodedFileContents"].encode("ascii"))
            )
        return models.group_client_api.Base64EncodedFileData.parse_obj(response_json)

    def download_document(
        self, user_id: str, fineos_document_id: str, absence_id: Optional[str] = None
    ) -> models.customer_api.Base64EncodedFileData:
        header_content_type = None
        fineos_is_running_v24 = features.get_config().fineos.is_running_v24
        if fineos_is_running_v24:
            fineos_document_id = self._prepare_document_id(fineos_document_id)
        if absence_id is not None:
            response = self._customer_api(
                "GET",
                f"customer/cases/{absence_id}/documents/{fineos_document_id}/base64Download",
                user_id,
                "download_document",
                header_content_type=header_content_type,
            )
        else:
            response = self._customer_api(
                "GET",
                f"customer/document/{fineos_document_id}/base64Download",
                user_id,
                "download_document",
                header_content_type=header_content_type,
            )

        response_json = response.json()
        # populate spec required field missing in fineos response
        if "fileSizeInBytes" not in response_json:
            response_json["fileSizeInBytes"] = len(
                base64.b64decode(response_json["base64EncodedFileContents"].encode("ascii"))
            )

        return models.customer_api.Base64EncodedFileData.parse_obj(response_json)

    def mark_document_as_received(
        self, user_id: str, absence_id: str, fineos_document_id: str
    ) -> None:
        self._customer_api(
            "POST",
            f"customer/cases/{absence_id}/documents/{fineos_document_id}/doc-received-for-outstanding-supporting-evidence",
            user_id,
            "mark_document_as_recieved",
        )

    def get_outstanding_evidence(
        self, user_id: str, case_id: str
    ) -> list[OutstandingSupportingEvidence]:
        response = self._customer_api(
            "GET",
            f"customer/cases/{case_id}/outstanding-supporting-evidence",
            user_id,
            "get_outstanding_evidence",
        )

        return [OutstandingSupportingEvidence.parse_obj(evidence) for evidence in response.json()]

    def get_week_based_work_pattern(
        self, user_id: str, occupation_id: Union[str, int]
    ) -> models.customer_api.WeekBasedWorkPattern:

        response = self._customer_api(
            "GET",
            f"customer/occupations/{occupation_id}/week-based-work-pattern",
            user_id,
            "get_week_based_work_pattern",
        )

        json = response.json()

        return models.customer_api.WeekBasedWorkPattern.parse_obj(json)

    def add_week_based_work_pattern(
        self,
        user_id: str,
        occupation_id: Union[str, int],
        week_based_work_pattern: models.customer_api.WeekBasedWorkPattern,
    ) -> models.customer_api.WeekBasedWorkPattern:

        response = self._customer_api(
            "POST",
            f"customer/occupations/{occupation_id}/week-based-work-pattern",
            user_id,
            "add_week_based_work_pattern",
            data=week_based_work_pattern.json(exclude_none=True),
        )

        json = response.json()

        return models.customer_api.WeekBasedWorkPattern.parse_obj(json)

    def update_week_based_work_pattern(
        self,
        user_id: str,
        occupation_id: Union[str, int],
        week_based_work_pattern: models.customer_api.WeekBasedWorkPattern,
    ) -> models.customer_api.WeekBasedWorkPattern:

        response = self._customer_api(
            "POST",
            f"customer/occupations/{occupation_id}/week-based-work-pattern/replace",
            user_id,
            "update_week_based_work_pattern",
            data=week_based_work_pattern.json(exclude_none=True),
        )

        json = response.json()

        return models.customer_api.WeekBasedWorkPattern.parse_obj(json)

    def submit_intermittent_leave_episode(
        self,
        web_id: str,
        absence_id: str,
        elements: CreateActualAbsencePeriodCommandElements,
    ) -> List[ActualAbsencePeriodResource]:
        response = self._customer_api(
            "POST",
            f"customer/absence/absences/{absence_id}/actual-absence-periods/bulk-create",
            web_id,
            "submit_intermittent_leave_episode",
            data=elements.json(exclude_none=True),
        )

        # returns list of absence periods inside "elements"

        absence_periods = response.json().get("elements", [])

        return [
            models.customer_api.ActualAbsencePeriodResource.parse_obj(absence_period)
            for absence_period in absence_periods
        ]

    def get_actual_absence_period_resources(
        self,
        user_id: str,
        absence_id: str,
    ) -> ActualAbsencePeriodResources:
        response = self._customer_api(
            "GET",
            f"customer/absence/absences/{absence_id}/actual-absence-periods",
            user_id,
            "get_actual_absence_period_resources",
        )

        actual_absence_period_resources = (
            models.customer_api.ActualAbsencePeriodResources.parse_obj(response.json())
        )
        return actual_absence_period_resources

    def create_or_update_leave_admin(
        self, leave_admin_create_or_update: models.CreateOrUpdateLeaveAdmin
    ) -> Tuple[Optional[str], Optional[str]]:
        """Create or update a leave admin in FINEOS."""
        xml_body = self._create_or_update_leave_admin_payload(leave_admin_create_or_update)
        response = self._integration_services_api(
            "POST",
            "rest/externalUserProvisioningService/createOrUpdateEmployerViewpointUser",
            self.wscomposer_user_id,
            "create_or_update_leave_admin",
            data=xml_body.encode("utf-8"),
        )
        response_decoded = self._decode_xml_response(
            create_or_update_leave_admin_response_schema, response.text
        )
        if response_decoded["ns2:errorCode"]:
            logger.warning(
                "create_or_update_leave_admin - FINEOS API responded with an error",
                extra={
                    "fineos_web_id": leave_admin_create_or_update.fineos_web_id,
                    "error": response_decoded["ns2:errorMessage"],
                },
            )
        return response_decoded["ns2:errorCode"], response_decoded["ns2:errorMessage"]

    def create_or_update_employer(
        self,
        employer_create_or_update: models.CreateOrUpdateEmployer,
        existing_organization: Optional[models.OCOrganisationItem] = None,
    ) -> Tuple[str, int]:
        """Create or update an employer in FINEOS."""
        xml_body = self._create_or_update_employer_payload(
            employer_create_or_update,
            existing_organization,
        )
        response = self._wscomposer_request(
            "POST",
            "webservice",
            "create_or_update_employer",
            {"config": "UpdateOrCreateParty"},
            xml_body,
        )
        response_decoded = self._decode_xml_response(
            update_or_create_party_response_schema, response.text
        )
        # The value returned in CUSTOMER_NUMBER is the organization's primary key
        # in FINEOS which we store as fineos_employer_id in the employer model.
        fineos_employer_id: dict = next(
            (
                item
                for item in response_decoded["additional-data-set"]["additional-data"]
                if item["name"] == "CUSTOMER_NUMBER"
            ),
            {},
        )

        if fineos_employer_id == {}:
            response_code: dict = next(
                (
                    item
                    for item in response_decoded["additional-data-set"]["additional-data"]
                    if item["name"] == "SERVICE_RESPONSE_CODE"
                ),
                {},
            )
            response_code_value = response_code.get("value")
            validation_msg: dict = next(
                (
                    item
                    for item in response_decoded["additional-data-set"]["additional-data"]
                    if item["name"] == "VALIDATION_MESSAGE"
                ),
                {},
            )
            validation_msg_value = validation_msg.get("value")
            raise exception.FINEOSFatalResponseError(
                "create_or_update_employer",
                Exception(
                    f"Employer not created. Response Code: {response_code_value}, {validation_msg_value}"
                ),
            )
        else:
            fineos_employer_id_value = fineos_employer_id.get("value")
            logger.info(
                f"Employer ID created or updated is {fineos_employer_id_value} for "
                f"CustomerNo {employer_create_or_update.fineos_customer_nbr}"
            )
            fineos_employer_id_int = int(str(fineos_employer_id_value))

        return employer_create_or_update.fineos_customer_nbr, fineos_employer_id_int

    def create_or_update_leave_period_change_request(
        self,
        fineos_web_id: str,
        absence_id: str,
        change_request: CreateLeavePeriodsChangeRequestCommand,
    ) -> LeavePeriodsChangeRequestResource:
        # NOTE: this call will not work in some envs until https://lwd.atlassian.net/browse/PFMLPB-2055 is complete
        response = self._customer_api(
            "POST",
            f"customer/absence/absences/{absence_id}/leave-periods-change-requests",
            fineos_web_id,
            "create_or_update_leave_period_change_request",
            data=change_request.json(exclude_none=True),
        )
        response_json = response.json()
        return LeavePeriodsChangeRequestResource.parse_obj(response_json)

    @staticmethod
    def _create_or_update_leave_admin_payload(
        leave_admin_create_or_update: models.CreateOrUpdateLeaveAdmin,
    ) -> str:
        leave_admin_phone = models.PhoneNumber(
            area_code=leave_admin_create_or_update.admin_area_code,
            contact_number=leave_admin_create_or_update.admin_phone_number,
            extension_code=leave_admin_create_or_update.admin_phone_extension,
        )
        leave_admin_create_payload = models.CreateOrUpdateLeaveAdminRequest(
            full_name=leave_admin_create_or_update.admin_full_name,
            party_reference=str(leave_admin_create_or_update.fineos_employer_id),
            user_id=leave_admin_create_or_update.fineos_web_id,
            email=leave_admin_create_or_update.admin_email,
            phone=leave_admin_phone,
            enabled=leave_admin_create_or_update.admin_enabled,
        )

        # NOTE: By default, we are enabling the leave administrator. If you want to update leave admin information
        #       without re-enabling the leave admin, you _must_ explicitly pass in the `admin_enabled="0"` flag.
        leave_admin_create_payload.enabled = leave_admin_create_or_update.admin_enabled or "1"
        payload_as_dict = leave_admin_create_payload.dict(by_alias=True)
        xml_element = create_or_update_leave_admin_request_schema.encode(payload_as_dict)
        return xml.etree.ElementTree.tostring(
            cast(Element, xml_element), encoding="unicode", xml_declaration=True
        )

    @staticmethod
    def _create_or_update_employer_payload(
        employer_create_or_update: models.CreateOrUpdateEmployer,
        existing_organization: Optional[models.OCOrganisationItem] = None,
    ) -> str:

        organization_default = models.OCOrganisationDefaultItem(
            CustomerNo=employer_create_or_update.fineos_customer_nbr,
            CorporateTaxNumber=employer_create_or_update.employer_fein,
            DoingBusinessAs=employer_create_or_update.employer_dba,
            LegalBusinessName=employer_create_or_update.employer_legal_name,
            Name=employer_create_or_update.employer_legal_name,
            ShortName=employer_create_or_update.employer_legal_name[0:8],
            UpperName=employer_create_or_update.employer_legal_name.upper(),
            UpperShortName=employer_create_or_update.employer_legal_name[0:8].upper(),
        )

        # The ReadEmployer endpoint doesn't return an OCOrganisationDefaultItem
        # as a part of its response, so using the top-level attributes to
        # populate the OCOrganisationDefaultItem in the update
        # OAR Dec 18, 2020 - Removed Name from sync. DOR is source of truth for legal name.
        if existing_organization:
            organization_default.PronouncedAs = existing_organization.PronouncedAs
            organization_default.AccountingDate = existing_organization.AccountingDate
            organization_default.FinancialYearEnd = existing_organization.FinancialYearEnd
            organization_default.PartyType = existing_organization.PartyType
            organization_default.DateBusinessCommenced = existing_organization.DateBusinessCommenced
            organization_default.DateOfIncorporation = existing_organization.DateOfIncorporation
            organization_default.GroupClient = existing_organization.GroupClient
            organization_default.SecuredClient = existing_organization.SecuredClient
            organization_default.NotificationIssued = existing_organization.NotificationIssued

        organization_name = models.OCOrganisationNameItem(
            DoingBusinessAs=employer_create_or_update.employer_dba,
            LegalBusinessName=employer_create_or_update.employer_legal_name,
            Name=employer_create_or_update.employer_legal_name,
            ShortName=employer_create_or_update.employer_legal_name[0:8],
            UpperName=employer_create_or_update.employer_legal_name.upper(),
            UpperShortName=employer_create_or_update.employer_legal_name[0:8].upper(),
            organisationWithDefault=models.OCOrganisationWithDefault(
                OCOrganisation=[organization_default]
            ),
        )

        # The ReadEmployer endpoint doesn't return an OCOrganisationName as a
        # part of its response, so using the top-level attributes to populate
        # the OCOrganisationName in the update
        # OAR Dec 18, 2020 - Removed Name from sync. DOR is source of truth for legal name.
        if existing_organization:
            organization_name.PronouncedAs = existing_organization.PronouncedAs

        organization = models.OCOrganisationItem(
            CustomerNo=employer_create_or_update.fineos_customer_nbr,
            CorporateTaxNumber=employer_create_or_update.employer_fein,
            DoingBusinessAs=employer_create_or_update.employer_dba,
            LegalBusinessName=employer_create_or_update.employer_legal_name,
            Name=employer_create_or_update.employer_legal_name,
            ShortName=employer_create_or_update.employer_legal_name[0:8],
            UpperName=employer_create_or_update.employer_legal_name.upper(),
            UpperShortName=employer_create_or_update.employer_legal_name[0:8].upper(),
            names=models.OCOrganisationName(OCOrganisationName=[organization_name]),
        )

        if existing_organization:
            organization.PronouncedAs = existing_organization.PronouncedAs
            organization.AccountingDate = existing_organization.AccountingDate
            organization.FinancialYearEnd = existing_organization.FinancialYearEnd
            organization.PartyType = existing_organization.PartyType
            organization.DateBusinessCommenced = existing_organization.DateBusinessCommenced
            organization.DateOfIncorporation = existing_organization.DateOfIncorporation
            organization.GroupClient = existing_organization.GroupClient
            organization.SecuredClient = existing_organization.SecuredClient
            organization.NotificationIssued = existing_organization.NotificationIssued

        party_dto = models.PartyIntegrationDTOItem(
            organisation=models.OCOrganisation(OCOrganisation=[organization])
        )

        employer_create_payload = models.UpdateOrCreatePartyRequest(
            update_data=models.UpdateData(PartyIntegrationDTO=[party_dto])
        )

        payload_as_dict = employer_create_payload.dict(by_alias=True)

        """
        Relevant tickets: EDM-291, CPS-2703
        After updating the OCOrganisation model to include the organisationUnits field for the ReadEmployer.Response, it did not match the previous OCOrganisation schema used for UpdateOrCreateParty.Request.
        TODO Once FINEOS adds the organisationUnits field in the UpdateOrCreateParty.Request to allow creation of employers with a predefined list of org. units, the code deleting the organisationUnits key needs to be removed.
        TODO The XSDS and the test file of expected XML will also need to be regenerated.
        """
        del payload_as_dict["update-data"]["PartyIntegrationDTO"][0]["organisation"][
            "OCOrganisation"
        ][0]["organisationUnits"]
        del payload_as_dict["update-data"]["PartyIntegrationDTO"][0]["organisation"][
            "OCOrganisation"
        ][0]["names"]["OCOrganisationName"][0]["organisationWithDefault"]["OCOrganisation"][0][
            "organisationUnits"
        ]
        # Remove OID from the payload as it is not required for creating or updating an employer
        oc_organisation = payload_as_dict["update-data"]["PartyIntegrationDTO"][0]["organisation"][
            "OCOrganisation"
        ][0]
        if "OID" in oc_organisation:
            del oc_organisation["OID"]

        xml_element = cast(Element, update_or_create_party_request_schema.encode(payload_as_dict))
        return xml.etree.ElementTree.tostring(xml_element, encoding="unicode", xml_declaration=True)

    def create_service_agreement_for_employer(
        self,
        fineos_employer_id: int,
        service_agreement_inputs: models.CreateOrUpdateServiceAgreement,
    ) -> str:
        """Create a Service Agreement for an employer in FINEOS."""
        xml_body = self._create_service_agreement_payload(
            fineos_employer_id, service_agreement_inputs
        )

        response = self._wscomposer_request(
            "POST",
            "webservice",
            "create_service_agreement_for_employer",
            {"config": "ServiceAgreementService"},
            xml_body,
        )

        response_decoded = self._decode_xml_response(
            service_agreement_service_response_schema, response.text
        )

        # The service agreement service always returns a 200 status code,
        # even if it does not succeed. The presence of service errors
        # indicates failure.
        self._guard_against_service_errors(
            response_decoded,
            "create_service_agreement_for_employer",
            {"fineos_employer_id": fineos_employer_id},
        )

        # The value returned in ServiceAgreementRevisionCaseNumber is the SA's revision case number
        # in FINEOS which we store as fineos_service_agreement_case_number in the fineos_service_agreement model.
        # The revision case number is not present in every request so we must also
        # check for the ServiceAgreementCaseNumber in the response.
        fineos_service_agreement_case_number = find_in_list(
            # Sorting the list in reverse order ensures that ServiceAgreementRevisionCaseNumber is used if somehow both
            # values are present.
            sorted(
                response_decoded["additional-data-set"]["additional-data"],
                key=lambda item: item["name"],
                reverse=True,
            ),
            lambda item: item["name"]
            in ["ServiceAgreementRevisionCaseNumber", "ServiceAgreementCaseNumber"],
        )

        if not fineos_service_agreement_case_number:
            raise exception.FINEOSFatalResponseError(
                "create_service_agreement_for_employer",
                Exception(
                    f"Could not create service agreement ServiceAgreementRevisionCaseNumber and ServiceAgreementCaseNumber are empty for FINEOS employer id: {fineos_employer_id}"
                ),
            )

        logger.info(
            "Service agreement response info",
            extra={
                "fineos_employer_id": fineos_employer_id,
                "fineos_case_number_field_name": fineos_service_agreement_case_number.get("name"),
                "fineos_case_number_field_value": fineos_service_agreement_case_number.get("value"),
            },
        )

        fineos_activity_messages = find_in_list(
            response_decoded["additional-data-set"]["additional-data"],
            lambda item: item["name"] == "ActivityMessages",
        )

        if not isinstance(fineos_activity_messages, dict):
            raise exception.FINEOSClientBadResponse(
                "create_service_agreement_for_employer",
                0,
                0,
                message=f"No activity message found in service agreement submission for FINEOS employer id: {fineos_employer_id}",
            )

        # The response to a successfully created service agreement should include the data below in the additional details
        # <additional-data>
        #     <name>ActivityMessages</name>
        #     <value>Created Service Agreement (SA). Case Number: <CASE_NUMBER>, Stage Name: Pending, Phase: Pending. Linking leave plan found using short name 'MA PFML - Family' to the service agreement. LeavePlan DisplayReference: <REFERENCE> LongName: <LONG_NAME>. Service agreement details have been set. Attempting to progress case forward from stage 'Pending' to 'Review'. Case Number: <CASE_NUMBER>, Stage Name: Pending, Phase: Pending. After attempting to progress case foward from stage 'Pending' to 'Review'. Steps : [Pending, Review], Case Number: <CASE_NUMBER>, Stage Name: Review, Phase: Pending. Attempting to progress case forward from stage 'Review' to 'Active'. Case Number: <CASE_NUMBER>, Stage Name: Review, Phase: Pending. After attempting to progress case foward from stage 'Review' to 'Active'. Steps : [Review, Active], Case Number: <CASE_NUMBER>, Stage Name: Active, Phase: Decided. Service agreement has been moved to 'Active' stage. Created Basic Master Plan: <MASTER_PLAN></value>
        # </additional-data>
        # The presence of the "Stage Name: Active" string indicates that the SA was successfully created
        # If "Stage Name: Active" is absent the SA agreement was not successfully created
        # This can happen when creating a SA agreement revision using an inactive leave plan for a new employer
        if "Stage Name: Active" not in fineos_activity_messages.get("value", ""):
            message = fineos_activity_messages.get("value", "")
            raise exception.FINEOSClientBadResponse(
                "create_service_agreement_for_employer",
                0,
                0,
                message=f"Created service agreement is not active FINEOS employer id: {fineos_employer_id}, activity messages: `{message}`",
            )

        return fineos_service_agreement_case_number.get("value")

    @staticmethod
    def _create_service_agreement_payload(
        fineos_employer_id: int, service_agreement_inputs: models.CreateOrUpdateServiceAgreement
    ) -> str:
        fineos_employer_id_data = models.AdditionalData(
            name="CustomerNumber", value=str(fineos_employer_id)
        )
        unlink_leave_plans_data = models.AdditionalData(
            name="UnlinkAllExistingLeavePlans",
            value=str(service_agreement_inputs.unlink_leave_plans),
        )
        version_data = models.AdditionalData(
            name="Version", value=str(service_agreement_inputs.version)
        )

        additional_data_set = models.AdditionalDataSet()
        additional_data_set.additional_data.append(fineos_employer_id_data)
        additional_data_set.additional_data.append(unlink_leave_plans_data)
        additional_data_set.additional_data.append(version_data)

        if service_agreement_inputs.absence_management_flag is not None:
            additional_data_set.additional_data.append(
                models.AdditionalData(
                    name="AbsenceManagement",
                    value=str(service_agreement_inputs.absence_management_flag),
                )
            )
        if service_agreement_inputs.leave_plans is not None:
            leave_plans_data = models.AdditionalData(
                name="LeavePlans", value=service_agreement_inputs.leave_plans
            )
            additional_data_set.additional_data.append(leave_plans_data)

        if service_agreement_inputs.start_date:
            additional_data_set.additional_data.append(
                models.AdditionalData(
                    name="StartDate", value=service_agreement_inputs.start_date.isoformat()
                )
            )
        if service_agreement_inputs.end_date:
            additional_data_set.additional_data.append(
                models.AdditionalData(
                    name="EndDate", value=service_agreement_inputs.end_date.isoformat()
                )
            )

        service_data = models.ServiceAgreementData(additional_data_set=additional_data_set)

        service_request = models.ServiceAgreementServiceRequest(update_data=service_data)

        payload_as_dict = service_request.dict(by_alias=True)

        xml_element = cast(
            Element, service_agreement_service_request_schema.encode(payload_as_dict)
        )
        return xml.etree.ElementTree.tostring(xml_element, encoding="unicode", xml_declaration=True)

    @staticmethod
    def _create_tax_preference_payload(absence_id, tax_preference):

        additional_data_set = models.AdditionalDataSet()

        additional_data_set.additional_data.append(
            models.AdditionalData(name="AbsenceCaseNumber", value=str(absence_id))
        )
        additional_data_set.additional_data.append(
            models.AdditionalData(name="FlagValue", value=str(tax_preference))
        )

        tax_data = models.TaxWithholdingUpdateData(additional_data_set=additional_data_set)

        service_request = models.TaxWithholdingUpdateRequest(update_data=tax_data)

        payload = service_request.dict(by_alias=True)
        xml_element = cast(Element, update_tax_withholding_pref_request_schema.encode(payload))
        return xml.etree.ElementTree.tostring(xml_element, encoding="unicode", xml_declaration=True)

    def _guard_against_service_errors(
        self, response: dict, method_name: str, log_extra: Optional[dict] = None
    ) -> None:
        service_errors = find_in_list(
            response["additional-data-set"]["additional-data"],
            lambda item: item["name"] == "ServiceErrors",
        )

        if service_errors is not None:
            self._handle_service_err(service_errors, method_name, log_extra)

    @staticmethod
    def _handle_service_err(
        service_errors_obj: dict, method_name: str, log_extra: Optional[dict] = None
    ) -> NoReturn:
        fineos_err = (
            service_errors_obj["value"]
            if service_errors_obj
            else "Failed to retrieve FINEOS err msg"
        )

        logger.warning(f"FINEOS API responded with an error: {fineos_err}", extra=log_extra)
        raise exception.FINEOSServiceError(method_name, fineos_err)

    def send_tax_withholding_preference(self, absence_id: str, tax_preference: bool) -> None:
        """Update tax withholding preference in FINEOS."""
        xml_body = self._create_tax_preference_payload(absence_id, tax_preference)

        response = self._wscomposer_request(
            "POST",
            "webservice",
            "send_tax_withholding_preference",
            {"config": "OptInSITFITService"},
            xml_body,
        )
        response_decoded = self._decode_xml_response(
            update_tax_withholding_pref_response_schema, response.text
        )

        # The OptInSITFITService can return a 200 status code even if it does
        # not succeed. The presence of service errors indicates failure.
        self._guard_against_service_errors(
            response_decoded,
            "send_tax_withholding_preference",
            {"absence_id": absence_id, "absence_case_id": absence_id},
        )

    def read_tax_withholding_preference(self, absence_id: str) -> Optional[bool]:
        """Read tax withholding preference from FINEOS."""
        response = self._wscomposer_request(
            "GET",
            "COMReadPaidLeaveInstruction",
            "read_tax_withholding_preference",
            {"param_str_casenumber": absence_id},
            "",
        )

        response_decoded = self._decode_xml_response(
            read_tax_withholding_pref_response_schema, response.text
        )

        # The service can return a 200 status code even if it does
        # not succeed. The presence of service errors indicates failure.
        self._guard_against_service_errors(
            response_decoded,
            "read_tax_withholding_preference",
            {"absence_id": absence_id, "absence_case_id": absence_id},
        )

        case = self._find_matching_case(response_decoded, absence_id)
        return self._find_tax_withholding_value(case)

    @staticmethod
    def _find_matching_case(container: Dict[str, Any], absence_id: str) -> Dict[str, Any]:
        cases = container["OCCase"]

        for case in cases:
            if case["CaseNumber"] == absence_id:
                return case

        raise ValueError("Could not find case data from FINEOS response")

    @staticmethod
    def _find_tax_withholding_value(case: Dict[str, Any]) -> Optional[bool]:
        leave_requests = deepget(case, "caseProxies.OCCaseForAbsence.0.leaveRequests.LeaveRequest")

        if len(leave_requests) == 0:
            raise ValueError("Could not find leave request from FINEOS response")

        results = [
            deepget(
                request,
                "selectedLeavePlans.SelectedLeavePlan.0.paidLeaveInstruction.PaidLeaveInstruction.0.SITFITOptIn",
            )
            for request in leave_requests
        ]

        # If multiple values are found for different leave requests under the case, we have no way of knowing
        # which value is appropriate -- the OID for these leave requests do not match the leave_request_id or any of
        # the AbsencePeriod data that we get from `fineos.get_absence`.
        if len(set(results)) > 1:
            raise ValueError("Multiple tax withholding values found for leave request")

        result = results[0]

        if result is not None and not isinstance(result, bool):
            raise TypeError(f"SITFITOptIn value is of type {type(result).__name__}, expected bool")

        return result

    @staticmethod
    def _create_appeal_payload(absence_id: str, user_id: str, password: str) -> str:
        payload = re.sub(r"{username}", user_id, create_appeal_template)
        payload = re.sub(r"{password}", password, payload)
        payload = re.sub(r"{absence_id}", absence_id, payload)
        return payload

    @staticmethod
    def _parse_xml_response(response_text: str, absence_id: str) -> Any:
        try:
            # extract xml
            xml_start_regex = re.compile(r"<\?xml.*", re.DOTALL)
            result = re.search(xml_start_regex, response_text)
            if not result:
                raise ValueError("XML body not found in response.")
            xml_body = result.group()
            # remove closing MIMEBoundary
            xml_body = re.sub(r"--MIME.*", "", xml_body)
            return defusedxml.ElementTree.fromstring(xml_body)
        except Exception as error:
            logger.warning(
                "Error parsing XML from FINEOS response",
                extra={
                    "absence_case_id": absence_id,
                },
            )
            raise error

    def create_appeal_case(self, absence_id: str) -> Optional[str]:
        """Create appeal case in FINEOS."""
        xml_request_body = self._create_appeal_payload(
            absence_id=absence_id, user_id=self.soap_user_id, password=self.soap_password
        )
        response = self._integration_services_soap_api(
            "POST",
            "services/CaseServices",
            self.soap_user_id,
            "create_appeal_case",
            "urn:createCase",
            data=xml_request_body.encode("utf-8"),
        )
        xml_response_body = self._parse_xml_response(response.text, absence_id)
        # There may be multiple CaseNumbers, but the new appeal id will be first
        case_number = xml_response_body.find(".//CaseNumber")
        if case_number is None:
            raise ValueError("Case number not found in response.")
        return case_number.text

    def update_reflexive_questions(
        self,
        user_id: str,
        absence_id: Optional[str],
        additional_information: models.customer_api.AdditionalInformation,
    ) -> None:
        """Update reflexive questions."""
        self._customer_api(
            "POST",
            f"customer/absence/{absence_id}/reflexive-questions",
            user_id,
            "update_reflexive_questions",
            data=additional_information.json(exclude_none=True),
        )

    def upload_document_to_dms(self, file_name: str, file: bytes, data: Any) -> Response:
        """Upload document to FINEOS"""

        return self._integration_services_api(
            "POST",
            "api/v1/document/uploadAndIndexDocumentToFineosDMS",
            self.wscomposer_user_id,
            "upload_document_to_dms",
            header_content_type=None,
            files={
                "file": (file_name, file, "application/pdf"),
                "documentCreationRequest": (
                    "documentCreationRequest",
                    json.dumps(data),
                    "application/json",
                ),
            },
        )

    def create_task(
        self,
        work_type: WorkTypeIdentifier,
        case: CaseIdentifier,
        activity_subject: Optional[ActivitySubjectIdentifier],
        description: Optional[str],
    ) -> CreateTaskResponse:
        """Create a task for an absence case in FINEOS. Returns CreateTaskResponse
        or raises an exception if task creation failed."""

        xml_body = self._create_task_payload(
            username=self.soap_user_id,
            password=self.soap_password,
            work_type=work_type,
            case=case,
            activity_subject=activity_subject,
            description=description,
        )

        log_details = {"work_type_name": work_type.Name, "absence_case_id": case.CaseNumber}

        try:
            response = self._activity_services_request(ActivityServiceMethod.CREATE_TASK, xml_body)
            xml_response = self._parse_xml_response(response.text, case.CaseNumber)
            response_decoded = self._decode_xml_response(
                fineos_activity_services_response_schema, xml_response
            )
            return CreateTaskResponse.parse_obj(response_decoded)

        except exception.FINEOSFatalResponseError as err:
            if (
                err.response_status == 500
                and "<errorMessage>A case with Case Number" in err.message  # noqa: B306
            ):
                logger.error(
                    "Create task request to FINEOS failed: absence case not found.",
                    extra=log_details,
                )
            elif (
                err.response_status == 500
                and "<errorMessage>No result found for Work Type supplied."
                in err.message  # noqa: B306
            ):
                logger.error(
                    "Create task request to FINEOS failed: invalid task name.", extra=log_details
                )
            raise

    @staticmethod
    def _create_task_payload(
        username: str,
        password: str,
        work_type: WorkTypeIdentifier,
        case: CaseIdentifier,
        activity_subject: Optional[ActivitySubjectIdentifier] = None,
        description: Optional[str] = None,
    ) -> str:

        request_model = create_task_request(
            username,
            password,
            work_type,
            case,
            activity_subject,
            description,
        )
        xml_dict = request_model.dict(by_alias=True)

        xml_element = cast(Element, fineos_activity_services_request_schema.encode(xml_dict))
        return defusedxml.ElementTree.tostring(
            xml_element, encoding="unicode", xml_declaration=True
        )

    def _decode_xml_response(self, schema: xmlschema.XMLSchema, text: str) -> Dict[str, Any]:
        try:
            # by default, the response is decoded in `strict` mode, meaning any errors
            # encountered will throw an exception instead of returned in this response.
            # if we changed to lax mode, we would need to inspect the decoded object
            # for errors and handle them, therefore the `Tuple[Optional[Any], List[XMLSchemaValidationError]]``
            # part of the decode return type is only relevant in lax mode
            decoded_schema = schema.decode(text)
            if not decoded_schema:
                raise ValueError("text could not be decoded.")

            response_decoded = cast(Dict[str, Any], decoded_schema)
            return response_decoded
        except Exception:
            logger.exception("Error decoding response", extra={"schema": repr(schema)})
            raise

    def is_customer_api_available(self) -> bool:

        response = self._customer_api(
            "GET",
            "healthcheck",
            "ANONYMOUS_USER",
            "get_is_registered",
        )

        return response.status_code == requests.codes.ok

    def is_integration_services_api_available(self) -> bool:

        response = self._integration_services_api(
            "GET",
            "healthcheck",
            "ANONYMOUS_USER",
            "is_integration_services_api_available",
        )

        return response.status_code == requests.codes.ok

    def is_group_client_api_available(self) -> bool:
        response = self._group_client_api(
            "GET",
            "healthcheck",
            "ANONYMOUS_USER",
            "is_group_client_api_available",
        )

        return response.status_code == requests.codes.ok

    def get_db_session_raw(self):
        if FINEOSClient.db_session_raw is None:
            FINEOSClient.db_session_raw = db.init()
        return FINEOSClient.db_session_raw

    def get_absence_reason(self, user_id: str, absence_reason_id: str) -> AbsenceReasonResource:
        response = self._customer_api(
            "GET",
            f"customer/absence/absence-reasons/{absence_reason_id}",
            user_id,
            "get_absence_reason",
        )

        absence_reason_resource = AbsenceReasonResource.parse_obj(response.json())

        return absence_reason_resource

    def add_absence_period_to_absence_case(
        self,
        user_id: str,
        absence_id: str,
        absence_period: CreateRequestedAbsencePeriodCommandCustomer,
    ) -> RequestedAbsencePeriodResource:
        """Adds a new requested absence period to an absence case.
        fineos documentation: https://documentation.fineos.com/support/documentation/customer-api-webapp-25.1.0.html#tag/Requested-Absence-Period
        """
        response = self._customer_api(
            "POST",
            f"customer/absence/absences/{absence_id}/requested-absence-periods",
            user_id,
            "add_absence_period_to_absence_case",
            data=absence_period.json(exclude_none=True),
        )

        requested_absence_period_resource = RequestedAbsencePeriodResource.parse_obj(
            response.json()
        )

        return requested_absence_period_resource

    def update_requested_absence_period(
        self,
        user_id: str,
        absence_id: str,
        requested_absence_period_id: str,
        edit_absence_period_command: EditRequestedAbsencePeriodCommand,
    ) -> RequestedAbsencePeriodResource:
        """Updates the requested absence period.
        fineos documentation: https://documentation.fineos.com/support/documentation/customer-api-webapp-24.8.0.html#tag/Requested-Absence-Period/operation/edit
        """
        response = self._customer_api(
            "POST",
            f"customer/absence/absences/{absence_id}/requested-absence-periods/{requested_absence_period_id}/edit",
            user_id,
            "update_requested_absence_period",
            data=edit_absence_period_command.json(exclude_none=True),
        )

        requested_absence_period_resource = RequestedAbsencePeriodResource.parse_obj(
            response.json()
        )

        return requested_absence_period_resource

    def _get_internal_error(self, key: str, error_msg: Any) -> str:
        """Get Internal Fineos Error message."""
        internal_error = ""
        try:
            if key and error_msg and key in error_msg:
                pattern = rf"(?:\"{key})(?:\"\s?:\s?\")(.*)(?:\")"
                match = re.search(pattern, error_msg)
                internal_error = match.group(1) if match else internal_error
        except Exception as ex:
            logger.error(
                "Failed to get internal error message from FINEOS API response",
                extra={"key": key, "error_msg": error_msg},
                exc_info=ex,
            )
            internal_error = error_msg
        return internal_error


def is_fineos_api_logging_to_db_enabled() -> bool:
    return os.environ.get("ENABLE_FINEOS_API_LOGGING_TO_DB", "0") == "1"
