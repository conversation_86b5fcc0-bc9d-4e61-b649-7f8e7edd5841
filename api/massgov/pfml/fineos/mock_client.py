#
# FINEOS client - mock implementation.
#
# This implementation is intended for use in local development or in test cases. It may also be
# used in deployed environments when needed.
#

import base64
import copy
import datetime
import pathlib
from decimal import Decimal
from typing import Any, Dict, Iterable, List, Optional, Tuple, Union

import requests
from requests.models import Response

import massgov.pfml.util.logging
import massgov.pfml.util.logging.wrapper
from massgov.pfml.api.models.IntermittentLeaveEpisodes.common import (
    EpisodePeriodBasis,
    IntermittentLeaveEpisode,
)
from massgov.pfml.db.lookup_data.phone import PhoneType
from massgov.pfml.features import get_config, initialize
from massgov.pfml.fineos.activity_services.models import (
    ActivitySubjectIdentifier,
    CaseIdentifier,
    CreateTaskResponse,
    WorkTypeIdentifier,
)
from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import PFML_ContactDetails
from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import (
    PFML_EForm as Customer_PFML_EForm,
)
from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import (
    PFML_EFormSummary as Customer_PFML_EFormSummary,
)
from massgov.pfml.fineos.models.abstraction_layer.abstraction_models import PFML_PhoneNumber
from massgov.pfml.fineos.models.customer_api import (
    AbsenceReasonResource,
    ActualAbsencePeriodResource,
    CreateActualAbsencePeriodCommandElements,
    CreateRequestedAbsencePeriodCommandCustomer,
    EditRequestedAbsencePeriodCommand,
    EpisodeDurationBasisResponse,
    PaidLeaveCase,
    PaymentMethodResponse,
    RequestedAbsencePeriodResource,
    StatusResponse,
    TypeResponse,
)
from massgov.pfml.fineos.transforms.to_fineos.base import EFormBody

from . import client, exception, fineos_client, models
from .mock.eform import MOCK_CUSTOMER_EFORMS, MOCK_EFORMS
from .mock.field import fake_customer_no
from .models.customer_api import ChangeRequestPeriod
from .models.group_client_api import EForm

logger = massgov.pfml.util.logging.get_logger(__name__)

# Capture calls for unit testing.
_capture: Optional[List] = None

MOCK_DOCUMENT_DATA = {
    "caseId": "",
    "rootCaseId": "NTN-111",
    "documentId": 3011,
    "name": "",
    "type": "Document",
    "fileExtension": "",
    "fileName": "26e82dd7-dbfc-4e7b-9804-ea955627253d.png",
    "originalFilename": "",
    "receivedDate": "2020-09-01",
    "effectiveFrom": "2020-09-02",
    "effectiveTo": "2020-09-03",
    "description": "",
    "title": "",
    "isRead": False,
    "createdBy": "Roberto Carlos",
    "extensionAttributes": [],
}

MOCK_DOCUMENT_FOR_CUSTOMER_DATA = {
    "caseId": "",
    "id": "",
    "fileExtension": ".pdf",
    "documentTypeFolders": [],
    "creationDateTime": "2022-11-03T13:00:35Z",
    "name": "",
    "fileName": "26e82dd7-dbfc-4e7b-9804-ea955627253d.png",
    "originalFilename": "",
    "receivedDate": "2022-11-03",
    "effectiveFrom": "",
    "effectiveTo": "",
    "description": "",
    "title": "",
    "userCreated": "CONTENT",
    "extensionAttributes": [],
    "partyName": "Victoria Nicolas",
    "extensions": {},
    "isKeyDocument": False,
    "isShadowDoc": False,
    "privacyTag": {
        "name": "Unknown",
    },
    "status": {
        "name": "Completed",
    },
    "type": {"name": "Document"},
    "_links": {},
}

MOCK_DOCUMENT_METAS_FOR_CUSTOMER_DATA = {
    "caseId": "",
    "id": "",
    "fileExtension": ".pdf",
    "documentTypeFolders": ["Absence Case Inbound Documents", "Inbound Documents"],
    "creationDateTime": "2025-11-03T13:00:35Z",
    "name": "",
    "fileName": "26e82dd7-dbfc-4e7b-9804-ea955627253d.pdf",
    "originalFilename": "b1a4f44a-f797-4ebd-ae6c-7378de633ae7.pdf",
    "receivedDate": "2025-11-03",
    "effectiveFrom": "",
    "effectiveTo": "",
    "description": "",
    "title": "",
    "userCreated": "CONTENT",
    "extensionAttributes": [],
    "partyName": "Victoria Nicolas",
    "extensions": {
        "name": "Unknown",
    },
    "isKeyDocument": False,
    "isShadowDoc": False,
    "privacyTag": {
        "name": "Unknown",
    },
    "status": {
        "name": "Completed",
    },
    "type": {"name": "Document"},
    "_links": {
        "parent": "http://api.xyz.context/customer",
        "self": "http://api.xyz.context/customer/document-metas/12345-12345",
    },
}

MOCK_DOCUMENT_FOR_GROUP_CLIENT_DATA = [
    {
        "caseId": "NTN-11111-ABS-01",
        "rootCaseId": "NTN-11111",
        "documentId": "",
        "name": "",
        "type": "Document",
        "fileExtension": ".pdf",
        "fileName": "9e3e6939-49c6-4eba-981f-b68ec6733501.pdf",
        "originalFilename": "ID Proof.pdf",
        "receivedDate": "2025-03-31",
        "effectiveFrom": "",
        "effectiveTo": "",
        "description": "",
        "title": "",
        "isRead": False,
        "createdBy": "John J",
        "dateCreated": "2025-03-31",
        "extensionAttributes": [],
        "status": "Completed",
        "privacyTag": "Unknown",
        "readForMyOrganisation": False,
    },
]

MOCK_DOCUMENT_METAS_FOR_GROUP_CLIENT_DATA = {
    "elements": [
        {
            "meta": {},
            "caseId": "",
            "creationDateTime": "2025-03-31T17:08:17Z",
            "description": "",
            "documentTypeFolders": ["Outbound Documents"],
            "effectiveFrom": "",
            "effectiveTo": "",
            "extensions": {},
            "fileExtension": ".pdf",
            "fileName": "test.pdf",
            "id": "12345-67890",
            "isKeyDocument": False,
            "isShadowDoc": False,
            "name": "Approval Notice Explanation of Wages",
            "originalFileName": "",
            "partyName": "John A",
            "privacyTag": {
                "domainId": 243,
                "fullId": 7776000,
                "name": "Unknown",
                "domainName": "PrivacyTag",
                "_links": {},
            },
            "receivedDate": "",
            "status": {
                "domainId": 111,
                "fullId": 111111,
                "name": "Completed",
                "domainName": "Document Status",
                "_links": {},
            },
            "title": "",
            "type": {
                "domainId": 222,
                "fullId": 222222,
                "name": "Document",
                "domainName": "OCDocument Type",
                "_links": {},
            },
            "userCreated": "<EMAIL>",
            "_links": {},
        },
    ],
    "totalSize": 1,
    "meta": {},
    "_links": {},
}


TEST_IMAGE_FILE_PATH = pathlib.Path(__file__).parent / "mock_files" / "test.png"


def fake_date_of_birth(fake):
    """Generate a fake date of birth in a reproducible way."""
    return fake.date_between(datetime.date(1930, 1, 1), datetime.date(2010, 1, 1))


def mock_document(
    absence_id: str,
    document_type: str = "Approval Notice",
    file_name: str = "test.pdf",
    description: str = "Mock File",
) -> dict:
    mocked_document = copy.copy(MOCK_DOCUMENT_DATA)
    mocked_document.update(
        {
            "caseId": absence_id,
            "name": document_type,
            "fileExtension": pathlib.Path(file_name).suffix.lower(),
            "originalFilename": file_name,
            "description": description,
        }
    )

    return mocked_document


def mock_document_for_customer(
    document_id: Optional[str] = "11560-131654",
    document_type: Optional[str] = "1099G Tax Form for Claimants",
    description: Optional[str] = "26e82dd7-dbfc-4e7b-9804-ea955627253d.pdf",
) -> dict:
    mocked_document = copy.copy(MOCK_DOCUMENT_FOR_CUSTOMER_DATA)
    mocked_document.update({"id": document_id, "name": document_type, "description": description})

    return mocked_document


def mock_document_metas_for_customer(
    document_id: Optional[str] = "11560-131654",
    document_type: Optional[str] = "1099G Tax Form for Claimants",
    description: Optional[str] = "26e82dd7-dbfc-4e7b-9804-ea955627253d.pdf",
) -> dict:
    mocked_document = copy.copy(MOCK_DOCUMENT_METAS_FOR_CUSTOMER_DATA)
    mocked_document.update({"id": document_id, "name": document_type, "description": description})

    return mocked_document


def mock_document_for_group_client(
    document_id: Optional[str] = "11560-131654",
    document_type: Optional[str] = "1099G Tax Form for Claimants",
    description: Optional[str] = "26e82dd7-dbfc-4e7b-9804-ea955627253d.pdf",
) -> list:
    mocked_document = copy.copy(MOCK_DOCUMENT_FOR_GROUP_CLIENT_DATA)
    mocked_document[0].update(
        {"documentId": document_id, "name": document_type, "description": description}
    )

    return mocked_document


def mock_absence_reason_resource(absence_reason_id):
    return {
        "additionalAttributes": {"property1": {}, "property2": {}},
        "meta": {"property1": {}, "property2": {}},
        "id": absence_reason_id,
        "reasonName": "Mock reason name",
        "reasonQualifier1": "Mock qualifier 1",
        "reasonQualifier2": "Mock qualifier 2",
        "coveredPerson": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "askForSecondaryRelationship": True,
        "eventType": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "category": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "allowedGender": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "medicalRelated": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "seriousHealth": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "militaryRelated": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "disabilityIntegration": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "accommodation": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "notificationReason": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "validTransitions": ["14412-9", "14412-10", "14412-19"],
        "_links": {"property1": "string", "property2": "string"},
    }


def mock_document_metas_for_group_client(
    document_id: Optional[str] = "11560-131654",
    document_type: Optional[str] = "1099G Tax Form for Claimants",
    description: Optional[str] = "26e82dd7-dbfc-4e7b-9804-ea955627253d.pdf",
) -> dict:
    mocked_document = copy.copy(MOCK_DOCUMENT_METAS_FOR_GROUP_CLIENT_DATA)
    mocked_document.update({"id": document_id, "name": document_type, "description": description})

    return mocked_document


def mock_notification() -> Any:
    return {
        "id": "NTN-304819",
        "caseNumber": "NTN-304819",
        "notificationReason": {
            "domainId": 290,
            "fullId": 9280005,
            "name": "Caring for a family member",
            "domainName": "NotificationReason",
            "_links": {
                "domain": "http://api.server.context/enum-domains/290",
                "instance": "http://api.server.context/enum-domains/290/enum-instances/9280005",
            },
        },
        "notificationDate": "2022-10-06",
        "createdDate": "2022-10-06",
        "adminGroup": "Unknown",
        "status": "Pending",
        "subCases": [
            {
                "id": "NTN-304819-ABS-01",
                "caseType": "Absence Case",
                "caseNumber": "NTN-304819-ABS-01",
                "status": "Managed Leave",
                "caseHandler": {
                    "emailAddress": "",
                    "telephoneNo": "",
                    "name": "Not Assigned",
                    "id": "",
                },
            }
        ],
        "customer": {
            "customerNo": "5458762",
            "firstName": "Tre",
            "lastName": "Dietrich",
            "dateOfBirth": "1996-11-02",
            "idNumber": "516366493",
            "jobTitle": "DEFAULT",
            "employeeID": "22d511ff-692e-4292-8258-e0dfb27875d0",
            "id": "5458762",
            "_links": {
                "self": "http://api.server.context/groupClient/customers/5458762",
                "CustomerInfo": "http://api.server.context/groupClient/customers/5458762/customer-info",
            },
        },
        "caseHandler": {"emailAddress": "", "telephoneNo": "", "name": "Not Assigned", "id": ""},
        "notifiedBy": {
            "domainId": 289,
            "fullId": 9248000,
            "name": "Requester",
            "domainName": "NotificationCaseNotifiedBy",
            "_links": {
                "domain": "http://api.server.context/enum-domains/289",
                "instance": "http://api.server.context/enum-domains/289/enum-instances/9248000",
            },
        },
        "expectedRTWDate": "",
        "_links": {"self": "http://api.server.context/groupClient/notifications/NTN-304819"},
    }


# Build this notification to read the correct NTN-00 for PFMLPB-8036
def mock_notification_alt() -> Any:
    return {
        "id": "NTN-00",
        "caseNumber": "NTN-00",
        "notificationReason": {
            "domainId": 290,
            "fullId": 9280005,
            "name": "Caring for a family member",
            "domainName": "NotificationReason",
            "_links": {
                "domain": "http://api.server.context/enum-domains/290",
                "instance": "http://api.server.context/enum-domains/290/enum-instances/9280005",
            },
        },
        "notificationDate": "2022-10-06",
        "createdDate": "2022-10-06",
        "adminGroup": "Unknown",
        "status": "Pending",
        "subCases": [
            {
                "id": "NTN-00-ABS-01",
                "caseType": "Absence Case",
                "caseNumber": "NTN-00-ABS-01",
                "status": "Managed Leave",
                "caseHandler": {
                    "emailAddress": "",
                    "telephoneNo": "",
                    "name": "Not Assigned",
                    "id": "",
                },
            }
        ],
        "customer": {
            "customerNo": "5458762",
            "firstName": "Tre",
            "lastName": "Dietrich",
            "dateOfBirth": "1996-11-02",
            "idNumber": "*********",
            "jobTitle": "DEFAULT",
            "employeeID": "22d511ff-692e-4292-8258-e0dfb27875d0",
            "id": "5458762",
            "_links": {
                "self": "http://api.server.context/groupClient/customers/5458762",
                "CustomerInfo": "http://api.server.context/groupClient/customers/5458762/customer-info",
            },
        },
        "caseHandler": {"emailAddress": "", "telephoneNo": "", "name": "Not Assigned", "id": ""},
        "notifiedBy": {
            "domainId": 289,
            "fullId": 9248000,
            "name": "Requester",
            "domainName": "NotificationCaseNotifiedBy",
            "_links": {
                "domain": "http://api.server.context/enum-domains/289",
                "instance": "http://api.server.context/enum-domains/289/enum-instances/9248000",
            },
        },
        "expectedRTWDate": "",
        "_links": {"self": "http://api.server.context/groupClient/notifications/NTN-00"},
    }


def mock_absence_periods(absence_id: str) -> Any:
    employee_id = "1000" if absence_id == "int_fake_hours_worked_per_week" else "1079"
    type = "Time off period" if absence_id == "NTN-100-ABS-01" else "Reduced Schedule"
    return {
        "startDate": "2021-06-24",
        "endDate": "2021-10-28",
        "decisions": [
            {
                "absence": {"id": absence_id, "caseReference": absence_id},
                "employee": {"id": employee_id, "name": "ZZ: Olaf Aufderhar"},
                "period": {
                    "periodReference": "PL-14449-**********",
                    "parentPeriodReference": "",
                    "relatedToEpisodic": "false",
                    "startDate": "2021-06-24",
                    "endDate": "2021-06-25",
                    "balanceDeduction": 0,
                    "timeRequested": "",
                    "timeDeducted": "",
                    "timeDeductedBasis": "Hours",
                    "timeDecisionStatus": "Approved",
                    "timeDecisionReason": "Leave Request Approved",
                    "type": type,
                    "status": "Known",
                    "leavePlan": {
                        "id": "abdc368f-ace6-4d6a-b697-f1016fe8a314",
                        "name": "MA PFML - Employee",
                        "shortName": "MA PFML - Employee",
                        "applicabilityStatus": "Applicable",
                        "eligibilityStatus": "Met",
                        "availabilityStatus": "Approved",
                        "adjudicationStatus": "Accepted",
                        "evidenceStatus": "Satisfied",
                        "category": "Paid",
                        "calculationPeriodMethod": "Rolling Forward - Sunday",
                        "timeBankMethod": "Length / Duration",
                        "timeWithinPeriod": 52,
                        "timeWithinPeriodBasis": "Weeks",
                        "fixedYearStartDay": 0,
                        "fixedYearStartMonth": "Please Select",
                        "timeEntitlement": 20,
                        "timeEntitlementBasis": "Weeks",
                        "paidLeaveCaseId": "PL ABS-542",
                    },
                    "leaveRequest": {
                        "id": "PL-14432-**********",
                        "reasonName": "Serious Health Condition - Employee",
                        "qualifier1": "Not Work Related",
                        "qualifier2": "Sickness",
                        "decisionStatus": "Approved",
                        "approvalReason": "Fully Approved",
                        "denialReason": "Please Select",
                    },
                },
            },
            {
                "absence": {"id": "NTN-61-ABS-01", "caseReference": "NTN-61-ABS-01"},
                "employee": {"id": "1079", "name": "ZZ: Olaf Aufderhar"},
                "period": {
                    "periodReference": "PL-14449-**********",
                    "parentPeriodReference": "",
                    "relatedToEpisodic": "false",
                    "startDate": "2021-06-26",
                    "endDate": "2021-06-27",
                    "balanceDeduction": 0,
                    "timeRequested": "",
                    "timeDeducted": "",
                    "timeDeductedBasis": "",
                    "timeDecisionStatus": "",
                    "timeDecisionReason": "",
                    "type": "Time off period",
                    "status": "Known",
                    "leavePlan": {
                        "id": "abdc368f-ace6-4d6a-b697-f1016fe8a314",
                        "name": "MA PFML - Employee",
                        "shortName": "MA PFML - Employee",
                        "applicabilityStatus": "Applicable",
                        "eligibilityStatus": "Met",
                        "availabilityStatus": "Approved",
                        "adjudicationStatus": "Accepted",
                        "evidenceStatus": "Satisfied",
                        "category": "Paid",
                        "calculationPeriodMethod": "Rolling Forward - Sunday",
                        "timeBankMethod": "Length / Duration",
                        "timeWithinPeriod": 52,
                        "timeWithinPeriodBasis": "Weeks",
                        "fixedYearStartDay": 0,
                        "fixedYearStartMonth": "Please Select",
                        "timeEntitlement": 20,
                        "timeEntitlementBasis": "Weeks",
                        "paidLeaveCaseId": "PL ABS-542",
                    },
                    "leaveRequest": {
                        "id": "PL-14432-**********",
                        "reasonName": "Serious Health Condition - Employee",
                        "qualifier1": "Not Work Related",
                        "qualifier2": "Sickness",
                        "decisionStatus": "Approved",
                        "approvalReason": "Fully Approved",
                        "denialReason": "Please Select",
                    },
                },
            },
        ],
    }


def mock_absence_period_decisions(absence_id: str) -> Any:
    return {
        "startDate": "2022-06-01",
        "endDate": "2022-08-01",
        "absencePeriodDecisions": [
            {
                "periodId": "PL-14449-**********",
                "parentPeriodId": "",
                "startDate": "2022-06-01",
                "endDate": "2022-07-01",
                "absencePeriodStatus": "Known",
                "absencePeriodType": "Time off period",
                "absenceCaseId": "NTN-234373-ABS-01",
                "leavePlanId": "abdc368f-ace6-4d6a-b697-f1016fe8a314",
                "leavePlanCategory": "Paid",
                "leavePlanName": "MA PFML - Employee",
                "leavePlanShortName": "MA PFML - Employee",
                "timeBankMethod": "Length / Duration",
                "availabilityPeriodMethod": "Rolling Forward - Sunday",
                "timeWithinPeriod": 52,
                "timeWithinPeriodBasis": "Weeks",
                "fixedYearStartDay": 0,
                "fixedYearStartMonth": "Please Select",
                "timeEntitlement": 20,
                "timeEntitlementBasis": "Weeks",
                "reasonName": "Serious Health Condition - Employee",
                "qualifier1": "Not Work Related",
                "qualifier2": "Sickness",
                "applicabilityStatus": "Applicable",
                "eligibilityStatus": "Met",
                "adjudicationStatus": "Accepted",
                "evidenceStatus": "Satisfied",
                "availabilityStatus": "Approved",
                "leaveRequestId": "PL-14432-**********",
                "decisionStatus": "Approved",
                "approvalReason": "Fully Approved",
                "denialReason": "Please Select",
                "timeDecisionStatus": "Approved",
                "timeDecisionReason": "Leave Request Approved",
                "balanceDeduction": 4.43125,
                "actualForRequestedEpisodic": False,
                "timeRequested": "177:15",
                "timeDeducted": "177:15",
                "timeDeductedBasis": "Hours",
            }
        ],
    }


def mock_benefit_details():
    return [
        {
            "benefitId": "PL ABS-479052-PL ABS-01",
            "benefitCaseType": "Absence Paid Leave Benefit",
            "creationDate": "2023-01-17",
            "benefitIncurredDate": "2022-12-19",
            "customerName": "Stanley Romaguera",
            "policyReferences": "",
            "description": "",
            "status": "Unknown",
            "benefitHandler": "Not Assigned",
            "benefitHandlerPhoneNo": "",
            "benefitHandlerEmailAddress": "",
            "benefitRightCategory": "Recurring Benefit",
            "stageName": "Approved",
            "extensionAttributes": [],
        }
    ]


def mock_disability_benefits():
    return {
        "benefitSummary": {
            "benefitId": "PL ABS-479052-PL ABS-01",
            "benefitCaseType": "Absence Paid Leave Benefit",
            "creationDate": "2023-01-17",
            "benefitIncurredDate": "2022-12-19",
            "customerName": "Stanley Romaguera",
            "policyReferences": "",
            "description": "",
            "status": "Unknown",
            "benefitHandler": "Not Assigned",
            "benefitHandlerPhoneNo": "",
            "benefitHandlerEmailAddress": "",
            "benefitRightCategory": "Recurring Benefit",
            "stageName": "Approved",
            "extensionAttributes": [],
        },
        "disabilityBenefit": {
            "benefitIncurredDate": "2022-12-19",
            "brokerAuthorisationFlag": False,
            "initialNotificationDate": "",
            "notificationReceivedDate": "",
            "overrideClaimIncurredDate": False,
            "startDateOfBenefitForClaim": "2023-01-17",
            "sourceOfRequest": "Unknown",
            "earliestDateForClaimPayment": "",
            "employeeContributionPercentage": 0,
            "isReimbursement": False,
            "lateEnrollmentPeriod": 0,
            "latestDateForClaimPayment": "",
            "minimumQualifyPeriod": 0,
            "employerContributionPercentage": 0,
            "isUnderwritten": False,
            "policyWaitingPeriod": 0,
            "benefitType": "Absence Benefit",
            "basisOfMinimumQualifyPeriod": "Unknown",
            "basisOfLateEnrollmentPeriod": "Unknown",
            "basisOfPolicyWaitingPeriod": "Unknown",
            "amountType": "Unknown",
            "employeeContributionStatus": "Unknown",
            "accidentEliminationPeriod": 0,
            "eliminationPeriod": 7,
            "hospitalEliminationPeriod": 0,
            "hospitalizationClauseApplies": False,
            "basisOfEliminationPeriod": "Day(s)",
            "basisOfAccidentEliminationPeriod": "Unknown",
            "hospitalBasisOfEliminationPeriod": "Unknown",
            "accidentMaxBenefitPeriod": 0,
            "hospitalMaxBenefitPeriod": 0,
            "maxBenefitPeriod": 20,
            "minBenefitPeriod": 0,
            "basisOfMinBenefitPeriod": "Unknown",
            "basisOfMaxBenefitPeriod": "Week(s)",
            "accidentBasisOfMaxBenefitPeriod": "Unknown",
            "hospitalBasisOfMaxBenefitPeriod": "Unknown",
            "benefitStartDate": "2022-12-26",
            "benefitEndDate": "2023-01-13",
            "frequencyAmount": "0.00",
            "adviceToPayOverride": "Unknown",
            "administrationType": "Fully Insured",
            "serviceLevel": "Unknown",
            "extensionAttributes": [],
            "expectedResolutionDate": "",
            "approvedThroughDate": "2023-01-13",
            "periodType": "Fully Certified",
            "percentTaxable": 0,
            "percentageNonTaxable": 100,
            "employeePremiumTaxation": "Unknown",
            "employerPremiumTaxation": "Unknown",
        },
        "certificationPeriods": [
            {
                "periodType": "Fully Certified",
                "status": "Approved",
                "statusReason": "Unknown",
                "periodFromDate": "2022-12-19",
                "periodToDate": "2023-01-13",
                "totalPeriodDays": 26,
                "notes": "",
                "insurerReceivedDate": "",
                "consultationDate": "",
                "duration": 26,
            }
        ],
    }


def mock_customer_info():
    # The FINEOS response includes more fields than this,
    # but I've only included what is currently relevant to us
    return {
        "firstName": "Bud",
        "lastName": "Baxter",
        "secondName": "",
        "initials": "",
        "dateOfBirth": "1970-12-25",
        "idNumber": "*********",
        "address": {
            "premiseNo": "",
            "addressLine1": "55 Trinity Ave.",
            "addressLine2": "Suite 3450",
            "addressLine3": "",
            "addressLine4": "Atlanta",
            "addressLine5": "",
            "addressLine6": "GA",
            "addressLine7": "",
            "postCode": "30303",
            "country": {"name": "USA", "domainName": "Country", "domainId": 1, "fullId": 2},
            "extensions": {
                "ConsenttoShareData": True,
                "Confirmed": True,
                "MassachusettsID": "",
                "OutOfStateID": "",
            },
        },
    }


def mock_customer_details():
    # Similar to mock_customer_info, The FINEOS response includes more fields than this
    return {
        "firstName": "Samantha",
        "lastName": "Jorgenson",
        "secondName": "",
        "initials": "",
        "dateOfBirth": "1996-01-11",
        "idNumber": "123121232",
        "gender": "Neutral",
        "customerAddress": {
            "address": {
                "addressLine1": "37 Mather Drive",
                "addressLine2": "#22",
                "addressLine3": "",
                "addressLine4": "Amherst",
                "addressLine5": "",
                "addressLine6": "MA",
                "addressLine7": "",
                "postCode": "01003",
                "country": "USA",
            }
        },
        "classExtensionInformation": [
            {"name": "MassachusettsID", "stringValue": "123456789"},
            {"name": "OutOfStateID", "stringValue": "123"},
        ],
    }


# Used in `read_customer_details` call below
def mock_customer_details_alt():
    return {
        "firstName": "Bud",
        "lastName": "Baxter",
        "secondName": "",
        "initials": "",
        "dateOfBirth": "1970-12-25",
        "idNumber": "*********",
        "customerAddress": {
            "address": {
                "premiseNo": "",
                "addressLine1": "55 Trinity Ave.",
                "addressLine2": "Suite 3450",
                "addressLine3": "",
                "addressLine4": "Atlanta",
                "addressLine5": "",
                "addressLine6": "GA",
                "addressLine7": "",
                "postCode": "30303",
                "country": "USA",
                "extensions": {
                    "ConsenttoShareData": True,
                    "Confirmed": True,
                    "MassachusettsID": "",
                    "OutOfStateID": "",
                },
            },
        },
    }


def mock_customer_contact_details():
    # Mocks FINEOS response for customer contact details
    # This includes all the FINEOS fields from this endpoint
    return {
        "phoneNumbers": [
            {
                "id": 0,
                "preferred": False,
                "phoneNumberType": "Cell",
                "intCode": "1",
                "areaCode": "123",
                "telephoneNo": "4567890",
            },
            {
                "id": 1,
                "preferred": True,
                "phoneNumberType": "Cell",
                "intCode": "1",
                "areaCode": "321",
                "telephoneNo": "4567890",
            },
        ],
        "emailAddresses": [
            {
                "id": 0,
                "preferred": False,
                "emailAddress": "<EMAIL>",
                "emailAddressType": "Email",
            },
            {
                "id": 1,
                "preferred": True,
                "emailAddress": "<EMAIL>",
                "emailAddressType": "Email",
            },
        ],
        "preferredContactMethod": 1,
    }


def mock_managed_requirements():
    return [
        {
            "managedReqId": 123,
            "category": "Employer Confirmation",
            "type": "Employer Confirmation of Leave Data",
            "followUpDate": datetime.date(2021, 2, 1),
            "documentReceived": True,
            "creator": "Fake Creator",
            "status": "Open",
            "subjectPartyName": "Fake Name",
            "sourceOfInfoPartyName": "Fake Sourcee",
            "creationDate": datetime.date(2020, 1, 1),
            "dateSuppressed": datetime.date(2020, 3, 1),
        },
        {
            "managedReqId": 124,
            "category": "Employer Confirmation",
            "type": "Employer Confirmation of Leave Data",
            "followUpDate": datetime.date(2021, 2, 1),
            "documentReceived": True,
            "creator": "Fake Creator",
            "status": "Complete",
            "subjectPartyName": "Fake Name",
            "sourceOfInfoPartyName": "Fake Sourcee",
            "creationDate": datetime.date(2020, 1, 1),
            "dateSuppressed": datetime.date(2020, 3, 1),
        },
    ]


def mock_managed_requirements_parsed():
    return [
        models.group_client_api.ManagedRequirementDetails.parse_obj(r)
        for r in mock_managed_requirements()
    ]


def mock_tax_withholding_leave_request(
    tax_withholding_value: Optional[bool] = None,
) -> Dict[str, Any]:
    return {
        "OID": "PL:14432:0000031807",
        "BOEVersion": 3,
        "LastUpdateDate": "2022-06-09T11:58:12",
        "UserUpdatedBy": "CONTENT",
        "selectedLeavePlans": {
            "SelectedLeavePlan": [
                {
                    "OID": "PL:14437:0000048791",
                    "BOEVersion": 2,
                    "LastUpdateDate": "2022-06-09T11:58:12",
                    "UserUpdatedBy": "CONTENT",
                    "paidLeaveInstruction": {
                        "PaidLeaveInstruction": [
                            {
                                "OID": "PL:14462:0000047929",
                                "BOEVersion": 2,
                                "LastUpdateDate": "2022-06-09T13:46:30",
                                "UserUpdatedBy": "CONTENT",
                                "AutoApproveBenefits": {
                                    "InstanceDomainAndFullId": {
                                        "InstanceName": "Yes",
                                        "DomainName": "BenefitApprovalOption",
                                        "FullId": 218816000,
                                    }
                                },
                                "AverageDaysWorked": Decimal("0"),
                                "AverageWeeklyWage": {
                                    "CurrencyCode": "---",
                                    "Amount": Decimal("1730.77"),
                                },
                                "BenefitWaitingPeriod": 7,
                                "BenefitWaitingPeriodBasis": {
                                    "InstanceDomainAndFullId": {
                                        "InstanceName": "Calendar Days",
                                        "DomainName": "CalculationPeriodBasis",
                                        "FullId": *********,
                                    }
                                },
                                "Notes": None,
                                "PolicyReference": None,
                                "SITFITOptIn": tax_withholding_value,
                            }
                        ]
                    },
                }
            ]
        },
    }


def mock_tax_withholding_preference_read_response(
    leave_requests: Optional[List[Dict[str, Any]]] = None,
) -> Dict[str, Any]:
    return {
        "@xmlns:p": "http://www.fineos.com/wscomposer/COMReadPaidLeaveInstruction",
        "@xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance",
        "OCCase": [
            {
                "OID": "PE:11605:0000079949",
                "BOEVersion": 3,
                "LastUpdateDate": "2022-06-09T11:58:47",
                "UserUpdatedBy": "PROCESSTIMETRIGGERDAEMON",
                "AdminGroup": "Unknown",
                "AdminGroupAutoSet": False,
                "Anonymised": False,
                "AnonymiseDate": None,
                "AnonymiseMode": {
                    "InstanceDomainAndFullId": {
                        "InstanceName": "Default",
                        "DomainName": "AnonymizeMode",
                        "FullId": 9216000,
                    }
                },
                "C_CreatedBy": 1000,
                "CaseComplexity": {
                    "InstanceDomainAndFullId": {
                        "InstanceName": "Unknown",
                        "DomainName": "CaseComplexity",
                        "FullId": 7168000,
                    }
                },
                "CaseFlag": {
                    "InstanceDomainAndFullId": {
                        "InstanceName": "Unknown",
                        "DomainName": "CaseFlags",
                        "FullId": 7936000,
                    }
                },
                "CaseNumber": "NTN-38781-ABS-01",
                "CaseOwnerComment": None,
                "CollapseView": False,
                "DateCreated": "2022-06-09T11:57:32",
                "DefaultCurrency": {
                    "InstanceDomainAndFullId": {
                        "InstanceName": "---",
                        "DomainName": "Currency Type",
                        "FullId": 33600000,
                    }
                },
                "Description": "Serious Health Condition - Employee | Not Work Related | Sickness : 07/05/2022-07/12/2022 (Status: Episodic, Pattern: Intermittent)",
                "EffectiveDate": "2022-06-09T11:57:32",
                "FollowUpDate": None,
                "I_CreatedBy": 22936,
                "MasterPlanId": None,
                "MemberId": None,
                "NotProceedingWithDate": None,
                "ParentCaseNumber": None,
                "ToDate": None,
                "caseProxies": {
                    "OCCaseForAbsence": [
                        {
                            "OID": "PE:14442:**********",
                            "BOEVersion": 2,
                            "LastUpdateDate": "2022-06-09T11:57:38",
                            "UserUpdatedBy": "PFML_API_B4195B97-CC8C-4DC5-9836-E46A513A5D84",
                            "leaveRequests": {
                                "LeaveRequest": (
                                    leave_requests
                                    if leave_requests is not None
                                    else [mock_tax_withholding_leave_request()]
                                )
                            },
                        }
                    ]
                },
            }
        ],
        "additional-data-set": {
            "additional-data": [
                {"name": "PARAM_STR_CASENUMBER", "value": "NTN-38781-ABS-01"},
                {"name": "USERID", "value": "CONTENT"},
            ]
        },
    }


def mock_intermittent_leave_episode():
    episode = IntermittentLeaveEpisode(
        requested_date=datetime.date(2023, 8, 22),
        episodic_period_basis=EpisodePeriodBasis.Minutes,
        episodic_period=205,
    )
    return episode


def mock_leave_availability_response():
    return {
        "id": "mock_leave_plan_id",
        "name": "MA PFML - Employee",
        "approvedTime": 5,
        "pendingTime": 2,
        "timeBasis": "Hours",
        "timeEntitlement": 20,
        "timeWithinPeriod": 52,
        "timeWithinPeriodBasis": "Weeks",
        "availableBalance": 10,
        "projectedAvailableTime": 12,
        "availabilityPeriodStartDate": "2024-01-01",
        "availabilityPeriodEndDate": "2024-12-31",
        "notificationMessage": "Standard leave plan message",
        "sharedEntitlementPlans": [
            {
                "id": "5dce5e6b-9be2-4ad0-9eae-4654e1a83988",
                "name": "MA PFML - Limit",
                "approvedTime": 5,
                "pendingTime": 0,
                "timeBasis": "Weeks",
                "timeEntitlement": 26,
                "timeWithinPeriod": 52,
                "timeWithinPeriodBasis": "Weeks",
                "participantPlans": [
                    {
                        "id": "f25383ea-81ba-4d59-b5dd-20d33df6180e",
                        "name": "MA PFML - Family",
                        "approvedTime": 5,
                        "pendingTime": 0,
                        "timeBasis": "Weeks",
                    },
                    {
                        "id": "2994cbe7-3c1c-4a57-91af-ea11b6084434",
                        "name": "MA PFML - Military Care",
                        "approvedTime": 0,
                        "pendingTime": 0,
                        "timeBasis": "Weeks",
                    },
                ],
            }
        ],
    }


def mock_leave_availability_with_lower_limit_response():
    mock_leave_availability_dict = mock_leave_availability_response()
    mock_leave_availability_dict["sharedEntitlementPlans"][0]["approvedTime"] = 20

    return mock_leave_availability_dict


# TODO: this returns ActualAbsencePeriod, not AbsencePeriod
# rename to mock_actual_absence_periods
def mock_absence_period_response():
    return {
        "elements": [
            {
                "id": "************",
                "episodicLeaveRequestId": "14474-33098",
                "actualDate": datetime.date(2023, 8, 22),
                "episodePeriodDuration": 205,
                "episodePeriodBasis": {
                    "domainId": 6757,
                    "fullId": 216224001,
                    "name": "Minutes",
                    "domainName": "TimeUnits",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6757",
                        "instance": "http://api.server.context/enum-domains/6757/enum-instances/216224001",
                    },
                },
                "additionalNotes": "api",
                "type": {
                    "domainId": 6811,
                    "fullId": 217952005,
                    "name": "Incapacity",
                    "domainName": "AbsencePeriodType",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6811",
                        "instance": "http://api.server.context/enum-domains/6811/enum-instances/217952005",
                    },
                },
                "status": {
                    "domainId": 6821,
                    "fullId": 218272003,
                    "name": "Pending",
                    "domainName": "DecisionStatus",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6821",
                        "instance": "http://api.server.context/enum-domains/6821/enum-instances/218272003",
                    },
                },
                "receivedVia": {
                    "domainId": 33,
                    "fullId": 1081004,
                    "name": "Self Service",
                    "domainName": "Method Of Contact",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/33",
                        "instance": "http://api.server.context/enum-domains/33/enum-instances/1081004",
                    },
                },
                "reportedBy": {
                    "domainId": 6842,
                    "fullId": 218944001,
                    "name": "Employee",
                    "domainName": "ContactRole",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6842",
                        "instance": "http://api.server.context/enum-domains/6842/enum-instances/218944001",
                    },
                },
                "managerAccepted": {
                    "domainId": 2500,
                    "fullId": 80000002,
                    "name": "No",
                    "domainName": "YesNoUnknown",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/2500",
                        "instance": "http://api.server.context/enum-domains/2500/enum-instances/80000002",
                    },
                },
                "reportingPartyName": "Lowell hartmann",
                "startDateTime": "",
                "endDateTime": "",
                "timeZone": {
                    "domainId": 300,
                    "fullId": 9600000,
                    "name": "UTC",
                    "domainName": "StandardTimeZone",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/300",
                        "instance": "http://api.server.context/enum-domains/300/enum-instances/9600000",
                    },
                },
                "reportedDateTime": "2023-10-27T16:45:11Z",
                "additionalBusinessInfo": {},
                "_links": {
                    "self": "http://api.server.context/customer/absence/absences/NTN-527629-ABS-01/actual-absence-periods/************"
                },
            },
            {
                "id": "14449-95169",
                "episodicLeaveRequestId": "14474-33098",
                "actualDate": "2024-06-25",
                "episodePeriodDuration": 2,
                "episodePeriodBasis": {
                    "domainId": 6757,
                    "fullId": 216224002,
                    "name": "Hours",
                    "domainName": "TimeUnits",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6757",
                        "instance": "http://api.server.context/enum-domains/6757/enum-instances/216224002",
                    },
                },
                "additionalNotes": "",
                "type": {
                    "domainId": 6811,
                    "fullId": 217952005,
                    "name": "Incapacity",
                    "domainName": "AbsencePeriodType",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6811",
                        "instance": "http://api.server.context/enum-domains/6811/enum-instances/217952005",
                    },
                },
                "status": {
                    "domainId": 6821,
                    "fullId": 218272001,
                    "name": "Approved",
                    "domainName": "DecisionStatus",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6821",
                        "instance": "http://api.server.context/enum-domains/6821/enum-instances/218272001",
                    },
                },
                "receivedVia": {
                    "domainId": 33,
                    "fullId": 1056001,
                    "name": "Phone",
                    "domainName": "Method Of Contact",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/33",
                        "instance": "http://api.server.context/enum-domains/33/enum-instances/1056001",
                    },
                },
                "reportedBy": {
                    "domainId": 6842,
                    "fullId": 218944001,
                    "name": "Employee",
                    "domainName": "ContactRole",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6842",
                        "instance": "http://api.server.context/enum-domains/6842/enum-instances/218944001",
                    },
                },
                "managerAccepted": {
                    "domainId": 2500,
                    "fullId": 80000002,
                    "name": "No",
                    "domainName": "YesNoUnknown",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/2500",
                        "instance": "http://api.server.context/enum-domains/2500/enum-instances/80000002",
                    },
                },
                "reportingPartyName": "",
                "startDateTime": "",
                "endDateTime": "",
                "timeZone": {
                    "domainId": 300,
                    "fullId": 9600000,
                    "name": "UTC",
                    "domainName": "StandardTimeZone",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/300",
                        "instance": "http://api.server.context/enum-domains/300/enum-instances/9600000",
                    },
                },
                "reportedDateTime": "2024-06-27T17:11:05Z",
                "additionalBusinessInfo": {},
                "_links": {
                    "self": "http://api.server.context/customer/absence/absences/NTN-527629-ABS-01/actual-absence-periods/14449-95169"
                },
            },
            {
                "id": "14449-95171",
                "episodicLeaveRequestId": "14474-33098",
                "actualDate": "2024-06-25",
                "episodePeriodDuration": 1,
                "episodePeriodBasis": {
                    "domainId": 6757,
                    "fullId": 216224002,
                    "name": "Days",
                    "domainName": "TimeUnits",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6757",
                        "instance": "http://api.server.context/enum-domains/6757/enum-instances/216224002",
                    },
                },
                "additionalNotes": "",
                "type": {
                    "domainId": 6811,
                    "fullId": 217952005,
                    "name": "Incapacity",
                    "domainName": "AbsencePeriodType",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6811",
                        "instance": "http://api.server.context/enum-domains/6811/enum-instances/217952005",
                    },
                },
                "status": {
                    "domainId": 6821,
                    "fullId": 218272001,
                    "name": "Approved",
                    "domainName": "DecisionStatus",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6821",
                        "instance": "http://api.server.context/enum-domains/6821/enum-instances/218272001",
                    },
                },
                "receivedVia": {
                    "domainId": 33,
                    "fullId": 1056001,
                    "name": "Phone",
                    "domainName": "Method Of Contact",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/33",
                        "instance": "http://api.server.context/enum-domains/33/enum-instances/1056001",
                    },
                },
                "reportedBy": {
                    "domainId": 6842,
                    "fullId": 218944001,
                    "name": "Employee",
                    "domainName": "ContactRole",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/6842",
                        "instance": "http://api.server.context/enum-domains/6842/enum-instances/218944001",
                    },
                },
                "managerAccepted": {
                    "domainId": 2500,
                    "fullId": 80000002,
                    "name": "No",
                    "domainName": "YesNoUnknown",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/2500",
                        "instance": "http://api.server.context/enum-domains/2500/enum-instances/80000002",
                    },
                },
                "reportingPartyName": "",
                "startDateTime": "",
                "endDateTime": "",
                "timeZone": {
                    "domainId": 300,
                    "fullId": 9600000,
                    "name": "UTC",
                    "domainName": "StandardTimeZone",
                    "_links": {
                        "domain": "http://api.server.context/enum-domains/300",
                        "instance": "http://api.server.context/enum-domains/300/enum-instances/9600000",
                    },
                },
                "reportedDateTime": "2024-06-27T17:11:05Z",
                "additionalBusinessInfo": {},
                "_links": {
                    "self": "http://api.server.context/customer/absence/absences/NTN-527629-ABS-01/actual-absence-periods/14449-95171"
                },
            },
        ],
        "totalSize": 3,
        "meta": {},
        "_links": {
            "self": "http://api.server.context/customerapi/api/v5.0/customer/absence/absences/NTN-527629-ABS-01/actual-absence-periods/bulk-create"
        },
    }


def mock_leave_plans_response():
    return {
        "totalNumberOfRecords": 3,
        "leavePlans": [
            {
                "leavePlanId": "abdc368f-ace6-4d6a-b697-123456789031",
                "name": "MA PFML - Employee",
                "description": "M.G.L. c. 175M, Family and Medical Leave Law",
                "startDate": "2021-01-01",
                "endDate": "",
                "category": "Paid",
                "group": "State",
                "type": "EE Medical Leave",
                "leavePlanAlias": "MA PFML - Employee",
            },
            {
                "leavePlanId": "f25383ea-81ba-4d59-b5dd-12345678900e",
                "name": "MA PFML - Family",
                "description": "M.G.L. c. 175M, Family and Medical Leave Law",
                "startDate": "2021-01-01",
                "endDate": "",
                "category": "Paid",
                "group": "State",
                "type": "Family Medical Leave",
                "leavePlanAlias": "MA PFML - Family",
            },
            {
                "leavePlanId": "2994cbe7-3c1c-4a57-91af-123456789034",
                "name": "MA PFML - Military Caregiver",
                "description": "M.G.L. c. 175M, Family and Medical Leave Law",
                "startDate": "2021-01-01",
                "endDate": "",
                "category": "Paid",
                "group": "State",
                "type": "Military Related Leave",
                "leavePlanAlias": "MA PFML - Military Care",
            },
        ],
    }


def mock_requested_absence_period_resource_response():
    return {
        "additionalAttributes": {"property1": {}, "property2": {}},
        "meta": {"property1": {}, "property2": {}},
        "id": "string",
        "type": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "status": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "startDate": "1999-12-31",
        "endDate": "1999-12-31",
        "patternType": {
            "domainId": 0,
            "fullId": 0,
            "name": "string",
            "domainName": "string",
            "instances": [
                {
                    "name": "string",
                    "fullId": "string",
                    "meta": {
                        "retired": True,
                        "editable": True,
                        "alphaSort": True,
                        "defaultFirst": True,
                        "dOrder": True,
                    },
                }
            ],
            "_links": {"property1": "string", "property2": "string"},
        },
        "details": {
            "id": "string",
            "startHours": 0,
            "startMinutes": 0,
            "endHours": 0,
            "endMinutes": 0,
            "startDateAllDay": True,
            "endDateAllDay": True,
            "lastDayWorked": "1999-12-31",
        },
        "returnToWork": {
            "expectedReturnToWorkDate": "1999-12-31",
            "expectedPartialReturnToWorkDate": "1999-12-31",
            "actualReturnToWorkDate": "1999-12-31",
            "actualPartialReturnToWorkDate": "1999-12-31",
        },
        "_links": {"property1": "string", "property2": "string"},
    }


class MockFINEOSClient(client.AbstractFINEOSClient):
    """Mock FINEOS API client that returns fake responses."""

    def __init__(
        self,
        mock_eforms: Iterable[EForm] = MOCK_EFORMS,
        mock_customer_eforms: Iterable[Customer_PFML_EForm] = MOCK_CUSTOMER_EFORMS,
    ):
        self.mock_eform_map = {eform.eformId: eform for eform in mock_eforms}
        self.mock_customer_eforms = mock_customer_eforms
        self.mock_customer_eform_map = {eform.eformId: eform for eform in mock_customer_eforms}

    def read_employer(self, employer_fein: str) -> models.OCOrganisation:
        _capture_call("read_employer", None, employer_fein=employer_fein)

        if employer_fein == "*********":
            raise exception.FINEOSEntityNotFound("Employer not found.")

        organisationUnits = None
        if employer_fein == "999999998":
            organisationUnits = models.OCOrganisationUnit(
                OrganisationUnit=[
                    models.OCOrganisationUnitItem(OID="PE:00001:0000000001", Name="OrgUnitOne"),
                    models.OCOrganisationUnitItem(OID="PE:00001:0000000002", Name="OrgUnitTwo"),
                ]
            )

        if employer_fein == "999999997":
            organisationUnits = models.OCOrganisationUnit(
                OrganisationUnit=[
                    models.OCOrganisationUnitItem(OID="PE:00002:0000000001", Name="OrgUnitThree"),
                    models.OCOrganisationUnitItem(OID="PE:00002:0000000002", Name="OrgUnitFour"),
                ]
            )

        return models.OCOrganisation(
            OCOrganisation=[
                models.OCOrganisationItem(
                    CustomerNo=str(fake_customer_no(employer_fein)),
                    CorporateTaxNumber=employer_fein,
                    Name="Foo",
                    organisationUnits=organisationUnits,
                )
            ]
        )

    def find_employer(self, employer_fein: str) -> str:
        _capture_call("find_employer", None, employer_fein=employer_fein)

        if employer_fein == "*********":
            raise exception.FINEOSEntityNotFound("Employer not found.")
        else:
            return str(fake_customer_no(employer_fein))

    def register_api_user(self, employee_registration: models.EmployeeRegistration) -> None:
        _capture_call("register_api_user", None, employee_registration=employee_registration)

        if employee_registration.national_insurance_no == "*********":
            raise exception.FINEOSClientBadResponse("register_api_user", requests.codes.ok, 400)
        else:
            pass

    def health_check(self, user_id: str) -> bool:
        return True

    def read_customer_details(self, user_id: str) -> models.customer_api.Customer:
        return models.customer_api.Customer.parse_obj(mock_customer_details_alt())

    def update_customer_details(self, user_id: str, customer: models.customer_api.Customer) -> None:
        _capture_call("update_customer_details", user_id, customer=customer)

    def read_customer_contact_details(self, user_id: str) -> PFML_ContactDetails:
        _capture_call("update_customer_contact_details", user_id)

        return PFML_ContactDetails(
            phoneNumbers=[
                PFML_PhoneNumber(
                    id=1,
                    intCode="1",
                    areaCode="321",
                    telephoneNo="4567890",
                    phoneNumberType=PhoneType.PHONE.phone_type_description,
                )
            ]
        )

    def update_customer_contact_details(
        self, user_id: str, contact_details: PFML_ContactDetails
    ) -> PFML_ContactDetails:
        _capture_call("update_customer_contact_details", user_id, contact_details=contact_details)

        if contact_details.phoneNumbers is not None and len(contact_details.phoneNumbers) > 0:
            if contact_details.phoneNumbers[0].id is None:
                contact_details.phoneNumbers[0].id = 1

        return contact_details

    def start_absence(
        self, user_id: str, absence_case: models.customer_api.AbsenceCase
    ) -> models.customer_api.AbsenceCaseSummary:
        call_count = (
            len([capture for capture in get_capture() if capture[0] == "start_absence"])
            if get_capture()
            else 0
        )
        _capture_call("start_absence", user_id, absence_case=absence_case)
        fineos_case_id_number = 259 + call_count

        start_date = None
        end_date = None

        if absence_case.timeOffLeavePeriods:
            start_date = absence_case.timeOffLeavePeriods[0].startDate
            end_date = absence_case.timeOffLeavePeriods[0].endDate
        elif absence_case.reducedScheduleLeavePeriods:
            start_date = absence_case.reducedScheduleLeavePeriods[0].startDate
            end_date = absence_case.reducedScheduleLeavePeriods[0].endDate
        elif absence_case.episodicLeavePeriods:
            start_date = absence_case.episodicLeavePeriods[0].startDate
            end_date = absence_case.episodicLeavePeriods[0].endDate

        absence_case_summary = models.customer_api.AbsenceCaseSummary(
            absenceId=f"NTN-{fineos_case_id_number}-ABS-01",
            notificationCaseId=f"NTN-{fineos_case_id_number}",
            startDate=start_date,
            endDate=end_date,
        )
        return absence_case_summary

    def complete_intake(
        self, user_id: str, notification_case_id: str
    ) -> models.customer_api.NotificationCaseSummary:
        _capture_call("complete_intake", user_id, notification_case_id=notification_case_id)

        notification_case_summary = models.customer_api.NotificationCaseSummary(
            notificationCaseId=notification_case_id
        )
        return notification_case_summary

    def get_absences(self, user_id: str) -> List[models.customer_api.AbsenceCaseSummary]:
        return [models.customer_api.AbsenceCaseSummary()]

    def get_absence(self, user_id: str, absence_id: str) -> models.customer_api.AbsenceDetails:
        absence_details = models.customer_api.AbsenceDetails()

        # This returns an absence case with no absence periods
        # Used for testing that we handle absence cases with no periods correctly
        if absence_id == "NTN-012345-ABS-01":
            absence_details.absencePeriods = []
        elif absence_id == "NTN-304363-ABS-01":
            absence_details.absenceId = "NTN-304363-ABS-01"
            absence_period = models.customer_api.AbsencePeriod()
            absence_period.id = "PL-14449-0000002237"
            absence_period.absenceType = "Continuous"
            absence_period.reason = "Child Bonding"
            absence_period.reasonQualifier1 = "Foster Care"
            absence_period.reasonQualifier2 = ""
            absence_period.startDate = datetime.date(2021, 1, 29)
            absence_period.endDate = datetime.date(2021, 1, 30)
            absence_period.requestStatus = "Pending"

            absence_details.absencePeriods = [absence_period]

        # This returns an absence case with multiple paid leave cases corresponding to the same leave request
        # Used for testing that we prohibit channel switching for such absence cases
        elif absence_id == "NTN-1729-ABS-01":
            absence_details.paidLeaveCase = [
                PaidLeaveCase(leaveRequestId="1"),
                PaidLeaveCase(leaveRequestId="1"),
            ]

        # Conversely, this returns an absence case wit multiple paid leave cases, each corresponding to a different
        # leave request. This is used for testing that channel switching is allowed.
        elif absence_id == "NTN-12407-ABS-01":
            absence_details.paidLeaveCase = [
                PaidLeaveCase(leaveRequestId="1"),
                PaidLeaveCase(leaveRequestId="2"),
            ]

        return absence_details

    def get_leave_plans(self, user_id: str) -> List[models.customer_api.LeavePlan]:
        return [
            models.customer_api.LeavePlan.parse_obj(leave_plan)
            for leave_plan in mock_leave_plans_response()["leavePlans"]
        ]

    def get_leave_availability(
        self, user_id: str, leave_plan_id: str
    ) -> models.customer_api.EmployeeLeaveBalance:
        return models.customer_api.EmployeeLeaveBalance.parse_obj(
            mock_leave_availability_response()
        )

    def read_claim_benefit(
        self, user_id: str, absence_paid_leave_case_id: str
    ) -> List[models.customer_api.BenefitSummary]:
        mock_benefit_detail_list = mock_benefit_details()
        return [models.customer_api.BenefitSummary.parse_obj(mock_benefit_detail_list[0])]

    def read_disability_benefit(
        self, user_id: str, absence_paid_leave_case_id: str, benefit_id: str
    ) -> models.customer_api.ReadDisabilityBenefitResult:
        return models.customer_api.ReadDisabilityBenefitResult.parse_obj(mock_disability_benefits())

    def get_group_client_notification(
        self, employer_user_id: str, notification_id: str
    ) -> models.group_client_api.Notification:
        if notification_id == "NTN-304819" or notification_id == "NTN-001":
            fineos_notification = mock_notification()
            return models.group_client_api.Notification.parse_obj(fineos_notification)

        if notification_id == "NTN-00":
            fineos_notification = mock_notification_alt()
            return models.group_client_api.Notification.parse_obj(fineos_notification)

        return models.group_client_api.Notification()

    def get_group_client_absence_period_decisions(
        self, user_id: str, absence_id: str
    ) -> models.group_client_api.PeriodDecisions:
        _capture_call("get_group_client_absence_period_decisions", user_id, absence_id=absence_id)
        absence_periods = mock_absence_periods(absence_id)
        return models.group_client_api.PeriodDecisions.parse_obj(absence_periods)

    def get_customer_absence_period_decisions(
        self, user_id: str, absence_id: str
    ) -> models.customer_api.AbsencePeriodDecisions:
        _capture_call("get_customer_absence_period_decisions", user_id, absence_id=absence_id)
        absence_period_decisions = mock_absence_period_decisions(absence_id)
        return models.customer_api.AbsencePeriodDecisions.parse_obj(absence_period_decisions)

    def get_customer_info(
        self, user_id: str, customer_id: str
    ) -> models.group_client_api.CustomerInfo:
        return models.group_client_api.CustomerInfo.parse_obj(mock_customer_info())

    def get_customer_occupations_customer_api(
        self, user_id: str
    ) -> List[models.customer_api.ReadCustomerOccupation]:
        _capture_call("get_customer_occupations_customer_api", user_id)

        return [
            models.customer_api.ReadCustomerOccupation(
                occupationId=12345,
                hoursWorkedPerWeek=37.5,
                workPatternBasis="Unknown",
                employmentStatus="Active",
            )
        ]

    def get_customer_occupations(
        self, user_id: str, customer_id: str
    ) -> models.group_client_api.CustomerOccupations:
        _capture_call("get_customer_occupations", user_id, customer_id=customer_id)

        hrsWorkedPerWeek = 37 if customer_id == "1000" else 37.5
        return models.group_client_api.CustomerOccupations(
            elements=[
                models.group_client_api.CustomerOccupation(
                    id="12345", hrsWorkedPerWeek=str(hrsWorkedPerWeek)
                )
            ]
        )

    def get_outstanding_information(
        self, user_id: str, case_id: str
    ) -> List[models.group_client_api.OutstandingInformationItem]:
        """Get outstanding information"""
        _capture_call("get_outstanding_information", user_id, case_id=case_id)

        if case_id == "NTN-CASE-WITHOUT-OUTSTANDING-INFO":
            return []

        return [
            models.group_client_api.OutstandingInformationItem(
                informationType="Employer Confirmation of Leave Data", infoReceived=False
            )
        ]

    def update_outstanding_information_as_received(
        self,
        user_id: str,
        case_id: str,
        outstanding_information: models.group_client_api.OutstandingInformationData,
    ) -> None:
        """Update outstanding information received"""
        _capture_call(
            "update_outstanding_information_as_received",
            user_id,
            outstanding_information=outstanding_information,
            case_id=case_id,
        )

    def get_eform_summary(
        self, user_id: str, absence_id: str
    ) -> List[models.group_client_api.EFormSummary]:
        _capture_call("get_eform_summary", user_id, absence_id=absence_id)
        results = []

        for eform in self.mock_eform_map.values():
            if eform.eformType:
                results.append(
                    models.group_client_api.EFormSummary(
                        eformId=eform.eformId,
                        eformTypeId="PE-11212-%010i" % eform.eformId,
                        effectiveDateFrom=None,
                        effectiveDateTo=None,
                        eformType=eform.eformType,
                    )
                )

        return results

    def customer_get_eform_summary(
        self, user_id: str, absence_id: str
    ) -> List[Customer_PFML_EFormSummary]:
        _capture_call("customer_get_eform_summary", user_id, absence_id=absence_id)
        results = []

        for eform in self.mock_customer_eforms:
            if eform.eformType:
                results.append(
                    Customer_PFML_EFormSummary(
                        eformId=eform.eformId,
                        eformTypeId="PE-11212-" + eform.eformId.zfill(10),  # pad the id with 0s
                        effectiveDateFrom=None,
                        effectiveDateTo=None,
                        eformType=eform.eformType,
                    )
                )

        return results

    def get_eform(
        self, user_id: str, absence_id: str, eform_id: int
    ) -> models.group_client_api.EForm:
        _capture_call("get_eform", user_id, absence_id=absence_id, eform_id=eform_id)
        if eform_id in self.mock_eform_map:
            return self.mock_eform_map[eform_id]
        raise exception.FINEOSForbidden("get_eform", 200, 403, "Permission denied")

    def customer_get_eform(
        self, user_id: str, absence_id: str, eform_id: str
    ) -> Customer_PFML_EForm:
        _capture_call("customer_get_eform", user_id, absence_id=absence_id, eform_id=eform_id)
        if eform_id in self.mock_customer_eform_map:
            return self.mock_customer_eform_map[eform_id]
        raise exception.FINEOSForbidden("get_eform", 200, 403, "Permission denied")

    def create_eform(
        self, user_id: str, absence_id: str, eform: EFormBody
    ) -> models.group_client_api.EForm:
        _capture_call("create_eform", user_id, eform=eform, absence_id=absence_id)
        new_eform = models.group_client_api.EForm(
            eformId=max(self.mock_eform_map) + 2,
            eformType=eform.eformType,
            eformAttributes=[
                models.group_client_api.EFormAttribute.parse_obj(attr)
                for attr in eform.eformAttributes
            ],
        )
        self.mock_eform_map[new_eform.eformId] = new_eform
        return new_eform

    def customer_create_eform(self, user_id: str, absence_id: str, eform: EFormBody) -> None:
        _capture_call("customer_create_eform", user_id, eform=eform, absence_id=absence_id)

    def get_case_occupations(
        self, user_id: str, case_id: str
    ) -> List[models.customer_api.ReadCustomerOccupation]:
        _capture_call("get_case_occupations", user_id, case_id=case_id)
        return [models.customer_api.ReadCustomerOccupation(occupationId=12345)]

    def get_customer_payment_preference(
        self, user_id: str
    ) -> models.customer_api.PaymentPreferenceCustomerResources:
        _capture_call(
            "get_customer_payment_preference",
            user_id=user_id,
        )
        data = {
            "elements": [
                {
                    "id": "7706-964225",
                    "description": "Check",
                    "effectiveFromDate": "",
                    "effectiveToDate": "",
                    "paymentMethod": {
                        "domainId": 2069,
                        "fullId": 66236007,
                        "name": "Check",
                        "domainName": "Payment Method",
                        "_links": {
                            "domain": "http://api.server.context/enum-domains/2069",
                            "instance": "http://api.server.context/enum-domains/2069/enum-instances/66236007",
                        },
                    },
                    "address": {
                        "premiseNo": "",
                        "addressLine1": "35830 Steuber Corner",
                        "addressLine2": "",
                        "addressLine3": "",
                        "addressLine4": "Tallahassee",
                        "addressLine5": "",
                        "addressLine6": "AZ",
                        "addressLine7": "",
                        "postCode": "20004",
                        "country": {
                            "domainId": 21,
                            "fullId": 672007,
                            "name": "USA",
                            "domainName": "Country",
                            "_links": {
                                "domain": "http://api.server.context/enum-domains/21",
                                "instance": "http://api.server.context/enum-domains/21/enum-instances/672007",
                            },
                        },
                        "extensions": {},
                    },
                    "bulkPayee": False,
                    "identifier": "1314883",
                    "paymentDay": {
                        "domainId": 2307,
                        "fullId": 73824000,
                        "name": "on cycle start date",
                        "domainName": "PaymentDueDay",
                        "_links": {
                            "domain": "http://api.server.context/enum-domains/2307",
                            "instance": "http://api.server.context/enum-domains/2307/enum-instances/73824000",
                        },
                    },
                    "paymentPeriod": {
                        "domainId": 2332,
                        "fullId": 74624000,
                        "name": "Unknown",
                        "domainName": "PaymentPeriod",
                        "_links": {
                            "domain": "http://api.server.context/enum-domains/2332",
                            "instance": "http://api.server.context/enum-domains/2332/enum-instances/74624000",
                        },
                    },
                    "extensions": {},
                    "meta": {},
                    "default": True,
                    "_links": {
                        "self": "http://api.server.context/customer/payment-preferences/7706-964225"
                    },
                }
            ],
            "totalSize": 36,
            "_links": {
                "self": "http://api.server.context/customerapi/api/v5.0/customer/payment-preferences/"
            },
        }

        return models.customer_api.PaymentPreferenceCustomerResources.parse_obj(data)

    def create_customer_payment_preference(
        self, user_id: str, payment_preference: models.customer_api.CreatePaymentPreferenceCommand
    ) -> models.customer_api.PaymentPreferenceResource:
        account_type = models.customer_api.AccountTypeResponse(
            name="Checking",
            domainId=2069,
            fullId=********,
            domainName="Payment Method",
        )
        account_detail = models.customer_api.AccountDetailEmbeddable(
            accountNo=(
                payment_preference.accountDetail.accountNo
                if payment_preference.accountDetail is not None
                else None
            ),
            accountName=(
                payment_preference.accountDetail.accountName
                if payment_preference.accountDetail is not None
                else None
            ),
            routingNumber=(
                payment_preference.accountDetail.routingNumber
                if payment_preference.accountDetail is not None
                else None
            ),
            accountType=account_type,
        )
        _capture_call(
            "create_customer_payment_preference",
            user_id=user_id,
            payment_preference=payment_preference,
        )
        return models.customer_api.PaymentPreferenceResource(
            id="1201",
            accountDetail=account_detail,
            paymentMethod=PaymentMethodResponse(
                domainId=101,
                domainName="PaymentMethodDomain",
                fullId=123,
                name=payment_preference.paymentMethod.name or "",
            ),
        )

    def update_customer_payment_preference(
        self,
        user_id: str,
        payment_preference_id: str,
        payment_preference: models.customer_api.EditPaymentPreferenceCommand,
    ) -> models.customer_api.PaymentPreferenceResource:
        account_type = models.customer_api.AccountTypeResponse(
            name="Checking",
            domainId=2069,
            fullId=********,
            domainName="Payment Method",
        )
        account_detail = models.customer_api.AccountDetailEmbeddable(
            accountNo="**********",
            accountName="Constance Griffin",
            routingNumber="*********",
            accountType=account_type,
        )
        _capture_call(
            "update_customer_payment_preference",
            user_id=user_id,
            payment_preference_id=payment_preference_id,
            payment_preference=payment_preference,
        )
        return models.customer_api.PaymentPreferenceResource(
            id="1201",
            accountDetail=account_detail,
        )

    def update_occupation(
        self,
        occupation_id: int,
        employment_status: Optional[str],
        hours_worked_per_week: Optional[Decimal],
        fineos_org_unit_id: Optional[str],
        worksite_id: Optional[str],
    ) -> None:
        _capture_call(
            "update_occupation",
            None,
            employment_status=employment_status,
            hours_worked_per_week=hours_worked_per_week,
            occupation_id=occupation_id,
            fineos_org_unit_id=fineos_org_unit_id,
            worksite_id=worksite_id,
        )

    def upload_document(
        self,
        user_id: str,
        absence_id: str,
        document_type: str,
        file_content: bytes,
        file_name: str,
        content_type: str,
        description: str,
    ) -> models.customer_api.Document:
        _capture_call(
            "upload_document",
            user_id,
            absence_id=absence_id,
            document_type=document_type,
            file_content=file_content,
            file_name=file_name,
            content_type=content_type,
            description=description,
        )

        document = mock_document(absence_id, document_type, file_name, description)
        return models.customer_api.Document.parse_obj(document)

    def upload_document_multipart(
        self,
        user_id: str,
        absence_id: str,
        document_type: str,
        file_content: bytes,
        file_name: str,
        content_type: str,
        description: str,
    ) -> models.customer_api.Document:
        _capture_call(
            "upload_document_multipart",
            user_id,
            absence_id=absence_id,
            document_type=document_type,
            file_content=file_content,
            file_name=file_name,
            content_type=content_type,
            description=description,
        )

        document = mock_document(absence_id, document_type, file_name, description)
        return models.customer_api.Document.parse_obj(document)

    def is_running_v24(self) -> bool:
        initialize()
        return get_config().fineos.is_running_v24

    def get_documents(
        self, user_id: str, absence_id: Optional[str] = None, api_params: Optional[Dict] = None
    ) -> List[models.customer_api.Document]:
        _capture_call(
            "get_documents",
            user_id,
            absence_id=absence_id,
            api_params=api_params,
        )
        document = {}
        if absence_id:
            document = mock_document(absence_id)
        else:
            fineos_is_running_v24 = self.is_running_v24()
            if fineos_is_running_v24:
                document = mock_document_metas_for_customer()
            else:
                document = mock_document_for_customer()

        return [
            models.customer_api.Document.parse_obj(
                fineos_client.fineos_document_empty_dates_to_none(document)
                if absence_id
                else fineos_client.parse_fineos_document_for_customer(document)
            )
        ]

    def get_managed_requirements(
        self, user_id: str, absence_id: str
    ) -> List[models.group_client_api.ManagedRequirementDetails]:
        return mock_managed_requirements_parsed()

    def group_client_get_documents(
        self, user_id: str, absence_id: str
    ) -> List[models.group_client_api.GroupClientDocument]:
        if absence_id == "leave_admin_allowable_doc_type":
            document = mock_document(absence_id)
        else:
            document = mock_document(absence_id, document_type="Disallowed Doc Type")
        return [
            models.group_client_api.GroupClientDocument.parse_obj(
                fineos_client.fineos_document_empty_dates_to_none(document)
            )
        ]

    def download_document_as_leave_admin(
        self, user_id: str, absence_id: str, fineos_document_id: str
    ) -> models.group_client_api.Base64EncodedFileData:
        file_bytes = open(str(TEST_IMAGE_FILE_PATH), "rb").read()
        encoded_file_contents_str = base64.b64encode(file_bytes).decode("ascii")

        return models.group_client_api.Base64EncodedFileData.parse_obj(
            {
                "fileName": "test.pdf",
                "fileExtension": "pdf",
                "contentType": "application/pdf",
                "base64EncodedFileContents": encoded_file_contents_str,
                "fileSizeInBytes": len(file_bytes),
            }
        )

    def download_document(
        self, user_id: str, fineos_document_id: str, absence_id: Optional[str] = None
    ) -> models.customer_api.Base64EncodedFileData:
        _capture_call(
            "download_document",
            None,
            fineos_document_id=fineos_document_id,
            absence_id=absence_id,
        )
        file_bytes = open(str(TEST_IMAGE_FILE_PATH), "rb").read()
        encoded_file_contents_str = base64.b64encode(file_bytes).decode("ascii")

        return models.customer_api.Base64EncodedFileData.parse_obj(
            {
                "fileName": "test.pdf",
                "fileExtension": "pdf",
                "contentType": "application/pdf",
                "base64EncodedFileContents": encoded_file_contents_str,
                "fileSizeInBytes": len(file_bytes),
            }
        )

    def mark_document_as_received(
        self, user_id: str, absence_id: str, fineos_document_id: str
    ) -> None:
        _capture_call(
            "mark_document_as_received",
            user_id,
            absence_id=absence_id,
            fineos_document_id=fineos_document_id,
        )

    def get_outstanding_evidence(
        self, user_id: str, case_id: str
    ) -> list[models.customer_api.OutstandingSupportingEvidence]:
        _capture_call(
            "get_outstanding_evidence",
            user_id,
            case_id=case_id,
        )

        return []

    def get_week_based_work_pattern(
        self, user_id: str, occupation_id: Union[str, int]
    ) -> models.customer_api.WeekBasedWorkPattern:
        _capture_call("get_week_based_work_pattern", user_id, occupation_id=occupation_id)
        return models.customer_api.WeekBasedWorkPattern(
            workPatternType="Fixed",
            workPatternDays=[
                models.customer_api.WorkPatternDay(
                    dayOfWeek="Monday", weekNumber=12, hours=4, minutes=5
                )
            ],
        )

    def add_week_based_work_pattern(
        self,
        user_id: str,
        occupation_id: Union[str, int],
        week_based_work_pattern: models.customer_api.WeekBasedWorkPattern,
    ) -> models.customer_api.WeekBasedWorkPattern:
        _capture_call(
            "add_week_based_work_pattern", user_id, week_based_work_pattern=week_based_work_pattern
        )

        if user_id == "USER_WITH_EXISTING_WORK_PATTERN":
            raise exception.FINEOSForbidden("add_week_based_work_pattern", 200, 403)
        else:
            return week_based_work_pattern

    def update_week_based_work_pattern(
        self,
        user_id: str,
        occupation_id: Union[str, int],
        week_based_work_pattern: models.customer_api.WeekBasedWorkPattern,
    ) -> models.customer_api.WeekBasedWorkPattern:
        _capture_call(
            "update_week_based_work_pattern",
            user_id,
            week_based_work_pattern=week_based_work_pattern,
        )
        return week_based_work_pattern

    def create_or_update_employer(
        self,
        employer_create_or_update: models.CreateOrUpdateEmployer,
        existing_organization: Optional[models.OCOrganisationItem] = None,
    ) -> Tuple[str, int]:
        _capture_call(
            "create_or_update_employer",
            None,
            employer_create_or_update=employer_create_or_update,
            existing_organization=existing_organization,
        )

        if employer_create_or_update.employer_fein == "*********":
            raise exception.FINEOSFatalResponseError(
                "create_or_update_employer",
                Exception(
                    "Employer not created. Response Code: 422, "
                    "Party alias pfml_api_21ecb120-9a9a-4f8d-968d-e710b120e148 for alias "
                    "type Unknown already exists for customer 2569"
                ),
            )

        return (
            employer_create_or_update.fineos_customer_nbr,
            fake_customer_no(employer_create_or_update.employer_fein),
        )

    def create_or_update_leave_period_change_request(
        self,
        fineos_web_id: str,
        absence_id: str,
        change_request: models.customer_api.CreateLeavePeriodsChangeRequestCommand,
    ) -> models.customer_api.LeavePeriodsChangeRequestResource:
        _capture_call(
            "create_or_update_leave_period_change_request",
            fineos_web_id,
            absence_id=absence_id,
            additional_information=change_request,
        )
        return models.customer_api.LeavePeriodsChangeRequestResource(
            reason=models.customer_api.ReasonResponse(
                fullId=0, name="Employee Request", domainId=0, domainName="Foo"
            ),
            changeRequestPeriods=[
                ChangeRequestPeriod(
                    startDate=datetime.date(2022, 2, 14), endDate=datetime.date(2022, 2, 15)
                )
            ],
            additionalNotes="Withdrawal",
        )

    def create_or_update_leave_admin(
        self, leave_admin_create_or_update: models.CreateOrUpdateLeaveAdmin
    ) -> Tuple[Optional[str], Optional[str]]:
        _capture_call(
            "create_or_update_leave_admin",
            None,
            leave_admin_create_or_update=leave_admin_create_or_update,
        )
        return "", ""

    def update_reflexive_questions(
        self,
        user_id: str,
        absence_id: Optional[str],
        additional_information: models.customer_api.AdditionalInformation,
    ) -> None:
        _capture_call(
            "update_reflexive_questions",
            user_id,
            absence_id=absence_id,
            additional_information=additional_information,
        )

    def create_service_agreement_for_employer(
        self,
        fineos_employer_id: int,
        service_agreement_inputs: models.CreateOrUpdateServiceAgreement,
    ) -> str:
        _capture_call(
            "create_service_agreement_for_employer",
            None,
            fineos_employer_id=fineos_employer_id,
            service_agreement_inputs=service_agreement_inputs,
        )

        return f"SA-777{fineos_employer_id}-SAR-01"

    def send_tax_withholding_preference(self, absence_id: str, is_withholding_tax: bool) -> None:
        _capture_call(
            "send_tax_withholding_preference",
            None,
            absence_id=absence_id,
            is_withholding_tax=is_withholding_tax,
        )

    def upload_document_to_dms(self, file_name: str, file: bytes, data: Any) -> Response:
        _capture_call(
            "upload_document_to_dms",
            None,
            file_name=file_name,
            file=file,
            data=data,
        )

        response = Response()
        response.status_code = 200
        return response

    def create_appeal_case(self, absence_case_id: str) -> str:
        _capture_call("create_appeal_case", None, absence_case_id=absence_case_id)
        return absence_case_id + "-AP-01"

    def read_tax_withholding_preference(self, absence_id: str) -> Optional[bool]:
        _capture_call(
            "read_tax_withholding_preference",
            None,
            absence_id=absence_id,
        )
        return False

    def create_task(
        self,
        work_type: WorkTypeIdentifier,
        case: CaseIdentifier,
        activity_subject: Optional[ActivitySubjectIdentifier],
        description: Optional[str],
    ) -> CreateTaskResponse:
        _capture_call(
            "create_task",
            None,
            work_type=work_type,
            case=case,
            activity_subject=activity_subject,
            description=description,
        )

        mock_response: dict = {
            "{http://schemas.xmlsoap.org/soap/envelope/}Body": {
                "{http://www.fineos.com/frontoffice/workmanager/operationtypes}CreateTaskResponse": [
                    {"return": {"TaskID": {"IndexID": 1288296}}}
                ]
            }
        }

        return CreateTaskResponse.parse_obj(mock_response)

    def is_customer_api_available(self):
        return True

    def is_integration_services_api_available(self):
        return True

    def is_group_client_api_available(self):
        return True

    def submit_intermittent_leave_episode(
        self,
        user_id: str,
        absence_id: str,
        elements: CreateActualAbsencePeriodCommandElements,
    ) -> List[ActualAbsencePeriodResource]:
        element = elements.elements[0]
        _capture_call(
            "submit_intermittent_leave_episode",
            user_id=user_id,
            absence_id=absence_id,
            additional_information=element,
        )
        return [
            ActualAbsencePeriodResource(
                episodePeriodBasis=EpisodeDurationBasisResponse(
                    name=(
                        element.episodePeriodBasis.name
                        if element.episodePeriodBasis.name
                        else "Minutes"
                    ),
                    domainId=123,
                    fullId=124,
                    domainName="test",
                ),
                episodePeriodDuration=element.episodePeriodDuration,
                actualDate=element.actualDate,
                type=TypeResponse(name="Incapacity", domainId=123, domainName="test", fullId=123),
                status=StatusResponse(name="Success", domainId=123, domainName="test", fullId=123),
                reportedDateTime=datetime.datetime.now(),
            )
        ]

    def get_actual_absence_period_resources(
        self,
        user_id: str,
        absence_id: str,
    ) -> models.customer_api.ActualAbsencePeriodResources:
        _capture_call(
            "get_actual_absence_period_resources",
            user_id=user_id,
            absence_id=absence_id,
        )

        mock_data = mock_absence_period_response()
        actual_absence_period_resources = (
            models.customer_api.ActualAbsencePeriodResources.parse_obj(mock_data)
        )

        return actual_absence_period_resources

    def create_overpayment_recovery(self, overpayment_recovery: models.OverpaymentRecovery) -> str:

        method_name = "create_overpayment_recovery"
        _capture_call(
            method_name,
            None,
            overpayment_recovery=overpayment_recovery,
        )

        return "PE:07329:0000000001"

    def get_absence_reason(self, user_id: str, absence_reason_id: str) -> AbsenceReasonResource:
        _capture_call("get_absence_reason", user_id=user_id, absence_reason_id=absence_reason_id)

        mock_data = mock_absence_reason_resource(absence_reason_id)
        absence_reason_resource = AbsenceReasonResource.parse_obj(mock_data)

        return absence_reason_resource

    def add_absence_period_to_absence_case(
        self,
        user_id: str,
        absence_id: str,
        absence_period: CreateRequestedAbsencePeriodCommandCustomer,
    ) -> RequestedAbsencePeriodResource:
        _capture_call(
            "add_absence_period_to_absence_case",
            user_id=user_id,
            absence_id=absence_id,
            absence_period=absence_period,
        )

        mock_data = mock_requested_absence_period_resource_response()
        requested_absence_period_resource = RequestedAbsencePeriodResource.parse_obj(mock_data)

        return requested_absence_period_resource

    def update_requested_absence_period(
        self,
        user_id: str,
        absence_id: str,
        requested_absence_period_id: str,
        edit_absence_period_command: EditRequestedAbsencePeriodCommand,
    ) -> RequestedAbsencePeriodResource:
        _capture_call(
            "update_requested_absence_period",
            user_id=user_id,
            absence_id=absence_id,
            requested_absence_period_id=requested_absence_period_id,
            edit_absence_period_command=edit_absence_period_command,
        )

        mock_data = mock_requested_absence_period_resource_response()
        requested_absence_period_resource = RequestedAbsencePeriodResource.parse_obj(mock_data)

        return requested_absence_period_resource


massgov.pfml.util.logging.wrapper.log_all_method_calls(MockFINEOSClient, logger)


def start_capture():
    """Start capturing API calls made via MockFINEOSClient."""
    global _capture
    _capture = []


def _capture_call(method_name, user_id, **args):
    """Record the name and arguments of an API call."""
    global _capture
    if _capture is not None:
        _capture.append((method_name, user_id, args))


def get_capture():
    """Return the list of API calls captured since start_capture() was called."""
    global _capture
    return _capture
