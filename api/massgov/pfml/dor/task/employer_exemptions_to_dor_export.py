import os
import tempfile
from enum import Enum
from typing import List

import massgov.pfml.db as db
import massgov.pfml.dor.importer.dor_extract_config as dor_config
import massgov.pfml.util.logging as logging
from massgov.pfml import features
from massgov.pfml.dor.employer_exemptions.employer_exemptions_moveit_step import (
    EmployerExemptionsMoveITStep,
)
from massgov.pfml.dor.employer_exemptions.employer_exemptions_write_file_step import (
    EmployerExemptionsFileWriterStep,
)
from massgov.pfml.util.batch.task_runner import TaskRunner
from massgov.pfml.util.bg import background_task
from massgov.pfml.util.datetime import get_now_us_eastern

logger = logging.get_logger(__name__)


class EmployerExemptionsDORExporterTaskRunner(TaskRunner):

    def __init__(self, s3_config: dor_config.DorS3Config, input_args: List[str] | None = None):
        self.s3_config = s3_config
        super().__init__(input_args)

    class StepMapping(str, Enum):
        EMPLOYER_EXEMPTIONS_EXPORT_FILE_CREATION = "employer-exemptions-export"
        EMPLOYER_EXEMPTIONS_EXPORT_FILE_MOVEIT = "employer-exemptions-export-moveit"

    now = get_now_us_eastern()
    exemptions_file_dir = tempfile.gettempdir()
    exemptions_file_name = f"EMPLOYER_EXEMPTIONS_EXPORT_{now.strftime('%Y%m%d')}.txt"
    exemptions_file_path = os.path.join(
        exemptions_file_dir,
        exemptions_file_name,
    )

    def run_steps(self, db_session: db.Session, log_entry_db_session: db.Session) -> None:
        # TODO(PFMLPB-23385): remove enable_employer_exemptions_dor_data_transfer feature flag
        export_to_dor_ff = (
            features.get_config().employer_exemptions.enable_employer_exemptions_dor_data_transfer
        )
        if export_to_dor_ff:
            """Process Nightly Reports"""
            logger.info("Start - Employer Exemptions Export ")
            logger.info(f"Current time: {self.now}")
            if self.is_enabled(self.StepMapping.EMPLOYER_EXEMPTIONS_EXPORT_FILE_CREATION):
                EmployerExemptionsFileWriterStep(
                    db_session=db_session,
                    log_entry_db_session=log_entry_db_session,
                    exemptions_file_path=self.exemptions_file_path,
                ).run()
            logger.info("Done - Writing Employer Exemptions Export File")
            if self.is_enabled(self.StepMapping.EMPLOYER_EXEMPTIONS_EXPORT_FILE_MOVEIT):
                EmployerExemptionsMoveITStep(
                    db_session=db_session,
                    log_entry_db_session=log_entry_db_session,
                    exemptions_file_path=self.exemptions_file_path,
                    moveit_dor_file_path=self.s3_config.dor_output_folder_path,
                    exemptions_file_name=self.exemptions_file_name,
                ).run()
            logger.info("Done - Moving Employer Exemptions Export File")
        else:
            logger.info(
                "Employer Exemptions Export to DOR is disabled. " "Skipping the export steps."
            )

    # TODO (PFMLPB-23385): add code to clean up temp dir and file


@background_task("employer-exemptions-export-for-dor")
def main():
    """Entry point for Employer Exemptions Export to DOR task."""
    EmployerExemptionsDORExporterTaskRunner(dor_config.get_s3_config()).run()


if __name__ == "__main__":
    main()
