import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.dor.step import DorStep

logger = massgov.pfml.util.logging.get_logger(__name__)


class EmployerExemptionsMoveITStep(DorStep):
    """
    This step copies the flat file for employer exemptions
    from the temporary location to DOR via MoveIT.
    """

    exemptions_file_path: str
    moveit_dor_file_path: str

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
        exemptions_file_path: str,
        moveit_dor_file_path: str,
        exemptions_file_name: str,
    ):
        self.exemptions_file_path = exemptions_file_path
        self.moveit_dor_file_path = moveit_dor_file_path
        self.exemptions_file_name = exemptions_file_name
        super().__init__(db_session, log_entry_db_session)

    def run_step(self):

        logger.info("Processing Employer Exemptions MoveIT Transfer")
        try:
            file_util.copy_file(
                self.exemptions_file_path,
                f"{self.moveit_dor_file_path}/{self.exemptions_file_name}",
            )
            logger.info(
                "Employer Exemptions Export File copied to MoveIT",
                extra={"moveit_dor_file_path": self.moveit_dor_file_path},
            )

        except Exception as e:
            logger.exception("Unable to copy employer exemptions file to DOR via MoveIT")
            raise e
