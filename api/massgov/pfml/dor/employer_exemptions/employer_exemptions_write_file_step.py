from typing import List

import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.dor.step import DorStep
from massgov.pfml.dor.util.employer_exemption_dor_export import (
    EmployerExemptionApplication,
    format_employer_exemption_line,
    get_modified_employer_exemptions,
)

logger = massgov.pfml.util.logging.get_logger(__name__)


class EmployerExemptionsFileWriterStep(DorStep):
    """
    This step creates the flat file for employer exemptions
    and puts it in a temporary location.
    The file will be used by the next step to copy to DOR via MoveIT.
    """

    exemptions_file_path: str

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
        exemptions_file_path: str,
    ):
        self.exemptions_file_path = exemptions_file_path
        super().__init__(db_session, log_entry_db_session)

    def run_step(self):
        logger.info("Processing Employer Exemptions Records")
        exemptions_for_dor = get_modified_employer_exemptions(self.db_session)
        if not exemptions_for_dor:
            logger.info("No exemptions found for DOR")
        else:
            logger.info(f"There are {len(exemptions_for_dor)} Employer Exemptions for DOR")
        self._build_exemptions_file(exemptions_for_dor)
        logger.info("Successfully wrote Employer Exemptions file")

    def _build_exemptions_file(
        self, exemptions_for_dor: List[EmployerExemptionApplication]
    ) -> None:
        """
        Build Employer Exemptions file
        :param exemptions_for_dor: List of Employer Exemption Applications
        :return: None
        """
        header_line = "H" + str(len(exemptions_for_dor)).rjust(10, "0")
        try:
            # write header and exemptions to file
            with file_util.open_stream(self.exemptions_file_path, mode="w") as file:
                file.write(header_line + "\n")
                for exemption in exemptions_for_dor:
                    exemption_line = format_employer_exemption_line(self.db_session, exemption)
                    file.write(exemption_line + "\n")

        except Exception as e:
            logger.exception("Unable to create Employers Exemptions file")
            raise e
