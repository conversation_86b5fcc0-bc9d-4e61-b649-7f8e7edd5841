[tool.poetry]
name = "massgov.pfml.api"
version = "0.1.0"
description = "Massachusetts Paid Family & Medical Leave API"
authors = ["Mass PFML API Team <<EMAIL>>"]
packages = [{ include = "massgov" }]
include = ["openapi.yaml", "newrelic.ini"]

[tool.poetry.dependencies]
alembic = "^1.4.2"
boto3 = "^1.28.24"
boto3-extensions = "^0.20.0"
connexion = { version = "3.1.0", extras = ["flask", "swagger-ui", "uvicorn"] }
csvsorter = "^1.4"
ebcdic = "^1.1.1"
factory_boy = "^3.2.0"
faker = "^37.0.0"
flask-bouncer = { version = "^0.3.0", optional = true }
flask_cors = { version = "^6.0.0", optional = true }
Flask-HTTPAuth = { version = "^4.2.0", optional = true }
gunicorn = { version = "^23.0.0", optional = true }
# Locking indirect lxml dependency to version that addresses CVE-2021-28957. Can remove when Zeep
# pins lxml dependency to version that addresses CVE-2021-28957.
lxml = { version = "^5.3.0", optional = true }
newrelic = "10.13.0"
paramiko = "^3.4.0"
phonenumbers = "^9.0.0"
psycopg2-binary = "^2.8.5"
puremagic = "^1.10"
pydantic = { version = "^1.10.13", extras = ["dotenv"] }
pydash = "^8.0.5"
python = "^3.10"
python-dateutil = "2.9.0.post0"
python-gnupg = "^0.5.0"
pyyaml = "^6.0.1"
requests = "2.32.4"
requests-pkcs12 = "^1.25"
requests_oauthlib = "^1.3.0"
smart-open = {version = "7.0.5", extras = ["s3"]}
sqlalchemy = "^2.0.31"
strict-rfc3339 = "^0.7"
tenacity = "^9.0.0"
xmlschema = "^3.4.3"
zeep = { version = "^3.4.0", optional = true }
Flask = "^2.2.5"
SQLAlchemy-Utils = "^0.41.2"
msal = "^1.32.3"
email-validator = "~2.2.0"
csv-diff = "^1.1"
deepdiff = "^8.3.0"
pip = "25.0.1"
werkzeug = "3.0.6"
cryptography = "^45.0.3"
pyopenssl = "^25.0.0"
authlib = "^1.3.1"
pyjwt = "^2.8.0"
jwcrypto = "^1.5.6"
urllib3 = "^2.5.0"
uvicorn-worker = "^0.2.0"
starlette = "0.40.0"
python-multipart = "0.0.18"
unidecode = "^1.3.8"
flask-compress = "^1.17"
numpy = "^2.2.5"

[tool.poetry.group.dev.dependencies]
bandit = "^1.6.2"
click = "^8.0.4"
coverage = "^7.6.12"
datamodel-code-generator = "^0.28.4"
debugpy = "^1.6.2"
flake8 = "^3.7.9"
flake8-alfred = "^1.1.1"
flake8-bugbear = "^20.1.4"
flaky = "^3.8.1"
freezegun = "^1.5.1"
isort = "^6.0.1"
moto = { extras = ["cognitoidp", "s3", "ssm"], version = "^5.0.2" }
mypy = "^1.8.0"
oyaml = "^1.0"
pydot = "^1.4.2"
pytest = "^8.3.5"
pytest-cov = "^6.1.1"
pytest-httpserver = "^1.1.2"
pytest-mock = "^3.14.0"
pytest-testmon = "^2.1.3"
pytest-timeout = "^2.3.1"
pytest-watcher = "^0.4.3"
pytest-xdist = "^3.6.1"
rich = "13.9.4"
sadisplay = "^0.4.9"
safety = "^1.9.0"
types-PyYAML = "6.0.12.20241230"
types-enum34 = "1.1.0"
types-ipaddress = "1.0.0"
types-paramiko = "^3.4.0.20240106"
types-python-dateutil = "2.8.0"
types-pytz = "2025.1.0.20250204"
types-requests = "2.25.6"
types-six = "1.16.1"
Pillow = "^11.3.0"
wheel = "^0.40.0"
black = "^25.1.0"
sqlfluff = "^3.3.1"

[tool.poetry.extras]
# These are packages only used by the API component itself. Historically, these
# were separated out so they could be excluded from the AWS Lambda builds to
# reduce the size of the Lambda package below AWS limits.
api-only-dependencies = [
    "connexion",
    "flask-bouncer",
    "flask-compress",
    "flask_cors",
    "Flask-HTTPAuth",
    "gunicorn",
    "lxml",
    "zeep",
]

[tool.poetry.scripts]
# Run `make build` (or `make deps` if running outside docker) after adding a script here, so that it can be run
# locally using `poetry run ...`.
massgov-pfml-api = "massgov.pfml.api.__main__:main"
db-migrate-up = "massgov.pfml.db.migrations.run:up"
db-migrate-down = "massgov.pfml.db.migrations.run:down"
db-migrate-down-all = "massgov.pfml.db.migrations.run:downall"
db-admin-create-db-users = "massgov.pfml.db.admin:create_users"
db-check-model-parity = "massgov.pfml.db.migrations.run:check_model_parity"
db-create-fineos-user = "massgov.pfml.db.create_api_user:create_fineos_user"
db-create-idp-user = "massgov.pfml.db.create_api_user:create_idp_user"
db-create-imageaccess-user = "massgov.pfml.db.create_api_user:create_imageaccess_user"
db-create-imageaccess-idp-user = "massgov.pfml.db.create_api_user:create_imageaccess_idp_user"
db-create-servicenow-user = "massgov.pfml.db.create_api_user:create_service_now_user"
dua-generate-and-send-employee-request-file = "massgov.pfml.dua.dua_generate_employee_request_file:main"
dua-generate-and-send-employer-request-file = "massgov.pfml.dua.dua_generate_employer_request_file:main"
execute-sql = "massgov.pfml.db.execute_sql:execute_sql"
employer-exemptions-export-for-dor = "massgov.pfml.dor.task.employer_exemptions_to_dor_export:main"
register-leave-admins-with-fineos = "massgov.pfml.fineos.leave_admin_creation.register_leave_admins_with_fineos:main"
dor-generate = "massgov.pfml.dor.mock.generate:main"
dor-import = "massgov.pfml.dor.importer.import_dor:handler"
dor_create_pending_filing_submission = "massgov.pfml.dor.pending_filing.pending_filing_submission:handler"
dor-pending-filing-response-import = "massgov.pfml.dor.pending_filing.pending_filing_response:handler"
dor-pending-filing-response-import-cli = "massgov.pfml.dor.pending_filing.pending_filing_response:main"
dor-import-exempt = "massgov.pfml.dor.importer.import_exempt_dor:handler"
dor-import-exempt-repush = "massgov.pfml.dor.importer.import_exempt_dor_repush:main"
dua-wages-from-dor-import = "massgov.pfml.dor.task.dua_wages_from_dor_import:main"
generate-wagesandcontributions = "massgov.pfml.api.generate_wagesandcontributions:main"
load-employers-to-fineos = "massgov.pfml.fineos.employer_load:handler"
load-service-agreements-to-fineos = "massgov.pfml.fineos.service_agreement_load:handler"
leave-admin-csv-export = "massgov.pfml.tasks.leave_admin_csv_export:main"
leave-admin-review-reminder-notifications = "massgov.pfml.tasks.leave_admin_review_reminder_notifications:main"
fineos-eligibility-feed-export = "massgov.pfml.fineos.eligibility_feed_export.eligibility_export:main"
fineos-import-service-agreements = "massgov.pfml.fineos.import_fineos_service_agreements:main"
pub-payments-process-fineos = "massgov.pfml.delegated_payments.task.process_fineos_extracts:main"
pub-payments-create-pub-files = "massgov.pfml.delegated_payments.task.process_pub_payments:main"
pub-payments-process-pub-returns = "massgov.pfml.delegated_payments.task.process_pub_responses:main"
pub-payments-process-snapshot = "massgov.pfml.delegated_payments.task.process_fineos_reconciliation_extracts:main"
pub-payments-backfill-data = "massgov.pfml.delegated_payments.task.process_backfill_pub_payments:main"
pub-overpayments-backfill-data = "massgov.pfml.delegated_payments.task.process_backfill_overpayments:main"
pub-payments-mock-generate = "massgov.pfml.delegated_payments.mock.mock_generate:main"
pub-payments-copy-audit-report = "massgov.pfml.delegated_payments.audit.copy_audit_report_task:main"
pub-payments-verify-fineos-extract = "massgov.pfml.delegated_payments.task.verify_fineos_extract:main"
process-prepaid-debit-registration = "massgov.pfml.delegated_payments.task.process_prepaid_debit_card_registration:main"
fineos-import-iaww = "massgov.pfml.delegated_payments.task.process_iaww_from_fineos:main"
sync-fineos-extracts-to-pfml-models = "massgov.pfml.tasks.sync_fineos_extracts_to_pfml_models:main"
ach-reader = "massgov.pfml.delegated_payments.util.ach.ach_tool:ach_reader"
check-reader = "massgov.pfml.delegated_payments.pub.check_tool:check_reader"
fineos-daily-generate = "massgov.pfml.fineos.mock.generate:main"
fineos-import-employee-updates = "massgov.pfml.fineos.import_fineos_updates:main"
fineos-bucket-tool = "massgov.pfml.fineos.tool.bucket:main"
fineos-update-xsds = "massgov.pfml.fineos.wscomposer.fineos_update_xsds:handler"
fineos-import-la-units = "massgov.pfml.fineos.import_fineos_leave_admin_org_units:handler"
cps-errors-crawler = "massgov.pfml.fineos.etl.cps_errors_crawler:main"
import-fineos-to-warehouse = "massgov.pfml.fineos.import_fineos_to_warehouse:handler"
reductions-process-agency-data = "massgov.pfml.reductions.process_agency_data:main"
reductions-send-claimant-lists-to-agencies = "massgov.pfml.reductions.send_claimant_lists_to_agencies:main"
transmogrify-state-logs = "massgov.pfml.util.tasks.transmogrify_states:transmogrify_states"
delegated-payment-audit-rejects-generate = "massgov.pfml.delegated_payments.audit.mock.delegated_payment_audit_generator:generate_payment_rejects_file"
delegated-payment-check-response-generate = "massgov.pfml.delegated_payments.mock.generate_check_response:generate_check_response_files"
report-sequential-employment = "massgov.pfml.report.sequential_employment:main"
update-gender-data-from-rmv = "massgov.pfml.rmv.update_gender_data:main"
dua-import-employee-demographics = "massgov.pfml.dua.import_employee_demographics:main"
dua-import-employer = "massgov.pfml.dua.import_employer:main"
dua-import-employer-unit = "massgov.pfml.dua.import_employer_unit:main"
pub-payments-process-1099-documents = "massgov.pfml.delegated_payments.task.process_1099_documents:main"
sftp-tool = "massgov.pfml.sftp.utility:main"
backfill-benefit-years = "massgov.pfml.api.eligibility.task.backfill_benefit_years:main"
backfill-benefit-year-base-periods = "massgov.pfml.api.eligibility.task.backfill_benefit_year_base_periods:main"
backfill-fineos-vbi-documents = "massgov.pfml.tasks.backfill_fineos_vbi_documents:main"
backfill-ready-for-review-time-column = "massgov.pfml.tasks.backfill_ready_for_review_time_column:main"
appeals-generate-intake-pdfs = "massgov.pfml.appeals.generate_intake_pdfs:main"
appeals-import-extract = "massgov.pfml.appeals.import_extract.service:main"
add-merger-acquisition = "massgov.pfml.services.merger_acquisition.add_merger_acquisition_task:main"
pub-process-weekly-reports = "massgov.pfml.delegated_payments.task.process_weekly_reports:main"
child-support-dor-import = "massgov.pfml.dor.task.child_support_dor_import:main"
mock-fineos-writeback-status-for-payments = "massgov.pfml.delegated_payments.mock.mock_fineos_writeback_status_for_payments:main"
analyze-fineos-entitlement-periods = "massgov.pfml.api.eligibility.task.analyze_fineos_entitlement_periods:main"
update-uuid = "massgov.pfml.util.uuids.task.update_uuid:main"
create-submittable-application-spanning-benefit-years = "massgov.pfml.services.applications.create_submittable_application_spanning_benefit_years:main"
add-employer = "massgov.pfml.util.admin.task.add_employer:main"
patch-employer = "massgov.pfml.util.admin.task.patch_employer:main"
add-leave-admin = "massgov.pfml.util.admin.task.add_leave_admin:main"
change-default-organization = "massgov.pfml.util.admin.task.change_default_organization:main"
remove-leave-admin = "massgov.pfml.util.admin.task.deactivate_leave_admin:main"
dfml-fines-and-repayments = "massgov.pfml.dfml_fines_repayments.task.process_dfml_fines_repayments:main"
process-new-relic-dashboard-recovery = "massgov.pfml.new_relic_dashboard.task.process_dashboard_recovery:main"
update-employer = "massgov.pfml.util.admin.task.update_employer:main"
process-overpayment-referrals = "massgov.pfml.delegated_payments.task.process_overpayment_referrals:main"
process-mmars-response-file = "massgov.pfml.delegated_payments.task.process_mmars_response_file:main"
process-overpayment-collections = "massgov.pfml.delegated_payments.task.process_overpayment_collections:main"
process-rfi-renotifications = "massgov.pfml.notifications.task.process_rfi_renotifications:main"
compare-fineos-urls = "massgov.pfml.fineos.api_compare.compare_fineos_urls:main"
address-extract = "massgov.pfml.tasks.address_extract:main"
clean-up-oauth-log = "massgov.pfml.util.tasks.clean_up_oauth_log:main"
process-overpayment-generate-mock-mmars-response = "massgov.pfml.delegated_payments.mock.mmars_response_generator:main"
update-application = "massgov.pfml.util.admin.task.update_application:main"
submit-deferred-items = "massgov.pfml.tasks.submit_deferred_items:main"
update-errored-child-support-payments = "massgov.pfml.util.admin.task.update_errored_child_support_payments:main"

[tool.black]
line-length = 100

[tool.isort]
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
line_length = 100

[tool.mypy]
color_output = true
error_summary = true
ignore_missing_imports = true
namespace_packages = true
pretty = true
show_column_numbers = true
show_error_context = true
warn_unused_configs = true

local_partial_types = true
check_untyped_defs = true
disallow_incomplete_defs = true
no_implicit_optional = true
strict_equality = true
warn_no_return = true
warn_redundant_casts = true
warn_unreachable = true
warn_unused_ignores = true

plugins = ["pydantic.mypy"]

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true
warn_untyped_fields = true

[tool.pytest.ini_options]
testpaths = "tests"
norecursedirs = "tests/helpers"
filterwarnings = [
    "ignore::DeprecationWarning:boto3.*",
    "ignore::DeprecationWarning:connexion.*",
    "ignore::DeprecationWarning:zeep.loader.*",
    "ignore::DeprecationWarning:newrelic.*",
    # Though attempted, doesn't seem to have actually been fixed upstream:
    # - https://github.com/joke2k/faker/pull/1614
    # - https://github.com/joke2k/faker/pull/1614#issuecomment-1077679795
    # - https://github.com/joke2k/faker/issues/1824
    "ignore:.*randrange.*:DeprecationWarning:faker.*",
]
addopts = "--strict-markers"
markers = [
    "integration: mark a test as an integration test (requires external connections/real resources)",
    "dev_focus: Convenience marker to target tests during development (run focused tests using `make test-watch-focus`)",
    "flaky: Indicate a test is known to inconsistently pass/fail",
]

[tool.coverage.report]
exclude_lines = [
    # Have to re-enable the standard pragma
    "pragma: no cover",
    # https://github.com/nedbat/coveragepy/issues/970
    "@overload",
]

[tool.coverage.run]
omit = ["massgov/pfml/db/migrations/*.py"]

[build-system]
requires = ["poetry>=1.2.0"]
build-backend = "poetry.masonry.api"
