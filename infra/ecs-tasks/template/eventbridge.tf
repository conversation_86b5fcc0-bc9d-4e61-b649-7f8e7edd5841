# Recurring task schedules are configured here using the ecs_eventbridge_schedule module.
#
# ## NOTE: If you are adding a new scheduled event here, please add monitoring by including it
#          in the list in infra/modules/alarms_api_portal/alarms-aws.tf.

############################################################
#          EventBridge Schedules for ECS Tasks             #
############################################################
module "fineos_bucket_tool_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_fineos_data_export_tool

  scheduled_task_name = "fineos-data-export-tool"
  schedule_expression = var.fineos_data_export_tool_schedule_expression
  group_name          = var.environment_name

  scheduler_arn           = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn             = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids          = var.app_subnet_ids
  security_group_ids      = [aws_security_group.tasks.id]
  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["fineos-bucket-tool"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "fineos-bucket-tool"
      command = [
        "fineos-bucket-tool",
        "--recursive",
        "--dated-folders",
        "--copy_dir", "${var.fineos_data_export_path}",
        "--to_dir", "s3://${data.aws_s3_bucket.business_intelligence_tool.bucket}/fineos/dataexports",
        "--file_prefixes", "all",
        "--log", "fineos-data-export-tool",
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "fineos_extract_schedule" {
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_fineos_report_extracts_tool
  scheduled_task_name = "fineos-report-extracts-tool"
  schedule_expression = var.fineos_report_extract_tool_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["fineos-bucket-tool"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "fineos-bucket-tool"
      command = [
        "fineos-bucket-tool",
        "--recursive",
        "--dated-folders",
        "--copy_dir", "${var.fineos_report_export_path}",
        "--to_dir", "s3://${data.aws_s3_bucket.business_intelligence_tool.bucket}/fineos/dataexports",
        "--file_prefixes", "all",
        "--log", "fineos-report-extracts-tool",
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "fineos_monthly_extract_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_fineos_monthly_extract_scheduler

  scheduled_task_name = "fineos-monthly-extract-scheduler"
  schedule_expression = var.fineos_monthly_extract_scheduler_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["fineos-bucket-tool"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "fineos-bucket-tool"
      command = [
        "fineos-bucket-tool",
        "--recursive",
        "--dated-folders",
        "--copy_dir", "${var.fineos_monthly_data_export_path}",
        "--to_dir", "s3://${data.aws_s3_bucket.business_intelligence_tool.bucket}/fineos/dataexports",
        "--file_prefixes", "all",
        "--log", "fineos-monthly-extract-scheduler",
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "address_extract_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_address_extract_schedule

  scheduled_task_name = "address_extract"
  schedule_expression = var.address_extract_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["address-extract"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "import_fineos_to_warehouse_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_import_fineos_to_warehouse

  scheduled_task_name = "import-fineos-to-warehouse"
  schedule_expression = var.import_fineos_to_warehouse_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["import-fineos-to-warehouse"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "fineos_error_extract_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_fineos_error_extract_tool

  scheduled_task_name = "fineos-error-extract-tool"
  schedule_expression = var.fineos_error_extract_tool_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["fineos-bucket-tool"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "fineos-bucket-tool"
      command = [
        "fineos-bucket-tool",
        "--recursive",
        "--dated-folders",
        "--copy_dir", "${var.fineos_error_export_path}",
        "--to_dir", "s3://${data.aws_s3_bucket.agency_transfer.bucket}/cps-errors/received/",
        "--archive_dir", "s3://${data.aws_s3_bucket.agency_transfer.bucket}/cps-errors/processed/",
        "--file_prefixes", "all",
        "--log", "fineos-error-extract-tool",
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "export_leave_admins_created_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_export_leave_admins_created

  scheduled_task_name = "export-leave-admins-created"
  schedule_expression = var.export_leave_admins_created_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["execute-sql"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "execute-sql"
      command = [
        "execute-sql",
        "--job_name=ExportLeaveAdminsCreated",
        "--s3_output=api_db/accounts_created",
        "--s3_bucket=massgov-pfml-${var.environment_name}-business-intelligence-tool",
        "--use_date",
        "SELECT pu.email_address as email, pu.user_id as user_id, ula.fineos_web_id as fineos_id,e.employer_name as employer_name,e.employer_dba as employer_dba,e.employer_fein as fein,e.fineos_employer_id as fineos_customer_number,ula.created_at as date_created FROM public.user pu LEFT JOIN link_user_leave_administrator ula ON (pu.user_id=ula.user_id) LEFT JOIN employer e ON (ula.employer_id=e.employer_id) WHERE ula.created_at >= now() - interval '24 hour'"
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "export_psd_report_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_export_psd_report

  scheduled_task_name = "export-psd-report"
  schedule_expression = var.export_psd_report_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["execute-sql"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "execute-sql"
      command = [
        "execute-sql",
        "--job_name=ExportPsdReport",
        "--s3_output=dfml-reports/applications_passed_part_3",
        "--s3_bucket=massgov-pfml-${var.environment_name}-reports",
        "select claim_id, fineos_absence_id, updated_at FROM claim where claim_id in (select claim_id from application where completed_time is not null and completed_time > CURRENT_DATE - 1) and (is_id_proofed = false or is_id_proofed is null)"
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "export_60_day_comms_report_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_export_60_day_comms_report

  scheduled_task_name = "export-60-day-comms-report"
  schedule_expression = var.export_60_day_comms_report_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["execute-sql"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "execute-sql"
      command = [
        "execute-sql",
        "--job_name=Export60DayCommsReport",
        "--s3_output=dfml-reports/applications_entering_60_day_window",
        "--s3_bucket=massgov-pfml-${var.environment_name}-reports",
        "${replace(file("${path.module}/sql_reports/60_day_comms_report.sql"), "\n", " ")}"
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "cps_errors_crawler_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_cps_errors_crawler

  scheduled_task_name = "cps_errors_crawler"
  schedule_expression = var.cps_errors_crawler_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["cps-errors-crawler"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "reductions_dia_send_claimant_lists_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_reductions_send_claimant_lists_to_agencies_schedule

  scheduled_task_name = "reductions-dia-send-claimant-lists"
  schedule_expression = var.reductions_dia_send_claimant_lists_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["reductions-send-claimant-lists"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "reductions-send-claimant-lists"
      command = [
        "reductions-send-claimant-lists-to-agencies",
        "--steps=DIA"
      ]
      environment = [
        {
          name  = "FAILED_CLAIMANTS_THRESHOLD_PCT",
          value = "5"
        }
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "reductions_dua_send_claimant_lists_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_reductions_send_claimant_lists_to_agencies_schedule

  scheduled_task_name = "reductions-dua-send-claimant-lists"
  schedule_expression = var.reductions_dua_send_claimant_lists_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["reductions-send-claimant-lists"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "reductions-send-claimant-lists"
      command = [
        "reductions-send-claimant-lists-to-agencies",
        "--steps=DUA"
      ]
      environment = [
        {
          name  = "FAILED_CLAIMANTS_THRESHOLD_PCT",
          value = "5"
        }
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "reductions_process_agency_data_lists_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_reductions_process_agency_data_schedule

  scheduled_task_name = "reductions-process-agency-data"
  schedule_expression = var.reductions_process_agency_data_lists_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["reductions-process-agency-data"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "pub_payments_process_fineos_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_pub_automation_fineos

  scheduled_task_name = "pub-payments-process-fineos"
  schedule_expression = var.pub_payments_process_fineos_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["pub-payments-process-fineos"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "pub-payments-process-fineos"
      environment = [
        {
          name  = "DB_STATEMENT_TIMEOUT",
          value = "5400000"
        },
        {
          name  = "FAILED_ADDRESS_ERROR_THRESHOLD_PCT",
          value = var.failed_address_error_threshold_pct
        },
        {
          name  = "FAILED_ADDRESS_WARNING_THRESHOLD_PCT",
          value = var.failed_address_warning_threshold_pct
        },
        {
          name  = "ADDRESS_VALIDATION_STATUS_CHANGE_THRESHOLD_COUNT",
          value = var.address_validation_status_change_threshold_count
        }
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "pub_payments_process_fineos_monday_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_pub_automation_fineos_monday

  scheduled_task_name = "pub-payments-process-fineos-monday"
  schedule_expression = var.pub_payments_process_fineos_monday_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["pub-payments-process-fineos"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "pub-payments-process-fineos"
      environment = [
        {
          name  = "DB_STATEMENT_TIMEOUT",
          value = "5400000"
        },
        {
          name  = "FAILED_ADDRESS_ERROR_THRESHOLD_PCT",
          value = var.failed_address_error_threshold_pct
        },
        {
          name  = "FAILED_ADDRESS_WARNING_THRESHOLD_PCT",
          value = var.failed_address_warning_threshold_pct
        },
        {
          name  = "ADDRESS_VALIDATION_STATUS_CHANGE_THRESHOLD_COUNT",
          value = var.address_validation_status_change_threshold_count
        }
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "pub_payments_verify_fineos_extract_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_pub_automation_fineos

  scheduled_task_name = "pub-payments-verify-fineos-extract"
  schedule_expression = var.pub_payments_verify_fineos_extract_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["pub-payments-verify-fineos-extract"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "sunday_pub_payments_process_fineos_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_sunday_pub_payments_process_fineos_schedule

  scheduled_task_name = "sunday-pub-claimant-extract"
  schedule_expression = var.sunday_pub_payments_process_fineos_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["pub-payments-process-fineos"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "pub-payments-process-fineos"
      command = [
        "pub-payments-process-fineos",
        "--steps", "wait-for-fineos-extracts", "consume-fineos-claimant", "consume-vbi-task-report-delta", "consume-vbi-entitlemtperiod",
        "sync-employee-feed-aggregate", "sync-eft", "sync-claims", "sync-absence-periods", "sync-employees", "sync-leave-requests", "sync-entitlement-periods-to-benefit-years", "create-success-file"
      ],
      environment = [
        {
          name  = "DB_STATEMENT_TIMEOUT",
          value = "5400000"
        },
        {
          name  = "FAILED_ADDRESS_ERROR_THRESHOLD_PCT",
          value = var.failed_address_error_threshold_pct
        },
        {
          name  = "FAILED_ADDRESS_WARNING_THRESHOLD_PCT",
          value = var.failed_address_warning_threshold_pct
        },
        {
          name  = "ADDRESS_VALIDATION_STATUS_CHANGE_THRESHOLD_COUNT",
          value = var.address_validation_status_change_threshold_count
        }
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "saturday_pub_payments_process_fineos_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_saturday_pub_payments_process_fineos_schedule

  scheduled_task_name = "saturday-pub-claimant-extract"
  schedule_expression = var.saturday_pub_payments_process_fineos_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["pub-payments-process-fineos"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "pub-payments-process-fineos"
      command = [
        "pub-payments-process-fineos",
        "--steps", "wait-for-fineos-extracts", "consume-fineos-claimant", "consume-vbi-task-report", "consume-vbi-task-report-delta", "consume-vbi-entitlemtperiod",
        "sync-employee-feed-aggregate", "sync-eft", "sync-claims", "sync-absence-periods", "sync-employees", "sync-leave-requests", "sync-entitlement-periods-to-benefit-years", "create-success-file"
      ],
      environment = [
        {
          name  = "DB_STATEMENT_TIMEOUT",
          value = "5400000"
        },
        {
          name  = "FAILED_ADDRESS_ERROR_THRESHOLD_PCT",
          value = var.failed_address_error_threshold_pct
        },
        {
          name  = "FAILED_ADDRESS_WARNING_THRESHOLD_PCT",
          value = var.failed_address_warning_threshold_pct
        },
        {
          name  = "ADDRESS_VALIDATION_STATUS_CHANGE_THRESHOLD_COUNT",
          value = var.address_validation_status_change_threshold_count
        }
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "fineos_import_la_units_schedule" {
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_fineos_import_la_units
  scheduled_task_name = "fineos-import-la-units"
  schedule_expression = var.fineos_import_la_units_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["fineos-import-la-units"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "weekend_fineos_import_la_units_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_weekend_fineos_import_la_units

  scheduled_task_name = "weekend-fineos-import-la-units"
  schedule_expression = var.weekend_fineos_import_la_units_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["fineos-import-la-units"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "fineos_snapshot_extract_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_fineos_snapshot_extract_tool

  scheduled_task_name = "fineos-snapshot-extracts-tool"
  schedule_expression = var.fineos_snapshot_extract_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["fineos-bucket-tool"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "fineos-bucket-tool"
      command = [
        "fineos-bucket-tool",
        "--copy", "s3://${data.aws_s3_bucket.agency_transfer.bucket}/payments/static/fineos-query/config-fineos-payments-snapshot.json",
        "--to", "${var.fineos_adhoc_data_export_path}/config/config.json",
        "--log", "fineos-snapshot-extracts-tool",
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "appeals_generate_intake_pdfs_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_appeals_generate_intake_pdfs

  scheduled_task_name = "appeals-generate-intake-pdfs"
  schedule_expression = var.appeals_generate_intake_pdfs_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["appeals-generate-intake-pdfs"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "appeals_import_extract_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_appeals_import_extract

  scheduled_task_name = "appeals-import-extract"
  schedule_expression = var.appeals_import_extract_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["appeals-import-extract"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "appeals-import-extract"
      command = [
        "appeals-import-extract",
        "--folder", "${var.fineos_data_export_path}"
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "pub_payments_process_snapshot_schedule" {
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_pub_payments_process_snapshot_fineos
  scheduled_task_name = "pub-payments-process-snapshot"
  schedule_expression = var.pub_payments_process_snapshot_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["pub-payments-process-snapshot"].arn

  input = jsonencode({
    containerOverrides = [{
      name = "pub-payments-process-snapshot"
      environment = [
        {
          name  = "DB_STATEMENT_TIMEOUT",
          value = "8100000"
        }
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "pub_payments_copy_audit_report_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_pub_payments_copy_audit_report_schedule

  scheduled_task_name = "pub-payments-copy-audit-report"
  schedule_expression = var.pub_payments_copy_audit_report_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["pub-payments-copy-audit-report"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "standalone_fineos_import_employee_updates_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_standalone_fineos_import_employee_updates

  scheduled_task_name = "fineos-import-employee-updates"
  schedule_expression = var.fineos_import_employee_updates_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["fineos-import-employee-updates"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "saturday_standalone_fineos_import_employee_updates_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_saturday_standalone_fineos_import_employee_updates

  scheduled_task_name = "saturday-fineos-import-employee-updates"
  schedule_expression = var.saturday_fineos_import_employee_updates_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["fineos-import-employee-updates"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "pub_process_weekly_reports_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_pub_process_weekly_reports

  scheduled_task_name = "pub-process-weekly-reports"
  schedule_expression = var.pub_process_weekly_reports_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["pub-process-weekly-reports"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "child_support_dor_import_schedule" {
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_child_support_automation
  scheduled_task_name = "child-support-dor-import"
  schedule_expression = var.child_support_dor_import_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["child-support-dor-import"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "process_prepaid_debit_registration_schedule" {
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_process_prepaid_debit_registration
  scheduled_task_name = "process-prepaid-debit-registration"
  schedule_expression = var.process_prepaid_debit_registration_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["process-prepaid-debit-registration"].arn
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "pub_payments_process_1099_documents_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_1099_generator

  scheduled_task_name = "process-1099-documents"
  schedule_expression = var.pub_payments_process_1099_documents_schedule_expression

  group_name = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks_1099.arn

  input = jsonencode({
    containerOverrides = [{
      name = "pub-payments-process-1099-documents"
      command = [
        "pub-payments-process-1099-documents",
        "--steps", "audit-batch", "validate-correction-batch", "populate-payments", "populate-withholdings", "populate-refunds", "populate-1099", "prepare-generate-1099-documents", "generate-1099-documents", "merge-1099-documents", "report", "upload-1099-documents", "copy-1099-documents"
      ],
      environment = [
        {
          name  = "IRS_1099_CORRECTION_IND",
          value = var.irs_1099_correction_ind
        },
        {
          name  = "ENABLE_OFFSET_GET_1099",
          value = var.enable_offset_get_1099
        },
        {
          name  = "UPLOAD_1099_DOC_BATCH_START",
          value = var.upload_1099_doc_batch_start
        },
        {
          name  = "UPLOAD_1099_DOC_BATCH_END",
          value = var.upload_1099_doc_batch_end
        },
        {
          name  = "IRS_1099_TAX_YEAR",
          value = var.irs_1099_tax_year
        },
        {
          name  = "GENERATE_1099_MAX_FILES",
          value = var.generate_1099_max_files
        },
        {
          name  = "UPLOAD_MAX_FILES_TO_FINEOS",
          value = var.upload_max_files_to_fineos
        }
      ]
    }]
  })
}
# - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
module "process_new_relic_dashboard_recovery_schedule" {
  count               = var.environment_name == "prod" ? 1 : 0
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_process_new_relic_dashboard_recovery
  scheduled_task_name = "process-new-relic-dashboard-recovery"
  schedule_expression = var.process_new_relic_dashboard_recovery_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["process-new-relic-dashboard-recovery"].arn
}

module "process_overpayment_referrals_schedule" {
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_process_overpayment_referrals
  scheduled_task_name = "process-overpayment-referrals"
  schedule_expression = var.process_overpayment_referrals_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["process-overpayment-referrals"].arn
  input = jsonencode({
    containerOverrides = [{
      name = "process-overpayment-referrals"
      command = [
        "process-overpayment-referrals",
        "--steps", "sync-vcc-records", "process-vcc-records", "send-vcc-inf-file", "sync-re-records", "process-re-records", "send-re-inf-file"
      ],
    }]
  })
}

module "process_mmars_response_file_schedule" {
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_process_mmars_response_file
  scheduled_task_name = "process-mmars-response-file"
  schedule_expression = var.process_mmars_response_file_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["process-mmars-response-file"].arn
}

module "process_overpayment_collections_schedule" {
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_process_overpayment_collections
  scheduled_task_name = "process-overpayment-collections"
  schedule_expression = var.process_overpayment_collections_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["process-overpayment-collections"].arn
}

module "process_rfi_renotifications_schedule" {
  source              = "./ecs_eventbridge_schedule"
  scheduler_enabled   = var.enable_re_notification
  scheduled_task_name = "process-rfi-renotifications"
  schedule_expression = var.process_rfi_renotifications_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["process-rfi-renotifications"].arn
}

module "dua_wages_from_dor_import_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_dua_wages_from_dor_import_schedule

  scheduled_task_name = "dua-wages-from-dor-import"
  schedule_expression = var.dua_wages_from_dor_import_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["dua-wages-from-dor-import"].arn
}


module "leave_admin_review_reminder_notifications_schedule" {
  source            = "./ecs_eventbridge_schedule"
  scheduler_enabled = var.enable_leave_admin_review_reminder_notifications

  scheduled_task_name = "leave-admin-review-reminder-notifications"
  schedule_expression = var.leave_admin_review_reminder_notifications_schedule_expression
  group_name          = var.environment_name

  scheduler_arn      = aws_iam_role.eventbridge_ecs_task_schedule.arn
  cluster_arn        = data.aws_ecs_cluster.cluster.arn
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  ecs_task_definition_arn = aws_ecs_task_definition.ecs_tasks["leave-admin-review-reminder-notifications"].arn
}
