variable "environment_name" {
  description = "Name of the environment"
  type        = string
}

variable "service_docker_tag" {
  description = "Tag of the docker image to associate with ECS tasks"
  type        = string
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "pdf_api_lb_port" {
  description = "Port of the load balancer reserved for PDF API target groups"
  type        = number
}

variable "vpc_name" {
  description = "Used to grab the correct LB name"
  type        = string
}

variable "service_now_base_url" {
  description = "URL for Service Now post requests"
  type        = string
  default     = ""
}

variable "portal_base_url" {
  description = "Portal base URL to use when creating links"
  type        = string
  default     = ""
}

variable "app_subnet_ids" {
  type        = list(string)
  description = "App subnet IDS."
  default     = []
}

variable "fineos_client_customer_api_url" {
  description = "URL of the FINEOS Customer API"
  type        = string
  default     = ""
}

variable "fineos_client_group_client_api_url" {
  description = "URL of the FINEOS Group Client API"
  type        = string
  default     = ""
}

variable "fineos_client_integration_services_api_url" {
  description = "URL of the FINEOS Integration Services API"
  type        = string
  default     = ""
}

variable "fineos_client_wscomposer_api_url" {
  description = "URL of the FINEOS Web Services Composer API"
  type        = string
  default     = ""
}

variable "fineos_client_wscomposer_user_id" {
  description = "User id for FINEOS Web Services Composer API"
  type        = string
  default     = "CONTENT"
}

variable "fineos_client_soap_user_id" {
  description = "User id for FINEOS SOAP API endpoints"
  type        = string
  default     = "CONTENT"
}

variable "fineos_client_oauth2_url" {
  description = "URL of the FINEOS OAuth2 token endpoint."
  type        = string
  default     = ""
}

variable "fineos_aws_iam_role_arn" {
  description = "ARN for role in the FINEOS AWS account that must be used to access resources inside of it"
  type        = string
  default     = ""
}

variable "fineos_aws_iam_role_external_id" {
  description = "ExternalId paramter for assuming role specified by fineos_aws_iam_role_arn"
  type        = string
  default     = ""
}

variable "fineos_report_export_path" {
  description = "Location for additional FINEOS exports"
  type        = string
  default     = ""
}
variable "fineos_eligibility_feed_output_directory_path" {
  description = "Location the FINEOS Eligibility Feed export should write output to"
  type        = string
  default     = ""
}

variable "pfml_fineos_eligibility_feed_archive_directory_path" {
  description = "Location the FINEOS Eligibility Feed export should archive to"
  type        = string
  default     = ""
}

variable "pfml_fineos_eligibility_feed_temp_directory_path" {
  description = "Location the FINEOS Eligibility Feed export files are downloaded to"
  type        = string
  default     = "/tmp"
}

variable "fineos_import_employee_updates_input_directory_path" {
  description = "Location of the FINEOS extract to process into our DB."
  type        = string
  default     = null
}

variable "logging_level" {
  description = "Logging level override"
  type        = string
  default     = ""
}

variable "fineos_data_export_path" {
  description = "FINEOS generates data export files for PFML API to pick up"
  type        = string
  default     = ""
}

variable "fineos_monthly_data_export_path" {
  description = "FINEOS generates monthly data export files for PFML API to pick up"
  type        = string
  default     = ""
}

variable "fineos_adhoc_data_export_path" {
  description = "FINEOS generates adhoc data export files from custom queries for PFML API to pick up"
  type        = string
  default     = ""
}

variable "fineos_data_import_path" {
  description = "PFML API generates files for FINEOS to process"
  type        = string
  default     = ""
}

variable "fineos_error_export_path" {
  description = "FINEOS generates error export files for PFML API to pick up"
  type        = string
  default     = ""
}

variable "fineos_is_running_v24" {
  description = "Whether the environment is running Fineos V24+"
  type        = string
  default     = "0"
}

variable "pfml_fineos_inbound_path" {
  description = "PFML API stores a copy of all files that FINEOS generates for us"
  type        = string
  default     = ""
}

variable "pfml_fineos_outbound_path" {
  description = "PFML API stores a copy of all files that we generate for FINEOS"
  type        = string
  default     = ""
}

variable "pfml_bi_tool_extract_path" {
  description = "Path to extracts for the business intelligence tool."
  type        = string
  default     = ""
}

variable "dor_fineos_etl_schedule_expression" {
  description = "EventBridge schedule for DOR FINEOS ETL in US/Eastern time"
  type        = string
  # Default to 7:30 PM ET
  default = "cron(30 19 * * ? *)"
}

variable "dor_fineos_etl_schedule_enabled" {
  description = "Enable the DOR FINEOS ETL schedule"
  type        = bool
  default     = true
}

variable "eolwd_moveit_sftp_uri" {
  description = "URI for LWD MOVEit instance"
  type        = string
  default     = ""
}

variable "pfml_error_reports_path" {
  description = "PFML API stores a copy of all error reports generated"
  type        = string
  default     = ""
}

variable "dfml_project_manager_email_address" {
  description = "DFML Project manager email address"
  type        = string
  default     = ""
}

variable "dfml_business_operations_email_address" {
  description = "Email address for DFML Business Operations team"
  type        = string
  default     = ""
}

variable "agency_reductions_email_address" {
  description = "Generic send from address for outgoing emails"
  type        = string
  default     = ""
}

variable "agency_service_desk_email_address" {
  description = "Email address for operations team to CC for reductions reports"
  type        = string
  default     = ""
}

variable "enable_register_admins_job" {
  description = "Is the cloudwatch event to register admins enabled?"
  type        = bool
  default     = true
}

variable "task_failure_email_address_list" {
  type        = list(string)
  description = "List of email addresses"
  default     = []
}

variable "payment_audit_report_upload_notification_email_address" {
  description = "Email ids when payment audit report is uploaded"
  type        = string
  default     = "<EMAIL>"
}

variable "payment_audit_report_upload_notification_cc_email_address" {
  description = "CC Email ids when payment audit report is uploaded"
  type        = string
  default     = "<EMAIL>"
}

variable "payment_audit_report_available_notification_email_address" {
  description = "Email ids when payment audit report is available"
  type        = string
  default     = "<EMAIL>"
}

variable "payment_audit_report_available_notification_cc_email_address" {
  description = "CC Email ids when payment audit report is available"
  type        = string
  default     = "<EMAIL>"
}

variable "payment_audit_report_outbound_folder_path" {
  description = "Payment audit report outbound folder path"
  type        = string
  default     = ""
}

variable "payment_audit_report_sent_folder_path" {
  description = "Payment audit report sent folder path"
  type        = string
  default     = ""
}

variable "enable_pub_automation_create_pub_files" {
  description = "Enable scheduling for pub automation create pub files task"
  default     = false
}

variable "enable_pub_automation_process_returns" {
  description = "Enable scheduling for pub automation return processing task"
  default     = false
}

variable "enable_pub_automation_claimant_address_validation" {
  description = "Enable scheduling for pub automation claimant address validation task"
  default     = false
}

variable "enable_sync_fineos_extracts_to_pfml_models" {
  description = "Enable scheduling the Sync FINEOS extracts to PFML models task"
  default     = false
}

variable "enable_sync_leave_requests_step" {
  description = "Enable sync leave requests"
  type        = string
  default     = "0"
}

# @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review
variable "enable_mark_applications_ready_for_review" {
  description = "Enable marking applications ready for review"
  type        = string
  default     = "0"
}

variable "enable_re_notification" {
  description = "Enable Re-Notification workflow"
  type        = string
  default     = "0"
}

variable "enable_pub_automation_process_1099_documents" {
  description = "Enable scheduling for pub automation 1099 documents processing task"
  default     = false
}

variable "service_agreement_load" {
  description = "Service agreement load"
  type        = string
  default     = "false"
}

variable "rmv_client_base_url" {
  description = "The base URL for the Registry of Motor Vehicles (RMV) API."
  type        = string
  default     = ""
}

variable "rmv_client_certificate_binary_arn" {
  description = "The secretsmanager ARN for the Registry of Motor Vehicles (RMV) certificate."
  type        = string
  default     = ""
}

variable "rmv_api_behavior" {
  description = "Specifies if the RMV response is mocked"
  type        = string
  default     = "fully_mocked"
}

variable "usbank_client_base_url" {
  description = "The base URL for the US Bank REST API client."
  type        = string
  default     = ""
}

variable "usbank_client_soap_base_url" {
  description = "The base URL for the US Bank SOAP API client."
  type        = string
  default     = ""
}

variable "usbank_client_oauth2_url" {
  description = "The oauth2 URL for the US Bank client authentication."
  type        = string
  default     = ""
}

variable "usbank_client_certificate_binary_arn" {
  description = "The secretsmanager ARN for the US Bank API client certificate."
  type        = string
  default     = ""
}

variable "localhost_pdf_api_host" {
  description = "URL of the Localhost PDF API used by 1099 tasks"
  type        = string
  default     = ""
}

variable "usbank_transId" {
  description = "The transId for US Bank Card Creation Request"
  type        = string
  default     = ""
}

variable "usbank_certCardId" {
  description = "The certCardId for US Bank Card Creation Request"
  type        = string
  default     = ""
}

variable "usbank_certCardPasscode" {
  description = "The certCardPasscode for US Bank Card Creation Request"
  type        = string
  default     = ""
}

variable "usbank_fundingCardId" {
  description = "The fundingCardId for US Bank Card Creation Request"
  type        = string
  default     = ""
}

variable "usbank_cardType" {
  description = "The cardType for US Bank Card Creation Request"
  type        = string
  default     = ""
}

variable "pdf_api_host" {
  description = "URL of the PDF API"
  type        = string
  default     = ""
}

variable "generate_1099_max_files" {
  description = "Maximum number of 1099s to generate"
  default     = "1000"
}

variable "upload_max_files_to_fineos" {
  description = "max number of 1099 documents to upload to Fineos API"
  default     = "10"
}

variable "enable_1099_testfile_generation" {
  description = "Enable IRS 1099 test file generation"
  default     = "0"
}

variable "irs_1099_correction_ind" {
  description = "Declares if the 1099 batch should be a correction run"
  default     = "0"
}

variable "enable_offset_get_1099" {
  description = "Declares to process the new logic to upload 1099 documents"
  default     = "0"
}

variable "upload_1099_doc_batch_start" {
  description = "Declares the start of the 1099 records to upload the 1099 document in a batch run"
  default     = "1"
}

variable "upload_1099_doc_batch_end" {
  description = "Declares the end of the 1099 records to upload the 1099 document in a batch run"
  default     = "10"
}

variable "irs_1099_tax_year" {
  description = "Declares the tax year for the 1099 batch"
  default     = "2000"
}

variable "use_overpayment_table_for_over_payments_max_weekly_benefit" {
  description = "Use overpayment table for max weekly benefit validation step"
  default     = "0"
}

variable "fineos_overpayment_extract_max_history_date" {
  description = "Declares the earliest date that an overpayment extract file can be processed. Any file with timestamp before this date will be skipped and not be staged"
  default     = "2028-06-01"
}

variable "enable_fineos_overpayment_processing" {
  description = "Enable the FINEOS overpayment extract processing steps"
  default     = "1"
}

variable "enable_overpayment_adjustment_processing" {
  description = "Enable the overpayment adjustment processing step to refer REMs to MMARS"
  default     = "0"
}

variable "enable_1099_print_shop_copying" {
  description = "This flag configures whether or not the step to copy 1099 documents to the S3 print shop will execute."
  default     = "1"
}

variable "delete_files_from_print_shop_path" {
  description = "This flag determines if the print shop S3 path will be cleared before files are copied."
  default     = "1"
}

variable "pfml_1099_batch_id_of_documents_to_copy" {
  description = "This flag is used to copy 1099 documents to the print shop S3 path. If not provided then the latest batch ID will be used."
  type        = string
  default     = ""
}

variable "enable_pub_undeliverable_checks_file_processing" {
  description = "Enable processing of pub undeliverable checks files"
  default     = "0"
}

variable "pub_payment_starting_check_number" {
  description = "Number to start issuing checks from"
  default     = "0"
}

variable "pfml_1099_generate_document_sequence_start" {
  description = "Starting sequence number when generating 1099 documents."
  default     = null
}

variable "pfml_1099_generate_document_sequence_end" {
  description = "Ending sequence number when generating 1099 documents."
  default     = null
}

variable "use_service_agreement_versions" {
  description = "Feature flag to enable creating FINEOS service agreement using versions."
  default     = "0"
}

variable "enable_service_agreement_version_fineos_write" {
  description = "Feature flag to enable FINEOS service agreement write using versions."
  default     = "1"
}

variable "service_agreement_preselected_employers" {
  description = "Run service agreement load task for preselect employers."
  type        = string
  default     = null
}

variable "employer_service_agreement_version_update_limit" {
  description = "Feature flag to limit employer service agreement update using version."
  type        = number
  default     = 0
}

variable "seconds_between_initial_sa_calls" {
  description = "Seconds to delay between FINEOS calls to create initial service agreements."
  type        = number
  default     = 0
}

variable "overpayments_backfill_extract_max_history_date" {
  description = "The earliest we will backfill for the overpayment extract."
  type        = string
  default     = "2021-01-01"
}

variable "overpayments_backfill_extract_min_history_date" {
  description = "The latest we will backfill for the overpayment extract."
  type        = string
  default     = "2021-01-03"
}

########## Variables for Step Functions ################

variable "st_use_mock_dor_data" {
  description = "Step Function Mock DOR Data"
  default     = false
}

variable "st_decrypt_dor_data" {
  description = "Step Function Decrypted DOR Data"
  default     = false
}

variable "st_file_limit_specified" {
  description = "Step Function Eligibility Feed Export File Number Limit"
  default     = true
}

variable "st_employee_export_limit_specified" {
  description = "Step Function Eligibility Feed Export Maximum Employee Limit"
  default     = false
}

variable "st_employer_update_limit" {
  description = "Employer Upload Update Limit"
  type        = number
}

variable "enforce_execute_sql_read_only" {
  description = "Determines whether write access or read-only access is granted against an API RDS DB. Read-only access is enforced on production and breakfix for the execute-sql task."
  type        = bool
  default     = true
}

variable "release_version" {
  description = "API release version"
  type        = string
  default     = ""
}

variable "enable_image_access_document_upload" {
  description = "If the API supports document uploads from Image Access"
  type        = string
  default     = "false"
}

variable "s3_access_logging_enabled" {
  description = "this enables access logging for buckets that need access logging enabled"
  type        = bool
  default     = false
}

variable "enable_employer_overpayment" {
  description = "Flag to set relevant party to Employer for Overpayment"
  default     = 0
}

variable "bucket_replication_enabled" {
  description = "Enables bucket replication per environment"
  type        = bool
  default     = false
}

variable "enable_payment_audit_report_emails" {
  description = "Enable Payment Audit Report Emails"
  default     = "0"
}

############ Eventbridge Schedule Variables ##############
variable "fineos_data_export_tool_schedule_expression" {
  description = "EventBridge schedule for FINEOS Bucket Tool in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "fineos_report_extract_tool_schedule_expression" {
  description = "EventBridge schedule for FINEOS Extract Report in US/Eastern time"
  type        = string
  default     = null
}

variable "fineos_monthly_extract_scheduler_schedule_expression" {
  description = "EventBridge schedule for Fineos Monthly Extract in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "import_fineos_to_warehouse_schedule_expression" {
  description = "EventBridge schedule for Import Fineos to Warehouse in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "fineos_error_extract_tool_schedule_expression" {
  description = "EventBridge schedule for Fineos Error Extract in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "export_leave_admins_created_schedule_expression" {
  description = "EventBridge schedule for Export Leave Admins Created in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "export_psd_report_schedule_expression" {
  description = "EventBridge schedule for Export PSD Report in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "export_60_day_comms_report_schedule_expression" {
  description = "EventBridge schedule for Export 60 Day Comms Report in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "cps_errors_crawler_schedule_expression" {
  description = "EventBridge schedule for CPS Errors Crawler in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "reductions_dia_send_claimant_lists_schedule_expression" {
  description = "EventBridge schedule for Reductions DIA Send Claimant Lists in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "reductions_dua_send_claimant_lists_schedule_expression" {
  description = "EventBridge schedule for Reductions DUA Send Claimant Lists in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "reductions_process_agency_data_lists_schedule_expression" {
  description = "EventBridge schedule for Reductions Process Agency Data Lists in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "pub_payments_process_fineos_schedule_expression" {
  description = "EventBridge schedule for Pub Payments Process Fineos in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "pub_payments_process_fineos_monday_schedule_expression" {
  description = "EventBridge schedule for Pub Payments Process Fineos on Mondays in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "pub_payments_verify_fineos_extract_schedule_expression" {
  description = "EventBridge schedule for Pub Payments Verify Fineos Extract in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "sunday_pub_payments_process_fineos_schedule_expression" {
  description = "EventBridge schedule for Sunday Pub Payments Process Fineos in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "saturday_pub_payments_process_fineos_schedule_expression" {
  description = "EventBridge schedule for Saturday Pub Payments Process Fineos in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "fineos_import_la_units_schedule_expression" {
  description = "EventBridge schedule for Fineos Import La Units in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "weekend_fineos_import_la_units_schedule_expression" {
  description = "EventBridge schedule for Weekend Fineos Import La Units in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "fineos_snapshot_extract_schedule_expression" {
  description = "EventBridge schedule for Fineos Snapshot Extract in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "appeals_generate_intake_pdfs_schedule_expression" {
  description = "EventBridge schedule for Appeals Generate Intake Pdfs in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "appeals_import_extract_schedule_expression" {
  description = "EventBridge schedule for Appeals Import Extract in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "pub_payments_process_snapshot_schedule_expression" {
  description = "EventBridge schedule for Pub Payments Process Snapshot  in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "pub_payments_copy_audit_report_schedule_expression" {
  description = "EventBridge schedule for Pub Payments Copy Audit Report in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "fineos_import_employee_updates_schedule_expression" {
  description = "EventBridge schedule for Standalone Fineos Import Employee Updates in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "saturday_fineos_import_employee_updates_schedule_expression" {
  description = "EventBridge schedule for Saturday Standalone Fineos Import Employee Updates in US/Eastern time"
  type        = string
  default     = null // "cron(0 2 ? * SAT *)"
}

variable "pub_process_weekly_reports_schedule_expression" {
  description = "EventBridge schedule for Pub Process Weekly Reports in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "child_support_dor_import_schedule_expression" {
  description = "EventBridge schedule for Child Support DOR Import in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "pub_payments_process_1099_documents_schedule_expression" {
  description = "EventBridge schedule for Pub Payments Process 1099 Documents in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "process_prepaid_debit_registration_schedule_expression" {
  description = "EventBridge schedule for Process Prepaid Debit Registration in US/Eastern time"
  type        = string
  default     = null
}

variable "process_overpayment_referrals_schedule_expression" {
  description = "EventBridge schedule for process overpayment referrals in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "process_mmars_response_file_schedule_expression" {
  description = "EventBridge schedule for process overpayment referrals in US/Eastern time"
  type        = string
  default     = null // "cron(30 19 * * ? *)"
}

variable "process_overpayment_collections_schedule_expression" {
  description = "EventBridge schedule for process overpayment collections in US/Eastern time"
  type        = string
  default     = null // Schedule is TBD
}

variable "process_rfi_renotifications_schedule_expression" {
  description = "EventBridge schedule for rfi re-notifications in US/Eastern time"
  type        = string
  default     = null
}

variable "enable_sync_employees_step" {
  description = "Enable SyncEmployeesStep"
  type        = string
  default     = "0"
}

variable "enable_sync_absence_paid_leave_cases_step" {
  description = "Enable SyncAbsencePaidLeaveCasesStep"
  type        = string
  default     = "0"
}

variable "enable_sync_efts_step" {
  description = "Enable SyncEFTsStep"
  type        = string
  default     = "0"
}

variable "enable_sync_claims_step" {
  description = "Enable SyncClaimsStep"
  type        = string
  default     = "0"
}

variable "enable_sync_absence_periods_step" {
  description = "Enable SyncAbsencePeriodsStep"
  type        = string
  default     = "0"
}

########## Variables for Cloudwatch Rule Schedule ################

### original schedule
variable "enable_fineos_import_iaww" {
  description = "Enable scheduling for fineos IAWW processing task"
  default     = false
}

# pub-payments-copy-audit-report
variable "enable_pub_payments_copy_audit_report_schedule" {
  description = "Enable scheduling for 'pub-payments-copy-audit-report' ECS task"
  type        = bool
  default     = false
}

# fineos-import-employee-updates
variable "enable_standalone_fineos_import_employee_updates" {
  description = "Enable scheduling for 'standalone-fineos-import-employee-updates' ECS task"
  type        = bool
  default     = false
}

# fineos-import-employee-updates
variable "enable_saturday_standalone_fineos_import_employee_updates" {
  description = "Enable Saturday scheduling for 'standalone-fineos-import-employee-updates' ECS task"
  type        = bool
  default     = false
}

# child-support-dor-import
variable "enable_child_support_automation" {
  description = "Enable child support automation"
  default     = "0"
}
variable "child_support_fineos_attempt_cutoff_days" {
  description = "The number of days a claimant-obligation (crosswalk match) can keep attempting updates to tasks/eForms in FINEOS before it's marked FAILED"
  type        = string
  default     = "15"
}

# process-1099-documents
variable "enable_1099_generator" {
  description = "Enable scheduling for ammendments and corrections"
  default     = false
}

# pub-payments-verify-fineos-extract
# pub-payments-process-fineos
variable "enable_pub_automation_fineos" {
  description = "Enable scheduling for pub automation fineos task"
  default     = false
}
variable "enable_pub_automation_fineos_monday" {
  description = "Enable different scheduling for pub automation fineos task on Monday"
  default     = false
}

variable "enable_sunday_pub_payments_process_fineos_schedule" {
  description = "Enable Weekend Pub Payments Process Fineos task"
  default     = true
}

variable "enable_saturday_pub_payments_process_fineos_schedule" {
  description = "Enable Weekend Pub Payments Process Fineos task"
  default     = true
}

variable "address_validation_status_change_threshold_count" {
  description = "Minimum number of transactions for enabling address validation status change"
  type        = number
  default     = 50
}

variable "failed_address_error_threshold_pct" {
  description = "Minimum threshold percentage for changing address validation status to error"
  type        = number
  default     = 50
}

variable "failed_address_warning_threshold_pct" {
  description = "Minimum threshold percentage for changing address validation status to warning"
  type        = number
  default     = 20
}

# pub-payments-process-snapshot
variable "enable_pub_payments_process_snapshot_fineos" {
  description = "Enable different scheduling for payments process snapshot"
  default     = true
}

# reductions-process-agency-data
variable "enable_reductions_process_agency_data_schedule" {
  description = "Enable scheduling for 'reductions-process-agency-data' ECS task"
  type        = bool
  default     = false
}

# reductions-dua-send-claimant-lists
# reductions-dia-send-claimant-lists
variable "enable_reductions_send_claimant_lists_to_agencies_schedule" {
  description = "Enable scheduling for 'reductions-send-claimant-lists-to-agencies' ECS task"
  type        = bool
  default     = false
}

# process-prepaid-debit-registration
variable "enable_process_prepaid_debit_registration" {
  description = "Enable scheduling for 'process-prepaid-debit-registration' ECS task"
  type        = bool
  default     = false
}

### new shcedule

variable "enable_fineos_data_export_tool" {
  description = "CloudWatch rule schedule for fineos-data-export-tool ECS task"
  type        = bool
  default     = false
}

variable "enable_fineos_report_extracts_tool" {
  description = "CloudWatch rule schedule for fineos-report-extracts-tool ECS task"
  type        = bool
  default     = false
}

variable "enable_fineos_import_employee_updates" {
  description = "CloudWatch rule schedule enable_fineos_import_employee_updates"
  type        = bool
  default     = false
}

variable "enable_fineos_error_extract_tool" {
  description = "CloudWatch rule schedule for fineos-error-extract-tool ECS task"
  type        = bool
  default     = false
}

variable "enable_fineos_import_la_units" {
  description = "CloudWatch rule schedule enable_fineos_import_la_units"
  type        = bool
  default     = false
}

variable "enable_fineos_monthly_extract_scheduler" {
  description = "CloudWatch rule schedule for fineos-monthly-extract-scheduler ECS task"
  type        = bool
  default     = false
}

variable "enable_import_fineos_to_warehouse" {
  description = "CloudWatch rule schedule for import-fineos-to-warehouse ECS task"
  type        = bool
  default     = false
}

variable "enable_export_leave_admins_created" {
  description = "CloudWatch rule schedule for export-leave-admins-created ECS task"
  type        = bool
  default     = false
}

variable "enable_export_psd_report" {
  description = "CloudWatch rule schedule for export-psd-report ECS task"
  type        = bool
  default     = false
}

variable "enable_export_60_day_comms_report" {
  description = "CloudWatch rule schedule for export-60-day-comms-report ECS task"
  type        = bool
  default     = false
}

variable "enable_cps_errors_crawler" {
  description = "CloudWatch rule schedule for cps_errors_crawler ECS task"
  type        = bool
  default     = false
}

variable "enable_weekend_fineos_import_la_units" {
  description = "CloudWatch rule schedule for weekend-fineos-import-la-units ECS task"
  type        = bool
  default     = false
}

variable "enable_fineos_snapshot_extract_tool" {
  description = "CloudWatch rule schedule for fineos-snapshot-extracts-tool ECS task"
  type        = bool
  default     = false
}

variable "enable_appeals_generate_intake_pdfs" {
  description = "CloudWatch rule schedule for appeals-generate-intake-pdfs ECS task"
  type        = bool
  default     = false
}

variable "enable_appeals_import_extract" {
  description = "CloudWatch rule schedule for appeals-import-extract ECS task"
  type        = bool
  default     = false
}

variable "enable_pub_process_weekly_reports" {
  description = "CloudWatch rule schedule for pub-process-weekly-reports ECS task"
  type        = bool
  default     = false
}

variable "enable_fines_and_repayments_automation" {
  description = "Enable scheduling for dfml fines and repayments ECS task"
  type        = bool
  default     = false
}

variable "dfml_fines_repayments_email_address" {
  description = "Email address for dfml fines and repayments ECS task"
  type        = string
  default     = "<EMAIL>"
}

variable "dfml_db_migration_email_address" {
  description = "Email address for PFML database schema changes."
  type        = string
  default     = "<EMAIL>"
}

variable "dfml_db_migration_cc_email_address" {
  description = "CC email address for PFML database schema changes."
  type        = string
  default     = "<EMAIL>"
}

variable "dfml_fines_repayments_cc_email_address" {
  description = "CC Email address for dfml fines and repayments ECS task"
  type        = string
  default     = "<EMAIL>"
}

variable "enable_dor_import_employer_dor_row_population" {
  description = "Enable Employer DOR row db table population for 'dor-import' ECS task"
  type        = string
  default     = "1"
}

variable "dia_dua_annotations_enabled" {
  description = "Enable DIA and DUA annotation within PaymentAuditReport"
  type        = string
  default     = "0"
}

variable "sync_employee_feed_aggregate_step_enable" {
  description = "Enable employee feed aggregate step"
  type        = string
  default     = "0"
}


variable "fineos_service_agreement_export_file_path" {
  description = "File Path (local or s3) to Fineos service agreements export csv file"
  type        = string
  default     = ""
}

variable "pfml_1099_merge_document_start_batch" {
  description = "Declares the sub batch number from where the merge starts"
  default     = "0"
}

# process-new-relic-dashboard-recovery
variable "enable_process_new_relic_dashboard_recovery" {
  description = "Enable New Relic Dashboard Recovery process"
  default     = "0"
}

variable "process_new_relic_dashboard_recovery_schedule_expression" {
  description = "EventBridge schedule for New Relic Dashboard Recovery US/Eastern time"
  type        = string
  default     = null // "cron(0 9 ? * FRI *)"
}

variable "idp_ssm_env" {
  description = "SSM name fragment: LMG host"
  type        = string
  default     = "non_prod"
}

variable "dor_employee_integrity_email_address" {
  description = "DOR Employee Integrity Report email address"
  type        = string
  default     = "<EMAIL>"
}
variable "enable_leave_admin_mtc_verification" {
  description = "A feature flag is needed to enable leave admins to verify using their MTC number."
  default     = "0"
}

variable "enable_prepaid_impact_payments" {
  description = "Feature flag to protect the existing payments processing to facilitate development around Prepaid debit cards"
  default     = "0"
}

variable "enable_sync_payment_preference" {
  description = "Feature flag to enable sync of payment preference"
  default     = "0"
}

variable "enable_s3_direct_copy" {
  description = "Feature flag to enable direct copy of s3 files"
  default     = "0"
}

variable "mymassgov_api_personal_application_id_uri" {
  description = "MMG API app ID urls for personal scopes"
  type        = string
  default     = "https://TestMassGovB2C.onmicrosoft.com/cc7f9e56-2b0e-4836-8cf5-1f7522620038"
}

variable "mymassgov_api_business_application_id_uri" {
  description = "MMG API app ID urls for business scopes"
  type        = string
  default     = "https://PartnerTestMassGovB2C.onmicrosoft.com/bbce28f8-bd8e-4902-b22b-f324da2531bf"
}

variable "my_mass_gov_api_client_base_url" {
  description = "MMG API base URL"
  type        = string
  default     = "https://api.my.test-next.tss.mass.gov"
}

variable "enable_document_upload_optional" {
  description = "Feature Flag to make document uploads for claimants optional"
  default     = "0"
}

variable "enable_process_overpayment_referrals" {
  description = "Feature flag to enable overpayment processing."
  type        = bool
  default     = false
}

variable "enable_process_mmars_response_file" {
  description = "Feature flag to enable mars response file processing."
  type        = bool
  default     = false
}

variable "enable_process_overpayment_collections" {
  description = "Feature flag to enable overpayment collections processing"
  type        = bool
  default     = false
}
variable "enable_sync_c_i_values" {
  description = "Enable syncing C & I values from fineos employee feed"
  default     = "0"
}
variable "enable_sync_claimant_address_step" {
  description = "Enable syncing claimant address fineos employee feed"
  default     = "0"
}

variable "edm_client_oauth2_url" {
  description = "URL of the EDM Vendor OAUTH2"
  type        = string
  default     = ""
}

variable "edm_client_api_base_url" {
  description = "URL of the EDM Vendor API"
  type        = string
  default     = ""
}

variable "enable_email_overpayment_referrals_inf_file" {
  description = "Enable sending overpaymnmets referrals INF file on email"
  type        = string
  default     = "0"
}


variable "enable_dua_wages_from_dor_import_schedule" {
  description = "Enable scheduling for 'dua-wages-from-dor-import' ECS task"
  type        = bool
  default     = false
}

variable "dua_wages_from_dor_import_schedule_expression" {
  description = "Schedule for 'dua-wages-from-dor-import' ECS task"
  type        = string
  default     = null
}

variable "sync_dua_employees_and_wages_record_process_limit" {
  description = "Maximum number of records to process per 'SyncDuaEmployeesAndWagesStep' run"
  default     = null
}

variable "enable_mock_edm_repayment_response" {
  description = "Flag to enable or disable the creation of mock data from database for EDM repayment API response"
  default     = "0"
}

variable "overpayment_repayment_fetch_start_date" {
  description = "Start date for fetching overpayment repayment when  syncing overpayment collections from the EDM API. If set, it will override the 'overpayment_repayment_fetch_days_prior' configuration."
  type        = string
  default     = "" // Example: "2025-06-01"
}

variable "overpayment_repayment_fetch_end_date" {
  description = "End date for fetching overpayment repayments when syncing overpayment collections from the EDM API. If set, it will override the 'overpayment_repayment_fetch_days_prior' configuration."
  type        = string
  default     = "" // Example: "2025-06-15"
}

variable "overpayment_repayment_fetch_days_prior" {
  description = "The number of days to look back for overpayment repayment when syncing overpayment collections from the EDM API"
  default     = "14"
}

variable "enable_address_extract_schedule" {
  description = "Feature flag to enable the address_extract_schedule"
  type        = bool
  default     = false
}

variable "address_extract_schedule_expression" {
  description = "Schedule for 'address-extract' ECS task"
  type        = string
  default     = "cron(0 0 1 * ? *)" # Run at midnight on the first day of the month
}

variable "enable_to_use_feed_aggregate_table" {
  description = "Feature flag to enable using feed aggregate table"
  type        = string
  default     = "0"
}

variable "enable_sync_address_using_aggregate_table" {
  description = "Feature flag to enable sync address using feed aggregate table"
  type        = string
  default     = "0"
}

variable "enable_sync_payment_preferences_using_aggregate_table" {
  description = "Feature flag to enable sync payment preferences using the aggregate table"
  type        = string
  default     = "0"
}

variable "enable_payment_line_matching" {
  description = "Feature flag to enable payment line matching in the related payments step"
  type        = string
  default     = "0"
}

variable "mmars_overpayment_email_address" {
  description = "DFML MMARS Overpayment Email Address"
  type        = string
  default     = ""
}

variable "mmars_overpayment_bcc_email_address" {
  description = "DFML MMARS Overpayment BCC Email Address"
  type        = string
  default     = ""
}

variable "enable_sync_eft_using_aggregate_table" {
  description = "Feature flag to enable sync EFT using feed aggregate table"
  type        = string
  default     = "0"
}

variable "enable_overpayment_vcmt_automation" {
  description = "Feature flag to enable VCMT automation"
  type        = string
  default     = "0"
}

variable "enable_delayed_submission_of_modifications" {
  description = "Feature flag for enabling and disabling the delayed processing of modifications backend pipeline component (BPC) of Med to Bonding"
  type        = string
  default     = "0"
}

variable "enable_payment_reject_issue_resolutions_milestone1" {
  description = "Feature flag for enabling payment reject issue resolutions for DUA Additional Income, DIA Additional Income, Invalid Payment Date, Cancel Time Submitted and Payment Audit Error"
  type        = string
  default     = "0"
}

variable "enable_payment_reject_issue_resolutions_milestone2" {
  description = "Feature flag for enabling payment reject issue resolutions not included in milestone1"
  type        = string
  default     = "0"
}

variable "enable_notify_edm_team_of_pfml_schema_changes" {
  description = "Feature flag to enable notifying the EDM team of PFML schema changes"
  default     = "0"
}

variable "enabled_new_fineos_eligibility_feed_export" {
  description = "Enable new fineos eligibility feed export"
  default     = "0"
}

variable "update_customer_names_max_concurrent_thread_count" {
  description = "The maximum number of concurrent threads used to update customer names with FINEOS"
  default     = "5"
}


variable "enable_leave_admin_review_reminder_notifications" {
  description = "Enable the leave admin review reminder notifications schedule"
  type        = bool
  default     = false
}

variable "leave_admin_review_reminder_notifications_schedule_expression" {
  description = "Cron expression for leave admin review reminder notifications schedule"
  type        = string
  default     = null
}
