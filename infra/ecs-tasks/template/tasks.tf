# Templating and configurations for ECS tasks.
#
# If you need to add a new ECS task, add it to the locals.tasks variable
# in the following format:
#
# "${local.app_name}-your-task-name" = {
#   command = ["the-command-name"],
#   env = [
#     local.db_access,
#     ...
#   ]
# }
#
# The command name comes from pyproject.toml.
#
# Application AWS Permissions
# ===========================
#
# The task_role should be defined, but can be null if your running task does not need
# to be assigned an IAM role for additional permissions to AWS resources.
# If not null, it should have a task_role_arn stored in it for the aws_ecs_task_definition.
#
# Configuring Environment Variables and Secrets
# =============================================
#
# If your task requires unique SSM / secretsmanager permissions, please define and specify an
# execution_role for your task.
#
# Use the "env" key to specify the environment variables needed by your ECS task. Shared groups of variables
# are stored as locals in task_config.tf and can be used:
#
# env = [ local.db_access ]
#
# If you need unique variables, you can specify them inline:
#
# env = [
#   local.db_access,
#   { name: "COOL_ENDPOINT_URL", value: "something" }
# ]
#
# You can also specify secrets that are pulled from parameter store using the "valueFrom" key:
#
# env = [
#   local.db_access,
#   { name: "COOL_ENDPOINT_URL", value: "something" },
#   { name: "COOL_ENDPOINT_SECRET_KEY", valueFrom: "/service/pfml-api/test/cool_endpoint_secret_key" }
# ]
#
# Note that AWS ECS provides protections against duplicate or invalid keys. This won't be caught at the PR stage,
# but a terraform apply will indicate any AWS errors, e.g.:
#
# Error: ClientException: Duplicate secret names found: DB_NESSUS_PASSWORD. Each secret name must be unique.
#
# If your environment variable must be configurable for each environment, add a variable in `variables.tf` and
# reference it using `var.my_variable_name`. This value can be passed in by each environment's main.tf file:
#
# module "tasks" {
#   ...
#   my_variable_name = "something"
# }
#
# Resource Limits
# ===============
#
# CPU and memory defaults are 1024 (CPU units) and 2048 (MB).

# If you need more resources than this, add "cpu" or "memory" keys to your ECS task's
# entry in locals.tasks. The defaults will be used if these keys are absent.
#
# readonlyRootFilesystem defaults to true.
# If you need to write to the filesystem, set readonlyRootFilesystem to false.
#
# Testing ECS Tasks
# =================
#
# Once you're ready, apply your terraform changes and test your new ECS task in the test environment.
#
# From the root of the git repository (example):
#
# ./bin/run-ecs-task/run-task.sh test db-migrate-up kevin.yeh
#
# CPU and RAM Utilization Alerting
# ================================
#
# Remember to add your task and it's CPU & RAM allocation to infra/monitoring/alarms_api_portal/alarms-aws.tf
# so alerts will trigger if the task is running hot.

locals {
  tasks = {
    "db-migrate-up" = {
      command                = ["db-migrate-up"],
      task_role              = aws_iam_role.db_user_pfml_migrate.arn
      cpu                    = 2048
      memory                 = 4096
      readonlyRootFilesystem = false
      env = [
        local.db_migrate_access,
        local.emails_db_migration,
        { name : "PFML_SCHEMA_CHANGES_DIRECTORY", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/edm/pfml-schema-changes" },
        { name : "ENABLE_NOTIFY_EDM_TEAM_OF_PFML_SCHEMA_CHANGES", value : var.enable_notify_edm_team_of_pfml_schema_changes }
      ]
    },

    "db-migrate-down" = {
      command                = ["db-migrate-down"]
      task_role              = aws_iam_role.db_user_pfml_migrate.arn
      readonlyRootFilesystem = false
      env = [
        local.db_migrate_access,
        local.emails_db_migration,
        { name : "PFML_SCHEMA_CHANGES_DIRECTORY", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/edm/pfml-schema-changes" },
        { name : "ENABLE_NOTIFY_EDM_TEAM_OF_PFML_SCHEMA_CHANGES", value : var.enable_notify_edm_team_of_pfml_schema_changes }
      ]
    }

    "db-admin-create-db-users" = {
      command   = ["db-admin-create-db-users"]
      task_role = aws_iam_role.db_user_pfml_admin.arn
      cpu       = 2048
      memory    = 4096
      env = [
        local.db_admin_access,
        { name : "DB_NESSUS_PASSWORD", valueFrom : "/service/${local.app_name}/${var.environment_name}/db-nessus-password" }
      ]
    },

    "db-create-fineos-user" = {
      command   = ["db-create-fineos-user"],
      task_role = aws_iam_role.db_user_pfml_admin.arn
      env = [
        local.db_admin_access,
        { name : "FINEOS_APP_SUB_ID", valueFrom : "/service/${local.app_name}/${var.idp_ssm_env}/service_account/fineos/sub_id" },
        { name : "INTERNAL_FINEOS_ROLE_APP_SUB_ID", valueFrom : "/service/${local.app_name}/${var.idp_ssm_env}/service_account/fineos_internal/sub_id" }
      ]
    },

    "db-create-servicenow-user" = {
      command   = ["db-create-servicenow-user"],
      task_role = aws_iam_role.db_user_pfml_admin.arn
      env = [
        local.db_admin_access,
        { name : "SERVICENOW_APP_SUB_ID", valueFrom : "/service/${local.app_name}/${var.idp_ssm_env}/service_account/servicenow/sub_id" },
        { name : "INTERNAL_SERVICENOW_ROLE_APP_SUB_ID", valueFrom : "/service/${local.app_name}/${var.idp_ssm_env}/service_account/servicenow_internal/sub_id" }
      ]
    },

    "db-create-imageaccess-idp-user" = {
      command   = ["db-create-imageaccess-idp-user"],
      task_role = aws_iam_role.db_user_pfml_admin.arn
      env = [
        local.db_admin_access,
        { name : "IMAGEACCESS_IDP_APP_SUB_ID", valueFrom : "/service/${local.app_name}/${var.idp_ssm_env}/service_account/imageaccess_idp/sub_id" },
        { name : "INTERNAL_IMAGEACCESS_IDP_ROLE_APP_SUB_ID", valueFrom : "/service/${local.app_name}/${var.idp_ssm_env}/service_account/imageaccess_idp_internal/sub_id" }

      ]
    },

    "db-create-idp-user" = {
      command   = ["db-create-idp-user"],
      task_role = aws_iam_role.db_user_pfml_admin.arn
      env = [
        local.db_admin_access,
        { name : "IDP_APP_SUB_ID", valueFrom : "/service/${local.app_name}/${var.idp_ssm_env}/service_account/idp/sub_id" },
        { name : "INTERNAL_IDP_ROLE_APP_SUB_ID", valueFrom : "/service/${local.app_name}/${var.idp_ssm_env}/service_account/idp_internal/sub_id" }

      ]
    },

    "execute-sql" = {
      command   = ["execute-sql"]
      task_role = aws_iam_role.task_execute_sql_task_role_batch_user.arn,
      env = [
        local.db_access,
        { name : "S3_EXPORT_BUCKET", value : "massgov-pfml-${var.environment_name}-execute-sql-export" }
      ]
    },

    "dor-import" = {
      command                = ["dor-import"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 4096,
      memory                 = 30720,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.pub_s3_folders,
        local.emails_employee_integrity,
        { name : "DECRYPT", value : "true" },
        { name : "FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/received" },
        { name : "PROCESSED_FOLDER", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/processed" },
        { name : "GPG_DECRYPTION_KEY", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key" },
        { name : "GPG_DECRYPTION_KEY_PASSPHRASE", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key_passphrase" },
        { name : "EMPLOYER_DOR_ROW_POPULATION", value : var.enable_dor_import_employer_dor_row_population },
        { name : "DOR_EMPLOYEE_INTEGRITY_FILE_PATH_PREFIX", value : "s3://massgov-pfml-${var.environment_name}-reports" }
      ]
    },

    "dor_create_pending_filing_submission" = {
      command   = ["dor_create_pending_filing_submission"],
      task_role = aws_iam_role.dor_import_task_role.arn,
      cpu       = 4096,
      memory    = 18432,
      env = [
        local.db_access,
        { name : "INPUT_FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dfml" },
        { name : "OUTPUT_FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor" }
      ]
    },
    "employer-exemptions-export-for-dor" = {
      command   = ["employer-exemptions-export-for-dor"],
      task_role = aws_iam_role.dor_import_task_role.arn,
      cpu       = 4096,
      memory    = 18432,
      env = [
        local.db_access,
        { name : "DOR_OUTPUT_FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor" }
      ]
    },
    "dor-pending-filing-response-import" = {
      command                = ["dor-pending-filing-response-import"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 4096,
      memory                 = 18432,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        { name : "DECRYPT", value : "true" },
        { name : "FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/received" },
        { name : "CSV_FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dfml/received/" },
        { name : "GPG_DECRYPTION_KEY", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key" },
        { name : "GPG_DECRYPTION_KEY_PASSPHRASE", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key_passphrase" }
      ]
    },

    "dor-import-exempt" = {
      command                = ["dor-import-exempt"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 8192,
      memory                 = 61440,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        { name : "DECRYPT", value : "true" },
        { name : "FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/received" },
        { name : "GPG_DECRYPTION_KEY", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key" },
        { name : "GPG_DECRYPTION_KEY_PASSPHRASE", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key_passphrase" }
      ]
    },

    "dor-import-exempt-repush" = {
      command                = ["dor-import-exempt-repush"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 4096,
      memory                 = 18432,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        { name : "DECRYPT", value : "true" },
        { name : "FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/received" },
        { name : "RETAIN_RECEIVED_FILES", value : "true" },
        { name : "PROCESSED_PAIRS_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/processed/import-exempt-repush/" },
        { name : "GPG_DECRYPTION_KEY", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key" },
        { name : "GPG_DECRYPTION_KEY_PASSPHRASE", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key_passphrase" }
      ]
    },

    "fineos-import-employee-updates" = {
      command                = ["fineos-import-employee-updates"]
      task_role              = aws_iam_role.fineos_updates_task_role.arn
      cpu                    = 2048
      memory                 = 9216
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders
      ]
    },

    "fineos-import-la-units" = {
      command   = ["fineos-import-la-units"]
      task_role = aws_iam_role.fineos_updates_task_role.arn
      cpu       = 2048
      memory    = 9216
      env = [
        local.db_access,
        local.fineos_api_access,
        local.fineos_s3_access
      ]
    },

    "fineos-import-service-agreements" = {
      command                = ["fineos-import-service-agreements"],
      task_role              = aws_iam_role.fineos_bucket_tool_role.arn,
      cpu                    = 8192,
      memory                 = 61440,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders,
        { name : "FINEOS_SERVICE_AGREEMENT_EXTRACT_MAX_HISTORY_DATE", value : "2024-04-01" },
      ]
    },

    "leave-admin-review-reminder-notifications" = {
      command   = ["leave-admin-review-reminder-notifications"]
      task_role = aws_iam_role.db_user_pfml_batch.arn
      cpu       = 2048
      memory    = 9216
      env = [
        local.db_access,
        local.service_now_access
      ]
    },

    "register-leave-admins-with-fineos" = {
      command   = ["register-leave-admins-with-fineos"]
      task_role = aws_iam_role.register_admins_task_role.arn,
      cpu       = 4096,
      memory    = 18432,
      env = [
        local.db_access,
        local.fineos_api_access
      ]
    },

    "leave-admin-csv-export" = {
      command = ["leave-admin-csv-export"]
      cpu     = 2048
      memory  = 9216
      env = [
        local.db_access,
        local.service_now_access,
        local.pub_s3_folders
      ]
    }

    "load-employers-to-fineos" = {
      command = ["load-employers-to-fineos"]
      env = [
        local.db_access,
        local.fineos_api_access,
        { name : "USE_SERVICE_AGREEMENT_VERSIONS", value : var.use_service_agreement_versions },
        { name : "ENABLE_SERVICE_AGREEMENT_VERSION_FINEOS_WRITE", value : var.enable_service_agreement_version_fineos_write },
        { name : "SERVICE_AGREEMENT_LOAD", value : "1" },
      ]
    },

    "load-service-agreements-to-fineos" = {
      command = ["load-service-agreements-to-fineos"]
      cpu     = 2048
      memory  = 16384
      env = [
        local.db_access,
        local.fineos_api_access,
        { name : "USE_SERVICE_AGREEMENT_VERSIONS", value : var.use_service_agreement_versions },
        { name : "ENABLE_SERVICE_AGREEMENT_VERSION_FINEOS_WRITE", value : var.enable_service_agreement_version_fineos_write },
        { name : "SERVICE_AGREEMENT_PRESELECTED_EMPLOYERS", value : var.service_agreement_preselected_employers },
        { name : "SERVICE_AGREEMENT_LOAD", value : var.service_agreement_load },
      ]
    }

    "reductions-process-agency-data" = {
      command                = ["reductions-process-agency-data"]
      task_role              = aws_iam_role.reductions_workflow_task_role.arn,
      execution_role         = aws_iam_role.reductions_workflow_execution_role.arn
      cpu                    = 4096
      memory                 = 8192
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.eolwd_moveit_access,
        local.reductions_folders,
        local.emails_reductions,
        local.pub_s3_folders
      ]
    },

    "reductions-send-claimant-lists" = {
      command                = ["reductions-send-claimant-lists-to-agencies"]
      task_role              = aws_iam_role.reductions_workflow_task_role.arn,
      execution_role         = aws_iam_role.reductions_workflow_execution_role.arn
      cpu                    = 4096
      memory                 = 8192
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.eolwd_moveit_access,
        local.reductions_folders
      ]
    },

    "pub-payments-create-pub-files" = {
      command                = ["pub-payments-create-pub-files"]
      task_role              = aws_iam_role.pub_payments_create_pub_files_task_role.arn
      cpu                    = 2048
      memory                 = 16384
      readonlyRootFilesystem = false
      env = [
        local.db_access,
        local.fineos_api_access,
        local.fineos_s3_access,
        local.pub_s3_folders,
        local.emails,
        { name : "PUB_PAYMENT_STARTING_CHECK_NUMBER", value : var.pub_payment_starting_check_number },
        { name : "DFML_PUB_ROUTING_NUMBER", valueFrom : "/service/${local.app_name}/${var.environment_name}/dfml_pub_routing_number" },
        { name : "DFML_PUB_ACCOUNT_NUMBER", valueFrom : "/service/${local.app_name}/${var.environment_name}/dfml_pub_account_number" },
        { name : "PAYMENT_AUDIT_REPORT_UPLOAD_NOTIFICATION_EMAIL_ADDRESS", value : var.payment_audit_report_upload_notification_email_address },
        { name : "PAYMENT_AUDIT_REPORT_UPLOAD_NOTIFICATION_CC_EMAIL_ADDRESS", value : var.payment_audit_report_upload_notification_cc_email_address },
        { name : "ENABLE_PAYMENT_AUDIT_REPORT_EMAILS", value : var.enable_payment_audit_report_emails },
        { name : "ENABLE_PREPAID_IMPACT_PAYMENTS", value : var.enable_prepaid_impact_payments },
        { name : "ENABLE_PAYMENT_REJECT_ISSUE_RESOLUTIONS_MILESTONE1", value : var.enable_payment_reject_issue_resolutions_milestone1 }
      ]
    },

    "pub-payments-process-pub-returns" = {
      command                = ["pub-payments-process-pub-returns"]
      task_role              = aws_iam_role.pub_payments_process_pub_returns_task_role.arn
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders,
        local.fineos_api_access,
        { name : "PUB_PAYMENT_STARTING_CHECK_NUMBER", value : var.pub_payment_starting_check_number },
        { name : "PUB_UNDELIVERABLE_CHECKS_FILE_PROCESSING_IS_ENABLED", value : var.enable_pub_undeliverable_checks_file_processing }
      ]
    },

    "fineos-eligibility-feed-export" = {
      command                = ["fineos-eligibility-feed-export"]
      task_role              = aws_iam_role.fineos_eligibility_feed_export_task_role.arn
      cpu                    = 4096
      memory                 = 8192
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_api_access,
        local.fineos_s3_access,
        local.archive_s3_folders,
        { "name" : "OUTPUT_DIRECTORY_PATH", "value" : var.fineos_eligibility_feed_output_directory_path },
        { "name" : "ARCHIVE_DIRECTORY_PATH", "value" : var.pfml_fineos_eligibility_feed_archive_directory_path },
        { "name" : "TEMP_DIRECTORY_PATH", "value" : var.pfml_fineos_eligibility_feed_temp_directory_path },
        { "name" : "ENABLED_NEW_FINEOS_ELIGIBILITY_FEED_EXPORT", "value" : var.enabled_new_fineos_eligibility_feed_export },
        { "name" : "UPDATE_CUSTOMER_NAMES_MAX_CONCURRENT_THREAD_COUNT", "value" : var.update_customer_names_max_concurrent_thread_count }
      ]
    },

    "pub-payments-process-fineos" = {
      command                = ["pub-payments-process-fineos"]
      task_role              = aws_iam_role.pub_payments_process_fineos_task_role.arn
      cpu                    = 16384
      memory                 = 73728
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders,
        local.fineos_api_access,
        local.service_now_access,
        local.emails,
        { name : "FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE", value : "2021-06-12" },
        { name : "FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE", value : "2021-06-12" },
        { name : "FINEOS_VBI_TASKREPORT_SOM_EXTRACT_MAX_HISTORY_DATE", value : "2022-03-11" },
        { name : "FINEOS_VBI_TASKREPORT_DELTA_SOM_EXTRACT_MAX_HISTORY_DATE", value : "2024-07-16" },
        { name : "USE_EXPERIAN_SOAP_CLIENT", value : "1" },
        { name : "EXPERIAN_AUTH_TOKEN", valueFrom : "/service/${local.app_name}/common/experian-auth-token" },
        { name : "FINEOS_VBI_ENTITLEMTPERIOD_SOM_EXTRACT_MAX_HISTORY_DATE", value : "2022-10-28" },
        { name : "USE_OVER_PAYMENT_TABLE_FOR_OVER_PAYMENTS_MAX_WEEKLY_BENEFIT", value : var.use_overpayment_table_for_over_payments_max_weekly_benefit },
        { name : "ENABLE_PAYMENT_AUDIT_REPORT_EMAILS", value : var.enable_payment_audit_report_emails },
        { name : "PAYMENT_AUDIT_REPORT_AVAILABLE_NOTIFICATION_EMAIL_ADDRESS", value : var.payment_audit_report_available_notification_email_address },
        { name : "PAYMENT_AUDIT_REPORT_AVAILABLE_NOTIFICATION_CC_EMAIL_ADDRESS", value : var.payment_audit_report_available_notification_cc_email_address },
        { name : "ENABLE_SYNC_EMPLOYEES_STEP", value : var.enable_sync_employees_step },
        { name : "ENABLE_SYNC_C_I_VALUES", value : var.enable_sync_c_i_values },
        { name : "ENABLE_SYNC_EFTS_STEP", value : var.enable_sync_efts_step },
        { name : "ENABLE_SYNC_CLAIMS_STEP", value : var.enable_sync_claims_step },
        { name : "ENABLE_SYNC_LEAVE_REQUESTS_STEP", value : var.enable_sync_leave_requests_step },
        { name : "ENABLE_SYNC_ABSENCE_PERIODS_STEP", value : var.enable_sync_absence_periods_step },
        { name : "ENABLE_SYNC_PAYMENT_PREFERENCE", value : var.enable_sync_payment_preference },
        { name : "ENABLE_S3_DIRECT_COPY", value : var.enable_s3_direct_copy },
        { name : "ENABLE_PREPAID_IMPACT_PAYMENTS", value : var.enable_prepaid_impact_payments },
        { name : "DIA_DUA_ANNOTATIONS_ENABLED", value : var.dia_dua_annotations_enabled },
        { name : "SYNC_EMPLOYEE_FEED_AGGREGATE_STEP_ENABLE", value : var.sync_employee_feed_aggregate_step_enable },
        { name : "ENABLE_TO_USE_FEED_AGGREGATE_TABLE", value : var.enable_to_use_feed_aggregate_table },
        { name : "ENABLE_SYNC_ADDRESS_USING_AGGREGATE_TABLE", value : var.enable_sync_address_using_aggregate_table },
        { name : "ENABLE_SYNC_EFT_USING_AGGREGATE_TABLE", value : var.enable_sync_eft_using_aggregate_table },
        { name : "ENABLE_SYNC_PAYMENT_PREFERENCES_USING_AGGREGATE_TABLE", value : var.enable_sync_payment_preferences_using_aggregate_table },
        { name : "ENABLE_PAYMENT_LINE_MATCHING", value : var.enable_payment_line_matching },
        # @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review
        { name : "ENABLE_MARK_APPLICATIONS_READY_FOR_REVIEW", value : var.enable_mark_applications_ready_for_review },
        { name : "ENABLE_RE_NOTIFICATION", value : var.enable_re_notification },
        { name : "ENABLE_PAYMENT_REJECT_ISSUE_RESOLUTIONS_MILESTONE1", value : var.enable_payment_reject_issue_resolutions_milestone1 }
      ]
    },

    "pub-overpayments-backfill-data" = {
      command                = ["pub-overpayments-backfill-data"]
      task_role              = aws_iam_role.overpayments_backfill_data_task_role.arn
      cpu                    = 2048
      memory                 = 16384
      readonlyRootFilesystem = false
      env = [
        local.db_access,
        local.pub_s3_folders,
        local.bi_tool_s3_access,
        { name : "OVERPAYMENTS_BACKFILL_EXTRACT_MAX_HISTORY_DATE", value : var.overpayments_backfill_extract_max_history_date },
        { name : "OVERPAYMENTS_BACKFILL_EXTRACT_MIN_HISTORY_DATE", value : var.overpayments_backfill_extract_min_history_date },
      ]
    },

    "pub-payments-verify-fineos-extract" = {
      command   = ["pub-payments-verify-fineos-extract"],
      task_role = aws_iam_role.pub_payments_process_fineos_task_role.arn,
      cpu       = 2048
      memory    = 16384
      env = [
        local.db_access,
        local.pub_s3_folders,
        { name : "LOOKBACK_PERIOD_IN_HRS", value : "12" },
      ]
    },

    "pub-payments-process-snapshot" = {
      command                = ["pub-payments-process-snapshot"]
      task_role              = aws_iam_role.pub_payments_process_fineos_task_role.arn
      cpu                    = 4096
      memory                 = 30720
      readonlyRootFilesystem = false
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders,
        { name : "FINEOS_PAYMENT_RECONCILIATION_EXTRACT_MAX_HISTORY_DATE", value : "2021-10-26" }
      ]
    },

    "process-prepaid-debit-registration" = {
      command                = ["process-prepaid-debit-registration"]
      task_role              = aws_iam_role.process_prepaid_debit_registration_task_role.arn
      cpu                    = 2048
      memory                 = 16384
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders,
        local.usbank_api_access,
        local.fineos_api_access
      ]
    }

    "fineos-import-iaww" = {
      command                = ["fineos-import-iaww"]
      task_role              = aws_iam_role.fineos_import_iaww_task_role.arn
      cpu                    = 2048
      memory                 = 12288
      readonlyRootFilesystem = false
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders,

        { name : "FINEOS_IAWW_EXTRACT_MAX_HISTORY_DATE", value : "2021-11-15" },
        { name : "ENABLE_SYNC_ABSENCE_PAID_LEAVE_CASES_STEP", value : var.enable_sync_absence_paid_leave_cases_step },
        { name : "DB_STATEMENT_TIMEOUT", value : "5400000" }
      ]
    },

    "sync-fineos-extracts-to-pfml-models" = {
      command                = ["sync-fineos-extracts-to-pfml-models"]
      task_role              = aws_iam_role.fineos_import_iaww_task_role.arn
      cpu                    = 2048
      memory                 = 8192
      readonlyRootFilesystem = false
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders,
        local.service_now_access,
        { name : "FINEOS_VBI_DOCUMENT_SOM_EXTRACT_MAX_HISTORY_DATE", value : "2022-03-11" },
        { name : "FINEOS_VBI_DOCUMENT_DELTA_SOM_EXTRACT_MAX_HISTORY_DATE", value : "2024-12-01" },
        { name : "FINEOS_1099_DATA_EXTRACT_MAX_HISTORY_DATE", value : "2022-01-01" },
        { name : "FINEOS_OVERPAYMENT_EXTRACT_MAX_HISTORY_DATE", value : var.fineos_overpayment_extract_max_history_date },
        { name : "ENABLE_FINEOS_OVERPAYMENT_PROCESSING", value : var.enable_fineos_overpayment_processing },
        { name : "ENABLE_OVERPAYMENT_ADJUSTMENT_PROCESSING", value : var.enable_overpayment_adjustment_processing },
        { name : "ENABLE_EMPLOYER_OVERPAYMENT", value : var.enable_employer_overpayment },
        { name : "ENABLE_SYNC_CLAIMANT_ADDRESS_STEP", value : var.enable_sync_claimant_address_step },
      ]
    },

    "pub-process-weekly-reports" = {
      command                = ["pub-process-weekly-reports"]
      task_role              = aws_iam_role.pub_process_weekly_reports_task_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false
      env = [
        local.db_access,
        local.pub_s3_folders,
        local.fineos_s3_access,
      ]
    },

    "child-support-dor-import" = {
      command                = ["child-support-dor-import"]
      task_role              = aws_iam_role.child_support_dor_import_task_role.arn
      execution_role         = aws_iam_role.dor_import_execution_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_api_access,
        local.pub_s3_folders,
        { name : "DECRYPT", value : "true" },
        { name : "FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/received" },
        { name : "PROCESSED_FOLDER", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/processed" },
        { name : "CHILD_SUPPORT_FINEOS_ATTEMPT_CUTOFF_DAYS", value : "${var.child_support_fineos_attempt_cutoff_days}" },
        { name : "GPG_DECRYPTION_KEY", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key" },
        { name : "GPG_DECRYPTION_KEY_PASSPHRASE", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key_passphrase" }
      ]
    },

    "fineos-bucket-tool" = {
      command                = ["fineos-bucket-tool"]
      task_role              = aws_iam_role.fineos_bucket_tool_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false
      env = [
        local.fineos_s3_access,
        local.db_access,
      ]
    },

    "sftp-tool" = {
      command        = ["sftp-tool"]
      task_role      = aws_iam_role.sftp_tool_role.arn
      execution_role = aws_iam_role.reductions_workflow_execution_role.arn
      env = [
        local.base_sftp_access
      ]
    },

    "cps-errors-crawler" = {
      command             = ["cps-errors-crawler"]
      containers_template = "default_template.json"
      task_role           = aws_iam_role.cps_errors_crawler_task_role.arn
      cpu                 = 2048,
      memory              = 4096,
      env = [
        local.db_access,
        { name : "CPS_ERROR_REPORTS_RECEIVED_S3_PATH", value : "s3://${data.aws_s3_bucket.agency_transfer.id}/cps-errors/received/" },
        { name : "CPS_ERROR_REPORTS_PROCESSED_S3_PATH", value : "s3://${data.aws_s3_bucket.agency_transfer.id}/cps-errors/processed/" },
      ]

    },

    "import-fineos-to-warehouse" = {
      command                = ["import-fineos-to-warehouse"]
      task_role              = aws_iam_role.fineos_bucket_tool_role.arn
      readonlyRootFilesystem = false
      cpu                    = 2048,
      memory                 = 4096,
      env = [
        local.fineos_s3_access,
        local.db_access,
        { name : "BI_WAREHOUSE_PATH", value : "s3://massgov-pfml-${var.environment_name}-business-intelligence-tool/warehouse/raw/fineos/" }
      ]
    },

    "update-gender-data-from-rmv" = {
      command   = ["update-gender-data-from-rmv"]
      task_role = aws_iam_role.update_gender_data_from_rmv_task_role.arn
      env = [
        local.db_access,
        local.rmv_api_access
      ]
    },

    "dua-generate-and-send-employee-request-file" = {
      command                = ["dua-generate-and-send-employee-request-file"]
      task_role              = aws_iam_role.dor_import_task_role.arn
      execution_role         = aws_iam_role.reductions_workflow_execution_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.eolwd_moveit_access,
        local.reductions_folders
      ]
    },

    "dua-generate-and-send-employer-request-file" = {
      command                = ["dua-generate-and-send-employer-request-file"]
      task_role              = aws_iam_role.dor_import_task_role.arn
      execution_role         = aws_iam_role.reductions_workflow_execution_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.eolwd_moveit_access,
        local.reductions_folders
      ]
    },

    "dua-import-employee-demographics" = {
      command                = ["dua-import-employee-demographics"]
      task_role              = aws_iam_role.dor_import_task_role.arn
      execution_role         = aws_iam_role.reductions_workflow_execution_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.eolwd_moveit_access,
        local.reductions_folders
      ]
    },

    "dua-import-employer" = {
      command                = ["dua-import-employer"]
      task_role              = aws_iam_role.dor_import_task_role.arn
      execution_role         = aws_iam_role.reductions_workflow_execution_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.eolwd_moveit_access,
        local.reductions_folders
      ]
    },

    "dua-import-employer-unit" = {
      command                = ["dua-import-employer-unit"]
      task_role              = aws_iam_role.dor_import_task_role.arn
      execution_role         = aws_iam_role.reductions_workflow_execution_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.eolwd_moveit_access,
        local.reductions_folders
      ]
    },

    "dua-wages-from-dor-import" = {
      command                = ["dua-wages-from-dor-import"]
      task_role              = aws_iam_role.dor_import_task_role.arn
      execution_role         = aws_iam_role.dor_import_execution_role.arn
      cpu                    = 2048,
      memory                 = 8192,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        { name : "RECEIVED_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/received" },
        { name : "PROCESSED_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/processed" },
        { name : "ERROR_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/dor/error" },
        { name : "GPG_DECRYPTION_KEY", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key" },
        { name : "GPG_DECRYPTION_KEY_PASSPHRASE", valueFrom : "/service/${local.app_name}-dor-import/${var.environment_name}/gpg_decryption_key_passphrase" },
        { name : "SYNC_EMPLOYEES_AND_WAGES_RECORD_PROCESS_LIMIT", value : var.sync_dua_employees_and_wages_record_process_limit },

        # PFMLPB-19864: only process a few files during first run(s)
        { name : "DuaWagesExtractStep_MAX_FILES", value : "5" },
        { name : "DuaWagesExtractStep_MAX_FILES_BEHAVIOR", value : "warn_and_halt" },
      ]
    },

    "report-sequential-employment" = {
      command   = ["report-sequential-employment"]
      task_role = aws_iam_role.task_execute_sql_task_role_batch_user.arn
      env = [
        local.db_access,
        { name : "S3_BUCKET", value : "s3://massgov-pfml-${var.environment_name}-execute-sql-export" }
      ]
    },

    "pub-payments-copy-audit-report" = {
      command                = ["pub-payments-copy-audit-report"]
      task_role              = aws_iam_role.pub_payments_copy_audit_report_task_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.pub_s3_folders,
      ]
    },

    "backfill-benefit-years" = {
      command = ["backfill-benefit-years"]
      env = [
        local.db_access
      ]
    },

    "backfill-benefit-year-base-periods" = {
      command = ["backfill-benefit-year-base-periods"]
      env = [
        local.db_access
      ]
    },

    "backfill-fineos-vbi-documents" = {
      command = ["backfill-fineos-vbi-documents"]
      env = [
        local.db_access
      ]
    },

    "backfill-ready-for-review-time-column" = {
      command = ["backfill-ready-for-review-time-column"]
      env = [
        local.db_access
      ]
    },

    "backfill-wages-and-contributions-datasource" = {
      command = ["backfill-wages-and-contributions-datasource"]
      env = [
        local.db_access
      ]
    },

    "appeals-generate-intake-pdfs" = {
      command = ["appeals-generate-intake-pdfs"],
      cpu     = 2048,
      memory  = 4096,
      env = [
        local.db_access,
        local.fineos_api_access,
        local.pdf_api_access,
      ]
    },

    "appeals-import-extract" = {
      command   = ["appeals-import-extract"],
      task_role = aws_iam_role.appeals_import_extract_task_role.arn
      cpu       = 2048,
      memory    = 4096,
      env = [
        local.db_access,
        local.fineos_s3_access,
      ]
    }

    "add-merger-acquisition" = {
      command = ["add-merger-acquisition"]
      env = [
        local.db_access,
      ]
    }

    "mock-fineos-writeback-status-for-payments" = {
      command   = ["mock-fineos-writeback-status-for-payments"],
      task_role = aws_iam_role.mock_fineos_writeback_status_for_payments_role.arn,
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders
      ]
    }

    "analyze-fineos-entitlement-periods" = {
      command                = ["analyze-fineos-entitlement-periods"],
      task_role              = aws_iam_role.fineos_updates_task_role.arn,
      cpu                    = 2048,
      memory                 = 16384,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_s3_access,
        local.pub_s3_folders
      ]
    }

    "update-uuid" = {
      command   = ["update-uuid"],
      task_role = aws_iam_role.db_user_pfml_batch.arn,
      env = [
        local.db_access
      ]
    }

    "update-employer" = {
      command   = ["update-employer"],
      task_role = aws_iam_role.db_user_pfml_batch.arn,
      env = [
        local.db_access
      ]
    }

    "add-employer" = {
      command                = ["add-employer"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 4096,
      memory                 = 18432,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        { name : "CSV_FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/employer/" }
      ]
    }

    "patch-employer" = {
      command                = ["patch-employer"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 4096,
      memory                 = 8192,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_api_access,
        { name : "CSV_FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/employer/" }
      ]
    }

    "add-leave-admin" = {
      command                = ["add-leave-admin"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 4096,
      memory                 = 18432,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_api_access,
        { name : "CSV_FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/employer/" }
      ]
    }

    "remove-leave-admin" = {
      command                = ["remove-leave-admin"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 4096,
      memory                 = 18432,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_api_access,
        { name : "CSV_FOLDER_PATH", value : "s3://massgov-pfml-${var.environment_name}-agency-transfer/employer/" }
      ]
    }

    "change-default-organization" = {
      command                = ["change-default-organization"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 4096,
      memory                 = 18432,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
      ]
    }

    "dfml-fines-and-repayments" = {
      command                = ["dfml-fines-and-repayments"]
      task_role              = aws_iam_role.dfml_fines_and_repayments_task_role.arn
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fines_repayments_s3_folders,
        local.pub_s3_folders,
        local.emails_fines_and_repayments,

      ]
    }

    "process-new-relic-dashboard-recovery" = {
      command                = ["process-new-relic-dashboard-recovery"]
      task_role              = aws_iam_role.process_new_relic_dashboard_task_role.arn
      execution_role         = aws_iam_role.process_new_relic_dashboard_task_exe_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.new_relic_api_access,
        { name : "NR_DASHBOARD_ARCHIVE_PATH", value : "s3://${data.aws_s3_bucket.new_relic_dashboard.id}/dashboards" },
      ]
    },

    "process-overpayment-referrals" = {
      command                = ["process-overpayment-referrals"]
      task_role              = aws_iam_role.process_overpayment_referrals_task_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_api_access,
        local.pfml_overpayment_case_referrals,
        local.emails_mmars_overpayment,
        { name : "ENABLE_OVERPAYMENT_VCMT_AUTOMATION", value : var.enable_overpayment_vcmt_automation }
      ]
    },

    "process-mmars-response-file" = {
      command                = ["process-mmars-response-file"]
      task_role              = aws_iam_role.process_mmars_response_file_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_api_access,
        local.pfml_overpayment_case_referrals,
      ]
    },


    "process-overpayment-generate-mock-mmars-response" = {
      command                = ["process-overpayment-generate-mock-mmars-response"]
      task_role              = aws_iam_role.sftp_tool_role.arn
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.pfml_overpayment_mock_mmars_response,
      ]
    },

    "process-overpayment-collections" = {
      command                = ["process-overpayment-collections"]
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_api_access,
        local.pfml_overpayment_case_referrals,
        { name : "OVERPAYMENT_REPAYMENT_FETCH_DAYS_PRIOR", value : var.overpayment_repayment_fetch_days_prior },
        { name : "OVERPAYMENT_REPAYMENT_FETCH_START_DATE", value : var.overpayment_repayment_fetch_start_date },
        { name : "OVERPAYMENT_REPAYMENT_FETCH_END_DATE", value : var.overpayment_repayment_fetch_end_date },
        { name : "ENABLE_MOCK_EDM_REPAYMENT_RESPONSE", value : var.enable_mock_edm_repayment_response },
      ]
    },

    "process-rfi-renotifications" = {
      command                = ["process-rfi-renotifications"]
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.fineos_api_access,
      ]
    },

    "address-extract" = {
      command                = ["address-extract"]
      task_role              = aws_iam_role.fineos_import_iaww_task_role.arn,
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.pub_s3_folders,
      ]
    },

    "clean-up-oauth-log" = {
      command                = ["clean-up-oauth-log"]
      cpu                    = 2048,
      memory                 = 4096,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
      ]
    },

    "update-application" = {
      command                = ["update-application"],
      task_role              = aws_iam_role.dor_import_task_role.arn,
      execution_role         = aws_iam_role.dor_import_execution_role.arn,
      cpu                    = 4096,
      memory                 = 18432,
      readonlyRootFilesystem = false,
      env = [
        local.db_access,
        local.pub_s3_folders
      ]
    },

    "submit-deferred-items" = {
      command   = ["submit-deferred-items"]
      task_role = aws_iam_role.submit_deferred_items_task_role.arn
      env = [
        local.db_access,
        local.fineos_api_access,
        { name : "ENABLE_DELAYED_SUBMISSION_OF_MODIFICATIONS", value : var.enable_delayed_submission_of_modifications }
      ]
    },
  }

  # List of Shadow enviorments
  shadow_environments = ["tst2", "infra-test", "prod"]

  # Create shadow tasks for each of the above tasks.
  # replace db_access with the appropriate db_access for the shadow task.
  # replace -prod- with -shadow- for s3 buckets
  # only prod is replaced, by default other environments are non-prod
  # seperate value and valueFrom into environment and secrets
  # set defaults for cpu, memory and execution_role
  shadow_tasks = contains(local.shadow_environments, var.environment_name) ? {
    # Cycle through each of the above tasks and create a shadow task.
    for task_name, task_definition in local.tasks : task_name => {
      command                = task_definition.command,
      task_role              = aws_iam_role.shadow_task_executor[0].arn,
      execution_role         = aws_iam_role.shadow_task_executor[0].arn,
      cpu                    = lookup(task_definition, "cpu", 1024),
      memory                 = lookup(task_definition, "memory", 2048),
      readonlyRootFilesystem = lookup(task_definition, "readonlyRootFilesystem", true),

      # Create envionment variables for the shadow task.
      environment = [
        for env_var in [
          for env_val in
          flatten(
            concat(
              # Replace local_db_access with shadow_db_access
              [for env_val_group in task_definition.env : env_val_group if !contains([var.enforce_execute_sql_read_only ? local.db_read_only_access : local.db_access, local.db_read_only_access, local.db_access, local.db_admin_access, local.db_migrate_access, local.eolwd_moveit_access, local.fineos_api_access], env_val_group)],
              local.shadow_db_access,
              local.common,
              local.shadow_eolwd_moveit_access,
              local.shadow_fineos_api_access
            )
            # Only keep "value" items and remove "valueFrom"
        ) : env_val if contains(keys(env_val), "value")]
        : env_var.value == null ? env_var
        # Add "-shadow" to s3 bucket names
        : { name = env_var.name, value = replace(env_var.value, "-prod-", "-prod-shadow-") }
      ],

      # Create secrets for the shadow task.
      secrets = [
        for env_val in
        flatten(
          concat(
            # Replace loca.ldb_access with shadow_db_access
            [for env_val_group in task_definition.env : env_val_group if !contains([var.enforce_execute_sql_read_only ? local.db_read_only_access : local.db_access, local.db_read_only_access, local.db_access, local.db_admin_access, local.db_migrate_access, local.eolwd_moveit_access, local.fineos_api_access], env_val_group)],
            local.shadow_db_access,
            local.common,
            local.shadow_eolwd_moveit_access,
            local.shadow_fineos_api_access
          )
          # Keep "valueFrom" by removing "value"
        ) : env_val if !contains(keys(env_val), "value")
      ]
    }
  } : {}
}

data "aws_ecr_repository" "app" {
  name = local.app_name
}

# this resource is used as a template to provision each ECS task in local.tasks
resource "aws_ecs_task_definition" "ecs_tasks" {
  for_each                 = local.tasks
  family                   = "${local.app_name}-${var.environment_name}-${each.key}"
  task_role_arn            = lookup(each.value, "task_role", aws_iam_role.db_user_pfml_batch.arn)
  execution_role_arn       = lookup(each.value, "execution_role", aws_iam_role.task_executor.arn)
  cpu                      = tostring(lookup(each.value, "cpu", 1024))
  memory                   = tostring(lookup(each.value, "memory", 2048))
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"

  lifecycle {
    create_before_destroy = true
  }

  container_definitions = jsonencode([
    {
      name                   = each.key,
      image                  = format("%s:%s", data.aws_ecr_repository.app.repository_url, var.service_docker_tag),
      command                = each.value.command,
      cpu                    = lookup(each.value, "cpu", 1024),
      memory                 = lookup(each.value, "memory", 2048),
      networkMode            = "awsvpc",
      essential              = true,
      readonlyRootFilesystem = lookup(each.value, "readonlyRootFilesystem", true),

      linuxParameters = {
        capabilities = {
          drop = ["ALL"]
        },
        initProcessEnabled = true
      },
      logConfiguration = {
        logDriver = "awslogs",
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.ecs_tasks.name,
          "awslogs-region"        = data.aws_region.current.name,
          "awslogs-stream-prefix" = var.environment_name
        }
      },

      # Split env into static environment variables or secrets based on whether they contain "value" or "valueFrom"
      # I know, it's not very readable but this is how terraform is.
      #
      # We use !contains("value") for secrets instead of contains("valueFrom") so that any items with typos are
      # caught and error out when trying to apply the task definition. Otherwise, anything with a typo could
      # silently cause env vars to go missing which would definitely confuse someone for a day or two.
      #
      environment = [for val in flatten(concat(lookup(each.value, "env", []), local.common)) : val if contains(keys(val), "value")]
      secrets     = [for val in flatten(concat(lookup(each.value, "env", []), local.common)) : val if !contains(keys(val), "value")]
    }
  ])
}

# this resource is used as a template to provision Shadow ECS task using the shadow_tasks list
# envirioments are specified in the contains function of the for_each loop, currently infra-test and prod
resource "aws_ecs_task_definition" "shadow_ecs_tasks" {
  # The for_each loop is used to provision Shadow ECS tasks for only for infra-test and prod environments
  for_each                 = { for k, v in local.shadow_tasks : k => v if contains(local.shadow_environments, var.environment_name) }
  family                   = "${local.app_name}-${var.environment_name}-shadow-${each.key}"
  task_role_arn            = each.value.task_role
  execution_role_arn       = each.value.execution_role
  cpu                      = tostring(each.value.cpu)
  memory                   = tostring(each.value.memory)
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"

  lifecycle {
    create_before_destroy = true
  }

  container_definitions = jsonencode([
    {
      name                   = "${each.key}-shadow",
      image                  = format("%s:%s", data.aws_ecr_repository.app.repository_url, var.service_docker_tag),
      command                = each.value.command,
      cpu                    = each.value.cpu,
      memory                 = each.value.memory,
      networkMode            = "awsvpc",
      essential              = true,
      readonlyRootFilesystem = lookup(each.value, "readonlyRootFilesystem", true),
      linuxParameters = {
        capabilities = {
          drop = ["ALL"]
        },
        initProcessEnabled = true
      },
      logConfiguration = {
        logDriver = "awslogs",
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.shadow_ecs_tasks[0].name,
          "awslogs-region"        = data.aws_region.current.name,
          "awslogs-stream-prefix" = "${var.environment_name}-shadow"
        }
      },
      environment = each.value.environment
      secrets     = each.value.secrets
    }
  ])
}
