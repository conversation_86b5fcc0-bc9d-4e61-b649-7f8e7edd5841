#
# Terraform configuration for AWS Step Functions.
#

#
# State machine for daily DOR FINEOS ETL.
#
locals {
  # Get the right details based on the two variables
  st_decrypt_value = jsonencode({ "Name" : "DECRYPT", "Value" : tostring(var.st_decrypt_dor_data) })

  # Allow first step to either generate mock data _or_ import employees from FINEOS
  first_step = var.st_use_mock_dor_data ? "dor_generate" : "fineos_import_employee_updates"

  # Pass the details into the step functions definition
  dor_fineos_etl_definition = templatefile("${path.module}/step_function/dor_fineos_etl.json",
    {
      app_name = "pfml-api"

      first_step                                      = local.first_step
      st_decrypt_value                                = local.st_decrypt_value
      st_file_limit_specified                         = var.st_file_limit_specified
      st_employee_export_limit_specified              = var.st_employee_export_limit_specified
      st_employer_update_limit                        = var.st_employer_update_limit
      service_agreement_load                          = var.service_agreement_load
      employer_service_agreement_version_update_limit = var.employer_service_agreement_version_update_limit
      seconds_between_initial_sa_calls                = var.seconds_between_initial_sa_calls

      cluster_arn           = data.aws_ecs_cluster.cluster.arn
      environment_name      = var.environment_name
      security_group        = aws_security_group.tasks.id
      subnet_1              = var.app_subnet_ids[0]
      subnet_2              = var.app_subnet_ids[1]
      sns_failure_topic_arn = aws_sns_topic.task_failure.arn

      # Only alert for the alerting environments
      task_failure_notification_enabled = contains(module.constants.alerting_environments, var.environment_name)
  })
}

################### Original ####################

#
# State machine for daily DOR FINEOS ETL.
#
resource "aws_sfn_state_machine" "dor_fineos_etl" {
  name       = "${local.app_name}-${var.environment_name}-dor-fineos-etl"
  role_arn   = aws_iam_role.step_functions_execution.arn
  definition = local.dor_fineos_etl_definition

  logging_configuration {
    log_destination        = "${aws_cloudwatch_log_group.dor_fineos_etl.arn}:*"
    include_execution_data = true
    level                  = "ERROR"
  }
}

resource "aws_cloudwatch_log_group" "dor_fineos_etl" {
  name              = "/aws/statemachine/${local.app_name}-${var.environment_name}/dor-fineos-etl"
  retention_in_days = 180
}

resource "aws_iam_role" "step_functions_execution" {
  name               = "${local.app_name}-${var.environment_name}-step-functions"
  assume_role_policy = data.aws_iam_policy_document.step_functions_execution.json
}
data "aws_iam_policy_document" "step_functions_execution" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["states.amazonaws.com"]
    }
  }
}

resource "aws_iam_role_policy" "step_functions_execution" {
  name   = "${local.app_name}-${var.environment_name}-step-functions-role-policy"
  role   = aws_iam_role.step_functions_execution.id
  policy = data.aws_iam_policy_document.iam_policy_step_functions.json
}

data "aws_iam_policy_document" "iam_policy_step_functions" {
  statement {
    effect = "Allow"
    actions = [
      "ecs:RunTask",
      "ecs:StopTask",
      "ecs:DescribeTasks"
    ]
    resources = ["*"]
  }

  statement {
    effect  = "Allow"
    actions = ["iam:PassRole"]
    resources = [
      aws_iam_role.task_executor.arn,
      aws_iam_role.dor_import_task_role.arn,
      aws_iam_role.dor_import_execution_role.arn,
      aws_iam_role.ecs_tasks.arn,
      aws_iam_role.fineos_updates_task_role.arn,
      aws_iam_role.db_user_pfml_batch.arn,
      aws_iam_role.fineos_eligibility_feed_export_task_role.arn,
      aws_iam_role.fineos_bucket_tool_role.arn
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "events:PutTargets",
      "events:PutRule",
      "events:DescribeRule"
    ]
    resources = [
      "arn:aws:events:us-east-1:${data.aws_caller_identity.current.account_id}:rule/StepFunctionsGetEventsForECSTaskRule"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "sns:Publish",
    ]
    resources = [
      aws_sns_topic.task_failure.arn
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "logs:CreateLogDelivery",
      "logs:CreateLogStream",
      "logs:GetLogDelivery",
      "logs:UpdateLogDelivery",
      "logs:DeleteLogDelivery",
      "logs:ListLogDeliveries",
      "logs:PutLogEvents",
      "logs:PutResourcePolicy",
      "logs:DescribeResourcePolicies",
      "logs:DescribeLogGroups"
    ]
    resources = ["*"]
  }
}

#
# Scheduling using EventBridge Scheduler (previously called CloudWatch Events).
#
resource "aws_scheduler_schedule_group" "dor_fineos_etl" {
  name = var.environment_name
}
resource "aws_scheduler_schedule" "dor_fineos_etl" {
  name        = "dor-fineos-etl-${var.environment_name}-schedule"
  group_name  = aws_scheduler_schedule_group.dor_fineos_etl.name
  description = "Schedule dor-fineos-etl step function"
  state       = var.dor_fineos_etl_schedule_enabled ? "ENABLED" : "DISABLED"

  schedule_expression          = var.dor_fineos_etl_schedule_expression
  schedule_expression_timezone = "US/Eastern"
  flexible_time_window {
    mode = "OFF"
  }
  target {
    arn      = aws_sfn_state_machine.dor_fineos_etl.arn
    role_arn = aws_iam_role.eventbridge_step_functions.arn
  }
  depends_on = [
    aws_scheduler_schedule_group.dor_fineos_etl
  ]
}

# Role that allows EventBridge to start Step Functions.
resource "aws_iam_role" "eventbridge_step_functions" {
  name               = "${local.app_name}-${var.environment_name}-eventbridge-step-functions"
  assume_role_policy = data.aws_iam_policy_document.eventbridge_step_functions.json
}

data "aws_iam_policy_document" "eventbridge_step_functions" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["scheduler.amazonaws.com"]
    }
  }
}

resource "aws_iam_role_policy" "eventbridge_step_functions_role_policy" {
  name   = "${local.app_name}-${var.environment_name}-eventbridge-step-functions-role-policy"
  role   = aws_iam_role.eventbridge_step_functions.name
  policy = data.aws_iam_policy_document.eventbridge_step_functions_role_policy_document.json
}

data "aws_iam_policy_document" "eventbridge_step_functions_role_policy_document" {
  statement {
    actions   = ["states:StartExecution"]
    resources = [aws_sfn_state_machine.dor_fineos_etl.arn]
  }
}
