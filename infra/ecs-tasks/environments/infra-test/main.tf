locals {
  environment_name = "infra-test"
}

provider "aws" {
  region = "us-east-1"
}

terraform {
  backend "s3" {
    bucket         = "massgov-pfml-infra-test-env-mgmt"
    key            = "terraform/ecs-tasks.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
  }
}

data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "aws_secretsmanager_secret" "rmv_client_certificate" {
  name = "/service/pfml-api-${local.environment_name}/rmv_client_certificate"
}

data "aws_secretsmanager_secret" "usbank_client_certificate" {
  name = "/service/pfml-api/${local.environment_name}/usbank_client_certificate"
}

module "tasks" {
  source = "../../template"

  environment_name                  = "infra-test"
  service_docker_tag                = local.service_docker_tag
  vpc_id                            = data.aws_vpc.vpc.id
  vpc_name                          = local.vpc
  app_subnet_ids                    = data.aws_subnets.vpc_app.ids
  st_employer_update_limit          = 1500
  rmv_client_certificate_binary_arn = data.aws_secretsmanager_secret.rmv_client_certificate.arn
  enforce_execute_sql_read_only     = false

  usbank_client_base_url               = "https://alpha-apip2-prepaid.usbank.com"
  usbank_client_soap_base_url          = "https://stage-apip.prepaidgateway.com"
  usbank_client_certificate_binary_arn = data.aws_secretsmanager_secret.usbank_client_certificate.arn
  usbank_client_oauth2_url             = "https://stage-apip.prepaidgateway.com/oauth/oauth20/token"
  usbank_transId                       = "3491"
  usbank_certCardId                    = "**********"
  usbank_certCardPasscode              = "3770"
  usbank_fundingCardId                 = "**********"
  usbank_cardType                      = "2"

  # TODO: These values are provided by FINEOS.
  fineos_client_integration_services_api_url = ""
  fineos_client_customer_api_url             = ""
  fineos_client_group_client_api_url         = ""
  fineos_client_wscomposer_api_url           = ""
  fineos_client_oauth2_url                   = ""

  fineos_aws_iam_role_arn                             = ""
  fineos_aws_iam_role_external_id                     = ""
  fineos_eligibility_feed_output_directory_path       = ""
  fineos_import_employee_updates_input_directory_path = ""

  eolwd_moveit_sftp_uri   = "sftp://<EMAIL>"
  pfml_error_reports_path = "s3://massgov-pfml-infra-test-agency-transfer/error-reports/outbound"

  dfml_project_manager_email_address     = "<EMAIL>"
  dfml_business_operations_email_address = "<EMAIL>"
  dfml_fines_repayments_email_address    = "<EMAIL>"
  dfml_fines_repayments_cc_email_address = "<EMAIL>"
  dor_employee_integrity_email_address   = "<EMAIL>"
  agency_reductions_email_address        = "<EMAIL>"
  agency_service_desk_email_address      = "<EMAIL>"
  mmars_overpayment_email_address        = "<EMAIL>"
  mmars_overpayment_bcc_email_address    = "<EMAIL>"
  dfml_db_migration_email_address        = "<EMAIL>"
  dfml_db_migration_cc_email_address     = "<EMAIL>"

  # TODO: Values from FINEOS.
  fineos_data_export_path         = ""
  fineos_monthly_data_export_path = ""
  fineos_adhoc_data_export_path   = ""
  fineos_data_import_path         = ""
  fineos_error_export_path        = ""

  pfml_fineos_inbound_path                            = "s3://massgov-pfml-infra-test-agency-transfer/cps/inbound"
  pfml_fineos_outbound_path                           = "s3://massgov-pfml-infra-test-agency-transfer/cps/outbound"
  pfml_fineos_eligibility_feed_archive_directory_path = "s3://massgov-pfml-infra-test-agency-transfer/cps/eligibility-feed-to-cps"

  payment_audit_report_outbound_folder_path = "s3://massgov-pfml-infra-test-agency-transfer/audit/outbound"
  payment_audit_report_sent_folder_path     = "s3://massgov-pfml-infra-test-agency-transfer/audit/sent"

  sync_employee_feed_aggregate_step_enable              = "1"
  enable_to_use_feed_aggregate_table                    = "1"
  enable_sync_address_using_aggregate_table             = "1"
  enable_sync_eft_using_aggregate_table                 = "1"
  enable_sync_payment_preferences_using_aggregate_table = "0"
  enable_payment_line_matching                          = "1"
  enable_document_upload_optional                       = "1"
  # @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review
  enable_mark_applications_ready_for_review = "1"
  # @TODO(PFMLPB-24796): Remove enable_re_notification
  enable_re_notification = "0"

  # EligibilityFeed Feature Flag
  enabled_new_fineos_eligibility_feed_export = "1"

  enable_register_admins_job = true

  task_failure_email_address_list = ["<EMAIL>"]

  # FineOS ETL Variables, cron expressions are in ET timezone
  dor_fineos_etl_schedule_enabled    = false
  dor_fineos_etl_schedule_expression = "cron(30 19 ? * MON-FRI *)" # 7:30 PM ET Monday through Friday

  # Used for 1099 only
  localhost_pdf_api_host = "http://localhost:5000"
  # Used for all other PDF generation
  generate_1099_max_files         = "10000"
  upload_max_files_to_fineos      = "10000"
  enable_1099_testfile_generation = "0"
  irs_1099_correction_ind         = "1"
  irs_1099_tax_year               = "2024"

  enable_offset_get_1099      = "0"
  upload_1099_doc_batch_start = "1"
  upload_1099_doc_batch_end   = "10000"

  # PDF API Service Variables
  pdf_api_lb_port = 3606

  # EDM API Sevice Variables
  edm_client_api_base_url = "https://coma-lwd-dev.privatelink.snowflakecomputing.com/api/v2/statements"
  edm_client_oauth2_url   = "https://coma-lwd-dev.privatelink.snowflakecomputing.com/oauth/token-request"


  enable_pub_undeliverable_checks_file_processing = "1"
  enable_employer_overpayment                     = "0"
  enable_email_overpayment_referrals_inf_file     = "0"

  pub_payment_starting_check_number = "106"

  # Service Agreement feature flag config for creating FINEOS service agreement using versions
  use_service_agreement_versions = "0"
  # Allow link_claim_to_application step to send notifications
  portal_base_url      = "https://paidleave-test.mass.gov"
  service_now_base_url = "https://savilinxtest.servicenowservices.com"

  release_version = var.release_version

  enable_prepaid_impact_payments = "1"
  enable_sync_payment_preference = "1"
  enable_s3_direct_copy          = "0"

  enable_overpayment_vcmt_automation = "0"

  # Med to Bonding
  # @TODO(PFMLPB-24292): Remove feature flag for Med to Bonding BPC
  enable_delayed_submission_of_modifications = "0"

  ####  Cloudwatch ecs schedules

  appeals_generate_intake_pdfs_schedule_expression = "rate(30 minutes)"
  enable_appeals_generate_intake_pdfs              = false

  appeals_import_extract_schedule_expression = "cron(0 10 ? * * *)"
  enable_appeals_import_extract              = false

  child_support_dor_import_schedule_expression = "cron(0 9 ? * FRI *)"
  enable_child_support_automation              = "0"

  cps_errors_crawler_schedule_expression = "cron(0 15 * * ? *)"
  enable_cps_errors_crawler              = false

  export_60_day_comms_report_schedule_expression = "cron(0 6 ? * WED *)"
  enable_export_60_day_comms_report              = false

  export_leave_admins_created_schedule_expression = "rate(24 hours)"
  enable_export_leave_admins_created              = false

  export_psd_report_schedule_expression = "cron(0 13 * * ? *)"
  enable_export_psd_report              = false

  fineos_data_export_tool_schedule_expression = "cron(0 9 * * ? *)"
  enable_fineos_data_export_tool              = false

  fineos_error_extract_tool_schedule_expression = "cron(0 14 * * ? *)"
  enable_fineos_error_extract_tool              = false

  fineos_import_employee_updates_schedule_expression = "cron(30 13 * * ? *)"
  enable_standalone_fineos_import_employee_updates   = false

  saturday_fineos_import_employee_updates_schedule_expression = "cron(0 2 ? * SAT *)"
  enable_saturday_standalone_fineos_import_employee_updates   = true # true or false

  fineos_import_la_units_schedule_expression = "cron(0 3 ? * MON-FRI *)"
  enable_fineos_import_la_units              = false

  fineos_monthly_extract_scheduler_schedule_expression = "cron(25 9 * * ? *)"
  enable_fineos_monthly_extract_scheduler              = false

  fineos_report_extract_tool_schedule_expression = "cron(30 17,21 * * ? *)"
  enable_fineos_report_extracts_tool             = false

  fineos_snapshot_extract_schedule_expression = "cron(0 10 ? * WED *)"
  enable_fineos_snapshot_extract_tool         = false

  import_fineos_to_warehouse_schedule_expression = "cron(0 4 * * ? *)"
  enable_import_fineos_to_warehouse              = false

  # If you are updating "enable_1099_generator" from "false" to "true", you must also update
  # pub_payments_process_1099_documents_schedule's "start_date" in eventbridge.tf since its
  # current value is older than what EventBridge allows. It has to run every other Thursday
  # at 13:00 UTC since "2024-02-01T13:00:00Z". Reach out to Infra Team for additional help.

  # we are switching to the cron scheduler to schedule the job evry business day. This will be
  # revert back to the rate(14 days) in future. That time we need to uncomment the rate
  # and remove the cron scheduler below
  # pub_payments_process_1099_documents_schedule_expression = "rate(14 days)"
  pub_payments_process_1099_documents_schedule_expression = "cron(0 8 ? * WED *)"
  enable_1099_generator                                   = false

  pub_payments_copy_audit_report_schedule_expression = "cron(0 16 ? * MON-FRI *)"
  enable_pub_payments_copy_audit_report_schedule     = false

  pub_payments_process_fineos_schedule_expression        = "cron(15 5 ? * MON-FRI *)"
  pub_payments_process_snapshot_schedule_expression      = "cron(0 14 ? * WED *)"
  pub_payments_verify_fineos_extract_schedule_expression = "cron(30 12 ? * MON-FRI *)"
  enable_pub_automation_fineos                           = false
  enable_pub_payments_process_snapshot_fineos            = true

  pub_process_weekly_reports_schedule_expression = "cron(0 14 ? * TUE *)"
  enable_pub_process_weekly_reports              = false

  reductions_dia_send_claimant_lists_schedule_expression     = "cron(0 9 ? * MON-FRI *)"
  reductions_dua_send_claimant_lists_schedule_expression     = "cron(0 9 ? * MON-FRI *)"
  enable_reductions_send_claimant_lists_to_agencies_schedule = false

  reductions_process_agency_data_lists_schedule_expression = "cron(0/15 0-2,18-23 ? * MON-FRI *)"
  enable_reductions_process_agency_data_schedule           = false

  weekend_fineos_import_la_units_schedule_expression = "cron(0 10 ? * SAT-SUN *)"
  enable_weekend_fineos_import_la_units              = false

  sunday_pub_payments_process_fineos_schedule_expression = "cron(0 11 ? * SUN *)"
  enable_sunday_pub_payments_process_fineos_schedule     = false

  saturday_pub_payments_process_fineos_schedule_expression = "cron(0 8 ? * SAT *)"
  enable_saturday_pub_payments_process_fineos_schedule     = false

  process_prepaid_debit_registration_schedule_expression = "cron(0 14 ? * MON-FRI *)"
  enable_process_prepaid_debit_registration              = true # true or false

  process_overpayment_referrals_schedule_expression = "cron(0 9 ? * THU *)"
  enable_process_overpayment_referrals              = true

  process_mmars_response_file_schedule_expression = "cron(30 8 ? * MON-FRI *)"
  enable_process_mmars_response_file              = true # true or false

  process_overpayment_collections_schedule_expression = "cron(0 14 ? * MON-FRI *)"
  enable_process_overpayment_collections              = true # true or false

  process_rfi_renotifications_schedule_expression = "cron(0 13 ? * * *)"

  dua_wages_from_dor_import_schedule_expression = "cron(30 10 * * ? *)"
  enable_dua_wages_from_dor_import_schedule     = false

  # Leave admin reminder notifications
  leave_admin_review_reminder_notifications_schedule_expression = "cron(0 8 ? * MON-FRI *)" # Runs at 8:00 AM EST, Monday through Friday
  enable_leave_admin_review_reminder_notifications              = false
}
