module "portal_dev_response_header_policy" {
  # An AWS account can only have 20 cloudfront response header policies.
  #
  # Each Portal dev workspace shares this header policy to ensure that we
  # aren't hitting the 20-policy limit. Note that up to 100 cloudfront 
  # distributions can be connected to a single response header policy.
  source  = "./cloudfront_response_headers_policy"
  name    = "portal-dev-response-header-policy"
  comment = "Portal response header policy"
}