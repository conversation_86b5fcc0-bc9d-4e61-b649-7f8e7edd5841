# This file houses all IAM resources for OIDC auth between AWS and GitHub Actions
# OIDC allows GitHub Actions to use one-time session tokens rather than an access key and secret to authenticate with AWS
# The role, policies, and documents defined below grant the necessary permissions to GitHub Actions to run CI/CD pipelines
# to create, modify, and delete AWS resources within the PFML account

locals {
  external_roles = [
    "ADFS-*",
    "AWS-${data.aws_caller_identity.current.account_id}-*",
    "EOTSS-*",
    "eotss_*",
    "EOLWD-PFML-VPC-FlowLogs",
    "VPCFlowLogStack-*",
    "workspaces_DefaultRole",
    "us-east-1-config-role",
    "tss-soe-softwarecompliance",
    "EOL-SOE-*",
    "SOE-*",
    "soe-*",
    "smx-*",
    "AWSCloudFormationStackSetExecutionRole",
    "lambda_s3_access",
    "idaptive-Admin",
    "AWS_Events_Invoke_Event_Bus_*",
  ]
}

resource "aws_iam_openid_connect_provider" "github_actions_oidc" {
  url             = "https://token.actions.githubusercontent.com"
  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = ["1b511abead59c6ce207077c0bf0e0043b1382612"]
}

resource "aws_iam_role" "github_actions_oidc" {
  name                 = "ci-run-deploys-oidc"
  assume_role_policy   = data.aws_iam_policy_document.github_actions_oidc.json
  max_session_duration = 21600 # 6hrs
  managed_policy_arns = [
    aws_iam_policy.ci_run_deploys_bulk_policy.arn,
    aws_iam_policy.ci_run_deploys_iam.arn,
    aws_iam_policy.ci_run_deploys_e2e.arn,
  ]
}

# Trust policy: Allows EOLWD GitHub Org GHA runners to assume this role
# Also allow infrastructure-admins role the ability to assume this role if needed
data "aws_iam_policy_document" "github_actions_oidc" {
  statement {
    actions = ["sts:AssumeRoleWithWebIdentity"]
    effect  = "Allow"

    principals {
      type        = "Federated"
      identifiers = [aws_iam_openid_connect_provider.github_actions_oidc.arn]
    }

    condition {
      test     = "StringEquals"
      variable = "token.actions.githubusercontent.com:aud"
      values   = ["sts.amazonaws.com"]
    }

    condition {
      test     = "StringLike"
      variable = "token.actions.githubusercontent.com:sub"
      values   = ["repo:EOLWD/*"]
    }
  }

  statement {
    sid = "PFMLInfraAssumeRole"
    actions = [
      "sts:AssumeRole",
      "sts:TagSession"
    ]
    principals {
      type = "AWS"
      identifiers = [
        module.constants.infra_admin_sso_arn
      ]
    }
  }
}

resource "aws_iam_policy" "ci_run_deploys_bulk_policy" {
  name        = "ci-run-deploys-bulk-policy"
  description = "Bulk of the access policies for deploying PFML applications"
  policy      = data.aws_iam_policy_document.ci_run_deploys_bulk_policy.json
}

data "aws_iam_policy_document" "ci_run_deploys_bulk_policy" {
  statement {
    actions = [
      # Allow access the terraform_locks and Infra Tooling tables.
      "dynamodb:*",

      # Allow domain certificate lookups.
      "acm:Describe*",
      "acm:Get*",
      "acm:List*",
      "acm:AddTagsToCertificate",

      # Allow teams to configure logging and monitoring.
      "cloudtrail:*",
      "cloudwatch:*",
      "logs:*",

      # Allow CI to access SQS and EventBridge Pipes resources
      "sqs:*",
      "pipes:*",

      # Allow teams to read and set Cloudwatch event rules.
      "events:*",

      # Allow teams to look at IAM users and roles; additional (restricted)
      # Allow tagging.
      "iam:Get*",
      "iam:Tag*",
      "iam:List*",

      # Allow teams to use KMS encryption keys.
      "kms:*",

      # Allow teams to read and store data in S3. Also allows the Portal
      # team to deploy the application to S3-hosted buckets.
      "s3:*",

      # Allow teams to manage SNS topics and subscriptions.
      "sns:*",

      # Allow CI to read EC2 Resources.
      "ec2:Describe*",

      # Allow teams to manage security groups and tags.
      "ec2:AuthorizeSecurityGroupEgress",
      "ec2:AuthorizeSecurityGroupIngress",
      "ec2:CreateSecurityGroup",
      "ec2:DeleteSecurityGroup",
      "ec2:RevokeSecurityGroupEgress",
      "ec2:RevokeSecurityGroupIngress",
      "ec2:CreateTags",
      "ec2:DeleteTags",

      # Allow teams to configure VPC endpoints for network privacy.
      "ec2:CreateVpcEndpoint",
      "ec2:DeleteVpcEndpoints",
      "ec2:ModifyVpcEndpoint",

      # Allow teams to configure Lambda ENIs.
      "ec2:CreateNetworkInterface",
      "ec2:DeleteNetworkInterface",
      "ec2:DetachNetworkInterface",

      # Allow teams to configure VPC Links for network privacy.
      "ec2:CreateVpcEndpointServiceConfiguration",
      "ec2:DeleteVpcEndpointServiceConfigurations",
      "ec2:ModifyVpcEndpointServicePermissions",

      # Allow the Portal team to set up the Portal application, including:
      # - Cloudfront distributions of the statically-built files.
      # - Simple Email Service for sending information to folks using the Portal.
      "cloudfront:*",
      "ses:*",

      # Allow API team to set up the API, including:
      # - API Gateway and load balancing
      # - Docker container management on ECR + ECS
      # - RDS data storage
      # - Lambdas for data pipelining.
      # - Web Application Firewall (WAF)
      # - Firehose (for WAF logs)
      # - Kinesis Analytics (for WAF logs)
      "apigateway:*",
      "ecr:*",
      "ecs:*",
      "elasticloadbalancing:*",
      "rds:*",
      "lambda:*",
      "application-autoscaling:*",
      "wafv2:*",
      "waf-regional:*",
      "firehose:*",
      "kinesis:*",
      "kinesisanalytics:*",

      # Allow Infra Admins to view resources associated with WAF ACL
      "appsync:ListGraphqlApis",

      # Allow API team to deploy Step Functions, such as the DOR Import.
      "states:*",

      # Allow teams to deploy CloudFormation stacks to manage resources
      # Terraform can not
      "cloudformation:*",

      # Allow listing of IAM role policies
      "iam:ListRolePolicies",

      # Allow creation of Security Hub Custom Insights
      "securityhub:CreateInsight",
      "securityhub:DeleteInsight",
      "securityhub:UpdateInsight",
      "securityhub:GetInsights",

      # Allow read, list the AWS Secret Manager secrets
      "secretsmanager:DescribeSecret",
      "secretsmanager:GetSecretValue",
      "secretsmanager:ListSecrets",
      "secretsmanager:GetResourcePolicy",

      # Event Bridge Schduler access
      "scheduler:Create*",
      "scheduler:Delete*",
      "scheduler:Get*",
      "scheduler:List*",
      "scheduler:TagResource",
      "scheduler:UpdateSchedule",

      # Create Amplify Deployment to Update Schedules Table
      "amplify:CreateDeployment",
      "amplify:StartDeployment",

      # Create, Update, Delete AWS Glue Databases
      "glue:Get*",
      "glue:UpdateDatabase",
      "glue:DeleteDatabase",
      "glue:TagResource",

      # SSM is required to pull down database passwords, which are used
      # when generating the database itself or when updating the password
      # through a rotation.
      "ssm:Describe*",
      "ssm:GetParameter",
      "ssm:GetParameters",
      "ssm:ListTagsForResource",
      "ssm:PutParameter",
      "ssm:AddTagsToResource",
      "ssm:RemoveTagsFromResource",
      "ssm:GetCommandInvocation",

      # Allow runners to connect to EC2 instances when needed
      "ssm:StartSession",
      "ssm:SendCommand",
      "ssm:TerminateSession",
      "ssm:GetCommandInvocation",
      "ssmmessages:*",

      # Cost Anomoly Detection tooling automation
      "ce:TagResource",
      "ce:GetAnomalyMonitors",
      "ce:CreateAnomalyMonitor",
      "ce:UpdateAnomalyMonitor",
      "ce:DeleteAnomalyMonitor",
      "ce:GetAnomalies",
      "ce:GetAnomalySubscriptions",
      "ce:CreateAnomalySubscription",
      "ce:DeleteAnomalySubscription",
      "ce:UpdateAnomalySubscription",
      "ce:ListTagsForResource",

      # R53 used by ALBs and dev (portal preview) environments
      "route53:Get*",
      "route53:List*",
      "route53:ChangeResourceRecordSets",
    ]

    resources = [
      "*"
    ]
  }
}

resource "aws_iam_policy" "ci_run_deploys_e2e" {
  name        = "ci-run-deploys-e2e"
  description = "Allows E2E GHA workflows to perform tests with AWS services"
  policy      = data.aws_iam_policy_document.ci_run_deploys_e2e.json
}

data "aws_iam_policy_document" "ci_run_deploys_e2e" {
  statement {
    sid    = "E2ETests"
    effect = "Allow"
    actions = [
      "ssm:SendCommand",
      "sts:AssumeRole",
    ]
    resources = [
      "arn:aws:ec2:us-east-1:*:instance/i-074fd4158f3364b21",
      "arn:aws:iam::666444232783:role/*",
      "arn:aws:iam::016390658835:role/*",
      "arn:aws:iam::637423617089:role/*",
      "arn:aws:ssm:us-east-1::document/AWS-RunShellScript",
    ]
  }
}

resource "aws_iam_policy" "ci_run_deploys_iam" {
  name        = "ci-run-deploys-iam"
  description = "Allows GHAs to modify IAM resouces with the exception of externally managed IAM roles"
  policy      = data.aws_iam_policy_document.ci_run_deploys_iam.json
}

data "aws_iam_policy_document" "ci_run_deploys_iam" {
  statement {
    sid     = "IamRolesAccess"
    actions = ["iam:*"]
    not_resources = [
      for role in local.external_roles : "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/${role}"
    ]
  }
  # Allow Infra engineers to run TF in LWD account for self-hosted GHA runners
  statement {
    sid = "InfraAssumeLWDRole"
    actions = [
      "sts:AssumeRole",
      "sts:TagSession"
    ]
    resources = [
      "arn:aws:iam::************:role/massgov-infrastructure-admin"
    ]
  }
}