# This file was originally generated from the following command:
#
#   bin/bootstrap-env.sh uat api
#
# If adding new variables, it's recommended to update the bootstrap
# templates so there's less manual work in creating new envs.
#

locals {
  environment_name = "uat"
}

provider "aws" {
  region = "us-east-1"
}

terraform {
  backend "s3" {
    bucket         = "massgov-pfml-uat-env-mgmt"
    key            = "terraform/api.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
  }
}

data "aws_ecs_cluster" "uat" {
  cluster_name = "uat"
}

data "aws_secretsmanager_secret" "rmv_client_certificate" {
  name = "/service/pfml-api-${local.environment_name}/rmv_client_certificate"
}

data "aws_secretsmanager_secret" "usbank_client_certificate" {
  name = "/service/pfml-api/${local.environment_name}/usbank_client_certificate"
}

module "api" {
  source = "../../template"

  environment_name                = local.environment_name
  service_app_count               = 1
  service_max_app_count           = 10
  service_docker_tag              = local.service_docker_tag
  service_ecs_cluster_arn         = data.aws_ecs_cluster.uat.arn
  vpc_id                          = data.aws_vpc.vpc.id
  vpc_app_subnet_ids              = data.aws_subnets.vpc_app.ids
  vpc_db_subnet_ids               = data.aws_subnets.vpc_db.ids
  vpc_name                        = local.vpc
  lwd_cdata_ip_addresses          = ["**************/32"]
  postgres_version                = "14.13"
  postgres_parameter_group_family = "postgres14"
  lb_port                         = 3503
  cors_origins = [
    # Allow requests from the Portal and API Gateway (Swagger) UAT environment.
    "https://paidleave-uat.mass.gov",
    "https://d31sked9ffq37g.cloudfront.net",
    "https://paidleave-api-uat.mass.gov",
    # Allow requests from the Admin Portal
    "https://paidleave-admin-uat.dfml.eol.mass.gov",
    "https://0mv19lqx41.execute-api.us-east-1.amazonaws.com"
  ]

  rmv_client_base_url               = "https://atlas-staging-gateway.massdot.state.ma.us/vs"
  rmv_client_certificate_binary_arn = data.aws_secretsmanager_secret.rmv_client_certificate.arn
  rmv_api_behavior                  = "partially_mocked" # TODO?
  rmv_check_mock_success            = "1"

  usbank_client_base_url               = "https://alpha-apip2-prepaid.usbank.com"
  usbank_client_certificate_binary_arn = data.aws_secretsmanager_secret.usbank_client_certificate.arn

  # copied from tst1 for now, with replacements for idt --> uat. may not be right
  fineos_client_customer_api_url                      = "https://uat-api.masspfml.fineos.com/customerapi/"
  fineos_client_integration_services_api_url          = "https://uat-api.masspfml.fineos.com/integration-services/"
  fineos_client_group_client_api_url                  = "https://uat-api.masspfml.fineos.com/groupclientapi/"
  fineos_client_wscomposer_api_url                    = "https://uat-api.masspfml.fineos.com/integration-services/wscomposer/"
  fineos_client_wscomposer_user_id                    = "OASIS"
  fineos_client_soap_user_id                          = "<EMAIL>"
  fineos_client_oauth2_url                            = "https://uat-api.masspfml.fineos.com/oauth2/token"
  fineos_import_employee_updates_input_directory_path = "s3://fin-sompre-data-export/UAT/dataexports"

  service_now_base_url            = "https://savilinxuat.servicenowservices.com"
  portal_base_url                 = "https://paidleave-uat.mass.gov"
  admin_portal_base_url           = "https://paidleave-admin-uat.dfml.eol.mass.gov"
  fineos_aws_iam_role_arn         = "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-S0EP9ABIA02Z"
  fineos_aws_iam_role_external_id = "8jFBtjr4UA@"
  enable_application_fraud_check  = "0"
  release_version                 = var.release_version

  azure_ad_authority_domain = "login.microsoftonline.com"
  azure_ad_client_id        = "ecc75e15-cd60-4e28-b62f-d1bf80e05d4d"
  azure_ad_parent_group     = "TSS-SG-PFML_ADMIN_PORTAL_NON_PROD"
  azure_ad_tenant_id        = "3e861d16-48b7-4a0e-9806-8c04d81b7b2a"

  enable_document_multipart_upload                           = "1"
  limit_ssn_fein_max_attempts                                = "5"
  claim_status_v2_employer_review                            = "0"
  enable_email_change                                        = "1"
  enable_service_agreement_versions_for_existing_automations = "1"
  enable_system_message_translation                          = "0"

  enable_prepaid_impact_payments = "1"
  enable_sync_payment_preference = "1"
  enable_s3_direct_copy          = "0"

  # Med to Bonding
  # @TODO(PFMLPB-24292): Remove feature flag for Med to Bonding BPC
  enable_delayed_submission_of_modifications = "0"

  # @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review
  enable_mark_applications_ready_for_review = "1"

  # @TODO(PFMLPB-24796): Remove enable_re_notification
  enable_re_notification = "0"

  enable_document_upload_optional = "1"

  fineos_v21_upgrade_date = "2022-04-04"
  fineos_is_running_v24   = "1"

  # PDF API Service Variables
  pdf_api_lb_port               = 3603
  pdf_api_service_app_count     = 1
  pdf_api_service_max_app_count = 3

  # Following provided by BI Team
  snowflake_snowpipe_user_arn    = "arn:aws:iam::************:user/0mxi0000-s"
  snowflake_snowpipe_external_id = "NOB05670_SFCRole=2_f0xEhdRaLwAPqaN8TrZzakX/C9E="
  snowflake_snowpipe_queue_arn   = "arn:aws:sqs:us-east-1:************:sf-snowpipe-AIDAQ3EGUUD3O2NTJNW2O-nUVNDs78JnWBOLxd-0YleQ"

  # US Bank Client settings
  usbank_client_oauth2_url = "https://stage-apip.prepaidgateway.com/oauth/oauth20/token"
}
