# This file was originally generated from the following command:
#
#   bin/bootstrap-env.sh prod api
#
# If adding new variables, it's recommended to update the bootstrap
# templates so there's less manual work in creating new envs.
#

locals {
  environment_name = "prod"
}

provider "aws" {
  region = "us-east-1"
}

terraform {
  backend "s3" {
    bucket         = "massgov-pfml-prod-env-mgmt"
    key            = "terraform/api.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
    kms_key_id     = "arn:aws:kms:us-east-1:************:key/641eba51-98e5-4776-98b6-98ed06866ec8"
  }
}

data "aws_ecs_cluster" "prod" {
  cluster_name = "prod"
}

data "aws_secretsmanager_secret" "rmv_client_certificate" {
  name = "/service/pfml-api-${local.environment_name}/rmv_client_certificate"
}

data "aws_secretsmanager_secret" "usbank_client_certificate" {
  name = "/service/pfml-api/${local.environment_name}/usbank_client_certificate"
}

module "api" {
  source = "../../template"

  environment_name                = local.environment_name
  service_app_count               = 5
  service_max_app_count           = 10
  service_docker_tag              = local.service_docker_tag
  service_ecs_cluster_arn         = data.aws_ecs_cluster.prod.arn
  vpc_id                          = data.aws_vpc.vpc.id
  vpc_app_subnet_ids              = data.aws_subnets.vpc_app.ids
  vpc_db_subnet_ids               = data.aws_subnets.vpc_db.ids
  vpc_name                        = local.vpc
  lwd_cdata_ip_addresses          = ["**************/32"]
  postgres_version                = "14.13"
  postgres_parameter_group_family = "postgres14"
  db_allocated_storage            = 998
  db_max_allocated_storage        = 999
  db_instance_class               = "db.r5.2xlarge" # For now, this must be changed in AWS Console. Modifications to this field will yield no result.
  db_iops                         = 1000
  db_storage_type                 = "io1" # For now, this must be changed in AWS Console. Modifications to this field will yield no result.
  db_multi_az                     = true
  lb_port                         = 443
  cors_origins = [
    # Allow requests from the Portal and API Gateway (Swagger) production environment.
    "https://paidleave.mass.gov",
    "https://d2pc6g7x2eh1yn.cloudfront.net",
    "https://paidleave-api.mass.gov",
    # Allow requests from the Admin Portal
    "https://paidleave-admin.dfml.eol.mass.gov",
    "https://zi7eve1v85.execute-api.us-east-1.amazonaws.com"
  ]

  rmv_client_base_url               = "https://atlas-gateway.massdot.state.ma.us"
  rmv_client_certificate_binary_arn = data.aws_secretsmanager_secret.rmv_client_certificate.arn
  rmv_api_behavior                  = "not_mocked"

  usbank_client_base_url               = "https://apip2-prepaid.usbank.com"
  usbank_client_certificate_binary_arn = data.aws_secretsmanager_secret.usbank_client_certificate.arn

  fineos_client_customer_api_url             = "https://prd-api.masspfml.fineos.com/customerapi/"
  fineos_client_integration_services_api_url = "https://prd-api.masspfml.fineos.com/integration-services/"
  fineos_client_group_client_api_url         = "https://prd-api.masspfml.fineos.com/groupclientapi/"
  fineos_client_wscomposer_api_url           = "https://prd-api.masspfml.fineos.com/integration-services/wscomposer/"
  fineos_client_wscomposer_user_id           = "OASIS"
  fineos_client_soap_user_id                 = "OASIS"
  fineos_client_oauth2_url                   = "https://prd-api.masspfml.fineos.com/oauth2/token"
  fineos_v21_upgrade_date                    = "2022-05-20"
  fineos_is_running_v24                      = "1"

  service_now_base_url            = ""
  portal_base_url                 = "https://paidleave.mass.gov"
  admin_portal_base_url           = "https://paidleave-admin.dfml.eol.mass.gov"
  enable_application_fraud_check  = "1"
  fineos_aws_iam_role_arn         = "arn:aws:iam::************:role/somprod-IAMRoles-CustomerAccountAccessRole-83KBPT56FTQP"
  fineos_aws_iam_role_external_id = "8jFBtjr4UA@"

  fineos_import_employee_updates_input_directory_path = "s3://fin-somprod-data-export/PRD/dataexports"

  release_version = var.release_version

  new_plan_proofs_active_at = "2021-06-26 00:00:00+00:00"

  enable_document_multipart_upload                           = "1"
  channel_switch_unsupported_claims                          = "0"
  enable_verification_limits                                 = "1"
  claim_status_v2_employer_review                            = "0"
  enable_email_change                                        = "1"
  enable_service_agreement_versions_for_existing_automations = "0"
  enable_system_message_translation                          = "0"
  enable_fineos_api_logging_to_db                            = "0"

  enable_prepaid_impact_payments = "1"
  enable_sync_payment_preference = "1"
  enable_s3_direct_copy          = "0"

  # Med to Bonding
  # @TODO(PFMLPB-24292): Remove feature flag for Med to Bonding BPC
  enable_delayed_submission_of_modifications = "0"

  # @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review
  enable_mark_applications_ready_for_review = "1"

  # @TODO(PFMLPB-24796): Remove enable_re_notification
  enable_re_notification = "0"

  enable_document_upload_optional = "1"

  azure_ad_authority_domain = "login.microsoftonline.com"
  azure_ad_client_id        = "c87412f7-a62f-48dd-8971-c753c063574b"
  azure_ad_parent_group     = "TSS-SG-PFML_ADMIN_PORTAL_PROD"
  azure_ad_tenant_id        = "3e861d16-48b7-4a0e-9806-8c04d81b7b2a"


  # PDF API Service Variables
  pdf_api_lb_port               = 8443
  pdf_api_service_app_count     = 1
  pdf_api_service_max_app_count = 3

  # Following provided by BI Team
  snowflake_snowpipe_user_arn    = "arn:aws:iam::685689791302:user/6kb00000-s"
  snowflake_snowpipe_external_id = "XOB38888_SFCRole=31_VGfNX0xwLVtL8mv0XVOmQUfiXiU="
  snowflake_snowpipe_queue_arn   = "arn:aws:sqs:us-east-1:685689791302:sf-snowpipe-AIDAZ7JSL4NDB2RS7JO7Y-xeJKmD67sJoii00Xr9m8EA"

  # S3 Access Logging
  s3_access_logging_enabled = true

  # Enable bucket replication
  bucket_replication_enabled = true

  # LMG SETTINGS
  idp_ssm_env                 = "prod"
  lmg_personal_base_url       = "https://personal.login.mass.gov/a1a3107f-6bd6-4b8c-8324-51e880cdee7c"
  lmg_personal_application_id = "2430c069-95b0-4cb3-a655-f3812763a1a4"
  lmg_business_base_url       = "https://admin.login.mass.gov/c569b992-c0e9-489d-a50b-e0d6dabe9d2f"
  lmg_business_application_id = "26010e95-2d51-444b-b374-44b662e564b3"

  # MMG API SETTINGS
  my_mass_gov_api_client_base_url           = "https://api.my.mass.gov"
  mymassgov_api_personal_application_id_uri = "https://MassGovB2C.onmicrosoft.com/ab56e3bb-14ae-4c13-b7f8-4642242a4b2c"
  mymassgov_api_business_application_id_uri = "https://MassGovPartnersB2C.onmicrosoft.com/11f5852c-2076-4d5c-9f90-44920de146a2"

  # US Bank Client settings
  usbank_client_oauth2_url = "https://www-apip.prepaidgateway.com/oauth/oauth20/token"
}

