# This file was originally generated from the following command:
#
#   bin/bootstrap-env.sh training api
#
# If adding new variables, it's recommended to update the bootstrap
# templates so there's less manual work in creating new envs.
#

locals {
  environment_name = "training"
}

provider "aws" {
  region = "us-east-1"
}

terraform {
  backend "s3" {
    bucket         = "massgov-pfml-training-env-mgmt"
    key            = "terraform/api.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
  }
}

data "aws_ecs_cluster" "training" {
  cluster_name = "training"
}

data "aws_secretsmanager_secret" "rmv_client_certificate" {
  name = "/service/pfml-api-${local.environment_name}/rmv_client_certificate"
}

data "aws_secretsmanager_secret" "usbank_client_certificate" {
  name = "/service/pfml-api/${local.environment_name}/usbank_client_certificate"
}

module "api" {
  source = "../../template"

  environment_name                = local.environment_name
  service_app_count               = 1
  service_max_app_count           = 10
  service_docker_tag              = local.service_docker_tag
  service_ecs_cluster_arn         = data.aws_ecs_cluster.training.arn
  vpc_id                          = data.aws_vpc.vpc.id
  vpc_app_subnet_ids              = data.aws_subnets.vpc_app.ids
  vpc_db_subnet_ids               = data.aws_subnets.vpc_db.ids
  vpc_name                        = local.vpc
  postgres_version                = "14.13"
  postgres_parameter_group_family = "postgres14"
  lb_port                         = 3502
  cors_origins = [
    # Allow requests from the API Gateway (Swagger) and Portal training environments.
    "https://mo0nk02mkg.execute-api.us-east-1.amazonaws.com",
    "https://dist3ws941qq9.cloudfront.net",
    "https://paidleave-api-training.mass.gov",
    "https://paidleave-training.mass.gov",
    # Allow requests from the Admin Portal
    "https://paidleave-admin-training.dfml.eol.mass.gov",
  ]

  rmv_client_base_url               = "https://atlas-staging-gateway.massdot.state.ma.us/vs"
  rmv_client_certificate_binary_arn = data.aws_secretsmanager_secret.rmv_client_certificate.arn
  rmv_api_behavior                  = "partially_mocked"
  rmv_check_mock_success            = "1"

  usbank_client_base_url               = "https://alpha-apip2-prepaid.usbank.com"
  usbank_client_certificate_binary_arn = data.aws_secretsmanager_secret.usbank_client_certificate.arn

  fineos_client_integration_services_api_url          = "https://trn-api.masspfml.fineos.com/integration-services/"
  fineos_client_group_client_api_url                  = "https://trn-api.masspfml.fineos.com/groupclientapi/"
  fineos_client_customer_api_url                      = "https://trn-api.masspfml.fineos.com/customerapi/"
  fineos_client_wscomposer_api_url                    = "https://trn-api.masspfml.fineos.com/integration-services/wscomposer/"
  fineos_client_oauth2_url                            = "https://trn-api.masspfml.fineos.com/oauth2/token"
  fineos_import_employee_updates_input_directory_path = "s3://fin-sompre-data-export/TRN/dataexports"
  fineos_aws_iam_role_arn                             = "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-S0EP9ABIA02Z"
  fineos_aws_iam_role_external_id                     = "8jFBtjr4UA@"
  service_now_base_url                                = "https://savilinxtrain.servicenowservices.com"
  admin_portal_base_url                               = "https://paidleave-admin-training.dfml.eol.mass.gov"
  portal_base_url                                     = "https://paidleave-training.mass.gov"
  enable_application_fraud_check                      = "0"
  release_version                                     = var.release_version
  fineos_v21_upgrade_date                             = "2022-04-04"

  azure_ad_authority_domain = "login.microsoftonline.com"
  azure_ad_client_id        = "ecc75e15-cd60-4e28-b62f-d1bf80e05d4d"
  azure_ad_parent_group     = "TSS-SG-PFML_ADMIN_PORTAL_NON_PROD"
  azure_ad_tenant_id        = "3e861d16-48b7-4a0e-9806-8c04d81b7b2a"

  limit_ssn_fein_max_attempts       = "5"
  claim_status_v2_employer_review   = "0"
  enable_email_change               = "1"
  enable_system_message_translation = "0"

  enable_prepaid_impact_payments = "1"
  enable_sync_payment_preference = "1"
  enable_s3_direct_copy          = "0"
  fineos_is_running_v24          = "1"

  # Med to Bonding
  # @TODO(PFMLPB-24292): Remove feature flag for Med to Bonding BPC
  enable_delayed_submission_of_modifications = "0"

  # @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review
  enable_mark_applications_ready_for_review = "1"

  # @TODO(PFMLPB-24796): Remove enable_re_notification
  enable_re_notification = "0"

  enable_document_upload_optional = "1"

  # PDF API Service Variables
  pdf_api_lb_port               = 3602
  pdf_api_service_app_count     = 1
  pdf_api_service_max_app_count = 3

  # LMG SETTINGS FOR NON-MFA
  lmg_ssm_mfa_state           = "mfa_off"
  lmg_personal_application_id = "b1d56125-75b5-417a-b416-52b45b971825"
  lmg_business_application_id = "8f24d0e5-e309-4857-982c-fd7ddf1957b7"

  # US Bank Client settings
  usbank_client_oauth2_url = "https://stage-apip.prepaidgateway.com/oauth/oauth20/token"
}
