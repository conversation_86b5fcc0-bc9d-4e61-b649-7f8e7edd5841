# IAM User Key Rotation
Automation to stage newly created IAM User Access Keys/Secrets for rotation after 11 months. Also handles old key deactivation (12 months) and deletion (13 months) after an inactivity period.

Note: Only service accounts have new keys staged and old keys deactivated/deleted. Non-service account IAM user creds are only notified via email to perform their own rotation. Service and non-service accounts are differentiated by a list in the Lambda function.

Note: Service accounts used by SES must have their Access Key Secret converted to SMTP credentials. This will eventually be handled by the code, but for now is manual. Follow [SMTP Credentials Convert](https://docs.aws.amazon.com/ses/latest/dg/smtp-credentials.html#smtp-credentials-convert) doc. The following users need this performed:
- pfml-ses-savilinx-sn-prod-user
- pfml-ses-savilinx-sn-test-user
- pfml-ses-third-party-smtp-user

## How it works
AWS Lambda function runs on a daily schedule to check each IAM user's access credentials. If the criteria is met, a new access key and secret is created then staged for PFML Infra team to share with the IAM user's owner for rotation. After the old keys have been rotated, 14 days of key inactivity will trigger the lambda to update the key to an Inactive state. After 30 days, the key is deleted. This ensure's that no active key is permaturely deactivated or deleted.

### AWS services used
| Service | Purpose |
|---------|---------|
| Lambda | Executes the Python code on a weekly schedule |
| Secrets Manager | Stores the Access key ID and Secret securely for Infra to retrieve for the IAM user owner. A resource policy exists on the secret so that only Infra team is able to view the IAM access key ID and Secret | 
| DynamoDB | Acts as a record keeper and stores information on the old key for the Lambda to reference |
SES | Sends email to the identified IAM user's owner on key creation. Also sends monthly reminders if the old key hasn't been rotated yet. Additional recipients are cc'd for visibility. | 

### Guardrails
As stated above, this process does not deactivate or delete access keys that are still in use as this could cause production issues. Deactivation only occurs once the old key hasn't been used in 14 days, and deletion after 30 days only if the key is deactivated. 

While the code is executed weekly, recipients are only emailed every 30 days. DynamoDB has a field tracking date of last email sent. In addition to the IAM user's owner, the following email distributions are CC'd for visibilty:
- <EMAIL>
- <EMAIL>

IAM users are mapped to owners via a dictionary in the Lambda code. This requires the list is updated each time a new user is added. If an expired IAM user credential is found but the user isn't in the dictionary, the email is sent to the Infra team lead so that the real owner can be added to the thread. The CC'd recipients are still present.

## Error handling and alerting
Throughout the code, exceptions are handled gracefully so that individual key errors do not disrupt the other keys. If an exception is raised, a low priority PD ticket is opened for the Infra team to investigate. 

If any unhandled exceptions are encountered, the Lambda has a fall-back CloudWatch alert to notify Infra team of the failure. Infra will manually intervene if necessary to ensure the key is rotated.

## What to do after the automated email is first sent
Infra team will reply on the email thread to facilitate the rotation. This is a manual step. The email should follow the below template:

> Hello,
>
> This is the PFML Infra team following up on the previous automated email to facilitate the key rotation for `<IAM user>`. PFML performs yearly rotation on our service accounts. I will provide the new access key ID and secret over LWD MoveIT so you or a delegate can perform the rotation on your side.
>
> Who should receive the new access key and secret?
> 
> Thank you,  
> `<name>`  
> PFML Infra Team

As stated above, the method for sending the keys is [LWD MoveIT](https://mft.mass.gov), but the individual performing the rotation may not always have a mass.gov email. When that's the case, store the key pair in your mass.gov OneDrive then share the file with the user's email. 

It's recommended that you create a Jira ticket to track the rotations and to annotate any changes to key owners for next time. 