import boto3
from aws_lambda_powertools import Logger
from datetime import timedelta, datetime, timezone
import os
from typing import Literal
import json
import requests

logger = Logger(service="iam_user_key_rotation")

SES = boto3.client("sesv2")
SM = boto3.client("secretsmanager")
IAM = boto3.client("iam")
DYNAMODB = boto3.client("dynamodb")

NON_SERVICE_ACCT_USERS = [
    "roy.mounier",
    "william.cole",
]

USERS_MAP = {
    # Non-service accounts
    "roy.mounier": ["<PERSON><PERSON>@mass.gov"],
    "william.cole": ["<EMAIL>"],
    # Service accounts
    "pfml-ctr-moveit-nonprod": ["<EMAIL>", "<EMAIL>"],
    "pfml-ctr-moveit-prod": ["<EMAIL>", "<EMAIL>"],
    "pfml-department-of-revenue-moveit-nonprod": [
        "Daniel<PERSON><PERSON><PERSON><EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "pfml-department-of-revenue-moveit-prod": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "pfml-idp-nonprod": ["<EMAIL>", "<EMAIL>"],
    "pfml-idp-prod": ["<EMAIL>", "<EMAIL>"],
    "pfml-informatica-nonprod": ["<EMAIL>", "<EMAIL>"],
    "pfml-informatica-prod": ["<EMAIL>", "<EMAIL>"],
    "pfml-pub-moveit-nonprod": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "pfml-pub-moveit-prod": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "pfml-ses-savilinx-sn-prod-user": [
        "<EMAIL>",
        "<EMAIL>",
    ],
    "pfml-ses-savilinx-sn-test-user": [
        "<EMAIL>",
        "<EMAIL>",
    ],
    "pfml-ses-third-party-smtp-user": [
        "<EMAIL>",
        "<EMAIL>",
    ],
}


def get_key_last_used(key=None):
    response = IAM.get_access_key_last_used(AccessKeyId=key)
    return response["AccessKeyLastUsed"].get("LastUsedDate")


def delete_key(context=None, username=None, old_key=None, old_key_create_date=None, old_key_status=None, old_key_last_used=None):
    if old_key_status == "Inactive":
        if (datetime.now(timezone.utc) - old_key_last_used).days >= 30:
            logger.info(f"Key '{old_key}' has not been used in 30 days. Deleting.")
            try:
                IAM.delete_access_key(UserName=username, AccessKeyId=old_key)
                logger.info(f"Key '{old_key}' for user '{username}' has been deleted.")
            except Exception as e:
                logger.error(f"Error deleting key '{old_key}' for user '{username}': {e}")
                post_to_pagerduty(
                    context=context, username=username, key=old_key, error_message=e
                )
            else:
                dynamo_db_insert(
                    context=context,
                    username=username,
                    key=old_key,
                    status="Deleted",
                    action="updated",
                )
        else:
            logger.info(
                f"Key '{old_key}' for user '{username}' has been used in the last 30 days. It cannot be deleted."
            )

    else:
        logger.info(
            f"Key '{old_key}' for user '{username}' is still active. It must be deactivated before deletion."
        )
        deactivate_key(
            context=context,
            username=username,
            old_key=old_key,
            old_key_create_date=old_key_create_date,
            old_key_last_used=old_key_last_used,
            old_key_status=old_key_status,
        )


def deactivate_key(
    context=None,
    username=None,
    old_key=None,
    old_key_create_date=None,
    old_key_last_used=None,
    old_key_status=None,
):
    if (
        datetime.now(timezone.utc) - old_key_last_used
    ).days >= 14 and old_key_status == "Active":
        try:
            IAM.update_access_key(
                UserName=username, AccessKeyId=old_key, Status="Inactive"
            )
            logger.info(f"Key '{old_key}' for user '{username}' has been deactivated.")
        except Exception as e:
            logger.error(
                f"Error deactivating key '{old_key}' for user '{username}': {e}"
            )
            post_to_pagerduty(
                context=context, username=username, key=old_key, error_message=e
            )
        else:
            dynamo_db_insert(
                context=context,
                username=username,
                key=old_key,
                status="Inactive",
                action="updated",
            )
    elif old_key_status == "Active":
        logger.warning(
            f"Key '{old_key}' for user '{username}' has been used in the last 14 days. It cannot be deactivated."
        )
        response = IAM.list_access_keys(UserName=username)
        new_key = next(
            (
                key["AccessKeyId"]
                for key in response["AccessKeyMetadata"]
                if key["AccessKeyId"] != old_key
            ),
            None,
        )
        send_email(
            context=context,
            username=username,
            key=new_key,
            old_key=old_key,
            old_key_create_date=old_key_create_date,
            old_key_last_used=old_key_last_used,
            status=old_key_status,
            caller="reminder",
        )


def create_key(
    context=None,
    username=None,
    old_key=None,
    old_key_create_date=None,
    old_key_status=None,
    old_key_last_used=None,
):
    try:
        response = IAM.create_access_key(UserName=username)
        access_key = response["AccessKey"]["AccessKeyId"]
        secret_key = response["AccessKey"]["SecretAccessKey"]
        logger.info(
            f"Key '{access_key} for user '{username}' has been created. This key will replace key '{old_key}'."
        )
    except Exception as e:
        logger.error(f"Error creating key for user '{username}': {e}")
        post_to_pagerduty(
            context=context, username=username, error_message=e
        )
    else:
        create_secret(
            context=context,
            username=username,
            access_key=access_key,
            secret_key=secret_key,
        )
        dynamo_db_insert(
            context=context,
            username=username,
            key=old_key,
            status=old_key_status,
            action="stored",
        )
        send_email(
            context=context,
            username=username,
            key=access_key,
            old_key=old_key,
            old_key_create_date=old_key_create_date,
            old_key_last_used=old_key_last_used,
            status=old_key_status,
            caller="create_key",
        )


def dynamo_db_insert(
    context,
    username: str,
    key: str,
    status: str,
    action: Literal["stored", "updated"],
    email: bool = False,
):
    item = {
        "Access_Key_ID": {"S": key},
        "Username": {"S": username},
        "Status": {"S": status},
    }

    if email:
        item["Email_Last_Sent"] = {"S": datetime.now(timezone.utc).isoformat()}

    try:
        DYNAMODB.put_item(
            Item=item,
            TableName=os.environ["DYNAMODB_TABLE"],
        )
    except Exception as e:
        logger.error(f"Unable to insert key '{key}' into DynamoDB: {e}")
        post_to_pagerduty(context=context, username=username, key=key, error_message=e)
    else:
        logger.info(
            f"Key '{key}' for user '{username}' has been successfully {action} in DynamoDB."
        )


def dynamodb_get_last_email_date(context=None, key=None, username=None):
    try:
        response = DYNAMODB.get_item(
            TableName=os.environ["DYNAMODB_TABLE"],
            Key={"Access_Key_ID": {"S": key}, "Username": {"S": username}},
        )

        if "Item" not in response or "Email_Last_Sent" not in response["Item"]:
            return True

        email_last_sent = datetime.fromisoformat(
            response["Item"]["Email_Last_Sent"]["S"]
        )
        days_since_last_email = (datetime.now(timezone.utc) - email_last_sent).days

        # Return True if it's been 30 or more days since last email and the status is Active
        return days_since_last_email >= 30 and response["Item"]["Status"] == "Active"

    except Exception as e:
        logger.error(f"Error checking email status for key '{key}': {e}")
        post_to_pagerduty(context=context, username=username, key=key, error_message=e)
        return False


def create_secret(context=None, username=None, access_key=None, secret_key=None):
    secret_name = f"/service/pfml-api/{username}"
    secret_value = {
        "username": username,
        "access_key": access_key,
        "secret_key": secret_key,
    }
    # If you run black format, it will make the policy malformed. Make sure you remove any formatting from the policy.
    resource_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "DenyAllExceptSpecificRoles",
                "Effect": "Deny",
                "Principal": "*",
                "Action": [
                    "secretsmanager:GetSecretValue",
                    "secretsmanager:DescribeSecret"
                ],
                "Resource": "*",
                "Condition": {
                    "StringNotEquals": {
                        "aws:PrincipalArn": [
                            "arn:aws:iam::************:role/aws-reserved/sso.amazonaws.com/AWSReservedSSO_eolwd-pfml-infrastructure-admin_9049548fba1c97b7",
                            "arn:aws:iam::************:role/ADFS-Admin"
                        ]
                    }
                }
            }
        ]
    }

    try:
        SM.create_secret(
            Name=secret_name,
            SecretString=json.dumps(secret_value),
            Tags=[
                {
                    "Key": "purpose",
                    "Value": f"Stores access key/secret for {username}. Only accessible by specific roles.",
                },
                {
                    "Key": "createdby",
                    "Value": f"massgov_pfml_iam_user_key_rotation",
                },
                {
                    "Key": "agency",
                    "Value": "dfml",
                },
                {
                    "Key": "application",
                    "Value": "pfml",
                },
                {
                    "Key": "businessowner",
                    "Value": "<EMAIL>",
                },
                {
                    "Key": "itowner",
                    "Value": "<EMAIL>",
                },
                {
                    "Key": "secretariat",
                    "Value": "eolwd",
                },
            ],
        )
        logger.info(
            f"Secret '{secret_name}' created for user {username} for access key '{access_key}'."
        )
    except SM.exceptions.ResourceExistsException:
        SM.update_secret(SecretId=secret_name, SecretString=json.dumps(secret_value))
        logger.info(
            f"Secret '{secret_name}' updated for user {username} for access key '{access_key}'."
        )
    except Exception as e:
        logger.error(
            f"Error creating secret for user '{username} and access key '{access_key}'': {e}"
        )
        post_to_pagerduty(
            context=context, username=username, key=access_key, error_message=e
        )
    else:
        SM.put_resource_policy(
            SecretId=secret_name,
            ResourcePolicy=json.dumps(resource_policy),
        )
        return secret_name


def send_email(
    context=None,
    username=None,
    key=None,
    old_key=None,
    old_key_create_date=None,
    old_key_last_used=None,
    status=None,
    caller=None,
    cc_recipients=["<EMAIL>", "<EMAIL>"],
):
    old_key_last_used_trimmed = (
        str(old_key_last_used).split()[0] if old_key_last_used else "Never"
    )
    old_key_create_date_trimmed = str(old_key_create_date).split()[0]
    old_key_exp_date = str(old_key_create_date + timedelta(days=365)).split()[0]

    access_key_created = f"""
Hello!

If you're receiving this email, it's because you have been identified as an owner of expiring/expired PFML IAM user access key '{old_key}' for user '{username}'. A new key has been created and is ready to replace it. PFML Infra team will be following up on this email to coordinate the rotation.

This is an automated email. For any questions or help, reply here <NAME_EMAIL>

- Username: {username}
- Old Access Key ID: {old_key}
- Old Access Key status: {status}
- Old Access Key last used: {old_key_last_used_trimmed}
- Old Access Key creation date: {old_key_create_date_trimmed}
- Old Access Key expiration date: {old_key_exp_date}


Respectfully,
PFML Infra Team
"""

    reminder = f"""
Hello!

If you're receiving this email, it's because you have been identified as an owner of an expired PFML IAM user access key. Key '{old_key}' for user '{username}' has exceeded it's intended 1yr lifespan. We attempted to deactivate it, but the key was recently used. If no one from the PFML Infra team has reached out to you, feel free to reply to this email and we'll promptly respond. 

You will continue to receive these emails until the old key hasn't been used in 14 days.
If you've already rotated the keys, please ignore this email. Once the old key hasn't been used in 14 days, it will be deactivated then deleted after 30 days of inactivity.

This is an automated email. For any questions or help, reply here <NAME_EMAIL>

- Username: {username}
- Old Access Key ID: {old_key}
- Old Access Key status: {status}
- Old Access Key last used: {old_key_last_used_trimmed}
- Old Access Key creation date: {old_key_create_date_trimmed}
- Old Access Key expiration date: {old_key_exp_date}

Respectfully,
PFML Infra Team
"""

    non_service_acct = f"""
Hello!

If you're receiving this email, it's because you have been identified as the owner of expiring/expired PFML IAM user access key '{old_key}' for user '{username}'. As this is not a service account, we won't be rotating it. Please manually create a new key then deactivate and delete the old key. You will continue to receive these emails until the old key has been deleted.

This is an automated email. For any questions or help, reply here <NAME_EMAIL>

- Username: {username}
- AWS sign-in URL: https://coma.awsapps.com/start/#/
- AWS account name: massit-pfml
- AWS account ID: ************
- Old Access Key ID: {old_key}
- Old Access Key status: {status}
- Old Access Key last used: {old_key_last_used_trimmed}
- Old Access Key creation date: {old_key_create_date_trimmed}
- Old Access Key expiration date: {old_key_exp_date}

Respectfully,
PFML Infra Team
"""

    subject = f"PFML IAM Service Acct Rotation - {username}"
    if caller == "create_key":
        body = access_key_created
    elif not dynamodb_get_last_email_date(
        context=context, username=username, key=old_key
    ):
        logger.info(
            f"Email reminder for key '{old_key}' for user '{username}' has already been sent in the last 30 days. No email will be sent."
        )
        return
    elif caller == "non_service_acct":
        subject = f"PFML IAM User Rotation - {username}"
        body = non_service_acct
    else:
        body = reminder
    try:
        SES.send_email(
            FromEmailAddress="<EMAIL>",
            Destination={
                "ToAddresses": USERS_MAP.get(
                    username,
                    [
                        "<EMAIL>"
                    ],  # Send to Infra team lead if username is not in the map
                ),
                "CcAddresses": cc_recipients,
            },
            Content={
                "Simple": {
                    "Subject": {"Data": subject},
                    "Body": {"Text": {"Data": body}},
                }
            },
        )
    except Exception as e:
        logger.error(f"Error sending email for key '{key}' for user '{username}': {e}")
        post_to_pagerduty(context=context, username=username, key=key, error_message=e)
    else:
        dynamo_db_insert(
            context=context,
            username=username,
            key=old_key,
            status=status,
            action=f"stored" if caller == "create_key" else "updated",
            email=True,
        )


def post_to_pagerduty(context=None, username=None, key=None, error_message=None):
    decode_stream = (
        context.log_stream_name.replace("$", "$2524")
        .replace("[", "$255B")
        .replace("]", "$255D")
        .replace("/", "$252F")
    )
    lambda_url = f"https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:log-groups/log-group/$252Faws$252Flambda$252Fmassgov_pfml_iam_user_key_rotation/log-events/{decode_stream}"
    try:
        response = requests.post(
            url="https://events.pagerduty.com/v2/enqueue",
            headers={"Content-Type": "application/json"},
            json={
                "routing_key": os.environ["PD_INFRA_LOW_PRI"],
                "event_action": "trigger",
                "dedup_key": key,
                "payload": {
                    "summary": "IAM User Key Rotation Runtime Error",
                    "severity": "warning",
                    "source": "massgov_pfml_iam_user_key_rotation",
                    "custom_details": {
                        "description": f"""
User: {username}
Access Key: {key}
Error: {error_message}
Link to Lambda logs: {lambda_url}
                        """,
                    },
                },
            },
        )
    except Exception as e:
        logger.error(e)
    else:
        logger.info(response.text)


def check_for_second_key(username=None):
    response = IAM.list_access_keys(UserName=username)
    key_count = len(response["AccessKeyMetadata"])
    return key_count == 2


def handler(event=None, context=None):
    users = IAM.list_users()
    for user in users["Users"]:
        keys = IAM.list_access_keys(UserName=user["UserName"])
        for key in keys["AccessKeyMetadata"]:
            username = key["UserName"]
            key_id = key["AccessKeyId"]
            status = key["Status"]
            create_date = key["CreateDate"]
            check = get_key_last_used(key=key_id)
            if username not in NON_SERVICE_ACCT_USERS:
                if (
                    datetime.now(timezone.utc) - create_date
                ).days >= 396 and check_for_second_key(
                    username=username
                ):  # 13 months
                    delete_key(
                        context=context,
                        username=username,
                        old_key=key_id,
                        old_key_create_date=create_date,
                        old_key_status=status,
                        old_key_last_used=check,
                    )
                elif (
                    datetime.now(timezone.utc) - create_date
                ).days >= 365 and check_for_second_key(
                    username=username
                ):  # 12 months
                    deactivate_key(
                        context=context,
                        username=username,
                        old_key=key_id,
                        old_key_create_date=create_date,
                        old_key_last_used=check,
                        old_key_status=status,
                    )
                elif (
                    datetime.now(timezone.utc) - create_date
                ).days >= 334 and not check_for_second_key(
                    username=username
                ):  # 11 months
                    create_key(
                        context=context,
                        username=username,
                        old_key=key_id,
                        old_key_create_date=create_date,
                        old_key_status=status,
                        old_key_last_used=check,
                    )
            elif (datetime.now(timezone.utc) - create_date).days >= 334:  # 11 months
                send_email(
                    context=context,
                    username=username,
                    old_key=key_id,
                    old_key_create_date=create_date,
                    status=status,
                    old_key_last_used=check,
                    caller="non_service_acct",
                )


if __name__ == "__main__":
    handler()
