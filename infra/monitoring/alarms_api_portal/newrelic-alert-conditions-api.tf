
#################################################
### Alert Conditions
#################################################

# ----------------------------------------------------------------------------------------------------------------------
# Alerts relating to the API's generic performance metrics

resource "newrelic_nrql_alert_condition" "api_error_rate" {
  # WARN: error rate above 5% in any five-minute period, with at least 10 unique users.
  # CRIT: error rate above 10% in two five-minute periods, with at least 10 unique users in each period.
  #
  # These should be tuned down once we can better distinguish
  # certain types of errors.
  name               = "API error rate too high"
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.high_priority_lwd_it_support_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type               = "static"
  enabled            = true
  aggregation_window = 300 # 5-minute window
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  fill_option        = "none"
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#API-error-rate-too-high"

  nrql {
    # Calculate error percentage. Clamping is applied to ensure that any data points with less than 10 unique users
    # is removed from the calculation and interpreted as 0%. This is a workaround to prevent periods of low activity
    # (e.g. late night) from triggering the alarm and waking people up.
    #
    # Ignoring error.message value.
    # A generic error message is logged for all errors => Message removed by New Relic 'strip_exception_messages' setting
    # https://lwd.atlassian.net/browse/PFMLPB-16003
    #
    # This keeps the error rate signal clean so we can catch new frequent issues.
    #
    # Also ignore the following transactions:
    # - push_db (This is a before_request method that New Relic breaks out as a separate transaction)
    # - 503 errors (captured in a separate alarm with a higher threshold)
    #
    # Note that 504 errors are not mentioned here because 504s are not reflected in Transaction/TransactionError.
    # 504s come from the API Gateway, which sit in front of the API servers.
    #
    query = <<-NRQL
      SELECT filter(
        count(error.message),
        WHERE NOT error.class = 'massgov.pfml.fineos.exception:FINEOSFatalUnavailable'
      ) * 100 * clamp_max(floor(uniqueCount(current_user.user_id) / 10), 1) / uniqueCount(traceId)
      FROM Transaction, TransactionError
      WHERE appName='PFML-API-${upper(var.environment_name)}'
        AND (name IS NULL or name NOT LIKE '%push_db')
        AND numeric(response.status) != 503
        AND (transactionName LIKE 'WebTransaction%' or transactionType = 'Web')
    NRQL
  }

  violation_time_limit_seconds = 86400 # 24 hours

  warning {
    threshold_duration    = 300 # five minutes
    threshold             = 5   # units: percentage
    operator              = "above"
    threshold_occurrences = "at_least_once"
  }

  critical {
    threshold_duration    = 600 # ten minutes (2 windows)
    threshold             = 10  # units: percentage
    operator              = "above"
    threshold_occurrences = "all"
  }
}

resource "newrelic_nrql_alert_condition" "api_network_error_rate" {
  # WARN: error rate above 10% in any five-minute period
  # CRIT: error rate above 20% in two five-minute periods
  #
  name               = "API high rate of 503 network errors"
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.high_priority_lwd_it_support_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type               = "static"
  enabled            = true
  aggregation_window = 300 # 5-minute window
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#API-high-rate-of-503-network-errors"

  nrql {
    query = <<-NRQL
      SELECT filter(
          count(*),
          WHERE numeric(response.status) = 503
        ) * 100 * clamp_max(floor(uniqueCount(current_user.user_id) / 10), 1) / uniqueCount(traceId)
      FROM Transaction
      WHERE appName='PFML-API-${upper(var.environment_name)}'
        AND name NOT LIKE '%push_db'
        AND transactionType = 'Web'
    NRQL
  }

  violation_time_limit_seconds = 86400 # 24 hours

  warning {
    threshold_duration    = 300 # five minutes
    threshold             = 10  # units: percentage
    operator              = "above"
    threshold_occurrences = "at_least_once"
  }

  critical {
    threshold_duration    = 600 # ten minutes (2 windows)
    threshold             = 20  # units: percentage
    operator              = "above"
    threshold_occurrences = "all"
  }
}

resource "newrelic_nrql_alert_condition" "api_network_error_rate_machine_user_pfml_crm" {
  # WARN: error rate above 10% in any five-minute period
  # CRIT: error rate above 20% in two five-minute periods
  #
  name               = "API error rate too high for PFML CRM/ServiceNOW machine users"
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type               = "static"
  enabled            = true
  aggregation_window = 300 # 5-minute window
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  runbook_url        = "API error rate too high for PFML CRM/ServiceNOW machine users"

  nrql {
    query = <<-NRQL
      SELECT filter(
          count(*),
          WHERE response.status NOT LIKE '2%'
          AND current_user.has_role_PFML_CRM is TRUE
        ) * 100 * clamp_max(floor(uniqueCount(mass_pfml_agent_id) / 10), 1) / uniqueCount(traceId)
      FROM Transaction, TransactionError
      WHERE appName='PFML-API-${upper(var.environment_name)}'
        AND (name IS NULL or name NOT LIKE '%push_db')
        AND (transactionName LIKE 'WebTransaction%' or transactionType = 'Web')
    NRQL
  }

  violation_time_limit_seconds = 86400 # 24 hours

  warning {
    threshold_duration    = 300 # five minutes
    threshold             = 10  # units: percentage
    operator              = "above"
    threshold_occurrences = "at_least_once"
  }

  critical {
    threshold_duration    = 600 # ten minutes (2 windows)
    threshold             = 20  # units: percentage
    operator              = "above"
    threshold_occurrences = "all"
  }
}

resource "newrelic_nrql_alert_condition" "api_network_error_rate_machine_user_fineos" {
  # WARN: error rate above 10% in any five-minute period
  # CRIT: error rate above 20% in two five-minute periods
  #
  # /v1/rmv-check is handled separately in `api_rmv_check_error_rate` since it is known to have high frequency
  name               = "API error rate too high for FINEOS machine users"
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type               = "static"
  enabled            = true
  aggregation_window = 300 # 5-minute window
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#API-error-rate-too-high-for-FINEOS-machine-users"

  nrql {
    query = <<-NRQL
      SELECT filter(
          count(*),
          WHERE response.status NOT LIKE '2%'
          AND transactionType = 'Web'
          AND current_user.has_role_Fineos is TRUE
          AND NOT (request.uri = '/v1/rmv-check' AND numeric(response.status) = 400)
        ) * 100 * clamp_max(floor(uniqueCount(request_id) / 10), 1) / uniqueCount(traceId)
      FROM Transaction
      WHERE appName='PFML-API-${upper(var.environment_name)}'
        AND (name IS NULL or name NOT LIKE '%push_db')
        AND (transactionName LIKE 'WebTransaction%' or transactionType = 'Web')
    NRQL
  }

  violation_time_limit_seconds = 86400 # 24 hours

  warning {
    threshold_duration    = 300 # five minutes
    threshold             = 10  # units: percentage
    operator              = "above"
    threshold_occurrences = "at_least_once"
  }

  critical {
    threshold_duration    = 600 # ten minutes (2 windows)
    threshold             = 20  # units: percentage
    operator              = "above"
    threshold_occurrences = "all"
  }
}

resource "newrelic_nrql_alert_condition" "api_rmv_check_error_rate" {
  # WARN: error rate above 10% in any five-minute period
  # CRIT: error rate above 20% in two five-minute periods
  #
  name               = "API high rate of 400 network errors on the /rmv-check endpoint"
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type               = "static"
  enabled            = false
  aggregation_window = 300 # 5-minute window
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#API-high-rate-of-400-network-errors-on-the-%2Frmv-check-endpoint"

  nrql {
    query = <<-NRQL
      SELECT filter(
          count(*),
          WHERE numeric(response.status) = 400
          AND request.uri = '/v1/rmv-check'
          AND current_user.has_role_Fineos is TRUE
        ) * 100 * clamp_max(floor(uniqueCount(request_id) / 10), 1) / uniqueCount(traceId)
      FROM Transaction
      WHERE appName='PFML-API-${upper(var.environment_name)}'
        AND (name IS NULL or name NOT LIKE '%push_db')
        AND (transactionName LIKE 'WebTransaction%' or transactionType = 'Web')
    NRQL
  }

  violation_time_limit_seconds = 86400 # 24 hours

  warning {
    threshold_duration    = 300 # five minutes
    threshold             = 20  # units: percentage
    operator              = "above"
    threshold_occurrences = "at_least_once"
  }

  critical {
    threshold_duration    = 600 # ten minutes (2 windows)
    threshold             = 30  # units: percentage
    operator              = "above"
    threshold_occurrences = "all"
  }
}

resource "newrelic_alert_condition" "api_response_time" {
  # WARN: Average response time above 750ms for at least ten minutes
  # CRIT: Average response time above 2½sec for at least ten minutes
  policy_id       = (var.environment_name == "prod") ? newrelic_alert_policy.high_priority_lwd_it_support_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name            = "API response time too high"
  type            = "apm_app_metric"
  entities        = [data.newrelic_entity.pfml-api.application_id]
  metric          = "response_time_web"
  condition_scope = "application"
  runbook_url     = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#API-response-time-too-high"

  # Disabled due to frequent overnight alerts, until PSD-7473 is resolved.
  enabled = false

  term {
    priority      = "warning"
    time_function = "all" # e.g. "for at least..."
    duration      = 10    # units: minutes
    operator      = "above"
    threshold     = 0.75 # units: seconds
  }

  term {
    priority      = "critical"
    time_function = "all" # e.g. "for at least..."
    duration      = 10    # units: minutes
    operator      = "above"
    threshold     = 2.5 # units: seconds
  }
}

resource "newrelic_nrql_alert_condition" "get_claims_response_time" {
  # WARN: 95th percentile response time for GET /claims queries is > 4 seconds for any 10 minute period
  # CRIT: 95th percentile response time for GET /claims queries is > 7 seconds for any 10-minute period
  policy_id                    = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name                         = "GET Claims response time too high (${upper(var.environment_name)})"
  aggregation_window           = 600 # units: seconds
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  violation_time_limit_seconds = 86400 # 24 hours
  fill_option                  = "last_value"
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#GET-Claims-response-time-too-high"

  nrql {
    query = "SELECT percentile(duration, 95) FROM Transaction WHERE appName = 'PFML-API-${upper(var.environment_name)}' AND request.uri LIKE '/v1/claims' AND request.method = 'GET'"
  }

  warning {
    threshold_occurrences = "ALL"
    threshold_duration    = 1800 # units: seconds
    operator              = "above"
    threshold             = 4 # units: seconds
  }

  critical {
    threshold_occurrences = "ALL"
    threshold_duration    = 1800 # units: seconds
    operator              = "above"
    threshold             = 7 # units: seconds
  }
}

resource "newrelic_alert_muting_rule" "get_claims_response_time_muting_rule" {
  name        = "GET Claims response time too high (${upper(var.environment_name)}) Muting Rule"
  description = "A muting rule for GET Claims response time too high Alert."
  enabled     = true
  condition {
    conditions {
      attribute = "conditionId"
      operator  = "EQUALS"
      values    = [split(":", newrelic_nrql_alert_condition.get_claims_response_time.id)[1]]
    }
    operator = "AND"
  }
  schedule {
    start_time         = "2024-08-30T02:00:00"
    end_time           = "2024-08-30T07:00:00"
    time_zone          = "America/New_York"
    repeat             = "WEEKLY"
    weekly_repeat_days = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"]
  }
}

resource "newrelic_nrql_alert_condition" "employee_search_snow_response_time" {
  # WARN: 95th percentile response time for POST /employees/search queries is > 1 second for any 15 minute period
  # CRIT: 95th percentile response time for POST /employees/search queries is > 2 seconds for any 15-minute period
  policy_id                    = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name                         = "POST Employee Search response time too high (${upper(var.environment_name)})"
  aggregation_window           = 600 # units: seconds
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  violation_time_limit_seconds = 86400 # 24 hours
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#POST-Employee-Search-response-time-too-high-(PROD)"

  nrql {
    query = "SELECT percentile(duration, 95) FROM Transaction WHERE appName = 'PFML-API-${upper(var.environment_name)}' AND request.uri LIKE '/v1/employees/search' AND request.method = 'POST'"
  }

  warning {
    threshold_occurrences = "ALL"
    threshold_duration    = 1800 # units: seconds
    operator              = "above"
    threshold             = 1 # units: seconds
  }

  critical {
    threshold_occurrences = "ALL"
    threshold_duration    = 1800 # units: seconds
    operator              = "above"
    threshold             = 2 # units: seconds
  }
}

resource "newrelic_nrql_alert_condition" "verifications_response_time" {
  # WARN: 95th percentile response time for POST /verifications queries is > 8 second for any 15 minute period
  # CRIT: 95th percentile response time for POST /verifications queries is > 15 seconds for any 15-minute period
  policy_id                    = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name                         = "POST Verifications response time too high (${upper(var.environment_name)})"
  aggregation_window           = 60 # units: seconds
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  violation_time_limit_seconds = 86400 # 24 hours
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#POST-Verifications-response-time-too-high"

  nrql {
    query = "SELECT percentile(duration, 95) FROM Transaction WHERE appName = 'PFML-API-${upper(var.environment_name)}' AND request.uri LIKE '/v1/employers/verifications' AND request.method = 'POST'"
  }

  warning {
    threshold_occurrences = "ALL"
    threshold_duration    = 900 # units: seconds
    operator              = "above"
    threshold             = 8 # units: seconds
  }

  critical {
    threshold_occurrences = "ALL"
    threshold_duration    = 900 # units: seconds
    operator              = "above"
    threshold             = 15 # units: seconds
  }
}
# ----------------------------------------------------------------------------------------------------------------------
# Alerts relating to the API's RDS database

resource "newrelic_nrql_alert_condition" "rds_high_cpu_utilization" {
  # WARN: CPU Utilization above 75% for at least 5 minutes
  # CRIT: CPU Utilization above 90% for at least 5 minutes
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.high_priority_lwd_it_support_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name               = "RDS CPU Utilization too high"
  type               = "static"
  enabled            = true
  aggregation_window = 60
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/PSD/pages/**********/Alerts+Migration+Plan#RDS-CPU-Utilization-too-high"


  nrql {
    query = "SELECT percentile(`provider.cpuUtilization.total`, 99) from DatastoreSample where provider = 'RdsDbInstance' and displayName = data.aws_db_instance.default.db_instance_identifier"
  }

  violation_time_limit_seconds = 86400 # 24 hours

  warning {
    threshold_duration    = 300
    threshold             = 75
    operator              = "above"
    threshold_occurrences = "ALL"
  }

  critical {
    threshold_duration    = 300
    threshold             = 90
    operator              = "above"
    threshold_occurrences = "ALL"
  }
}

resource "newrelic_nrql_alert_condition" "rds_low_storage_space" {
  # WARN: RDS storage space is below 30% for at least 5 minutes
  # CRIT: RDS storage space is below 20% for at least 5 minutes
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.high_priority_lwd_it_support_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name               = "RDS instance has <= 20% free storage space"
  type               = "static"
  enabled            = true
  aggregation_window = 300
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#RDS-instance-has-%3C%3D-20%25-free-storage-space"

  nrql {
    query = "SELECT average(provider.freeStorageSpaceBytes.Maximum) / average(provider.allocatedStorageBytes ) * 100 FROM DatastoreSample where provider = 'RdsDbInstance' and displayName = data.aws_db_instance.default.db_instance_identifier"
  }

  violation_time_limit_seconds = 86400 # 24 hours

  warning {
    threshold_duration    = 300
    threshold             = 30
    operator              = "below"
    threshold_occurrences = "ALL"
  }

  critical {
    threshold_duration    = 300
    threshold             = 20
    operator              = "below"
    threshold_occurrences = "ALL"
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# Alerts relating to abnormal traffic against the /notifications endpoint, where FINEOS POSTs new claims

resource "newrelic_nrql_alert_condition" "notifications_endpoint_infinite_email_spam" {
  # CRIT: ≥ 12 transactions to this endpoint in 15 minutes, for the same absence case ID and recipient type
  # Traffic surges have happened in the past for the same absence case ID, but different recipient types

  description = <<-TXT
    There's too much traffic on the notifications endpoint for a specific absence case ID & specific type of recipient.
    This usually means FINEOS is stuck in an infinite loop and is sending huge quantities of emails to a real human.
    This can also mean E2E testing traffic against nonprod is producing a false positive (see INFRA-637).
    This alarm SHOULD never go off in prod, now that FINEOS has released their 6/26/2021 service pack.
  TXT
  name        = "(${upper(var.environment_name)}) Notifications endpoint spam alert"
  policy_id   = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type        = "static"
  enabled     = true
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Notifications-endpoint-spam-alert"

  aggregation_window           = 900 # 15 minutes
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  violation_time_limit_seconds = 172800 # 72 hours; longest surge yet recorded lasted about four days

  nrql {
    query = <<-NRQL
      SELECT count(*) FROM Transaction
      WHERE appName = 'PFML-API-${upper(var.environment_name)}' AND request.uri = '/v1/notifications'
      FACET notification.absence_case_id, notification.recipient_type
    NRQL
  }

  critical {
    threshold             = 11  # to emulate a 'greater than or equal to 12' threshold
    threshold_duration    = 900 # 15 minutes
    operator              = "above"
    threshold_occurrences = "all"
  }
}

resource "newrelic_nrql_alert_condition" "notifications_error_rate" {
  # WARN: count errors for POST /notifications queries is > 3 errors for any 15 minute period
  # CRIT: count errors for POST /notifications queries is >  5 errors for any 15-minute period
  policy_id                    = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name                         = "POST Notifications error rate too high (${upper(var.environment_name)})"
  aggregation_window           = 900 # 15 minutes
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  fill_option                  = "none"
  violation_time_limit_seconds = 86400 # 24 hours
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#POST-Notifications-error-rate-too-high"

  nrql {
    query = "SELECT count(*) FROM Transaction WHERE request.uri LIKE '/v1/notifications' AND response.status != '201' AND request.method = 'POST' AND appName = 'PFML-API-${upper(var.environment_name)}'"
  }

  warning {
    threshold_occurrences = "ALL"
    threshold_duration    = 900 # 15 minutes
    operator              = "above"
    threshold             = 3 # units: count
  }

  critical {
    threshold_occurrences = "ALL"
    threshold_duration    = 900 # 15 minutes
    operator              = "above"
    threshold             = 5 # units: count
  }
}
# ----------------------------------------------------------------------------------------------------------------------
# Alarms relating to problems in the PUB delegated payments pipeline

module "pub_delegated_payments_errors" {
  count  = (var.environment_name == "prod") ? 1 : 0
  source = "../newrelic_single_error_alarm"

  enabled     = true
  name        = "Errors encountered by a PUB delegated payments ECS task (${var.environment_name})"
  policy_id   = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Errors-encountered-by-a-PUB-delegated-payments-ECS-task"

  nrql = <<-NRQL
    SELECT count(*) FROM Log
    WHERE aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
      AND aws.logStream LIKE '${var.environment_name}/pub-payments%'
      AND levelname = 'ERROR'
  NRQL
}

module "pub_delegated_payments_ecs_task_failures" {
  source    = "../newrelic_single_error_alarm"
  policy_id = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id

  enabled     = true
  name        = "PUB delegated payments ECS task failed (${var.environment_name})"
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#PUB-delegated-payments-ECS-task-failed"

  nrql = <<-NRQL
    SELECT count(*) FROM Log
    WHERE aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
      AND aws.logStream LIKE '${var.environment_name}/pub-payments%'
      AND message LIKE 'Traceback%'
  NRQL
}

# ----------------------------------------------------------------------------------------------------------------------
# Alarms relating to problems in the IAWW processing task

module "fineos_import_iaww_errors" {
  count  = (var.environment_name == "prod") ? 1 : 0
  source = "../newrelic_single_error_alarm"

  enabled     = true
  name        = "Errors encountered by a Fineos import IAWW ECS task"
  policy_id   = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Errors-encountered-by-a-Fineos-import-IAWW-ECS-task"

  nrql = <<-NRQL
    SELECT count(*) FROM Log
    WHERE aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
      AND aws.logStream LIKE '${var.environment_name}/fineos-import-iaww%'
      AND levelname = 'ERROR'
  NRQL
}

module "fineos_import_iaww_ecs_task_failures" {
  source = "../newrelic_single_error_alarm"

  policy_id   = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  enabled     = true
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Fineos-import-IAWW-ECS-task-failed"
  name        = "Fineos import IAWW ECS task failed"

  nrql = <<-NRQL
    SELECT count(*) FROM Log
    WHERE aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
      AND aws.logStream LIKE '${var.environment_name}/fineos-import-iaww%'
      AND message LIKE 'Traceback%'
  NRQL
}

# ----------------------------------------------------------------------------------------------------------------------
# Alarms relating to problems in the Sync FINEOS Extracts to PFML Models task

module "sync_fineos_extracts_to_pfml_models_ecs_task_failures" {
  source = "../newrelic_single_error_alarm"

  policy_id   = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  enabled     = true
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Sync-FINEOS-extracts-to-PFML-models-ECS-task-failed"
  name        = "Sync FINEOS extracts to PFML models ECS task failed"
  nrql        = <<-NRQL
    SELECT count(*) FROM Log
    WHERE aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
      AND aws.logStream LIKE '${var.environment_name}/sync-fineos-extracts-to-pfml-models%'
      AND message LIKE 'Traceback%'
  NRQL
}

# ------------------------------------------------------------------------------------------------------------------------------

resource "newrelic_nrql_alert_condition" "unsuccessful_register_leave_admin_job" {
  # WARN: Register Leave Admin job has only run 2 times in the past hour
  # CRIT: Register Leave Admin job has not run in the past hour
  name               = "Leave admin registration in FINEOS is failing in ${upper(var.environment_name)}"
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type               = "static"
  fill_option        = "last_value"
  enabled            = true
  aggregation_window = 300
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Leave-admin-registration-in-FINEOS-is-failing-in-PROD"

  nrql {
    query = <<-NRQL
    SELECT count(*) AS 'Successful Job completion' FROM Log WHERE message = 'Completed FINEOS Leave Admin Creation Script'
    AND aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
    NRQL
  }

  violation_time_limit_seconds = 86400 # one day

  warning {
    threshold_duration    = 1800 # thirty minutes
    threshold             = 1    # register leave admin job ran only twice in an hour
    operator              = "below"
    threshold_occurrences = "all"
  }

  critical {
    threshold_duration    = 3600 # sixty minutes
    threshold             = 1    # register leave admin job didn't run at all in an hour
    operator              = "below"
    threshold_occurrences = "all"
  }
}

# ------------------------------------------------------------------------------------------------------------------------------

resource "newrelic_nrql_alert_condition" "unprocessed_leave_admin_records" {
  # WARN: Register Leave Admin job has left unprocessed records twice
  # CRIT: Register Leave Admin job has left unprocessed records four times
  name               = "Leave admin registration in FINEOS did not process all records in ${upper(var.environment_name)}"
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type               = "static"
  fill_option        = "last_value"
  enabled            = true
  aggregation_window = 300
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Leave-admin-registration-in-FINEOS-did-not-process-all-records-in-PROD"

  nrql {
    query = <<-NRQL
    SELECT COUNT(*) as 'Job runs with unprocessed records' FROM Log WHERE message LIKE '%Leave admin records left unprocessed%' AND aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks' AND numeric(`Left unprocessed`) > 0
    NRQL
  }

  violation_time_limit_seconds = 86400 # one day

  warning {
    threshold_duration    = 4500 # seventy-five minutes, job runs every 15 minutes, should account for ~4-5 runs
    threshold             = 1    # more than 1 of the past 4-5 runs has left unprocessed records
    operator              = "above"
    threshold_occurrences = "at_least_once"
  }

  critical {
    threshold_duration    = 4500 # seventy-five minutes, job runs every 15 minutes, should account for ~4-5 runs
    threshold             = 3    # 4 of the past 4-5 runs have left unprocessed records
    operator              = "above"
    threshold_occurrences = "at_least_once"
  }
}

# ------------------------------------------------------------------------------------------------------------------------------

module "newrelic_alert_info_request-errors" {
  source = "../newrelic_baseline_error_rate"

  policy_id   = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Info-request-error-rate-too-high"
  name        = "Info request error rate too high"
  query       = <<-NRQL
    SELECT percentage(
            COUNT(*), WHERE status_code != '200' 
          ) FROM Log
          WHERE method in ('GET', 'PATCH') AND path LIKE '/v1/employers/claims/%/review' and aws.logGroup like '%${var.environment_name}' and status_code IS NOT NULL
    NRQL
}

# ----------------------------------------------------------------------------------------------------------------------
# Alarms related to unexpected startup issues

# module "unexpected_import_error" {
#   source = "../newrelic_single_error_alarm"

#   enabled   = true
#   name      = "Unexpected import error"
#   policy_id = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_api_alerts.id : newrelic_alert_policy.api_alerts.id
#   runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Unexpected-import-error"

#   nrql = <<-NRQL
#     SELECT count(*) FROM Log
#     WHERE message LIKE '%Unable to import module%'
#       AND aws.logGroup LIKE '%${var.environment_name}%'
#   NRQL
# }

# ----------------------------------------------------------------------------------------------------------------------
# Alarms relating to problems with appeal intake PDF generation

resource "newrelic_nrql_alert_condition" "failed_appeal_intake_pdf_generation" {
  # WARN: Generate Appeal Intake PDF job has left 2 or more unprocessed appeals in the past 1-2 runs
  # CRIT: Generate Appeal Intake PDF job has left 1 or more unprocessed appeals in the past 1-2 runs
  name               = "Generate Appeal Intake PDF task did not process all appeals in ${upper(var.environment_name)}"
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type               = "static"
  fill_option        = "last_value"
  enabled            = true
  aggregation_window = 3600         # 60 minutes
  aggregation_delay  = 120          # default
  aggregation_method = "event_flow" # default
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Generate-Appeal-Intake-PDF-task-did-not-process-all-appeals-in-PROD"

  nrql {
    query = <<-NRQL
    SELECT UNIQUECOUNT(aws.logStream) as 'Job runs with unprocessed appeals' FROM Log WHERE message LIKE '%generate_intake_pdf - Failed to process appeal%' AND aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
    NRQL
  }

  violation_time_limit_seconds = 86400 # one day

  warning {
    threshold_duration    = 3600 # sixty minutes (1 aggregation window, 2 possible ECS task runs)
    threshold             = 1    # 2 of the past 1-2 runs have left unprocessed records
    operator              = "above"
    threshold_occurrences = "all"
  }

  critical {
    threshold_duration    = 3600 # sixty minutes (1 aggregation window, 2 possible ECS task runs)
    threshold             = 0    # 1-2 of the past 1-2 runs have left unprocessed records
    operator              = "above"
    threshold_occurrences = "all"
  }
}

# ------------------------------------------------------------------------------------------------------------------------------

resource "newrelic_nrql_alert_condition" "unsuccessful_appeal_intake_pdf_job" {
  # WARN: Generate Appeal Intake PDF job has only run 2 times in the past two hours
  # CRIT: Generate Appeal Intake PDF job has not run in the past hour
  name        = "Generate Appeal Intake PDF task has not run in ${upper(var.environment_name)}"
  policy_id   = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type        = "static"
  fill_option = "last_value"
  enabled     = true
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Generate-Appeal-Intake-PDF-task-has-not-run-in-PROD"

  aggregation_window = 1800 # 30 minutes
  aggregation_method = "event_flow"
  aggregation_delay  = 120

  nrql {
    query = <<-NRQL
    SELECT count(*) AS 'Successful Job completion' FROM Log WHERE message = '%generate_intake_pdf - Finished processing appeals%'
    AND aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
    NRQL
  }

  violation_time_limit_seconds = 86400 # one day

  warning {
    threshold_duration    = 7200 # two hours (4 aggregation windows, 4 possible ECS task runs)
    threshold             = 3    # generate intake pdf job ran only twice in two hours
    operator              = "below"
    threshold_occurrences = "all"
  }

  critical {
    threshold_duration    = 3600 # sixty minutes (2 aggregation windows, 2 possible ECS task runs)
    threshold             = 1    # generate intake pdf job didn't run at all in an hour
    operator              = "below"
    threshold_occurrences = "all"
  }
}

# ------------------------------------------------------------------------------------------------------------------------------

resource "newrelic_nrql_alert_condition" "user_not_found_claim_type" {
  # CRIT: User Not Found Claim type does not match application leave reason.
  #
  # Alert on every occurrence.
  #
  name               = "User Not Found Claim type does not match application leave reason"
  policy_id          = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  type               = "static"
  enabled            = true
  aggregation_window = 600 # 10-minute window
  aggregation_method = "event_flow"
  aggregation_delay  = 120
  fill_option        = "none"
  runbook_url        = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#User-Not-Found-Claim-type-does-not-match-application-leave-reason"

  nrql {
    # Claim type does not match application leave reason
    query = <<-NRQL
		SELECT count(*) FROM Log WHERE aws.logStream LIKE '${var.environment_name}/pub-payments-process-fineos/%' AND message = 'Claim type does not match application leave reason'
    NRQL
  }

  violation_time_limit_seconds = 86400 # 24 hours


  critical {
    threshold_duration    = 600 # ten minutes (2 windows)
    threshold             = 0   # units: percentage
    operator              = "above"
    threshold_occurrences = "all"
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# Alarms relating to problems in the PUB Process Weekly Reports

# module "pub_process_weekly_report_errors" {
#   count  = (var.environment_name == "prod") ? 1 : 0
#   source = "../newrelic_single_error_alarm"

#   enabled   = true
#   name      = "Errors encountered by a PUB Process Weekly Reports ECS task (${var.environment_name})"
#   policy_id = newrelic_alert_policy.low_priority_api_alerts.id

#   nrql = <<-NRQL
#     SELECT count(*) FROM Log
#     WHERE aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
#       AND aws.logStream LIKE '${var.environment_name}/pub-process-weekly-reports%'
#       AND levelname = 'ERROR'
#   NRQL
# }

# module "pub_process_weekly_report_ecs_task_failures" {
#   source    = "../newrelic_single_error_alarm"
#   policy_id = (var.environment_name == "prod") ? newrelic_alert_policy.api_alerts.id : newrelic_alert_policy.low_priority_api_alerts.id

#   enabled = true
#   name    = "PUB Process Weekly Reports ECS task failed (${var.environment_name})"
#   nrql    = <<-NRQL
#     SELECT count(*) FROM Log
#     WHERE aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
#       AND aws.logStream LIKE '${var.environment_name}/pub-process-weekly-reports%'
#       AND message LIKE 'Traceback%'
#   NRQL
# }

# ----------------------------------------------------------------------------------------------------------------------
# Alert for RMV ID check when the matching rate goes below 80 percentage

# module "overall_rmv_check_error_alert" {
#   source = "../newrelic_single_error_alarm"

#   enabled   = true
#   name      = "RMV ID check error alert for below 80 percentage in past seven days"
#   policy_id = newrelic_alert_policy.low_priority_api_alerts.id

#   nrql = <<-NRQL
#           SELECT percentage(count(*), WHERE (rmv_check.check_expiration = 'True' AND rmv_check.check_customer_inactive = 'True' AND rmv_check.check_active_fraudulent_activity = 'True' AND rmv_check.check_mass_id_number = 'True' AND rmv_check.rmv_error_code IS NULL AND rmv_check.api_error_code IS NULL) OR rmv_check.has_passed_required_checks = 'True') AS 'Overall Pass Rate'
#           FROM Log 
#           WHERE ((message != 'RMV Check started' AND rmv_check.rmv_check_id IS NOT NULL AND rmv_check.has_passed_required_checks IS NULL) OR message = 'RMV Check completed') 
#           AND newrelic.source = 'api.logs'
#           WHERE 'Overall Pass Rate' < 80
#         NRQL
# }

# ----------------------------------------------------------------------------------------------------------------------

resource "newrelic_nrql_alert_condition" "dns_failure_encountered" {
  # CRIT: dns failure encountered
  policy_id                    = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name                         = "dns failure encountered (${upper(var.environment_name)})"
  aggregation_window           = 60 # units: seconds
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  violation_time_limit_seconds = 86400 # 24 hours
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#DNS-failure-encountered"

  nrql {
    query = "SELECT count(*) FROM Log WHERE aws.logStream LIKE '${var.environment_name}%' AND ( message LIKE '%Temporary failure in name resolution%' OR message LIKE  '%Name or service not known%')"

  }


  critical {
    threshold_occurrences = "ALL"
    threshold_duration    = 60 # units: seconds
    operator              = "above"
    threshold             = 0 # units: count
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# Alarms relating to problems in the DIA / DUA send claimant lists to agencies

module "dia_dua_send_claimant_lists_to_agencies_partial_failure_warning" {
  source = "../newrelic_single_error_alarm"

  enabled     = true
  name        = "Partial failure encountered by DIA / DUA reductions-send-claimant-lists-to-agencies (${var.environment_name})"
  policy_id   = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook"

  nrql = <<-NRQL
    SELECT count(*) FROM Log
    WHERE aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
      AND aws.logStream LIKE '${var.environment_name}/dia_dua_send_claimant_lists_to_agencies_failure%'
      AND levelname = 'WARNING'
      AND message LIKE '%encountered partial failure less than threshold value%'
  NRQL
}

module "dia_dua_send_claimant_lists_to_agencies_failure" {
  source = "../newrelic_single_error_alarm"

  enabled     = true
  name        = "Error encountered by DIA / DUA reductions-send-claimant-lists-to-agencies (${var.environment_name})"
  policy_id   = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  runbook_url = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook"

  nrql = <<-NRQL
    SELECT count(*) FROM Log
    WHERE aws.logGroup = 'service/pfml-api-${var.environment_name}/ecs-tasks'
      AND aws.logStream LIKE '${var.environment_name}/reductions-send-claimant-lists%'
      AND message LIKE 'Traceback%'
  NRQL
}

resource "newrelic_nrql_alert_condition" "high_experian_soap_error_rate_4xx" {
  # WARN: percentage of 4xx error response for queries > 5% for any 30 minutes period
  # CRIT: percentage of 4xx error response for queries > 80% for any 5 minutes period
  policy_id                    = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name                         = "[4xx] High Experian SOAP error rate - (${upper(var.environment_name)})"
  aggregation_window           = 300 # units: seconds
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  violation_time_limit_seconds = 86400 # 24 hours
  fill_option                  = "none"
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#GET-Claims-response-time-too-high"

  nrql {
    query = "SELECT percentage(COUNT(*), WHERE error.response.code >= 400 AND error.response.code < 500) FROM ExperianAddressValidateSOAPError WHERE appName = 'PFML-API-${upper(var.environment_name)}'"
  }

  warning {
    threshold_occurrences = "ALL"
    threshold_duration    = 1800 # units: seconds
    operator              = "above"
    threshold             = 5 # units: percentage
  }

  critical {
    threshold_occurrences = "ALL"
    threshold_duration    = 300 # units: seconds
    operator              = "above"
    threshold             = 80 # units: percentage
  }
}

resource "newrelic_nrql_alert_condition" "high_experian_soap_error_rate_5xx" {
  # WARN: percentage of 5xx error response for queries > 5% for any 30 minutes period
  # CRIT: percentage of 5xx error response for queries > 80% for any 5 minutes period
  policy_id                    = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name                         = "[5xx] High Experian SOAP error rate - (${upper(var.environment_name)})"
  aggregation_window           = 300 # units: seconds
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  violation_time_limit_seconds = 86400 # 24 hours
  fill_option                  = "none"
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#GET-Claims-response-time-too-high"

  nrql {
    query = "SELECT percentage(COUNT(*), WHERE error.response.code >= 500) FROM ExperianAddressValidateSOAPError WHERE appName = 'PFML-API-${upper(var.environment_name)}'"
  }

  warning {
    threshold_occurrences = "ALL"
    threshold_duration    = 1800 # units: seconds
    operator              = "above"
    threshold             = 5 # units: percentage
  }

  critical {
    threshold_occurrences = "ALL"
    threshold_duration    = 300 # units: seconds
    operator              = "above"
    threshold             = 80 # units: percentage
  }
}

resource "newrelic_nrql_alert_condition" "load_employers_to_fineos_limit_reached" {
  policy_id                    = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name                         = "Load Employers to Fineos limit reached - (${upper(var.environment_name)})"
  aggregation_window           = 300
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  violation_time_limit_seconds = 86400
  fill_option                  = "none"
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Load-Employers-to-Fineos-limit-reached"

  nrql {
    query = "FROM Log SELECT count(*) where message = 'Finished loading employer updates to FINEOS.' and entity.name = 'PFML-API-${upper(var.environment_name)}' and report like '%Employer update limit reached:%'"
  }

  warning {
    threshold_occurrences = "ALL"
    threshold_duration    = 300
    operator              = "above"
    threshold             = 0
  }
}

resource "newrelic_nrql_alert_condition" "eligibility_feed_export_file_number_limit_reached" {
  policy_id                    = (var.environment_name == "prod") ? newrelic_alert_policy.low_priority_psd_policy[0].id : newrelic_alert_policy.low_priority_nonprod_policy[0].id
  name                         = "Eligibility feed export file number limit reached - (${upper(var.environment_name)})"
  aggregation_window           = 300
  aggregation_method           = "event_flow"
  aggregation_delay            = 120
  violation_time_limit_seconds = 86400
  fill_option                  = "none"
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/API+Alert+Playbook#Fineos-Eligibility-Feed-Export-limit-reached"

  nrql {
    query = "FROM Log SELECT count(*) where message = 'Finished writing all eligibility feeds' and entity.name = 'PFML-API-${upper(var.environment_name)}' and report like '%Employer update file limit reached:%'"
  }

  warning {
    threshold_occurrences = "ALL"
    threshold_duration    = 300
    operator              = "above"
    threshold             = 0
  }
}