# CPU and Memory Alarms for ECS Batch Jobs and Services
# CloudFront Portal Prod 4XX/5XX Errors Alarms

locals {
  tasks = {
    "add-employer" = {
      cpu = 4096
      mem = 18432
    },
    "patch-employer" = {
      cpu = 4096
      mem = 8192
    },
    "add-leave-admin" = {
      cpu = 4096
      mem = 18432
    },
    "add-merger-acquisition" = {
      cpu = 1024
      mem = 2048
    },
    "analyze-fineos-entitlement-periods" = {
      cpu = 2048
      mem = 16384
    },
    "appeals-generate-intake-pdfs" = {
      cpu = 2048
      mem = 4096
    },
    "appeals-import-extract" = {
      cpu = 2048
      mem = 4096
    },
    "backfill-benefit-year-base-periods" = {
      cpu = 1024
      mem = 2048
    },
    "backfill-benefit-years" = {
      cpu = 1024
      mem = 2048
    },
    "backfill-wages-and-contributions-datasource" = {
      cpu = 1024
      mem = 2048
    },
    "child-support-dor-import" = {
      cpu = 2048
      mem = 4096
    },
    "cps-errors-crawler" = {
      cpu = 2048
      mem = 4096
    },
    "db-admin-create-db-users" = {
      cpu = 2048
      mem = 4096
    },
    "db-create-fineos-user" = {
      cpu = 1024
      mem = 2048
    },
    "db-create-imageaccess-user" = {
      cpu = 1024
      mem = 2048
    },
    "db-create-servicenow-user" = {
      cpu = 1024
      mem = 2048
    },
    "db-migrate-down" = {
      cpu = 1024
      mem = 2048
    },
    "db-migrate-up" = {
      cpu = 2048
      mem = 4096
    },
    "dfml-fines-and-repayments" = {
      cpu = 1024
      mem = 2048
    },
    "dor-import" = {
      cpu = 4096
      mem = 30720
    },
    "dor-import-exempt" = {
      cpu = 8192
      mem = 61440
    },
    "employer-exemptions-export-for-dor" = {
      cpu = 4096
      mem = 18432
    },
    "dor-import-exempt-repush" = {
      cpu = 4096
      mem = 18432
    },
    "dor-pending-filing-response-import" = {
      cpu = 4096
      mem = 18432
    },
    "dor_create_pending_filing_submission" = {
      cpu = 4096
      mem = 18432
    },
    "dua-generate-and-send-employee-request-file" = {
      cpu = 2048
      mem = 4096
    },
    "dua-generate-and-send-employer-request-file" = {
      cpu = 2048
      mem = 4096
    },
    "dua-import-employee-demographics" = {
      cpu = 2048
      mem = 4096
    },
    "dua-import-employer" = {
      cpu = 2048
      mem = 4096
    },
    "dua-import-employer-unit" = {
      cpu = 2048
      mem = 4096
    },
    "dua-wages-from-dor-import" = {
      cpu = 2048
      mem = 8192
    },
    "execute-sql" = {
      cpu = 1024
      mem = 2048
    },
    "fineos-bucket-tool" = {
      cpu = 2048
      mem = 4096
    },
    "fineos-eligibility-feed-export" = {
      cpu = 4096
      mem = 8192
    },
    "fineos-import-employee-updates" = {
      cpu = 2048
      mem = 9216
    },
    "fineos-import-iaww" = {
      cpu = 2048
      mem = 12288
    },
    "fineos-import-la-units" = {
      cpu = 2048
      mem = 9216
    },
    "fineos-import-service-agreements" = {
      cpu = 8192
      mem = 61440
    },
    "load-service-agreements-to-fineos" = {
      cpu = 2048
      mem = 16384
    },
    "import-fineos-to-warehouse" = {
      cpu = 2048
      mem = 4096
    },
    "load-employers-to-fineos" = {
      cpu = 1024
      mem = 2048
    },
    "mock-fineos-writeback-status-for-payments" = {
      cpu = 1024
      mem = 2048
    },
    "pub-overpayments-backfill-data" = {
      cpu = 2048
      mem = 16384
    },
    "pub-payments-copy-audit-report" = {
      cpu = 2048
      mem = 4096
    },
    "pub-payments-create-pub-files" = {
      cpu = 2048
      mem = 16384
    },
    "pub-payments-process-fineos" = {
      cpu = 16384
      mem = 73728
    },
    "pub-payments-process-pub-returns" = {
      cpu = 1024
      mem = 2048
    },
    "pub-payments-process-snapshot" = {
      cpu = 4096
      mem = 30720
    },
    "pub-payments-verify-fineos-extract" = {
      cpu = 2048
      mem = 16384
    },
    "pub-process-weekly-reports" = {
      cpu = 2048
      mem = 4096
    },
    "reductions-process-agency-data" = {
      cpu = 4096
      mem = 8192
    },
    "reductions-send-claimant-lists" = {
      cpu = 4096
      mem = 8192
    },
    "register-leave-admins-with-fineos" = {
      cpu = 4096
      mem = 18432
    },
    "remove-leave-admin" = {
      cpu = 4096
      mem = 18432
    },
    "report-sequential-employment" = {
      cpu = 1024
      mem = 2048
    },
    "sftp-tool" = {
      cpu = 1024
      mem = 2048
    },
    "sync-fineos-extracts-to-pfml-models" = {
      cpu = 2048
      mem = 8192
    },
    "update-gender-data-from-rmv" = {
      cpu = 1024
      mem = 2048
    },
    "update-uuid" = {
      cpu = 1024
      mem = 2048
    },
    "update-employer" = {
      cpu = 1024
      mem = 2048
    },
    "process-overpayment-generate-mock-mmars-response" = {
      cpu = 2048,
      mem = 4096,
    },
    "process-rfi-renotifications" = {
      cpu = 2048,
      mem = 4096,
    },
    "update-application" = {
      cpu = 4096
      mem = 18432
    },
    "submit-deferred-items" = {
      cpu = 1024
      mem = 2048
    },
  }
}

##############################################################################
# ECS Tasks (Batch Jobs)
##############################################################################

# Only creating warning level for Batch Jobs to help limit the number of CW alarms
resource "aws_cloudwatch_metric_alarm" "batch_cpu_warn" {
  for_each          = { for task, attr in local.tasks : task => attr if contains(module.constants.alerting_environments, var.environment_name) }
  alarm_name        = "${local.pfml-api_app_name}-${var.environment_name}-${each.key}_CPU-Warning"
  alarm_description = "(${local.pfml-api_app_name}-${var.environment_name}-${each.key} CPU WARN) CPU usage exceeded 90%"
  namespace         = "ECS/ContainerInsights"
  dimensions = {
    ClusterName          = var.environment_name
    TaskDefinitionFamily = "${local.pfml-api_app_name}-${var.environment_name}-${each.key}"
  }
  statistic           = "Average"
  metric_name         = "CpuUtilized"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = (each.value.cpu * 0.90)
  evaluation_periods  = "10" # look back at the last 10 minutes
  datapoints_to_alarm = "10" # all one-minute periods
  period              = "60" # polling on one-minute intervals
  treat_missing_data  = "notBreaching"
  actions_enabled     = true
  alarm_actions       = [(var.environment_name == "prod") ? var.psd_low_priority_cloudwatch_alerts_topic_arn : var.pfml_nonprod_low_priority_cloudwatch_alerts_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "batch_ram_warn" {
  for_each          = { for task, attr in local.tasks : task => attr if contains(module.constants.alerting_environments, var.environment_name) }
  alarm_name        = "${local.pfml-api_app_name}-${var.environment_name}-${each.key}_RAM-Warning"
  alarm_description = "(${local.pfml-api_app_name}-${var.environment_name}-${each.key} RAM WARN) RAM usage exceeded 75%"
  namespace         = "ECS/ContainerInsights"
  dimensions = {
    ClusterName          = var.environment_name
    TaskDefinitionFamily = "${local.pfml-api_app_name}-${var.environment_name}-${each.key}"
  }
  statistic           = "Average"
  metric_name         = "MemoryUtilized" # units: MiB
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = (each.value.mem * 0.75)
  evaluation_periods  = "1"  # look back at the last minute
  datapoints_to_alarm = "1"  # any one-minute period
  period              = "60" # polling on one-minute intervals
  treat_missing_data  = "notBreaching"
  actions_enabled     = true
  alarm_actions       = [(var.environment_name == "prod") ? var.psd_low_priority_cloudwatch_alerts_topic_arn : var.pfml_nonprod_low_priority_cloudwatch_alerts_topic_arn]
}

##############################################################################
# ECS Services (API / PDF API)
##############################################################################

resource "aws_cloudwatch_metric_alarm" "service_cpu_warn" {
  for_each          = { for service in toset(["pfml-api-${var.environment_name}", "pfml-pdf-api-${var.environment_name}"]) : service => service if contains(module.constants.alerting_environments, var.environment_name) }
  alarm_name        = "${each.value}_CPU-Warning"
  alarm_description = "(${each.value} CPU WARN) CPU usage exceeded 75% for 15 minutes"
  namespace         = "ECS"
  dimensions = {
    ClusterName = var.environment_name
    ServiceName = each.key
  }
  statistic           = "Average"
  metric_name         = "CPUUtilization" # units: percentage
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = 75
  evaluation_periods  = "15" # look back at the last fifteen minutes
  datapoints_to_alarm = "15" # all 15 one-minute periods
  period              = "60" # polling on one-minute intervals
  actions_enabled     = true
  alarm_actions       = [(var.environment_name == "prod") ? var.psd_low_priority_cloudwatch_alerts_topic_arn : var.pfml_nonprod_low_priority_cloudwatch_alerts_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "service_cpu_crit" {
  for_each          = { for service in toset(["pfml-api-${var.environment_name}", "pfml-pdf-api-${var.environment_name}"]) : service => service if contains(module.constants.alerting_environments, var.environment_name) }
  alarm_name        = "${each.value}_CPU-Critical"
  alarm_description = "(${each.value} CPU CRIT) CPU usage exceeded 95% for 15 minutes"
  namespace         = "ECS"
  dimensions = {
    ClusterName = var.environment_name
    ServiceName = each.key
  }
  statistic           = "Average"
  metric_name         = "CPUUtilization" # units: percentage
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = 95
  evaluation_periods  = "15" # look back at the last fifteen minutes
  datapoints_to_alarm = "15" # all 15 one-minute periods
  period              = "60" # polling on one-minute intervals
  actions_enabled     = true
  alarm_actions       = [(var.environment_name == "prod") ? var.lwd_it_support_high_priority_alerts_topic_arn : var.pfml_nonprod_low_priority_cloudwatch_alerts_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "service_ram_warn" {
  for_each          = { for service in toset(["pfml-api-${var.environment_name}", "pfml-pdf-api-${var.environment_name}"]) : service => service if contains(module.constants.alerting_environments, var.environment_name) }
  alarm_name        = "${each.key}_RAM-Warning"
  alarm_description = "(${each.key} RAM WARN) RAM usage exceeded 75% for 15 minutes"
  namespace         = "ECS"
  dimensions = {
    ClusterName = var.environment_name
    ServiceName = each.key
  }
  statistic           = "Average"
  metric_name         = "MemoryUtilization" # units: percentage
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = 75
  evaluation_periods  = "15" # look back at the last fifteen minutes
  datapoints_to_alarm = "15" # all 15 one-minute periods
  period              = "60" # polling on one-minute intervals
  actions_enabled     = true
  alarm_actions       = [(var.environment_name == "prod") ? var.psd_low_priority_cloudwatch_alerts_topic_arn : var.pfml_nonprod_low_priority_cloudwatch_alerts_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "service_ram_crit" {
  for_each          = { for service in toset(["pfml-api-${var.environment_name}", "pfml-pdf-api-${var.environment_name}"]) : service => service if contains(module.constants.alerting_environments, var.environment_name) }
  alarm_name        = "${each.key}_RAM-Critical"
  alarm_description = "(${each.key} RAM Critical) RAM usage exceeded 95% for 15 minutes"
  namespace         = "ECS"
  dimensions = {
    ClusterName = var.environment_name
    ServiceName = each.key
  }
  statistic           = "Average"
  metric_name         = "MemoryUtilized" # units: percentage
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = 95
  evaluation_periods  = "15" # look back at the last fifteen minutes
  datapoints_to_alarm = "15" # all 15 one-minute periods
  period              = "60" # polling on one-minute intervals
  actions_enabled     = true
  alarm_actions       = [(var.environment_name == "prod") ? var.lwd_it_support_high_priority_alerts_topic_arn : var.pfml_nonprod_low_priority_cloudwatch_alerts_topic_arn]
}

##############################################################################
# CloudFront Portal Prod 4XX/5XX Alarms
##############################################################################

resource "aws_cloudwatch_metric_alarm" "cloudfront_4xx_errors" {
  count             = var.environment_name == "prod" ? 1 : 0
  alarm_name        = "Portal-Prod-CloudFront-4XX-Errors"
  alarm_description = "Prod Portal CloudFront (${data.terraform_remote_state.portal_prod.outputs.cloudfront_distribution_id}) 4XX Errors exceeded 5% for 20 minutes. Runbook URL: https://lwd.atlassian.net/wiki/x/A4DPz"
  namespace         = "AWS/CloudFront"
  dimensions = {
    DistributionId = data.terraform_remote_state.portal_prod.outputs.cloudfront_distribution_id
    Region         = "Global"
  }
  statistic           = "Average"
  metric_name         = "4xxErrorRate"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = 5
  evaluation_periods  = "20" # look back at the last 20 minutes
  datapoints_to_alarm = "20" # all 20 one-minute periods
  period              = "60" # polling on one-minute intervals
  treat_missing_data  = "notBreaching"
  actions_enabled     = true
  alarm_actions       = [var.lwd_it_support_high_priority_alerts_topic_arn]
  ok_actions          = [var.lwd_it_support_high_priority_alerts_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "cloudfront_5xx_errors" {
  count             = var.environment_name == "prod" ? 1 : 0
  alarm_name        = "Portal-Prod-CloudFront-5XX-Errors"
  alarm_description = "Prod Portal CloudFront (${data.terraform_remote_state.portal_prod.outputs.cloudfront_distribution_id}) 5XX Errors exceeded 5% for 20 minutes. Runbook URL: https://lwd.atlassian.net/wiki/x/Rwfezg"
  namespace         = "AWS/CloudFront"
  dimensions = {
    DistributionId = data.terraform_remote_state.portal_prod.outputs.cloudfront_distribution_id
    Region         = "Global"
  }
  statistic           = "Average"
  metric_name         = "5xxErrorRate"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = 5
  evaluation_periods  = "20" # look back at the last 20 minutes
  datapoints_to_alarm = "20" # all 20 one-minute periods
  period              = "60" # polling on one-minute intervals
  treat_missing_data  = "notBreaching"
  actions_enabled     = true
  alarm_actions       = [var.lwd_it_support_high_priority_alerts_topic_arn]
  ok_actions          = [var.lwd_it_support_high_priority_alerts_topic_arn]
}