# Terraform configuration for any alarm that's stored in New Relic.

locals {
  environments = module.constants.monitoring_environments
}

module "alarms_api_portal" {
  for_each = toset(local.environments)
  source   = "./alarms_api_portal"

  environment_name                                      = each.key
  high_priority_lwd_it_support_nr_integration_key       = data.pagerduty_service_integration.lwd_it_support_high_priority_newrelic_notification.integration_key
  low_priority_lwd_it_support_nr_integration_key        = data.pagerduty_service_integration.lwd_it_support_low_priority_newrelic_notification.integration_key
  high_priority_psd_nr_integration_key                  = data.pagerduty_service_integration.psd_high_priority_newrelic_notification.integration_key
  low_priority_psd_nr_integration_key                   = data.pagerduty_service_integration.psd_low_priority_newrelic_notification.integration_key
  low_priority_nonprod_nr_integration_key               = data.pagerduty_service_integration.pfml_nonprod_low_priority_newrelic_notification.integration_key
  psd_low_priority_email_alerts_topic_arn               = module.psd_alerts["psd-low-priority-email-alerts"].sns_topic_arn
  psd_low_priority_cloudwatch_alerts_topic_arn          = module.psd_alerts["psd-low-priority-cloudwatch-alerts"].sns_topic_arn
  lwd_it_support_high_priority_alerts_topic_arn         = aws_sns_topic.lwd_it_support_high_priority_alerts_topic.arn
  pfml_nonprod_low_priority_email_alerts_topic_arn      = module.pfml_nonprod_low_priority_alerts["nonprod-low-priority-email-alerts"].sns_topic_arn
  pfml_nonprod_low_priority_cloudwatch_alerts_topic_arn = module.pfml_nonprod_low_priority_alerts["nonprod-low-priority-cloudwatch-alerts"].sns_topic_arn
}

module "alarms_infra" {
  for_each = toset(local.environments)
  source   = "./alarms_infra"

  environment_name                       = each.key
  infra_low_priority_nr_integration_key  = data.pagerduty_service_integration.infra_low_priority_newrelic_notification.integration_key
  infra_high_priority_nr_integration_key = data.pagerduty_service_integration.infra_high_priority_newrelic_notification.integration_key
  infra_high_priority_sns_topic          = module.infra_sns["infra-high-priority-cloudwatch-alerts"].sns_topic_arn
  infra_low_priority_sns_topic           = module.infra_sns["infra-low-priority-cloudwatch-alerts"].sns_topic_arn
  dba_low_priority_sns_topic             = aws_sns_topic.dba-low-priority-alerts-topic.arn
  dba_high_priority_sns_topic            = aws_sns_topic.dba-high-priority-alerts-topic.arn
}

module "ses_alarms" {
  source = "./ses_alarms"

  low_priority_psd_nr_integration_key = data.pagerduty_service_integration.psd_low_priority_newrelic_notification.integration_key
  infra_low_priority_sns_topic        = module.infra_sns["infra-low-priority-cloudwatch-alerts"].sns_topic_arn
}

module "sns_alarms" {
  source                                         = "./sns_alarms"
  sns_monthly_spend_limit                        = module.constants.aws_sns_sms_monthly_spend_limit
  psd_low_priority_pager_duty_integration_key    = data.pagerduty_service_integration.psd_low_priority_cloudwatch_notification.integration_key
  psd_high_priority_pager_duty_integration_key   = data.pagerduty_service_integration.psd_high_priority_cloudwatch_notification.integration_key
  infra_high_priority_pager_duty_integration_key = data.pagerduty_service_integration.infra_high_priority_cloudwatch_notification.integration_key
  infra_low_priority_pager_duty_integration_key  = data.pagerduty_service_integration.infra_low_priority_cloudwatch_notification.integration_key
  sns_log_group_name                             = data.aws_cloudwatch_log_group.direct_publish_to_phone_number.name
  sns_failure_log_group_name                     = data.aws_cloudwatch_log_group.direct_publish_to_phone_number_failure.name
}

module "ssm_expiration_alarm" {
  source = "./ssm_expiration_alarm"

  lambda_directory                          = local.lambda_directory
  infra_low_priority_events_integration_key = data.pagerduty_service_integration.infra_low_priority_events_api.integration_key
  schedules = {
    parameters_expiration_check_schedule = {
      expression = "rate(1 day)"
      timezone   = null
      payload    = null
    }
  }
}

locals {
  lambda_directory = "lambda_functions"
}

module "aws_resource_changes_alarm" {
  source = "./aws_resource_change_alarm"

  lambda_directory = local.lambda_directory
}

module "aws_health_alerting" {
  source = "./aws_health_alerting"

  lambda_directory                          = local.lambda_directory
  infra_low_priority_events_integration_key = data.pagerduty_service_integration.infra_low_priority_events_api.integration_key
}

module "ecs_failure_monitor" {
  source = "./ecs_failure_monitor"

  lambda_directory                                    = local.lambda_directory
  psd_low_priority_events_integration_key             = data.pagerduty_service_integration.psd_low_priority_events_api.integration_key
  lwd_it_support_high_priority_events_integration_key = data.pagerduty_service_integration.lwd_it_support_high_priority_events_api.integration_key
  pfml_nonprod_low_priority_events_integration_key    = data.pagerduty_service_integration.pfml_nonprod_low_priority_events_api.integration_key
}

module "newrelic_kinesis_log_transform" {
  source = "./newrelic_kinesis_log_transform"

  lambda_directory      = local.lambda_directory
  kinesis_firehose_name = "massgov-pfml-sms-newrelic"
  dlq_bucket_name       = "massgov-pfml-sms-kinesis-to-newrelic-dlq"
  function_name         = "newrelic_kinesis_log_transform"
  function_description  = "Kinesis filter to mask sms destinations before sending to newrelic"
  nr_log_group_name     = data.aws_cloudwatch_log_group.direct_publish_to_phone_number.name
  cw_log_group_names = [
    data.aws_cloudwatch_log_group.direct_publish_to_phone_number.name,
    data.aws_cloudwatch_log_group.direct_publish_to_phone_number_failure.name
  ]
}

#### Synthetic Monitoring for Fineos Version and SSO

locals {
  watcher_configs = {
    # Use SCRIPT_API for non-sso environments
    api = {
      "trn2" : "https://trn2-claims-webapp.masspfml.fineos.com",
      "tst1" : "https://idt1-claims-webapp.masspfml.fineos.com",
    }
    # Use SCRIPT_BROWSER fro sso environments
    browser = {
      "tst3" : {
        "fineos_url" : "https://idt3-claims-webapp.masspfml.fineos.com"
        "has_landing_page" : true
      },
      "uat" : {
        "fineos_url" : "https://uat-claims-webapp.masspfml.fineos.com"
        "has_landing_page" : false
      },
      "performance" : {
        "fineos_url" : "https://perf-claims-webapp.masspfml.fineos.com",
        "has_landing_page" : false
      }
      "breakfix" : {
        "fineos_url" : "https://pfx-claims-webapp.masspfml.fineos.com"
        "has_landing_page" : false
      }
      "training" : {
        "fineos_url" : "https://trn-claims-webapp.masspfml.fineos.com",
        "has_landing_page" : "false"
      }
      "tst2" : {
        "fineos_url" : "https://idt2-claims-webapp.masspfml.fineos.com",
        "has_landing_page" : "true"
      }
    }
  }
}

module "fineos_version_watcher" {
  for_each    = local.watcher_configs.api
  source      = "./fineos_version_watcher"
  environment = each.key
  fineos_url  = each.value
}

module "fineos_sso_version_watcher" {
  for_each         = local.watcher_configs.browser
  source           = "./fineos_sso_version_watcher"
  environment      = each.key
  fineos_url       = each.value.fineos_url
  has_landing_page = each.value.has_landing_page
}

module "auto_ip_blocking_newrelic" {
  source = "./auto_ip_blocking_newrelic"
}

module "logging_to_snowflake" {
  source = "./logging_to_snowflake"
}

module "newrelic_dashboards" {
  source = "./newrelic_dashboards"
}

module "us_bank" {
  source = "./us_bank"
}