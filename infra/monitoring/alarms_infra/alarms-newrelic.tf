# Infra New Relic alarms and supporting resources

locals {
  infra_alert_channel = {
    "performance" = var.infra_low_priority_nr_integration_key,
    "training"    = var.infra_low_priority_nr_integration_key,
    "uat"         = var.infra_low_priority_nr_integration_key,
    "breakfix"    = var.infra_low_priority_nr_integration_key,
    "trn2"        = var.infra_low_priority_nr_integration_key,
    "tst3"        = var.infra_low_priority_nr_integration_key,
    "tst2"        = var.infra_low_priority_nr_integration_key,
    "tst1"        = var.infra_low_priority_nr_integration_key,
    "prod"        = var.infra_high_priority_nr_integration_key,
  }
}

#################################################
### Alert Policies
#################################################
resource "newrelic_alert_policy" "infra_alerts" {
  name                = "PFML Infra Alerts (${upper(var.environment_name)})"
  account_id          = module.constants.newrelic_account_id
  incident_preference = "PER_CONDITION" # a new alarm will sound for every distinct alert condition violated
}

#################################################
### Workflows
#################################################
resource "newrelic_workflow" "infra_workflow" {
  name                  = "PFML Infra Workflow (${upper(var.environment_name)})"
  muting_rules_handling = "NOTIFY_ALL_ISSUES"
  issues_filter {
    name = "Filter"
    type = "FILTER"
    predicate {
      attribute = "labels.policyIds"
      operator  = "EXACTLY_MATCHES"
      values    = [newrelic_alert_policy.infra_alerts.id]
    }
  }

  destination {
    channel_id = newrelic_notification_channel.infra_notification_channel.id
  }
}

#################################################
### Notification Destinations & Channels
#################################################
resource "newrelic_notification_destination" "infra_notification_destination" {
  name = "PFML Infra ${var.environment_name == "prod" ? "High" : "Low"} priority destination (${upper(var.environment_name)})"
  type = "PAGERDUTY_SERVICE_INTEGRATION"
  property {
    key   = ""
    value = ""
  }
  auth_token {
    prefix = "Token token="
    token  = local.infra_alert_channel[var.environment_name]
  }
}

resource "newrelic_notification_channel" "infra_notification_channel" {
  name           = "PFML Infra Notification Channel"
  type           = "PAGERDUTY_SERVICE_INTEGRATION"
  destination_id = newrelic_notification_destination.infra_notification_destination.id
  product        = "IINT"
  property {
    key   = "summary"
    value = "{{ annotations.title.[0] }}"
  }
}

#################################################
### Alert Conditions
#################################################

resource "newrelic_nrql_alert_condition" "no_logs_received" {
  # Only create for prod
  count                        = contains(["prod"], var.environment_name) ? 1 : 0
  name                         = "No logs received for 2 hours in service/pfml-api-${var.environment_name}"
  policy_id                    = newrelic_alert_policy.infra_alerts.id
  type                         = "static"
  fill_option                  = "last_value"
  enabled                      = true
  aggregation_window           = 7200 # 2 hours
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/KB/pages/2801172545/Log+query+result+is+0.0+for+120+minutes+on+No+logs+received+for+2+hours+in+service+pfml-api-prod"
  violation_time_limit_seconds = 86400 # 24 hours

  # The filter() changes order of operations so that 0 is returned instead of null
  # https://docs.newrelic.com/docs/alerts-applied-intelligence/new-relic-alerts/alert-conditions/create-nrql-alert-conditions/#query-order
  nrql {
    query = <<-NRQL
    SELECT filter(count(*), WHERE aws.logGroup LIKE 'service/pfml-api-${var.environment_name}') FROM Log
    NRQL
  }

  critical {
    threshold_duration    = 7200 # 2 hours
    threshold             = 0    # No results
    operator              = "equals"
    threshold_occurrences = "all"
  }
}

# Alert if there is a DNS outage 
resource "newrelic_nrql_alert_condition" "dns_outage" {
  name                         = "Possible EOTSS DNS Outage"
  policy_id                    = newrelic_alert_policy.infra_alerts.id
  enabled                      = true
  aggregation_window           = 900 # 15 minutes
  aggregation_method           = "event_timer"
  aggregation_timer            = 300 # 5 minutes
  runbook_url                  = "https://lwd.atlassian.net/wiki/spaces/KB/pages/2802286595/Log+query+result+is+50.0+for+at+least+15+mins+on+Possible+EOTSS+DNS+Outage"
  violation_time_limit_seconds = 86400 # 24 hours

  nrql {
    query = <<-NRQL
    SELECT count(*) FROM Log WHERE `message` LIKE '%%\'Name or service not known\'%%' 
    AND entity.name = 'PFML-API-${upper(var.environment_name)}'
    NRQL
  }

  critical {
    threshold_duration    = 900 # 15 minutes
    threshold             = 25  # 25 results or more
    operator              = "above"
    threshold_occurrences = "all"
  }
}