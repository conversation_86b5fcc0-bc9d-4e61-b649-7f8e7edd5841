# This file was originally generated from the following command:
#
#   bin/bootstrap-env.sh tst2 portal
#
# If adding new variables, it's recommended to update the bootstrap
# templates so there's less manual work in creating new envs.
#
provider "aws" {
  region = "us-east-1"
}

terraform {
  backend "s3" {
    bucket         = "massgov-pfml-tst2-env-mgmt"
    key            = "terraform/portal.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
  }
}

output "cloudfront_distribution_id" {
  description = "Cloudfront distribution id for portal environment. Used for cache invalidation in github workflow."
  value       = module.massgov_pfml.cloudfront_distribution_id
}

module "massgov_pfml" {

  # Firewall rules
  # 'true' will set rule to 'BLOCK' (or 'NONE' which is equivalent)
  # 'false' will set rule to 'COUNT' (counts traffic that meets rule(s) instead of blocking)
  enforce_rate_limit_rule                = true
  enforce_fortinet_rules                 = true
  enforce_blocklist_rule                 = true
  enforce_block_high_risk_countries_rule = true
  enforce_log4j_rule                     = true
  enforce_unrestricted_ip_rule           = false

  # You probably don't need to change the variables below:
  source                 = "../../template"
  environment_name       = "tst2"
  cloudfront_origin_path = local.cloudfront_origin_path
}
