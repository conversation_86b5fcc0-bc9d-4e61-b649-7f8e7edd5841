# This file was originally generated from the following command:
#
#   bin/bootstrap-env.sh prod portal
#
# If adding new variables, it's recommended to update the bootstrap
# templates so there's less manual work in creating new envs.
# 
provider "aws" {
  region = "us-east-1"
}

terraform {
  backend "s3" {
    bucket         = "massgov-pfml-prod-env-mgmt"
    key            = "terraform/portal.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
    kms_key_id     = "arn:aws:kms:us-east-1:498823821309:key/641eba51-98e5-4776-98b6-98ed06866ec8"
  }
}

output "cloudfront_distribution_id" {
  description = "Cloudfront distribution id for portal environment. Used for cache invalidation in github workflow."
  value       = module.massgov_pfml.cloudfront_distribution_id
}

module "massgov_pfml" {

  # Firewall rules
  # 'true' will set rule to 'BLOCK' (or 'NONE' which is equivalent)
  # 'false' will set rule to 'COUNT' (counts traffic that meets rule(s) instead of blocking)
  enforce_rate_limit_rule                = true
  enforce_fortinet_rules                 = true
  enforce_blocklist_rule                 = true
  enforce_block_high_risk_countries_rule = true
  enforce_log4j_rule                     = true
  enforce_unrestricted_ip_rule           = false
  enforce_block_verified_bots_rule       = true

  # You probably don't need to change the variables below:
  source                 = "../../template"
  environment_name       = "prod"
  cloudfront_origin_path = local.cloudfront_origin_path

  # S3 Access Logging
  s3_access_logging_enabled = true
}
