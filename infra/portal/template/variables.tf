variable "cloudfront_origin_path" {
  description = "Path to latest portal release. Set through environment variable in Github worfklow."
  type        = string
}

variable "domain" {
  description = "Domain name to point to CloudFront distribution (including TLD)"
  type        = string
  default     = ""
}

variable "environment_name" {
  description = "Name of the environment (dev, sandbox, tst1, uat, tst2, prod)"
  type        = string
}

variable "enforce_rate_limit_rule" {
  type    = bool
  default = true
}

variable "enforce_fortinet_rules" {
  type    = bool
  default = true
}

variable "enforce_blocklist_rule" {
  type    = bool
  default = true
}

variable "enforce_block_high_risk_countries_rule" {
  type    = bool
  default = true
}

variable "enforce_log4j_rule" {
  type    = bool
  default = true
}

variable "enforce_unrestricted_ip_rule" {
  type    = bool
  default = false
}

variable "enforce_block_verified_bots_rule" {
  type    = bool
  default = false
}

variable "is_dev_workspace" {
  description = "Whether this is running as a development workspace (preview environment)"
  type        = bool
  default     = false
}

variable "sms_mfa_message" {
  description = "Message sent to user with MFA code"
  type        = string
  default     = "Your 6-digit code is {####}. Enter this code to log in to your paidleave.mass.gov account. The code expires in 3 minutes."
}

variable "s3_access_logging_enabled" {
  description = "this enables access logging for buckets that need access logging enabled"
  type        = bool
  default     = false
}

variable "csp_enabled" {
  description = "Have CloudFront handle Content Security Policy"
  type        = bool
  default     = false
}
