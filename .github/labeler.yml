#
# Pull request label rules.
#
# Runs in action pull-request-labeler.yml.
#
# See https://github.com/marketplace/actions/labeler
#

# Labels for programming languages.
javascript:
  - changed-files:
    - any-glob-to-any-file: "**/*.js"
python:
  - changed-files:
    - any-glob-to-any-file: "**/*.py"
terraform:
  - changed-files:
    - any-glob-to-any-file: "**/*.tf"
typescript:
  - changed-files:
    - any-glob-to-any-file: "**/*.ts"

# Labels for specific components.
admin portal:
  - changed-files:
    - any-glob-to-any-file: "admin/**/*"
api:
  - changed-files:
    - any-glob-to-any-file: "api/**/*"
pdf-api:
  - changed-files:
    - any-glob-to-any-file: ["pdf_api/**/*", "api/pdf_api/*"]
ci/cd:
  - changed-files:
    - any-glob-to-any-file: ".github/workflows/**/*"
docs:
  - changed-files:
    - any-glob-to-any-file: "docs/**/*"
e2e:
  - changed-files:
    - any-glob-to-any-file: "e2e/**/*"
infra:
  - changed-files:
    - any-glob-to-any-file: "infra/**/*"
portal:
  - changed-files:
    - any-glob-to-any-file: "portal/**/*"

database:
  - changed-files:
    - any-glob-to-any-file: "api/massgov/pfml/db/**/*"
openapi:
  - changed-files:
    - any-glob-to-any-file: "api/openapi.yaml"
payments:
  - changed-files:
    - any-glob-to-any-file: "api/massgov/pfml/delegated_payments/**/*"