name: E2E Test Suite (All Environments)
#
# This workflow is to trigger scheduled runs every morning
#
# Scheduled runs will trigger a workflow_dispatch for all
# environments via the End-to-End Test Suite workflow

on:
  workflow_dispatch:
    inputs:
      cypress_tags:
        description: "Cypress Run Tags"
        required: false
        default: "Manual - Other (all envs)"
        type: choice
        options:
          - Manual - PR Re-run (all envs)
          - Manual - PR Env Check (all envs)
          - Manual - Post Morning Run Check (all envs)
          - Manual - Environment Sanity Check (all envs)
          - Manual - New Dataset Check (all envs)
          - Manual - Targeted Specs Check (all envs)
          - Manual - Other (all envs)
      run_deploy:
        description: "Run tests in deploy group"
        required: false
        default: true
        type: boolean
      run_unstable:
        description: "Run tests in unstable group"
        required: false
        default: false
        type: boolean
      run_morning:
        description: "Run tests in morning group"
        required: false
        default: false
        type: boolean
      run_cps:
        description: "Run tests in cps group"
        required: false
        default: false
        type: boolean
      run_playwright:
        description: "Run Playwright tests"
        required: false
        default: false
        type: boolean
      targeted_pattern:
        description: |
          Run a specific test, comma separated list of test (no spaces), or
          a blob (e.g. cypress/specs/deploy/**)
        required: false
        default: ""
        type: "string"
      record_video:
        description: "Save video to Cypress cloud"
        required: false
        default: false
        type: boolean
  schedule:
    # Morning Run
    - cron: "15 11 * * 1-5"
    # CPS group
    - cron: "0 19 * * 2,4"

permissions:
  id-token: write
  contents: read

jobs:
  get-e2e-enabled-environments:
    name: "Get E2E enabled environments"
    runs-on: [self-hosted, ecs]
    outputs:
      environments: ${{ steps.get-parameter.outputs.environments }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
          role-duration-seconds: 3600
      - name:  parameter from parameter store
        id: get-parameter
        run: |
          E2E_ENVS=$(aws ssm get-parameter --name "/admin/e2e/environments" --output text --query "Parameter.Value")
          JSON_ARRAY=$(echo $E2E_ENVS | jq -c -R 'split(",")')
          echo "environments=$JSON_ARRAY" >> $GITHUB_OUTPUT

  trigger_e2e:
    # This workflow intentionally doesn't use workflow_call because at the moment, it doesn't support matrix builds.
    # Also, since we don't wait for results or care about having an accurate run URL, the benefit is small.
    name: Run Cypress Test
    runs-on: [self-hosted, ecs]
    needs: get-e2e-enabled-environments
    strategy:
      fail-fast: false
      matrix:
        environment: ${{ fromJson(needs.get-e2e-enabled-environments.outputs.environments) }}
    steps:
      - name: Trigger E2E Workflow
        uses: lasith-kg/dispatch-workflow@v2
        with:
          dispatch-method: workflow_dispatch
          repo: pfml
          owner: EOLWD
          ref: ${{ github.ref }}
          workflow: e2e-cypress.yml
          token: ${{ secrets.PFML_DEVOPS_TOKEN }}
          workflow-inputs: |
            {
              "target_environment": "${{ matrix.environment }}",
              "cypress_tags": "${{ github.event.schedule == '15 11 * * 1-5' && 'Morning Run' || github.event.schedule == '0 19 * * 2,4' && 'Manual - Targeted Specs Check (all envs)' || github.event.inputs.cypress_tags }}",
              "run_playwright": "${{ github.event.schedule == '15 11 * * 1-5' && 'true' || github.event.schedule == '0 19 * * 2,4' && 'false' || github.event.inputs.run_playwright }}",
              "run_deploy": "${{ github.event.schedule == '15 11 * * 1-5' && 'true' || github.event.schedule == '0 19 * * 2,4' && 'false' || github.event.inputs.run_deploy }}",
              "run_unstable": "${{ github.event.schedule == '15 11 * * 1-5' && 'true' || github.event.inputs.run_unstable }}",
              "run_morning": "${{ github.event.schedule == '15 11 * * 1-5' && 'true' || github.event.inputs.run_morning }}",
              "record_video": "${{ github.event.inputs.record_video == 'true' }}",
              "run_cps": "${{ github.event.schedule == '15 11 * * 1-5' && 'false' || github.event.schedule == '0 19 * * 2,4' && ( matrix.environment != 'training' && matrix.environment != 'trn2' ) && 'true' || github.event.inputs.run_cps }}",
              "targeted_pattern": "${{ github.event.inputs.targeted_pattern }}"
            }
