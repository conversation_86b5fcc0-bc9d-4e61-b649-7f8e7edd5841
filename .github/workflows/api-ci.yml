# Run tests and linting for the Paid Leave API.
#
name: API CI Testing

on:
  push:
    branches:
      - main
    paths:
      - api/**
  pull_request:
    paths:
      - api/**
      - docs/assets/api/erds/**

defaults:
  run:
    working-directory: ./api

env:
  CI: true
  workflow_self_link: "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"

permissions:
  id-token: write
  contents: read

jobs:
  # Check for formatting and linting issues.
  lint:
    runs-on: [self-hosted, ec2]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
          role-duration-seconds: 3600

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: build container
        run: make build

      - name: install project dependencies not built into container
        run: make deps

      # TODO (API-1624) Re-enable once the dependency issue can be resolved.
      # Currently disabled so the rest of this CI action continues to operate and
      # block PR's as expected
      # - name: check dependencies
      #   run: make deps-check

      - name: format-check
        run: make format-check

      - name: run migrations
        run: make db-upgrade

      - name: lint
        run: make lint

      - name: security
        run: make lint-security

      - name: deprecated-columns
        run: make lint-remove-non-deprecated-column args=origin/${{ github.base_ref }}

  # Check for test failures.
  get-test-dirs:
    runs-on: [self-hosted, ecs]

    outputs:
      matrix: ${{ steps.get-dirs.outputs.matrix }}

    steps:
      - uses: actions/checkout@v4

      - name: get test directories
        id: get-dirs
        run: |
          sudo apt-get install jq
          cd tests/
          TEST_DIRS=$(ls -d */ | cut -f1 -d'/' | grep -v pycache | grep -v helpers | jq -R . | jq -s -c .)
          echo "matrix=$TEST_DIRS" >> $GITHUB_OUTPUT

  test:
    runs-on: [self-hosted, ec2]
    timeout-minutes: 60
    if: always()

    needs: get-test-dirs

    strategy:
      fail-fast: false
      matrix:
        test_dirs: ${{ fromJSON(needs.get-test-dirs.outputs.matrix) }}

    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
          role-duration-seconds: 3600

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: build container
        run: make build

      - name: start db
        run: make start-db

      - name: test for multi-headed migration situation
        run: DB_NAME=no_default_db make test args="tests/db/test_migrations.py::test_only_single_head_revision_in_migrations"

      - name: test with coverage
        run: DB_NAME=no_default_db make test-coverage args=tests/${{ matrix.test_dirs }}

  # Check we can build a release
  build-release:
    runs-on: [self-hosted, ec2]

    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
          role-duration-seconds: 3600

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: build
        run: make api-image-build

      - name: pre-flight-test
        run: make pre-flight-test

  # Temporarily disabled due to Tenable Container Security being EOL'd
  # https://docs.tenable.com/PDFs/EOL/legacy-container-security.pdf
  # Run Tenable container scanning test
  # scan-api-container:
  #   runs-on: [self-hosted, ec2]
  #   timeout-minutes: 15

  #   steps:
  #     - uses: actions/checkout@v4

  #     - name: Configure AWS Credentials
  #       uses: aws-actions/configure-aws-credentials@v4
  #       with:
  #         aws-region: us-east-1
  #         role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
  #         role-duration-seconds: 3600

  #     - name: Login to Amazon ECR
  #       id: login-ecr
  #       uses: aws-actions/amazon-ecr-login@v2

  #     - name: build API image
  #       run: make api-image-build

  #     - name: pull latest tenable image
  #       env:
  #         DOCKER_PW: ${{ secrets.PFML_TENABLE_DOCKER_PASSWORD }}
  #         DOCKER_USER: ${{ secrets.PFML_TENABLE_DOCKER_USER }}
  #       run: |
  #         echo "$DOCKER_PW" | docker login --username "$DOCKER_USER" --password-stdin tenableio-docker-consec-local.jfrog.io
  #         docker pull tenableio-docker-consec-local.jfrog.io/cs-scanner:latest

  #     - name: run tenable scan on API image
  #       env:
  #         TENABLE_ACCESS_KEY: ${{ secrets.TENABLE_ACCESS_KEY }}
  #         TENABLE_SECRET_KEY: ${{ secrets.TENABLE_SECRET_KEY }}
  #       run: |
  #         DOCKER_TAG=$(docker images | grep mass-pfml-api | grep -v latest | awk '{print $2}')
  #         docker save mass-pfml-api:$DOCKER_TAG | docker run \
  #         --env TENABLE_ACCESS_KEY=$TENABLE_ACCESS_KEY \
  #         --env TENABLE_SECRET_KEY=$TENABLE_SECRET_KEY \
  #         --env IMPORT_REPO_NAME=mass-pfml-api \
  #         --env CHECK_POLICY=true \
  #         --interactive tenableio-docker-consec-local.jfrog.io/cs-scanner:latest inspect-image mass-pfml-api:$DOCKER_TAG

  # scan-pdf-api-container:
  #   runs-on: [self-hosted, ec2]
  #   timeout-minutes: 15

  #   steps:
  #     - uses: actions/checkout@v4

  #     - name: Configure AWS Credentials
  #       uses: aws-actions/configure-aws-credentials@v4
  #       with:
  #         aws-region: us-east-1
  #         role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
  #         role-duration-seconds: 3600

  #     - name: Login to Amazon ECR
  #       id: login-ecr
  #       uses: aws-actions/amazon-ecr-login@v2

  #     - name: build PDF API image
  #       run: make -f ../api/Makefile pdf-api-image-build
  #       working-directory: ./pdf_api

  #     - name: pull latest tenable image
  #       env:
  #         DOCKER_PW: ${{ secrets.PFML_TENABLE_DOCKER_PASSWORD }}
  #         DOCKER_USER: ${{ secrets.PFML_TENABLE_DOCKER_USER }}
  #       run: |
  #         echo "$DOCKER_PW" | docker login --username "$DOCKER_USER" --password-stdin tenableio-docker-consec-local.jfrog.io
  #         docker pull tenableio-docker-consec-local.jfrog.io/cs-scanner:latest
  #     - name: run tenable scan on PDF API image
  #       env:
  #         TENABLE_ACCESS_KEY: ${{ secrets.TENABLE_ACCESS_KEY }}
  #         TENABLE_SECRET_KEY: ${{ secrets.TENABLE_SECRET_KEY }}
  #       run: |
  #         DOCKER_TAG=$(docker images | grep mass-pfml-api-pdf | grep -v latest | awk '{print $2}')
  #         docker save mass-pfml-api-pdf:$DOCKER_TAG | docker run \
  #         --env TENABLE_ACCESS_KEY=$TENABLE_ACCESS_KEY \
  #         --env TENABLE_SECRET_KEY=$TENABLE_SECRET_KEY \
  #         --env IMPORT_REPO_NAME=mass-pfml-api-pdf \
  #         --env CHECK_POLICY=true \
  #         --interactive tenableio-docker-consec-local.jfrog.io/cs-scanner:latest inspect-image mass-pfml-api-pdf:$DOCKER_TAG

  # Run Terriyay
  post-to-teams:
    name: Post end status to teams
    needs: [test]
    if: always() && github.ref == 'refs/heads/main'
    runs-on: [self-hosted, ecs]
    steps:
      - uses: actions/checkout@v4
      - name: Set end-state value
        id: end-state
        run: |
          if ${{ contains(needs.*.result, 'failure') }}; then echo "end-state=failure" >> $GITHUB_OUTPUT && echo "theme-color=FF0000" >> $GITHUB_OUTPUT
          elif ${{ contains(needs.*.result, 'success') }}; then echo "end-state=success" >> $GITHUB_OUTPUT && echo "theme-color=2986CC" >> $GITHUB_OUTPUT
          fi

      - name: Post to Teams
        run: |
          RESPONSE=$(curl -fsLS -X POST ${{ secrets.TEAMS_URI }} \
          --header 'Content-Type: application/json' \
          --data '{
            "@type": "MessageCard",
            "themeColor": "${{ steps.end-state.outputs.theme-color }}",
            "title": "API tests ${{ steps.end-state.outputs.end-state }}",
            "text": "API tests ${{ steps.end-state.outputs.end-state }} in main ${{ env.workflow_self_link }}"
            }'
          )
