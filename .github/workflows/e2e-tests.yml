#
# This workflow actually runs E2E tests: Cypress and Jest.
#
# It is a "reusable" workflow, meaning it can only be triggered as part of another workflow.
# It runs tests in parallel, and tests are split into four categories:
#   * Stable - tests that must pass before release.
#   * Unstable - tests that are advisory (for upcoming features) and should not block release.
#   * Ignored - tests that are not run at all in CI.
#   * Morning - tests that run to check features are working correctly.
# This name shouldn't matter, as it isn't displayed. We add "ZZ" to make it float to the bottom
# of the workflows list in case it somehow gets triggered directly.
name: "ZZ: E2E Tests"

on:
  # Note: Inputs must be kept in sync between workflow_call and workflow_run.
  workflow_call:
    inputs:
      target_environment:
        description: "Target Environment"
        default: "tst2"
        type: string
      cypress_tags:
        description: "Cypress Run Tags"
        required: false
        default: "Manual - Other"
        type: string
      title_override:
        description: "Title Override (replaces commit message in Cypress Dashboard)"
        type: string
        required: false
      run_deploy:
        description: "Run tests in deploy group"
        required: false
        default: true
        type: boolean
      run_unstable:
        description: "Run tests in unstable group"
        required: false
        default: false
        type: boolean
      run_morning:
        description: "Run tests in morning group"
        required: false
        default: false
        type: boolean
      run_cps:
        description: "Run tests in cps group"
        required: false
        default: false
        type: boolean
      run_playwright:
        description: "Run Playwright tests"
        required: false
        default: false
        type: boolean
      targeted_pattern:
        description: |
          Run a specific test, comma separated list of test (no spaces), or
          a blob (e.g. cypress/specs/deploy/**)
        required: false
        default: ""
        type: string
      record_video:
        description: "Save video to Cypress cloud"
        required: false
        default: false
        type: boolean

    secrets:
      # Note: this is EVERY secret we use in this workflow. It's critical that these line up with what's
      # in our actual secrets, so when this workflow is invoked using workflow_dispatch, we can still access
      # them.
      E2E_FINEOS_PASSWORD: { description: "", required: true }
      E2E_PORTAL_PASSWORD: { description: "", required: true }
      E2E_EMPLOYER_PORTAL_PASSWORD: { description: "", required: true }
      E2E_TESTMAIL_APIKEY: { description: "", required: true }
      E2E_SSO_PASSWORD: { description: "", required: true }
      E2E_SSO_USERNAME: { description: "", required: true }
      CYPRESS_RECORD_KEY: { description: "", required: true }
      CYPRESS_RECORD_KEY_UNSTABLE: { description: "", required: true }
      CYPRESS_RECORD_KEY_MORNING: { description: "", required: true }
      CYPRESS_RECORD_KEY_TARGETED: { description: "", required: true }
      CYPRESS_RECORD_KEY_CPS: { description: "", required: true }
      AWS_ECR_PW: { description: "", required: true }

defaults:
  run:
    working-directory: e2e

permissions:
  id-token: write
  contents: write
  pull-requests: read

env:
  E2E_ENVIRONMENT: ${{ inputs.target_environment }}
  COMMIT_INFO_MESSAGE: |
    ${{ inputs.title_override || format('Manual Run: {0}', inputs.target_environment) }}
  COMMIT_INFO_AUTHOR: ${{ github.actor }}
  COMMIT_INFO_BRANCH: ${{ github.ref_name }}
  # Setup any secret overrides that are necessary here.
  # Most other environment variables will come from /e2e/config.json,
  # as determined by the E2E_ENVIRONMENT variable.
  E2E_FINEOS_PASSWORD: ${{ secrets.E2E_FINEOS_PASSWORD }}
  E2E_PORTAL_PASSWORD: ${{ secrets.E2E_PORTAL_PASSWORD }}
  E2E_EMPLOYER_PORTAL_PASSWORD: ${{ secrets.E2E_EMPLOYER_PORTAL_PASSWORD }}
  E2E_TESTMAIL_APIKEY: ${{ secrets.E2E_TESTMAIL_APIKEY }}
  E2E_SSO_PASSWORD: ${{ secrets.E2E_SSO_PASSWORD }}
  E2E_SSO_USERNAME: ${{ secrets.E2E_SSO_USERNAME }}
  E2E_TAG: "${{ inputs.cypress_tags }},Env-${{ inputs.target_environment }}"
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  # Caching/build performance - Move Cypress, Playwright binary storage to .npm to leverage caching.
  CYPRESS_CACHE_FOLDER: ./.npm/.cypress-cache
  PLAYWRIGHT_BROWSERS_PATH: ./.npm/.playwright-cache
  PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: 1 # We never actually need puppeteer chromium installed
  TEST_RUN_ID: ${{ github.repository }}-${{ github.run_id }}-${{ github.run_attempt}}

# Prevent this workflow from running more than 1x concurrently in any environment.
# This prevents us from overloading the environment with requests, which causes false failures.
# Cypress & Flood share the same concurrency key to prevent E2E and Flood running concurrently.
concurrency: E2E-${{ inputs.target_environment }}-${{ github.workflow }}

jobs:
  get-e2e-enabled-environments:
    name: "Get E2E enabled environments"
    runs-on: [self-hosted, ecs]
    outputs:
      environments: ${{ steps.get-parameter.outputs.environments }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
          role-duration-seconds: 3600
      - name: Get parameter from parameter store
        id: get-parameter
        working-directory: /
        run: |
          E2E_ENVS=$(aws ssm get-parameter --name "/admin/e2e/environments" --output text --query "Parameter.Value")
          JSON_ARRAY=$(echo $E2E_ENVS | jq -c -R 'split(",")')
          echo "environments=$JSON_ARRAY" >> $GITHUB_OUTPUT

  # Health check job. Ensures Fineos is online before proceeding with testing. This saves resources
  # when we know we're going to get a high failure rate.
  health:
    name: Health Check ** ${{ inputs.target_environment }} **
    runs-on: [self-hosted, ecs]
    needs: [get-e2e-enabled-environments]
    if: contains(fromJson(needs.get-e2e-enabled-environments.outputs.environments), inputs.target_environment)
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - name: Check Fineos Online
        run: |
          fineos_url=$(cat config.json | jq -re '.["${{ env.E2E_ENVIRONMENT }}"].FINEOS_BASEURL')
          status_code=$(curl -s -o /dev/null -w "%{http_code}" "$fineos_url")
          echo "Received ${status_code} from ${fineos_url}"
          test "$status_code" -lt 500

  # Job to "warm-up" servers during morning runs. This allows e2e tests to not work off "cold starts" during morning runs.
  warm-up:
    name: Warm up PFML and Fineos servers
    runs-on: [self-hosted, ecs]
    needs: [health]
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v4
        if: ${{ inputs.cypress_tags == 'Morning Run'}}
      - name: Setup Node
        uses: actions/setup-node@v4
        with: { node-version: "20.x" }
        if: ${{ inputs.cypress_tags == 'Morning Run'}}
      - name: "Install Node Modules"
        uses: bahmutov/npm-install@v1
        with:
          working-directory: e2e
        if: ${{ inputs.cypress_tags == 'Morning Run'}}
      - name: Run submission
        run: E2E_ENVIRONMENT=${{inputs.target_environment}} node_modules/.bin/ts-node src/scripts/warm-up-pfml-fineos.ts
        if: ${{ inputs.cypress_tags == 'Morning Run'}}

  calculate-cypress-containers:
    name: Calculate Cypress containers
    runs-on: [self-hosted, ecs]
    needs: [get-e2e-enabled-environments]
    timeout-minutes: 5
    outputs:
      containers: ${{ steps.cypress-containers.outputs.containers }}
    defaults:
      run:
        shell: bash
        working-directory: ./
    steps:
      - uses: actions/checkout@v4
      - name: Calculate Cypress containers
        run: echo "containers=$(./bin/ci/calculate-cypress-containers.sh || echo '[1,2,3,4,5,6,7,8,9,10]')" >> $GITHUB_OUTPUT
        id: cypress-containers
        env:
          RUN_DEPLOY: ${{ inputs.run_deploy }}
          RUN_MORNING: ${{ inputs.run_morning }}
          RUN_UNSTABLE: ${{ inputs.run_unstable }}
          RUN_CPS: ${{ inputs.run_cps }}
          TARGETED_PATTERN: ${{ inputs.targeted_pattern }}
      - name: Print string of Cypress containers
        run: |
          echo "Cypress containers: ${{ steps.cypress-containers.outputs.containers }}"

  cypress:
    name: Cypress
    needs: [health, warm-up, calculate-cypress-containers]
    runs-on: [self-hosted, ec2]
    container:
      image: 498823821309.dkr.ecr.us-east-1.amazonaws.com/cypress/browsers:node-20.18.0-chrome-129.0.6668.89-1-ff-131.0.2-edge-129.0.2792.65-1
      credentials:
        username: AWS
        password: ${{ secrets.AWS_ECR_PW }}
    # At the job level, we extend timeout from 6 minutes to 70. We'll set tighter timeouts for steps below to prevent
    # hung tests from spinning endlessly.
    timeout-minutes: 70
    strategy:
      fail-fast: false
      matrix:
        containers: ${{ fromJson(needs.calculate-cypress-containers.outputs.containers) }}
    steps:
      - uses: actions/checkout@v4
      - name: Setup Tests
        uses: ./.github/actions/e2e-test-setup
        with:
          target-environment: ${{ inputs.target_environment }}

      # Run Cypress tests.
      - name: Cypress Tests (Deploy)
        uses: cypress-io/github-action@v6
        timeout-minutes: 25
        id: cypress-deploy
        with:
          spec: "cypress/specs/deploy/**"
          record: true
          parallel: true
          install: false
          working-directory: e2e
          ci-build-id: ${{ env.TEST_RUN_ID }}
          group: Deploy
          tag: ${{ env.E2E_TAG }}
          config-file: cypress.config.ts
          # With this action, we need to force triggering Cypress via CLI rather than API to fix a node version
          # mixup. See https://github.com/cypress-io/github-action/issues/489
          command-prefix: "--"
          browser: chrome
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          CYPRESS_PROJECT_ID: "wjoxhr"
          CYPRESS_CONTAINER_ID: ${{ matrix.containers }}
        if: inputs.run_deploy

      # Run Cypress tests.
      - name: Cypress Tests (Unstable)
        uses: cypress-io/github-action@v6
        id: cypress-unstable
        with:
          spec: "cypress/specs/unstable/**"
          record: true
          parallel: true
          working-directory: e2e
          ci-build-id: ${{ env.TEST_RUN_ID }}
          group: Unstable
          install: false
          tag: ${{ env.E2E_TAG }}
          config: trashAssetsBeforeRuns=false,video=${{ inputs.record_video }}
          config-file: cypress.config.ts
          # With this action, we need to force triggering Cypress via CLI rather than API to fix a node version
          # mixup. See https://github.com/cypress-io/github-action/issues/489
          command-prefix: "--"
          browser: chrome
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY_UNSTABLE }}
          CYPRESS_PROJECT_ID: "2wzg2w"
          CYPRESS_CONTAINER_ID: ${{ matrix.containers }}
        # Always run this step if requested, even if stable tests fail.
        if: always() && inputs.run_unstable
        # Do not allow this step to fail the job. It is informational, not mandatory.
        continue-on-error: true
        # Setting a step timeout here prevents a hung unstable test from timing out at the job level - it times out at
        # the step level instead, allowing continue-on-error to do its job.
        timeout-minutes: 15

      # Run Morning Cypress tests (with the Morning runs).
      - name: Cypress Tests (Morning)
        uses: cypress-io/github-action@v6
        timeout-minutes: 25
        id: cypress-morning
        with:
          spec: "cypress/specs/morning/**"
          record: true
          parallel: true
          working-directory: e2e
          group: Morning
          ci-build-id: ${{ env.TEST_RUN_ID }}
          install: false
          tag: ${{ env.E2E_TAG }}
          config: trashAssetsBeforeRuns=false,video=${{ inputs.record_video }}
          config-file: cypress.config.ts
          # With this action, we need to force triggering Cypress via CLI rather than API to fix a node version
          # mixup. See https://github.com/cypress-io/github-action/issues/489
          command-prefix: "--"
          browser: chrome
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY_MORNING }}
          CYPRESS_PROJECT_ID: "tvt4k6"
          CYPRESS_CONTAINER_ID: ${{ matrix.containers }}
        # Always run this step if requested, even if stable tests fail.
        if: always() && inputs.run_morning

      # Run Cypress tests
      - name: Cypress Tests (CPS)
        uses: cypress-io/github-action@v6
        timeout-minutes: 15
        id: cypress-cps
        with:
          spec: |
            cypress/specs/cps/feature/**
            cypress/specs/cps/scenario/**
          record: true
          parallel: true
          working-directory: e2e
          group: cps
          ci-build-id: ${{ env.TEST_RUN_ID }}
          install: false
          tag: ${{ env.E2E_TAG }}
          config: trashAssetsBeforeRuns=false,video=${{ inputs.record_video }}
          config-file: cypress.config.ts
          # With this action, we need to force triggering Cypress via CLI rather than API to fix a node version
          # mixup. See https://github.com/cypress-io/github-action/issues/489
          command-prefix: "--"
          browser: chrome
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY_CPS }}
          CYPRESS_PROJECT_ID: "oowxt1"
          CYPRESS_CONTAINER_ID: ${{ matrix.containers }}
        if: inputs.run_cps

      # Run Cypress tests (Targeted).
      - name: Cypress Tests (Targeted)
        id: cypress-targeted
        uses: cypress-io/github-action@v6
        timeout-minutes: 20
        with:
          spec: ${{ inputs.targeted_pattern }}
          record: true
          parallel: true
          install: false
          working-directory: e2e
          ci-build-id: ${{ env.TEST_RUN_ID }}
          group: Targeted
          tag: ${{ env.E2E_TAG }}
          config: trashAssetsBeforeRuns=false,video=${{ inputs.record_video }}
          config-file: cypress.config.ts
          # With this action, we need to force triggering Cypress via CLI rather than API to fix a node version
          # mixup. See https://github.com/cypress-io/github-action/issues/489
          command-prefix: "--"
          browser: chrome

        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY_TARGETED }}
          CYPRESS_PROJECT_ID: "98snsi"
          CYPRESS_CONTAINER_ID: ${{ matrix.containers }}
        if: inputs.targeted_pattern != ''

      - name: "Archive screenshots"
        uses: actions/upload-artifact@v4
        if: always()
        with:
          # See https://github.com/actions/upload-artifact?tab=readme-ov-file#not-uploading-to-the-same-artifact
          # for an explanation of why we use a different name for each instance.
          name: cypress-screenshots ${{ matrix.containers }}
          path: e2e/cypress/playwright-screenshots
          if-no-files-found: "ignore"
          retention-days: ${{ vars.ARTIFACT_RETENTION_DAYS }}

  collect-failed-specs:
    name: Collect failed specs
    runs-on: [self-hosted, ec2]
    outputs:
      failed-specs: ${{ steps.get-failed.outputs.failed_specs }}
      failed-specs-count: ${{ steps.get-failed.outputs.failed_specs_count }}
      containers: ${{ steps.failed-specs-cypress-containers.outputs.containers }}
    timeout-minutes: 5
    defaults:
      run:
        working-directory: ./
    needs: [health, cypress]
    if: always() && needs.cypress.result == 'failure' && inputs.cypress_tags != 'PR'
    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
          role-duration-seconds: 3600

      - name: Setup & Configure New Relic CLI
        uses: ./.github/actions/setup-nr-cli

      - name: Query NR to get list of failed specs
        run: ./bin/ci/get-list-failed-specs.sh ${{ env.E2E_ENVIRONMENT }} ${{ env.TEST_RUN_ID }}
        shell: bash
        id: get-failed

      - name: Print string of failed specs for auto trigger
        run: |
          echo "Previous run had failure[s] w/the following runId: ${{ env.TEST_RUN_ID }}"
          echo "List of failed specs: ${{ steps.get-failed.outputs.failed_specs }}"
          echo "Count of failed specs: ${{ steps.get-failed.outputs.failed_specs_count }}"

      - name: More than 10 failures detected (ABORTING RE-RUN)
        run: echo "::error::More than 10 specs failed - aborting auto re-run!" && exit 1
        if: steps.get-failed.outputs.failed_specs_count > 10

      - name: Calculate Cypress containers
        run: echo "containers=$(./bin/ci/calculate-cypress-containers.sh || echo '[1,2,3,4,5]')" >> $GITHUB_OUTPUT
        id: failed-specs-cypress-containers
        env:
          RUN_DEPLOY: false
          RUN_MORNING: false
          RUN_UNSTABLE: false
          RUN_CPS: false
          TARGETED_PATTERN: ${{ steps.get-failed.outputs.failed_specs }}
      - name: Print string of Cypress containers
        run: |
          echo "Cypress containers: ${{ steps.failed-specs-cypress-containers.outputs.containers }}"

  # Run "failed" specs
  run-failed-specs:
    name: Re-run failed specs
    runs-on: [self-hosted, ec2]
    container:
      image: 498823821309.dkr.ecr.us-east-1.amazonaws.com/cypress/browsers:node-20.18.0-chrome-129.0.6668.89-1-ff-131.0.2-edge-129.0.2792.65-1
      credentials:
        username: AWS
        password: ${{ secrets.AWS_ECR_PW }}
    env:
      COMMIT_INFO_MESSAGE: |
        ${{ inputs.title_override || format('Manual Run: {0}', inputs.target_environment) }}-Auto Retriggered
    timeout-minutes: 30
    strategy:
      fail-fast: false
      matrix:
        containers: ${{ fromJson(needs.collect-failed-specs.outputs.containers) }}
    defaults:
      run:
        working-directory: ./
        shell: bash
    needs: [health, cypress, collect-failed-specs]
    if: always() && needs.cypress.result == 'failure' && inputs.cypress_tags != 'PR' && needs.collect-failed-specs.result != 'failure' && needs.collect-failed-specs.outputs.failed-specs-count != 0
    steps:
      - uses: actions/checkout@v4
      - name: Setup Tests
        uses: ./.github/actions/e2e-test-setup
        with:
          target-environment: ${{ inputs.target_environment }}

      - name: Cypress Tests (Auto Re-run - failed specs)
        id: run-failed
        uses: cypress-io/github-action@v6
        timeout-minutes: 15
        with:
          spec: ${{ needs.collect-failed-specs.outputs.failed-specs }}
          record: true
          parallel: true
          install: false
          working-directory: e2e
          ci-build-id: ${{ env.TEST_RUN_ID }}-failed-specs
          group: Targeted
          tag: ${{ env.E2E_TAG }}
          # With this action, we need to force triggering Cypress via CLI rather than API to fix a node version
          # mixup. See https://github.com/cypress-io/github-action/issues/489
          command-prefix: "--"
          browser: chrome
          config: trashAssetsBeforeRuns=false,video=${{ inputs.record_video }}
          config-file: cypress.config.ts
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY_TARGETED }}
          CYPRESS_PROJECT_ID: "98snsi"
          CYPRESS_CONTAINER_ID: ${{ matrix.containers }}

  playwright:
    name: Playwright
    runs-on: [self-hosted, ec2]
    needs: [health, warm-up]
    if: ${{ inputs.run_playwright }}
    steps:
      - uses: actions/checkout@v4
      - name: Setup Tests
        uses: ./.github/actions/e2e-test-setup
        with:
          target-environment: ${{ inputs.target_environment }}

      - name: Install Playwright
        run: npx playwright install --with-deps chrome

      - name: Playwright Tests
        timeout-minutes: 60
        run: npm run playwright test

  # Run Integration Tests.
  integration:
    name: Integration
    needs: [health, warm-up]
    runs-on: [self-hosted, ecs]
    steps:
      - uses: actions/checkout@v4
      - name: Setup Tests
        uses: ./.github/actions/e2e-test-setup
        with:
          target-environment: ${{ inputs.target_environment }}

      - name: Integration Tests
        timeout-minutes: 60
        run: npm run test:integration:ci -- --group=stable${{ inputs.run_unstable && ' --group=unstable' || '' }}${{ inputs.run_morning && ' --group=morning' || '' }} --forceExit

      - name: "Archive screenshots"
        uses: actions/upload-artifact@v4
        if: always()
        with:
          # See https://github.com/actions/upload-artifact?tab=readme-ov-file#not-uploading-to-the-same-artifact
          # for an explanation of why we use a different name for each instance.
          name: integration-screenshots
          path: e2e/test/playwright-screenshots
          if-no-files-found: "ignore"
          retention-days: ${{ vars.ARTIFACT_RETENTION_DAYS }}

    if: ${{ inputs.targeted_pattern == '' }}
