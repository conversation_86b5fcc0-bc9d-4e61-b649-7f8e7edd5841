#
# This file contains the **trigger** for the E2E suite, not the tests themselves.
#
# It exists to provide a way to trigger the E2E workflow using workflow_dispatch rather than workflow_call.
# We provide this for three reasons:
#    1. Allowing manual triggers through the Github UI.
#    2. Allowing external dispatch through the Github API.
#    3. Legacy implementations in the deploy workflows that still use workflow_dispatch.
#
# @todo: Rename this file to e2e-dispatch.yml once we've cleared up the legacy implementations.

name: E2E Test Suite
run-name: E2E Test Suite (${{inputs.target_environment}})

on:
  # Manual runs.
  workflow_dispatch:
    inputs:
      target_environment:
        description: |
          🛑 WARNING:
            This list is no longer being edited when enabling/disabling environments for testing.
            Selecting a disabled environment will result in a run ending early.
          ---------------------
          ⬇️ Target Environment:
        required: true
        default: "tst2"
        type: choice
        # Note: Infra-test is intentionally not E2E tested, as it shares a Fineos instance.
        options:
          - tst1
          - tst2
          - tst3
          - uat
          - breakfix
          - performance
          - training
          - trn2
      cypress_tags:
        description: "Cypress Run Tags"
        required: false
        default: "Manual - Other"
        type: choice
        options:
          - Manual - PR Re-run
          - Manual - PR Env Check
          - Manual - Post Morning Run Check
          - Manual - Post API Deployment Check
          - Manual - Post Portal Deployment Check
          - Manual - Post Fineos Deployment Check
          - Manual - Fineos Trigger
          - Manual - Environment Sanity Check
          - Manual - New Dataset Check
          - Manual - Web Portal Flow Content Check
          - Manual - Post Database Refresh Check
          - Manual - Targeted Specs Check
          - Manual - Other
          - Manual - PR Re-run (all envs)
          - Manual - PR Env Check (all envs)
          - Manual - Post Morning Run Check (all envs)
          - Manual - Environment Sanity Check (all envs)
          - Manual - New Dataset Check (all envs)
          - Manual - Targeted Specs Check (all envs)
          - Manual - Other (all envs)
          - Deploy,Deploy-Fineos
          - Deploy,Deploy-API
          - Deploy,Deploy-Portal
          - Deploy,Deploy-Infra
          - Morning Run
          - Morning Run (Sunday Adhoc)
          - PR
      title_override:
        description: "Title Override (replaces commit message in Cypress Dashboard)"
        required: false
      run_deploy:
        description: |
          Run tests in deploy group. "DEPLOY" group is selected by
          default. This includes a core suite of tests that are needed to pass
          prior to deployments.
        required: false
        default: true
        type: boolean
      run_morning:
        description: "Run tests in morning group"
        required: false
        default: false
        type: boolean
      run_cps:
        description: "Run tests in cps group"
        required: false
        default: false
        type: boolean
      run_unstable:
        description: "Run tests in unstable group"
        required: false
        default: false
        type: boolean
      run_playwright:
        description: "Run Playwright tests"
        required: false
        default: false
        type: boolean
      targeted_pattern:
        description: |
          Run a specific test, comma separated list of test (no spaces), or
          a blob (e.g. cypress/specs/deploy/**)
        required: false
        default: ""
        type: "string"
      record_video:
        description: "Save video to Cypress cloud"
        required: false
        default: false
        type: boolean

jobs:
  trigger:
    name: E2E Tests
    # This workflow triggers tests from the same branch/commit it was triggered on.
    # When we reuse this snippet from deployments, we will likely want to use the main branch.
    uses: ./.github/workflows/e2e-tests.yml
    with:
      target_environment: ${{ github.event.inputs.target_environment }}
      cypress_tags: ${{ github.event.inputs.cypress_tags }}
      run_deploy: ${{ github.event.inputs.run_deploy == 'true' }}
      run_unstable: ${{ github.event.inputs.run_unstable == 'true' }}
      run_morning: ${{ github.event.inputs.run_morning == 'true' }}
      run_cps: ${{ github.event.inputs.run_cps == 'true' }}
      run_playwright: ${{ github.event.inputs.run_playwright == 'true' }}
      title_override: ${{ github.event.inputs.title_override }}
      targeted_pattern: ${{ github.event.inputs.targeted_pattern }}
      record_video: ${{ github.event.inputs.record_video == 'true' }}
    secrets: inherit
