# End to End testing CI processes.
#
name: E2E CI Testing

on:
  pull_request:
    paths:
      - e2e/**
      - .github/workflows/e2e-ci.yml
defaults:
  run:
    working-directory: e2e

env:
  CYPRESS_CACHE_FOLDER: /home/<USER>/.npm/.cypress-cache
  PLAYWRIGHT_BROWSERS_PATH: /home/<USER>/.npm/.playwright-cache
  PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: 1 # We never actually need puppeteer chromium installed.
  E2E_TESTMAIL_APIKEY: ${{ secrets.E2E_TESTMAIL_APIKEY }}

jobs:
  lint:
    name: Lint
    runs-on: [self-hosted, ecs]
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with: { node-version: "20.x" }
      - name: "Install Node Modules"
        uses: bahmutov/npm-install@v1
        with:
          working-directory: |
            e2e
            e2e/nerdpack
      - name: "Lint"
        run: npm run lint:ci
      - name: "Check Format"
        run: npm run format-check:all

  test:
    name: "Unit Tests"
    runs-on: [self-hosted, ecs]
    env:
      E2E_ENVIRONMENT: tst2
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with: { node-version: "20.x" }
      - name: "Install Node Modules"
        uses: bahmutov/npm-install@v1
        with:
          working-directory: e2e
      - name: "Run Jest"
        run: npm run test:unit

  changed-files:
    name: "Check Changed Files"
    runs-on: [self-hosted, ecs]
    outputs:
      has-artillery-changes: ${{ steps.artillery-changed-files.outputs.any_changed }}
      has-cypress-changes: ${{ steps.cypress-changed-files.outputs.any_changed }}
      has-playwright-changes: ${{ steps.playwright-changed-files.outputs.any_changed }}
      has-nerdpack-changes: ${{ steps.nerdpack-changed-files.outputs.any_changed }}
    steps:
      - uses: actions/checkout@v4
      - name: "Check if Artillery files have been changed"
        id: artillery-changed-files
        uses: tj-actions/changed-files@v46
        with:
          files: |
            e2e/src/artillery/**
            e2e/src/submission/**
            e2e/package.json
            e2e/Dockerfile
      - name: "Check if Cypress files have been changed"
        id: cypress-changed-files
        uses: tj-actions/changed-files@v46
        with:
          files: |
            e2e/cypress/**
            e2e/package.json
      - name: "Check if Playwright files have been changed"
        id: playwright-changed-files
        uses: tj-actions/changed-files@v46
        with:
          files: |
            e2e/playwright/**
            e2e/package.json
      - name: "Check if Nerdpack files have been changed"
        id: nerdpack-changed-files
        uses: tj-actions/changed-files@v46
        with:
          files: |
            e2e/nerdpack/nerdlets/**
            e2e/nerdpack/types/**
            e2e/nerdpack/tsconfig.json

  # @todo: reinstate after vulnerability in https://lwd.atlassian.net/browse/PFMLPB-13615 is addressed
  # check-artillery:
  #   name: "Run docker and build image for artillery deploy"
  #   runs-on: ubuntu-latest
  #   needs: changed-files
  #   env:
  #     E2E_ENVIRONMENT: performance
  #     E2E_FINEOS_PASSWORD: ${{ secrets.E2E_FINEOS_PASSWORD }}
  #     E2E_PORTAL_PASSWORD: ${{ secrets.E2E_PORTAL_PASSWORD }}
  #     E2E_EMPLOYER_PORTAL_PASSWORD: ${{ secrets.E2E_EMPLOYER_PORTAL_PASSWORD }}
  #     E2E_TESTMAIL_APIKEY: ${{ secrets.E2E_TESTMAIL_APIKEY }}
  #     E2E_FINEOS_USERS: ${{ secrets.E2E_FINEOS_USERS }}
  #   if: needs.changed-files.outputs.has-artillery-changes == 'true'
  #   steps:
  #     - uses: actions/checkout@v4
  #     - name: "Build the Docker Image ..."
  #       run: docker build --rm -f ./Dockerfile ../e2e

  trigger-cypress:
    name: End-to-End Tests
    # Note: While we'd love to convert this to use workflow_call, we can't right now due to a bug in
    # actions where you can't run a reusable action from the current commit if you're responding to a PR event.
    # See https://github.community/t/ref-head-in-reusable-workflows/203690/70
    runs-on: [self-hosted, ecs]
    needs: changed-files
    if: ( needs.changed-files.outputs.has-cypress-changes == 'true' || needs.changed-files.outputs.has-playwright-changes == 'true' ) && github.event.pull_request.draft == false
    steps:
      - name: Trigger E2E Workflow
        uses: lasith-kg/dispatch-workflow@v2
        with:
          dispatch-method: workflow_dispatch
          repo: pfml
          owner: EOLWD
          workflow: e2e-cypress.yml
          token: ${{ secrets.PFML_DEVOPS_TOKEN }}
          ref: ${{ github.event.pull_request.head.ref }}
          workflow-inputs: |
            {
              "target_environment": "${{ vars.CI_ENV }}",
              "cypress_tags": "PR",
              "run_playwright": "${{ needs.changed-files.outputs.has-playwright-changes == 'true' }}",
              "run_deploy": "${{ needs.changed-files.outputs.has-cypress-changes == 'true' }}",
              "run_unstable": "false",
              "run_morning": "false",
              "run_cps": "false",
              "targeted_pattern": "",
              "title_override": ${{ toJSON(format('PR: {0}', github.event.pull_request.title)) }}
            }
