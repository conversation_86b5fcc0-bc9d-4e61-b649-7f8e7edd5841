name: Infra Shadow Prod Task with Feature Branch

run-name: ${{ github.workflow }} - ${{ github.event.inputs.task }} 

on:
  workflow_dispatch:
    inputs:
      task:
        required: true
        description: |
          ⬆️ Change 'main' to the feature branch you want to use. The image will be built from this branch.
          --------------------------------------
          ⬇️ Task Name
          e.g. pub-payments-process-fineos
        type: string
      shadow-db:
        required: true
        type: boolean
        default: false
        description: |
          ⬇️ Create Shadow Database?
          🤔 If not selected, no shadow db is created and the below inputs are ignored. Name is automatically generated and provided upon workflow success.
          --------------------------------------
      date_time:
        default: "latest"
        type: string
        description: |
          ⬇️ Restore date and time
          🤔 latest or yyyy-mm-ddThh:mm:ssZ
          --------------------------------------
      storage_type:
        required: true
        default: "gp3"
        type: choice
        options:
          - gp3
          - io2
        description: |
          ⬇️ Storage Type. Keep it on gp3 unless you explicitly need io2.
          --------------------------------------
      instance_size:
        required: true
        default: "db.r5.xlarge"
        type: choice
        options:
          - db.r5.xlarge
          - db.r5.2xlarge
        description: |
          ⬇️ Instance Size. Keep it on db.r5.xlarge unless you explicitly need db.r5.2xlarge.
          --------------------------------------
  schedule:
    - cron: '0 0 * * 0' # Every Sunday at midnight

env:
  workflow_self_link: "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_number }}"

defaults:
  run:
    working-directory: ./bin/aws/shadow/create_shadow_prod_task_from_feature_branch

permissions:
  id-token: write
  contents: read
  actions: write

jobs:
  commit-hash:
    if: github.event_name == 'workflow_dispatch'
    name: Get commit hash
    runs-on: [self-hosted, ecs]
    environment: Shadow
    outputs:
      commit_hash: ${{ steps.calculate_commit_hash.outputs.commit_hash }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
          ref: ${{ github.ref_name }}

      - name: Calculate commit hash
        id: calculate_commit_hash
        run: |
          commit_hash=$(git rev-parse HEAD)
          echo "Received branch ${{ github.ref_name }}"

          echo "commit_hash=${commit_hash}" >> $GITHUB_OUTPUT

  shadow-db:
    if: (github.event_name == 'workflow_dispatch' && github.event.inputs.shadow-db == 'true')
    needs: commit-hash
    name: Create Shadow DB
    uses: ./.github/workflows/infra-shadow-db-create-destroy.yml
    with:
      action: "create"
      environ: "prod"
      date_time: ${{ inputs.date_time }}
      name: "massgov-pfml-prod-adhoc-${{ github.run_number }}-shadow"
      storage_type: ${{ inputs.storage_type }}
      instance_size: ${{ inputs.instance_size }}
      skip_approval: true
    secrets: inherit

  image-build:
    if: github.event_name == 'workflow_dispatch'
    name: Build API Docker Image
    needs: commit-hash
    runs-on: [self-hosted, ec2]
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
          ref: ${{ needs.commit-hash.outputs.commit_hash }}

      - name: Build API Image
        uses: ./.github/actions/build-image
        with:
          image-name: ${{ needs.commit-hash.outputs.commit_hash }}
          repo: pfml-api
          working-dir: api
          build-args: |
            RUN_USER=container
            RUN_UID=4000
          target: app

  create-task:
    if: github.event_name == 'workflow_dispatch'
    name: Create ECS Task Definition
    needs: [commit-hash, image-build]
    runs-on: [self-hosted, ecs]
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
          ref: ${{ needs.commit-hash.outputs.commit_hash }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::************:role/ci-run-deploys-oidc
          role-duration-seconds: 3600
      
      - name: Create ECS Task Definition
        run: |
          pip install boto3
          python3 -u create_shadow_prod_task_from_feature_branch.py \
            --task ${{ github.event.inputs.task }} \
            --image ${{ needs.commit-hash.outputs.commit_hash }} \
            --run_number ${{ github.run_number }} \
            --action create

  cleanup:
    name: Remove Shadow Adhoc Tasks
    if: github.event_name == 'schedule'
    runs-on: [self-hosted, ecs]
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
          ref: ${{ github.ref_name }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::************:role/ci-run-deploys-oidc
          role-duration-seconds: 3600
      
      - name: Remove Shadow Adhoc Tasks
        run: |
          pip install boto3
          python3 -u create_shadow_prod_task_from_feature_branch.py --action cleanup

