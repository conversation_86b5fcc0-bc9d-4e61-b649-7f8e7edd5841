# This workflow can do 2 things:
#   1. Create a prod or test DB from latest restorable time or a point in time. https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_PIT.html
#       - This can take anywhere from 30min to 3hrs to complete
#   2. Destroy one or more shadow DBs
#       - Separate each shadow DB name by a space

name: Infra Shadow DB Create or Destroy

run-name: ${{ github.workflow }} - ${{ github.event.inputs.action }} - ${{ github.event.inputs.name }}

on:
  workflow_dispatch:
    inputs:
      action:
        required: true
        default: "create"
        description: |
          🛑 DO NOT CHANGE THE DROPDOWN ABOVE FOR THE WORKFLOW. KEEP IT ON "main" BRANCH.
          --------------------------------------
          ⬇️ Action
        type: choice
        options:
          - create
          - destroy
      environ:
        required: true
        default: "prod"
        description: |
          ⬇️ Environment
        type: choice
        options:
          - tst2
          - prod
      date_time:
        required: true
        default: "latest"
        description: |
          ⬇️ Date and Time
          🤔 latest or yyyy-mm-ddThh:mm:ssZ
        type: string
      name:
        required: true
        default: "massgov-pfml-<env>-<date>-shadow"
        description: |
          ⬇️ Shadow DB Name
          🤔 Must end with "-shadow"
        type: string
      storage_type:
        required: true
        default: "gp3"
        description: |
          ⬇️ Storage Type. Keep it on gp3 unless you explicitly need io2.
        type: choice
        options:
          - gp3
          - io2
      instance_size:
        required: true
        default: "db.r5.xlarge"
        description: |
          ⬇️ Instance Size. Keep it on db.r5.xlarge unless you explicitly need db.r5.2xlarge.
        type: choice
        options:
          - db.r5.xlarge
          - db.r5.2xlarge
  workflow_call:
    inputs:
      action:
        required: true
        type: string
      environ:
        required: true
        type: string
      date_time:
        required: true
        type: string
      name:
        required: true
        type: string
      storage_type:
        required: true
        type: string
      instance_size:
        required: true
        type: string
      skip_approval:
        required: false
        type: boolean
        default: false

env:
  workflow_self_link: "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"

defaults:
  run:
    working-directory: ./bin/aws/shadow/create_destroy_shadow_db

permissions:
  id-token: write
  contents: read

jobs:
  shadow:
    name: ${{ inputs.action }} Shadow DB
    runs-on: [self-hosted, ec2]
    environment: ${{ !inputs.skip_approval && 'Shadow' || '' }}

    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
          role-duration-seconds: 21600 # 6hrs 
      
      - name: Run Shadow DB ${{ inputs.action }}
        id: input-state
        run: |
          pip install boto3
          python3 -u create_destroy_shadow.py --action ${{ inputs.action }} --env ${{ inputs.environ }} --date_time ${{ inputs.date_time }} --name ${{ inputs.name }} --storage_type ${{ inputs.storage_type }} --instance_size ${{ inputs.instance_size }}

  post-to-teams:
    name: Post end status to teams
    needs: [shadow]
    if: always()
    runs-on: [self-hosted, ec2]
    steps:
      - uses: actions/checkout@v4
      
      - name: Set end-state value
        id: end-state
        run: |
          if ${{ contains(needs.*.result, 'failure') }}; then echo "end-state=failure" >> $GITHUB_OUTPUT && echo "theme-color=FF0000" >> $GITHUB_OUTPUT
          elif ${{ contains(needs.*.result, 'success') }}; then echo "end-state=success" >> $GITHUB_OUTPUT && echo "theme-color=2986CC" >> $GITHUB_OUTPUT
          else echo "end-state=skipped" >> $GITHUB_OUTPUT
          fi

      - name: Post to Teams
        run: |
          RESPONSE=$(curl -fsLS -X POST ${{ secrets.TEAMS_URI }} \
          --header 'Content-Type: application/json' \
          --data '{
            "@type": "MessageCard",
            "themeColor": "${{ steps.end-state.outputs.theme-color }}",
            "title": "Infra Shadow DB ${{ inputs.action }} ${{ steps.end-state.outputs.end-state }}",
            "text": "[View run details](${{ env.workflow_self_link }})"
            }'
          )