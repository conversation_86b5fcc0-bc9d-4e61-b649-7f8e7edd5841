#
# This workflow triggers on PR approval with comment '/publish-nerdpack'
# Automatically merge latest changes from main, publish nerdpack, and commit and push to current PR branch
#
name: Automated Nerdpack Publication
on:
  pull_request_review:
    types: [submitted]

permissions:
  id-token: write
  contents: write
  pull-requests: read

defaults:
  run:
    working-directory: e2e/nerdpack

jobs:
  publish-and-commit-nerdpack-updates:
    runs-on: [self-hosted, ecs]
    if: >
      github.event.review.state == 'approved' &&
      contains(github.event.review.body, '/publish-nerdpack')

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{github.event.pull_request.head.ref}}
          repository: ${{github.event.pull_request.head.repo.full_name}}
          persist-credentials: false

      - name: Setup Node
        uses: actions/setup-node@v4
        with: { node-version: "20.x" }

        # Runs in root by default so needs working-directory
      - name: Install Node Modules
        uses: bahmutov/npm-install@v1
        with:
          working-directory: e2e/nerdpack

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::************:role/ci-run-deploys-oidc
          role-duration-seconds: 3600

      - name: Configure PFML DevOps Github Account
        run: |
          git config user.name "PFMLDevOps"
          git config user.email "<EMAIL>"
          git remote set-url origin https://x-access-token:${GITHUB_TOKEN}@github.com/${{ github.repository }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

        # Merge from main first to avoid version conflicts
      - name: Merge Latest Changes From Main
        run: |
          git fetch origin main
          git merge origin/main --no-edit

      - name: Pull New Relic parameters from SSM Parameter Store
        uses: "dkershner6/aws-ssm-getparameters-action@v2"
        with:
          parameterPairs: "/admin/e2e/newrelic-nerdpack-api-key = NEW_RELIC_API_KEY"

      - name: Install New Relic One CLI
        run: |
          curl -s https://cli.nr-ext.net/installer.sh | sudo bash
          nr1 --version

      - name: Set New Relic Profile & Publish Nerdpack
        run: nr1 profiles:add --name pfml --api-key "${NEW_RELIC_API_KEY}" --region us && npm run publish
        env:
          NEW_RELIC_API_KEY: ${{ env.NEW_RELIC_API_KEY }}

      - name: Commit and Push Updates
        run: |
          git add package.json package-lock.json
          git commit -m "Automated package.json and package-lock.json updates"          
          git push origin "${HEAD_REF}"
        env:
          HEAD_REF: ${{ github.event.pull_request.head.ref }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
