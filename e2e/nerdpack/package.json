{"private": true, "name": "pfml-nr-nerdpack", "version": "2.0.158", "scripts": {"compile:check": "tsc --noEmit", "start": "nr1 nerdpack:serve", "test": "exit 0", "publish": "npm run compile:check && npm version patch && nr1 nerdpack:publish --tag=DEV ", "publish:dry": "nr1 nerdpack:publish --dry-run"}, "nr1": {"uuid": "1561bd97-7e69-4def-9d9c-29156cb5c149"}, "dependencies": {"@newrelic/nr-labs-components": "^1.23.5", "ansi-regex": "^6.1.0", "clipboard-polyfill": "^4.1.1", "date-fns": "^2.29.3", "dompurify": "^3.2.4", "lodash": "^4.17.21", "prop-types": "^15.6.2", "react": "^18.3.1", "react-dom": "^18.3.1"}, "browserslist": ["last 2 versions", "not ie < 11", "not dead"], "devDependencies": {"@types/react": "^16.14.0", "typescript": "^4.9.4"}}