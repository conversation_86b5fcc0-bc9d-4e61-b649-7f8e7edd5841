import React, { useState } from "react";

import ClipboardButton from "../components/ClipboardButton";
import TestGridHeader, { TestGridHeaderProps } from "./TestGridHeader";
import TestGridRowDetails, {
  TestGridRowDetailsProps,
} from "./TestGridRowDetails";

export interface TestGridRowProps {
  readonly fileName: string;
  readonly groupName: keyof DashboardUrlMap;
  readonly testFile: TestFile;
  readonly testSuiteRuns: readonly TestSuiteRun[];
}

interface TestFile {
  readonly runs: Record<string, TestFileRun>;
  readonly testCases: Record<string, TestCase>;
}

interface TestCase {
  readonly order: number;
  readonly runs: Record<string, TestCaseRun>;
}

interface TestCaseRun {
  readonly messages: TestGridRowDetailsProps["messages"];
  readonly status: TestGridRowDetailsProps["status"];
}

interface TestFileRun {
  readonly fail: number;
  readonly pass: number;
  readonly percent: number;
  readonly skip: number;
  readonly status: "fail" | "pass" | "skip" | null;
  readonly testCaseCount: number;
  readonly tryCount: number;
}

interface TestSuiteRun {
  readonly computeTimeMs: TestGridHeaderProps["computeTimeMs"];
  readonly dashboardUrls: DashboardUrlMap;
  readonly endTime: TestGridHeaderProps["endTime"];
  readonly environment: TestGridHeaderProps["environment"];
  readonly id: TestGridHeaderProps["runId"];
  readonly startTime: TestGridHeaderProps["startTime"];
  readonly tags: TestGridHeaderProps["tags"];
}

type DashboardUrlMap = Record<string, TestGridHeaderProps["dashboardUrl"]>;

export default function TestGridRow(props: TestGridRowProps) {
  const [isOpen, setIsOpen] = useState(false);
  const toggleIsOpen = () => setIsOpen((prevIsOpen) => !prevIsOpen);
  const firstTestSuiteRun = props.testSuiteRuns[0];
  const intentionalSkips = countIntentionalSkips(props);
  if (firstTestSuiteRun === undefined) {
    return null; // No runs
  }

  const firstTestFileRun = props.testFile.runs[firstTestSuiteRun.id];
  const progressClassName = createProgressClassName(
    intentionalSkips,
    firstTestFileRun
  );

  return (
    <>
      <tr className="highlight" onClick={toggleIsOpen}>
        <td className={`filename ${progressClassName}`}>
          <div className="filename__content">
            <ClipboardButton text={props.fileName} />
            {props.fileName}
          </div>
        </td>
        {props.testSuiteRuns.map((run) => {
          const testFileRun = props.testFile.runs[run.id];
          const progressClassName = createProgressClassName(
            intentionalSkips,
            testFileRun
          );
          return (
            <React.Fragment key={run.id}>
              <td />
              <td>
                <div
                  style={{ display: "flex", flexDirection: "row" }}
                  className={`runProgress ${progressClassName}`}
                >
                  <div
                    className={`progress ${progressClassName}`}
                    style={createProgressStyle(intentionalSkips, testFileRun)}
                  >
                    {createProgressDisplay(intentionalSkips, testFileRun)}
                  </div>
                </div>
              </td>
            </React.Fragment>
          );
        })}
      </tr>
      <tr className={isOpen ? "open" : "closed"}>
        <td className="subTable" colSpan={props.testSuiteRuns.length * 2 + 2}>
          <table className={"runDetails"}>
            {props.testSuiteRuns.length > 1 && (
              <thead>
                <tr>
                  {props.testSuiteRuns.map((run, index) => {
                    const testFileRun = props.testFile.runs[run.id];

                    const progressClassName = createProgressClassName(
                      intentionalSkips,
                      testFileRun
                    );

                    return (
                      <React.Fragment key={index}>
                        <th className={progressClassName}>
                          <TestGridHeader
                            computeTimeMs={run.computeTimeMs}
                            dashboardUrl={run.dashboardUrls[props.groupName]}
                            endTime={run.endTime}
                            environment={run.environment}
                            runId={run.id}
                            runNumber={index + 1}
                            startTime={run.startTime}
                            tags={run.tags}
                          />
                        </th>
                      </React.Fragment>
                    );
                  })}
                </tr>
              </thead>
            )}
            <tbody>
              {Object.entries(props.testFile.testCases)
                .sort(([, caseA], [, caseB]) => caseA.order - caseB.order)
                .map(([testCaseName, testCase]) => {
                  const isSingleRun = props.testSuiteRuns.length === 1;
                  const firstTestCaseRun = testCase.runs[firstTestSuiteRun.id];

                  const status = isSingleRun
                    ? firstTestCaseRun?.status?.toUpperCase() ?? null
                    : null;

                  return (
                    <React.Fragment key={testCaseName}>
                      <tr>
                        <td
                          className="testCaseName"
                          colSpan={props.testSuiteRuns.length}
                        >
                          {props.testSuiteRuns.length === 1 && (
                            <span className={`pill ${status}`}>{status}</span>
                          )}
                          {testCaseName}
                        </td>
                      </tr>
                      <tr>
                        {props.testSuiteRuns.map((run) => {
                          const testCaseRun = testCase.runs[run.id];

                          return (
                            <TestGridRowDetails
                              isSingleRun={isSingleRun}
                              key={run.id}
                              messages={testCaseRun?.messages ?? []}
                              status={testCaseRun?.status ?? null}
                            />
                          );
                        })}
                      </tr>
                    </React.Fragment>
                  );
                })}
            </tbody>
          </table>
        </td>
      </tr>
    </>
  );
}

function createProgressClassName(
  intentionalSkipCount: number,
  testFileRun?: TestFileRun
) {
  if (testFileRun === undefined || testFileRun.status === null) {
    return "na";
  }

  if (testFileRun.status === "pass") {
    if (testFileRun.tryCount > testFileRun.testCaseCount) {
      return "flake";
    }
    if (
      intentionalSkipCount > 0 &&
      testFileRun.testCaseCount === testFileRun.pass + intentionalSkipCount
    ) {
      return "pass";
    }
    if (testFileRun.percent < 100) {
      return "partialPass";
    }
  }

  return testFileRun.status;
}

function countIntentionalSkips(props: TestGridRowProps) {
  let count = 0;

  if (["Playwright"].includes(props.groupName)) {
    for (const run of Object.values(props.testFile.runs)) {
      count += run.skip;
    }
  } else {
    for (const title in props.testFile.testCases) {
      if (title.includes("Intentionally Skipped")) {
        count += 1;
      }
    }
  }

  return count;
}

function calculateSkipPercentages(
  intentionalSkipCount: number,
  testFileRun: TestFileRun
) {
  // check if all tests of the suite have unintentionally skipped...if so, a full progress bar with SKIP text is displayed
  // else calculate percentage to use for Width of status bar. When calculating a percentage:
  // Intentional Skips are excluded from calculation.
  // Unintentional Skips count as failures.
  if (testFileRun.skip === testFileRun.testCaseCount) {
    return 100;
  } else if (
    testFileRun.status === "skip" &&
    testFileRun.testCaseCount === intentionalSkipCount
  ) {
    return 100;
  } else {
    const newCaseCount = testFileRun.testCaseCount - intentionalSkipCount;
    return Math.round((testFileRun.pass / newCaseCount) * 100);
  }
}

function createProgressDisplay(
  intentionalSkips: number,
  testFileRun?: TestFileRun
) {
  if (testFileRun === undefined || testFileRun.percent === null) {
    return "N/A";
  }

  if (testFileRun.status === "skip") {
    return "SKIP";
  }

  if (testFileRun.percent === 100) {
    return "PASS";
  }
  if (intentionalSkips) {
    const percent = calculateSkipPercentages(intentionalSkips, testFileRun);
    if (percent < 100) {
      return `${percent}%`;
    } else {
      return "PASS";
    }
  } else {
    return `${testFileRun.percent}%`;
  }
}

function createProgressStyle(
  intentionalSkips: number,
  testFileRun?: TestFileRun
) {
  let width;
  if (testFileRun === undefined || testFileRun.percent === null) {
    width = 100;
  } else {
    width = calculateSkipPercentages(intentionalSkips, testFileRun);
  }
  return { width: `${width}%` };
}
