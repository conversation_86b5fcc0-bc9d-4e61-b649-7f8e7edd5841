// @ts-ignore
import { I<PERSON>, <PERSON>, navigation, Tooltip } from "nr1";
import React from "react";

import { DAO } from "../DAO";
import { calculateWithRerunData } from "../services";
import { ENV_OFFLINE } from "../utils";
import { groups } from "../utils/constants";
import { E2EQuery } from "./E2EQuery";
import { Warning } from "./InfoMessages";
import RunIndicator, { Run } from "./RunIndicator";

export interface RunIndicatorsProps {
  readonly limit?: string | number;
  readonly maxItems?: string | number | undefined;
  readonly env: string;
  readonly since: string;
  readonly where: string;
}

const groupsLowercase = groups.map((group) => group.toLowerCase());

export default function RunIndicatorsOverview({
  limit = 5,
  maxItems,
  env,
  since = "",
  where = "",
}: RunIndicatorsProps) {
  return (
    <>
      {ENV_OFFLINE[env] && <Warning>{ENV_OFFLINE[env]}</Warning>}

      {!ENV_OFFLINE[env] && (
        <RunIndicatorBody
          env={env}
          limit={limit}
          maxItems={maxItems}
          since={since}
          where={where}
        />
      )}
    </>
  );
}

function RunIndicatorBody(props: RunIndicatorsProps) {
  return (
    <E2EQuery
      DAO={DAO.RunIndicators()
        .env(props.env)
        .where(props.where)
        .since(props.since)
        .limit(props.limit)}
    >
      {({ data }: { data: Run[] }) => {
        const processedRunData = getProcessRunData(data, props.maxItems);
        const compareRunLinks = getCompareRunLinks(data);
        return (
          <>
            <Tooltip text="Compare All">
              <Link
                to={compareRunLinks}
                className={"allLink"}
                ariaLabel="Compare Runs"
              >
                <Icon
                  type={Icon.TYPE.HARDWARE_AND_SOFTWARE__HARDWARE__CLUSTER}
                />
              </Link>
            </Tooltip>
            {processedRunData.map((run: Run) => (
              <RunIndicator run={run} />
            ))}
          </>
        );
      }}
    </E2EQuery>
  );
}

// Integration test results don't contain unique tags to indicate whether a test was triggered
// using either e2e or integration test GHA workflows. This check is able to tell if e2e tests ran
// along side the integration tests.
function removeIntegrationOnlyRuns(run: Run): boolean {
  return (
    (run.deploy.total ?? 0) > 0 ||
    (run.morning.total ?? 0) > 0 ||
    (run.cps.total ?? 0) > 0 ||
    (run.unstable?.total ?? 0) > 0 ||
    (run.playwright?.total ?? 0) > 0
  );
}

function getProcessRunData(
  data: Run[],
  maxItems: string | number | undefined
): Run[] {
  // sort data by run id
  data = data.filter(removeIntegrationOnlyRuns).sort((a: Run, b: Run) => {
    return b.runId.localeCompare(a.runId, "en");
  });

  const calculatedWithRerunData = calculateWithRerunData(data);

  // remove original run results from array of results to prevent displaying duplicate run data
  const removedOriginalsIfRerunExists = calculatedWithRerunData.filter(
    (run: Run, idx: number) => {
      if (idx === 0) {
        return true;
      }
      const previousRun = calculatedWithRerunData[idx - 1];
      return previousRun.runId.includes(run.runId) === false;
    }
  );

  //Subtract intentionally skipped tests count from the run total. We don't want them to be factored in when calculating the 'passing' percentages
  for (const run of removedOriginalsIfRerunExists) {
    for (const group of groupsLowercase) {
      if (run[group]) {
        run[group].total -= run[group].intentionalSkips;
      }
    }
  }

  // some integration tests are skipped in each environment so the pass count won't equal the total specs even if nothing failed
  // we check to see if nothing failed in this group
  // if nothing failed, then set total to passCount
  return removedOriginalsIfRerunExists
    .reduce((arr: Run[], run: Run) => {
      if (removeIntegrationOnlyRuns(run)) {
        arr.push(run);
      }
      return arr;
    }, [])
    .slice(0, maxItems);
}

function getCompareRunLinks(data: Run[]) {
  return navigation.getOpenStackedNerdletLocation({
    id: "panel-testgrid",
    urlState: { runIds: data.map((run: Run) => run.runId) },
  });
}
