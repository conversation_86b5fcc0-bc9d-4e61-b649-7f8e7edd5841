import { isWithinInterval, parseISO } from "date-fns";
import fs from "fs";
import { shuffle } from "lodash";
import os from "os";
import pRetry from "p-retry";
import path from "path";
import pdf from "pdf-parse";
import { collect } from "streaming-iterables";

import {
  EligibilityRequest,
  EmployerClaimRequestBody,
  GETClaimsByFineosAbsenceIdResponse,
  postFinancialEligibility,
} from "../../src/_api";
import config, { configuration } from "../../src/config";
import { getFeatureFlagSettings } from "../../src/featureFlags";
import { ClaimGenerator } from "../../src/generation/ClaimGenerator";
import EmployerPool, {
  Employer,
  EmployerPickSpec,
} from "../../src/generation/Employer";
import {
  DehydratedClaim,
  DehydratedClaimMetadata,
} from "../../src/generation/types";
import NewRelicClient from "../../src/NewRelicClient";
import { beforeRunCollectMetadata } from "../../src/reporter/utils";
import * as scenarios from "../../src/scenarios";
import { Fineos } from "../../src/submission/fineos.pages";
import { PostSubmitSpec } from "../../src/submission/PostSubmitAction";
import TestMailClient, {
  Email,
  GetEmailsOpts,
} from "../../src/submission/TestMailClient";
import {
  ApplicationSubmissionResponse,
  ClaimGenData,
  Credentials,
  EmployeeClaimGenData,
  EmployeeClaimGenResponse,
  NrqlUNFLog,
  Submission,
} from "../../src/types";
import {
  extractLeavePeriod,
  extractLeavePeriodType,
} from "../../src/util/claims";
import {
  getAuthManager,
  getClaimsForClaimant,
  getEmployeePool,
  getEmployerMPPool,
  getEmployerPool,
  getPortalSubmitter,
  getVerificationFetcher,
  searchEmployeeBySSN,
} from "../../src/util/common";
import {
  generateCredentials,
  generateDefaultLeaveAdminUsername,
  getApiCredentialsForFineos,
  getClaimantCredentials,
  getLeaveAdminCredentials,
} from "../../src/util/credentials";
import { FineosPresetKey } from "../../src/util/fineosRolePresets";
import { chooseRolePreset } from "../../src/util/fineosRoleSwitching";
import { removeLeaveAdmin } from "../../src/util/leaveAdmin";
import {
  getClaimantOAuthState,
  getLeaveAdminOAuthState,
  registerLeaveAdmin,
  startAppAndVerifyIdentity,
} from "../../src/util/myMassGov";
import { queryDb } from "../../src/util/queries";
import {
  assertIsNotNull,
  assertIsTypedArray,
  isObjectType,
} from "../../src/util/typeUtils";
import EmployeePool, {
  Employee,
  EmployeePickSpec,
} from "./../../src/generation/Employee";

// This is the directory where Cypress stores temporary files.
const crossPlatformTempDir = os.tmpdir();

/// <reference types="cypress" />
// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

export default async function (
  on: Cypress.PluginEvents,
  cypressConfig: Cypress.PluginConfigOptions
) {
  const verificationFetcher = getVerificationFetcher();
  const authenticator = getAuthManager();
  const submitter = getPortalSubmitter();

  // Declare tasks here.
  on("task", {
    getAuthVerification: (toAddress: string) => {
      return verificationFetcher.getVerificationCodeForUser(toAddress);
    },
    getEmailChangeVerification: (toAddress: string) => {
      return verificationFetcher.getEmailChangeVerificationCodeForUser(
        toAddress
      );
    },
    readFile(filename: string): string | null {
      try {
        const content = fs.readFileSync(filename, "utf-8");
        return content;
      } catch (err) {
        // There was an error reading the file. If it doesn't exist, we can continue.
        if (err.code === "ENOENT") {
          // File doesn't exist, so return null.
          return null;
        } else {
          // Some other error has occurred, which is unrecoverable.
          throw err;
        }
      }
    },
    async chooseFineosRole({
      userId,
      preset,
      debug = false,
    }: {
      userId: string;
      preset: FineosPresetKey;
      debug: boolean;
    }) {
      await Fineos.withBrowser(
        async (page) => {
          await chooseRolePreset(
            page,
            // ID of the account you want to switch the roles for
            userId,
            // Role preset you want to switch to.
            preset
          );
        },
        { debug }
      );
      return null;
    },

    getEmails(opts: GetEmailsOpts): Promise<Email[]> {
      const client = new TestMailClient(
        config("TESTMAIL_APIKEY"),
        config("TESTMAIL_NAMESPACE")
      );
      return client.getEmails(opts);
    },

    generateCredentials,
    async pickEmployer(spec: EmployerPickSpec): Promise<Employer> {
      return (await getEmployerPool()).pick(spec);
    },

    async registerClaimant(
      options: Credentials & {
        captureAuthenticatorKey?: boolean;
        fein?: string;
      }
    ): Promise<true> {
      const { captureAuthenticatorKey, password, username } = options;
      await authenticator.registerClaimant(
        { username, password },
        { captureAuthenticatorKey }
      );
      return true;
    },

    async registerLeaveAdmin(
      options: Credentials & {
        captureAuthenticatorKey?: boolean;
        fein?: string;
      }
    ): Promise<true> {
      const { captureAuthenticatorKey, fein, username, password } = options;
      const credentials = { username, password };

      if (fein) {
        await authenticator.registerLeaveAdmin(credentials, fein, {
          captureAuthenticatorKey,
        });
      } else {
        // Registers the leave admin without adding an employer or consenting to
        // data sharing.
        await registerLeaveAdmin(credentials);
      }

      return true;
    },

    async registerAndVerifyLeaveAdmin(
      options: Credentials & {
        captureAuthenticatorKey?: boolean;
        fein: string;
        withholdings: Record<string, number>;
      }
    ): Promise<boolean> {
      const {
        captureAuthenticatorKey,
        fein,
        username,
        password,
        withholdings,
      } = options;
      const credentials = { username, password };
      await authenticator.registerLeaveAdmin(credentials, fein, {
        captureAuthenticatorKey,
      });
      await authenticator.verifyLeaveAdmin(credentials, withholdings);
      return true;
    },

    async removeLeaveAdmin(verbose: boolean = false): Promise<boolean> {
      try {
        await removeLeaveAdmin(verbose);
        return true;
      } catch (error) {
        console.error("Error removing leave administrator:", error);
        return false;
      }
    },

    async getEmployerFeins(employerFilePath: string): Promise<string[]> {
      const employerPool = await EmployerPool.load(employerFilePath);
      return [...employerPool].map((employer) => employer.fein);
    },

    async getMasterPlanData(ssn: string): Promise<MasterPlanData> {
      const employee = await searchEmployeeBySSN(ssn);
      const newEmployer = (await getEmployerMPPool()).pick();
      return { newEmployer, employeeId: employee.employee_id };
    },

    async postProcess(postProcessingOptions: {
      claim: DehydratedClaim;
      submission: ApplicationSubmissionResponse;
      action: PostSubmitSpec;
      options?: Partial<DehydratedClaimMetadata>;
    }) {
      const { claim, submission, action, options } = postProcessingOptions;
      claim.metadata = { ...claim.metadata, ...options, postSubmit: action };

      const hydratedClaim = await ClaimGenerator.hydrate(
        postProcessingOptions.claim,
        crossPlatformTempDir
      );
      if (hydratedClaim.metadata?.postSubmit?.execute === undefined) {
        throw new Error("invalid state");
      }

      await hydratedClaim.metadata.postSubmit.execute({
        claim: hydratedClaim,
        applicationId: submission.application_id,
        fineosAbsenceId: submission.fineos_absence_id,
      });
      return null;
    },

    async submitClaimToAPI(
      application: DehydratedClaim & {
        credentials?: Credentials;
        employerCredentials?: Credentials;
      }
    ): Promise<ApplicationSubmissionResponse> {
      if (!application.claim) {
        throw new Error("Application missing!");
      }

      const { credentials, employerCredentials, ...claim } = application;
      return submitter
        .submit(
          await ClaimGenerator.hydrate(claim, crossPlatformTempDir),
          credentials ?? getClaimantCredentials(),
          {
            leaveAdminCredentials:
              employerCredentials ??
              getLeaveAdminCredentials(application.employer),
          }
        )
        .catch((err) => {
          console.error("Failed to submit claim:", err.data);
          throw new Error(err);
        });
    },

    async submitClaimPartOneToAPI(args: {
      claim: DehydratedClaim;
      credentials?: Credentials;
      fraudCheck?: boolean;
    }): Promise<ApplicationResponse> {
      const { credentials, claim, fraudCheck } = args;
      return submitter.createApplicationAndSubmitPartOne(
        claim.claim,
        credentials ?? getClaimantCredentials(),
        fraudCheck
      );
    },

    async submitClaimPartsTwoAndThreeToAPI(args: {
      claim: DehydratedClaim;
      credentials?: Credentials;
      submission: Submission;
    }): Promise<ApplicationSubmissionResponse> {
      const { claim, credentials, submission } = args;
      const { application_id, fineos_absence_id } = submission;
      const resolvedCredentials = credentials ?? getClaimantCredentials();
      const generatedClaim = await ClaimGenerator.hydrate(
        claim,
        crossPlatformTempDir
      );
      await submitter.submitPartTwo(
        application_id,
        generatedClaim,
        resolvedCredentials
      );
      return await submitter.submitPartThree({
        applicationId: application_id,
        claim: generatedClaim,
        credentials: resolvedCredentials,
        fineosAbsenceId: fineos_absence_id,
      });
    },

    async submitEmployerResponseToApi(args: {
      employerResponse: EmployerClaimRequestBody;
      identifiers: { employerFein: string; fineosAbsenceId: string };
    }) {
      const { employerResponse, identifiers } = args;
      const { employerFein, fineosAbsenceId } = identifiers;
      const credentials = {
        password: config("EMPLOYER_PORTAL_PASSWORD"),
        username: generateDefaultLeaveAdminUsername(employerFein),
      };

      await submitter.submitEmployerResponse(
        credentials,
        fineosAbsenceId,
        employerResponse
      );

      return null;
    },
    async checkFinancialEligibility(eligibilityRequest: EligibilityRequest) {
      const apiCreds = getApiCredentialsForFineos();
      const token = await authenticator.getAPIBearerToken(apiCreds);
      const pmflApiOptions = {
        baseUrl: config("API_BASEURL"),
        headers: {
          Authorization: `Bearer ${token}`,
          "User-Agent": `PFML Integration Testing (Financially Eligible)`,
        },
      };
      return await postFinancialEligibility(eligibilityRequest, pmflApiOptions);
    },

    async getBenefitYears(fineosAbsenceId: string) {
      return submitter.getBenefitYears(fineosAbsenceId);
    },

    async submitStepsOneTwoOfPartOne(args: {
      application: ApplicationRequestBody;
      credentials?: Credentials;
    }) {
      const submitter = getPortalSubmitter();
      return await submitter.submitStepsOneTwoOfPartOne(
        args.application,
        args.credentials ?? getClaimantCredentials()
      );
    },

    async completeSSOLoginFineos(credentials?: Credentials): Promise<string> {
      const ssoCookies = await Fineos.withBrowser(
        async (page) => {
          return JSON.stringify(await page.context().cookies());
        },
        {
          debug: false,
          screenshots: path.join(__dirname, "..", "playwright-screenshots"),
          credentials,
        }
      );
      return ssoCookies;
    },

    async completeClaimantSsoLogin(credentials: Credentials) {
      return getClaimantOAuthState(credentials);
    },
    async startApplicationAndIDV(args: {
      claim: ApplicationRequestBody;
      credentials?: Credentials;
    }) {
      const { claim, credentials } = args;
      assertIsNotNull(credentials);
      return startAppAndVerifyIdentity(claim, credentials);
    },
    async completeLeaveAdminSsoLogin(credentials: Credentials) {
      return getLeaveAdminOAuthState(credentials);
    },

    async generateClaim(args: ClaimGenData): Promise<DehydratedClaim> {
      const {
        scenario,
        employeePoolFileName,
        employerPoolFileName,
        occupationIndex,
        workPattern,
      } = args;
      const scenarioSpec = getScenarioSpec(scenario);
      const claim = ClaimGenerator.generate(
        employeePoolFileName
          ? await EmployeePool.load(employeePoolFileName, {
              employerFilePath: employerPoolFileName,
            })
          : await getEmployeePool(),
        scenarioSpec.employee,
        scenarioSpec.claim as APIClaimSpec,
        occupationIndex,
        workPattern
      );
      // Dehydrate (save) documents to the temp directory, where they can be picked up later on.
      // The file for a document is normally a callback function, which cannot be serialized and
      // sent to the browser using Cypress.
      return ClaimGenerator.dehydrate(claim, crossPlatformTempDir);
    },

    generateClaimForEmployee,

    async getParsedPDF(filename: string): Promise<pdf.Result> {
      return pdf(fs.readFileSync(filename));
    },

    /**
     * Wait for a file to be readable.
     * @param file name of the file to be found, including the path
     * @returns Promise->null if the file is readable within 15s. Rejects otherwise
     */
    waitForFileToBeReadable(file: string): Promise<null> {
      return pRetry(
        () => fs.promises.access(file, fs.constants.R_OK).then(() => null),
        { maxTimeout: 15_000 }
      );
    },

    async deleteDownloadFolder(folderName): Promise<true> {
      try {
        await fs.promises.rm(folderName, { maxRetries: 5, recursive: true });
      } catch (error) {
        // Ignore the error if download folder doesn't exist.
        if (error.code === "ENOENT") {
          return true;
        }
        throw error;
      }
      return true;
    },

    syslog(arg: unknown | unknown[]): null {
      if (Array.isArray(arg)) {
        console.log(...arg);
      } else {
        console.log(arg);
      }
      return null;
    },

    async findApplicationsLinkedNrLogs({ days }: { days: number }) {
      const env = config("ENVIRONMENT");
      const nrClient = new NewRelicClient(
        config("NEWRELIC_APIKEY"),
        config("NEWRELIC_ACCOUNTID")
      );

      const fields = [
        "application.application_id as 'application_id'",
        "application.submitted_time as 'submitted_time'",
        "application.completed_time as 'completed_time'",
        "application.additional_user_not_found_info.submitted_time as 'unf_submitted_time'",
        "application.updated_at as 'updated_at'",
        "message",
        "claim.claim_id as 'claim_id'",
        "fineos_absence_id",
      ];
      const query = `SELECT ${fields.join(", ")}
      FROM Log
      WHERE aws.logGroup = 'service/pfml-api-${env}/ecs-tasks'
        AND application.additional_user_not_found_info.submitted_time IS NOT NULL
        AND application.additional_user_not_found_info.employer_name = '[Assume this matches claim]'
        AND message LIKE '%Attached claim % to application%'
      SINCE ${days < 1 ? 1 : days} DAY AGO`;

      const data = await nrClient.nrql(query);

      if (data.length === 0) {
        return [] as NrqlUNFLog[];
      }

      assertIsTypedArray(
        data,
        isObjectType<NrqlUNFLog>({
          application_id: "",
          claim_id: "",
          submitted_time: "",
          unf_submitted_time: "",
          message: "",
          updated_at: "",
          fineos_absence_id: "",
        })
      );

      function getApplicationStatus(application: NrqlUNFLog) {
        // these statements need to be in this order to return the correct status
        // this should always mirror pfml api functionality
        if (application.completed_time !== null) {
          return "Completed";
        }

        if (application.submitted_time !== null) {
          return "Submitted";
        }

        if (application.unf_submitted_time !== null) {
          return "In Manual Review";
        }

        return "Started";
      }

      const processedData: NrqlUNFLog[] = data.map((log) => ({
        ...log,
        status: getApplicationStatus(log),
      }));

      return processedData;
    },
    async getApplicationById({ application_id }: { application_id: string }) {
      return submitter
        .getApplication(application_id)
        .then(({ data }) => data.data)
        .catch((err) => {
          console.error(`${err.data.message}: ${application_id}`);
          if (err.data.status_code === 403) {
            return null;
          }
          throw Error(err);
        });
    },
    queryDb,
    generateClaimForEmployeeWithoutClaims,
    generateClaimForEmployeeWithClaims,
    getConcurrentEmployeesTaxIdentifiersByScenarioNumber,
    async syncLeaveDetails({
      fineosId,
      credentials,
    }: {
      fineosId: string;
      credentials?: Credentials;
    }): Promise<DetailedClaimResponse | undefined> {
      return submitter.syncLeaveDetails(fineosId, credentials);
    },
  });

  on("before:browser:launch", (browser, launchOptions) => {
    if (browser.name === "chrome" && browser.isHeadless) {
      // Cypress doesn't automatically set the browser size to
      // match the viewport, so we need to manually override it.
      const width = cypressConfig.viewportWidth;
      const height = cypressConfig.viewportHeight;
      launchOptions.args.push(`--window-size=${width},${height}`);

      // force screen to be non-retina and just use our given resolution
      launchOptions.args.push("--force-device-scale-factor=1");
    }

    // Other browsers won't be affected here, but it doesn't matter since we
    // only use Chrome in CI runs.

    return launchOptions;
  });

  // Pass config values through as environment variables, which we will access via Cypress.env() in actions/common.ts.
  const featureFlagSettings = await getFeatureFlagSettings();
  const featureFlagEntries = featureFlagSettings.map(([k, v]) => [
    `E2E_${k}`,
    v,
  ]);
  const configEntries = Object.entries(configuration).map(([k, v]) => [
    `E2E_${k}`,
    v,
  ]);

  // Add metadata collection
  const reporterOptions = cypressConfig.reporterOptions ?? {};
  const hasNewRelicReporter =
    reporterOptions.reporterEnabled?.match(/dist.+cypress/);
  if (hasNewRelicReporter) {
    on("before:run", beforeRunCollectMetadata);
  }

  return {
    ...cypressConfig,
    env: Object.fromEntries([...featureFlagEntries, ...configEntries]),
    reporterOptions,
  };
}

/**
 * Generate a Claim using a particular Employee/Claimant
 */
export async function generateClaimForEmployee(
  args: EmployeeClaimGenData
): Promise<EmployeeClaimGenResponse> {
  const { employee, scenario } = args;
  const scenarioSpec = getScenarioSpec(scenario);

  const pickedEmployee =
    employee ?? (await getEmployeePool()).pick(scenarioSpec.employee);
  const claim = ClaimGenerator.generateFromEmployee(
    pickedEmployee,
    scenarioSpec.employee,
    scenarioSpec.claim,
    scenarioSpec.previousOccupationIndex ?? scenarioSpec.currentOccupationIndex
  );

  // Dehydrate (save) documents to the temp directory, where they can be picked up later on.
  // The file for a document is normally a callback function, which cannot be serialized and
  // sent to the browser using Cypress.
  return {
    claim: await ClaimGenerator.dehydrate(claim, crossPlatformTempDir),
    employee: pickedEmployee,
  };
}

export async function generateClaimForEmployeeWithoutClaims(
  args: ClaimGenData
): Promise<EmployeeClaimGenResponse> {
  const { scenario, employeePoolFileName, employerPoolFileName } = args;
  // If there is no employee pool, we'll fall back to the config - used for logging
  const employeePoolSourceFile =
    employeePoolFileName ?? config("EMPLOYEES_FILE");
  const scenarioSpec = getScenarioSpec(scenario);
  // Load, filter and shuffle employees
  const employees = shuffle(
    await collect(
      (employeePoolFileName
        ? await EmployeePool.load(employeePoolFileName, {
            employerFilePath: employerPoolFileName,
          })
        : await getEmployeePool()
      ).filter(scenarioSpec.employee)
    )
  );

  // The only reason we're limiting the employees to 1000 is to prevent the errors:
  // "The total size of your parameter(s) and document exceeds the 100KB limit"
  // "Unexpected end of JSON input"
  // However, this limitation increases the probability
  // that we won't find an employee without claims
  const randomlyPickedEmployees = employees
    .slice(0, 1000)
    .map((e) => `'${e.ssn.replace(/-/g, "")}'`)
    .join(",");

  const claimantsWithoutClaims = `SELECT json_agg(json_build_object(
    'ssn', ti.tax_identifier
  ))
  FROM employee AS e
  JOIN tax_identifier AS ti ON e.tax_identifier_id = ti.tax_identifier_id
  WHERE ti.tax_identifier IN (${randomlyPickedEmployees})
  AND e.employee_id NOT IN (
    SELECT c.employee_id
    FROM claim AS c
    WHERE c.employee_id = e.employee_id
  );`;

  const availableEmployees: Employee[] = await queryDb(claimantsWithoutClaims);
  if (availableEmployees.length <= 0) {
    // Could retry with a different slice of employees, or throw error.
    throw new Error(
      `Could not find claimants without claims from '${employeePoolSourceFile}' in the database!`
    );
  }

  const employee = employees.find(
    (e) => e.ssn.replace(/-/g, "") === availableEmployees[0].ssn
  );

  // The following error is nearly impossible, only here as a safeguard
  if (!employee) {
    throw new Error(
      `Could not find API claimant SSN '${availableEmployees[0].ssn}' in '${employeePoolSourceFile}'`
    );
  }

  return await generateClaimForEmployee({ scenario, employee });
}

export async function generateClaimForEmployeeWithClaims(
  args: ClaimGenData
): Promise<EmployeeClaimGenResponse> {
  const { scenario, employeePoolFileName, employerPoolFileName } = args;
  const scenarioSpec = getScenarioSpec(scenario);
  // Load, filter and shuffle employees
  const employees = shuffle(
    await collect(
      (employeePoolFileName
        ? await EmployeePool.load(employeePoolFileName, {
            employerFilePath: employerPoolFileName,
          })
        : await getEmployeePool()
      ).filter(scenarioSpec.employee)
    )
  );

  // The only reason we're limiting the employees to 1000 is to prevent the errors:
  // "The total size of your parameter(s) and document exceeds the 100KB limit"
  // "Unexpected end of JSON input"
  // However, this limitation increases the probability
  // that we won't find an employee without claims
  const randomlyPickedEmployees = employees
    .slice(0, 1000)
    .map((e) => `'${e.ssn.replace(/-/g, "")}'`)
    .join(",");

  const claimantsWithClaims = `SELECT json_agg(json_build_object(
    'ssn', ti.tax_identifier
  ))
  FROM employee AS e
  JOIN tax_identifier AS ti ON e.tax_identifier_id = ti.tax_identifier_id
  WHERE ti.tax_identifier IN (${randomlyPickedEmployees})
  AND e.employee_id IN (
    SELECT c.employee_id
    FROM claim AS c
    WHERE c.employee_id = e.employee_id
  );`;

  const availableEmployees: Employee[] = await queryDb(claimantsWithClaims);
  if (availableEmployees.length <= 0) {
    // Could retry with a different slice of employees, or throw error.
    throw new Error(
      `Could not find claimants with claims from '${employeePoolFileName}' in the database!`
    );
  }

  const employee = employees.find(
    (e) => e.ssn.replace(/-/g, "") === availableEmployees[0].ssn
  );

  // The following error is nearly impossible, only here as a safeguard
  if (!employee) {
    throw new Error(
      `Could not find API claimant SSN '${availableEmployees[0].ssn}' in '${employeePoolFileName}'`
    );
  }

  return await generateClaimForEmployee({ scenario, employee });
}

/**
 * Given a claim with arbitrary leave period types, return the leave periods.
 * @param claim ApplicationRequestBody
 * @returns [Date, Date]
 */
export function extractStartAndEndDates(claim: ApplicationRequestBody) {
  claim;
  const argMap: Record<
    ReturnType<typeof extractLeavePeriodType>,
    Parameters<typeof extractLeavePeriod>[1]
  > = {
    Continuous: "continuous_leave_periods",
    Intermittent: "intermittent_leave_periods",
    "Reduced Schedule": "reduced_schedule_leave_periods",
  };
  const leaveType = extractLeavePeriodType(claim.leave_details);
  if (leaveType) {
    return extractLeavePeriod(claim, argMap[leaveType]);
  }
  throw Error("Leave type could not be determined");
}

/**
 * Given a list of claims, determine if all claims do not have overlapping leave
 * dates with leave periods in the generatedClaim objects. Returns true if overlapping leave exists
 * @param generatedClaim DehydratedClaim
 * @param claims GETClaimsByFineosAbsenceIdResponse[]
 * @returns boolean
 */
export function determineCrossBenefit(
  generatedClaim: DehydratedClaim,
  claims: GETClaimsByFineosAbsenceIdResponse[]
) {
  const leaveDates = extractStartAndEndDates(generatedClaim.claim);
  const overlappingClaims = claims.filter((claim) => {
    const leavePeriods = claim?.data?.absence_periods?.pop();
    // If these properties are undefined - assume claim has overlapping leave dates to stay on the safe side
    if (
      !leavePeriods ||
      !leavePeriods.absence_period_start_date ||
      !leavePeriods.absence_period_end_date
    ) {
      return true;
    }
    const isOverlapping =
      isWithinInterval(parseISO(leavePeriods.absence_period_start_date), {
        start: leaveDates[0],
        end: leaveDates[1],
      }) ||
      isWithinInterval(parseISO(leavePeriods.absence_period_end_date), {
        start: leaveDates[0],
        end: leaveDates[1],
      });
    return isOverlapping;
  });
  // If overlapping claims exist return true to indicate a cross-benefit
  return overlappingClaims.length > 0;
}

/**
 * Given a claim, find an employee that meets the employee
 * claim spec and doesn't have leave requests that overlap with
 * the DehydratedClaim leave periods
 * @param claim DehydratedClaim
 * @param employeePickSpec EmployeePickSpec
 * @param employeeFile string
 * @returns
 */
export async function findEmployeeWithoutCrossBenefit(
  claim: DehydratedClaim,
  employeePickSpec: EmployeePickSpec,
  employeeFile: string
) {
  // Load and filter employees
  const employees = shuffle(
    await collect(
      (await EmployeePool.load(employeeFile)).filter(employeePickSpec)
    )
  );
  const batchSize = 20;
  for (let i = 0; i * batchSize < employees.length; ++i) {
    const batch = employees.slice(i * batchSize, (i + 1) * batchSize);
    const checkCrossBenefit = await Promise.all(
      batch.map(async (employee) => {
        const claims = await getClaimsForClaimant(employee.ssn);
        if (!claims) {
          throw Error(`Unable to fetch claims for claimant ${employee.ssn}`);
        }
        if (claims.length === 0) {
          return employee;
        }
        const hasCrossBenefit = determineCrossBenefit(claim, claims);
        return hasCrossBenefit ? undefined : employee;
      })
    );
    const match = checkCrossBenefit.find((employee) => employee !== undefined);
    if (match) {
      return match;
    }
  }

  // no match has been found, so throw an error
  throw new Error("Unable to find employee without cross benefit");
}

export function replaceWithClaimantInformation(
  claim: DehydratedClaim,
  employee: Employee
): DehydratedClaim {
  return {
    ...claim,
    claim: {
      ...claim.claim,
      employer_fein: employee.occupations[0].employer.fein,
      first_name: employee.first_name,
      last_name: employee.last_name,
      tax_identifier: employee.ssn,
    },
    employer: employee.occupations[0].employer,
  };
}

function getScenarioSpec(scenario: Scenarios) {
  if (!(scenario in scenarios)) {
    throw new Error(`Invalid scenario: ${scenario}`);
  }

  return scenarios[scenario];
}

export async function getConcurrentEmployeesTaxIdentifiersByScenarioNumber(
  concurrentScenario: number
): Promise<string> {
  const employeeFileName = config("CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE");
  const employerFileName = config("CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE");
  const employeePool = await EmployeePool.load(employeeFileName, {
    employerFilePath: employerFileName,
  });

  const selectedScenarioEmployees = employeePool.filter({
    metadata: {
      concurrentEmploymentScenario: concurrentScenario,
    },
  });

  return selectedScenarioEmployees
    .getTaxIdentifers()
    .map((taxIdentifier) => {
      return `'${taxIdentifier.replace(/-/g, "")}'`;
    })
    .join(", ");
}
