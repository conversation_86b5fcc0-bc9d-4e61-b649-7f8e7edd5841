import { format } from "date-fns";

import config from "../../../src/config";
import { DehydratedDocument } from "../../../src/generation/documents";
import {
  FineosCorrespondenceType,
  FineosProcess,
  FineosRMVCheckStatus,
} from "../../../src/types";
import { fineos } from "..";
import {
  assertMessageAlert,
  assertMissingPaymentInformationAlert,
  before,
  clickBottomWidgetButton,
  clickNext,
  ensureAlertPopUpClosed,
  ensureCaseSummaryPanelIsNotCollaspsed,
  ensurePanelIsNotCollapsed,
  isAlertPopUpOpen,
  onTab,
  visitClaim,
  wait,
  waitForAjaxComplete,
} from "../fineos";
import { AdjudicationPage } from "./AdjudicationPage";
import { AlertsPage } from "./AlertsPage";
import { BenefitsExtensionPage } from "./BenefitsExtensionPage";
import { CaseNotesBar } from "./CaseNotesBar";
import { DocumentsPage } from "./DocumentsPage";
import { HistoricalAbsence } from "./HistoricalAbsence";
import { LeaveDetailsPage } from "./LeaveDetailsPage";
import { NotesPage } from "./NotesPages";
import { NotificationPageError } from "./NotificationPage";
import { OutstandingRequirementsPage } from "./OutstandingRequirementsPage";
import { PaidLeavePage } from "./PaidLeave";
import { RecordActualTime } from "./RecordActualTime";
import { RecordActualTimeCalendar } from "./RecordActualTimeCalendar";
import { TasksPage } from "./TasksPage";

type StatusCategory =
  | "Applicability"
  | "Eligibility"
  | "Evidence"
  | "Availability"
  | "Restriction"
  | "Protocols"
  | "PlanDecision";

export class ClaimPage {
  private constructor() {}

  static get(): ClaimPage {
    cy.get("h2").should("contain.text", "Absence Case");
    return new ClaimPage();
  }

  static visit(
    id: string,
    ignorePotentialConcurrentAbsenceAlert = false
  ): ClaimPage {
    before();
    visitClaim(id);
    ignorePotentialConcurrentAbsenceAlert && ensureAlertPopUpClosed();
    return new ClaimPage();
  }

  // The Preliminary Designation is triggered to resolve a race
  // condition where the one-minute time trigger on this notice can
  // result in an "Out of Date Data" error when attempting to edit the
  // leave period dates.
  static visitAndTriggerPreliminaryDesignation(id: string) {
    const claimPage = ClaimPage.visit(id);
    claimPage.triggerNotice("Preliminary Designation");
    claimPage.resetTasksTab();
    onTab("Absence Hub");
    return claimPage;
  }

  addHistoricalAbsenceCase(): HistoricalAbsence {
    return HistoricalAbsence.create();
  }

  assertRMVCheckStatus(status: FineosRMVCheckStatus): this {
    // Even though this element has a label, it's rendered as a div, which
    // isn't considered a labelable element per the HTML5 spec.
    // https://html.spec.whatwg.org/multipage/forms.html#category-label
    cy.get("[id*='_identificationStatus']").should(
      "contain.text",
      `Verification check ${status}`
    );
    return this;
  }

  recordActualLeave<T>(cb: (page: RecordActualTime) => T): T {
    // Start the submission process.
    cy.findByText("Record Actual").click();
    waitForAjaxComplete();
    return cb(new RecordActualTime());
  }

  recordActualLeaveCalendar<T>(cb: (page: RecordActualTimeCalendar) => T): T {
    waitForAjaxComplete();
    return cb(new RecordActualTimeCalendar());
  }

  shouldHaveEpisodicLeave(
    date: Date,
    hours: number,
    status: "Pending" | "Approved"
  ) {
    const dateFormat = config("HAS_FR25_1")
      ? "eeee, MMMM do yyyy"
      : "eeee, MMMM do";
    const formattedDate = format(date, dateFormat);
    cy.contains(".flex-container", `${formattedDate} - ${hours} hours`).should(
      "contain.text",
      status
    );
  }

  paidLeave(cb: (page: PaidLeavePage) => unknown): this {
    cy.findAllByText("Absence Paid Leave Case", { selector: "a" })
      .first()
      .as("absencePaidLeaveCase");
    cy.get("@absencePaidLeaveCase").focus();
    cy.get("@absencePaidLeaveCase").click();
    waitForAjaxComplete();
    cy.wait(200);
    cb(new PaidLeavePage());
    cy.findByText("Absence Case", { selector: "a" }).click();
    waitForAjaxComplete();
    cy.wait(1500);
    return this;
  }

  // FINEOS changed for In Review workflow after the April upgrade 2022.
  adjudicateInReview(cb: (page: AdjudicationPage) => unknown): this {
    cb(new AdjudicationPage());
    if (config("HAS_FR25_1")) {
      onTab("Absence Hub");
    } else {
      cy.get("#footerButtonsBar input[value='OK']").click();
    }

    return this;
  }

  adjudicate(cb: (page: AdjudicationPage) => unknown): this {
    waitForAjaxComplete();
    cy.get('input[type="submit"][value="Adjudicate"]', { timeout: 15_000 })
      .should("be.visible")
      .click();
    cb(new AdjudicationPage());
    waitForAjaxComplete();
    if (config("HAS_FR25_1")) {
      ensureCaseSummaryPanelIsNotCollaspsed();
      cy.findByText("Absence Case", { selector: "a" }).click();
    } else {
      cy.get("#footerButtonsBar input[value='OK']").click();
    }

    return this;
  }

  /**
   * Receives evidence, prefills certification periods, and optionally accepts
   * the leave plan.
   */
  completeAdjudication(
    evidenceDocuments: readonly DehydratedDocument[],
    options: { acceptLeavePlan?: boolean } = {}
  ): ClaimPage {
    return this.adjudicate((adjudication) => {
      adjudication
        .evidence((evidence) => {
          evidenceDocuments.forEach((document) => {
            evidence.receive(document.document_type);
          });
        })
        .certificationPeriods((certPeriods) => certPeriods.prefill())
        .acceptLeavePlan(options.acceptLeavePlan ?? true);
    });
  }

  viewRequest(cb: (page: AdjudicationPage) => unknown): this {
    cy.get('input[type="submit"][value="View Request"]').click();
    ensurePanelIsNotCollapsed("leave-request-panel");
    cb(new AdjudicationPage());
    cy.get("#footerButtonsBar input[value='Close']").click();
    return this;
  }

  tasks(cb: (page: TasksPage) => unknown): this {
    onTab("Tasks");
    cb(new TasksPage());
    onTab("Absence Hub");
    return this;
  }

  appealDocuments(cb: (page: DocumentsPage) => unknown): this {
    onTab("Documents");
    cb(new DocumentsPage());
    return this;
  }

  appealTasks(cb: (page: TasksPage) => unknown): this {
    onTab("Tasks");
    cb(new TasksPage());
    return this;
  }

  documents(cb: (page: DocumentsPage) => unknown): this {
    onTab("Documents");
    cb(new DocumentsPage());
    onTab("Absence Hub");
    return this;
  }

  alerts(cb: (page: AlertsPage) => unknown): this {
    onTab("Alerts");
    cb(new AlertsPage());
    onTab("Absence Hub");
    return this;
  }

  caseNotesBar(cb: (bar: CaseNotesBar) => void): this {
    cb(new CaseNotesBar());
    return this;
  }

  outstandingRequirements(
    cb: (page: OutstandingRequirementsPage) => unknown
  ): this {
    ensureAlertPopUpClosed();
    onTab("Outstanding Requirements");
    cb(new OutstandingRequirementsPage());
    onTab("Absence Hub");
    return this;
  }

  notes(cb: (page: NotesPage) => unknown): this {
    onTab("Notes");
    cb(new NotesPage());
    onTab("Absence Hub");
    return this;
  }

  leaveDetails(cb: (page: LeaveDetailsPage) => unknown): this {
    onTab("Leave Details");
    cb(new LeaveDetailsPage());
    onTab("Absence Hub");
    return this;
  }

  benefitsExtension(cb: (page: BenefitsExtensionPage) => unknown): this {
    cy.findByText("Add Time").click();
    cb(new BenefitsExtensionPage());
    return this;
  }

  shouldHaveStatus(category: StatusCategory, expected: string): this {
    const selector =
      category === "PlanDecision"
        ? `.divListviewGrid .ListTable td[id*='ListviewWidget${category}0']`
        : `.divListviewGrid .ListTable td[id*='ListviewWidget${category}Status0']`;

    cy.get(selector).should((element) => {
      expect(
        element,
        `Expected claim's "${category}" to be "${expected}"`
      ).to.have.text(expected);
    });
    return this;
  }
  triggerNotice(type: FineosProcess): this {
    const docRegex = new RegExp(`^${type}`);
    onTab("Task");
    onTab("Processes");
    cy.contains(".TreeNodeElement", docRegex).click({
      force: true,
    });
    waitForAjaxComplete();
    cy.wait(250);
    // When we're firing time triggers, there's always the possibility that the trigger has already happened
    // by the time we get here. When this happens, the "Properties" button will be grayed out and unclickable.
    cy.get('input[type="submit"][value="Properties"]').then((el) => {
      if (el.is(":disabled")) {
        cy.log("Skipping trigger because this time trigger has already fired");
        return;
      }
      cy.wrap(el).click();
      waitForAjaxComplete();
      cy.get("body").then((element) => {
        if (element.find('input[type="submit"][value="Continue"]').length > 0) {
          cy.wait(1000);
          cy.get('input[type="submit"][value="Continue"]').first().click();
        } else {
          cy.get('input[type="submit"][value="Close"]').first().click();
        }
        waitForAjaxComplete();
        cy.wait(2000);
        if (type === "Preliminary Designation") {
          // It's possible to encounter an alert if the case has been updated
          // by the automatic trigger for the Preliminary Designation notice type
          // while Cypress manually triggers the task i.e. https://onenr.io/0qwLdLLkmw5
          // If this happens we can close the alert and page to assert
          // that the notice has been triggered
          isAlertPopUpOpen().then((alertPopUpIsOpen) => {
            if (alertPopUpIsOpen) {
              ensureAlertPopUpClosed();
              // Then click the close button
              cy.get('input[type="submit"][value="Close"]').first().click();
            }
          });
        }
      });
    });
    return this;
  }

  assertClaimStatus(expected: string, hasDeniedExtension = false) {
    const selector = hasDeniedExtension
      ? "label[id*='deniedLeaveDescription']"
      : ".key-info-bar .status dd";
    cy.get(selector).should("contain.text", expected);
    return this;
  }

  approve(
    status: "Approved" | "Completed" | "Adjudication" = "Approved",
    alertMsg?: NotificationPageError
  ): this {
    if (config("HAS_FR25_1")) {
      // Already approved through the new workflow in acceptLeavePlan under FR25-1 release
      return this;
    }
    cy.get('a[title="Approve the pending/in review leave request"]').as(
      "approveLeaveRequest"
    );
    cy.get("@approveLeaveRequest").focus();
    cy.get("@approveLeaveRequest").click();
    waitForAjaxComplete();
    cy.wait(500);
    cy.get("body").then(($body) => {
      if ($body.find("div[id*='page_messages_container']").length > 0) {
        assertMissingPaymentInformationAlert();
        if (alertMsg) {
          assertMessageAlert(alertMsg);
        }
      }
    });
    ensureAlertPopUpClosed();
    this.assertClaimStatus(status);
    return this;
  }

  deny(reason: string, assertStatus = true, denyextension: boolean): this {
    cy.get("input[type='submit'][value='Adjudicate']").click();
    if (config("HAS_FR25_1")) {
      cy.get('button[data-testid="leave-request-primary-button"]')
        .should("be.visible")
        .click();
      cy.get('button[id="leave-request-progress-window-ok-button"]')
        .should("be.visible")
        .click();
      cy.get('span[data-domain-name="DecisionStatus"]').should(
        "contain.text",
        "Fully Adjudicated - Denied"
      );
      cy.get(
        'span[id="footerButtonsBar"] input[type="submit"][value="Close"]'
      ).click({ force: true });
    } else {
      // Make sure the page is fully loaded by waiting for the leave plan to show up.
      cy.get("table[id*='selectedLeavePlans'] tr")
        .should("have.length", 1)
        .click();
      waitForAjaxComplete();
      // wait for buttons to be rendered before clicking
      cy.wait("@ajaxRender");
      cy.wait(500);
      cy.get("#button-reject").should("be.visible").click();
      cy.get("#dropdown-reject-reason").click();
      cy.get("[title='Employee not eligible']").click();
      cy.get("#moval-evaluate-ok").click();
      cy.wait(250);
      waitForAjaxComplete();
      clickBottomWidgetButton("OK");
      const selector = 'a[title="Deny the pending/in review leave request"]';
      cy.get(selector).click();
      cy.get('span[id="leaveRequestDenialDetailsWidget"]')
        .find("select")
        .select(reason);
      cy.get('input[type="submit"][value="OK"]').click();
      // denying an extension for another reason will cause this assertion to fail
      assertStatus &&
        this.assertClaimStatus(
          denyextension ? "Previously denied" : "Declined",
          denyextension
        );
    }
    return this;
  }

  selectFromDropdown(testId: string, option: string): this {
    cy.findByTestId(testId).within(() => {
      cy.get(".ant-select-selection-item").click();
    });
    cy.get(`.ant-select-item[title="${option}"]`).click();
    return this;
  }

  denyAppeal(): this {
    if (config("HAS_FR25_1")) {
      cy.findByText("Absence Case", { selector: "a" }).click();
      waitForAjaxComplete();
      onTab("Leave Details");

      fineos.openLeaveRequestDetails();

      // Opens the menu which contains option to initiate an appeal denial
      cy.findByTestId(/selected-leave-plan-more-*/).click();

      // Initiates an appeal denial
      cy.findByTestId("selected-leave-plan-appeal-denial").click();

      // Appeal denials open a modal form, so fill out all the required form fields
      this.selectFromDropdown("appealedById", "Employee");
      this.selectFromDropdown("decisionTimePeriodUnitId", "Weeks");

      // Cypress thinks the input is overlapped but it's only partially overlapped and not being blocked by any popups/modals
      cy.findByTestId("decisionTimePeriodId").type("4", { force: true });

      // Submit the form
      cy.contains(".ant-btn-primary", "OK").click();
      waitForAjaxComplete();

      // Wait till modal has fully closed before moving on.
      cy.get(".ant-modal-wrap").should("not.exist");
      cy.get(".ant-spin-text", { timeout: 40_000 }).should("not.exist");
      cy.wait("@reactRender");

      // Assert status text indicating the appeal is now in progress
      cy.findByTestId("leave-request-status").contains("Appeal in Progress");
    } else {
      cy.findByText("Absence Case", { selector: "a" }).click();
      waitForAjaxComplete();
      cy.get("#link_Options").siblings("a").contains("Options").click();
      cy.contains("Appeal Denial").focus();
      cy.contains("Appeal Denial").click();
      waitForAjaxComplete();
      this.assertClaimStatus("Adjudication");
      waitForAjaxComplete();
      cy.wait(500);
    }

    return this;
  }

  addActivity(activity: string): this {
    cy.get("[id^=MENUBAR\\.CaseSubjectMenu]")
      .findByText("Add Activity")
      .as("caseMenu");
    cy.get("@caseMenu").click();
    cy.get("@caseMenu")
      .parents("li")
      .findByText(activity)
      .parent()
      .as("caseMenuParent");
    cy.get("@caseMenuParent").focus();
    cy.get("@caseMenuParent").click();
    return this;
  }

  addCorrespondenceDocument(action: FineosCorrespondenceType): this {
    const document = new DocumentsPage();

    cy.get("[id^=MENUBAR\\.CaseSubjectMenu]")
      .findByText("Correspondence")
      .as("correspondence");
    cy.get("@correspondence").click();
    cy.wait(1000);
    cy.get("@correspondence")
      .parents("li")
      .findByText(action)
      .parent()
      .as("correspondenceParent");
    cy.get("@correspondenceParent").focus();
    cy.get("@correspondenceParent").dblclick();
    cy.then(wait);

    // These are automatically generated by FINEOS, and don't require an
    // additional file upload.
    const generatedDocuments: FineosCorrespondenceType[] = [
      "Leave Allotment Change Notice",
      "Employer Reimbursement Denial Notice",
      "Employer Reimbursement Approval Notice",
    ];

    if (generatedDocuments.includes(action)) {
      cy.get("#footerButtonsBar input[value='Next']").click();
    } else {
      document.uploadDocumentAlt(action);
    }
    return this;
  }

  // This will deny extended time in the Leave Details.
  // No assert ClaimStatus for Declined for the absence case
  // won't say "Declined".
  denyExtendedTime(reason: string): this {
    onTab("Leave Details");
    const selector = 'a[title="Deny the pending/in review leave request"]';
    waitForAjaxComplete();
    cy.get("table[id$='leaveRequestListviewWidget']").within(() => {
      cy.get("tr.ListRowSelected").click();
    });
    cy.get(selector).click();
    cy.get('span[id="leaveRequestDenialDetailsWidget"]')
      .find("select")
      .select(reason);
    cy.get('input[type="submit"][value="OK"]').click();
    return this;
  }

  addAppeal(): this {
    cy.get('a[title="Add Sub Case"]').click();
    waitForAjaxComplete();
    cy.wait(200);
    cy.get('a[title="Create Appeal"]').focus();
    cy.get('a[title="Create Appeal"]').click();
    waitForAjaxComplete();
    cy.wait(200);
    return this;
  }

  checkOrgUnitAdminGroup(adminGroup: string): this {
    fineos.onTab("General");
    cy.get(`div[id$="_adminGroup"]`).should((element) => {
      expect(
        element,
        "Organization Unit should have the `${adminGroup}`"
      ).to.have.text(`${adminGroup}`);
    });
    return this;
  }

  addEmployer(employer_fein: string): this {
    //Select employer from drop down menu
    cy.get("[id^=MENUBAR\\.CaseSubjectMenu]")
      .findByText("Add Participant")
      .as("participant");
    cy.get("@participant").click();
    cy.get("@participant")
      .parents("li")
      .findByText("Employer")
      .parent()
      .as("participantParent");
    cy.get("@participantParent").focus();
    cy.get("@participantParent").click();
    cy.wait(250);

    //change radio to Organization
    cy.contains("label", "Organization").click();
    // input employer FEIN
    cy.get('input[type="text"][id$=_Social_Security_No\\._\\(SSN\\)]').type(
      employer_fein?.replace("-", "")
    );
    //Search
    cy.get('input[type="submit"][value="Search"]').click();
    waitForAjaxComplete();
    cy.get('input[type="submit"][value="OK"][id$=_searchPageOk]').click();
    return this;
  }

  withdraw(): this {
    cy.get('a[title="Withdraw the Pending Leave Request"').click();
    cy.get("#leaveRequestWithdrawPopupWidget_PopupWidgetWrapper").within(() => {
      cy.findByLabelText("Withdrawal Reason").select("Employee Withdrawal");
      cy.findByText("OK").click();
    });
    this.assertClaimStatus("Closed");
    return this;
  }

  reviewClaim(): this {
    onTab("Leave Details");
    waitForAjaxComplete();
    if (!config("HAS_FR25_1")) {
      cy.contains("td", "Approved").click();
      // Note in the new workflow for putting a claim into Review we are going directly to the Adjudication
      // instead of click on the Absence Hub. Then clicking the Adjudication button on the Absence Hub tab.
      cy.contains("button", "Review").click();
      cy.wait("@reactRender");
      cy.get(`.ant-modal`)
        .should("be.visible")
        .within(() => {
          cy.get('input[id="secondOption"][type="radio"]').click();
          cy.contains("button", "OK").click();
        });
      cy.get("body").should("not.contain", ".ant-modal");
      cy.url().should("contain", "editleaverequest", { timeout: 20_000 });
      cy.contains(
        'span[id$="_openReviewDocumentLabel"]',
        "The leave request is now in review"
      );
      waitForAjaxComplete();
      cy.wait(500);
    }
    return this;
  }

  recordCancellation(): this {
    const recordCancelledTime = () => {
      cy.contains("td", "Known").click();
      cy.get(
        'input[title="Record Cancelled Time for the selected absence period"]'
      ).click();
      waitForAjaxComplete();
      cy.get('input[type="submit"][value="OK"]').click();
      waitForAjaxComplete();
      clickNext();
      waitForAjaxComplete();
      fineos.clickNext();
      waitForAjaxComplete();
    };
    const additionalReportingInformation = () => {
      cy.get('select[id$="reportedBy"]').select("Employee");
      waitForAjaxComplete();
      cy.get('select[id$="receivedVia"]').select("Phone");
      waitForAjaxComplete();
      cy.get('select[id$="cancellationReason"]').select(
        "Employee Requested Cancellation"
      );
      waitForAjaxComplete();
      cy.get(
        'input[type="checkbox"][id$="MasterMultiSelectCB_CHECKBOX"]'
      ).click();
      cy.get('input[type="submit"][title="Apply"]').click();
      waitForAjaxComplete();
      fineos.clickNext();
      waitForAjaxComplete();
    };
    const makeDecision = () => {
      cy.get('select[id$="period-decision-status"]').select("Approved");
      waitForAjaxComplete();
      cy.get(
        'input[type="checkbox"][id$="MasterMultiSelectCB_CHECKBOX"]'
      ).click();
      waitForAjaxComplete();
      cy.get('input[type="submit"][title="Apply"]').click();
      waitForAjaxComplete();
      fineos.clickNext();
      waitForAjaxComplete();
    };

    cy.findByText("Record Cancellation", {
      selector: "span",
    }).click();
    // steps for cancelling time
    recordCancelledTime();
    additionalReportingInformation();
    makeDecision();
    return this;
  }

  ensureAlertsOpen() {
    cy.get(".alertsList-wrap").then((alerts) => {
      if (alerts.hasClass("hidden")) {
        cy.get(".alertsMenuButton").click();
      }
    });
  }

  /**
   * To test secure action task only with our current E2E test suite.
   * Suppress and remove suppression in the same task. Two options on how to remove the suppression when
   * clicking the Notification to remove the suppression in the pop-up widget is very flaky in the headless browser.
   * So the second option is to cancel the suppress notification instead.
   */
  removeSuppressCorrespondence(hasAction: boolean): this {
    waitForAjaxComplete();
    ensureAlertPopUpClosed();
    cy.contains("Options").click();
    if (hasAction) {
      cy.contains("Notifications").focus();
      cy.contains("Notifications").click();
      cy.get("input[type='submit'][value='Suppress Notifications']").click();
      cy.contains(
        "Automatic Notifications and Correspondence have been suppressed."
      );
      cy.get(".alertsHeader_messageButton").within(() => {
        cy.contains("Open").click();
        waitForAjaxComplete();
      });
      cy.contains(
        "Automatic Notifications and Correspondence have been suppressed."
      );
      cy.contains("Close Task").click();
      cy.get("table.PopupBean")
        .first()
        .within(() => {
          cy.get("input[type='submit'][value='Yes']").click();
        });
      clickBottomWidgetButton("OK");
    } else {
      cy.contains(
        "span[title='Control is protected by a Secured Action.']",
        "Notifications"
      ).should("have.attr", "disabled");
    }
    return this;
  }

  reopen() {
    cy.get("a[title='Reopen the Closed Case']").focus();
    cy.get("a[title='Reopen the Closed Case']").click();
    fineos.waitForAjaxComplete();
    cy.get('a[title*="Close the Case"]');
    return this;
  }

  createHistoricalDisabled() {
    cy.contains("Options").click();
    cy.contains("Add Historical Absence").focus();
    cy.contains("Add Historical Absence").click();
    cy.contains("div", "timeOffHistoricalAbsencePeriodsListviewWidget")
      .find("input")
      .should("have.attr", "disabled");
    return this;
  }

  private resetTasksTab() {
    cy.contains(".TabStrip td", "Processes").prev().trigger("click");
  }
}
