import { addDays, addMonths, format } from "date-fns";

import { formatDateUS } from "../../global.common";
import config from "../../src/config";
import { MasterPlanData } from "../../src/generation/Employer";
import {
  AbsenceStatus,
  ClaimGenData,
  Credentials,
  FineosDocumentType,
} from "../../src/types";
import { assertValidClaim } from "../../src/util/typeUtils";
import { getFineosBaseUrl } from "../util";
import { fineos } from ".";
import { generateClaim } from "./claim";
import { ClaimantPage } from "./fineos.claimant";
import { NotificationPageError } from "./fineos.pages/NotificationPage";

/**
 * This function is used to fetch and set the proper cookies for access Fineos UAT
 *
 * Note: Only used for UAT environment
 */
function SSO(credentials?: Credentials): void {
  cy.clearCookies();
  cy.session(credentials?.username ?? "default", () => {
    // Perform SSO login in a task. We can't visit other domains in Cypress.
    cy.task("completeSSOLoginFineos", credentials).then((cookiesJson) => {
      const deserializedCookies: Record<string, string>[] =
        JSON.parse(cookiesJson);
      // Filter out any cookies that will fail to be set. Those are ones where secure: false
      // and sameSite: "None"
      const noSecure = deserializedCookies.filter(
        (cookie) => !(!cookie.secure && cookie.sameSite === "None")
      );
      for (const cookie_info of noSecure) {
        cy.setCookie(cookie_info.name, cookie_info.value, cookie_info);
      }
    });
  });
}

/**
 * Sets up Cypress to work with fineos. This includes:
 * 1. Intercepting known errors and error pages.
 * 2. Handling authentication and SSO Login.
 * 3. Setting the baseURL
 * 4. Navigating to fineos home page.
 * @param credentials you can override the credentials used for authenticating to fineos,
 * an example use case would be testing secure actions in `uat` environment,
 * where we need to use 2 different fineos accounts.
 * @example
 * fineos.before() // set's up fineos with default credentials.
 * fineos.before({username:config('SSO2_USERNAME'), password: config('SSO2_PASSWORD')})
 * // set's up login for the second SSO account
 */
export function before(credentials?: Credentials): void {
  const cfg = Cypress.config();
  cfg.baseUrl = getFineosBaseUrl(credentials?.username, credentials?.password);
  const fineosBaseUrl = config("FINEOS_BASEURL");

  cy.url().then((url) => {
    if (cfg.baseUrl) {
      if (!url.includes(fineosBaseUrl)) {
        // Check if before() has already been run, by-pass if true to avoid duplicate logic
        Cypress.config("pageLoadTimeout", 90_000);
        // Fineos error pages have been found to cause test crashes when rendered. This is very hard to debug, as Cypress
        // crashes with no warning and removes the entire run history, so when a Fineos error page is detected, we replace the
        // page with an error page and capture the real response to a file for future debugging.
        cy.intercept(
          /\/(util\/errorpage\.jsp|outofdatedataerror\.jsp)/,
          (req) => {
            req.continue((res) => {
              // We need to extract this obstructive logic included in a FINEOS error page and replace it with a setTimeout to throw an error letting us know this page was encountered
              // Using the "modifyObstuctiveCode" property in the cypress.json was enough to get the error page to display but it was not enough to mitigate the test from hanging.
              // This approach behaves in a much more predictable manner (Error thrown)
              const body: string = res.body.replace(
                "if (top != self) { top.location=self.location }",
                ""
              );
              const doc = new DOMParser().parseFromString(body, "text/html");
              const debugInfo = doc.getElementById("ErrorString")?.innerText; //  Out Of Date Data error pages won't contain a stack trace
              res.send(body);
              // allow 1 second to pass - allowing page to display in CI recordings
              setTimeout(() => {
                throw new Error(
                  `A FINEOS error page was detected during this test. An error is being thrown in order to prevent Cypress from crashing.${
                    debugInfo
                      ? `\n\nDebug Information:\n----------\n${debugInfo}`
                      : ""
                  }}`
                );
              }, 300);
            });
          }
        );

        // Set up a route we can listen to wait on ajax rendering to complete.
        cy.intercept(
          /(ajax\/pagerender\.jsp|sharedpages\/ajax\/listviewpagerender\.jsp|AJAXRequestHandler\.do)/
        ).as("ajaxRender");

        cy.intercept(
          /(frameworks\/private\/api\/(translations|react))|(suite-react\/locales\/en)/
        ).as("reactRender");

        cy.intercept(/suite-react\/asset-manifest.json/).as("reactRender2");

        if (
          config("ENVIRONMENT") === "uat" ||
          config("ENVIRONMENT") === "breakfix" ||
          config("HAS_FR25_1")
        ) {
          SSO(credentials);
        }
        cy.visit("/", { timeout: 90_000 });
      }
    }
  });
}

export function generateClaimAndCreateNotification({
  scenario,
  employeePoolFileName,
  employerPoolFileName,
  absenceStatus,
  logSubmissionToNewRelic = false,
}: ClaimGenData): void {
  generateClaim(scenario, employeePoolFileName, employerPoolFileName).then(
    (claim) => {
      cy.stash("claim", claim);
      createClaim(claim, { logSubmissionToNewRelic, absenceStatus });
    }
  );
}

export function createClaim(
  claim: DehydratedClaim,
  options: {
    logSubmissionToNewRelic?: boolean;
    absenceStatus?: AbsenceStatus;
  } = {}
) {
  before();
  assertValidClaim(claim.claim);
  const dateOfBirth = format(new Date(claim.claim.date_of_birth), "MM/dd/yyyy");
  ClaimantPage.visit(claim.claim.tax_identifier)
    .editPersonalIdentification({
      date_of_birth: dateOfBirth,
      mass_id: claim.claim.mass_id,
    })
    .setPreferredLanguage(claim.preferredLanguage)
    .addAddressIfNone()
    .createNotification(claim.claim, {
      withholdingPreference: claim.is_withholding_tax,
      absenceStatus: options.absenceStatus,
    })
    .then((fineos_absence_id) => {
      cy.log(fineos_absence_id);
      cy.stash(
        "submission",
        {
          fineos_absence_id,
          timestamp_from: Date.now(),
        },
        { logToNewRelic: options.logSubmissionToNewRelic ?? false }
      );
    });
}

export function visitClaim(claimId: string): void {
  cy.get('a[aria-label="Cases"]').click();
  onTab("Case");
  cy.findByLabelText("Case Number").type(claimId);
  cy.findByLabelText("Case Type").select("Absence Case");
  cy.get('input[type="submit"][value="Search"]').click();
  assertAbsenceCaseNumber(claimId);
}

/**
 * Return to the Dashboard using the home icon on the left navigation.
 */
export function visitDashboard(): void {
  cy.get(`a[aria-label="Dashboard"]`).click();
}

/**
 * Go to tasks page using the icon on the left navigation.
 */
export function visitTasks(): void {
  cy.get(`a[aria-label="Tasks"]`).click();
}

export function assertMissingPaymentInformationAlert(): void {
  const selector = "table[id*='val_message_table'] span[id*='PageMessage1']";
  cy.get(selector).should(
    "not.contain",
    `Payment information required is missing: Average weekly wage / Post Benefit Year End AWW field = $0`
  );
}

export function assertMessageAlert(msg: NotificationPageError): void {
  const selector = "table[id*='val_message_table'] span[id*='PageMessage1']";
  cy.get(selector).should("contain", msg);
}

export function assertHasDocument(name: string): void {
  // This can be called from both the Party view and the Case view, which have slightly different IDs for the Documents table.
  cy.get("table[id^='DocumentsFor'][id*='ListviewWidget']").should((table) => {
    expect(table, `Expected to find a "${name}" document`).to.have.descendants(
      `a:contains("${name}")`
    );
  });
}

/**
 * Called from the claim page, asserts Absence Case is the expected value.
 */
export function assertAbsenceCaseNumber(claimNumber: string): void {
  cy.get(".case_pageheader_title").should((statusElement) => {
    expect(
      statusElement,
      `Absence Case ID should be: ${claimNumber}`
    ).to.contain.text(claimNumber);
  });
}

/**
 * Helper to switch to a particular tab.
 */
export function onTab(label: string): void {
  cy.contains(".TabStrip td", label).then((tab) => {
    if (tab.hasClass("TabOn")) {
      return; // We're already on the correct tab.
    }
    // Here we are splitting the action and assertion, because the tab class can be added after a re-render.
    cy.contains(".TabStrip td", label).click();
    waitForAjaxComplete();
    cy.contains(".TabStrip td", label).should("have.class", "TabOn");
  });
}

/**
 * Helper to switch to a particular tab in FR25_1.
 */
export function onFR25Tab(label: string): void {
  waitForAjaxComplete();
  cy.contains('[role="tab"]', label).then((tab) => {
    if (tab.attr("aria-selected") === "true") {
      return;
    }
    cy.contains('[role="tab"]', label).click();
    waitForAjaxComplete();

    // Assert tab is currently selected
    cy.contains('[role="tab"]', label).should(
      "have.attr",
      "aria-selected",
      "true"
    );
  });
}

const alertsIdSelector = config("HAS_FR25_1")
  ? "#AlertsHeaderWidget"
  : "#alertsHeader";

/**
 * Closes the popup for certain Alert Messages.
 */
export function closeAlertPopUp(): void {
  cy.get(`${alertsIdSelector} div`)
    .first()
    .then((el) => {
      if (!el.hasClass("hidden")) {
        cy.get(".bottom-close").click();
      }
      cy.wait(200);
    });
}

export function isAlertPopUpOpen() {
  return cy
    .get("#main")
    .then(
      (header) =>
        header.find(alertsIdSelector).length > 0 ||
        header.find("td[class$='_closeMessage']").length > 0
    );
}

export function ensureAlertPopUpClosed(): void {
  waitForAjaxComplete();
  cy.get("#main").then((header) => {
    if (header.find(alertsIdSelector).length > 0) {
      closeAlertPopUp();
    }
    if (header.find("td[class$='_closeMessage']").length > 0) {
      cy.get("table[id='val_message_table'] td[class$='_closeMessage'] a")
        .first()
        .click();
    }
  });
  waitForAjaxComplete();
}

/**
 * Helper to wait for ajax-y actions to complete before proceeding.
 *
 * Note: Please do not add explicit waits here. This function should be fast -
 * it will be called often. Try to find a better way to determine if we can move
 * on with processing (element detection).
 */
export function wait(): void {
  cy.wait("@ajaxRender");
  // The complicated cy.root().closest('html')... command makes sure this function can be used inside the cy.within() context
  cy.root()
    .should(($el) => {
      expect(Cypress.dom.isAttached($el)).to.be.true;
    })
    .closest(`html`)
    .should(($el) => {
      expect(Cypress.dom.isAttached($el)).to.be.true;
    })
    .find(`#disablingLayer`)
    .should(($el) => {
      expect(Cypress.dom.isAttached($el)).to.be.true;
    })
    .should("not.be.visible");
}

export function waitForAjaxComplete(): void {
  cy.wait(250);
  cy.window({ timeout: 30_000 })
    .invoke("axGetAjaxQueueManager", { timeout: 15_000 })
    .should((q) => {
      const inFlight = Object.values(q.requests).filter(
        // @ts-ignore - ignore uses of Fineos internal window properties.
        (req) => req.state() !== "resolved"
      );
      expect(inFlight, "In-flight Ajax requests should be 0").to.have.length(0);
    });
  cy.wait(350);
}

export function clickBottomWidgetButton(value = "OK"): void {
  waitForAjaxComplete();
  cy.get(`#PageFooterWidget input[value="${value}"]`).click();
}

export function addBondingLeaveFlow(timeStamp: Date): void {
  cy.get('a[aria-label="Add Time"]').click();
  cy.get('input[type="radio"][value*="another_reason_id"]').click();
  cy.get('input[type="submit"][title="Add Time Off Period"]').click();
  cy.wait("@ajaxRender");
  cy.wait(200);
  cy.get(".popup-container").within(() => {
    cy.findByLabelText("Absence status").select("Known");
    cy.wait("@ajaxRender");
    cy.wait(200);
    const startDate = addMonths(timeStamp, 2);
    const startDateFormatted = format(startDate, "MM/dd/yyyy");
    const endDateFormatted = format(addDays(startDate, 2), "MM/dd/yyyy");

    cy.findByLabelText("Absence start date").type(
      `{selectall}{backspace}${startDateFormatted}{enter}`
    );
    cy.wait("@ajaxRender");
    cy.wait(200);
    cy.findByLabelText("Absence end date").type(
      `{selectall}{backspace}${endDateFormatted}{enter}`
    );
    cy.wait("@ajaxRender");
    cy.wait(200);
    cy.get("input[type='checkbox'][id*='startDateAllDay_CHECKBOX']").click();
    cy.get("input[type='checkbox'][id*='endDateAllDay_CHECKBOX']").click();
    cy.get("input[type='submit'][value='OK']").click();
  });
  clickBottomWidgetButton("Next");
  cy.wait("@ajaxRender");
  cy.wait(200);
  // Work Pattern
  cy.get("input[type='checkbox'][id*='standardWorkWeek_CHECKBOX']").click();
  cy.findByLabelText("Pattern Status").select("Known");
  clickBottomWidgetButton("Next");
  cy.wait("@ajaxRender");
  cy.wait(200);
  // Complete Details
  cy.findByLabelText("Primary Relationship to Employee").select("Child");
  cy.wait("@ajaxRender");
  cy.wait(200);
  cy.wait("@ajaxRender");
  cy.wait(200);
  cy.findByLabelText("Qualifier 1").select("Biological");
  clickBottomWidgetButton("Next");
  // Additional Info
  clickBottomWidgetButton("Next");
  // Wrap up
  clickBottomWidgetButton("OK");
  // Assert bonding leave request was added
  cy.get("[id*='processPhaseEnum']").should("contain.text", "Adjudication");
  cy.get("[id*='requestedLeaveCardWidget']").should(
    "contain.text",
    "Pending leave"
  );
  cy.get(".absencePeriodDescription").should(
    "contain.text",
    "Fixed time off for Child Bonding"
  );
  cy.wait(1000);
}

export function findOtherLeaveEForm(claimNumber: string): void {
  visitClaim(claimNumber);
  onTab("Documents");
  assertHasDocument("Other Leaves");
  cy.wait(200);
}

/**Clicks on the 'Next' or 'Previous' button to move to the next/previous step during the intake process or recording actual leave */
export const clickNext = (
  buttonName: "Next" | "Previous" = "Next"
): Cypress.Chainable<JQuery<HTMLElement>> =>
  cy.get(`#nextPreviousButtons input[value*='${buttonName} ']`).click();
/**
 * Takes document type and returns fixture file name.
 * @param document_type document type as specified in the `claim.documents`
 * @returns name of the fixture file, see `e2e/cypress/fixtures`
 */
export function getFixtureDocumentName(
  document_type: FineosDocumentType
): string {
  switch (document_type) {
    case "Driver's License Mass":
    case "Identification Proof":
    case "Passport": {
      return "MA_ID" as const;
    }
    case "Driver's License Other State": {
      return "OOS_ID" as const;
    }
    case "Own serious health condition form": {
      return "HCP" as const;
    }
    case "Child bonding evidence form": {
      return "FOSTER" as const;
    }

    default: {
      return "HCP" as const;
    }
  }
}

/**
 * Asserts there's an error message displayed which matches a given text.
 * @param message text of the message
 * @example
 * fineos.assertErrorMessage("Hours worked per week must be entered");
 */
export function assertErrorMessage(message: string): void {
  cy.get(`#page_messages_container`).should("contain.text", message);
}

/**
 * Selects the folder at the end of the path, opening subfolders along the way as needed.
 * @param path path leading to the folder
 * @example
 * selectFolder(["State of Mass", "eForms"])
 */
export function selectFolder(path: string[]): void {
  const log = Cypress.log({
    displayName: "SELECT FOLDER",
    message: [`Opening path: ${JSON.stringify(path)}`],
    // @ts-ignore
    autoEnd: false,
  });
  log.snapshot("before");

  const lastPathIndex = path.length - 1;
  let treePathId = "0"; // Starts at root folder
  path.forEach((subfolder: string, index: number) => {
    cy.get(`[treepathid=${treePathId}]`, { log: false }).within(() => {
      // Get folder with specific name, and then either expand it or select it
      cy.contains(".TreeNodeElement", new RegExp(`^${subfolder}$`, "i"), {
        log: false,
      })
        .closest(".TreeNodeContainer", { log: false })
        .then(($treeNodeContainer) => {
          const currentTreepathId = $treeNodeContainer.attr("treepathid");
          if (!currentTreepathId) {
            throw new Error(
              "Found tree node container with no 'treepathid' attribute"
            );
          }
          treePathId = currentTreepathId; // Update subfolder tree path id

          // Expand subfolder if it's not the last item; otherwise, select it
          if (index < lastPathIndex) {
            expandSubfolder(treePathId);
          } else {
            selectFinalSubfolder(treePathId);
          }
        });
    });
  });

  log.snapshot("after");
  log.end();
}

function expandSubfolder(treePathId: string) {
  // Find and click subfolder expand handle
  cy.get(`[treepathid=${treePathId}] #nodeHandle`, { log: false })
    .first({ log: false }) // Get this subfolder's node handle and not one of it's children's
    .find("img")
    .then(($img) => {
      // Using icon/img state due to Fineos inconsistent toggling of collapse/expanded class
      const isExpanded = $img.attr("title") === "Collapse";
      if (!isExpanded) {
        cy.wrap($img, { log: false }).click({ log: false });
        waitForAjaxComplete(); // Entire tree directory get's re-rendered
      }
    });
}

function selectFinalSubfolder(treePathId: string) {
  // Find and click on subfolder/node
  cy.get(`[treepathid=${treePathId}]`, { log: false }).then(($container) => {
    if ($container.find(".TreeNodeSelected").length === 0) {
      const subfolderNode = $container.find(".TreeNodeElement").first();
      cy.wrap(subfolderNode, {
        log: false,
      }).click({ log: false });

      waitForAjaxComplete(); // Entire tree directory get's re-rendered
    }
  });
}

/**
 * Opens the folder at the end of a given path, checks if contains given document(s)
 * @param documentName a string or an array of strings describing documents you expect to find.
 * @param path path to folder containing the documents as an array of strings.
 * @example
 * const certificationDocuments = ["Own serious health condition form", "Pregnancy/Maternity form"]
 * assertDocumentsInFolder(certificationDocuments, ["State of Mass", "Inbound Documents"]);
 * //Opens folder located at root/"State of Mass"/"Inbound Documents"
 */
export function assertDocumentsInFolder(
  documentName: string | string[],
  path: string[]
): void {
  selectFolder(path);
  if (Array.isArray(documentName)) {
    return documentName.forEach((name) => {
      return cy.get("#DocumentTypeListviewWidget").should("contain.text", name);
    });
  }
  cy.get("#DocumentTypeListviewWidget").should("contain.text", documentName);
}

/**
 * Find the Appeals claim subcase number
 * @return appeal_case_id
 */
export function findAppealNumber(type: string): Cypress.Chainable<string> {
  const appealmatcher = new RegExp(
    `${type} - (NTN-[0-9]{1,6}-[A-Z]{3}-[0-9]{2}-[A-Z]{2}-[0-9]{2})`
  );
  return cy.findAllByText(appealmatcher).then((el) => {
    const match = el.text().match(appealmatcher);
    if (!match) {
      throw new Error();
    }
    return cy.wrap(match[1]);
  });
}

/**
 * Returns claim adjudication status wrapped in Cypress.Chainable.
 * @returns Adjudication status of the claim
 * @example
 * fineos.getClaimStatus().then((status) => {
 *  if (status === "Approved"){
 *    //...your code here
 *  }
 * }
 */
export function getClaimStatus(): Cypress.Chainable<
  | "Adjudication"
  | "Approved"
  | "Declined"
  | "Closed"
  | "In Review"
  | "Completed"
> {
  return cy.get(".key-info-bar .status dd").invoke("text");
}

export function claimHasAppeal(): Cypress.Chainable<boolean> {
  return cy
    .get("div.caseMapBorder")
    .find(".tabset .hoverContainer a")
    .then((els) => {
      const elements = [...els].map((el) => el.innerText.trim());
      const options = new Set(elements);
      return cy.wrap(options.has("Appeal"));
    });
}

export function goToOrganizationsTabAndSearchEmployer(fein: string): void {
  cy.get('a[aria-label="Parties"]').click();
  waitForAjaxComplete();
  cy.contains("label", "Organization").click();
  cy.findByLabelText("Identification Number").type(fein?.replace("-", ""), {
    delay: 10,
  });
  cy.get('input[type="submit"][value="Search"]').click();
  waitForAjaxComplete();
  clickBottomWidgetButton("OK");
}

export function checkNewlyEEAddedToTheERMasterPlan(
  masterPlanData: MasterPlanData
): void {
  onTab("Affiliates");
  cy.get(`a[id^="optionalInfoWidget1_"][id$="_serviceAgreementLink"]`).click();
  onTab("Employer");
  onTab("Master Plan");

  cy.on("window:before:load", (win) => {
    cy.stub(win, "open")
      .as("open")
      .callsFake((url) => {
        // @ts-ignore - wrappedMethod used to get original unstubbed win.open()
        return win.open.wrappedMethod.call(win, url, "_self");
      });
  });
  cy.get(`a[id^="masterPlanDetailsWidget_"][id$="_caseNumber"]`).click();
  cy.get("@open").should("have.been.calledOnce");

  waitForAjaxComplete();
  onTab("Members");
  cy.wait("@reactRender", { timeout: 30_000 });
  cy.findByPlaceholderText("e.g. 12345678").type(
    `${masterPlanData?.employeeId}{enter}`,
    {
      delay: 10,
    }
  );
  cy.get(`.Field_MemberId > div`).should((employeeIDElement) => {
    expect(
      employeeIDElement,
      `Expected to find the following employee by '${masterPlanData?.employeeId}'.`
    ).to.have.text(masterPlanData?.employeeId);
  });
}

export function hasAccessToHistory(): void {
  cy.get("a.icon-config").click();
  cy.contains("Company Structure").click();
  waitForAjaxComplete();
  cy.contains("div.TreeNodeContainer", "DI Life Servicing Company(sec)")
    .find("span#nodeHandle")
    .then((el) => {
      cy.wrap(el, { log: false }).eq(0).click({ log: false });
      waitForAjaxComplete();
    });
  cy.contains("div.TreeNodeContainer", "Absence(sec)")
    .find("span#nodeHandle")
    .then((el) => {
      cy.wrap(el, { log: false }).eq(0).click({ log: false });
      waitForAjaxComplete();
    });
  cy.contains("div.TreeNodeContainer", "DFML(sec)")
    .find("span#nodeHandle")
    .then((el) => {
      cy.wrap(el, { log: false }).eq(0).click({ log: false });
      waitForAjaxComplete();
    });
  cy.contains("div.TreeNodeContainer", "DFML PI(sec)")
    .find("span#nodeHandle")
    .then((el) => {
      cy.wrap(el, { log: false }).eq(0).click({ log: false });
      waitForAjaxComplete();
    });
  cy.contains("div.TreeNodeContainer", "DFML Claims Supervisors(sec)").click();
  fineos.onTab("Secured Actions");
  cy.contains("FO_ALLOW_ACCESS_TO_BO_HISTORY").should("exist");
}

function ensureNewPageOpensInSameWindow(): void {
  cy.on("window:before:load", (win) => {
    cy.stub(win, "open")
      .as("open")
      .callsFake((url) => {
        // @ts-ignore - wrappedMethod used to get original unstubbed win.open()
        return win.open.wrappedMethod.call(win, url, "_self");
      });
  });
}

function openHistoryPage() {
  cy.get("[title*='View history of changes for ']").click();
  cy.contains("History For ");
}

export function canViewHistoryForAbsenceCase(): void {
  turnOnHistory();
  fineos.onTab("Leave Details");
  cy.get('input[type="submit"][value="Edit"]').click();
  fineos.onTab("Request Information");
  const randomDate = formatDateUS(addMonths(new Date(), -1), "/");
  cy.get('input[id$="notificationDate"]').clear();
  cy.get('input[id$="notificationDate"]').type(randomDate);
  cy.get('select[id$="notificationMethod"]').select("In Writing");
  cy.get('input[id$="editPageSave"][value="OK"]').click();
  waitForAjaxComplete();
  ensureNewPageOpensInSameWindow();
  cy.get('input[type="submit"][value="Edit"]').click();
  fineos.onTab("Request Information");
  openHistoryPage();
  cy.contains("td", randomDate).should("exist");
}

export function canViewHistoryForPaidBenefits(): void {
  turnOnHistory();
  fineos.onTab("Leave Details");
  cy.get("[id*=_editLeaveRequestButton]").click();
  fineos.onTab("Paid Benefits");
  cy.get('input[type="submit"][value="Edit"]').click();
  cy.get("span[title='View history of changes for PaidLeaveInstruction']");

  cy.get("input[id$=_averageWeeklyWage]").type("{selectAll}{backspace}1500");
  cy.get("select[id*=_benefitWaitingPeriodBasis]").select("Working Days");
  cy.get("input[id$=_benefitWaitingPeriod]").type("{selectAll}{backspace}4");

  cy.get('input[id$="editPageSave"][value="OK"]').click();
  waitForAjaxComplete();

  ensureNewPageOpensInSameWindow();
  fineos.onTab("Paid Benefits");
  cy.get('input[type="submit"][value="Edit"]').click();
  openHistoryPage();
}

function toggleAnalyticsDropdown() {
  cy.findByTitle("Reporting & Analytics").click();
}

export function turnOnHistory() {
  toggleAnalyticsDropdown();
  cy.contains("span.LinkText", "View History [off]").trigger("click");
}

export function viewHistoryButtonDisabled() {
  toggleAnalyticsDropdown();
  cy.contains("span.LinkText", "View History [off]")
    .parent()
    .should("have.class", "ButtonDisabled");
}

export function ensureOnManagePlansPage() {
  ensurePanelIsNotCollapsed("leave-request-panel");
  cy.get("body").then((body) => {
    // Check if the panel exists on the current page
    if (
      body.find('[data-testid^="leave-plan-applicability-panel"]').length > 0
    ) {
      cy.get('[data-testid^="leave-plan-applicability-panel"]').should(
        "be.visible"
      );
    } else {
      // If the panel is not found, navigate to the correct page
      cy.get('[data-testid="manage-plans-button-testid"]').click();
      waitForAjaxComplete();
      cy.get('[data-testid^="leave-plan-applicability-panel"]')
        .should("exist")
        .and("be.visible");
    }
  });
}

function isPanelCollapsed() {
  waitForLoad();
  return cy.get(".ant-collapse-item").then((header) => {
    return !header.hasClass("ant-collapse-item-active");
  });
}

function isCaseSummaryPanelStatusCollapsed() {
  waitForLoad();

  return cy.get(".collapsiblepanel_sidebar").then((panel) => {
    return panel.hasClass("collapsiblepanel_collapsed");
  });
}

function toggleCollapse(panelTestId: string) {
  cy.get(`[data-testid^='${panelTestId}']`).within(() => {
    cy.get(".ant-collapse-header").click();
  });
}

function toggleCaseSummaryPanel() {
  cy.get(".collapsiblepanel_open.collapsiblepanel_enabled").click();
}

export function ensurePanelIsNotCollapsed(panelTestId: string) {
  if (config("HAS_FR25_1")) {
    isPanelCollapsed().then((isCollapsed) => {
      if (isCollapsed) {
        toggleCollapse(panelTestId);
      }
    });
  }
}

export function ensureCaseSummaryPanelIsNotCollaspsed() {
  isCaseSummaryPanelStatusCollapsed().then((isCollapsed) => {
    if (isCollapsed) {
      toggleCaseSummaryPanel();
    }
  });
}

export function openLeaveRequestDetails() {
  ensurePanelIsNotCollapsed("leave-request-panel");
  cy.get("body").then((element) => {
    if (
      element.find("[data-testid='leave-request-leave-plans-tab']").length < 1
    ) {
      cy.findByTestId("leave-request-id").click();
      clickBottomWidgetButton("Close");
    }
  });

  cy.wait("@ajaxRender");
  cy.wait("@reactRender");
  waitForAjaxComplete();
  cy.get("[data-testid='leave-request-leave-plans-tab']")
    .should("be.visible")
    .click();
  cy.wait("@ajaxRender");
  cy.wait("@reactRender");
  waitForAjaxComplete();
}

function waitForLoad() {
  cy.contains("Processing", { timeout: 10_000 }).should("not.exist");
  cy.contains("Loading", { timeout: 10_000 }).should("not.exist");
}
