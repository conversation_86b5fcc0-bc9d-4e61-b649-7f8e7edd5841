import { parseISO } from "date-fns";

import { BenefitYearResponse } from "../../../../src/_api";
import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import {
  asFixed,
  calculateWeeklyBenefit,
  getIndividualAverageWeeklyWages,
} from "../../../../src/util/benefit";
import {
  emailAddresses,
  getClaimantCredentials,
} from "../../../../src/util/credentials";
import { getClaimWithCompletedPaymentsQuery } from "../../../../src/util/queries";
import { findScenarioByName } from "../../../../src/util/scenarios";
import {
  assertIsTypedArray,
  assertValidClaim,
  isValidEmployerBenefit,
  isValidOtherIncome,
} from "../../../../src/util/typeUtils";
import { email, fineos, fineosPages, portal } from "../../../actions";
import {
  DehydratedCompletedPaymentPeriod,
  hydrateCompletedPaymentPeriod,
} from "../../../actions/fineos.pages";
import {
  checkNoticeForClaimant,
  PaymentDetail,
  viewPaymentDetails,
} from "../../../actions/portal";
import { unstashMultipleKeys } from "../../../util";

type ClaimInfo = {
  fineos_absence_id: string;
  created_at: string;
  absence_period_start_date: string;
};

const emailAddress = emailAddresses.paymentStatus2;
const credentials = getClaimantCredentials(emailAddress);

// Due to weekly db refreshes, we cannot test sent payments in trn or trn2
const environmentIsExcluded = ["training", "trn2"].includes(
  config("ENVIRONMENT")
);

const scenario = "BHAP1_OLB_ER";
const scenarioSpec = findScenarioByName(scenario);

const childSupportNotice = "Notice of Child Support Withholding";

describe("Claimant can view a payment status break down for individual leave periods in Portal.", () => {
  before(function () {
    if (environmentIsExcluded) {
      this.skip();
    }
  });

  describe("Approve the claim, apply reductions and submit notice", () => {
    const approvedClaim =
      it("CSR will approve and apply reductions to the claim", () => {
        fineos.before();
        portal.generateAndSubmitClaimToAPI({
          credentials,
          logSubmissionToNewRelic: true,
          scenario,
        });
        cy.unstash<DehydratedClaim>("claim").then((claim) => {
          const employerReportedBenefit =
            claim.employerResponse?.employer_benefits?.[0];
          if (!employerReportedBenefit) {
            throw Error("claim.employer_benefits undefined");
          }
          cy.unstash<Submission>("submission").then((submission) => {
            fineosPages.ClaimPage.visit(submission.fineos_absence_id)
              .completeAdjudication(claim.documents)
              .tasks((tasks) => {
                tasks.close("Employee Reported Other Income");
              })
              .appealDocuments((docPage) => {
                docPage.addAppealDocument(childSupportNotice);
              })
              .triggerNotice(childSupportNotice)
              .approve()
              .paidLeave((paidLeavePage) => {
                claim.claim.employer_benefits = [employerReportedBenefit];
                assertValidClaim(claim.claim);
                const { other_incomes } = claim.claim;
                const employer_benefits =
                  claim.employerResponse?.employer_benefits;
                const { childSupportReductions } = scenarioSpec.claim;
                assertIsTypedArray(other_incomes, isValidOtherIncome);
                assertIsTypedArray(employer_benefits, isValidEmployerBenefit);
                paidLeavePage.applyReductions({
                  childSupportReductions,
                  other_incomes,
                  employer_benefits,
                });
              })
              .triggerNotice("Designation Notice");
          });
        });
      });

    it("should display notices in claimant portal", () => {
      cy.dependsOnPreviousPass([approvedClaim]);
      portal.before();
      cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
        portal.loginClaimant(credentials);
        portal.claimantGoToClaimStatus(fineos_absence_id);
        checkNoticeForClaimant("Child Support (PDF)");
      });
    });

    it(`should send a notification to the claimant`, { retries: 0 }, () => {
      cy.dependsOnPreviousPass([approvedClaim]);
      unstashMultipleKeys<{ claim: DehydratedClaim; submission: Submission }>([
        "claim",
        "submission",
      ]).then(({ claim, submission }) => {
        const subject = email.getNotificationSubject(
          "child support notice",
          submission.fineos_absence_id,
          `${claim.claim.first_name} ${claim.claim.last_name}`
        );
        email.getEmails(
          {
            address: "<EMAIL>",
            subject: subject,
            message: {
              pattern: `${submission.fineos_absence_id}.*You can see your Child Support notice with more information`,
            },
            timestamp_from: submission.timestamp_from,
            debugInfo: {
              fineosClaimID: submission.fineos_absence_id,
              triggerType: childSupportNotice,
              recipient: "Claimant",
            },
          },
          90_000
        );
      });
    });
  });

  describe("Claimant can view the payment details page in the claimant portal for a claim with completed payments", () => {
    before(() => {
      const query = getClaimWithCompletedPaymentsQuery(emailAddress);
      cy.task<ClaimInfo>("queryDb", query).then((result) => {
        if (!result) {
          throw Error("Couldn't find claim suitable for testing");
        }
        cy.stash("claimToUse", result);
      });
    });

    const scrapePayments =
      it("Finds actual payment information in FINEOS", () => {
        fineos.before();
        cy.unstash<ClaimInfo>("claimToUse").then((claimInfo) => {
          fineosPages.ClaimPage.visit(claimInfo.fineos_absence_id).paidLeave(
            (leaveCase) => {
              leaveCase
                .getPaymentPeriodsWithEffectiveDates()
                .then((paymentPeriod) => {
                  cy.stash("paymentPeriod", paymentPeriod);
                });
              leaveCase.getWorkingHours().then((hoursPerDay) => {
                cy.stash("hoursPerDay", hoursPerDay);
              });
            }
          );
        });
      });

    it("As a claimant, I can see a payment breakdown for a completed claim", () => {
      cy.dependsOnPreviousPass([scrapePayments]);
      portal.before();
      cy.unstash<ClaimInfo>("claimToUse").then((claimInfo) => {
        portal.loginClaimant(credentials);
        portal.claimantGoToClaimStatus(claimInfo.fineos_absence_id, {
          waitForAliases: ["@benefitYearsSearch"],
          visitLink: true,
        });
        portal.viewPaymentStatus();
        cy.unstash<number>("hoursPerDay").then((hoursPerDay) => {
          cy.unstash<DehydratedCompletedPaymentPeriod[]>("paymentPeriod").then(
            (paymentPeriod) => {
              cy.task("getBenefitYears", claimInfo.fineos_absence_id).then(
                (benefitYears) => {
                  const rateYear = getRateYear(
                    benefitYears,
                    claimInfo.fineos_absence_id
                  );

                  cy.wrap(paymentPeriod).each(
                    (period: DehydratedCompletedPaymentPeriod) => {
                      const hydratedPaymentPeriod =
                        hydrateCompletedPaymentPeriod(period);
                      const payments: PaymentDetail[] =
                        hydratedPaymentPeriod.periods.map((period) => ({
                          ...generatePaymentDetails(hoursPerDay, rateYear),
                          leavePeriod: [
                            period.datePeriodStart,
                            period.datePeriodEnd,
                          ],
                        }));
                      viewPaymentDetails(hydratedPaymentPeriod);
                      portal.assertPaymentDetails(payments);
                    }
                  );
                }
              );
            }
          );
        });
      });
    });
  });

  after(() => {
    portal.deleteDownloadsFolder();
  });
});

function generatePaymentDetails(
  certifiedHoursPerDay: number,
  rateYear: number
) {
  // This is required due to EDM-1286. It should be removed after the EDM is resolved and no more
  // affected claims can be pulled for testing.
  let paymentModifier = 1;
  switch (certifiedHoursPerDay) {
    case 4: {
      paymentModifier = 0.5;
      break;
    }
    case 12: {
      paymentModifier = 0.9;
      break;
    }
    default: {
      break;
    }
  }

  const adjust = (amount: number) => asFixed(amount * paymentModifier);

  const weeklyBenefit = calculateWeeklyBenefit(
    getIndividualAverageWeeklyWages(scenario),
    rateYear
  );
  const medicalLeaveInsurance = 500;
  const otherEmploymentIncome = 200;
  const childSupportReduction = 100;
  const benefitMinusOtherIncome = adjust(
    weeklyBenefit - medicalLeaveInsurance - otherEmploymentIncome
  );
  const sit = asFixed(benefitMinusOtherIncome * 0.05);
  const fit = asFixed(benefitMinusOtherIncome * 0.1);
  const paymentInfo = {
    grossPayment: adjust(weeklyBenefit),
    medicalLeaveInsurance: adjust(medicalLeaveInsurance),
    otherEmploymentIncome: adjust(otherEmploymentIncome),
    childSupportReduction: adjust(childSupportReduction),
    sit,
    fit,
    netPayment: benefitMinusOtherIncome - childSupportReduction - sit - fit,
  };
  console.log(paymentInfo);
  return paymentInfo;
}

function getRateYear(
  benefitYears: readonly BenefitYearResponse[],
  fineosAbsenceId: string
) {
  const currentBenefitYear = benefitYears.find(
    (year) => year.current_benefit_year
  );

  if (!currentBenefitYear) {
    throw new Error(
      `Could not find current benefit year for ${fineosAbsenceId}.`
    );
  }

  if (!currentBenefitYear.benefit_year_start_date) {
    throw new Error(
      `Current benefit year for ${fineosAbsenceId} does not have a start date.`
    );
  }

  return parseISO(currentBenefitYear.benefit_year_start_date).getFullYear();
}
