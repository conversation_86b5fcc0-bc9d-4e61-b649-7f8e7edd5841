import { format, subDays } from "date-fns";

import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import { extractLeavePeriod } from "../../../../src/util/claims";
import { assertValidClaim } from "../../../../src/util/typeUtils";
import {
  claim,
  claimantPages,
  email,
  fineos,
  fineosPages,
  portal,
} from "../../../actions";
import { requestModification } from "../../../actions/portal";

describe("Change leave end date request through the Portal", () => {
  after(portal.deleteDownloadsFolder);

  const submission =
    it("Submits a Care for a Family Member claim via the portal", () => {
      portal.before();
      claim.generateClaim("CCAP90ERSP").then((claim) => {
        cy.stash("claim", claim);
        portal.skipLoadingClaimantApplications();
        portal.loginClaimant();
        const application = claim.claim;
        portal.startClaimAndSubmitClaimPartOne(
          application,
          undefined,
          claim.employer
        );
        portal.waitForClaimSubmission({ logSubmissionToNewRelic: true });
        portal.submitClaimPartsTwoThree(application, claim.paymentPreference, {
          is_withholding_tax: claim.is_withholding_tax,
        });
      });
    });

  const employerApproval =
    it("Leave admin will submit ER approval for employee", () => {
      cy.dependsOnPreviousPass([submission]);
      portal.before();
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<Submission>("submission").then((submission) => {
          assertValidClaim(claim.claim);
          portal.loginLeaveAdmin(claim.claim.employer_fein);
          portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
          portal.visitActionRequiredERFormPage(submission.fineos_absence_id);
          portal.respondToLeaveAdminRequest({
            approval: true,
            gaveNotice: true,
          });
        });
      });
    });

  const approval = it("CSR will begin adjudication & Approve the Claim", () => {
    cy.dependsOnPreviousPass([employerApproval]);
    fineos.before();
    cy.unstash<DehydratedClaim>("claim").then((claim) => {
      cy.unstash<Submission>("submission").then((res) => {
        if (!claim.claim.tax_identifier) {
          throw new Error(
            'Unable to visit party record. Claim does not have a "tax_identifier".'
          );
        }

        const claimantPage = claimantPages.ClaimantPage.visit(
          claim.claim.tax_identifier
        );

        if (!claim.preferredLanguage) {
          throw new Error(
            'Unable to assert preferred language. Claim does not have a "preferredLanguage".'
          );
        }

        claimantPage.assertPreferredLanguage(claim.preferredLanguage);

        const claimPage = fineosPages.ClaimPage.visit(res.fineos_absence_id);

        claimPage.adjudicate((adjudicate) => {
          adjudicate
            .evidence((evidence) => {
              claim.documents.forEach((document) => {
                evidence.receive(document.document_type);
              });
            })
            .certificationPeriods((cert) => cert.prefill())
            .acceptLeavePlan(config("HAS_FR25_1"));
        });
        claimPage.leaveDetails((leaveDetails) => {
          leaveDetails.acceptLeave();
        });
        claimPage.approve("Completed");
        claimPage.triggerNotice("Designation Notice");
        claimPage.triggerNotice("Approval Notice Explanation of Wages");
      });
    });
  });

  it("Claimant should be able to download approval notice in preferred language", function () {
    cy.dependsOnPreviousPass([approval]);
    portal.before();
    portal.loginClaimant();

    cy.unstash<Submission>("submission").then((submission) => {
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.checkNoticeForClaimant("Approval notice (PDF)");
      portal.downloadNoticeAndAssertContent(
        "Approval notice (PDF)",
        "Aviso de Aprobación de Solicitud"
      );
    });
  });

  it("Leave administrator should be able download approval notice in claimant's preferred language", function () {
    cy.dependsOnPreviousPass([approval]);
    portal.before();

    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        if (!claim.employer_fein) {
          throw new Error(
            'Unable to log in as leave admin. Claim does not have an "employer_fein".'
          );
        }

        const employeeFullName = `${claim.first_name} ${claim.last_name}`;
        portal.loginLeaveAdmin(claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        portal.checkNoticeForLeaveAdmin(
          employeeFullName,
          "Approval notice (PDF)"
        );
        portal.downloadNoticeAndAssertContent(
          "Approval notice (PDF)",
          "Aviso de Aprobación de Solicitud"
        );
      });
    });
  });

  const claimantRequestDateChange =
    it("As a Claimant I can request to end my leave sooner via the portal", () => {
      cy.dependsOnPreviousPass([approval]);
      portal.before();
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then((claim) => {
          portal.loginClaimant();
          portal.claimantGoToClaimStatus(submission.fineos_absence_id);
          // submit request to end leave sooner
          if (claim.claim.leave_details === undefined) {
            throw new Error("Leave dates cannot be undefined.");
          }
          const [, endDate] = extractLeavePeriod(claim.claim);
          const subtracted = subDays(endDate, 2);
          requestModification("endLeaveEarly", subtracted);
        });
      });
    });

  it("As an agent I can process the change leave end date request in FINEOS", () => {
    cy.dependsOnPreviousPass([claimantRequestDateChange]);
    fineos.before();
    cy.unstash<DehydratedClaim>("claim").then((claim) => {
      cy.unstash<Submission>("submission").then((submission) => {
        const claimPage = fineosPages.ClaimPage.visit(
          submission.fineos_absence_id
        );
        // My work is triggered by a “Record Leave Period Removal” task.
        // So I should be able to see it under the Tasks tab.
        claimPage
          .appealTasks((task) => {
            task.assertTaskExists("Record Leave Period Removal");
          })
          // Next, in the documents tab,
          .documents((documents) => {
            // I can then see the Cancel Time eForm.
            documents
              .assertDocumentExists("Cancel Time")
              // When I open the eForm,
              .openEForm("Cancel Time", (eFormPage) => {
                // I can see the dates of cancellation the claimant has requested
                if (claim.claim.leave_details === undefined) {
                  throw new Error("Leave dates cannot be undefined.");
                }
                const [, endDate] = extractLeavePeriod(claim.claim);
                eFormPage.assertInputValueWithNoLabel(
                  "input[id$='_Date1']",
                  format(subDays(endDate, 1), "MM/dd/yyyy")
                );
                eFormPage.assertInputValueWithNoLabel(
                  "input[id$='_EndDate1']",
                  format(endDate, "MM/dd/yyyy")
                );
                eFormPage.assertInputValueWithNoLabel(
                  "textarea[id$='_AdditionalInformation']",
                  "Cancellation"
                );
              });
          });
      });
    });
  });

  it(
    "Claimant should receive an approval notification in preferred language",
    { retries: 0 },
    function () {
      cy.dependsOnPreviousPass([approval]);

      cy.unstash<Submission>("submission").then((submission) => {
        const pattern = config("HAS_NOTICE_ENHANCEMENTS")
          ? `Su solicitud de permiso familiar o médico pagado.*${submission.fineos_absence_id}.*, ha sido aprobada.`
          : submission.fineos_absence_id;
        email.getEmails({
          address: "<EMAIL>",
          subject: "Su solicitud de permiso pagado fue aprobada.",
          message: { pattern },
          timestamp_from: submission.timestamp_from,
          debugInfo: {
            fineosClaimID: submission.fineos_absence_id,
            triggerType: "Approval Notice Explanation of Wages",
            recipient: "Claimant",
          },
        });
      });
    }
  );
});
