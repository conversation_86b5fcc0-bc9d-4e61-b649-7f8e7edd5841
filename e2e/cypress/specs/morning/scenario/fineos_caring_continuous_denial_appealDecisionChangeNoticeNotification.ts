import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import { generateDateOfBirth } from "../../../../src/util/pii";
import { assertValidClaim } from "../../../../src/util/typeUtils";
import { claim, email, fineos, fineosPages, portal } from "../../../actions";
import { ClaimantPage } from "../../../actions/fineos.claimant";

const NOTICE_TYPE = "Appeal Acknowledgment (PDF)";

describe("Create a new continuous leave, caring leave claim in FINEOS", () => {
  const fineosSubmission =
    it("Should be able to create a Care for a Family Member claim", () => {
      fineos.before();
      claim.generateClaim("CDENY2").then((claim) => {
        cy.stash("claim", claim);
        assertValidClaim(claim.claim);
        ClaimantPage.visit(claim.claim.tax_identifier)
          .editPersonalIdentification({
            date_of_birth: generateDateOfBirth(),
          })
          .setPreferredLanguage(claim.preferredLanguage)
          .addAddressIfNone()
          .createNotification(claim.claim, {
            withholdingPreference: claim.is_withholding_tax,
          })
          .then((fineos_absence_id) => {
            cy.stash(
              "submission",
              {
                fineos_absence_id: fineos_absence_id,
                timestamp_from: Date.now(),
              },
              { logToNewRelic: true }
            );
            fineosPages.ClaimPage.visit(fineos_absence_id).adjudicate(
              (adjudication) => {
                adjudication.evidence((evidence) =>
                  claim.documents.forEach(({ document_type }) =>
                    evidence.receive(document_type)
                  )
                );
              }
            );
          });
      });
    });

  it("Leave admin will submit ER denial for employee", () => {
    cy.dependsOnPreviousPass([fineosSubmission]);
    portal.before();
    cy.unstash<DehydratedClaim>("claim").then((claim) => {
      cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
        assertValidClaim(claim.claim);
        portal.loginLeaveAdmin(claim.claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(fineos_absence_id);
        Cypress.currentRetry == 0 &&
          portal.visitActionRequiredERFormPage(fineos_absence_id);
        portal.respondToLeaveAdminRequest({
          approval: false,
          gaveNotice: true,
          isCaringLeave: true,
        });
      });
    });
  });

  const denyClaim = it("CSR will deny claim", () => {
    cy.dependsOnPreviousPass([fineosSubmission]);
    fineos.before();
    cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
      fineosPages.ClaimPage.visit(fineos_absence_id)
        .adjudicate((adjudication) => {
          if (config("HAS_FR25_1")) {
            adjudication.rejectLeavePlan();
          }
        })
        .deny("Covered family relationship not established", false, false);
    });
  });

  const csrAppeal = it("CSR will process a decision change", () => {
    cy.dependsOnPreviousPass([fineosSubmission, denyClaim]);
    fineos.before();
    cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
      const claimPage = fineosPages.ClaimPage.visit(fineos_absence_id, true);
      claimPage.addAppeal();
      claimPage.appealDocuments((docPage) => {
        docPage.uploadDocument("Appeals Supporting Documentation");
      });

      claimPage.triggerNotice("SOM Generate Appeals Notice");
      claimPage.appealDocuments((docPage) => {
        docPage.assertDocumentExists("Appeal Acknowledgment");
      });
      claimPage.appealDocuments((docPage) => {
        docPage.uploadDocument("Appeal Notice - Claim Decision Changed");
      });

      claimPage.denyAppeal();
    });
  });

  it("Should generate a Appeal Acknowledgment that the Leave Admin can view and download it", () => {
    cy.dependsOnPreviousPass([fineosSubmission, csrAppeal]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        if (!claim.claim.employer_fein) {
          throw new Error("Claim must include employer FEIN");
        }

        const employeeFullName = `${claim.claim.first_name} ${claim.claim.last_name}`;
        portal.loginLeaveAdmin(claim.claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);

        portal.checkNoticeForLeaveAdmin(employeeFullName, NOTICE_TYPE);
        portal.downloadLegalNoticeSubcase(
          submission.fineos_absence_id,
          NOTICE_TYPE
        );
      });
    });
  });

  it(
    "Check the Leave Admin email for the appeal notification.",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([fineosSubmission, csrAppeal]);
      portal.before();
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<ApplicationRequestBody>("claim").then((claim) => {
          const subjectEmployer = email.getNotificationSubject(
            "appeal (employer)",
            submission.fineos_absence_id,
            `${claim.first_name} ${claim.last_name}`
          );
          email
            .getEmails(
              {
                address: "<EMAIL>",
                subject: subjectEmployer,
                message: submission.fineos_absence_id,
                timestamp_from: submission.timestamp_from,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Appeal Acknowledgment",
                  recipient: "Leave Admin",
                },
              },
              90_000
            )
            .then(() => {
              cy.get(
                `a[href*="/employers/applications/status/?absence_id=${submission.fineos_absence_id}"]`
              );
            });
        });
      });
    }
  );
});
