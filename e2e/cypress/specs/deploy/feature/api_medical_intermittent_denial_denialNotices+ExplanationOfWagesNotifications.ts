import { Submission } from "../../../../src/types";
import { assertValidClaim } from "../../../../src/util/typeUtils";
import { claim, email, fineos, fineosPages, portal } from "../../../actions";
import { reduceToStepsOneTwoOfPartOne } from "../../../util";

const DOCUMENT_TYPE = "Denial of Application";
const CLAIMANT_DENIAL_NOTICE = "Denial Notice (PDF)";
const EMPLOYER_DENIAL_NOTICE = "Denial of Application (PDF)";

describe("Claim Denial Notification and Notice", () => {
  before(() => {
    portal.deleteDownloadsFolder();
  });
  const submit = it("Submits a claim", () => {
    const scenario = "MED_INTER_INEL";
    portal.before();
    claim.generateClaim(scenario).then((claim) => {
      cy.stash("claim", claim);
      const partsOne = reduceToStepsOneTwoOfPartOne(claim.claim);
      cy.task<string | undefined>("submitStepsOneTwoOfPartOne", {
        application: partsOne,
      }).then((applicationId) => {
        if (!applicationId) {
          throw Error("Application did not create successfully");
        }
        const application: ApplicationRequestBody = claim.claim;
        const paymentPreference = claim.paymentPreference;
        portal.skipLoadingClaimantApplications();
        portal.loginClaimant();
        portal.wrapUpPartOne(applicationId, application);
        portal.waitForClaimSubmission({ logSubmissionToNewRelic: true });
        portal.submitClaimPartsTwoThree(application, paymentPreference, {
          is_withholding_tax: claim.is_withholding_tax,
          alternateForm: "Employer FMLA",
        });
      });
    });
  });

  const deny = it("CSR Rep will deny the claim", () => {
    cy.dependsOnPreviousPass([submit]);
    fineos.before();
    cy.unstash<Submission>("submission").then((submission) => {
      const claimPage = fineosPages.ClaimPage.visit(
        submission.fineos_absence_id
      );
      claimPage.shouldHaveStatus("Eligibility", "Not Met");
      claimPage.deny("Claimant wages failed 30x rule", true, false);
      // Trigger Preliminary Designation to prevent 1 minute time trigger from
      // interfering with other triggers. If the Preliminary Designation
      // triggers while trying to manually trigger other notices, FINEOS will
      // display a "data out of date" error page.
      claimPage.triggerNotice("Preliminary Designation");
      claimPage.triggerNotice("Leave Request Declined");
      claimPage.triggerNotice("Denial Notice Explanation of Wages");

      claimPage.documents((docPage) => {
        docPage.assertDocumentExists(DOCUMENT_TYPE);
        docPage.assertDocumentExists("Denial Notice Explanation of Wages");
      });
      claimPage.tasks((tasks) => {
        // For clicking 2nd layer "Tasks" tab
        cy.contains("td[id*='CaseTasksTabWidget']", "Tasks").click();
        tasks.assertDoesNotExist("Print and Mail Correspondence");
      });
    });
  });

  it("Should generate a legal notice (Denial) that the claimant can view and download it", () => {
    cy.dependsOnPreviousPass([deny]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      portal.loginClaimant();
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.claimantAssertClaimStatus([
        {
          leave: "Serious Health Condition - Employee",
          status: "Denied",
        },
      ]);
      portal.checkNoticeForClaimant(CLAIMANT_DENIAL_NOTICE);
      portal.downloadLegalNotice(
        CLAIMANT_DENIAL_NOTICE,
        submission.fineos_absence_id
      );
    });
  });

  it("Should generate a legal notice (Denial) that the Leave Administrator can view and download it", () => {
    cy.dependsOnPreviousPass([deny]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        assertValidClaim(claim);
        const employeeFullName = `${claim.first_name} ${claim.last_name}`;
        portal.loginLeaveAdmin(claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        // The leave admin notice type is subtly different from the claimant
        portal.checkNoticeForLeaveAdmin(
          employeeFullName,
          EMPLOYER_DENIAL_NOTICE
        );
        portal.downloadLegalNotice(
          EMPLOYER_DENIAL_NOTICE,
          submission.fineos_absence_id
        );
      });
    });
  });

  it(
    "I should receive an 'application started' notification (employer)",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([submit]);
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        cy.unstash<Submission>("submission").then((submission) => {
          const employeeFullName = `${claim.first_name} ${claim.last_name}`;
          const subject = email.getNotificationSubject(
            "application started",
            submission.fineos_absence_id
          );
          email
            .getEmails(
              {
                address: "<EMAIL>",
                subjectWildcard: subject,
                timestamp_from: submission.timestamp_from,
                message: submission.fineos_absence_id,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Preliminary Designation",
                  recipient: "Leave Admin",
                },
              },
              75_000
            )
            .then(() => {
              cy.contains(
                `Employee ${employeeFullName} started an application for paid family and medical leave (PFML).`
              );

              cy.contains(submission.fineos_absence_id);
            });
        });
      });
    }
  );

  it(
    "Check Leave Administrator email for the Denial notification",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([deny]);
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          const employeeFullName = `${claim.first_name} ${claim.last_name}`;
          const subjectEmployer = email.getNotificationSubject(
            "denial (employer)",
            submission.application_id
          );
          // Check email for Employer/Leave Admin
          email
            .getEmails(
              {
                address: "<EMAIL>",
                subjectWildcard: subjectEmployer,
                message: submission.fineos_absence_id,
                timestamp_from: submission.timestamp_from,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Leave Request Declined",
                  recipient: "Leave Admin",
                },
              },
              // Adding an additional 30 seconds based on recent failures
              // retrieving this notification during E2E runs
              90_000
            )
            .then(() => {
              cy.contains(
                `A paid family or medical leave (PFML) application for Employee ${employeeFullName} has been Denied.`
              );

              cy.contains(submission.fineos_absence_id);
              cy.get(
                `a[href*="/employers/applications/status/?absence_id=${submission.fineos_absence_id}"]`
              );
            });
        });
      });
    }
  );

  it(
    "Check Claimant email for the Denial Notice (Explanation of wage)",
    { retries: 0 },
    function () {
      cy.dependsOnPreviousPass([deny]);
      cy.unstash<Submission>("submission").then((submission) => {
        email
          .getEmails({
            address: "<EMAIL>",
            subject: email.getNotificationSubject("denial (claimant)"),
            message: {
              pattern: `${submission.fineos_absence_id}.*has been denied`,
            },
            timestamp_from: submission.timestamp_from,
            debugInfo: {
              fineosClaimID: submission.fineos_absence_id,
              triggerType: "Denial Notice Explanation of Wages",
              recipient: "Claimant",
            },
          })
          .then(() => {
            cy.contains(submission.fineos_absence_id);
          });
      });
    }
  );
});
