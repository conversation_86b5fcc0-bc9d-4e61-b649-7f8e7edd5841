import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import { email, fineos, portal } from "../../../actions";
import { ClaimPage } from "../../../actions/fineos.pages";
import { unstashMultipleKeys } from "../../../util";

const DOCUMENT_TYPE = "Approval of Application Change";
const NOTICE_TYPE = "Approval of Application Change (PDF)";

describe("Change request approval (notifications/notices)", () => {
  after(() => {
    portal.deleteDownloadsFolder();
  });

  it("Submits a Serious Health Condition - Employee claim via the API", () => {
    portal.generateAndSubmitClaimToAPI({
      logSubmissionToNewRelic: true,
      scenario: "MED_LSDCR",
    });
  });

  const approval = it("CSR will approve initial claim", { retries: 0 }, () => {
    cy.dependsOnPreviousPass();
    fineos.before();

    unstashMultipleKeys<{ claim: DehydratedClaim; submission: Submission }>([
      "claim",
      "submission",
    ]).then(({ claim, submission }) => {
      ClaimPage.visitAndTriggerPreliminaryDesignation(
        submission.fineos_absence_id
      )
        .completeAdjudication(claim.documents, { acceptLeavePlan: true })
        .approve();
    });
  });

  const approveModification = it(
    "Put the claim in review and accept leave plan",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([approval]);
      fineos.before();

      cy.unstash<Submission>("submission").then((submission) => {
        ClaimPage.visit(submission.fineos_absence_id)
          .leaveDetails((leaveDetails) => {
            const adjudication = leaveDetails.inReview();
            if (config("HAS_FR25_1")) {
              adjudication.progressToFullyAdjudicated();
            } else {
              adjudication.acceptLeavePlan(true).clickOK();
            }
          })
          .approve();
      });
    }
  );

  const changeRequestApproved =
    it('Generates a "Change Request Approved" document', () => {
      cy.dependsOnPreviousPass([approveModification]);
      fineos.before();

      cy.unstash<Submission>("submission").then((submission) => {
        ClaimPage.visit(submission.fineos_absence_id)
          .triggerNotice("Review Approval Notice")
          .documents((docPage) => docPage.assertDocumentExists(DOCUMENT_TYPE));
      });
    });

  it("Check the Leave Admin Portal for the Change Request Approved notice and download it", () => {
    cy.dependsOnPreviousPass([changeRequestApproved]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        const employeeFullName = `${claim.first_name} ${claim.last_name}`;
        portal.loginLeaveAdmin(claim.employer_fein as string);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        portal.checkNoticeForLeaveAdmin(employeeFullName, NOTICE_TYPE);
        portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
      });
    });
  });

  it("Check the Claimant Portal for the legal notice (Change Request Approved) and download it", () => {
    cy.dependsOnPreviousPass([changeRequestApproved]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      portal.loginClaimant();
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.claimantAssertClaimStatus([
        {
          leave: "Serious Health Condition - Employee",
          status: "Approved",
        },
      ]);
      portal.checkNoticeForClaimant(NOTICE_TYPE);
      portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
    });
  });

  it(
    "Check the Claimant email for the Change Request Approved notification",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([changeRequestApproved]);
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          const subjectClaimant = email.getNotificationSubject(
            "appeal (claimant)",
            submission.fineos_absence_id,
            `${claim.first_name} ${claim.last_name}`
          );

          const pattern = config("HAS_NOTICE_ENHANCEMENTS")
            ? `A request to change your leave.*${submission.fineos_absence_id}.*has been approved.`
            : `${submission.fineos_absence_id}.*Your change request has been approved`;
          email.getEmails(
            {
              address: "<EMAIL>",
              subject: subjectClaimant,
              message: {
                pattern,
              },
              timestamp_from: submission.timestamp_from,
              debugInfo: {
                fineosClaimID: submission.fineos_absence_id,
                triggerType: "Review Approval Notice",
                recipient: "Claimant",
              },
            },
            90_000
          );
        });
      });
    }
  );

  it(
    "Check the Leave Admin email for the Change Request Approved notification",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([changeRequestApproved]);
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          const subjectEmployer = email.getNotificationSubject(
            "appeal (claimant)",
            submission.fineos_absence_id,
            `${claim.first_name} ${claim.last_name}`
          );
          const emailMessage = `Employee .*${claim.first_name} ${claim.last_name}.* has been notified of an approved change in their leave.`;
          email.getEmails(
            {
              address: "<EMAIL>",
              subject: subjectEmployer,
              message: {
                pattern: emailMessage,
              },
              timestamp_from: submission.timestamp_from,
              debugInfo: {
                fineosClaimID: submission.fineos_absence_id,
                triggerType: "Review Approval Notice",
                recipient: "Leave Admin",
              },
            },
            90_000
          );
        });
      });
    }
  );
});
