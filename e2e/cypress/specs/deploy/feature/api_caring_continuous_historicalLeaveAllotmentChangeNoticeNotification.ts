import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import { email, fineos, fineosPages, portal } from "../../../actions";

const NOTICE_TYPE = "Leave Allotment Change Notice (PDF)";

describe("Create a new caring leave claim in API submission and add Historical Absence case", () => {
  before(() => {
    portal.deleteDownloadsFolder();
  });
  it("Submits a Care for a Family Member claim via the API", () => {
    portal.generateAndSubmitClaimToAPI({
      logSubmissionToNewRelic: true,
      scenario: "HIST_CASE",
    });
  });

  const historical = it(
    "Create and approve historical absence case within claim & trigger designation notice",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass();
      fineos.before();
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<Submission>("submission").then((response) => {
          const claimPage = fineosPages.ClaimPage.visit(
            response.fineos_absence_id
          );
          claimPage.addHistoricalAbsenceCase();
          fineosPages.ClaimPage.visit(response.fineos_absence_id)
            .adjudicate((adjudication) => {
              adjudication
                .evidence((evidence) => {
                  claim.documents.forEach((doc) =>
                    evidence.receive(doc.document_type)
                  );
                })
                .certificationPeriods((cert) => cert.prefill())
                .acceptLeavePlan(true);
            })
            .approve()
            .triggerNotice("Designation Notice");
        });
      });
    }
  );

  const leaveAllotment = it(
    "Adding the Leave Allotment Change Notice and trigger the notice",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([historical]);
      fineos.before();
      cy.unstash<Submission>("submission").then((submission) => {
        const claimPage = fineosPages.ClaimPage.visit(
          submission.fineos_absence_id
        );
        claimPage.addCorrespondenceDocument("Leave Allotment Change Notice");
        claimPage.documents((docs) => {
          docs.assertDocumentExists("Leave Allotment Change Notice");
          docs.adjustDocumentStatus(
            "Leave Allotment Change Notice",
            "Completed"
          );
          docs.checkDocumentFileExtension(
            "Leave Allotment Change Notice",
            /\.pdf$/
          );
        });
        claimPage.triggerNotice("Leave Allotment Change Notice");
      });
    }
  );

  it("Check the Claimant Portal for the legal notice (Leave Allotment Change)", () => {
    cy.dependsOnPreviousPass([historical, leaveAllotment]);
    portal.before();
    portal.loginClaimant();
    cy.unstash<Submission>("submission").then((submission) => {
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.checkNoticeForClaimant(NOTICE_TYPE);
      portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
    });
  });

  it("Check the Leave Admin Portal for the legal notice (Leave Allotment Change)", () => {
    cy.dependsOnPreviousPass([historical, leaveAllotment]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        if (!claim.employer_fein) {
          throw new Error("Claim must include employer FEIN");
        }
        const employeeFullName = `${claim.first_name} ${claim.last_name}`;
        portal.loginLeaveAdmin(claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        portal.checkNoticeForLeaveAdmin(employeeFullName, NOTICE_TYPE);
        portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
      });
    });
  });

  it(
    "Check the Claimant email for the Leave Allotment Change notification",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([historical, leaveAllotment]);
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          const subjectClaimant = email.getNotificationSubject(
            "appeal (claimant)",
            submission.fineos_absence_id,
            `${claim.first_name} ${claim.last_name}`
          );
          const pattern = config("HAS_NOTICE_ENHANCEMENTS")
            ? `Your leave time has been reduced for.*${submission.fineos_absence_id}`
            : `${submission.fineos_absence_id}.*Your maximum leave allotment has changed`;
          email.getEmails(
            {
              address: "<EMAIL>",
              subject: subjectClaimant,
              message: {
                pattern,
              },
              timestamp_from: submission.timestamp_from,
              debugInfo: {
                fineosClaimID: submission.fineos_absence_id,
                triggerType: "Leave Allotment Change Notice",
                recipient: "Claimant",
              },
            },
            75_000
          );
        });
      });
    }
  );

  it(
    "Check the Leave Admin email for the Leave Allotment Change notification",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([historical, leaveAllotment]);
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          const subjectEmployer = email.getNotificationSubject(
            "appeal (claimant)",
            submission.fineos_absence_id,
            `${claim.first_name} ${claim.last_name}`
          );
          const emailMessage = `Employee .*${claim.first_name} ${claim.last_name}.* has been notified of a change in their leave allotment.`;
          email
            .getEmails(
              {
                address: "<EMAIL>",
                subject: subjectEmployer,
                message: {
                  pattern: emailMessage,
                },
                timestamp_from: submission.timestamp_from,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Leave Allotment Change Notice",
                  recipient: "Leave Admin",
                },
              },
              90_000
            )
            .then(() => {
              cy.get(
                `a[href*="/employers/applications/status/?absence_id=${submission.fineos_absence_id}"]`
              );
            });
        });
      });
    }
  );
});
