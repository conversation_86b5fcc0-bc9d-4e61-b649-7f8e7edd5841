import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import { email, fineos, fineosPages, portal } from "../../../actions";

const NOTICE_TYPE = "Appeal Acknowledgment (PDF)";

describe("Appeal Hearing Notification & Notice Confirmation", () => {
  after(() => {
    portal.deleteDownloadsFolder();
  });
  it("Submits a Care for a Family Member claim via the API", () => {
    portal.generateAndSubmitClaimToAPI({
      logSubmissionToNewRelic: true,
      scenario: "CDENY2ER",
    });
  });

  const submit = it("CSR will begin adjudication & Approve the Claim", () => {
    cy.dependsOnPreviousPass();
    fineos.before();
    cy.unstash<DehydratedClaim>("claim").then((claim) => {
      cy.unstash<Submission>("submission").then((response) => {
        const claimPage = fineosPages.ClaimPage.visit(
          response.fineos_absence_id
        );
        claimPage.adjudicate((adjudicate) => {
          adjudicate.evidence((evidence) => {
            claim.documents.forEach((document) => {
              evidence.receive(document.document_type);
            });
          });
          adjudicate.certificationPeriods((cert) => cert.prefill());
          adjudicate.acceptLeavePlan(true);
        });
        claimPage.approve();
      });
    });
  });

  const csrAppeal =
    it("CSR will add an appeal, trigger the appeal notice and schedule a hearing", () => {
      cy.dependsOnPreviousPass();
      fineos.before();
      cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
        const claimPage = fineosPages.ClaimPage.visit(fineos_absence_id);
        if (Cypress.currentRetry === 0) {
          claimPage.addAppeal();
          claimPage.triggerNotice("SOM Generate Appeals Notice");
        }
        claimPage.appealDocuments((docPage) => {
          docPage.assertDocumentExists("Appeal Acknowledgment");
        });
        claimPage.appealTasks((tasks) => {
          tasks.closeAppealReview(Cypress.currentRetry);
          tasks.close("Schedule Hearing");
          tasks.close("Conduct Hearing");
          tasks.close("Make Decision");
          tasks.closeConductHearing(Cypress.currentRetry);
          tasks.assertTaskExists("Send Decision Notice");
        });
        claimPage.assertClaimStatus("Closed - Claim Decision Changed");
        claimPage.appealDocuments((docPage) => {
          docPage.addAppealDocument("Appeal Approved");
        });
        claimPage.triggerNotice("SOM Generate Editable Notice");
      });
    });

  it("Should generate an Appeal Acknowledgment that the Leave Admin can view and download it", () => {
    cy.dependsOnPreviousPass([submit, csrAppeal]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        if (!claim.employer_fein) {
          throw new Error("Claim must include employer FEIN");
        }

        const employeeFullName = `${claim.first_name} ${claim.last_name}`;
        portal.loginLeaveAdmin(claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);

        portal.checkNoticeForLeaveAdmin(employeeFullName, NOTICE_TYPE);
        portal.downloadLegalNoticeSubcase(
          submission.fineos_absence_id,
          NOTICE_TYPE
        );
      });
    });
  });

  it("Should generate an Appeal Acknowledgment that the Claimant can view and download it", () => {
    cy.dependsOnPreviousPass([submit, csrAppeal]);
    portal.before();
    portal.loginClaimant();
    cy.unstash<Submission>("submission").then((submission) => {
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.checkNoticeForClaimant(NOTICE_TYPE);
      portal.downloadLegalNoticeSubcase(
        submission.fineos_absence_id,
        NOTICE_TYPE
      );
    });
  });

  it(
    "Check Claimant email for the appeal notification delivery",
    { retries: 0 },
    () => {
      {
        cy.dependsOnPreviousPass([submit, csrAppeal]);
        cy.unstash<Submission>("submission").then((submission) => {
          cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
            const subjectClaimant = email.getNotificationSubject(
              "appeal (claimant)",
              submission.fineos_absence_id,
              `${claim.first_name} ${claim.last_name}`
            );
            const pattern = config("HAS_NOTICE_ENHANCEMENTS")
              ? `Your appeal has been received for.*${submission.fineos_absence_id}`
              : `${submission.fineos_absence_id}.*Your appeal has been received`;
            email.getEmails(
              {
                address: "<EMAIL>",
                subject: subjectClaimant,
                message: {
                  pattern,
                },
                timestamp_from: submission.timestamp_from,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Appeal Acknowledgment",
                  recipient: "Claimant",
                },
              },
              90_000
            );
          });
        });
      }
    }
  );

  it(
    "Check Claimant email for the appeal approval notification delivery",
    { retries: 0 },
    function () {
      cy.dependsOnPreviousPass([submit, csrAppeal]);
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          const subjectClaimant = email.getNotificationSubject(
            "appeal (claimant)",
            submission.fineos_absence_id,
            `${claim.first_name} ${claim.last_name}`
          );
          email.getEmails(
            {
              address: "<EMAIL>",
              subject: subjectClaimant,
              message: {
                pattern: `${submission.fineos_absence_id}.*A decision has been made on your appeal`,
              },
              timestamp_from: submission.timestamp_from,
              debugInfo: {
                fineosClaimID: submission.fineos_absence_id,
                triggerType: "Appeal Approved",
                recipient: "Claimant",
              },
            },
            90_000
          );
          cy.get(
            `a[href*="/applications/status/?absence_case_id=${submission.fineos_absence_id}#view-notices"]`
          );
        });
      });
    }
  );

  it(
    "Check Leave Admin (employer) email for the appeal notification delivery",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([submit, csrAppeal]);
      portal.before();
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          const subjectEmployer = email.getNotificationSubject(
            "appeal (employer)",
            submission.fineos_absence_id,
            `${claim.first_name} ${claim.last_name}`
          );
          const emailMessage = `Employee .*${claim.first_name} ${claim.last_name}.* has filed an appeal.*${submission.fineos_absence_id}`;
          email
            .getEmails(
              {
                address: "<EMAIL>",
                subject: subjectEmployer,
                message: {
                  pattern: emailMessage,
                },
                timestamp_from: submission.timestamp_from,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Appeal Acknowledgment",
                  recipient: "Leave Admin",
                },
              },
              90_000
            )
            .then(() => {
              cy.get(
                `a[href*="/employers/applications/status/?absence_id=${submission.fineos_absence_id}"]`
              );
            });
        });
      });
    }
  );
});
