import { isSaturday } from "date-fns";

import config from "../../../../src/config";
import { DehydratedClaim } from "../../../../src/generation/types";
import { Submission } from "../../../../src/types";
import {
  findCertificationDoc,
  getDocumentReviewTaskName,
} from "../../../../src/util/documents";
import { email, fineos, fineosPages, portal } from "../../../actions";
import { unstashMultipleKeys } from "../../../util";

const NOTICE_TYPE = "Approval notice (PDF)";

describe("Approval (notifications/notices)", () => {
  after(() => {
    portal.deleteDownloadsFolder();
  });

  it("Submits a reduced Child Bonding - Foster Care claim via the API", () => {
    portal.generateAndSubmitClaimToAPI({
      logSubmissionToNewRelic: true,
      scenario: "REDUCED_ER_MIDWEEK",
    });
  });

  const approve = it(
    "CSR rep will approve claim & check for Tasks",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass();
      fineos.before();
      // Submit a claim via the API, including Employer Response.
      unstashMultipleKeys<{ claim: DehydratedClaim; submission: Submission }>([
        "claim",
        "submission",
      ]).then(({ claim, submission }) => {
        const claimPage = fineosPages.ClaimPage.visit(
          submission.fineos_absence_id
        );
        claimPage.adjudicate((adjudication) => {
          adjudication.evidence((evidence) => {
            // Receive and approve all of the documentation for the claim.
            claim.documents.forEach((doc) =>
              evidence.receive(doc.document_type)
            );
          });
          adjudication.certificationPeriods((cert) => cert.prefill());
        });

        claimPage.tasks((tasks) => {
          const certificationDoc = findCertificationDoc(claim.documents);
          const certificationTask = getDocumentReviewTaskName(
            certificationDoc.document_type
          );
          // Documents task will automatically close when “received and satisfied” or “received and not satisfied”
          // so we will need to check under the "All tasks" instead of "Open tasks".
          // https://lwd.atlassian.net/browse/CPS-4243
          tasks.all();
          tasks.assertTaskExists(certificationTask);
          // The ID is automatically marked as received and satisfied when it passes the RMV check per CPS-10718.
          if (!claim.claim.mass_id) {
            tasks.assertTaskExists("ID Review");
          }
        });
        claimPage.shouldHaveStatus("Applicability", "Applicable");
        claimPage.shouldHaveStatus("Eligibility", "Met");
        claimPage.shouldHaveStatus("Evidence", "Satisfied");
        claimPage.shouldHaveStatus("Availability", "Time Available");
        claimPage.shouldHaveStatus("Restriction", "Passed");
        if (config("HAS_FR25_1")) {
          claimPage.adjudicate((adjudication) => {
            adjudication.acceptLeavePlan(true);
          });
        }
        claimPage.approve();
        claimPage
          .triggerNotice("Designation Notice")
          .triggerNotice("Approval Notice Explanation of Wages")
          .documents((docPage) => {
            docPage.assertDocumentExists("Approval Notice");
            docPage.assertDocumentExists(
              "Approval Notice Explanation of Wages"
            );
          });
      });
    }
  );

  it("Payments are scheduled for Sundays even though claim doesn't start on a Sunday", () => {
    cy.dependsOnPreviousPass();
    fineos.before();
    cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
      fineosPages.ClaimPage.visit(fineos_absence_id).paidLeave(
        (paidLeavePage) => {
          paidLeavePage.getAmountsPending().then((records) => {
            const processingDates = records.reduce<Date[]>((acc, record) => {
              acc.push(new Date(record.processingDate));
              return acc;
            }, []);
            expect(processingDates.every((val) => isSaturday(val))).equal(
              true,
              "Payments are scheduled for Saturday"
            );
          });
        }
      );
    });
  });

  it("Should generate a legal notice (Approval) that the claimant can view and download", () => {
    cy.dependsOnPreviousPass([approve]);
    portal.before();
    portal.loginClaimant();
    cy.unstash<Submission>("submission").then((submission) => {
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.claimantAssertClaimStatus([
        { leave: "Child Bonding", status: "Approved" },
      ]);
      // TODO after PFMLPB-15365 is done, change the notice type to "Approval Notice Explanation of Wages".
      portal.checkNoticeForClaimant(NOTICE_TYPE);
      portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
    });
  });

  it("Should generate a legal notice (Approval) that the Leave Administrator can view and download", () => {
    cy.dependsOnPreviousPass([approve]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        if (!claim.employer_fein) {
          throw new Error("Claim must include employer FEIN");
        }
        const employeeFullName = `${claim.first_name} ${claim.last_name}`;
        portal.loginLeaveAdmin(claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        portal.checkNoticeForLeaveAdmin(employeeFullName, NOTICE_TYPE);
        portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
      });
    });
  });

  it(
    "Check the Leave Admin email for the 'Action Required' ER notification.",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([approve]);
      cy.unstash<Submission>("submission").then((submission) => {
        email.getEmails({
          address: "<EMAIL>",
          subjectWildcard: `Action required: Respond to *'s paid leave application`,
          message: submission.fineos_absence_id,
          timestamp_from: submission.timestamp_from,
          debugInfo: {
            fineosClaimID: submission.fineos_absence_id,
            triggerType: "Employer Confirmation of Leave Data",
            recipient: "Leave Admin",
          },
        });
      });
    }
  );

  it(
    "Check the Leave Administrator email for the Approval notification.",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([approve]);
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          const subjectEmployer = email.getNotificationSubject(
            "approval (employer)",
            submission.fineos_absence_id
          );
          // Check email for Employer/Leave Admin
          email
            .getEmails(
              {
                address: "<EMAIL>",
                subjectWildcard: subjectEmployer,
                message: submission.fineos_absence_id,
                timestamp_from: submission.timestamp_from,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Designation Notice",
                  recipient: "Leave Admin",
                },
              },
              90_000
            )
            .then(() => {
              cy.contains(
                `A paid family or medical leave (PFML) application for Employee ${claim.first_name} ${claim.last_name} has been Approved.`
              );

              cy.contains(submission.fineos_absence_id);
              cy.get(
                `a[href*="/employers/applications/status/?absence_id=${submission.fineos_absence_id}"]`
              );
            });
        });
      });
    }
  );

  it(
    "Check the Claimant email for the Approval notification.",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([approve]);
      cy.unstash<Submission>("submission").then((submission) => {
        const subjectClaimant = email.getNotificationSubject(
          "approval (claimant)",
          submission.fineos_absence_id
        );
        const pattern = config("HAS_NOTICE_ENHANCEMENTS")
          ? `Your application for Paid Family and Medical Leave.*${submission.fineos_absence_id}.*, has been approved.`
          : submission.fineos_absence_id;
        // Check email for Claimant/Employee
        email.getEmails({
          address: "<EMAIL>",
          subject: subjectClaimant,
          message: { pattern },
          timestamp_from: submission.timestamp_from,
          debugInfo: {
            fineosClaimID: submission.fineos_absence_id,
            triggerType: "Approval Notice Explanation of Wages",
            recipient: "Claimant",
          },
        });
      });
    }
  );
});
