import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import { email, fineos, fineosPages, portal } from "../../../actions";

const NOTICE_TYPE = "Benefit Amount Change Notice (PDF)";
describe(
  "Create a Benefit Amount Change Notice in FINEOS and check delivery to the LA/Claimant portal",
  {},
  () => {
    after(() => {
      portal.deleteDownloadsFolder();
    });

    const submission =
      it("Submits a Child Bonding - Foster Care claim via the API", () => {
        portal.generateAndSubmitClaimToAPI({
          logSubmissionToNewRelic: true,
          scenario: "BHAP1ER",
        });
      });

    it("Verify that no further action is required", () => {
      cy.dependsOnPreviousPass([submission]);
      portal.before();
      cy.unstash<Submission>("submission").then((submission) => {
        portal.loginClaimant();
        portal.claimantGoToClaimStatus(submission.fineos_absence_id);
        cy.contains("No action required at this time").should("be.visible");
        cy.contains(
          "We will notify you if we require more information."
        ).should("be.visible");
      });
    });

    const approval =
      it("Approves the child bonding claim and trigger notice", () => {
        cy.dependsOnPreviousPass();
        fineos.before();
        cy.unstash<DehydratedClaim>("claim").then((claim) => {
          cy.unstash<Submission>("submission").then((submission) => {
            const claimPage = fineosPages.ClaimPage.visit(
              submission.fineos_absence_id
            );
            if (Cypress.currentRetry > 0) {
              fineos.getClaimStatus().then((status) => {
                if (status === "Approved") {
                  claimPage.triggerNotice("Designation Notice");
                }
                return;
              });
            }
            claimPage
              .adjudicate((adjudication) => {
                adjudication
                  .evidence((evidence) => {
                    for (const document of claim.documents) {
                      evidence.receive(document.document_type);
                    }
                  })
                  .certificationPeriods((certificationPeriods) =>
                    certificationPeriods.prefill()
                  )
                  .acceptLeavePlan(true);
              })
              .approve()
              .triggerNotice("Designation Notice");
          });
        });
      });

    const notification =
      it("Generates 'Benefit Amount Change Notice' document and triggers notification", () => {
        cy.dependsOnPreviousPass([submission, approval]);
        fineos.before();
        cy.unstash<Submission>("submission").then((submission) => {
          fineosPages.ClaimPage.visit(submission.fineos_absence_id).paidLeave(
            (paidLeavePage) => {
              paidLeavePage
                .createCorrespondenceDocument("Benefit Amount Change Notice")
                .documents((documentsPage) =>
                  documentsPage
                    .assertDocumentExists("Benefit Amount Change Notice")
                    .properties(
                      "Benefit Amount Change Notice",
                      (propertiesPage) => propertiesPage.setStatus("Completed")
                    )
                    .properties(
                      "Benefit Amount Change Notice",
                      (propertiesPage) =>
                        propertiesPage.fileNameShouldMatch(/\.pdf$/)
                    )
                )
                .triggerPaidLeaveNotice("Benefit Amount Change Notice");
            }
          );
        });
      });

    it("Check the Leave Admin Portal for the Benefit Amount Change Notice and download it", () => {
      cy.dependsOnPreviousPass([submission, approval, notification]);
      portal.before();
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then((claim) => {
          if (!claim.claim.employer_fein) {
            throw new Error("Claim must include employer FEIN");
          }
          const employeeFullName = `${claim.claim.first_name} ${claim.claim.last_name}`;
          portal.loginLeaveAdmin(claim.claim.employer_fein);
          portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
          portal.checkNoticeForLeaveAdmin(employeeFullName, NOTICE_TYPE);
          portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
        });
      });
    });

    it("Check the Claimant Portal for the Benefit Amount Change Notice and download it", () => {
      cy.dependsOnPreviousPass([submission, approval, notification]);
      portal.before();
      cy.unstash<Submission>("submission").then((submission) => {
        portal.loginClaimant();
        portal.claimantGoToClaimStatus(submission.fineos_absence_id);
        portal.claimantAssertClaimStatus([
          {
            leave: "Child Bonding",
            status: "Approved",
          },
        ]);
        portal.checkNoticeForClaimant(NOTICE_TYPE);
        portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
      });
    });

    it("Claimant receives notification for Benefit Amount Change", () => {
      cy.dependsOnPreviousPass([submission, approval, notification]);
      cy.unstash<Submission>("submission").then((submission) => {
        const pattern = config("HAS_NOTICE_ENHANCEMENTS")
          ? `Your benefit amount has been reduced for.*${submission.fineos_absence_id}`
          : `${submission.fineos_absence_id}.*Your benefit amount has changed.`;
        email.getEmails(
          {
            address: "<EMAIL>",
            // this email uses the same subject as claimant appeals
            subject: email.getNotificationSubject("appeal (claimant)"),
            message: { pattern },
            timestamp_from: submission.timestamp_from,
            debugInfo: {
              fineosClaimID: submission.fineos_absence_id,
              triggerType: "Benefit Amount Change Notice",
              recipient: "Claimant",
            },
          },
          90_000
        );
      });
    });

    it("Leave Admin receives notification for Benefit Amount Change", () => {
      cy.dependsOnPreviousPass([submission, approval, notification]);
      cy.unstash<Submission>("submission").then((submission) => {
        const portalPath = `/employers/applications/status/?absence_id=${submission.fineos_absence_id}`;
        email.getEmails(
          {
            address: "<EMAIL>",
            // this email uses the same subject as claimaint appeals
            subject: email.getNotificationSubject("appeal (claimant)"),
            message: portalPath,
            timestamp_from: submission.timestamp_from,
            debugInfo: {
              fineosClaimID: submission.fineos_absence_id,
              triggerType: "Benefit Amount Change Notice",
              recipient: "Leave Admin",
            },
          },
          90_000
        );
        cy.unstash<DehydratedClaim>("claim").then((claim) => {
          const employeeFullName = `${claim.claim.first_name} ${claim.claim.last_name}`;
          cy.contains(
            `Employee ${employeeFullName} has been notified of a change in their benefit amount.`
          );
          cy.get(`a[href*='${portalPath}']`);
        });
      });
    });
  }
);
