import config from "../../../../src/config";
import { ScenarioSpecification } from "../../../../src/generation/Scenario";
import { CCAP90, MHAP4, WDCLAIM } from "../../../../src/scenarios";
import { FineosDocumentType, Submission } from "../../../../src/types";
import { claim, fineos, fineosPages, portal } from "../../../actions";
import { reduceToStepsOneTwoOfPartOne } from "../../../util";

describe("Optional certification document upload", () => {
  before(() => {
    portal.deleteDownloadsFolder();
  });
  interface testCase {
    scenarioType: string;
    scenario: ScenarioSpecification;
    scenarioLabel: Scenarios;
    certificationType: FineosDocumentType;
    bannerText: string;
  }

  const testCases: testCase[] = [
    {
      scenarioType: "Medical",
      scenario: WDCLAIM,
      scenarioLabel: "WDCLAIM",
      certificationType: "Own serious health condition form",
      bannerText: "Your Serious Health Condition",
    },
    {
      scenarioType: "Caring",
      scenario: CCAP90,
      scenarioLabel: "CCAP90",
      certificationType: "Care for a family member form",
      bannerText: "Your Family Member’s Serious Health Condition",
    },
    {
      scenarioType: "Pregnancy",
      scenario: MHAP4,
      scenarioLabel: "MHAP4",
      certificationType: config("HAS_FR25_1")
        ? "Pregnancy and Maternity form"
        : "Pregnancy/Maternity form",
      bannerText: "Your Serious Health Condition",
    },
  ];

  testCases.forEach((testCase) => {
    describe(`Submits a ${testCase.scenarioType} claim using the 'Upload Certification Document Later' flow and uploads the cert document via FINEOS`, () => {
      //To cover the scenario where a claimant submits a claim and mails the physical certification document to PFML, where a PFML employee would upload the doc themselves.
      it(`Submits a ${testCase.scenarioType} claim using the 'Upload Certification Document Later' user flow, setting up for FINEOS certification doc submission`, () => {
        portal.before();
        claim.generateClaim(testCase.scenarioLabel).then((claim) => {
          const partsOne = reduceToStepsOneTwoOfPartOne(claim.claim);
          cy.task<string | undefined>("submitStepsOneTwoOfPartOne", {
            application: partsOne,
          }).then((applicationId) => {
            if (!applicationId) {
              throw Error("Application did not create successfully");
            }
            const application: ApplicationRequestBody = claim.claim;
            const paymentPreference = claim.paymentPreference;
            portal.skipLoadingClaimantApplications();
            portal.loginClaimant();
            portal.wrapUpPartOne(applicationId, application);
            portal.waitForClaimSubmission({ logSubmissionToNewRelic: true });
            portal.submitClaimPartsTwoThree(application, paymentPreference, {
              is_withholding_tax: claim.is_withholding_tax,
              alternateForm: "Employer FMLA",
              withCertificationDocument: false,
            });
          });
        });
      });

      it(`Uploads a ${testCase.scenarioType} certification document via FINEOS to the previously submitted claim`, () => {
        fineos.before();
        cy.unstash<Submission>("submission").then((submission) => {
          const claimPage = fineosPages.ClaimPage.visit(
            submission.fineos_absence_id
          ).documents((docs) => {
            //asserts that document still required banner exists.
            docs.assertDocDoesntExist(testCase.certificationType);
            //TODO: remove this once FR25_1 is enabled in all envs
            if (testCase.certificationType === "Pregnancy/Maternity form") {
              docs.uploadDocument("Prgenancy" as FineosDocumentType);
            } else {
              docs.uploadDocument(testCase.certificationType);
            }
            cy.contains("Absence Hub").click();
          });
          claimPage.adjudicate((adjudicatePage) => {
            adjudicatePage.evidence((evidencePage) => {
              evidencePage.changeEvidenceStatus({
                evidenceType: testCase.certificationType,
                receipt: "Received",
                decision: "Satisfied",
                reason: "just because!",
              });
            });
          });
        });
      });

      it(`Verifies ${testCase.scenarioType} certification document was uploaded via FINEOS by checking the status displayed in the portal`, () => {
        portal.before();
        cy.unstash<Submission>("submission").then((submission) => {
          portal.loginClaimant();
          portal.claimantGoToClaimStatus(submission.fineos_absence_id, {
            visitLink: true,
          });
          cy.contains("Completed: Documents submitted").click();
          cy.contains(
            `Certification of ${testCase.bannerText} form submitted on `
          ).should("be.visible");
        });
      });
    });

    describe(`Submits a ${testCase.scenarioType} claim using the 'Upload Certification Document Later' flow and uploads the cert document via portal`, () => {
      it(`Submits a ${testCase.scenarioType} claim using the 'Upload Certification Document Later' user flow, setting up for portal certification doc submission`, () => {
        portal.before();
        claim.generateClaim(testCase.scenarioLabel).then((claim) => {
          const partsOne = reduceToStepsOneTwoOfPartOne(claim.claim);
          cy.task<string | undefined>("submitStepsOneTwoOfPartOne", {
            application: partsOne,
          }).then((applicationId) => {
            if (!applicationId) {
              throw Error("Application did not create successfully");
            }
            const application: ApplicationRequestBody = claim.claim;
            const paymentPreference = claim.paymentPreference;
            portal.skipLoadingClaimantApplications();
            portal.loginClaimant();
            portal.wrapUpPartOne(applicationId, application);
            portal.waitForClaimSubmission({ logSubmissionToNewRelic: true });
            portal.submitClaimPartsTwoThree(application, paymentPreference, {
              is_withholding_tax: claim.is_withholding_tax,
              alternateForm: "Employer FMLA",
              withCertificationDocument: false,
            });
            portal.uploadCertificationDocAsClaimant(
              testCase.scenario.claim.reason,
              testCase.bannerText
            );
          });
        });
      });
      it(`Verifies ${testCase.scenarioType} certification document was uploaded via portal by checking the status displayed in the portal`, () => {
        portal.before();
        cy.unstash<Submission>("submission").then((submission) => {
          portal.loginClaimant();
          portal.claimantGoToClaimStatus(submission.fineos_absence_id, {
            visitLink: true,
          });
          cy.contains("Completed: Documents submitted").click();
          cy.contains(
            `Certification of ${testCase.bannerText} form submitted on `
          ).should("be.visible");
        });
      });
    });
  });
});
