import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import { email, fineos, portal } from "../../../actions";
import { ClaimPage } from "../../../actions/fineos.pages";
import { unstashMultipleKeys } from "../../../util";

const APPROVAL_NOTICE_TYPE = "Approval notice (PDF)";
const DENIED_DOCUMENT_TYPE = "Denial of Application Change";
const DENIED_NOTICE_TYPE = "Denial of Application Change (PDF)";
describe("Post-approval (notifications/notices)", () => {
  after(() => {
    portal.deleteDownloadsFolder();
  });

  it("Submits a Care for a Family Member claim via the API", () => {
    portal.generateAndSubmitClaimToAPI({
      logSubmissionToNewRelic: true,
      scenario: "CARE_ER_APPROVE",
    });
  });

  const approval = it("CSR will begin adjudication & Approve the Claim", () => {
    cy.dependsOnPreviousPass();
    fineos.before();

    unstashMultipleKeys<{ claim: DehydratedClaim; submission: Submission }>([
      "claim",
      "submission",
    ]).then(({ claim, submission }) => {
      ClaimPage.visit(submission.fineos_absence_id)
        .completeAdjudication(claim.documents, { acceptLeavePlan: true })
        .approve()
        .triggerNotice("Designation Notice");
    });
  });

  const denyModification = it(
    'Generates a "Change Request Denial" document when deny an approved claim in review',
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([approval]);
      fineos.before();
      cy.unstash<Submission>("submission").then((submission) => {
        // visit claim after approval to put in review and deny.
        ClaimPage.visit(submission.fineos_absence_id)
          .leaveDetails((leaveDetails) => {
            const adjudication = leaveDetails.inReview();
            if (config("HAS_FR25_1")) {
              adjudication.rejectLeavePlan();
            } else {
              adjudication.rejectLeavePlan().clickOK();
            }
          })
          .deny("Claimant/Family member deceased", false, false);
      });
    }
  );

  // Triggering review denial notice in previous test case fails intermittently.
  // It was extracted here as a separate case for stability.
  const denialNotice = it("CSR will trigger denial notice", () => {
    cy.dependsOnPreviousPass([denyModification]);
    fineos.before();
    cy.unstash<Submission>("submission").then((response) => {
      const claimPage = ClaimPage.visit(response.fineos_absence_id);
      claimPage
        .triggerNotice("Review Denial Notice")
        .documents((docPage) =>
          docPage.assertDocumentExists(DENIED_DOCUMENT_TYPE)
        );
    });
  });

  it("Check the Claimant Portal status and for the legal notice Change Request Denied or Denial Notice and download it", () => {
    cy.dependsOnPreviousPass([denialNotice]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      portal.loginClaimant();
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.claimantAssertClaimStatus([
        {
          leave: "Care for a Family Member",
          status: "Denied",
        },
      ]);
      portal.checkNoticeForClaimant(APPROVAL_NOTICE_TYPE);
      portal.downloadLegalNotice(
        APPROVAL_NOTICE_TYPE,
        submission.fineos_absence_id
      );
    });
  });

  it("Check the Leave Admin Portal for the Change Request Denied notice and download it", () => {
    cy.dependsOnPreviousPass([denialNotice]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        if (!claim.claim.employer_fein) {
          throw new Error("Claim must include employer FEIN");
        }
        const employeeFullName = `${claim.claim.first_name} ${claim.claim.last_name}`;
        portal.loginLeaveAdmin(claim.claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        portal.checkNoticeForLeaveAdmin(employeeFullName, DENIED_NOTICE_TYPE);
        portal.downloadLegalNotice(
          DENIED_NOTICE_TYPE,
          submission.fineos_absence_id
        );
      });
    });
  });

  it(
    "Check the Claimant email for the Change Request Denial notification.",
    { retries: 0 },
    () => {
      {
        cy.dependsOnPreviousPass([denialNotice]);
        cy.unstash<Submission>("submission").then((submission) => {
          cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
            // The notification is using the same subject line as Appeals claimant.
            const subjectClaimant = email.getNotificationSubject(
              "appeal (claimant)",
              submission.fineos_absence_id,
              `${claim.first_name} ${claim.last_name}`
            );

            const pattern = config("HAS_NOTICE_ENHANCEMENTS")
              ? `A request to change your leave.*${submission.fineos_absence_id}.*has been denied`
              : `${submission.fineos_absence_id}.*Your change request has been denied`;
            email
              .getEmails(
                {
                  address: "<EMAIL>",
                  subject: subjectClaimant,
                  message: {
                    pattern,
                  },
                  timestamp_from: submission.timestamp_from,
                  debugInfo: {
                    fineosClaimID: submission.fineos_absence_id,
                    triggerType: "Review Denial Notice",
                    recipient: "Claimant",
                  },
                },
                90_000
              )
              .then(() => {
                cy.screenshot("Claimant email");
                cy.contains(submission.fineos_absence_id);
              });
          });
        });
      }
    }
  );
  it(
    "Check the Leave Admin email for the Change Request Denial notification.",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([denialNotice]);
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          // The notification is using the same subject line as Appeals claimant.
          const subjectEmployer = email.getNotificationSubject(
            "appeal (claimant)",
            submission.fineos_absence_id,
            `${claim.first_name} ${claim.last_name}`
          );
          const emailMessage = `Employee .*${claim.first_name} ${claim.last_name}.* has been notified of a denied change in their leave.`;
          email
            .getEmails(
              {
                address: "<EMAIL>",
                subject: subjectEmployer,
                // Had to adjust the message to use line for Leave Admin only.
                // Was getting a duplicate Claimant emails or not found because of to many notifications.
                message: {
                  pattern: emailMessage,
                },
                timestamp_from: submission.timestamp_from,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Review Denial Notice",
                  recipient: "Leave Admin",
                },
              },
              90_000
            )
            .then(() => {
              cy.screenshot("Leave Admin email");
              cy.contains(submission.fineos_absence_id);
              cy.get(
                `a[href*="/employers/applications/status/?absence_id=${submission.fineos_absence_id}"]`
              );
            });
        });
      });
    }
  );
});
