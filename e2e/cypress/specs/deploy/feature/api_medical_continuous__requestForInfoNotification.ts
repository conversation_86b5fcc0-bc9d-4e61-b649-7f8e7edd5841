import { addDays, format } from "date-fns";

import config from "../../../../src/config";
import { DehydratedClaim } from "../../../../src/generation/types";
import { Submission } from "../../../../src/types";
import { extractLeavePeriod } from "../../../../src/util/claims";
import { findCertificationDoc } from "../../../../src/util/documents";
import { email, fineos, fineosPages, portal } from "../../../actions";
import { getNextWorkDay } from "../../../util";

const NOTICE_TYPE = "Request for More Information (PDF)";

describe("Request for More Information (notifications/notices)", () => {
  before(() => {
    portal.deleteDownloadsFolder();
  });
  const submission =
    it("Submits a Care for a Family Member claim via the API", () => {
      portal.generateAndSubmitClaimToAPI({
        logSubmissionToNewRelic: true,
        scenario: "MED_RFI",
      });
    });

  const modification =
    it("CSR rep can modify leave dates pre-approval and trigger notice on Preliminary Designation", () => {
      cy.dependsOnPreviousPass([submission]);
      fineos.before();
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<Submission>("submission").then((submission) => {
          const [startDate] = extractLeavePeriod(claim.claim);
          const newStartDate = getNextWorkDay(claim.claim, startDate);
          const newEndDate = addDays(newStartDate, 1);
          const newStartDateString = format(newStartDate, "MM/dd/yyyy");
          const newEndDateString = format(newEndDate, "MM/dd/yyyy");

          cy.stash("modifiedLeaveDates", [
            newStartDateString,
            newEndDateString,
          ]);

          const claimPage = fineosPages.ClaimPage.visit(
            submission.fineos_absence_id
          );

          // The Preliminary Designation is triggered to resolve a race
          // condition where the one-minute time trigger on this notice can
          // result in an "Out of Date Data" error when attempting to edit the
          // leave period dates.
          claimPage.triggerNotice("Preliminary Designation");

          fineos.onTab("Absence Hub");

          claimPage.adjudicate((adjudication) => {
            adjudication.requestInformation((requestInformation) => {
              requestInformation.editRequestDates(
                newStartDateString,
                newEndDateString
              );
            });
          });
        });
      });
    });

  const requestForInformation = it(
    "CSR rep can request for additional information and trigger notice",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([modification]);
      fineos.before();
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<Submission>("submission").then((submission) => {
          fineosPages.ClaimPage.visit(submission.fineos_absence_id)
            .adjudicate((adjudicate) => {
              adjudicate
                .evidence((evidence) => {
                  const certificationDocument = findCertificationDoc(
                    claim.documents
                  );

                  evidence.requestAdditionalInformation(
                    certificationDocument.document_type,
                    {
                      "Medical Leave": "Content is illegible",
                    }
                  );
                })
                .certificationPeriods((certificationPeriods) =>
                  certificationPeriods.prefill()
                );
            })
            .tasks((task) => {
              // Documents task will automatically close when “received and satisfied” or “received and not satisfied”
              // so we will need to check under the "All tasks" instead of "Open tasks".
              task.assertTaskStatus("Caring Certification Review", "Open");
              task.assertTaskExists("Escalate Employer Reported Fraud");
              task.assertTaskStatus("Escalate Employer Reported Fraud", "Open");
            })
            .documents((document) => {
              document.assertDocumentPresent(
                "Own serious health condition form",
                1
              );
            })
            .shouldHaveStatus("PlanDecision", "Undecided")
            .triggerNotice("SOM Generate Legal Notice")
            .documents((docPage) => {
              docPage.assertDocumentExists("Request for more Information");
              docPage.assertDocumentExists(
                "Additional Evidence Information eForm"
              );
            });
        });
      });
    }
  );

  const upload =
    it("Should allow claimant to upload additional documents and generate a legal notice (Request for Information) that the claimant can view", () => {
      cy.dependsOnPreviousPass([modification, requestForInformation]);
      portal.before();
      cy.unstash<Submission>("submission").then((submission) => {
        portal.loginClaimant();
        cy.unstash<[string, string]>("modifiedLeaveDates").then(
          (leavePeriods) => {
            portal.claimantGoToClaimStatus(submission.fineos_absence_id);
            portal.claimantAssertClaimStatus([
              {
                leave: "Serious Health Condition - Employee",
                status: "Pending",
                leavePeriods,
              },
            ]);
            cy.contains("Leave to manage your own serious health condition");
            cy.contains("We need more information from you");
            portal.claimantAssertApplicationTimeline([
              {
                name: "Request for More Information",
                status: "Action required",
              },
              { name: "Employer response", status: "Completed" },
              { name: "DFML review", status: "In progress" },
            ]);
            portal.checkNoticeForClaimant(NOTICE_TYPE);
            portal.downloadLegalNotice(
              NOTICE_TYPE,
              submission.fineos_absence_id
            );
          }
        );
        portal.uploadAdditionalCertificationDocument("caring");
        cy.contains("You've successfully submitted your certification form");
      });
    });

  it("CSR rep can view the additional information uploaded by claimant", () => {
    cy.dependsOnPreviousPass([upload]);
    fineos.before();
    cy.unstash<Submission>("submission").then((submission) => {
      const page = fineosPages.ClaimPage.visit(submission.fineos_absence_id);
      page.tasks((taskPage) =>
        taskPage.assertTaskExists("Medical Certification Review")
      );
      page.documents((documentsPage) => {
        documentsPage.assertDocumentPresent(
          "Own serious health condition form",
          2
        );
      });
    });
  });

  it(
    "Claimant should receive a 'Thank you for successfully submitting your ... application' notification (employee)",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([submission]);
      cy.unstash<Submission>("submission").then((submission) => {
        email.getEmails({
          address: "<EMAIL>",
          subject:
            "Thank you for successfully submitting your Paid Family and Medical Leave Application",
          timestamp_from: submission.timestamp_from,
          message: submission.fineos_absence_id,
          debugInfo: {
            fineosClaimID: submission.fineos_absence_id,
            triggerType: "Employer Confirmation of Leave Data",
            recipient: "Claimant",
          },
        });
        cy.contains(submission.fineos_absence_id);
      });
    }
  );

  it(
    "Check the Claimant email for the Request for Information notification.",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([requestForInformation]);
      cy.unstash<Submission>("submission").then((submission) => {
        const subjectClaimant = email.getNotificationSubject(
          "request for additional info",
          submission.fineos_absence_id
        );
        const pattern = config("HAS_NOTICE_ENHANCEMENTS")
          ? `We need additional documentation from you.*${submission.fineos_absence_id}`
          : submission.fineos_absence_id;
        email.getEmails(
          {
            address: "<EMAIL>",
            subjectWildcard: subjectClaimant,
            message: { pattern },
            timestamp_from: submission.timestamp_from,
            debugInfo: {
              fineosClaimID: submission.fineos_absence_id,
              triggerType: "Additional Information Request",
              recipient: "Claimant",
            },
          },
          90_000
        );
      });
    }
  );
});
