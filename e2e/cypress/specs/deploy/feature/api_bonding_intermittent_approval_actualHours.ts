import {
  addDays,
  addMinutes,
  formatISO,
  isMonday,
  isSunday,
  isTuesday,
  startOfWeek,
  subDays,
  subMinutes,
} from "date-fns";
import { convertToTimeZone } from "date-fns-timezone";

import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import {
  emailAddresses,
  getClaimantCredentials,
} from "../../../../src/util/credentials";
import { email, fineos, fineosPages, portal } from "../../../actions";
import { waitForAjaxComplete } from "../../../actions/fineos";
import { ClaimPage } from "../../../actions/fineos.pages";
import { IntermittentLeavePeriodSpec } from "../../../actions/portal";

const INTERMITTENT_APPROVAL_DOCUMENT = "Intermittent Time Reported";

const IS_TRAINING = ["training", "trn2"].includes(config("ENVIRONMENT"));

const emailAddress = emailAddresses.actualsNotice;
const credentials = getClaimantCredentials(emailAddress);

describe("Report of intermittent leave hours notification in FINEOS", () => {
  after(() => {
    portal.deleteDownloadsFolder();
  });

  describe("Claim Submission", () => {
    before(() => {
      portal.generateAndSubmitClaimToAPI({
        credentials,
        logSubmissionToNewRelic: true,
        scenario: "BIAP60ER",
      });
    });

    beforeEach(() => {
      fineos.before();
    });

    const approval = it(
      "CSR will begin adjudication & Approve the Claim",
      { retries: 0 },
      () => {
        cy.unstash<DehydratedClaim>("claim").then((claim) => {
          cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
            const claimPage = fineosPages.ClaimPage.visit(fineos_absence_id);
            claimPage.shouldHaveStatus("Eligibility", "Met");
            claimPage.adjudicate((adjudication) => {
              adjudication
                .evidence((evidence) => {
                  claim.documents.forEach(({ document_type }) =>
                    evidence.receive(document_type)
                  );
                })
                .certificationPeriods((certPeriods) => certPeriods.prefill())
                .acceptLeavePlan(true);
            });
            const sunday = isSunday(
              convertToTimeZone(new Date(), {
                timeZone: "America/New_York",
              })
            );
            claimPage.approve(sunday ? "Approved" : "Completed");
          });
        });
      }
    );

    const notice = it(
      "CSR will manually Trigger the Designation Notice",
      { retries: 0 },
      () => {
        cy.dependsOnPreviousPass([approval]);
        cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
          const claimPage = fineosPages.ClaimPage.visit(fineos_absence_id);
          claimPage.triggerNotice("Designation Notice");
          waitForAjaxComplete();
        });
      }
    );

    it(
      "CSR Representative can record actual leave hours in calendar",
      { retries: 0 },
      () => {
        cy.dependsOnPreviousPass([notice]);
        cy.unstash<DehydratedClaim>("claim").then((claim) => {
          cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
            if (!claim.metadata || !claim.metadata.fineosIntermittentLeave) {
              throw Error("Intermittent leave hours are undefined.");
            }
            const fineosIntermittentLeave =
              claim.metadata.fineosIntermittentLeave;
            // Those are the specific dates fit to the scenario spec.
            // We need those so that fineos approves the actual leave time and generates payments
            const [startOfEpisode, endOfEpisode] =
              calculateIntermittentLeaveEpisode();
            const actualLeaveStart = formatISO(startOfEpisode);
            const actualLeaveEnd = formatISO(endOfEpisode);

            const claimPage = fineosPages.ClaimPage.visit(fineos_absence_id);
            claimPage.recordActualLeaveCalendar((recordActualTimeCalendar) => {
              recordActualTimeCalendar.fillTimePeriod({
                startDate: actualLeaveStart,
                endDate: actualLeaveEnd,
                timeSpanHoursStart: fineosIntermittentLeave.spanHoursStart,
                timeSpanMinutesStart: fineosIntermittentLeave.spanMinutesStart,
                timeSpanHoursEnd: fineosIntermittentLeave.spanHoursEnd,
                timeSpanMinutesEnd: fineosIntermittentLeave.spanMinutesEnd,
              });
            });
          });
        });
      }
    );
  });

  describe("Existing Claim", () => {
    before(function () {
      if (IS_TRAINING) {
        this.skip();
      }
      findRecentClaim();
    });

    it(
      "Should display an appropriate number of notices in FINEOS",
      { retries: 0 },
      () => {
        fineos.before();
        cy.unstash<PriorIntermittentClaimData>("previousClaim").then(
          ({ fineosAbsenceId }) => {
            const claimPage = ClaimPage.visit(fineosAbsenceId);
            claimPage.documents((docs) => {
              // All the reported actuals should be amalgamated into a single document.
              docs.assertDocumentPresent(INTERMITTENT_APPROVAL_DOCUMENT, 1);
            });
          }
        );
      }
    );

    it(
      "Employer should receive amalgamated '{Employee Name} reported their intermittent leave hours' notification",
      { retries: 0 },
      () => {
        cy.unstash<PriorIntermittentClaimData>("previousClaim").then(
          (previousClaim) => {
            const employerNotificationSubject = email.getNotificationSubject(
              "review leave hours",
              previousClaim.fineosAbsenceId
            );

            const notificationTimestamp = new Date(
              previousClaim.notificationTime
            );
            // Check email for Employer/Leave Admin
            email.getEmails(
              {
                address: "<EMAIL>",
                subjectWildcard: employerNotificationSubject,
                message: previousClaim.fineosAbsenceId,
                timestamp_from: subMinutes(notificationTimestamp, 15).getTime(),
                timestamp_to: addMinutes(notificationTimestamp, 15).getTime(),
                debugInfo: {
                  fineosClaimID: previousClaim.fineosAbsenceId,
                },
              },
              90_000
            );
          }
        );
      }
    );

    it(
      "Leave Admin should see the allotment for the intermittent leave hours",
      { retries: 0 },
      () => {
        cy.unstash<PriorIntermittentClaimData>("previousClaim").then(
          (previousClaim) => {
            const {
              employerFein,
              frequency_interval,
              frequency_interval_basis,
              duration,
              duration_basis,
              leave_type,
              fineosAbsenceId,
            } = previousClaim;

            if (!employerFein) {
              throw new Error("Claim must include employer FEIN");
            }

            portal.before();

            portal.loginLeaveAdmin(employerFein);
            portal.goToLeaveAllotmentForClaimantByAbsenceId(fineosAbsenceId);

            const leavePeriod: IntermittentLeavePeriodSpec = {
              frequency_interval,
              frequency_interval_basis,
              duration,
              duration_basis,
              leave_type,
            };

            portal.verifyIntermittentLeaveAllotmentForClaimant(leavePeriod);
          }
        );
      }
    );

    it(
      "Employee should receive amalgamated Intermittent hours recorded notification for previous day",
      { retries: 0 },
      () => {
        cy.unstash<PriorIntermittentClaimData>("previousClaim").then(
          (previousClaim) => {
            const employeeNotificationSubject = email.getNotificationSubject(
              "employee record intermittent hours",
              previousClaim.fineosAbsenceId
            );
            const notificationTimestamp = new Date(
              previousClaim.notificationTime
            );
            const pattern = config("HAS_NOTICE_ENHANCEMENTS")
              ? `Your intermittent leave hours have been reviewed for.*${previousClaim.fineosAbsenceId}`
              : `${previousClaim.fineosAbsenceId}.*Your intermittent leave hours have been reviewed.`;

            email.getEmails(
              {
                address: "<EMAIL>",
                subjectWildcard: employeeNotificationSubject,
                message: { pattern },
                timestamp_from: subMinutes(notificationTimestamp, 15).getTime(),
                timestamp_to: addMinutes(notificationTimestamp, 15).getTime(),
                debugInfo: {
                  fineosClaimID: previousClaim.fineosAbsenceId,
                  triggerType: "Intermittent Time Request Decision",
                },
              },
              90_000
            );
          }
        );
      }
    );
  });
});

function calculateIntermittentLeaveEpisode() {
  const mostRecentSunday = startOfWeek(new Date());
  const startOfEpisode = subDays(mostRecentSunday, 20);
  const endOfEpisode = addDays(startOfEpisode, 4);
  return [startOfEpisode, endOfEpisode];
}

function findRecentClaim() {
  const today = new Date();
  // The document won't actually be generated until 6.5 business hours (i.e. M-F 9-5) have elapsed.
  // This means that a claim created late in the day on one day won't exist until some point on the next
  // business day.
  // Since that might roll over a weekend, we add an extra 2 days to compensate at the start of the week.
  const earlyInWeek = isMonday(today) || isTuesday(today);
  const minDays = earlyInWeek ? 3 : 1;
  const maxDays = minDays + 2;

  const query = `
    SELECT json_build_object(
    'fineosAbsenceId', c.fineos_absence_id,
    'employerFein', a.employer_fein,
    'employerId', c.employer_id,
    'firstName', a.first_name,
    'lastName', a.last_name,
    'leave_type', lct.claim_type_description,
    'employeeId', c.employee_id,
    'leaveDecision', aplc.leave_request_decision_id,
    'frequency_interval', ilp.frequency_interval, 
    'frequency_interval_basis', ilp.frequency_interval_basis,
    'duration', ilp.duration,
    'duration_basis', ilp.duration_basis,
    'notificationTime', n.created_at)
    FROM claim AS c
    INNER JOIN lk_claim_type lct ON lct.claim_type_id = c.claim_type_id
    INNER JOIN application AS a ON c.claim_id = a.claim_id
    INNER JOIN public.user AS u ON a.user_id = u.user_id
    INNER JOIN notification AS n ON c.fineos_absence_id = n.fineos_absence_id
    INNER JOIN absence_period aplc ON aplc.claim_id = c.claim_id
    INNER JOIN intermittent_leave_period ilp ON a.application_id = ilp.application_id
    WHERE n.request_json->>'trigger' = 'Intermittent Time Request Decision'
      AND u.email_address = '${emailAddress}'
      AND c.created_at <  (CURRENT_DATE - INTERVAL '${minDays} days')
      AND c.created_at >= (CURRENT_DATE - INTERVAL '${maxDays} days')
    ORDER BY n.created_at DESC
    LIMIT 1;`;

  cy.task<{ fineosAbsenceId: string }>("queryDb", query).then((result) => {
    if (!result) {
      throw new Error("Unable to find recent claim");
    }
    cy.stash("previousClaim", result);
  });
}

type PriorIntermittentClaimData = {
  fineosAbsenceId: string;
  employerFein: string;
  employerId: string;
  firstName: string;
  lastName: string;
  employeeId: string;
  notificationTime: string;
  frequency_interval: number;
  frequency_interval_basis: "Days" | "Weeks" | "Months" | null | undefined;
  duration: number;
  duration_basis: "Days" | "Minutes" | "Hours" | null | undefined;
  leaveDecision: number;
  leave_type: string;
};
