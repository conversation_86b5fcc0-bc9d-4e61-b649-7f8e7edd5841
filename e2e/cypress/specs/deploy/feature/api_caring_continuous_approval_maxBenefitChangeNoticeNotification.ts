import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import { email, fineos, fineosPages, portal } from "../../../actions";

const NOTICE_TYPE = "Maximum Weekly Benefit Change Notice (PDF)";
describe("Create a Max Weekly Benefit Change Notice in FINEOS and check delivery to the LA/Claimant portal", () => {
  after(() => {
    portal.deleteDownloadsFolder();
  });

  const submission =
    it("Submits a Care for a Family Member claim via the API", () => {
      portal.generateAndSubmitClaimToAPI({
        logSubmissionToNewRelic: true,
        scenario: "CCAP90ER",
      });
    });

  const approval =
    it("CSR will begin adjudication Approve the Claim & trigger notice", () => {
      cy.dependsOnPreviousPass([submission]);
      fineos.before();
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<Submission>("submission").then((response) => {
          if (!response.fineos_absence_id) {
            throw new Error("Response contained no fineos_absence_id property");
          }
          const claimPage = fineosPages.ClaimPage.visit(
            response.fineos_absence_id
          );
          claimPage.adjudicate((adjudication) => {
            adjudication
              .evidence((evidence) => {
                for (const document of claim.documents) {
                  evidence.receive(document.document_type);
                }
              })
              .certificationPeriods((certificationPeriods) =>
                certificationPeriods.prefill()
              )
              .acceptLeavePlan(true);
          });
          claimPage.approve("Completed");
          claimPage.triggerNotice("Designation Notice");
        });
      });
    });

  const notification =
    it("CSR will generate a maximum weekly benefit amount change notice", () => {
      cy.dependsOnPreviousPass([approval]);
      fineos.before();
      cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
        fineosPages.ClaimPage.visit(fineos_absence_id).paidLeave(
          (paidLeavePage) =>
            paidLeavePage
              .createCorrespondenceDocument(
                "Maximum Weekly Benefit Change Notice"
              )
              .documents((documentsPage) =>
                documentsPage
                  .assertDocumentExists("Maximum Weekly Benefit Change Notice")
                  .properties(
                    "Maximum Weekly Benefit Change Notice",
                    (propertiesPage) => propertiesPage.setStatus("Completed")
                  )
                  .properties(
                    "Maximum Weekly Benefit Change Notice",
                    (propertiesPage) =>
                      propertiesPage.fileNameShouldMatch(/.pdf$/)
                  )
              )
              .triggerPaidLeaveNotice("Maximum Weekly Benefit Change Notice")
        );
      });
    });

  it("Check the leave admin portal for the maximum weekly benefit change notice and download it", () => {
    cy.dependsOnPreviousPass([approval, notification]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        if (!claim.employer_fein) {
          throw new Error("Claim must include employer FEIN");
        }
        const employeeFullName = `${claim.first_name} ${claim.last_name}`;
        portal.loginLeaveAdmin(claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        portal.checkNoticeForLeaveAdmin(employeeFullName, NOTICE_TYPE);
        portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
      });
    });
  });

  it("Check the claimant portal for the max weekly benefit change notice and download it", () => {
    cy.dependsOnPreviousPass([approval, notification]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      portal.loginClaimant();
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.claimantAssertClaimStatus([
        {
          leave: "Care for a Family Member",
          status: "Approved",
        },
      ]);
      portal.checkNoticeForClaimant(NOTICE_TYPE);
      portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
    });
  });

  it("Check Claimant email for maximum weekly benefit change notification", () => {
    cy.dependsOnPreviousPass([approval, notification]);
    cy.unstash<Submission>("submission").then((submission) => {
      const pattern = config("HAS_NOTICE_ENHANCEMENTS")
        ? `Your maximum weekly benefit amount has changed for.*${submission.fineos_absence_id}`
        : `${submission.fineos_absence_id}.*Your maximum weekly benefit has changed`;
      email.getEmails({
        address: "<EMAIL>",
        subject: email.getNotificationSubject("appeal (claimant)"),
        message: { pattern },
        timestamp_from: submission.timestamp_from,
        debugInfo: {
          fineosClaimID: submission.fineos_absence_id,
          triggerType: "Maximum Weekly Benefit Change Notice",
          recipient: "Claimant",
        },
      });
    });
  });

  it("Check Leave Admin email for maximum weekly benefit change notification", () => {
    cy.dependsOnPreviousPass([approval, notification]);
    cy.unstash<Submission>("submission").then((submission) => {
      const portalPath = `/employers/applications/status/?absence_id=${submission.fineos_absence_id}`;
      email.getEmails(
        {
          address: "<EMAIL>",
          subject: email.getNotificationSubject("appeal (claimant)"),
          message: portalPath,
          timestamp_from: submission.timestamp_from,
          debugInfo: {
            fineosClaimID: submission.fineos_absence_id,
            triggerType: "Maximum Weekly Benefit Change Notice",
            recipient: "Leave Admin",
          },
        },
        90_000
      );
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        cy.contains(
          `Employee ${claim.first_name} ${claim.last_name} has been notified that their maximum weekly benefit was changed.`
        );
        cy.get(`a[href*='${portalPath}']`);
      });
    });
  });
});
