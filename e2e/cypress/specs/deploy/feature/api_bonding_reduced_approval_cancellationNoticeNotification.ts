import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import {
  findCertificationDoc,
  getDocumentReviewTaskName,
} from "../../../../src/util/documents";
import { email, fineos, fineosPages, portal } from "../../../actions";
import { unstashMultipleKeys } from "../../../util";

const NOTICE_TYPE = "Approved Leave Dates Cancelled (PDF)";

describe("Approval (notifications/notices)", () => {
  after(() => {
    portal.deleteDownloadsFolder();
  });

  const submission =
    it("Submits a reduced Child Bonding - Foster Care claim via the API", () => {
      portal.generateAndSubmitClaimToAPI({
        logSubmissionToNewRelic: true,
        scenario: "REDUCED_ER",
      });
    });

  const approval =
    it("Submit a fully approved claim and trigger Designation Notice", () => {
      cy.dependsOnPreviousPass();
      fineos.before();
      unstashMultipleKeys<{ claim: DehydratedClaim; submission: Submission }>([
        "claim",
        "submission",
      ]).then(({ claim, submission }) => {
        if (!submission.fineos_absence_id) {
          throw new Error("Response contained no fineos_absence_id property");
        }
        const claimPage = fineosPages.ClaimPage.visit(
          submission.fineos_absence_id
        );
        claimPage
          .adjudicate((adjudication) => {
            adjudication
              .evidence((evidence) => {
                // Receive and approve all of the documentation for the claim.
                claim.documents.forEach((doc) =>
                  evidence.receive(doc.document_type)
                );
              })
              .certificationPeriods((cert) => cert.prefill());
            // We specifically avoid accepting the leave plan to test
            // auto-approval.
          })
          .tasks((tasks) => {
            const certificationDoc = findCertificationDoc(claim.documents);
            const certificationTask = getDocumentReviewTaskName(
              certificationDoc.document_type
            );
            // Documents task will automatically close when “received and satisfied” or “received and not satisfied”
            // so we will need to check under the "All tasks" instead of "Open tasks".
            // https://lwd.atlassian.net/browse/CPS-4243
            tasks.all();
            if (!claim.claim.mass_id) {
              tasks.assertTaskExists("ID Review");
            }
            tasks.assertTaskExists(certificationTask);
            tasks.checkAndCloseOverlappingAbsenceTask();
            tasks.assertTaskExists("Ready to Approve");
          })
          .shouldHaveStatus("Applicability", "Applicable")
          .shouldHaveStatus("Eligibility", "Met")
          .shouldHaveStatus("Evidence", "Satisfied")
          .shouldHaveStatus("Availability", "Time Available")
          .shouldHaveStatus("Restriction", "Passed")
          .shouldHaveStatus("PlanDecision", "Undecided");

        claimPage.triggerNotice("SOM Auto Approve Leave Request");
        claimPage.assertClaimStatus("Approved");

        claimPage.triggerNotice("Designation Notice");
      });
    });

  const cancellation =
    it("Records Cancellation and trigger Leave Cancellation Request notice", () => {
      cy.dependsOnPreviousPass([approval]);
      fineos.before();
      cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
        fineosPages.ClaimPage.visit(fineos_absence_id)
          .recordCancellation()
          .tasks((tasks) => {
            tasks
              .all()
              .assertTaskExists("Review and Decision Cancel Time Submitted");
          })
          .documents((docsPage) => {
            docsPage.assertDocumentExists("Record Cancel Time");
          })
          .triggerNotice("Leave Cancellation Request");
      });
    });

  it("Check the Leave Admin Portal for the Cancellation notice and download it", () => {
    cy.dependsOnPreviousPass([submission, cancellation]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        if (!claim.claim.employer_fein) {
          throw new Error("Claim must include employer FEIN");
        }
        const employeeFullName = `${claim.claim.first_name} ${claim.claim.last_name}`;
        portal.loginLeaveAdmin(claim.claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        portal.checkNoticeForLeaveAdmin(employeeFullName, NOTICE_TYPE);
        portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
      });
    });
  });

  it("Check the Claimant Portal for the legal Cancellation notice and download it", () => {
    cy.dependsOnPreviousPass([submission, cancellation]);
    portal.before();
    portal.loginClaimant();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.log("Finished waiting for documents");
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.claimantAssertClaimStatus([
        {
          leave: "Child Bonding",
          status: "Cancelled",
        },
      ]);
      portal.checkNoticeForClaimant(NOTICE_TYPE);
      portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
    });
  });

  it(
    "Check the Claimant email for the Cancellation notification.",
    { retries: 0 },
    () => {
      {
        cy.dependsOnPreviousPass([submission, cancellation]);
        cy.unstash<Submission>("submission").then((submission) => {
          cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
            // The notification is using the same subject line as Appeals claimant.
            const subjectClaimant = email.getNotificationSubject(
              "appeal (claimant)",
              submission.fineos_absence_id,
              `${claim.first_name} ${claim.last_name}`
            );
            const pattern = config("HAS_NOTICE_ENHANCEMENTS")
              ? `Leave dates for.*${submission.fineos_absence_id}.*have been cancelled`
              : `${submission.fineos_absence_id}.*Your approved time has been cancelled`;
            email.getEmails(
              {
                address: "<EMAIL>",
                subject: subjectClaimant,
                message: {
                  pattern,
                },
                timestamp_from: submission.timestamp_from,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Leave Cancellation Request",
                  recipient: "Claimant",
                },
              },
              90_000
            );
          });
        });
      }
    }
  );

  it(
    "Check the Leave Admin email for the Cancellation notification.",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([submission, cancellation]);
      cy.unstash<Submission>("submission").then((submission) => {
        cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
          // The notification is using the same subject line as Appeals claimant.
          const subjectEmployer = email.getNotificationSubject(
            "appeal (claimant)",
            submission.fineos_absence_id,
            `${claim.first_name} ${claim.last_name}`
          );
          const emailMessage = `Employee .*${claim.first_name} ${claim.last_name}.* has been notified that their approved leave time has been cancelled.`;
          email
            .getEmails(
              {
                address: "<EMAIL>",
                subject: subjectEmployer,
                message: {
                  pattern: emailMessage,
                },
                timestamp_from: submission.timestamp_from,
                debugInfo: {
                  fineosClaimID: submission.fineos_absence_id,
                  triggerType: "Leave Cancellation Request",
                  recipient: "Leave Admin",
                },
              },
              90_000
            )
            .then(() => {
              cy.contains(submission.fineos_absence_id);
              cy.get(
                `a[href*="/employers/applications/status/?absence_id=${submission.fineos_absence_id}"]`
              );
            });
        });
      });
    }
  );
});
