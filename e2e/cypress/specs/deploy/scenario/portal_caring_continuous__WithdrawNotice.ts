import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import { claim, email, fineos, fineosPages, portal } from "../../../actions";

const NOTICE_TYPE = "Pending Application Withdrawn (PDF)";

describe("Create a new caring leave claim in Portal. Then withdraw the Absence Case", () => {
  after(() => {
    portal.deleteDownloadsFolder();
  });

  const submit = it("I can start and withdraw a claim from the portal", () => {
    portal.before();

    portal.assertLanguagesMenuIsVisible();

    claim.generateClaim("HIST_CASE").then((claim) => {
      cy.stash("claim", claim);
      // Submit application without notifying the employer.
      if (claim.claim.leave_details) {
        claim.claim.leave_details.employer_notified = false;
      }

      portal.skipLoadingClaimantApplications();
      portal.loginClaimant();
      portal.startClaimAndSubmitClaimPartOne(
        claim.claim,
        undefined,
        claim.employer
      );
      portal
        .waitForClaimSubmission({ logSubmissionToNewRelic: true })
        .then((submission) => {
          portal.assertCanWithdrawApplication(submission.fineos_absence_id);
          portal.submitClaimPartsTwoThree(
            claim.claim,
            claim.paymentPreference,
            {
              is_withholding_tax: claim.is_withholding_tax,
            }
          );
        });
    });
  });

  const claimantWithdrawal =
    it("As a claimant, I can withdraw a non-adjudicated claim in Portal", () => {
      cy.dependsOnPreviousPass();
      portal.before();
      cy.unstash<Submission>("submission").then((submission) => {
        portal.loginClaimant();
        portal.claimantGoToClaimStatus(submission.fineos_absence_id);
        portal.claimantAssertClaimStatus([
          { leave: "Care for a Family Member", status: "Pending" },
        ]);
        portal.claimantAssertApplicationTimeline([
          { name: "Documents submitted", status: "Completed" },
          { name: "Employer response", status: "In progress" },
        ]);
        portal.withdrawClaim();
      });
    });

  const withdraw =
    it('Withdraw a claim in FINEOS and check for a "Pending Application Withdrawn" notice', () => {
      cy.dependsOnPreviousPass([submit, claimantWithdrawal]);
      fineos.before();
      cy.unstash<Submission>("submission").then((submission) => {
        const claimPage = fineosPages.ClaimPage.visit(
          submission.fineos_absence_id
        );
        claimPage.documents((docs) => {
          docs.assertDocumentExists("Cancel Time");
        });
        claimPage.tasks((tasks) => {
          tasks.assertTaskExists("Record Leave Period Removal");
          tasks.assertTaskStatus("Record Leave Period Removal", "Open");
        });
        claimPage.withdraw();
        claimPage.triggerNotice("Leave Request Withdrawn");
        claimPage.documents((docsPage) => {
          docsPage.assertDocumentExists("Pending Application Withdrawn");
        });
      });
    });

  it("Produce a withdrawn notice, available for download in Portal", () => {
    cy.dependsOnPreviousPass([withdraw]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      portal.loginClaimant();
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.claimantAssertClaimStatus([
        { leave: "Care for a Family Member", status: "Withdrawn" },
      ]);
      portal.checkNoticeForClaimant("Pending Application Withdrawn (PDF)");
    });
  });

  it('Employer can view "Pending Application Withdrawn (PDF)" notice from ER dashboard in Portal', () => {
    cy.dependsOnPreviousPass([withdraw]);
    portal.before();
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<DehydratedClaim>("claim").then(({ claim }) => {
        if (!claim.employer_fein) {
          throw new Error("Claim must include employer FEIN");
        }
        const employeeFullName = `${claim.first_name} ${claim.last_name}`;
        portal.loginLeaveAdmin(claim.employer_fein as string);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        portal.checkNoticeForLeaveAdmin(employeeFullName, NOTICE_TYPE);
        portal.downloadLegalNotice(NOTICE_TYPE, submission.fineos_absence_id);
      });
    });
  });

  it("Claimant should receive withdrawn notification", { retries: 0 }, () => {
    cy.dependsOnPreviousPass([withdraw]);
    cy.unstash<Submission>("submission").then((submission) => {
      cy.unstash<ApplicationRequestBody>("claim").then((claim) => {
        const emailSubject = email.getNotificationSubject(
          "employee withdrawal",
          submission.fineos_absence_id,
          `${claim.first_name} ${claim.last_name}`
        );
        const pattern = config("HAS_NOTICE_ENHANCEMENTS")
          ? `Your pending leave application has been withdrawn for.*${submission.fineos_absence_id}`
          : `${submission.fineos_absence_id}.*withdrawn`;
        email.getEmails(
          {
            address: "<EMAIL>",
            subject: emailSubject,
            message: { pattern },
            timestamp_from: submission.timestamp_from,
            debugInfo: {
              fineosClaimID: submission.fineos_absence_id,
              triggerType: "Leave Request Withdrawn",
              recipient: "Claimant",
            },
          },
          90_000
        );
      });
    });
  });
});
