import config from "../../../../src/config";
import {
  FineosSecurityGroup,
  FineosTask,
  Submission,
} from "../../../../src/types";
import { getPresetBySecuredActionGroup } from "../../../../src/util/fineosRolePresets";
import { createIntentionalSkipFlag } from "../../../../src/util/skipFlag";
import { fineos, portal } from "../../../actions";
import { ClaimPage } from "../../../actions/fineos.pages";
import { unstashMultipleKeys } from "../../../util";

const TEST_CASES: TestCase[] = getTestCases();

const intentionalSkipFlag = createIntentionalSkipFlag(!config("HAS_FR25_1"));

const SSO2_USERNAME = config("SSO2_USERNAME");
const SSO2_PASSWORD = config("SSO2_PASSWORD");

describe("Departments can put unworkable tasks in Unworkable Tasks department", () => {
  before(function () {
    //TODO remove uat condition when manual cps testing is over.
    if (!config("HAS_FR25_1") || config("ENVIRONMENT") === "uat") {
      this.skip();
    }

    portal.generateAndSubmitClaimToAPI({
      logSubmissionToNewRelic: true,
      employeePoolFileName: config("CPS_EMPLOYEES_FILE"),
      scenario: "CPS_BONDING_FOSTER_ER",
    });

    fineos.before();
    cy.unstash<Submission>("submission").then((submission) => {
      ClaimPage.visitAndTriggerPreliminaryDesignation(
        submission.fineos_absence_id
      );
      // We can't perform adjudication at this point since that would close
      // the ID Review and certification review tasks, preventing them from
      // being transferred.
    });
  });

  TEST_CASES.forEach(({ department, task, securityGroup }) => {
    describe(`${department} can move ${task} to Unworkable Tasks Queue`, () => {
      before(() => {
        const preset = getPresetBySecuredActionGroup(securityGroup);

        cy.task("chooseFineosRole", {
          userId: SSO2_USERNAME,
          preset: preset.key,
          debug: false,
        });
        fineos.before({
          username: SSO2_USERNAME,
          password: SSO2_PASSWORD,
        });
      });

      it(`${intentionalSkipFlag}${department} can move ${task} to Unworkable Tasks Queue`, () => {
        cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
          ClaimPage.visit(fineos_absence_id).tasks((tasks) => {
            tasks
              .all()
              .add(task)
              .assignToDepartment(task, UnworkableTasks)
              .assertIsAssignedToDepartment(task, UnworkableTasks);
          });
        });
      });
    });
  });

  describe("SL - Medical Leave can move Ready to Approve to Unworkable Tasks Queue", () => {
    before(() => {
      const preset = getPresetBySecuredActionGroup("SaviLinx Agents (sec)");
      cy.task("chooseFineosRole", {
        userId: SSO2_USERNAME,
        preset: preset.key,
        debug: false,
      });
      fineos.before({
        username: SSO2_USERNAME,
        password: SSO2_PASSWORD,
      });
    });

    it(`${intentionalSkipFlag}SL - Bonding Leave can move Ready to Approve to Unworkable Tasks Queue`, () => {
      unstashMultipleKeys<{ claim: DehydratedClaim; submission: Submission }>([
        "claim",
        "submission",
      ]).then(({ claim, submission }) => {
        ClaimPage.visit(submission.fineos_absence_id)
          .completeAdjudication(claim.documents, { acceptLeavePlan: false })
          .tasks((tasks) => {
            tasks
              .all()
              .assertTaskExists("Ready to Approve")
              .assignToDepartment("Ready to Approve", UnworkableTasks)
              .assertIsAssignedToDepartment(
                "Ready to Approve",
                UnworkableTasks
              );
          });
      });
    });
  });
});

interface TestCase {
  department: string;
  task: FineosTask;
  securityGroup: FineosSecurityGroup;
}

const UnworkableTasks = "DFML - Unworkable Tasks";

function getTestCases(): TestCase[] {
  return [
    {
      securityGroup: "DFML Claims Supervisors(sec)",
      department: "Claims Supervisor",
      task: "ID Review",
    },
    {
      securityGroup: "DFML Compliance Analyst(sec)",
      department: "Compliance Analyst",
      task: "Medical Pregnancy Certification Review",
    },
    {
      securityGroup: "DFML Appeals Examiner II(sec)",
      department: "Appeals Examiner I",
      task: "Bonding Certification Review",
    },
    {
      securityGroup: "DFML Compliance Supervisors(sec)",
      department: "Overpayment Lead",
      task: "Medical Certification Review",
    },
    {
      securityGroup: "SaviLinx Agents (sec)",
      department: "SL - Medical Leave",
      task: "Caring Certification Review",
    },
  ];
}
