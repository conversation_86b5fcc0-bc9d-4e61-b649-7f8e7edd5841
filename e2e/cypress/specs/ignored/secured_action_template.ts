import config from "../../../src/config";
import { Credentials, Submission } from "../../../src/types";
import { getPresetBySecuredActionGroup } from "../../../src/util/fineosRolePresets";
import { assertValidClaim } from "../../../src/util/typeUtils";
import { fineos, fineosPages, portal } from "../../actions";
import {
  ensureAlertPopUpClosed,
  waitForAjaxComplete,
} from "../../actions/fineos";
import { ClaimantPage } from "../../actions/fineos.claimant";
import { getSecuredActionConfig } from "../../config";
import { getFineosBaseUrl } from "../../util";

const secondaryAccount: Credentials = {
  username: config("SSO2_USERNAME"),
  password: config("SSO2_PASSWORD"),
};

const securedActionsTest = (
  securityGroup: Parameters<typeof getSecuredActionConfig>[0]
) => {
  const cfg = Cypress.config();
  cfg.baseUrl = getFineosBaseUrl();
  const securedActions = getSecuredActionConfig(securityGroup);
  const preset = getPresetBySecuredActionGroup(securityGroup);

  const describeBlock =
    describe(`${securityGroup} secured actions test`, () => {
      before(() => {
        // This first before call is to prevent Cypress from running this
        // task multiple times.
        fineos.before();
        cy.task("chooseFineosRole", {
          userId: secondaryAccount.username,
          preset: preset.key,
          debug: false,
        });
      });

      beforeEach(function () {
        if (!config("HAS_FR25_1")) {
          this.skip();
        }
      });

      const adjClaim = it("Create an absence case through the API", () => {
        cy.dependsOnPreviousPass();
        portal.generateAndSubmitClaimToAPI({
          employeePoolFileName: config("CPS_EMPLOYEES_FILE"),
          logSubmissionToNewRelic: true,
          scenario: "MHAP1_OLB_ER",
        });
      });

      it(
        "Check to see if can create a Historical Absence case (secure action) within an Absence Case",
        { retries: 0 },
        () => {
          cy.dependsOnPreviousPass([adjClaim]);
          fineos.before(secondaryAccount);
          cy.on("fail", (e) => {
            if (
              securedActions.modify_delete_historical_absence === false &&
              e.message.includes(`Control is protected by a Secured Action.`)
            ) {
              console.log("Failed as expected");
            } else {
              throw e;
            }
          });
          cy.unstash<Submission>("submission").then((submission) => {
            const claimPage = fineosPages.ClaimPage.visit(
              submission.fineos_absence_id
            );
            waitForAjaxComplete();
            ensureAlertPopUpClosed();
            claimPage.addHistoricalAbsenceCase();
          });
        }
      );

      it(
        "Check to see if the Suppress Correspondence (secure action) is available in the Absence Case",
        { retries: 0 },
        () => {
          cy.dependsOnPreviousPass([adjClaim]);
          fineos.before(secondaryAccount);
          cy.unstash<Submission>("submission").then((submission) => {
            fineosPages.ClaimPage.visit(submission.fineos_absence_id)
              .removeSuppressCorrespondence(
                securedActions.suppress_correspondence
              )
              .documents((docsPage) => {
                if (securedActions.suppress_correspondence) {
                  docsPage.assertDocumentExists(
                    "Notification Suppression Disabled"
                  );
                }
              });
          });
        }
      );

      const approveClaim =
        it("Approve the Absence Case for the other secure actions checks", () => {
          cy.dependsOnPreviousPass([adjClaim]);
          fineos.before();

          cy.unstash<Submission>("submission").then((submission) => {
            const claimPage = fineosPages.ClaimPage.visit(
              submission.fineos_absence_id
            );
            claimPage.adjudicate((adjudicate) => {
              adjudicate
                .evidence((evidence) => {
                  evidence.receive("Own serious health condition form");
                  evidence.receive("Identification Proof");
                })
                .certificationPeriods((cert) => cert.prefill())
                .acceptLeavePlan(true);
            });
          });
        });

      it(
        "Check to see if certain users are able to change or edit the document type (secure action)",
        { retries: 0 },
        () => {
          cy.dependsOnPreviousPass([adjClaim, approveClaim]);
          fineos.before(secondaryAccount);
          cy.unstash<Submission>("submission").then((submission) => {
            fineosPages.ClaimPage.visit(submission.fineos_absence_id).documents(
              (docPage) => {
                docPage.changeDocType(
                  "Identification Proof",
                  "State managed Paid Leave Confirmation",
                  securedActions.document_type_change
                );
              }
            );
          });
        }
      );

      it(
        "Check to see if the O/R buttons are enabled for the following buttons: Complete, Suppress, & Remove (secure action)",
        { retries: 0 },
        () => {
          cy.dependsOnPreviousPass([adjClaim, approveClaim]);
          fineos.before(secondaryAccount);
          cy.unstash<Submission>("submission").then((submission) => {
            const claimPage = fineosPages.ClaimPage.visit(
              submission.fineos_absence_id
            );
            claimPage.outstandingRequirements((outstanding_requirement) => {
              outstanding_requirement.add();
              waitForAjaxComplete();
              outstanding_requirement.complete(
                "Received",
                "Complete Employer Confirmation",
                securedActions.or_employer_complete
              );
              waitForAjaxComplete();
              outstanding_requirement.reopen(securedActions.or_employer_reopen);
              waitForAjaxComplete();
              outstanding_requirement.suppress(
                "Auto-Suppressed",
                "Suppress Employer Confirmation",
                securedActions.or_employer_suppress
              );
              waitForAjaxComplete();
              outstanding_requirement.reopen(securedActions.or_employer_reopen);
              waitForAjaxComplete();
              outstanding_requirement.removeOR(
                securedActions.or_employer_remove
              );
            });
          });
        }
      );

      it(
        "Check the Claimant profile to see if bulk payee is enabled under Payment Preferences (secure action)",
        { retries: 0 },
        () => {
          cy.dependsOnPreviousPass([adjClaim, approveClaim]);
          fineos.before(secondaryAccount);
          cy.unstash<DehydratedClaim>("claim").then((claim) => {
            assertValidClaim(claim.claim);
            ClaimantPage.visit(claim.claim.tax_identifier)
              .paymentPreferences()
              .edit(securedActions.can_edit_payment_preference)
              .checkBulkPayee(
                securedActions.can_edit_bulk_payee,
                !securedActions.can_edit_payment_preference
              );
          });
        }
      );
    });

  return describeBlock;
};

export default securedActionsTest;
