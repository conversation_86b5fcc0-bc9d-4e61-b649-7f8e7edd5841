import config from "../../../src/config";
import { portal } from "../../actions";

const scenario = "CONCURRENT_EMPLOYMENT_PORTAL";

describe("Concurrent employment portal applicants with variable work schedules", () => {
  const employeePoolFileName = config("CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE");
  const employerPoolFileName = config("CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE");
  it("As a claimant, I should be able to submit a continuous bonding application through the portal with variable work schedule", () => {
    portal.before();
    const claimGenData: ClaimGenData = {
      employerPoolFileName,
      employeePoolFileName,
      scenario,
    };
    cy.task("generateClaimForEmployeeWithoutClaims", claimGenData).then(
      ({ claim }) => {
        const { employer } = claim;

        cy.stash("claim", claim);
        const application: ApplicationRequestBody = claim.claim;
        const paymentPreference = claim.paymentPreference;

        portal.loginClaimant();
        portal.goToDashboardFromApplicationsPage();

        // Submit Claim
        portal.startClaimAndSubmitClaimPartOne(
          application,
          {
            concurrentEmploymentValidation: true,
            workPatternType: "Variable",
          },
          employer
        );

        portal.waitForClaimSubmission({ logSubmissionToNewRelic: true });
        portal.submitClaimPartsTwoThree(application, paymentPreference, {
          is_withholding_tax: claim.is_withholding_tax,
          withCertificationDocument: true,
        });
      }
    );
  });
});
