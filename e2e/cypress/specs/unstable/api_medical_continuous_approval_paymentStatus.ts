import { format } from "date-fns";

import { addBusinessDays } from "../../../global.common";
import config from "../../../src/config";
import { PaymentMade, Submission } from "../../../src/types";
import {
  emailAddresses,
  getClaimantCredentials,
} from "../../../src/util/credentials";
import { getClaimAfterNightlyExtractQuery } from "../../../src/util/queries";
import { fineos, fineosPages, portal } from "../../actions";
import {
  DehydratedPaymentMade,
  hydratePaymentMade,
} from "../../actions/fineos.pages";
import { PaymentStatus } from "../../actions/portal";
import { unstashMultipleKeys } from "../../util";

const scenario = "MED_CONT_ER_APPROVE";

type ClaimInfo = {
  fineos_absence_id: string;
  created_at: string;
  employer_fein: string;
};

const emailAddress = emailAddresses.paymentStatus;
const credentials = getClaimantCredentials(emailAddress);

// Due to weekly db refreshes, we cannot test sent payments in trn
const environmentIsExcluded = ["training", "trn2"].includes(
  config("ENVIRONMENT")
);

describe(
  "Create a new caring leave claim in FINEOS and Suppress Correspondence check",
  {},
  () => {
    beforeEach(function () {
      if (environmentIsExcluded) {
        this.skip();
      }
    });

    const claimSubmission = it("CSR Rep will submit claim to api", () => {
      portal.generateAndSubmitClaimToAPI({
        credentials,
        logSubmissionToNewRelic: true,
        scenario,
      });
    });

    const approval =
      it("CSR Rep will trigger notice on preliminary designation, approve claim and trigger notice on designation ", () => {
        cy.dependsOnPreviousPass([claimSubmission]);
        fineos.before();
        unstashMultipleKeys<{ claim: DehydratedClaim; submission: Submission }>(
          ["claim", "submission"]
        ).then(({ claim, submission }) => {
          const claimPage =
            fineosPages.ClaimPage.visitAndTriggerPreliminaryDesignation(
              submission.fineos_absence_id
            );
          claimPage.completeAdjudication(claim.documents).approve();
          claimPage.triggerNotice("Designation Notice");
        });
      });

    it("Provides a payment status 'Check back date' for the claimant to view payments ", () => {
      cy.dependsOnPreviousPass([approval]);
      portal.before();
      portal.loginClaimant(credentials);
      cy.unstash<Submission>("submission").then((submission) => {
        portal.claimantGoToClaimStatus(submission.fineos_absence_id);
        portal.viewPaymentStatus();
        portal.assertPaymentCheckBackDate(
          addBusinessDays(new Date(), 2, { skipHolidays: true })
        );
      });
    });

    const approvedClaims =
      it("Find claims whose application completed before today with paid payments", () => {
        fineos.before();
        const query = getClaimAfterNightlyExtractQuery(emailAddress);
        // Find claims whose application completed before today with paid payments that are approved.
        cy.task<ClaimInfo>("queryDb", query).then((result) => {
          if (!result || !result.fineos_absence_id) {
            throw Error("Unable to get fineos_absence_id from query");
          }
          cy.stash("claimInfo", {
            fineos_absence_id: result.fineos_absence_id,
            employer_fein: result.employer_fein,
            created_at: result.created_at,
          });
          const claimPage = fineosPages.ClaimPage.visit(
            result.fineos_absence_id
          );
          claimPage.paidLeave((leaveCase) => {
            leaveCase.getPaymentsMade().then((payments) => {
              cy.stash("payments", payments);
            });
          });
        });
      });

    it("Assert payment information after nightly payment extract", () => {
      cy.dependsOnPreviousPass([approvedClaims]);
      portal.before();
      cy.unstash<ClaimInfo>("claimInfo").then((claim) => {
        portal.loginClaimant(credentials);
        portal.claimantGoToClaimStatus(claim.fineos_absence_id, {
          waitForAliases: ["@benefitYearsSearch"],
          visitLink: true,
        });
        portal.viewPaymentStatus();
        cy.unstash<DehydratedPaymentMade[]>("payments").then(
          (dehydratedPaymentsMade) => {
            const paymentsMade = dehydratedPaymentsMade.map((p) =>
              hydratePaymentMade(p)
            );
            const payments = fineosPaymentsToPortal(paymentsMade);
            portal.assertPayments(payments);
          }
        );
      });
    });

    it("Displays payment status in ER Portal", () => {
      cy.dependsOnPreviousPass([approvedClaims]);
      portal.before();
      cy.unstash<ClaimInfo>("claimInfo").then((claim) => {
        portal.loginLeaveAdmin(claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(claim.fineos_absence_id);
        cy.unstash<DehydratedPaymentMade[]>("payments").then(
          (dehydratedPaymentsMade) => {
            const paymentsMade = dehydratedPaymentsMade.map((p) =>
              hydratePaymentMade(p)
            );
            const payments = fineosPaymentsToPortal(paymentsMade);
            portal.assertPayments(payments);
          }
        );
      });
    });
  }
);

function numberWithCommas(value: string) {
  return value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Extract from FINEOS payments the portal PaymentStatus objects.
function fineosPaymentsToPortal(paymentsMade: PaymentMade[]): PaymentStatus[] {
  const payments: PaymentStatus[] = [];

  paymentsMade.forEach((payment) => {
    const formattedCheckMailedDates = getPotentialCheckMailedDates(
      payment.dateEffective
    ).map((d) => format(d, "MMMM d, yyyy"));

    const payPeriod = `${format(
      payment.datePeriodStart,
      "M/d/yyyy"
    )} to ${format(payment.datePeriodEnd, "M/d/yyyy")}`;
    const status =
      payment.netPaymentAmount === "0.00"
        ? "There is an unpaid 7-day waiting period"
        : new RegExp(
            `Check was mailed on (${formattedCheckMailedDates.join("|")})`
          );
    payments.push({
      amount: numberWithCommas(payment.netPaymentAmount),
      payPeriod: payPeriod,
      status: status,
      waitingPeriod: "Unpaid Waiting Period",
    });
  });
  return payments;
}

/**
 * The check should be mailed either one or two business days after the effective date.
 */
function getPotentialCheckMailedDates(effectiveDate: Date) {
  return [
    addBusinessDays(effectiveDate, 1, { skipHolidays: true }),
    addBusinessDays(effectiveDate, 2, { skipHolidays: true }),
  ];
}
