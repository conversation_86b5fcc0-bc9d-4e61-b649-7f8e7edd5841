import config from "../../../src/config";
import { Submission } from "../../../src/types";
import { assertValidClaim } from "../../../src/util/typeUtils";
import { fineos, fineosPages, portal } from "../../actions";

const scenario = "CONCURRENT_EMPLOYMENT_PORTAL";

describe("Concurrent employment portal applicants with fixed work schedule and has an AWW of $0", () => {
  const employeePoolFileName = config("CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE");
  const employerPoolFileName = config("CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE");
  const submission =
    it("As a claimant, I should be able to submit a continuous bonding application through the portal", () => {
      portal.before();
      const claimGenData: ClaimGenData = {
        employerPoolFileName,
        employeePoolFileName,
        scenario: scenario,
      };
      cy.task("generateClaimForEmployeeWithoutClaims", claimGenData).then(
        ({ claim }) => {
          const { employer } = claim;

          cy.stash("claim", claim);
          const application: ApplicationRequestBody = claim.claim;
          const paymentPreference = claim.paymentPreference;

          portal.loginClaimant();
          portal.goToDashboardFromApplicationsPage();

          // Submit Claim
          portal.startClaimAndSubmitClaimPartOne(
            application,
            {
              concurrentEmploymentValidation: true,
            },
            employer
          );

          portal.waitForClaimSubmission({ logSubmissionToNewRelic: true });
          portal.submitClaimPartsTwoThree(application, paymentPreference, {
            is_withholding_tax: claim.is_withholding_tax,
            withCertificationDocument: true,
          });
        }
      );
    });

  it("Leave admin will submit ER approval for employee", () => {
    cy.dependsOnPreviousPass([submission]);
    portal.before();
    cy.unstash<DehydratedClaim>("claim").then((claim) => {
      cy.unstash<Submission>("submission").then((submission) => {
        assertValidClaim(claim.claim);
        portal.loginLeaveAdmin(claim.claim.employer_fein);
        portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
        portal.visitActionRequiredERFormPage(submission.fineos_absence_id);
        portal.respondToLeaveAdminRequest({
          approval: true,
          gaveNotice: true,
        });
      });
    });
  });

  it(
    "CSR rep will approve continuous bonding application",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass();
      fineos.before();
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<Submission>("submission").then((submission) => {
          const claimPage = fineosPages.ClaimPage.visit(
            submission.fineos_absence_id
          );
          claimPage.completeAdjudication(claim.documents, {
            acceptLeavePlan: true,
          });
          claimPage.approve();
        });
      });
    }
  );

  it("Should be able to confirm the average weekly wage is greater than $0", () => {
    cy.dependsOnPreviousPass();
    fineos.before();

    cy.unstash<DehydratedClaim>("claim").then((claim) => {
      cy.unstash<Submission>("submission").then((submission) => {
        assertValidClaim(claim.claim);
        const { fineos_absence_id } = submission;
        const claimPage = fineosPages.ClaimPage.visit(fineos_absence_id);
        claimPage.paidLeave((paidLeave) => {
          paidLeave.getIndividualAverageWeeklyWages().should("not.equal", 0);
        });
      });
    });
  });
});
