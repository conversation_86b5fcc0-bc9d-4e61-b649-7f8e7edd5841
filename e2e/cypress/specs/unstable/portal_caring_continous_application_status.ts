import { ApprovedClaim } from "../../../src/types";
import { getApprovedClaimWithApprovedLeaveRequest } from "../../../src/util/queries";
import { claim, portal } from "../../actions";
import { unstashMultipleKeys } from "../../util";

describe("Leave admins can see the IAWW and WBA for the applicant if approved", () => {
  it("Retrieve claimant with already existing claim", () => {
    cy.task<ApprovedClaim | null>(
      "queryDb",
      getApprovedClaimWithApprovedLeaveRequest()
    ).then((claim) => {
      if (!claim) {
        throw new Error("No existing approved claim could be retrieved");
      }
      cy.stash("fineosId", claim.fineosId);
      cy.stash("claim", claim);
    });
  });

  it(
    "Leave admin reviews the completed application to check the IAWW and WBA",
    { retries: 3 },
    () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ fineosId: string; claim: ApprovedClaim }>([
        "fineosId",
        "claim",
      ]).then(({ fineosId, claim }) => {
        portal.before();
        portal.loginLeaveAdmin(claim.fein);
        portal.selectClaimFromEmployerDashboard(fineosId);

        portal.waitForPageLoad();

        cy.contains(
          "Your employee's individual average weekly wage and weekly benefit amount"
        );

        cy.findByTestId("your-wages").within(() => {
          cy.get("table").within(() => {
            cy.contains("Individual average weekly wage (IAWW)");
            cy.contains("Weekly benefit amount");
          });
        });
      });
    }
  );
});

describe("Leave admins are not able to review IAWW and WBA are not approved", () => {
  it("The claimant submits the application", () => {
    const scenario = "MED_RFI";
    claim.generateClaim(scenario).then(async (claim) => {
      cy.stash("claim", claim);
      const { employer_fein } = claim.claim;
      if (!employer_fein) {
        throw new Error();
      }

      assert(employer_fein);

      // Submit Claim
      cy.task("submitClaimToAPI", { ...claim }).then(
        ({ fineos_absence_id }) => {
          cy.stash("fineosId", fineos_absence_id);
          cy.stash("claim", claim);
        }
      );
    });
  });

  it("Leave admin reviews the completed application", () => {
    cy.dependsOnPreviousPass();
    unstashMultipleKeys<{ fineosId: string; claim: DehydratedClaim }>([
      "fineosId",
      "claim",
    ]).then(({ fineosId, claim }) => {
      portal.before();
      portal.loginLeaveAdmin(claim.employer.fein);
      portal.selectClaimFromEmployerDashboard(fineosId);

      portal.waitForPageLoad();
      cy.findByTestId("your-wages").should("not.exist");
    });
  });
});
