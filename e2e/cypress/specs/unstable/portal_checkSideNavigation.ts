import config from "../../../src/config";
import {
  getClaimCompletedAndApprovedQuery,
  getClaimWithIntermittentLeaveHoursQuery,
} from "../../../src/util/queries";
import { portal } from "../../actions";

type ClaimInfo = {
  fineos_absence_id: string;
  created_at: string;
  absence_period_start_date: string;
};
describe("Check side navigation bar is available when navigating through claims on desktop and mobile", () => {
  const emailAddress = config("PORTAL_USERNAME");
  const mobileDimensions = "iphone-6";

  beforeEach(() => {
    portal.before();
    portal.loginClaimant();
    portal.goToDashboardFromApplicationsPage();
  });

  it("As a claimant, I should be able to navigate to claim and payment status when viewing a completed application", () => {
    testClaimNavigation(getClaimCompletedAndApprovedQuery);
  });

  it("As a claimant, I should be able to navigate to claim and payment status when viewing an application with intermittent leave", () => {
    testClaimNavigation(getClaimWithIntermittentLeaveHoursQuery);
  });

  it("As a claimant on mobile, I should be able to navigate to claim and payment status when viewing a completed application", () => {
    testClaimNavigation(getClaimCompletedAndApprovedQuery, true);
  });

  it("As a claimant on mobile, I should be able to navigate to claim and payment status when viewing an application with intermittent leave", () => {
    testClaimNavigation(getClaimWithIntermittentLeaveHoursQuery, true);
  });

  function testClaimNavigation(
    queryFunction: (email: string) => string,
    isMobile = false
  ) {
    if (isMobile) {
      cy.viewport(mobileDimensions);
    }

    const query = queryFunction(emailAddress);
    cy.task<ClaimInfo>("queryDb", query).then((result) => {
      if (!result) {
        throw new Error("Couldn't find claim suitable for testing");
      }

      portal.claimantGoToClaimStatus(result.fineos_absence_id, {
        visitLink: true,
      });

      portal.verifyPaymentStatus();
      portal.verifyApplicationNav();

      if (queryFunction === getClaimWithIntermittentLeaveHoursQuery) {
        portal.claimantGoToReportLeaveHours();
      }
      if (isMobile) {
        // Check that the menu button can open and close on mobile, the menu should always be defaulted to open
        cy.contains("button", "Menu").should("be.visible");
        cy.contains("button", "Menu").click();
        cy.contains("button", "Menu").should(
          "have.attr",
          "aria-expanded",
          "false"
        );
        cy.contains("button", "Menu").click();
        cy.contains("button", "Menu").should(
          "have.attr",
          "aria-expanded",
          "true"
        );
      }
    });
  }
});
