import {
  addBusinessDays,
  addDays,
  format,
  parseISO,
  subBusinessDays,
  subDays,
} from "date-fns";

import config from "../../../src/config";
import { Submission } from "../../../src/types";
import {
  emailAddresses,
  getClaimantCredentials,
} from "../../../src/util/credentials";
import { fineos, fineosPages, portal } from "../../actions";
import { ClaimPage } from "../../actions/fineos.pages";
import { unstashMultipleKeys } from "../../util";

const emailAddress = emailAddresses.surfaceKeyInformation;
const credentials = getClaimantCredentials(emailAddress);

describe("Surface key information for claim extensions", () => {
  describe("Scenario: Employer review pending", () => {
    context(
      "Scenario: Employer review pending - Submit approved base claims",
      () => {
        it("Scenario: Employer review pending - Submit base claim that has been approved by employer and DFML", () => {
          portal.generateAndSubmitClaimToAPI({
            credentials,
            logSubmissionToNewRelic: true,
            scenario: "REDUCED_CB_WITH_EXTENSION",
          });
        });

        it("Scenario: Employer review pending - DFML Agent adjudicates and approves the base claim", () => {
          cy.dependsOnPreviousPass();
          unstashMultipleKeys<{
            claim: DehydratedClaim;
            submission: Submission;
          }>(["claim", "submission"]).then(({ claim, submission }) => {
            fineos.before();
            const claimPage = ClaimPage.visit(submission.fineos_absence_id);

            claimPage
              .completeAdjudication(claim.documents, { acceptLeavePlan: true })
              .approve("Approved");
          });
        });
      }
    );

    context("Scenario: Employer review pending - Submit extension", () => {
      it("Scenario: Employer review pending - Retrieve already existing approved, claim for extension", () => {
        cy.task<BaseClaim>("queryDb", findBaseClaim()).then((baseClaim) => {
          if (!baseClaim) {
            throw new Error(
              "Could not retrieve an approved base claim that could be extended."
            );
          }
          cy.stash("baseClaim", baseClaim);
        });
      });

      it("Scenario: Employer review pending - The claimant submits an extension for the claim", () => {
        cy.dependsOnPreviousPass();
        portal.before();
        cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
          const endDate = parseISO(baseClaim.endDate);
          const newEndDate = addBusinessDays(endDate, 2);

          portal.loginClaimant(credentials);
          portal.claimantGoToClaimStatus(baseClaim.fineosId, {
            visitLink: true,
          });
          portal.requestModification("extension", newEndDate);
        });
      });

      it("Scenario: Employer review pending - The CSR confirms the extension", () => {
        cy.dependsOnPreviousPass();
        cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
          const endDate = parseISO(baseClaim.endDate);
          const newEndDate = addDays(endDate, 2);
          const extensionStartDate = subDays(newEndDate, 1);
          const claimPage = fineosPages.ClaimPage.visit(baseClaim.fineosId);
          claimPage
            .benefitsExtension((benefitsExtension) =>
              benefitsExtension.extendLeave(
                format(extensionStartDate, "MM/dd/yyyy"),
                format(newEndDate, "MM/dd/yyyy"),
                false,
                "reduced",
                false
              )
            )
            .adjudicate((page) =>
              page.evidence((page) => {
                page.receive("Identification Proof");
                page.receive("Child bonding evidence form");
              })
            );
        });
      });

      it("Scenario: Employer review pending - The leave admin reviews the application", () => {
        cy.dependsOnPreviousPass();

        const reviewDueDate = addBusinessDays(Date.now(), 10);
        cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
          const appCompletedAt = new Date(baseClaim.appCompletedDate);
          portal.before();
          portal.loginLeaveAdmin(baseClaim.fein);
          portal.goToEmployerApplicationsPage();
          portal.searchClaims(baseClaim.fineosId);
          cy.get(`[title="employers application status"] > p`)
            .should("contain.text", "Application completed")
            .should("contain.text", format(appCompletedAt, "M/d/yyyy"));
          cy.get(`[title="employers employer review"] > p`)
            .should("contain.text", "Employer review due")
            .should("contain.text", format(reviewDueDate, "M/d/yyyy"));
          cy.get('[data-label="To do"]')
            .should("contain.text", "Review Application")
            .should(
              "contain.text",
              `Due by ${format(reviewDueDate, "M/d/yyyy")}`
            );
        });
      });
    });
  });

  describe("Scenario: Employer review completed, awaiting decision", () => {
    context(
      "Scenario: Employer review completed, awaiting decision - Submit approved base claims",
      () => {
        it("Scenario: Employer review completed, awaiting decision - Submit base claim that has been approved by employer and DFML", () => {
          portal.generateAndSubmitClaimToAPI({
            credentials,
            logSubmissionToNewRelic: true,
            scenario: "REDUCED_CB_WITH_EXTENSION",
          });
        });

        it("Scenario: Employer review completed, awaiting decision - DFML Agent adjudicates and approves the base claim", () => {
          cy.dependsOnPreviousPass();
          unstashMultipleKeys<{
            claim: DehydratedClaim;
            submission: Submission;
          }>(["claim", "submission"]).then(({ claim, submission }) => {
            fineos.before();
            const claimPage = ClaimPage.visit(submission.fineos_absence_id);

            claimPage
              .completeAdjudication(claim.documents, { acceptLeavePlan: true })
              .approve("Approved");
          });
        });
      }
    );

    context(
      "Scenario: Employer review completed, awaiting decision - Submit extension",
      () => {
        it("Retrieve already existing approved, claim for extension", () => {
          cy.task<BaseClaim>("queryDb", findBaseClaim()).then((baseClaim) => {
            if (!baseClaim) {
              throw new Error(
                "Could not retrieve an approved base claim that could be extended."
              );
            }
            cy.stash("baseClaim", baseClaim);
          });
        });

        it("Scenario: Employer review completed, awaiting decision - The claimant submits an extension for the claim", () => {
          cy.dependsOnPreviousPass();
          portal.before();
          cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
            const endDate = parseISO(baseClaim.endDate);
            const newEndDate = addBusinessDays(endDate, 2);

            portal.loginClaimant(credentials);
            portal.claimantGoToClaimStatus(baseClaim.fineosId, {
              visitLink: true,
            });
            portal.requestModification("extension", newEndDate);
          });
        });

        it("Scenario: Employer review completed, awaiting decision - The CSR confirms the extension", () => {
          cy.dependsOnPreviousPass();
          cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
            const endDate = parseISO(baseClaim.endDate);
            const newEndDate = addDays(endDate, 2);
            const extensionStartDate = subDays(newEndDate, 1);
            const claimPage = fineosPages.ClaimPage.visit(baseClaim.fineosId);
            claimPage
              .benefitsExtension((benefitsExtension) =>
                benefitsExtension.extendLeave(
                  format(extensionStartDate, "MM/dd/yyyy"),
                  format(newEndDate, "MM/dd/yyyy"),
                  false,
                  "reduced",
                  false
                )
              )
              .adjudicate((page) =>
                page.evidence((page) => {
                  page.receive("Identification Proof");
                  page.receive("Child bonding evidence form");
                })
              );
          });
        });

        it("Scenario: Employer review completed, awaiting decision - The employer reviews the extension and approves it", () => {
          cy.dependsOnPreviousPass();
          cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
            cy.task("submitEmployerResponseToApi", {
              employerResponse: {
                employer_benefits: [],
                employer_decision: "Approve",
                fraud: "No",
                hours_worked_per_week: {
                  hours_worked: 40,
                  employer_changes: "Unchanged",
                },
                previous_leaves: [],
              },
              identifiers: {
                employerFein: baseClaim.fein,
                fineosAbsenceId: baseClaim.fineosId,
              },
            });
          });
        });

        it("Scenario: Employer review completed, awaiting decision - The leave admin reviews the application", () => {
          cy.dependsOnPreviousPass();

          cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
            portal.before();
            portal.loginLeaveAdmin(baseClaim.fein);
            portal.getLeaveAdminName().then((leaveAdminName) => {
              const reviewDueDate = addBusinessDays(Date.now(), 10);
              const appCompletedAt = new Date(baseClaim.appCompletedDate);

              portal.goToEmployerApplicationsPage();
              portal.searchClaims(baseClaim.fineosId);
              cy.contains(
                `Application completed ${format(appCompletedAt, "M/d/yyyy")}`
              );
              cy.contains(
                `DFML decision expected ${format(reviewDueDate, "M/d/yyyy")}`
              );
              cy.get('[title="employers employer review"] > p')
                .should("contain.text", "Employer review")
                .should("contain.text", `completed by ${leaveAdminName}`)
                .should("contain.text", format(Date.now(), "M/d/yyyy"));
              cy.get('[data-label="To do"]')
                .should("contain.text", "No action required")
                .should("contain.text", "Employer review completed");
            });
          });
        });
      }
    );
  });

  describe("Scenario: Employer review completed, differing decision", () => {
    context(
      "Scenario: Employer review completed, differing decision - Submit approved base claims",
      () => {
        it("Scenario: Employer review completed, differing decision - Submit base claim that has been approved by employer and DFML", () => {
          portal.generateAndSubmitClaimToAPI({
            credentials,
            logSubmissionToNewRelic: true,
            scenario: "REDUCED_CB_WITH_EXTENSION",
          });
        });

        it("Scenario: Employer review completed, differing decision - DFML Agent adjudicates and approves the base claim", () => {
          cy.dependsOnPreviousPass();
          unstashMultipleKeys<{
            claim: DehydratedClaim;
            submission: Submission;
          }>(["claim", "submission"]).then(({ claim, submission }) => {
            fineos.before();
            const claimPage = ClaimPage.visit(submission.fineos_absence_id);

            claimPage
              .completeAdjudication(claim.documents, { acceptLeavePlan: true })
              .approve("Approved");
          });
        });
      }
    );

    context(
      "Scenario: Employer review completed, differing decision - Submit extension",
      () => {
        it("Scenario: Employer review completed, differing decision - Retrieve already existing approved, claim for extension", () => {
          cy.task<BaseClaim>("queryDb", findBaseClaim()).then((baseClaim) => {
            if (!baseClaim) {
              throw new Error(
                "Could not retrieve an approved base claim that could be extended."
              );
            }
            cy.stash("baseClaim", baseClaim);
          });
        });

        it("Scenario: Employer review completed, differing decision - The claimant submits an extension for the claim", () => {
          cy.dependsOnPreviousPass();
          portal.before();
          cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
            const endDate = parseISO(baseClaim.endDate);
            const newEndDate = addDays(endDate, 2);

            portal.loginClaimant(credentials);
            portal.claimantGoToClaimStatus(baseClaim.fineosId, {
              visitLink: true,
            });
            portal.requestModification("extension", newEndDate);
          });
        });

        it("Scenario: Employer review completed, differing decision - The CSR confirms the extension", () => {
          cy.dependsOnPreviousPass();
          cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
            const endDate = parseISO(baseClaim.endDate);
            const newEndDate = addBusinessDays(endDate, 2);
            const extensionStartDate = subBusinessDays(newEndDate, 1);
            const claimPage = fineosPages.ClaimPage.visit(baseClaim.fineosId);
            claimPage
              .benefitsExtension((benefitsExtension) =>
                benefitsExtension.extendLeave(
                  format(extensionStartDate, "MM/dd/yyyy"),
                  format(newEndDate, "MM/dd/yyyy"),
                  false,
                  "reduced",
                  false
                )
              )
              .adjudicate((page) =>
                page.evidence((page) => {
                  page.receive("Identification Proof");
                  page.receive("Child bonding evidence form");
                })
              );
          });
        });

        it("Scenario: Employer review completed, differing decision - The employer reviews the extension and approves it", () => {
          cy.dependsOnPreviousPass();
          cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
            cy.task("submitEmployerResponseToApi", {
              employerResponse: {
                employer_benefits: [],
                employer_decision: "Approve",
                fraud: "No",
                hours_worked_per_week: {
                  hours_worked: 40,
                  employer_changes: "Unchanged",
                },
                previous_leaves: [],
              },
              identifiers: {
                employerFein: baseClaim.fein,
                fineosAbsenceId: baseClaim.fineosId,
              },
            });
          });
        });

        it("Scenario: Employer review completed, differing decision - The CSR denies the extension", () => {
          cy.dependsOnPreviousPass();
          unstashMultipleKeys<{ claim: DehydratedClaim; baseClaim: BaseClaim }>(
            ["claim", "baseClaim"]
          ).then(({ claim, baseClaim }) => {
            fineos.before();

            const claimPage = fineosPages.ClaimPage.visit(baseClaim.fineosId);

            if (config("HAS_FR25_1")) {
              claimPage.adjudicate((adjudication) => {
                adjudication.rejectLeavePlan();
              });

              claimPage.deny(
                "Covered family relationship not established",
                true,
                true
              );
            } else {
              claimPage
                .completeAdjudication(claim.documents)
                .denyExtendedTime("Claimant wages under minimum");
            }

            claimPage.triggerNotice("Preliminary Designation");
          });
        });

        it("Scenario: Employer review completed, differing decision - The leave admin reviews the application", () => {
          cy.dependsOnPreviousPass();

          cy.unstash<BaseClaim>("baseClaim").then((baseClaim) => {
            const appCompletedAt = new Date(baseClaim.appCompletedDate);

            portal.before();
            const credentials = getClaimantCredentials(emailAddress);
            cy.task("syncLeaveDetails", {
              fineosId: baseClaim.fineosId,
              credentials,
            }).then((_) => {
              portal.loginLeaveAdmin(baseClaim.fein);
              portal.getLeaveAdminName().then((leaveAdminName) => {
                portal.goToEmployerApplicationsPage();
                portal.searchClaims(baseClaim.fineosId);
                cy.contains(
                  `Application completed ${format(appCompletedAt, "M/d/yyyy")}`
                );
                cy.contains("-DFML decision reached-");
                cy.get('[title="employers employer review"] > p')
                  .should("contain.text", "Employer review")
                  .should("contain.text", `completed by ${leaveAdminName}`)
                  .should("contain.text", `${format(Date.now(), "M/d/yyyy")}`);
                cy.get('[data-label="To do"]').contains("No action required");
              });
            });
          });
        });
      }
    );
  });
});

/**
 * Find a claim which has been reviewed by the employer and DFML has adjudicated.
 * These claims do not have attached extensions nor appeals associated with them.
 */
export function findBaseClaim(): string {
  return `
    SELECT json_build_object(
        'fein', emp.employer_fein,
        'fineosId', c.fineos_absence_id,
        'startDate', c.claim_start_date,
        'endDate', c.claim_end_date,
        'ssn', ti.tax_identifier,
        'appCompletedDate', app.completed_time
    ) from claim c
      JOIN absence_period AS ap ON ap.claim_id = c.claim_id
      JOIN employee AS emy on emy.employee_id = c.employee_id
      JOIN employee_occupation AS eo on eo.employee_id = emy.employee_id
      JOIN employer AS emp on emp.employer_id = eo.employer_id 
      JOIN tax_identifier AS ti ON emy.tax_identifier_id = ti.tax_identifier_id
      JOIN application AS app ON app.claim_id = c.claim_id
      JOIN lk_leave_request_decision
            ON lk_leave_request_decision.leave_request_decision_id = ap.leave_request_decision_id
      WHERE ap.leave_request_decision_id = 3
          AND ap.created_at < (CURRENT_DATE + INTERVAL '1 day')::date
          AND emy.email_address  = '${emailAddress}'
          AND (
              SELECT count(*)
              FROM absence_period
              WHERE absence_period.claim_id = c.claim_id
            ) = 1
      LIMIT 1;
  `;
}

export type BaseClaim = {
  fein: string;
  fineosId: string;
  startDate: string;
  endDate: string;
  ssn: string;
  appCompletedDate: string;
};
