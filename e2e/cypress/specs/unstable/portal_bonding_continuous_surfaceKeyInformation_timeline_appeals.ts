import { addBusinessDays, format } from "date-fns";

import { Submission } from "../../../src/types";
import { fineos, fineosPages, portal } from "../../actions";
import { ClaimPage } from "../../actions/fineos.pages";
import { unstashMultipleKeys } from "../../util";

describe("Surface key information for appeals", () => {
  context("Scenario: Employer review missed", () => {
    it("The claimant submits the application", () => {
      portal.generateAndSubmitClaimToAPI({ scenario: "WDCLAIM" });
    });

    it("Scenario: Employer review missed - The employer misses the deadline to review claim", () => {
      cy.dependsOnPreviousPass();
      cy.unstash<Submission>("submission").then((submission) => {
        const claimPage = fineosPages.ClaimPage.visit(
          submission.fineos_absence_id
        );

        claimPage.outstandingRequirements((outstanding_requirement) => {
          outstanding_requirement.suppress(
            "Auto-Suppressed",
            "Suppress Employer Confirmation",
            true
          );
        });
      });
    });

    it("Scenario: Employer review missed - The DFML agent adjudicates the claim and denies", () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission }) => {
        fineos.before();
        const claimPage = ClaimPage.visit(submission.fineos_absence_id);

        claimPage
          .adjudicate((adjudication) => {
            adjudication.rejectLeavePlan();
          })
          .deny("Claimant wages failed 30x rule", false, false);
      });
    });

    it("Scenario: Employer review missed - The claimant files an appeal", () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission }) => {
        cy.dependsOnPreviousPass();
        portal.before();

        portal.loginClaimant();
        portal.claimantGoToClaimStatus(submission.fineos_absence_id, {
          visitLink: true,
        });
        portal.appeal(true);
      });
    });

    it("Scenario: Employer review missed - The DFML agent reviews and approves the appeal", () => {
      cy.dependsOnPreviousPass();
      fineos.before();
      cy.unstash<Submission>("submission").then((submission) => {
        const claimPage = fineosPages.ClaimPage.visit(
          submission.fineos_absence_id
        );
        claimPage.appealTasks((tasks) => {
          tasks.closeAppealReview(Cypress.currentRetry);
          tasks.close("Schedule Hearing");
          tasks.close("Conduct Hearing");
          tasks.close("Make Decision");
          tasks.closeConductHearing(Cypress.currentRetry);
          tasks.assertTaskExists("Send Decision Notice");
        });
        claimPage.assertClaimStatus("Closed - Claim Decision Changed");
        claimPage
          .appealDocuments((docPage) => {
            docPage.addAppealDocument("Appeal RFI");
          })
          .triggerNotice("SOM Generate Editable Notice");
      });
    });

    it("Scenario: Employer review missed -The leave admin reviews the application status from the timeline", () => {
      cy.dependsOnPreviousPass();

      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission, claim }) => {
        portal.before();
        cy.task("syncLeaveDetails", {
          fineosId: submission.fineos_absence_id,
        }).then((_) => {
          portal.loginLeaveAdmin(claim.employer.fein);
          portal.goToEmployerApplicationsPage();
          portal.searchClaims(submission.fineos_absence_id);

          const reviewDueDate = addBusinessDays(Date.now(), 10);

          cy.get('[title="appeal status"]').should(
            "contain.text",
            "-Appeal filed-"
          );

          cy.get('[data-testid="timeline-period"]').should(
            "contain.text",
            "DFML decision reached"
          );

          cy.get('[title="employers application status"]').should(
            "contain.text",
            "Application completed"
          );

          cy.get('[title="employers employer review"]').should(
            "contain.text",
            `Employer review due ${format(reviewDueDate, "M/d/yyyy")}`
          );

          cy.get('[data-label="To do"]').contains("No action required");
          cy.get('[data-label="To do"]').contains(
            "Employer review deadline missed"
          );
        });
      });
    });
  });

  context("Scenario: Employer review completed", () => {
    it("Scenario: Employer review completed - The claimant submits the application", () => {
      portal.generateAndSubmitClaimToAPI({ scenario: "MED_RFI" });
    });

    it("Scenario: Employer review completed - The DFML agent adjudicates the claim and denies", () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission }) => {
        fineos.before();
        const claimPage = ClaimPage.visit(submission.fineos_absence_id);

        claimPage
          .adjudicate((adjudication) => {
            adjudication.rejectLeavePlan();
          })
          .deny("Claimant wages failed 30x rule", false, false);
      });
    });

    it("Scenario: Employer review completed - The claimant files an appeal", () => {
      cy.dependsOnPreviousPass();
      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission }) => {
        cy.dependsOnPreviousPass();
        portal.before();

        portal.loginClaimant();
        portal.claimantGoToClaimStatus(submission.fineos_absence_id, {
          visitLink: true,
        });
        portal.appeal(true);
      });
    });

    it("Scenario: Employer review completed - The DFML agent reviews and approves the appeal", () => {
      cy.dependsOnPreviousPass();
      fineos.before();
      cy.unstash<Submission>("submission").then((submission) => {
        const claimPage = fineosPages.ClaimPage.visit(
          submission.fineos_absence_id
        );
        claimPage.appealTasks((tasks) => {
          tasks.closeAppealReview(Cypress.currentRetry);
          tasks.close("Schedule Hearing");
          tasks.close("Conduct Hearing");
          tasks.close("Make Decision");
          tasks.closeConductHearing(Cypress.currentRetry);
          tasks.assertTaskExists("Send Decision Notice");
        });
        claimPage.assertClaimStatus("Closed - Claim Decision Changed");
        claimPage
          .appealDocuments((docPage) => {
            docPage.addAppealDocument("Appeal RFI");
          })
          .triggerNotice("SOM Generate Editable Notice");
      });
    });

    it("Scenario: Employer review completed - The leave admin reviews the application status from the timeline", () => {
      cy.dependsOnPreviousPass();

      unstashMultipleKeys<{ submission: Submission; claim: DehydratedClaim }>([
        "submission",
        "claim",
      ]).then(({ submission, claim }) => {
        portal.before();
        portal.loginLeaveAdmin(claim.employer.fein);
        portal.getLeaveAdminName().then((leaveAdminName) => {
          const today = Date.now();
          portal.goToEmployerApplicationsPage();
          portal.searchClaims(submission.fineos_absence_id);

          cy.get('[title="appeal status"]').should(
            "contain.text",
            "-Appeal filed-"
          );

          cy.get('[data-testid="timeline-period"]').should(
            "contain.text",
            "DFML decision reached"
          );

          cy.get('[title="employers employer review"] > p')
            .should("contain.text", "Employer review")
            .should("contain.text", `completed by ${leaveAdminName}`)
            .should("contain.text", format(today, "M/d/yyyy"));

          cy.get('[title="employers application status"] > p')
            .should("contain.text", "Application completed")
            .should("contain.text", format(today, "M/d/yyyy"));

          cy.get('[data-label="To do"]').contains("No action required");
        });
      });
    });
  });
});
