import config from "../src/config";
import type { Credentials, FineosSecurityGroup } from "../src/types";

export function getLeaveAdminCredentials(fein: string): Credentials {
  return {
    username: `gqzap.employer.${fein.replace("-", "")}@inbox.testmail.app`,
    password: config("EMPLOYER_PORTAL_PASSWORD"),
  };
}

export function getSecuredActionConfig(
  securityGroup: Exclude<
    FineosSecurityGroup,
    "Post-Prod Admin(sec)" | "DFML PI(sec)"
  >
) {
  type SecuredActionConfig = {
    can_add_case: boolean;
    can_edit_bulk_payee: boolean;
    can_edit_payment_preference: boolean;
    document_type_change: boolean;
    modify_delete_historical_absence: boolean;
    or_employer_complete: boolean;
    or_employer_remove: boolean;
    or_employer_suppress: boolean;
    or_employer_reopen: boolean;
    suppress_correspondence: boolean;
    can_add_additional_payment: boolean;
  };
  const securedActionsConfig: Record<
    typeof securityGroup,
    SecuredActionConfig
  > = {
    "DFML Claims Examiners(sec)": {
      can_add_case: true,
      can_edit_bulk_payee: true,
      can_edit_payment_preference: true,
      document_type_change: false,
      modify_delete_historical_absence: true,
      or_employer_complete: true,
      or_employer_remove: true,
      or_employer_suppress: true,
      or_employer_reopen: true,
      suppress_correspondence: true,
      can_add_additional_payment: false,
    },
    "DFML Claims Supervisors(sec)": {
      can_add_case: true,
      can_edit_bulk_payee: true,
      can_edit_payment_preference: true,
      document_type_change: true,
      modify_delete_historical_absence: true,
      or_employer_complete: true,
      or_employer_remove: true,
      or_employer_suppress: true,
      or_employer_reopen: true,
      suppress_correspondence: true,
      can_add_additional_payment: false,
    },
    "DFML Compliance Analyst(sec)": {
      can_add_case: false,
      can_edit_bulk_payee: true,
      can_edit_payment_preference: true,
      document_type_change: false,
      modify_delete_historical_absence: true,
      or_employer_complete: true,
      or_employer_remove: true,
      or_employer_suppress: true,
      or_employer_reopen: true,
      suppress_correspondence: true,
      can_add_additional_payment: false,
    },
    "DFML Compliance Supervisors(sec)": {
      can_add_case: true,
      can_edit_bulk_payee: true,
      can_edit_payment_preference: true,
      document_type_change: true,
      modify_delete_historical_absence: true,
      or_employer_complete: true,
      or_employer_remove: true,
      or_employer_suppress: true,
      or_employer_reopen: true,
      suppress_correspondence: true,
      can_add_additional_payment: false,
    },
    "DFML Appeals Administrator(sec)": {
      can_add_case: true,
      can_edit_bulk_payee: false,
      can_edit_payment_preference: true,
      document_type_change: true,
      modify_delete_historical_absence: true,
      or_employer_complete: true,
      or_employer_remove: true,
      or_employer_suppress: true,
      or_employer_reopen: true,
      suppress_correspondence: true,
      can_add_additional_payment: false,
    },
    "DFML Appeals Examiner I(sec)": {
      can_add_case: true,
      can_edit_bulk_payee: false,
      can_edit_payment_preference: true,
      document_type_change: false,
      modify_delete_historical_absence: true,
      or_employer_complete: true,
      or_employer_remove: true,
      or_employer_suppress: true,
      or_employer_reopen: true,
      suppress_correspondence: false,
      can_add_additional_payment: false,
    },
    "DFML Appeals Examiner II(sec)": {
      can_add_case: true,
      can_edit_bulk_payee: false,
      can_edit_payment_preference: true,
      document_type_change: false,
      modify_delete_historical_absence: true,
      or_employer_complete: true,
      or_employer_remove: true,
      or_employer_suppress: true,
      or_employer_reopen: true,
      suppress_correspondence: false,
      can_add_additional_payment: false,
    },
    "SaviLinx Agents (sec)": {
      can_add_case: false,
      can_edit_bulk_payee: false,
      can_edit_payment_preference: false,
      document_type_change: false,
      modify_delete_historical_absence: false,
      or_employer_complete: false,
      or_employer_remove: false,
      or_employer_suppress: false,
      or_employer_reopen: false,
      suppress_correspondence: false,
      can_add_additional_payment: false,
    },
    "SaviLinx Secured Agents(sec)": {
      can_add_case: false,
      can_edit_bulk_payee: false,
      can_edit_payment_preference: false,
      document_type_change: false,
      modify_delete_historical_absence: false,
      or_employer_complete: false,
      or_employer_remove: false,
      or_employer_suppress: false,
      or_employer_reopen: false,
      suppress_correspondence: false,
      can_add_additional_payment: false,
    },
    "SaviLinx Supervisors(sec)": {
      can_add_case: false,
      can_edit_bulk_payee: false,
      can_edit_payment_preference: false,
      document_type_change: true,
      modify_delete_historical_absence: false,
      or_employer_complete: true,
      or_employer_remove: true,
      or_employer_suppress: true,
      or_employer_reopen: true,
      suppress_correspondence: true,
      can_add_additional_payment: false,
    },
    "SaviLinx Back Office Agents(sec)": {
      can_add_case: false,
      can_edit_bulk_payee: false,
      can_edit_payment_preference: false,
      document_type_change: true,
      modify_delete_historical_absence: false,
      or_employer_complete: false,
      or_employer_remove: false,
      or_employer_suppress: false,
      or_employer_reopen: false,
      suppress_correspondence: false,
      can_add_additional_payment: true,
    },
    "DFML IT(sec)": {
      can_add_case: false,
      can_edit_bulk_payee: false,
      can_edit_payment_preference: false,
      document_type_change: false,
      modify_delete_historical_absence: false,
      or_employer_complete: true,
      or_employer_remove: true,
      or_employer_suppress: true,
      or_employer_reopen: true,
      suppress_correspondence: false,
      can_add_additional_payment: false,
    },
  };
  return securedActionsConfig[securityGroup];
}
