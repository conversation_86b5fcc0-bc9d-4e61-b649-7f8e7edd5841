import { mergeTests } from "@playwright/test";

import { fineosBrowserContext } from "./fineos/browser-context";
import { fineosCasesPage } from "./fineos/cases-page";
import { fineosClaimPage } from "./fineos/claim";
import { fineosClaimAppealPage } from "./fineos/claim-appeal-page";
import { fineosClaimDocumentsPage } from "./fineos/claim-documents-page";
import { fineosClaimSubmissions } from "./fineos/claim-submissions";
import { loggedInFineosPage } from "./fineos/login";
import { fineosPage } from "./fineos/page";
import { fineosPaidLeavePage } from "./fineos/paid-leave";
import { scenarioIds } from "./fineos/scenario-ids";
import { fineosTasksPage } from "./fineos/tasks-page";
import { logger } from "./logger";
import { portalAuthorizeIdv } from "./portal/authorize-idv";
import { portalBrowserContext } from "./portal/browser-context";
import { portalEmployerLoginByClaim } from "./portal/employer-login-by-claim";
import { portalIndexPage } from "./portal/index-page";
import { portalLeaveAdminCredentials } from "./portal/leave-admin-credentials";
import { portalEmployerIndexPage } from "./portal/leave-admin-employer-index-page";
import { loggedInLeaveAdminPage } from "./portal/leave-admin-login";
import { portalPage } from "./portal/page";
import { portalUserApplicationStatusPage } from "./portal/user-application-status";
import { portalUserApplicationsPage } from "./portal/user-applications";
import { portalUserCreateApplicationPage } from "./portal/user-create-application-page";
import { portalClaimantCredentials } from "./portal/user-credentials";
import { loggedInClaimantPage } from "./portal/user-login";
import { claimsService } from "./services/claims";

export const test = mergeTests(
  fineosBrowserContext,
  fineosCasesPage,
  fineosClaimAppealPage,
  fineosClaimDocumentsPage,
  fineosClaimPage,
  fineosClaimSubmissions,
  fineosPaidLeavePage,
  fineosPage,
  fineosTasksPage,
  loggedInFineosPage,
  scenarioIds,
  loggedInLeaveAdminPage,
  loggedInClaimantPage,
  portalAuthorizeIdv,
  portalBrowserContext,
  portalClaimantCredentials,
  portalLeaveAdminCredentials,
  portalPage,
  portalIndexPage,
  portalUserApplicationsPage,
  portalUserApplicationStatusPage,
  portalUserCreateApplicationPage,
  portalEmployerIndexPage,
  portalEmployerLoginByClaim,
  claimsService,
  logger
);
export const expect = test.expect;
