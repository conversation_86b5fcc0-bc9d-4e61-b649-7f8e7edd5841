import { mergeTests } from "@playwright/test";

import { LoggedInClaimantPage } from "../../pages/portal/user/logged-in";
import { portalIndexPage } from "./index-page";
import { portalClaimantCredentials } from "./user-credentials";

/*
  <PERSON>gin as a claimant. Accepts credentials or will use default claimant credentials if none are provided.

  OPTIONS: 
    portalClaimantCredentials?
*/

type Fixture = {
  loggedInClaimantPage: LoggedInClaimantPage;
};

export const loggedInClaimantPage = mergeTests(
  portalClaimantCredentials,
  portalIndexPage
).extend<Fixture>({
  loggedInClaimantPage: async (
    { portalClaimantCredentials, portalIndexPage },
    use
  ) => {
    const loggedInClaimantPage = await portalIndexPage.logInAsClaimant(
      portalClaimantCredentials
    );
    await use(loggedInClaimantPage);
  },
});
