import test, { BrowserContext, BrowserContextOptions } from "@playwright/test";

/* 
  Creates a new browser context to be used by Portal Fixtures
*/

type Fixture = {
  portalBrowserContext: BrowserContext;
};

export const portalBrowserContext = test.extend<Fixture>({
  portalBrowserContext: async ({ browser }, use) => {
    const options: BrowserContextOptions = {};
    const context = await browser.newContext(options);

    await use(context);
    await context.close();
  },
});
