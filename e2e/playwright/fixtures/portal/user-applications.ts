import { mergeTests } from "@playwright/test";

import PortalUserApplicationsPage from "../../pages/portal/user/applications";
import { loggedInClaimantPage } from "./user-login";

/*
  Logs in as a claimant. Navigates to the page where the claimant can view all of their applications.
  Will throw error if claimant has no applications. Use portalUserCreateApplicationPage instead for a claimant who has no pre-existing claims.

  OPTIONS:
    portalClaimantCredentials?
*/

type Fixture = {
  portalUserApplicationsPage: PortalUserApplicationsPage;
};

export const portalUserApplicationsPage = mergeTests(
  loggedInClaimantPage
).extend<Fixture>({
  portalUserApplicationsPage: async ({ loggedInClaimantPage }, use) => {
    loggedInClaimantPage;
    const portalUserApplicationsPage =
      await loggedInClaimantPage.navigateToApplicationsPage();
    await use(portalUserApplicationsPage);
  },
});
