import test from "@playwright/test";

import { Scenarios } from "../../../src/types";

/*
  portalEmployerLoginByClaim is an option that allows logging into the Portal as an Employer attached to a specified claim.
  Pass the same scenarioId of the claim that you want to login as the employer of. 

  IMPORTANT: scenarioIds is required to be passed in order for this to work. 
  If not, there won't be a claim generated and therefore there won't be any specific employer to login as.

  Example: 
    test.use({
      scenarioIds: ["MED_INTER_INEL"],
      portalEmployerLoginByClaim: "MED_INTER_INEL"
    })  
*/

type Fixture = {
  portalEmployerLoginByClaim?: Scenarios;
};

export const portalEmployerLoginByClaim = test.extend<Fixture>({
  portalEmployerLoginByClaim: [undefined, { option: true }],
});
