import { mergeTests } from "@playwright/test";

import PortalUserCreateApplicationPage from "../../pages/portal/user/create-application";
import { loggedInClaimantPage } from "./user-login";

/*
  Logs in as a claimant. Navigates to the page where the claimant can create an application.
  This page can be reached even when a claimant has no existing claims, making it a good candidate for tests 
  where you don't know/care if a claimant has pre-existing claims.
  
  OPTIONS:
    portalClaimantCredentials?
*/

type Fixture = {
  portalUserCreateApplicationPage: PortalUserCreateApplicationPage;
};

export const portalUserCreateApplicationPage = mergeTests(
  loggedInClaimantPage
).extend<Fixture>({
  portalUserCreateApplicationPage: async ({ loggedInClaimantPage }, use) => {
    await use(await loggedInClaimantPage.navigateToCreateApplicationPage());
  },
});
