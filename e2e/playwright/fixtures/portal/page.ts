import { mergeTests, Page } from "@playwright/test";

import { authorizeIdvPage } from "../../utils/idv";
import { setup } from "../../utils/portal";
import { portalAuthorizeIdv } from "./authorize-idv";
import { portalBrowserContext } from "./browser-context";

/* 
  Checks for portalAuthorizeIdv argument and sets up the authorizeIdvPage util if true.
*/

type Fixture = {
  portalPage: Page;
};

export const portalPage = mergeTests(
  portalAuthorizeIdv,
  portalBrowserContext
).extend<Fixture>({
  portalPage: async ({ portalAuthorizeIdv, portalBrowserContext }, use) => {
    const page = await portalBrowserContext.newPage();
    if (portalAuthorizeIdv) {
      await authorizeIdvPage(page);
    }
    await setup(page);
    await use(page);
  },
});
