import { mergeTests } from "@playwright/test";

import PortalEmployerIndexPage from "../../pages/portal/admin";
import PortalConsentToDataSharingPage from "../../pages/portal/user/consent-to-data-sharing";
import PortalContactInfoPage from "../../pages/portal/user/contact-info";
import { loggedInLeaveAdminPage } from "./leave-admin-login";

/*
  Logs in as a Leave Admin and navigates to the index page. 
  Index page is usual landing page after logging in. Occasionally there can be edge cases where another page is loaded. 
  Use loggedInLeaveAdminPage instead if working on one of those edge cases.

  Options:
    portalLeaveAdminCredentials?: specifies which credentials to user. Default credentials will be used not provided
    portalEmployerLoginByClaim?: will login as the leave admin attached to a claim submitted in the test. IMPORTANT: requires fineosClaimSubmissions to be provided
    fineosClaimSubmissions?: submits a claim. Has no effect on login if portalEmployerLoginByClaim is not provided.
*/

type Fixture = {
  portalEmployerIndexPage: PortalEmployerIndexPage;
};

export const portalEmployerIndexPage = mergeTests(
  loggedInLeaveAdminPage
).extend<Fixture>({
  portalEmployerIndexPage: async ({ loggedInLeaveAdminPage }, use) => {
    if (await PortalContactInfoPage.isOnPage(loggedInLeaveAdminPage)) {
      const contactInfo = await PortalContactInfoPage.get(
        loggedInLeaveAdminPage
      );
      use(await contactInfo.fillContactInfo());
    } else if (
      await PortalConsentToDataSharingPage.isOnPage(loggedInLeaveAdminPage)
    ) {
      const consentPage = await PortalConsentToDataSharingPage.get(
        loggedInLeaveAdminPage
      );
      consentPage.continueAgree();
    } else {
      use(await PortalEmployerIndexPage.get(loggedInLeaveAdminPage));
    }
  },
});
