import test from "@playwright/test";

import { getMmgLeaveAdminCredentials } from "../../../src/util/credentials";

/* 
  Option used to login as a specific Leave Admin.
*/
type Fixture = {
  portalLeaveAdminCredentials: { username: string; password: string };
};

export const portalLeaveAdminCredentials = test.extend<Fixture>({
  portalLeaveAdminCredentials: [
    getMmgLeaveAdminCredentials(),
    { option: true },
  ],
});
