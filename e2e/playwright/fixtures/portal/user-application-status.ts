import { mergeTests } from "@playwright/test";

import PortalUserApplicationStatusPage from "../../pages/portal/user/application-status";
import { fineosClaimSubmissions } from "../fineos/claim-submissions";
import { portalUserApplicationsPage } from "./user-applications";

/*
  Logs in as a claimant. Navigates to the status page of a specific claim.

  OPTIONS:
    scenarioIds
    portalClaimantCredentials?
*/

type Fixture = {
  portalUserApplicationStatusPage: PortalUserApplicationStatusPage;
};

export const portalUserApplicationStatusPage = mergeTests(
  portalUserApplicationsPage,
  fineosClaimSubmissions
).extend<Fixture>({
  portalUserApplicationStatusPage: async (
    { portalUserApplicationsPage, fineosClaimSubmissions },
    use
  ) => {
    // Use the portalUserApplicationsPage to navigate to the application status page
    const portalUserApplicationStatusPage =
      await portalUserApplicationsPage.navigateToApplicationStatus(
        Object.values(fineosClaimSubmissions)[0].submission.fineos_absence_id
      );
    await use(portalUserApplicationStatusPage);
  },
});
