import { expect, mergeTests, Page } from "@playwright/test";

import config from "../../../src/config";
import { getLeaveAdminCredentials } from "../../../src/util/credentials";
import PortalOAuthStartPage from "../../pages/portal/oauth-start";
import { setup } from "../../utils/portal";
import { fineosClaimSubmissions } from "./../fineos/claim-submissions";
import { portalEmployerLoginByClaim } from "./employer-login-by-claim";
import { portalLeaveAdminCredentials } from "./leave-admin-credentials";
import { portalPage } from "./page";

/*
  Logs in as a leave Admin (aka Employer) 

  OPTIONS:
    portalLeaveAdminCredentials?: specifies which credentials to user. Default credentials will be used not provided
    portalEmployerLoginByClaim?: will login as the leave admin attached to a claim submitted in the test. IMPORTANT: requires fineosClaimSubmissions to be provided
    fineosClaimSubmissions?: submits a claim. Has no effect on login if portalEmployerLoginByClaim is not provided.
*/

type Fixture = {
  loggedInLeaveAdminPage: Page;
};

export const loggedInLeaveAdminPage = mergeTests(
  portalLeaveAdminCredentials,
  portalPage,
  fineosClaimSubmissions,
  portalEmployerLoginByClaim
).extend<Fixture>({
  loggedInLeaveAdminPage: async (
    {
      portalLeaveAdminCredentials,
      portalPage,
      fineosClaimSubmissions,
      portalEmployerLoginByClaim,
    },
    use
  ) => {
    let credentials = portalLeaveAdminCredentials;
    if (portalEmployerLoginByClaim) {
      const employer =
        fineosClaimSubmissions[portalEmployerLoginByClaim].claim.employer;
      credentials = getLeaveAdminCredentials(employer);
    }
    await setup(portalPage);
    const oAuthStartPage = await PortalOAuthStartPage.goto(portalPage);
    const myMassGovPage = await oAuthStartPage.logInAsLeaveAdmin();
    await myMassGovPage.logIn(credentials);
    const portalUrl = config("PORTAL_BASEURL");
    expect(portalPage.url()).toContain(portalUrl);
    await use(portalPage);
  },
});
