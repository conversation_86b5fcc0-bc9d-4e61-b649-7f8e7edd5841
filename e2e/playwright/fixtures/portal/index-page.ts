import { mergeTests } from "@playwright/test";

import { PortalPage } from "../../pages/portal/user/page";
import { authorizeIdvPage } from "../../utils/idv";
import { setup } from "../../utils/portal";
import { portalAuthorizeIdv } from "./authorize-idv";
import { portalBrowserContext } from "./browser-context";

/* 
  Checks for portalAuthorizeIdv argument and sets up the authorizeIdvPage util if true.
*/

type Fixture = {
  portalIndexPage: PortalPage;
};

export const portalIndexPage = mergeTests(
  portalAuthorizeIdv,
  portalBrowserContext
).extend<Fixture>({
  portalIndexPage: async (
    { portalAuthorizeIdv, portalBrowserContext },
    use
  ) => {
    const page = await portalBrowserContext.newPage();
    if (portalAuthorizeIdv) {
      await authorizeIdvPage(page);
    }
    await setup(page);
    await use(await PortalPage.get(page));
  },
});
