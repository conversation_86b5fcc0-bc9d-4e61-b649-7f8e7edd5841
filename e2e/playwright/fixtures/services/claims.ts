import { mergeTests } from "@playwright/test";
import { Debugger } from "debug";

import {
  Scenarios,
  Credentials,
  EmployeeClaimGenResponse,
} from "../../../src/types";
import {
  dispatchPostSubmit,
  generateClaims,
} from "../../../src/util/claimGenerator";
import { assertValidClaim } from "../../../src/util/typeUtils";
import { wait } from "../../../src/util/wait";
import { DehydratedClaim } from "../../../src/generation/types";
import { ClaimGenerator } from "../../../src/generation/ClaimGenerator";
import { ClaimSubmission } from "../fineos/claim-submissions";
import { logger } from "../logger";
import { generateClaimForEmployeeWithoutClaims } from "../../../cypress/plugins/index";
import { getPortalSubmitter } from "../../../src/util/common";
import {
  getClaimantCredentials,
  getLeaveAdminCredentials,
} from "../../../src/util/credentials";
import * as os from "os";
import * as path from "path";
import * as fs from "fs";

type Fixture = {
  claimsService: Claims;
};

export enum SubmitMethod {
  PFML_API,
  FINEOS_INTAKE,
}

type ClaimOptions = {
  employeePoolFile?: string;
  employerPoolFile?: string;
  submitMethod?: SubmitMethod;
  credentials?: Credentials;
  modifyClaim?: (claim: DehydratedClaim) => Promise<DehydratedClaim>;
  findCleanEmployee?: boolean;
  logSubmissionToNewRelic?: boolean;
};

type ClaimsGenerationOptions = ClaimOptions & {
  scenarioID: Scenarios;
};

type ClaimsBulkGenerationOptions = ClaimOptions & {
  scenarioIDs: Scenarios[];
};

export const claimsService = mergeTests(logger).extend<Fixture>({
  claimsService: [
    async ({ logger }, use) => {
      await use(new Claims(logger));
    },
    { timeout: 250_000 },
  ],
});

class Claims {
  private readonly logger: Debugger;

  constructor(logger: Debugger) {
    this.logger = logger;
  }

  private async _generateAndSubmit(
    scenarioId: Scenarios,
    options: ClaimOptions,
    attempt: number = 0
  ): Promise<ClaimSubmission> {
    const {
      employeePoolFile,
      employerPoolFile,
      submitMethod,
      credentials,
      modifyClaim,
      findCleanEmployee = true,
      logSubmissionToNewRelic = false,
    } = options;

    this.logger.log("generating claim for scenarioId:", scenarioId);
    try {
      let claim: DehydratedClaim;

      if (findCleanEmployee) {
        // Generate claim for employee without existing claims
        const response: EmployeeClaimGenResponse =
          await generateClaimForEmployeeWithoutClaims({
            scenario: scenarioId,
            employeePoolFileName: employeePoolFile,
            employerPoolFileName: employerPoolFile,
          });
        claim = response.claim;
      } else {
        // Generate regular claim and dehydrate it
        const generatedClaim = (
          await generateClaims(scenarioId, 1, employeePoolFile)
        )[0];
        // Create a temporary directory for dehydration
        const tempDir = path.join(os.tmpdir(), `pfml-claim-${Date.now()}`);
        await fs.promises.mkdir(tempDir, { recursive: true });
        claim = await ClaimGenerator.dehydrate(generatedClaim, tempDir);
      }

      // Apply claim modification if provided
      if (modifyClaim) {
        claim = await modifyClaim(claim);
      }

      claim.metadata ??= {};
      claim.metadata.useFineosSubmitter =
        submitMethod === SubmitMethod.FINEOS_INTAKE;

      // Create a temporary directory for hydration (similar to Cypress crossPlatformTempDir)
      const hydrateTempDir = path.join(
        os.tmpdir(),
        `pfml-hydrate-${Date.now()}`
      );
      await fs.promises.mkdir(hydrateTempDir, { recursive: true });

      // Hydrate the claim before submission (like Cypress submitClaimToAPI does)
      const hydratedClaim = await ClaimGenerator.hydrate(claim, hydrateTempDir);

      // Use the portal submitter directly (like Cypress does)
      const submitter = getPortalSubmitter();
      const resolvedCredentials = credentials ?? getClaimantCredentials();

      this.logger.log("submitting claim:", scenarioId);
      const res = await submitter.submit(hydratedClaim, resolvedCredentials, {
        leaveAdminCredentials: getLeaveAdminCredentials(hydratedClaim.employer),
      });

      assertValidClaim(claim.claim);
      this.logger.log("claim submitted:", scenarioId);

      // Store submission data (equivalent to Cypress stash)
      const submission = {
        application_id: res.application_id,
        fineos_absence_id: res.fineos_absence_id,
        timestamp_from: Date.now(),
      };

      // Log to New Relic if requested (placeholder for now)
      if (logSubmissionToNewRelic) {
        this.logger.log("Logging submission to New Relic:", submission);
      }

      return { claim: hydratedClaim, submission: res };
    } catch (error) {
      this.logger.log(
        `Error generating or submitting claim for scenarioId ${scenarioId}:`,
        error
      );
      this.logger.log(
        `Delaying and then will retry claim generation and submission for scenarioId ${scenarioId}...`
      );
      await wait(5000);
      if (attempt < 3) {
        this.logger.log(
          `Retrying claim generation and submission for scenarioId ${scenarioId}...`
        );
        return this._generateAndSubmit(scenarioId, options, attempt + 1);
      } else {
        this.logger.log(
          `Failed to generate or submit claim for scenarioId ${scenarioId} after 3 attempts`
        );
        throw error;
      }
    }
  }

  private async postSubmit(
    scenarioId: Scenarios,
    claimSubmission: ClaimSubmission,
    attempt: number = 0
  ): Promise<void> {
    this.logger.log("dispatchPostSubmit:", scenarioId);
    try {
      await dispatchPostSubmit(
        claimSubmission.claim,
        claimSubmission.submission
      );
      this.logger.log("claim submission dispatched:", scenarioId);
    } catch (error) {
      this.logger.log(
        `Error dispatching post submit for scenarioId ${scenarioId}:`,
        error
      );
      this.logger.log(
        `Delaying and then will retry dispatchPostSubmit for scenarioId ${scenarioId}...`
      );
      await wait(30_000);
      if (attempt < 3) {
        this.logger.log(
          `Retrying claim dispatchPostSubmit for scenarioId ${scenarioId}...`
        );
        await this.postSubmit(scenarioId, claimSubmission, attempt + 1);
      } else {
        this.logger.log(
          `Failed to dispatch post submit for scenarioId ${scenarioId} after 3 attempts`
        );
        throw error;
      }
    }
  }

  // Prefer generateBulk to generate many claims
  async generateAndSubmit(options: ClaimsGenerationOptions) {
    const { scenarioID } = options;

    const submission = await this._generateAndSubmit(scenarioID, options);
    this.postSubmit(scenarioID as Scenarios, submission);

    this.logger.log("FINEOS Claim Submission:", submission);

    return submission;
  }

  // Currently we don't parallelize this, so it's no different then calling generate() in step.
  // However, this may change in future. When generating multiple claims prefer this method.
  async generateAndSubmitBulk(options: ClaimsBulkGenerationOptions) {
    const submissions: Record<Scenarios, ClaimSubmission> = {} as Record<
      Scenarios,
      ClaimSubmission
    >;

    const { scenarioIDs } = options;

    // TODO: Investigate how to parallelize this. We need to be careful and avoid any type of rate limiting imposed on us.
    for (const scenarioID of scenarioIDs) {
      submissions[scenarioID] = await this._generateAndSubmit(
        scenarioID,
        options
      );
    }

    for (const [scenarioID, claimSubmission] of Object.entries(submissions)) {
      this.postSubmit(scenarioID as Scenarios, claimSubmission);
    }

    this.logger.log("FINEOS Claim Submission:", submissions);

    return submissions;
  }
}
