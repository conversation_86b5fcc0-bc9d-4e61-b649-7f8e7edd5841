import test from "@playwright/test";
import debug, { Debugger } from "debug";

type Fixture = {
  logger: Debugger;
};

export const logger = test.extend<Fixture>({
  logger: [
    async ({}, use, testInfo) => {
      const logger = debug("app:logs");

      const logs: string[] = [];
      logger.log = (...args) =>
        logs.push(
          args
            .map((log: string | object) => {
              if (typeof log === "string") {
                return log;
              }
              return JSON.stringify(log, null, "\t");
            })
            .join("")
        );
      debug.enable(logger.namespace);

      await use(logger);

      if (!debug.enabled || logs.length === 0) {
        return;
      }

      testInfo.attach("logs", {
        contentType: "text/plain",
        body: logs.join("\n"),
      });
    },
    { box: true },
  ],
});
