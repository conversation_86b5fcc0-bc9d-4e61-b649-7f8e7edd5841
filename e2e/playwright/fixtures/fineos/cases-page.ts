import { mergeTests } from "@playwright/test";

import FineosCasesPage from "../../pages/fineos/cases";
import { loggedInFineosPage } from "./login";

/* 
  Logs into FINEOS and navigates to the Cases tab where you can then search for Cases.
*/

type Fixture = {
  fineosCasesPage: FineosCasesPage;
};
export const fineosCasesPage = mergeTests(loggedInFineosPage).extend<Fixture>({
  fineosCasesPage: async ({ loggedInFineosPage }, use) => {
    // Use the logged-in FINEOS page to navigate to cases
    const fineosCasesPage = await loggedInFineosPage.navigateToCases();
    // Return the cases page for further use in tests
    await use(fineosCasesPage);
  },
});
