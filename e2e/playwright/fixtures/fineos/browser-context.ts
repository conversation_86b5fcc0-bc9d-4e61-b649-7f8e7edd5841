import test, { BrowserContext, BrowserContextOptions } from "@playwright/test";

import config from "../../../src/config";
import { isSSO } from "../../utils/sso";

/*
  Creates a browser context for use in FINEOS fixtures 
*/

type Fixture = {
  fineosBrowserContext: BrowserContext;
};

const basicUsername = config("FINEOS_USERNAME");
const basicPassword = config("FINEOS_PASSWORD");

export const fineosBrowserContext = test.extend<Fixture>({
  fineosBrowserContext: async ({ browser }, use) => {
    const options: BrowserContextOptions = {};
    if (!isSSO) {
      // If not in SSO environment, set the basic auth for the context
      options.httpCredentials = {
        username: basicUsername,
        password: basicPassword,
      };
    }
    const context = await browser.newContext(options);

    await use(context);
    await context.close();
  },
});
