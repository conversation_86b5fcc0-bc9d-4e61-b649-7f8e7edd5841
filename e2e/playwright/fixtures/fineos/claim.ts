import { mergeTests } from "@playwright/test";

import FineosClaimPage from "../../pages/fineos/claim";
import { fineosCasesPage } from "./cases-page";
import { fineosClaimSubmissions } from "./claim-submissions";
/* 
  Submits a claim(s) via FINEOS API, Logs into FINEOS and navigates to that Claim's Page or the first Claim's page if multiple claims are submitted.
*/

type Fixture = {
  fineosClaimPage: FineosClaimPage;
};
export const fineosClaimPage = mergeTests(
  fineosCasesPage,
  fineosClaimSubmissions
).extend<Fixture>({
  fineosClaimPage: async ({ fineosCasesPage, fineosClaimSubmissions }, use) => {
    // Navigate to the specific case using the FINEOS absence ID
    const fineosClaimPage = await fineosCasesPage.gotoCase(
      Object.values(fineosClaimSubmissions)[0].submission.fineos_absence_id
    );
    // Return the claim page for further use in tests
    await use(fineosClaimPage);
  },
});
