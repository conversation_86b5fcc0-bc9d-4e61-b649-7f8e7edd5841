import { test } from "@playwright/test";

import { Scenarios } from "../../../src/types";

/*
  scenarioIds is the argument you need to pass into tests that use fixtures which are dependent on the fineosClaimSubmissions fixture.
  fineosClaimSubmissions submits 1 claim via the FINEOS api for each scenarioId provided. 

  Example: 
    test.use({scenarioIds: ["MED_INTER_INEL"]})  
*/

type Fixture = {
  scenarioIds: Scenarios[];
};

export const scenarioIds = test.extend<Fixture>({
  scenarioIds: [[], { option: true }],
});
