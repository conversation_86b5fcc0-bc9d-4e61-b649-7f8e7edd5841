import { mergeTests } from "@playwright/test";

import FineosTasksPage from "../../pages/fineos/tasks";
import { loggedInFineosPage } from "./login";

/* 
  Logs into FINEOS and navigates to the Tasks Page
*/

type Fixture = {
  fineosTasksPage: FineosTasksPage;
};

export const fineosTasksPage = mergeTests(loggedInFineosPage).extend<Fixture>({
  fineosTasksPage: async ({ loggedInFineosPage }, use) => {
    // Use the logged-in FINEOS page to navigate to tasks
    const fineosTasksPage = await loggedInFineosPage.navigateToTasks();
    // Return the tasks page for further use in tests
    await use(fineosTasksPage);
  },
});
