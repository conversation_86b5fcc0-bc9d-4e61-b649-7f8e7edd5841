import { mergeTests } from "@playwright/test";

import FineosDashboardPage from "../../pages/fineos";
import FineosLoginPage from "../../pages/fineos/login";
import { fineosPage } from "./page";

/*
  Logs into FINEOS via SSO for SSO enables environments or through the credentials 
  provided by the fineosBrowserContext fixture for non-SSO envs. 
  Credentials details can be found in config.json
*/

type Fixture = {
  loggedInFineosPage: FineosDashboardPage;
};
export const loggedInFineosPage = mergeTests(fineosPage).extend<Fixture>({
  loggedInFineosPage: async ({ fineosPage }, use) => {
    const fineosLoginPage = await FineosLoginPage.goto(fineosPage);
    const fineosDashboardPage = await fineosLoginPage.logIn();
    await use(fineosDashboardPage);
  },
});
