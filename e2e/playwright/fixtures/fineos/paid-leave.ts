import { mergeTests } from "@playwright/test";

import FineosPaidLeavePage from "../../pages/fineos/paid-leave";
import { fineosClaimPage } from "./claim";

/*
  Submits a claim(s) via FINEOS API, Logs into FINEOS and navigates to that Claim's Paid Leave Page or the first claim's Paid Leave page if multiple claims are submitted.

  OPTIONS:
  scenarioIds (for more information view claim-submissions.ts)
*/

type Fixture = {
  fineosPaidLeavePage: FineosPaidLeavePage;
};
export const fineosPaidLeavePage = mergeTests(fineosClaimPage).extend<Fixture>({
  fineosPaidLeavePage: async ({ fineosClaimPage }, use) => {
    // Navigate to the paid leave page from the claim page
    const fineosPaidLeavePage = await fineosClaimPage.gotoPaidLeave();
    // Return the paid leave page for further use in tests
    await use(fineosPaidLeavePage);
  },
});
