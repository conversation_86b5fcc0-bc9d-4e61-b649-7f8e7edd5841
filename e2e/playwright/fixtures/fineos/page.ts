import { mergeTests, Page } from "@playwright/test";

import { fineosBrowserContext } from "./browser-context";

/* 
  Loads the FINEOS login page and attaches the non-SSO credentials to the URL to be used when logging into non SSO envs.
*/

type Fixture = {
  fineosPage: Page;
};
export const fineosPage = mergeTests(fineosBrowserContext).extend<Fixture>({
  fineosPage: async ({ fineosBrowserContext }, use) => {
    const page = await fineosBrowserContext.newPage();
    await use(page);
  },
});
