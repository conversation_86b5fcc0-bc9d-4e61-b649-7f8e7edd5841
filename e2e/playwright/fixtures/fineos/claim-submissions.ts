import { mergeTests } from "@playwright/test";

import { GeneratedClaim } from "../../../src/generation/types";
import { ApplicationSubmissionResponse, Scenarios } from "../../../src/types";
import { claimsService } from "../services/claims";
import { scenarioIds } from "./scenario-ids";

/*
  The fixture fineosClaimSubmissions submits claims through the FINEOS API. As many tests
  require a claim to be generated, this fixture is a dependency for many other of our custom Playwright fixtures. It's worth noting that there are 
  other ways to submit a claim (eg. through the portal) so you should be intentional about which claim submission method/fixture you want to use.

  OPTIONS:
    scenarioIds (for more information view claim-submissions.ts)
*/
//

export interface ClaimSubmission {
  claim: GeneratedClaim;
  submission: ApplicationSubmissionResponse;
}

type Fixture = {
  fineosClaimSubmissions: Record<Scenarios, ClaimSubmission>;
};

export const fineosClaimSubmissions = mergeTests(
  scenarioIds,
  claimsService
).extend<Fixture>({
  fineosClaimSubmissions: async ({ claimsService, scenarioIds }, use) => {
    await use(
      await claimsService.generateAndSubmitBulk({
        scenarioIDs: scenarioIds,
      })
    );
  },
});
