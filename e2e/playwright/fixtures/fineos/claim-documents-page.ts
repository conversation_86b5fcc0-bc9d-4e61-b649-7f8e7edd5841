import { mergeTests } from "@playwright/test";

import FineosClaimDocumentsPage from "../../pages/fineos/claim-documents";
import { fineosClaimPage } from "./claim";

/*
  Submits a claim(s) via FINEOS API, Logs into FINEOS and navigates to that Claim's Document Page 
  or the first claim's Document page if multiple claims are submitted.

  OPTIONS:
  scenarioIds (for more information view claim-submissions.ts)
*/

type Fixture = {
  fineosClaimDocumentsPage: FineosClaimDocumentsPage;
};
export const fineosClaimDocumentsPage = mergeTests(
  fineosClaimPage
).extend<Fixture>({
  fineosClaimDocumentsPage: async ({ fineosClaimPage }, use) => {
    // Navigate to the claim documents page from the claim page
    const fineosClaimDocumentsPage = await fineosClaimPage.gotoDocuments();
    // Return the claim documents page for further use in tests
    await use(fineosClaimDocumentsPage);
  },
});
