import { mergeTests } from "@playwright/test";

import FineosClaimAppealPage from "../../pages/fineos/claim-appeal";
import { fineosClaimPage } from "./claim";

/*
  Submits a claim(s) via FINEOS API, Logs into FINEOS and navigates to that Claim's Appeal Page 
  or the first claim's Appeal page if multiple claims are submitted.

  OPTIONS:
  scenarioIds (for more information view claim-submissions.ts)
*/

type Fixture = {
  fineosClaimAppealPage: FineosClaimAppealPage;
};
export const fineosClaimAppealPage = mergeTests(
  fineosClaimPage
).extend<Fixture>({
  fineosClaimAppealPage: async ({ fineosClaimPage }, use) => {
    // Navigate to the appeal page from the claim page
    const fineosClaimAppealPage = await fineosClaimPage.addAppeal();
    // Return the appeal page for further use in tests
    await use(fineosClaimAppealPage);
  },
});
