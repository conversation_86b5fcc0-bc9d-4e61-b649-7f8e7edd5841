import config from "../../src/config";
import { test } from "../fixtures";
import PortalOAuthStartPage from "../pages/portal/oauth-start";

test.skip(
  !config("HAS_GOOGLE_ANALYTICS"),
  "Google Analytics is only enabled in some environments."
);

test(`Portal will make outgoing calls to Google Analytics`, async ({
  portalPage,
}) => {
  const gaRequest = portalPage.waitForRequest(
    config("GOOGLE_ANALYTICS_PATTERN")
  );
  await PortalOAuthStartPage.goto(portalPage);
  await gaRequest;
});
