import config from "../../src/config";
import { getMmgClaimantCredentials } from "../../src/util/credentials";
import { expect, test } from "../fixtures";
import {
  assertInProgressApplicationPage,
  assertIsIdvStartPage,
} from "../utils/application";

test.skip(
  !config("USE_MMG_SSO"),
  "MyMassGov SSO testing is only enabled in certain environments."
);

test.describe("Claimant MyMassGov navigation", () => {
  test.use({
    portalClaimantCredentials: [getMmgClaimantCredentials(), { scope: "test" }],
  });

  test("claimant can navigate from the PFML Portal Settings page to the MyMassGov Settings page, and navigate back after clicking cancel", async ({
    portalUserCreateApplicationPage,
  }) => {
    const userSettingsPage =
      await portalUserCreateApplicationPage.navigateToSettings();
    const mmgUserSettingsPage = await userSettingsPage.changePassword();
    const portalUserSettingsPage = await mmgUserSettingsPage.cancelChanges();
    const portalUrl = config("PORTAL_BASEURL");
    expect(portalUserSettingsPage.url()).toContain(portalUrl);
  });
});

test.describe("Claimant MyMassGov Identity Verfication", () => {
  test.skip(
    !config("HAS_PROFILE_IDV_FEATURE"),
    "MyMassGov Identity Verification testing is only enabled in certain environments."
  );

  test.use({
    // specifying this will set add credentials to the context when routing to idv pages
    portalAuthorizeIdv: [true, { scope: "test" }],
    // specifying this will set which credentials to use when creating the loggedInClaimantPage
    portalClaimantCredentials: [getMmgClaimantCredentials(), { scope: "test" }],
  });

  test("claimant can navigate from the PFML Portal to the Identity Verification page", async ({
    portalUserCreateApplicationPage,
  }) => {
    const startApplicationPage =
      await portalUserCreateApplicationPage.createApplication();
    const inProgressApplicationPage =
      await startApplicationPage.agreeApplication();
    assertInProgressApplicationPage(inProgressApplicationPage);
    const idvPage = await inProgressApplicationPage.startApplicationPartOne();
    assertIsIdvStartPage(idvPage);
    await idvPage.exitMmg();
    // await portalPage.waitForLoadState("networkidle");
    // await portalPage.getByRole("heading", { name: "Redirecting you to continue" });
  });
});

test.describe("Leave Admin MyMassGov navigation", () => {
  test("leave admin can navigate from the PFML Portal Settings page to the MyMassGov Settings page, and navigate back after clicking cancel", async ({
    portalEmployerIndexPage,
  }) => {
    const userSettingsPage = await portalEmployerIndexPage.navigateToSettings();
    const mmgUserSettingsPage = await userSettingsPage.changePassword();
    const portalUserSettingsPage = await mmgUserSettingsPage.cancelChanges();
    const portalUrl = config("PORTAL_BASEURL");
    expect(portalUserSettingsPage.url()).toContain(portalUrl);
  });
});
