import config from "../../src/config";
import { GeneratedClaim } from "../../src/generation/types";
import { ApplicationSubmissionResponse } from "../../src/types";
import { getClaimantTaxIdCredentials } from "../../src/util/credentials";
import { registerClaimant } from "../../src/util/myMassGov";
import { assertValidClaim } from "../../src/util/typeUtils";
import { expect, test } from "../fixtures";
import { SubmitMethod } from "../fixtures/services/claims";
import { Document } from "../pages/fineos/claim-documents";
import { submitApplicationPartThree } from "../utils/application";

test.skip(["trn2", "training"].includes(config("ENVIRONMENT")));

test.describe("Claimant Registration with Channel Switching", () => {
  test.describe.configure({ mode: "serial" });

  let claimSubmission!: {
    submission: ApplicationSubmissionResponse;
    claim: GeneratedClaim;
  };

  test("Call center representative submits a continuous bonding application through FINEOS intake", async ({
    claimsService,
  }) => {
    claimSubmission = await claimsService.generateAndSubmit({
      scenarioID: "BCAP90",
      submitMethod: SubmitMethod.FINEOS_INTAKE,
    });
  });

  test("Claimant resumes the application via the web portal", async ({
    portalIndexPage,
  }) => {
    assertValidClaim(claimSubmission.claim.claim);
    const { fineos_absence_id } = claimSubmission.submission;
    const { tax_identifier } = claimSubmission.claim.claim;

    await test.step("Register claimaint", async () => {
      await registerClaimant(getClaimantTaxIdCredentials(tax_identifier));
    });

    const loginPage = await test.step("Login as claimant", async () =>
      await portalIndexPage.logInAsClaimant(
        getClaimantTaxIdCredentials(tax_identifier)
      ));

    const createApplicationsPage =
      await test.step("Navigate to create applications page", async () => {
        return await loginPage.navigateToCreateApplicationPage();
      });

    const importClaimPage =
      await test.step("Begin the claim import process", async () => {
        return await createApplicationsPage.resumeIntakeApplication();
      });

    await test.step("Fill in tax identifier field", async () => {
      await importClaimPage.clearTaxIdentifier();
      await importClaimPage.fillTaxIdentifier(tax_identifier);
    });

    await test.step("Fill in absence case id field", async () => {
      await importClaimPage.clearAbsenceCaseId();
      await importClaimPage.fillAbsenceCaseId(fineos_absence_id);
    });

    const applicationsPage =
      await test.step("Press the submit button", async () => {
        return await importClaimPage.submitApplication();
      });

    const inProgress =
      await test.step("Continue the application from the applications list page", async () => {
        return await applicationsPage.continueApplication(fineos_absence_id);
      });

    await test.step("Submit parts 8 and 9 of application", async () => {
      await submitApplicationPartThree(inProgress, {
        needUploadId: true,
        skipCertUpload: false,
      });
    });
  });

  test("Call center representative checks for the uploaded documents in FINEOS", async ({
    fineosCasesPage,
  }) => {
    const { fineos_absence_id } = claimSubmission.submission;

    const casesPage =
      await test.step("Go to the FINEOS cases page", async () => {
        return await fineosCasesPage.gotoCase(fineos_absence_id);
      });

    const documentsPage =
      await test.step("Go to the FINEOS documents tab", async () => {
        return await casesPage.gotoDocuments();
      });

    const documents =
      await test.step("Get list of documents in table", async () => {
        return await documentsPage.getDocuments();
      });

    await test.step("Assert that the documents match", async () => {
      const EXPECTED_DOCUMENTS: Document[] = [
        {
          caseNumber: fineos_absence_id,
          caseType: "Absence Case",
          status: "Completed",
          documentType: "Child bonding evidence form",
        },
        {
          caseNumber: fineos_absence_id,
          caseType: "Absence Case",
          status: "Completed",
          documentType: "Identification Proof",
        },
      ];
      expect(documents).toEqual(expect.arrayContaining(EXPECTED_DOCUMENTS));
    });
  });
});
