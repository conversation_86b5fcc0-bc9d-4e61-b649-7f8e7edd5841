import { Page } from "@playwright/test";

import config from "../../src/config";
import {
  generateMmgAccountDetails,
  getMmgClaimantCredentials,
  getMmgLeaveAdminCredentials,
} from "../../src/util/credentials";
import { expect, test } from "../fixtures";
import PortalOAuthStartPage from "../pages/portal/oauth-start";

test.describe("MyMassGov Claimant Authentication", () => {
  test.skip(
    !config("USE_MMG_SSO"),
    "MyMassGov SSO testing is only enabled in certain environments."
  );

  const testCases = [
    {
      accountTypeText: "PERSONAL ACCOUNT",
      logInCredentials: getMmgClaimantCredentials(),
      logInMethod: "logInAsClaimant",
      user: "claimant",
    },
    {
      accountTypeText: "BUSINESS ACCOUNT",
      logInCredentials: getMmgLeaveAdminCredentials(),
      logInMethod: "logInAsLeaveAdmin",
      user: "leave admin",
    },
  ] as const;

  testCases.forEach(({ user, ...testCase }) => {
    test(`${user} can register through MyMassGov`, async ({ portalPage }) => {
      const { logInMethod, accountTypeText } = testCase;
      const myMassGovPage = await navigateToMyMassGov(portalPage, logInMethod);
      const accountType = portalPage.getByText(accountTypeText);
      await expect(accountType).toBeVisible();

      const { email, firstName, lastName, password } =
        generateMmgAccountDetails();
      const verifyEmailPage = await myMassGovPage.createAccount();
      const accountDetailsPage = await verifyEmailPage.verifyEmail(email);
      const setUpPasswordPage = await accountDetailsPage.enterName(
        firstName,
        lastName
      );
      await setUpPasswordPage.enterPassword(password);

      const successMessage = portalPage.getByRole("heading", {
        name: "New PFML account created",
      });
      await expect(successMessage).toBeVisible();
      const accountEmail = portalPage.locator("#page").getByText(email);
      await expect(accountEmail).toBeVisible();
    });

    test(`${user} can log in through MyMassGov`, async ({ portalPage }) => {
      const { logInMethod, logInCredentials } = testCase;
      const myMassGovPage = await navigateToMyMassGov(portalPage, logInMethod);
      await myMassGovPage.logIn(logInCredentials);

      const portalUrl = config("PORTAL_BASEURL");
      expect(portalPage.url()).toContain(portalUrl);
    });
  });

  /**
   * Facade that starts at the Portal entry point and navigates to the MyMassGov
   * index page.
   *
   * Useful for simplifying the testing of this workflow in authentication tests.
   * Other tests that need to go to MyMassGov but are not explicitly testing this
   * workflow would benefit from a more direct `goto` method.
   */
  async function navigateToMyMassGov(page: Page, logInMethod: LogInMethod) {
    const oAuthStartPage = await PortalOAuthStartPage.goto(page);
    return oAuthStartPage[logInMethod]();
  }

  type LogInMethod = (typeof testCases)[number]["logInMethod"];
});
