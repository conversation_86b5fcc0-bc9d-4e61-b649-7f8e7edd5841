import { format } from "date-fns";

import config from "../../src/config";
import InfraClient from "../../src/InfraClient";
import { PaymentAuditPaymentField } from "../../src/types";
import { test } from "../fixtures";

test.describe("Payments Integration", () => {
  // test.describe.configure({ retries: 2 });

  test.use({
    // specifying this scenario will generate a claim for the claimant to use in the test
    scenarioIds: [
      [
        // 'CCAP90ERSP',
        // 'MED_ERRE',
        // "BHAP1_OLB_ER",
        "PMTA",
        "PMTB",
        "PMTC",
        "PMTD",
        "PMTE",
        "PMTF",
        "PMTG",
        "PMTH",
        "PMTI",
        "PMTJ",
        "PMTK",
      ],
      { scope: "test" },
    ],
  });

  test("confirm payment", async ({
    fineosCasesPage,
    fineosClaimSubmissions: _,
  }) => {
    test.setTimeout(360_000);
    const infra = InfraClient.create(config);
    const yesterday = new Date();
    const dayOfWeek = format(yesterday, "EEEE");
    const isMonday = dayOfWeek === "Monday";

    // If today is Monday, get Friday's report
    if (isMonday) {
      yesterday.setDate(yesterday.getDate() - 3);
    } else {
      yesterday.setDate(yesterday.getDate() - 1);
    }

    const paymentAudit = await infra.getPaymentAuditFile(yesterday);

    for (const payment of paymentAudit.values()) {
      // Formats any payment amount over 1000 with a comma
      for (const paymentField of paymentFields) {
        const paymentAmount = payment[paymentField];
        if (Number(paymentAmount) >= 1000) {
          payment[paymentField] = paymentAmount.replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ","
          );
        }
      }

      const caseNumber = payment["Absence Case Number"];
      const paidCasePage = await fineosCasesPage.gotoCase(caseNumber);
      const paidLeaveOptions = {
        startDate: payment["Payment Period Start"],
        endDate: payment["Payment Period End"],
      };
      const paidLeavePage = await paidCasePage.gotoPaidLeave(paidLeaveOptions);
      const fullName = payment["First Name"] + " " + payment["Last Name"];
      await paidLeavePage.verifyName(fullName);
      const paymentOptions = {
        preApproval: payment["Is Pre-approved"],
        sitAmount: payment["State Withholding Amount"],
        fitAmount: payment["Federal Withholding Amount"],
        duaIncome: payment["DUA Additional Income"],
        diaIncome: payment["DIA Additional Income"],
        overPayment: payment["Outstanding Overpayments"],
        waitingWeek: payment["Waiting Week"],
      };

      await paidLeavePage.verifyPaymentInformation(
        payment["Payment Amount"],
        paymentOptions
      );
      await paidLeavePage.returnToCases();
    }
  });

  const paymentFields = [
    "Payment Amount",
    "Gross Payment Amount",
    "Federal Withholding Amount",
    "State Withholding Amount",
    "DUA Additional Income",
    "DIA Additional Income",
  ] as PaymentAuditPaymentField[];
});
