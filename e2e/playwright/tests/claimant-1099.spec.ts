import { getMmgClaimantCredentials } from "../../src/util/credentials";
import { test } from "../fixtures";

test.describe("Claimant 1099s", () => {
  test.use({
    // specifying this will set which credentials to use when creating the loggedInClaimantPage
    portalClaimantCredentials: [getMmgClaimantCredentials(), { scope: "test" }],
  });

  test("should not display 1099s for an account with no claims", async ({
    portalUserCreateApplicationPage,
  }) => {
    const portalUserTaxDocumentsPage =
      await portalUserCreateApplicationPage.navigateToTaxDocuments();
    await portalUserTaxDocumentsPage.expectNoTaxDocumentsAvailable();
  });
});

test("should not display tax documents for an account for multiple users", async ({
  portalUserCreateApplicationPage,
}) => {
  await portalUserCreateApplicationPage.expectTaxDocumentsNavigationHidden();
});
