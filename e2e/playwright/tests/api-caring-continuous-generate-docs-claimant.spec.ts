import { expect, test } from "../fixtures";

test.describe("Care for Family Member Generate Documents - Claimant", () => {
  test.use({
    // specifying this scenario will generate a claim for the claimant to use in the test
    scenarioIds: [["CCAP90ERSP"], { scope: "test" }],
  });

  test.beforeEach(async ({ fineosClaimDocumentsPage }) => {
    await fineosClaimDocumentsPage.generateDocument("EFT Change Request");
  });

  test("checks portal for EFT Change Request notice and asserts for Spanish", async ({
    portalUserApplicationStatusPage,
  }) => {
    const notices = await portalUserApplicationStatusPage.getNotices();
    expect(notices).toContain("EFT Change Request (PDF)");
    await portalUserApplicationStatusPage.downloadNoticeAndAssertContent(
      "EFT Change Request (PDF)",
      "Formulario EFT"
    );
  });
});
