import { parse } from "csv-parse/sync";
import fs from "fs";

import config from "../../src/config";
import { getLeaveAdminCsvCredentials } from "../../src/util/credentials";
import { expect, test } from "../fixtures";

test.describe("Leave Admin CSV Download", () => {
  test.skip(
    !config("USE_MMG_SSO"),
    "MyMassGov SSO testing is only enabled in certain environments."
  );
  test.use({
    portalLeaveAdminCredentials: [
      getLeaveAdminCsvCredentials(),
      { scope: "test" },
    ],
  });

  test("Leave admin can download CSV file which has appropriate headers", async ({
    portalEmployerIndexPage,
  }) => {
    const applicationsPage =
      await portalEmployerIndexPage.navigateToApplications();
    const csvPath = await applicationsPage.downloadCsv();
    const csv = await parseLeaveAdminFile(csvPath);
    const csvColumns = Object.keys(csv[0]);
    expect(csvColumns.length).toBe(LeaveAdminCsvColumnNames.length);
    for (const columnNumber in csvColumns) {
      expect(csvColumns[columnNumber]).toBe(
        LeaveAdminCsvColumnNames[columnNumber]
      );
    }
  });

  test("Testing failed CSV download", async ({ portalEmployerIndexPage }) => {
    const applicationsPage =
      await portalEmployerIndexPage.navigateToApplications();
    await applicationsPage.triggerFailedDownload();
    await applicationsPage.expectFailedDownload();
  });
});

async function parseLeaveAdminFile(downloadPath: string) {
  const fileBuffer = fs.readFileSync(downloadPath);
  const csvContent = fileBuffer.toString();
  const parsedCsv = parse(csvContent, {
    columns: true,
    skip_empty_lines: true,
  });
  return parsedCsv;
}

const LeaveAdminCsvColumnNames = [
  "Application ID",
  "First name",
  "Last name",
  "Date of birth",
  "SSN or ITIN",
  "Organization",
  "EIN",
  "Department",
  "Leave type",
  "Leave start date",
  "Leave end date",
  "Leave frequency",
  "Application started",
  "Application completed",
  "Employer review due date",
  "Employer review completed by",
  "Employer review completion date",
  "Application status",
  "DFML decision expected date",
  "Weekly benefit amount",
  "IAWW",
] as const;
