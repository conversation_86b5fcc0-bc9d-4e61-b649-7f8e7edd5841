import { devices } from "@playwright/test";

import config from "../../src/config";
import {
  getClaimCompletedAndApprovedQuery,
  getClaimWithIntermittentLeaveHoursQuery,
  queryDb,
} from "../../src/util/queries";
import { test } from "../fixtures";
import PortalUserApplicationsPage from "../pages/portal/user/applications";

test.describe("On desktop, check side navigation bar is available when navigating through claims", () => {
  test("As a claimant, I should be able to navigate to claim and payment status when viewing a completed application", async ({
    portalUserApplicationsPage,
  }) => {
    await testClaimNavigation(
      getClaimCompletedAndApprovedQuery,
      portalUserApplicationsPage
    );
  });

  test("As a claimant, I should be able to navigate to claim and payment status when viewing an application with intermittent leave", async ({
    portalUserApplicationsPage,
  }) => {
    await testClaimNavigation(
      getClaimWithIntermittentLeaveHoursQuery,
      portalUserApplicationsPage
    );
  });
});

test.describe("On mobile, check side navigation bar is available when navigating through claims", () => {
  test.use({
    isMobile: true,
    viewport: devices["iPhone 6"].viewport,
  });

  test("As a claimant on mobile, I should be able to navigate to claim and payment status when viewing a completed application", async ({
    portalUserApplicationsPage,
    isMobile,
  }) => {
    await testClaimNavigation(
      getClaimCompletedAndApprovedQuery,
      portalUserApplicationsPage,
      isMobile
    );
  });

  test("As a claimant on mobile, I should be able to navigate to claim and payment status when viewing an application with intermittent leave", async ({
    portalUserApplicationsPage,
    isMobile,
  }) => {
    await testClaimNavigation(
      getClaimWithIntermittentLeaveHoursQuery,
      portalUserApplicationsPage,
      isMobile
    );
  });
});

async function testClaimNavigation(
  queryFunction: (email: string) => string,
  portalUserApplicationsPage: PortalUserApplicationsPage,
  isMobile = false
) {
  const emailAddress = config("PORTAL_USERNAME");
  const query = queryFunction(emailAddress);
  const result = await queryDb(query);
  const statusPage =
    await portalUserApplicationsPage.navigateToApplicationStatus(
      result.fineos_absence_id
    );

  const paymentsPage = await statusPage.sideNavigation.navigateToPayments();
  const backToStatusPage =
    await paymentsPage.sideNavigation.navigateToApplication(
      result.fineos_absence_id
    );

  if (queryFunction === getClaimWithIntermittentLeaveHoursQuery) {
    await backToStatusPage.sideNavigation.navigateToLeaveHours();
  }

  if (isMobile) {
    await statusPage.toggleMobileMenu(true);
    await statusPage.toggleMobileMenu(false);
  }
}
