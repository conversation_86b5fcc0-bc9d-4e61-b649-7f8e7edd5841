import { expect, test } from "../fixtures";
import { ROLES } from "../utils/roles";

test.describe("Choose roles for tasks", () => {
  test(`Allow a user to choose a role`, async ({ fineosTasksPage }) => {
    const chooseRolePage = await fineosTasksPage.navigateToChooseRole();
    const roles = await chooseRolePage.getActiveRoles();
    expect(roles).toEqual(expect.arrayContaining(ROLES));
  });
});
