import { expect, Page } from "@playwright/test";

import { FineosDocumentType } from "../../../src/types";
import PageObjectModel from "../PageObjectModel";
import FineosClaimAddDocumentPage from "./claim-add-document";

export interface Document {
  caseNumber: string | null;
  caseType: string | null;
  status: string | null;
  documentType: string | null;
}

export default class FineosClaimDocumentsPage extends PageObjectModel {
  static async get(page: Page) {
    const heading = page.getByRole("heading", { name: "Documents For Case" });
    await expect(heading).toBeVisible();
    return new FineosClaimDocumentsPage(page);
  }

  async getDocuments() {
    const documents: Document[] = [];

    const table = this.page.locator(
      `table[id^="DocumentsForCaseListviewWidget"]`
    );
    await expect(table).toBeVisible();

    const rows = table.locator('[class^="ListRow"]');

    for (const li of await rows.all()) {
      documents.push({
        caseNumber: await li.locator("td").nth(3).textContent(),
        caseType: await li.locator("td").nth(4).textContent(),
        status: await li.locator("td").nth(7).textContent(),
        documentType: await li.locator("td").nth(8).textContent(),
      });
    }

    return documents;
  }

  async generateDocument(documentType: FineosDocumentType) {
    await this.addDocumentButton.click();
    const fineosClaimAddDocumentPage = await FineosClaimAddDocumentPage.get(
      this.page
    );
    await fineosClaimAddDocumentPage.addDocument(documentType);
    await expect(
      this.page.getByRole("link", { name: documentType })
    ).toBeVisible();
  }

  private readonly addDocumentButton = this.page.getByRole("button", {
    name: "Add",
  });
}
