import { expect, Page } from "@playwright/test";

import PageObjectModel from "../PageObjectModel";

export default class FineosChooseRolePage extends PageObjectModel {
  static async get(page: Page) {
    const chooseRolePageTitle = page.getByTitle("Choose Role and Work Subset");
    await expect(chooseRolePageTitle).toBeVisible();
    return new FineosChooseRolePage(page);
  }

  // Returns a list of active roles enumerated in the table widget
  async getActiveRoles() {
    const roles = await this.page
      .locator(`[id^="treenode_DepartmentRoleTreeView_0_"]`)
      .locator("visible=true")
      .allTextContents();
    return roles.map((text) => text.trim());
  }
}
