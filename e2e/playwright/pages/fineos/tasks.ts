import { expect, Page } from "@playwright/test";

import PageObjectModel from "../PageObjectModel";
import FineosChooseRolePage from "./choose-role";

export default class FineosTasksPage extends PageObjectModel {
  static async get(page: Page) {
    await page.waitForLoadState("domcontentloaded");
    const heading = page.getByRole("heading", { name: "Tasks" });
    await expect(heading).toBeVisible();
    return new FineosTasksPage(page);
  }

  async navigateToChooseRole() {
    await this.chooseRoleLink.click();
    return FineosChooseRolePage.get(this.page);
  }

  private readonly chooseRoleLink = this.page.getByRole("link", {
    name: "Choose Role",
  });
}
