import { expect, Page } from "@playwright/test";

import PageObjectModel from "../PageObjectModel";
import FineosCasePage from "./case";
import FineosCasesPage from "./cases";
import FineosTasksPage from "./tasks";

export default class FineosDashboardPage extends PageObjectModel {
  static async get(page: Page) {
    const heading = page.getByRole("heading", { name: "Dashboard" });
    await expect(heading).toBeVisible();
    return new FineosDashboardPage(page);
  }

  async navigateToCases() {
    await this.casesLink.click();
    return await FineosCasesPage.get(this.page);
  }

  async navigateToTasks() {
    await this.tasksLink.click();
    return await FineosTasksPage.get(this.page);
  }

  async searchForApplication(applicationID: string) {
    this.page
      .getByRole("combobox", { name: "Universal Search" })
      .fill(applicationID);
    await this.page.waitForLoadState("networkidle");
    await this.page
      .locator("#universal-search_listbox div")
      .filter({ hasText: `Absence Case - ${applicationID}` })
      .nth(1)
      .click();
    return FineosCasePage.get(this.page);
  }

  private readonly casesLink = this.page.getByRole("link", {
    name: "Cases",
    exact: true,
  });

  private readonly tasksLink = this.page.getByRole("link", { name: "Tasks" });
}
