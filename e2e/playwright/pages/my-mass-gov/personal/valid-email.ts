import { expect, Page } from "@playwright/test";

import config from "../../../../src/config";
import PageObjectModel from "../../PageObjectModel";
import PortalUserSettingsPage from "../../portal/settings";

const url = `${config("PORTAL_BASEURL")}/user/settings/?action=change_email`;

export default class MmgValidEmailPage extends PageObjectModel {
  static async get(page: Page) {
    await expect(page.getByText("New email address:")).toBeVisible();
    return new MmgValidEmailPage(page);
  }

  static async isOnPage(page: Page) {
    return page.url() === url;
  }

  async continue() {
    await this.continueButton.click();
    return PortalUserSettingsPage.get(this.page);
  }

  private readonly continueButton = this.page.getByRole("button", {
    name: "Continue",
  });
}
