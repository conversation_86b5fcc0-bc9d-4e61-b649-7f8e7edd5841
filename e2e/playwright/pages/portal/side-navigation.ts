import { Page } from "@playwright/test";

import { expect } from "../../fixtures";
import PageObjectModel from "../PageObjectModel";
import PortalUserApplicationStatusPage from "./user/application-status";
import PortalUserApplicationStatusLeaveHoursPage from "./user/leave-hours-status";
import PortalUserApplicationStatusPaymentsPage from "./user/payments-status";

export class SideNavigation extends PageObjectModel {
  static async get(page: Page) {
    await expect(page.locator("nav ul.usa-sidenav")).toBeVisible();
    return new SideNavigation(page);
  }

  async navigateToApplication(abscenceId: string) {
    await this.applicationLink.click();
    return await PortalUserApplicationStatusPage.get(this.page, abscenceId);
  }

  async navigateToPayments() {
    await this.paymentLink.click();
    return await PortalUserApplicationStatusPaymentsPage.get(this.page);
  }

  async navigateToLeaveHours() {
    await this.leaveHoursLink.click();
    return await PortalUserApplicationStatusLeaveHoursPage.get(this.page);
  }

  private readonly paymentLink = this.page.getByRole("link", {
    name: "Payments",
    exact: true,
  });

  private readonly applicationLink = this.page.getByRole("link", {
    name: "Application",
    exact: true,
  });

  private readonly leaveHoursLink = this.page.getByRole("link", {
    name: "View and report your leave hours",
  });
}
