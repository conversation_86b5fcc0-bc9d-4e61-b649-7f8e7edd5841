import { expect, Page } from "@playwright/test";

import config from "../../../src/config";
import { waitForAccountLoad } from "../../utils/account";
import MmgPersonalSettingsPage from "../my-mass-gov/personal/settings";
import MmgPersonalNewVerifyEmailPage from "../my-mass-gov/personal/verify-new-email";
import PageObjectModel from "../PageObjectModel";

const url = `${config("PORTAL_BASEURL")}/user/settings/`;

export default class PortalUserSettingsPage extends PageObjectModel {
  static async get(page: Page) {
    const settingsPageTitle = page.getByRole("heading", {
      name: "Settings",
      exact: true,
    });

    await expect(settingsPageTitle).toBeVisible();
    return new PortalUserSettingsPage(page);
  }

  static async goto(page: Page) {
    await page.goto(url);
    return new PortalUserSettingsPage(page);
  }

  async changePassword() {
    await this.changePasswordLink.click();
    await waitForAccountLoad(this.page);
    return MmgPersonalSettingsPage.get(this.page);
  }

  async editEmail() {
    await this.editEmailLink.click();
    await waitForAccountLoad(this.page);
    return MmgPersonalNewVerifyEmailPage.get(this.page);
  }

  async getEmail() {
    // If you can find a better way to find email please replace this!
    const emailHeader = this.page.getByRole("heading", {
      level: 3,
      name: "Email address",
      exact: true,
    });
    const parent = this.page
      .locator("#page .grid-row")
      .filter({ has: emailHeader });
    const emailText = parent.locator("+ div var");
    await expect(emailText).toBeVisible();
    return await emailText.textContent();
  }

  private readonly changePasswordLink =
    this.page.getByTestId("edit-password-link");

  private readonly editEmailLink = this.page.getByTestId("edit-email-link");
}
