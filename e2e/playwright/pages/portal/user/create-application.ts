import { expect, Page } from "@playwright/test";

import config from "../../../../src/config";
import PageObjectModel from "../../PageObjectModel";
import PortalUserSettingsPage from "../settings";
import PortalUserApplicationAgreementPage from "./application-agreement";
import PortalUserApplicationsPage from "./applications";
import PortalImportClaimPage from "./import-claim";
import PortalUserTaxDocumentsPage from "./tax-documents";

const url = `${config("PORTAL_BASEURL")}/applications/get-ready/`;

export default class PortalUserCreateApplicationPage extends PageObjectModel {
  private constructor(page: Page) {
    super(page);
  }

  static async get(page: Page) {
    await page.waitForURL(url);
    return new PortalUserCreateApplicationPage(page);
  }

  static async isOnPage(page: Page) {
    return page.url() === url;
  }

  async navigateToApplicationsPage() {
    await this.page
      .getByRole("link", { name: "View all applications" })
      .click();

    return await PortalUserApplicationsPage.get(this.page);
  }

  async navigateToSettings() {
    await this.settingsLink.click();
    return PortalUserSettingsPage.get(this.page);
  }

  async navigateToTaxDocuments() {
    await this.taxDocumentsLink.click();
    return await PortalUserTaxDocumentsPage.get(this.page);
  }

  async expectTaxDocumentsNavigationHidden() {
    await expect(this.taxDocumentsLink).toBeHidden();
  }

  async createApplication() {
    await this.createApplicationButton.click();
    return PortalUserApplicationAgreementPage.get(this.page);
  }

  // Navigate to a page which will allow you to import a claim submitted through FINEOS intake
  async resumeIntakeApplication() {
    await this.page.getByText("Did you start an application by phone?").click();
    await this.page
      .getByRole("link", { name: "Add an existing application" })
      .click();

    return await PortalImportClaimPage.get(this.page);
  }

  private readonly settingsLink = this.page.getByRole("link", {
    name: "Settings",
  });

  private readonly createApplicationButton = this.page.getByRole("link", {
    name: "Create an application",
  });

  private readonly taxDocumentsLink = this.page.getByRole("link", {
    name: "Tax Documents",
  });
}
