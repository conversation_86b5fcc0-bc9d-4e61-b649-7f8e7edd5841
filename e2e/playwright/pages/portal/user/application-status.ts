import { expect, Page } from "@playwright/test";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";
import pdf from "pdf-parse";

import config from "../../../../src/config";
import PageObjectModel from "../../PageObjectModel";
import { SideNavigation } from "../side-navigation";

const downloadsFolder = path.join(os.tmpdir(), "playwright_downloads");

// PFMLPB-17392: Removed the following notice types:
// "Overpayment Payoff Notice (PDF)"
// "Payment Received-Updated Overpayment Balance (PDF)"
export type NoticeType =
  | "Approval notice (PDF)"
  | "Appeal Acknowledgment (PDF)"
  | "Benefit Amount Change Notice (PDF)"
  | "Maximum Weekly Benefit Change Notice (PDF)"
  | "Leave Allotment Change Notice (PDF)"
  | "Request for More Information (PDF)"
  | "Overpayment Notice (PDF)"
  | "Overpayment Notice - Full Balance Demand (PDF)"
  | "Overpayment Notice - Full Balance Recovery (PDF)"
  | "Overpayment Notice - Partial Recovery and Remaining Balance (PDF)"
  | "Appeal Dismissed - Exempt Employer (PDF)"
  | "Appeal Dismissed - Other (PDF)"
  | "Modify Decision (PDF)"
  | "Appeal Hearing Virtual Fillable (PDF)"
  | "Appeal - Returned To Adjudication (PDF)"
  | "Appeal Withdrawn (PDF)"
  | "Denial Notice Explanation of Wages (PDF)"
  | "Explanation of Wages (PDF)"
  | "Pending Application Withdrawn (PDF)"
  | "Denial Notice (PDF)"
  | "Approval of Application Change (PDF)"
  | "Denial of Application Change (PDF)"
  | "Approved Leave Dates Cancelled (PDF)"
  | "Denial of Application (PDF)"
  | "Overpayment Payment Received (PDF)"
  | "Overpayment Paid in Full (PDF)"
  | "EFT Change Request (PDF)"
  | "Dismissal for Failure to Attend Hearing (PDF)"
  // Below notices are new in FR24-4.
  | "Appeal Postponement Agency (PDF)"
  | "Appeal Postponement Approved (PDF)"
  | "Appeal Postponement Denied (PDF)"
  | "Appeal Reinstatement Denied (PDF)"
  | "Appeal Reinstatement Granted (PDF)"
  | "Notice of Default (PDF)"
  | "Child Support (PDF)";

export default class PortalUserApplicationStatusPage extends PageObjectModel {
  readonly sideNavigation: SideNavigation;

  private constructor(page: Page) {
    super(page);
    this.sideNavigation = new SideNavigation(page);
  }

  static async get(page: Page, abscenceId: string) {
    await expect(page.getByText(`Application ID: ${abscenceId}`)).toBeVisible();
    return new PortalUserApplicationStatusPage(page);
  }

  static async goto(page: Page, abscenceId: string) {
    const url = config("PORTAL_BASEURL");
    await page.goto(`${url}/applications/status/?absence_id=${abscenceId}`);
    return await PortalUserApplicationStatusPage.get(page, abscenceId);
  }

  static async isOnPage(page: Page) {
    return page.url().includes(url);
  }

  async getNotices() {
    return await this.page
      .getByTestId("view-your-notices")
      .getByRole("listitem")
      .getByRole("button")
      .allTextContents();
  }

  async downloadNoticeAndAssertContent(
    noticeType: NoticeType,
    expectedContent: string
  ) {
    const downloadedNotice = path.join(downloadsFolder, `${noticeType}.pdf`);
    // Start waiting for download before clicking. Note no await.
    const downloadPromise = this.page.waitForEvent("download");
    await this.page.getByRole("button", { name: noticeType }).click();
    const download = await downloadPromise;

    // Wait for the download process to complete and save the downloaded file somewhere.
    await download.saveAs(downloadedNotice);
    const parsedPdf = await pdf(fs.readFileSync(downloadedNotice));
    return expect(parsedPdf.text).toContain(expectedContent);
  }

  async toggleMobileMenu(expanded: boolean) {
    const menuButton = this.page.getByRole("button", { name: "Menu" });
    await expect(menuButton).toBeVisible();
    const currentState = await menuButton.getAttribute("aria-expanded");
    if (currentState !== expanded.toString()) {
      await menuButton.click();
    }
    await expect(menuButton).toHaveAttribute(
      "aria-expanded",
      expanded.toString()
    );
  }
}

const url = `${config("PORTAL_BASEURL")}/applications/status`;
