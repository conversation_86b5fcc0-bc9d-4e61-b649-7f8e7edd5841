import { Page } from "@playwright/test";

import { UnhandledNavigation } from "../../../utils/errors";
import PageObjectModel from "../../PageObjectModel";
import PortalUserApplicationsPage from "./applications";
import PortalConsentToDataSharingPage from "./consent-to-data-sharing";
import PortalUserCreateApplicationPage from "./create-application";

export class LoggedInClaimantPage extends PageObjectModel {
  static async get(page: Page) {
    return new LoggedInClaimantPage(page);
  }

  async navigateToApplicationsPage() {
    if (await PortalUserCreateApplicationPage.isOnPage(this.page)) {
      throw new UnhandledNavigation({
        from: this.page,
        msg: "Can not navigate to applications when claimant has no claims",
      });
    }
    return await PortalUserApplicationsPage.get(this.page);
  }

  async navigateToCreateApplicationPage() {
    if (await PortalConsentToDataSharingPage.isOnPage(this.page)) {
      const consentPage = await PortalConsentToDataSharingPage.get(this.page);
      await consentPage.continueAgree();
      return await PortalUserCreateApplicationPage.get(this.page);
    } else if (await PortalUserCreateApplicationPage.isOnPage(this.page)) {
      const portalUserCreateApplicationPage =
        await PortalUserCreateApplicationPage.get(this.page);
      return portalUserCreateApplicationPage;
    } else {
      const portalUserApplicationsPage = await PortalUserApplicationsPage.get(
        this.page
      );
      return await portalUserApplicationsPage.startNewApplication();
    }
  }
}
