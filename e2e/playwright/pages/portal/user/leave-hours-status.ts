import { expect, Page } from "@playwright/test";

import config from "../../../../src/config";
import PageObjectModel from "../../PageObjectModel";
import { SideNavigation } from "../side-navigation";

export default class PortalUserApplicationStatusLeaveHoursPage extends PageObjectModel {
  readonly sideNavigation: SideNavigation;

  private constructor(page: Page) {
    super(page);
    this.sideNavigation = new SideNavigation(page);
  }

  static async get(page: Page) {
    const heading = page.getByRole("heading", {
      name: "View and report leave hours",
    });
    const submitHoursButton = page.getByRole("button", {
      name: "Submit hours",
    });

    await expect(heading).toBeVisible();
    await expect(submitHoursButton).toBeVisible();

    return new PortalUserApplicationStatusLeaveHoursPage(page);
  }

  static async isOnPage(page: Page) {
    return page.url() === url;
  }
}

const url = `${config("PORTAL_BASEURL")}/application/status/report-leave`;
