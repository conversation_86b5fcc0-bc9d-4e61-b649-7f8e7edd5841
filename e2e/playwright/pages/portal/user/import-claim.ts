import { expect, Page } from "@playwright/test";

import config from "../../../../src/config";
import PageObjectModel from "../../PageObjectModel";
import PortalUserApplicationsPage from "./applications";

const url = `${config("PORTAL_BASEURL")}/applications/import-claim/`;

export default class PortalImportClaimPage extends PageObjectModel {
  static async get(page: Page) {
    await page.waitForURL(url);
    await expect(page.locator("form.usa-form")).toBeVisible();
    return new PortalImportClaimPage(page);
  }

  async clearTaxIdentifier() {
    await this.taxIdentifier.clear();
  }

  async fillTaxIdentifier(text: string) {
    await this.taxIdentifier.fill(text);
  }

  async clearAbsenceCaseId() {
    await this.absenceCaseId.clear();
  }

  async fillAbsenceCaseId(id: string) {
    await this.absenceCaseId.fill(id);
  }

  async submitApplication() {
    await this.submit.click();
    const absence_id = await this.absenceCaseId.inputValue();
    const applicationsPage = await PortalUserApplicationsPage.get(this.page, {
      applicationAssociated: absence_id,
    });

    await expect(
      this.page.getByText(
        `Application ${absence_id} has been added to your account`
      )
    ).toBeVisible({ timeout: 20_000 });

    return applicationsPage;
  }

  private readonly taxIdentifier = this.page.locator(
    "input[name=tax_identifier]"
  );

  private readonly absenceCaseId = this.page.locator(
    "input[name=absence_case_id]"
  );

  private readonly submit = this.page.locator("button[type=submit]");
}
