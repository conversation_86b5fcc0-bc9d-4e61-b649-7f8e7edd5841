import { expect, Page } from "@playwright/test";

import config from "../../../../../src/config";
import PageObjectModel from "../../../PageObjectModel";
import PortalApplicationReviewPage from "./part-five/review";
import PortalApplicationPreviousLeavePage from "./part-four/previous-leaves";
import PortalApplicationGenderPage from "./part-one/gender";
import PortalApplicationNamePage from "./part-one/name";
import MmgIdvStartPage from "./part-one/step-one/idv/idv-start";
import PortalApplicationPaymentMethodPage from "./part-six/payment-method";
import PortalApplicationReviewAndSubmitPage from "./part-six/review-and-submit";
import PortalApplicationTaxWitholdingPage from "./part-six/tax-witholding";
import PortalApplicationUploadIdPage from "./part-six/upload-id";
import PortalApplicationUploadLeavePage from "./part-six/upload-leave";
import PortalApplicationLeaveReasonPage from "./part-three/leave-reason";
import PortalApplicationEinPage from "./part-two/ein";

const hasProfileIDVFeature = config("HAS_PROFILE_IDV_FEATURE");
export default class PortalInProgressApplicationPage extends PageObjectModel {
  private constructor(page: Page) {
    super(page);
  }

  static async get(page: Page) {
    const applicationHeading = page.getByRole("heading", {
      name: "Your in-progress application",
    });
    await expect(applicationHeading).toBeVisible({ timeout: 20_000 });
    return new PortalInProgressApplicationPage(page);
  }

  async startApplicationPartOne() {
    await this.startPartOneButton.click();
    if (hasProfileIDVFeature) {
      return MmgIdvStartPage.get(this.page);
    } else {
      return PortalApplicationNamePage.get(this.page);
    }
  }

  async startApplicationPartTwo() {
    await this.startPartTwoButton.click();
    return PortalApplicationEinPage.get(this.page);
  }

  async startApplicationPartThree() {
    await this.startPartThreeButton.click();
    return PortalApplicationLeaveReasonPage.get(this.page);
  }

  async startApplicationPartFour() {
    await this.startPartFourButton.click();
    return PortalApplicationPreviousLeavePage.get(this.page);
  }

  async startApplicationPartFive() {
    await this.startPartFiveButton.click();
    return PortalApplicationGenderPage.get(this.page);
  }
  async startApplicationPartSix() {
    await this.startPartSixButton.click();
    return PortalApplicationReviewPage.get(this.page);
  }
  async startApplicationPartSeven() {
    await this.startPartSevenButton.click();
    return PortalApplicationPaymentMethodPage.get(this.page);
  }

  async startApplicationPartEight() {
    await this.startPartEightButton.click();
    return PortalApplicationTaxWitholdingPage.get(this.page);
  }

  async startApplicationUploadId() {
    await this.startUploadIdButton.click();
    return PortalApplicationUploadIdPage.get(this.page);
  }

  async startApplicationUploadLeave() {
    await this.startUploadLeaveButton.click();
    return PortalApplicationUploadLeavePage.get(this.page);
  }

  async uploadDocLater() {
    await this.page.getByText("Select this box if you or").click();
  }

  async submitApplication() {
    await this.reviewAndSubmitButton.click();
    return PortalApplicationReviewAndSubmitPage.get(this.page);
  }

  private readonly startPartOneButton = this.page.getByLabel(
    "Verify your identification"
  );

  private readonly startPartTwoButton = this.page.getByLabel(
    "Start: Enter employment information"
  );

  private readonly startPartThreeButton = this.page.getByLabel(
    "Start: Enter leave details"
  );
  private readonly startPartFourButton = this.page.getByLabel(
    "Start: Report other leave, benefits, and income"
  );

  private readonly startPartFiveButton = this.page
    .getByLabel("Start: Confirm demographic")
    .or(
      this.page.getByRole("link", {
        name: "Continue with: Confirm",
      })
    );

  private readonly startPartSixButton = this.page.getByLabel(
    "Start: Review and submit"
  );
  private readonly startPartSevenButton = this.page.getByLabel(
    "Start: Enter payment method"
  );

  private readonly startPartEightButton = this.page.getByLabel(
    "Start: Enter tax withholding"
  );

  private readonly startUploadLeaveButton = this.page.getByLabel(
    "Start: Upload leave"
  );

  private readonly startUploadIdButton = this.page.getByRole("link", {
    name: "Start: Upload proof of",
  });

  private readonly reviewAndSubmitButton = this.page.getByRole("link", {
    name: "Review and submit application",
  });
}
