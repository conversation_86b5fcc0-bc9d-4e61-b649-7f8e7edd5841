import { expect, Page } from "@playwright/test";

import PageObjectModel from "../../../../PageObjectModel";

export default class PortalSubmittedApplicationPage extends PageObjectModel {
  private constructor(page: Page) {
    super(page);
  }

  static async get(page: Page) {
    const submittedApplicationHeading = page.getByRole("heading", {
      name: "You submitted your application",
    });
    await expect(submittedApplicationHeading).toBeVisible({ timeout: 20_000 });
    return new PortalSubmittedApplicationPage(page);
  }

  async getApplicationID() {
    const textContent = await this.page
      .getByText("Your application ID is ")
      .textContent();

    if (!textContent) {
      throw new Error("Application ID text not found on the page.");
    }

    const match = textContent.match(/NTN-[^\s]+/);
    if (!match) {
      throw new Error("Application ID was not found.");
    }

    return match[0];
  }
}
