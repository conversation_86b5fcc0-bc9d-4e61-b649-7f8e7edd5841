import { expect, Page } from "@playwright/test";

import { returnFormPath } from "../../../../../utils/application";
import PageObjectModel from "../../../../PageObjectModel";
import PortalInProgressApplicationPage from "../in-progress-application";

export default class PortalApplicationUploadIdPage extends PageObjectModel {
  private constructor(page: Page) {
    super(page);
  }

  static async get(page: Page) {
    const uploadLeaveHeading = page.getByRole("heading", {
      name: "Upload the front and back of",
    });

    await expect(uploadLeaveHeading).toBeVisible();
    return new PortalApplicationUploadIdPage(page);
  }

  async uploadId() {
    const fileInputs = this.page.locator("input.c-input-file__input");
    await fileInputs
      .nth(0)
      .setInputFiles(`${returnFormPath("license-MA")}.pdf`);
    await fileInputs
      .nth(0)
      .setInputFiles(`${returnFormPath("license-back")}.pdf`);
    await this.saveAndContinueButton.click();
    await this.page.waitForLoadState("networkidle");
    return await PortalInProgressApplicationPage.get(this.page);
  }

  async continue() {
    await this.saveAndContinueButton.click();
    await this.page.waitForLoadState("networkidle");
    return await PortalInProgressApplicationPage.get(this.page);
  }

  private readonly saveAndContinueButton = this.page.getByRole("button", {
    name: "Save and continue",
  });
}
