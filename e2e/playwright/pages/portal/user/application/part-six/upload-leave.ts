import { expect, Page } from "@playwright/test";

import config from "../../../../../../src/config";
import { returnFormPath } from "../../../../../utils/application";
import PageObjectModel from "../../../../PageObjectModel";
import PortalInProgressApplicationPage from "../in-progress-application";

export default class PortalApplicationUploadLeavePage extends PageObjectModel {
  private constructor(page: Page) {
    super(page);
  }

  static async get(page: Page) {
    const uploadLeaveHeading = page.getByRole("heading", {
      name: "Upload certification",
    });

    await expect(uploadLeaveHeading).toBeVisible();
    return new PortalApplicationUploadLeavePage(page);
  }

  async uploadDocument() {
    if (config("HAS_PROFILE_IDV_FEATURE")) {
      await this.page.getByText("Certification of Your Serious").click();
      await this.saveAndContinueButton.click();
      await this.page.getByText("I am uploading one complete").click();
      await this.saveAndContinueButton.click();
    }
    const fileInputs = this.page.locator("input.c-input-file__input");
    await fileInputs
      .nth(0)
      .setInputFiles(`${returnFormPath("license-MA")}.pdf`);
    await this.saveAndContinueButton.click();
    await expect(
      this.page.getByRole("heading", {
        name: "Your in-progress application",
      })
    ).toBeVisible({ timeout: 20_000 });
    return PortalInProgressApplicationPage.get(this.page);
  }

  async continue() {
    await this.saveAndContinueButton.click();
    await this.page.waitForLoadState("networkidle");
    return PortalInProgressApplicationPage.get(this.page);
  }

  private readonly saveAndContinueButton = this.page.getByRole("button", {
    name: "Save and continue",
  });
}
