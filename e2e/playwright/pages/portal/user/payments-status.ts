import { expect, Page } from "@playwright/test";

import config from "../../../../src/config";
import PageObjectModel from "../../PageObjectModel";
import { SideNavigation } from "../side-navigation";

export default class PortalUserApplicationStatusPaymentsPage extends PageObjectModel {
  readonly sideNavigation: SideNavigation;

  private constructor(page: Page) {
    super(page);
    this.sideNavigation = new SideNavigation(page);
  }

  static async get(page: Page) {
    const heading = page.getByRole("heading", {
      name: "Your payments",
    });

    await expect(heading).toBeVisible();

    return new PortalUserApplicationStatusPaymentsPage(page);
  }

  static async isOnPage(page: Page) {
    return page.url() === url;
  }
}

const url = `${config("PORTAL_BASEURL")}/application/status/payments`;
