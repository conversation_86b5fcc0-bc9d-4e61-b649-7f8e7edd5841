import { expect, Page } from "@playwright/test";

import config from "../../../../src/config";
import PageObjectModel from "../../PageObjectModel";
import PortalUserSettingsPage from "../settings";
import PortalInProgressApplicationPage from "./application/in-progress-application";
import PortalUserApplicationStatusPage from "./application-status";
import PortalUserCreateApplicationPage from "./create-application";
import PortalUserTaxDocumentsPage from "./tax-documents";

type GetOptions = {
  applicationAssociated?: string;
};

export default class PortalUserApplicationsPage extends PageObjectModel {
  private constructor(page: Page) {
    super(page);
  }

  static async get(page: Page, { applicationAssociated }: GetOptions = {}) {
    const targetUrl = applicationAssociated
      ? `${url}?applicationAssociated=${applicationAssociated}`
      : url;
    await page.waitForURL(targetUrl);
    return new PortalUserApplicationsPage(page);
  }

  static async isOnPage(page: Page) {
    return page.url().includes(url);
  }

  async startNewApplication() {
    await this.newApplicationButton.click();
    return PortalUserCreateApplicationPage.get(this.page);
  }

  async continueApplication(absence_id: string) {
    const card = this.page.locator("article").filter({ hasText: absence_id });
    await expect(card).toBeVisible();
    const link = card.getByRole("link", { name: "Continue application" });
    await expect(link).toBeVisible({ timeout: 20_000 });
    await link.click();
    return await PortalInProgressApplicationPage.get(this.page);
  }

  async navigateToSettings() {
    await this.settingsLink.click();
    return PortalUserSettingsPage.get(this.page);
  }

  async navigateToApplicationStatus(abscenceId: string) {
    return await PortalUserApplicationStatusPage.goto(this.page, abscenceId);
  }

  async navigateToTaxDocuments() {
    await this.taxDocumentsLink.click();
    return await PortalUserTaxDocumentsPage.get(this.page);
  }

  async expectTaxDocumentsNavigationHidden() {
    await expect(this.taxDocumentsLink).toBeHidden();
  }

  private readonly newApplicationButton = this.page.getByRole("link", {
    name: "Start a new application",
  });

  private readonly settingsLink = this.page.getByRole("link", {
    name: "Settings",
  });

  private readonly taxDocumentsLink = this.page.getByRole("link", {
    name: "Tax Documents",
  });
}

const url = `${config("PORTAL_BASEURL")}/applications/`;
