import { expect, Page } from "@playwright/test";

import config from "../../../../src/config";
import { getClaimantCredentials } from "../../../../src/util/credentials";
import { setup } from "../../../utils/portal";
import PageObjectModel from "../../PageObjectModel";
import PortalOAuthStartPage from "../oauth-start";
import { LoggedInClaimantPage } from "./logged-in";

export class PortalPage extends PageObjectModel {
  static async get(page: Page) {
    return new PortalPage(page);
  }

  async logInAsClaimant(credentials = getClaimantCredentials()) {
    await setup(this.page);
    const oAuthStartPage = await PortalOAuthStartPage.goto(this.page);
    const myMassGovPage = await oAuthStartPage.logInAsClaimant();
    await myMassGovPage.logIn(credentials);
    const portalUrl = config("PORTAL_BASEURL");
    expect(this.page.url()).toContain(portalUrl);

    return await LoggedInClaimantPage.get(this.page);
  }
}
