import { describe, expect, jest } from "@jest/globals";
import {
  addBusinessDays,
  format,
  getDay,
  isSameDay,
  startOfMonth,
  subBusinessDays,
  subDays,
} from "date-fns";

import config from "../../src/config";
import { isFeatureFlagEnabled } from "../../src/featureFlags";
import InfraClient from "../../src/InfraClient";
// The `itIf` import is renamed to `it` so the reporter
// can determine test block names
import { itIf as it } from "../util";

const environment = config("ENVIRONMENT");

const today = new Date();

/** Extracts Lists */
// This list was created based on this table of daily expected extracts: https://lwd.atlassian.net/wiki/spaces/DD/pages/2316960217/FINEOS+Extracts+List
const dailyExtracts = [
  "ABSENCE_RFI_DATA_COM.csv",
  "EmployeeDataLoad_feed_SOM.csv",
  "Employee_feed.csv",
  "Employee_feed_DELTA_SOM.csv",
  "Employee_feed_SOM.csv",
  "VBI_1099DATA_SOM.csv",
  "VBI_ABSENCEAPPEALCASES.csv",
  "VBI_BENEFITPERIOD.csv",
  "VBI_DECISIONDAYS_SOM.csv",
  "VBI_DOCUMENT_DELTA_SOM.csv",
  "VBI_DOCUMENT_SOM.csv",
  "VBI_ENTITLEMTPERIOD_SOM.csv",
  "VBI_EXTRADATA_SOM.csv",
  "VBI_FUTUREPAYMENTS_SOM.csv",
  "VBI_LEAVEPLANREQUESTEDABSENCE.csv",
  "VBI_LEAVESUMMARY.csv",
  "VBI_LEAVESUMMARY_SOM.csv",
  "VBI_MANAGEDREQUIREMENT.csv",
  "VBI_OCCUPATION.csv",
  "VBI_ORGANISATION.csv",
  "VBI_ORGUNIT_DETAILS_SOM.csv",
  "VBI_OVERPAYMENT_ASSOCIATEDDUES_SOM.csv",
  "VBI_OVERPAYMENTCASE.csv",
  "VBI_OVERPAYMENTSACTUALRECOVERY_SOM.csv",
  "VBI_OVERPAYMENTSADJUSTMENTS_SOM.csv",
  "VBI_OVERPAYMENTSRECOVERYPLAN_SOM.csv",
  "VBI_PAIDLEAVECASEINFO_SOM.csv",
  "VBI_REQ_ABSENCE_SUBSEQDECISION.csv",
  "VBI_REQUESTEDABSENCE.csv",
  "VBI_REQUESTEDABSENCE_SOM.csv",
  "VBI_SERVICEAGREEMENTDETAILS_DELTA_SOM.csv",
  "VBI_SERVICEAGREEMENTDETAILS_SOM.csv",
  "VBI_TASKREPORT_DELTA_SOM.csv",
  "VBI_TASKREPORT_SOM.csv",
  "VBI_TASK_SOM.csv",
  "VPAIDLEAVEINSTRUCTION_SOM.csv",
  "vpeiclaimdetails.csv",
  "vpei.csv",
  "vpeipaymentdetails.csv",
  "vpeipaymentline.csv",
  "WAGESBENEFITBYCLAIM_COM.csv",
];

// Context for fr24_8: https://lwd.atlassian.net/browse/PFMLPB-25410
const fr24_8DeltaExtracts = ["VBI_PAIDLEAVECASEINFO_COM.csv"];

// Ex: s3://fin-sompre-data-export/UAT/monthlyExtracts/2024-03-01-02-06-37-VBI_REQUESTEDABSENCE_ANNUAL.csv
const monthlyExtracts = [
  "EmployeeDataLoad_Monthly.csv",
  "PEI_FULL_EXTRACT_MONTHLY_SOM.csv",
  "PEI_CANCELLED_RECORDS_MONTHLY_SOM.csv",
  "PEI_REPLACED_RECORDS_MONTHLY_SOM.csv",
  "VBI_REQUESTEDABSENCE_MONTHLY.csv",
];

const fridayExtracts = ["COMFSD-375_OPs_20220728.csv"];

// Context for FR25_1: https://lwd.atlassian.net/browse/PFMLPB-22779
const fullExtractsToOmitIfFR25_1 = [
  "VBI_TASKREPORT_SOM.csv",
  "VBI_DOCUMENT_SOM.csv",
  "VBI_SERVICEAGREEMENTDETAILS_SOM.csv",
];

/** Helper Functions */
function stripDateFromExtract(filename: string) {
  return filename.replace(/[0-9]{4}-([0-9]{2}-){5}/, "");
}

/**
 * Asserts that the number and names of matching files match the expected list, if not, it throws an error.
 * @param {string[]} extractFiles - The list of files returned from the FINEOS extracts API
 * @param {string[]} expectedFiles - The list of files that should be present in the FINEOS extracts API
 * @param {Date} testingDate - The date for which the test is verifying extracts
 */
function validateMatchingExtracts(
  extractFiles: string[],
  expectedFiles: string[],
  testingDate: Date
) {
  try {
    expect(extractFiles.length).toBe(expectedFiles.length);
  } catch (e) {
    const missingExtracts = expectedFiles.filter(
      (file) => !extractFiles.includes(file)
    );
    throw Error(
      `${environment} - Missing (${
        expectedFiles.length - extractFiles.length
      }) extracts for ${format(testingDate, "PPPP")}\n${"-".repeat(
        20
      )}\n${missingExtracts.join("\r\n")}\n${"-".repeat(20)}\n${e}`
    );
  }
}

function findMatchingExtracts(extractFiles: string[], expectedFiles: string[]) {
  const extracts = new Set(extractFiles.map(stripDateFromExtract));
  return expectedFiles.filter((file) => extracts.has(file));
}

const infraClient = InfraClient.create(config);
/**
 * Tests the FINEOS extracts for a given date and frequency by retrieving the extracts
 * and validating that they match the expected list of files.
 *
 * @param {Date} date - The date for which the extracts are being tested
 * @param {"daily" | "weekly" | "monthly"} frequency - The extract batch type
 * @param {string[]} expectedFiles - The list of files that are expected to be present
 */
async function testExtracts(
  date: Date,
  frequency: "daily" | "weekly" | "monthly",
  expectedFiles: string[]
) {
  const files = await infraClient.getFineosExtracts(date, frequency);
  const matchingExtracts = findMatchingExtracts(files, expectedFiles);
  validateMatchingExtracts(matchingExtracts, expectedFiles, date);
}

jest.setTimeout(1000 * 30);
/**
 * @group morning
 */
describe("FINEOS extracts", () => {
  it(true)(
    `Extracts produce every expected file from dailyExtracts`,
    async () => {
      const targetDate = subDays(today, 1);
      const baseExtracts = [...dailyExtracts];
      const additionalExtracts = (await isFeatureFlagEnabled("HAS_FR24_8"))
        ? fr24_8DeltaExtracts
        : [];
      const combinedExtracts = baseExtracts.concat(additionalExtracts);

      const expectedDailyExtracts = config("HAS_FR25_1")
        ? combinedExtracts.filter(
            (file) => !fullExtractsToOmitIfFR25_1.includes(file)
          )
        : combinedExtracts;

      await testExtracts(targetDate, "daily", expectedDailyExtracts);
    }
  );
  const isMonday = getDay(today) === 1;

  it(isMonday)(`Produces Friday's weekly extracts`, async () => {
    const targetDate = subBusinessDays(today, 1);
    await testExtracts(targetDate, "weekly", fridayExtracts);
  });

  const firstDayOfMonth = startOfMonth(today);
  const businessDayAfterFirst = addBusinessDays(firstDayOfMonth, 1);
  const isSameDayBoolean = isSameDay(businessDayAfterFirst, today);
  it(isSameDayBoolean)(`Produces monthly extracts`, async () => {
    await testExtracts(firstDayOfMonth, "monthly", monthlyExtracts);
  });
});
