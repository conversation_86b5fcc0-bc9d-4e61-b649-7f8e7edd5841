import { Page } from "@playwright/test";

import { ConfigPage } from "../submission/fineos.pages";
import { FineosPresetKey, fineosRolePresets } from "./fineosRolePresets";

export async function chooseRolePreset(
  page: Page,
  userId: string,
  preset: FineosPresetKey
): Promise<void> {
  if (userId === "SRV_SSO_Account") {
    throw new Error("Changing role permissions for this account is disallowed");
  }

  const userPreset = fineosRolePresets[preset];

  if (!userPreset) {
    throw new Error(
      `No role preset found for key: ${preset}. 
      Please make sure this group is defined in allPresets`
    );
  }

  const configPage = await ConfigPage.visit(page);
  await configPage.setRoles(userId, userPreset.departments);
}
