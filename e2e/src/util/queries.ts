import config from "../config";
import InfraClient from "../InfraClient";

/**
 * Directly query the PFML API's database
 * and returns the response in JSON.
 * Can retry up to 5 times.
 */
export async function queryDb(query: string) {
  const infraClient = InfraClient.create(config);
  const response = await infraClient.sendAndWaitForSSMShellCommand(
    infraClient.buildRdsCommand(query, ["-t", "-A"]),
    { retries: 10 }
  );

  const content = response.StandardOutputContent;
  if (content) {
    if (query.includes("json_agg") && content.trim() === "") {
      return [];
    }
    try {
      return JSON.parse(content);
    } catch (e) {
      throw Error(
        `DB query response content is not a valid JSON string. ${e.message}.`
      );
    }
  }
  return null;
}

export function getApprovedClaimWithApprovedLeaveRequest() {
  return `
    select json_build_object(
        'fein', emp.employer_fein,
        'fineosId', c.fineos_absence_id
    ) from claim c
        JOIN absence_period AS ap ON ap.claim_id = c.claim_id
        JOIN employee AS emy on emy.employee_id = c.employee_id
        JOIN employee_occupation AS eo on eo.employee_id = emy.employee_id
        JOIN employer AS emp on emp.employer_id = eo.employer_id 
        JOIN lk_leave_request_decision
                ON lk_leave_request_decision.leave_request_decision_id = ap.leave_request_decision_id
        JOIN application AS app ON app.claim_id = c.claim_id 
        WHERE lk_leave_request_decision.leave_request_decision_description IN ('Approved')
            AND ap.leave_request_decision_id = 3
        LIMIT 1;
    `;
}

export function getClaimCompletedAndApprovedTodayQuery(email: string): string {
  return `SELECT json_build_object(
        'fineos_absence_id', c.fineos_absence_id,
        'created_at', c.created_at,
        'employer_fein', e.employer_fein
      )
      FROM claim AS c
      INNER JOIN application AS a ON c.claim_id = a.claim_id
      INNER JOIN public.user AS u ON a.user_id = u.user_id
      INNER JOIN employer AS e ON c.employer_id = e.employer_id
      WHERE u.email_address = '${email}'
        AND a.completed_time::date = CURRENT_DATE
        AND c.approval_date = CURRENT_DATE
              ORDER BY a.completed_time DESC LIMIT 1;
      `;
}

export function getClaimWithCompletedPaymentsQuery(username: string): string {
  return `SELECT json_build_object(
        'fineos_absence_id', c.fineos_absence_id,
        'created_at', c.created_at,
        'absence_period_start_date', ap.absence_period_start_date
      )
      FROM claim AS c
      INNER JOIN absence_period AS ap ON ap.claim_id = c.claim_id
      INNER JOIN application AS a ON c.claim_id = a.claim_id
      INNER JOIN public.user AS u ON a.user_id = u.user_id
      INNER JOIN employee_occupation AS eo ON eo.employee_id = c.employee_id
      INNER JOIN payment AS p ON c.claim_id = p.claim_id
      INNER JOIN lk_absence_status AS lkas ON c.fineos_absence_status_id = lkas.absence_status_id
      WHERE u.email_address = '${username}'
        AND a.completed_time > (CURRENT_DATE - INTERVAL '28 days')::timestamp
        AND a.completed_time < (CURRENT_DATE - INTERVAL '7 days')::timestamp
        AND p.has_active_writeback_issue = FALSE
        AND lkas.absence_status_description = 'Closed'
        AND p.amount > 0
      ORDER BY a.completed_time DESC
      LIMIT 1`;
}

export function getClaimCompletedAndApprovedQuery(email: string): string {
  return `SELECT json_build_object(
        'fineos_absence_id', c.fineos_absence_id,
        'created_at', c.created_at,
        'employer_fein', e.employer_fein
      )
      FROM claim AS c
      INNER JOIN application AS a ON c.claim_id = a.claim_id
      INNER JOIN public.user AS u ON a.user_id = u.user_id
      INNER JOIN employer AS e ON c.employer_id = e.employer_id
      WHERE u.email_address = '${email}'
        AND c.approval_date IS NOT NULL
          ORDER BY a.completed_time DESC
          LIMIT 1`;
}

export function getClaimWithIntermittentLeaveHoursQuery(email: string): string {
  return `SELECT json_build_object(
        'fineos_absence_id', c.fineos_absence_id,
        'created_at', c.created_at,
        'employer_fein', e.employer_fein
      )
      FROM claim AS c
      INNER JOIN application AS a ON c.claim_id = a.claim_id
      INNER JOIN public.user AS u ON a.user_id = u.user_id
      INNER JOIN employer AS e ON c.employer_id = e.employer_id
        WHERE u.email_address = '${email}'
        AND c.approval_date IS NOT NULL
        AND a.has_intermittent_leave_periods = TRUE
      ORDER BY a.completed_time DESC
      LIMIT 1`;
}

export function getClaimAfterNightlyExtractQuery(username: string): string {
  return `SELECT json_build_object(
            'fineos_absence_id', c.fineos_absence_id,
            'created_at', c.created_at,
            'employer_fein', e.employer_fein
          )
          FROM claim AS c
          INNER JOIN application AS a ON c.claim_id = a.claim_id
          INNER JOIN application_payment_preference AS app ON a.payment_preference_id = app.payment_pref_id
          INNER JOIN lk_payment_method AS pm ON app.payment_method_id = pm.payment_method_id
          INNER JOIN public.user AS u ON a.user_id = u.user_id
          INNER JOIN payment AS p ON c.claim_id = p.claim_id
          INNER JOIN state_log AS sl ON p.payment_id = sl.payment_id
          INNER JOIN latest_state_log AS lsl ON sl.state_log_id = lsl.state_log_id
          INNER JOIN employer AS e ON c.employer_id = e.employer_id
          INNER JOIN employee_occupation AS eo ON eo.hours_worked_per_week = 40
          INNER JOIN lk_absence_status AS lkas ON c.fineos_absence_status_id = lkas.absence_status_id
          INNER JOIN lk_state AS st ON sl.end_state_id = st.state_id
          WHERE u.email_address = '${username}'
            AND a.completed_time > (CURRENT_DATE - INTERVAL '28 days')::timestamp
            AND a.completed_time < (CURRENT_DATE - INTERVAL '7 days')::timestamp
            AND p.has_active_writeback_issue = FALSE
            AND lkas.absence_status_description IN ('Approved','Closed','Completed')
            AND p.amount > 0
            AND st.state_description IN ('Payment complete', 'PUB Transaction sent - Check', 'PUB Transaction sent - EFT', 'Payment Complete with change notification')
            AND pm.payment_method_description = 'Check'
          ORDER BY a.completed_time DESC
          LIMIT 1`;
}

export function findSentEmailQuery(applicationId: string, trigger: string) {
  return `SELECT json_build_object('created_at', ntf.created_at, 'fineos_absence_id', ntf.fineos_absence_id)
    FROM notification AS ntf
    INNER JOIN claim AS c
    ON ntf.fineos_absence_id = c.fineos_absence_id
    WHERE c.claim_id = '${applicationId}'
    AND ntf.request_json::json->>'trigger' = '${trigger}'
    LIMIT 1;`;
}
