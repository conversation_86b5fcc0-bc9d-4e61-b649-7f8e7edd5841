import { FineosRoles, FineosSecurityGroup } from "../types";

/**
 * This module defines reusable role presets that map a named key like "DFMLOPS" to a list of
 * department assignments in FINEOS. Each preset includes:
 *
 * - `description`: A human-readable label used for documentation
 * - `securedActionGroup` (optional): A security group string tied to secure action testing.
 *    Included when a preset needs to be referenced in test suites or automated role validation flows
 * - `departments`: An array of department assignments, each specifying:
 *    - `role`: A FINEOS department name (from FineosRoles or FineosSecurityGroups)
 *    - `memberOf`: Whether the user should be assigned as a member of the department
 *    - `supervisorOf`: Whether the user should be assigned as a supervisor
 */

/**
 * A department assignment specifying if the user is a member or supervisor
 */
export interface DepartmentConfig {
  role: FineosRoles | FineosSecurityGroup;
  supervisorOf: boolean;
  memberOf: boolean;
}

/**
 * A complete role preset representing a type of FINEOS user
 */
interface RolePreset {
  description: string;
  securedActionGroup?: FineosSecurityGroup;
  departments: DepartmentConfig[];
}

/**
 * A record of name presets keyed by string identifiers
 */
export type PresetMap = Record<string, RolePreset>;
export type FineosPresetKey = keyof typeof fineosRolePresets;

/**
 * Presets for DFML Appeals users
 */
const appealsPresets: PresetMap = {
  DFMLOPS4: {
    description: "DFML Appeals Examiner",
    securedActionGroup: "DFML Appeals Examiner I(sec)",
    departments: [
      { role: "DFML Appeals", memberOf: true, supervisorOf: true },
      {
        role: "DFML Appeals Examiner I(sec)",
        memberOf: true,
        supervisorOf: false,
      },
    ],
  },
  DFMLOPS5: {
    description: "DFML Appeals Administrator",
    securedActionGroup: "DFML Appeals Administrator(sec)",
    departments: [
      {
        role: "DFML Appeals Administrator(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      { role: "DFML Appeals", memberOf: true, supervisorOf: true },
      {
        role: "DFML Appeals Examiner I(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      {
        role: "DFML Appeals Examiner II(sec)",
        memberOf: true,
        supervisorOf: false,
      },
    ],
  },
  SAVILINX4: {
    description: "DFML Appeals Examiner II",
    securedActionGroup: "DFML Appeals Examiner II(sec)",
    departments: [
      { role: "DFML Appeals", memberOf: true, supervisorOf: true },
      {
        role: "DFML Appeals Examiner II(sec)",
        memberOf: true,
        supervisorOf: false,
      },
    ],
  },
};
/**
 * Presets for DFML Program Integrity users
 */
const programIntegrityPresets: PresetMap = {
  DFMLOPS: {
    description: "DFML Claims Examiner",
    securedActionGroup: "DFML Claims Examiners(sec)",
    departments: [
      { role: "DFML Program Integrity", memberOf: true, supervisorOf: true },
      {
        role: "DFML Claims Examiners(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      { role: "DFML PI(sec)", memberOf: true, supervisorOf: false },
    ],
  },
  DFMLOPS1: {
    description: "DFML Claims Supervisor",
    securedActionGroup: "DFML Claims Supervisors(sec)",
    departments: [
      { role: "DFML Program Integrity", memberOf: true, supervisorOf: true },
      {
        role: "DFML Claims Examiners(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      {
        role: "DFML Claims Supervisors(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      { role: "DFML PI(sec)", memberOf: true, supervisorOf: false },
    ],
  },
  DFMLOPS2: {
    description: "DFML Compliance Analyst",
    securedActionGroup: "DFML Compliance Analyst(sec)",
    departments: [
      { role: "DFML Program Integrity", memberOf: true, supervisorOf: true },
      {
        role: "DFML Compliance Analyst(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      { role: "DFML PI(sec)", memberOf: true, supervisorOf: false },
    ],
  },
  DFMLOPS3: {
    description: "DFML Compliance Supervisor",
    securedActionGroup: "DFML Compliance Supervisors(sec)",
    departments: [
      {
        role: "DFML Compliance Supervisors(sec)",
        memberOf: true,
        supervisorOf: true,
      },
      { role: "DFML Program Integrity", memberOf: true, supervisorOf: true },
      {
        role: "DFML Compliance Analyst(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      {
        role: "DFML Compliance",
        memberOf: true,
        supervisorOf: false,
      },
      { role: "DFML PI(sec)", memberOf: true, supervisorOf: false },
    ],
  },
};
/**
 * Presets for DFML IT users
 */
const itPresets: PresetMap = {
  SAVILINX3: {
    description: "DFML IT",
    securedActionGroup: "DFML IT(sec)",
    departments: [
      { role: "DFML IT", memberOf: true, supervisorOf: true },
      { role: "DFML IT(sec)", memberOf: true, supervisorOf: true },
    ],
  },
};
/**
 * Presets for DFML Overpayments users
 */
const overpaymentsPresets: PresetMap = {
  DFMLOPS8: {
    description: "DFML Overpayments Specialist",
    departments: [
      { role: "DFML Overpayments", memberOf: true, supervisorOf: true },
      { role: "DFML PI(sec)", memberOf: true, supervisorOf: false },
    ],
  },
  DFMLOPS9: {
    description: "DFML Overpayments Lead",
    departments: [
      { role: "DFML Overpayments", memberOf: true, supervisorOf: true },
      { role: "DFML PI(sec)", memberOf: true, supervisorOf: false },
      { role: "DFML Program Integrity", memberOf: true, supervisorOf: true },
      {
        role: "DFML Compliance Supervisors(sec)",
        memberOf: true,
        supervisorOf: false,
      },
    ],
  },
};
/**
 * Presets for SaviLinx(SL) users
 */
const savilinxPresets: PresetMap = {
  SAVILINX: {
    description: "SaviLinx Agents",
    securedActionGroup: "SaviLinx Agents (sec)",
    departments: [
      { role: "SaviLinx", memberOf: true, supervisorOf: true },
      { role: "SaviLinx Agents (sec)", memberOf: true, supervisorOf: false },
    ],
  },
  SAVILINX1: {
    description: "SaviLinx Supervisors",
    securedActionGroup: "SaviLinx Supervisors(sec)",
    departments: [
      { role: "SaviLinx", memberOf: true, supervisorOf: true },
      { role: "SaviLinx Agents (sec)", memberOf: true, supervisorOf: false },
      {
        role: "SaviLinx Back Office Agents(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      {
        role: "SaviLinx Secured Agents(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      {
        role: "SaviLinx Supervisors(sec)",
        memberOf: true,
        supervisorOf: false,
      },
    ],
  },
  SAVILINX2: {
    description: "SaviLinx Back Office Agents",
    securedActionGroup: "SaviLinx Back Office Agents(sec)",
    departments: [
      { role: "SaviLinx", memberOf: true, supervisorOf: true },
      {
        role: "SaviLinx Back Office Agents(sec)",
        memberOf: true,
        supervisorOf: false,
      },
      {
        role: "SL - Back Office",
        memberOf: true,
        supervisorOf: true,
      },
      {
        role: "SaviLinx Back Office Agents(sec)",
        memberOf: true,
        supervisorOf: false,
      },
    ],
  },
  SAVILINX5: {
    description: "SaviLinx Secured Agents",
    securedActionGroup: "SaviLinx Secured Agents(sec)",
    departments: [
      { role: "SaviLinx", memberOf: true, supervisorOf: true },
      {
        role: "SaviLinx Secured Agents(sec)",
        memberOf: true,
        supervisorOf: false,
      },
    ],
  },
};

export const fineosRolePresets: PresetMap = {
  ...appealsPresets,
  ...itPresets,
  ...overpaymentsPresets,
  ...programIntegrityPresets,
  ...savilinxPresets,
};

/**
 * Finds a role by its associated `securedActionGroup`
 *
 * Used primarily in secure action tests, or situations where we start with a known security group
 * (e.g. "DFML Claims Examiners(sec)") and we need the full department configuration to apply it.
 * @param securedGroup - A valid FINEOS security group
 * @returns The matching role preset and its key in `fineosRolePresets`
 */
export function getPresetBySecuredActionGroup(
  securedGroup: FineosSecurityGroup
): RolePreset & { key: string } {
  const entry = Object.entries(fineosRolePresets).find(
    ([_, preset]) => preset.securedActionGroup === securedGroup
  );

  if (!entry) {
    throw new Error(
      `No preset found with secured action group: ${securedGroup}`
    );
  }

  const [key, preset] = entry;
  return { ...preset, key };
}

export function getPresetRoles(preset: FineosPresetKey): DepartmentConfig[] {
  const config = fineosRolePresets[preset];
  if (!config) {
    throw new Error(`Preset "${preset}" not found in fineosRolePresets`);
  }
  return config.departments;
}
