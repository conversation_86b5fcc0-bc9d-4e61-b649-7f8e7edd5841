import { addBusinessDays, addDays, format, subDays } from "date-fns";
import { convertToTimeZone } from "date-fns-timezone";
import delay from "delay";
import pRetry from "p-retry";
import winston from "winston";

import { DetailedClaimResponse } from "../_api";
import {
  ApplicationRequestBody,
  ChangeRequest,
  DocumentUploadRequest,
  EmployerClaimRequestBody,
  getApplicationsByApplication_id,
  getClaimsByFineos_absence_id,
  getEmployersClaimsByFineos_absence_idDocumentsAndFineos_document_id,
  getEmployersClaimsByFineos_absence_idReview,
  IntermittentLeavePeriods,
  patchApplicationsByApplication_id,
  patchChangeRequestByChange_request_id,
  patchEmployersClaimsByFineos_absence_idReview,
  PaymentPreferenceRequestBody,
  postApplications,
  postApplicationsByApplication_idCompleteApplication,
  postApplicationsByApplication_idDocuments,
  postApplicationsByApplication_idSubmitApplication,
  postApplicationsByApplication_idSubmitCustomerPaymentPreference,
  postApplicationsByApplication_idSubmitTaxWithholdingPreference,
  postBenefitYearsSearch,
  postChangeRequest,
  postChangeRequestByChange_request_idDocuments,
  postChangeRequestByChange_request_idSubmit,
  postReportIntermittentLeaveEpisode,
  RequestOptions,
  TaxWithholdingPreferenceRequestBody,
} from "../api";
import generateDocuments, {
  DocumentWithPromisedFile,
} from "../generation/documents";
import { Employer } from "../generation/Employer";
import { FineosIntermittentLeave, GeneratedClaim } from "../generation/types";
import { ApplicationSubmissionResponse, Credentials } from "../types";
import {
  extractLeavePeriodType,
  getPeriodFromLeaveDetails,
} from "../util/claims";
import {
  getClaimantCredentials,
  getLeaveAdminCredentials,
} from "../util/credentials";
import { getCertificationDocumentType } from "../util/documents";
import { generateFein } from "../util/employers";
import { getClaimantToken, getLeaveAdminToken } from "../util/myMassGov";
import { generateSsn } from "../util/pii";
import { assertIsString, assertValidClaim } from "../util/typeUtils";
import { wait } from "../util/wait";

type SubmitApplicationResponse = {
  fineos_absence_id: string;
  fineos_absence_status: string | null | undefined;
  first_name: string;
  last_name: string;
};

export default class PortalSubmitter {
  private base: string;

  constructor(apiBaseUrl: string) {
    this.base = apiBaseUrl;
  }

  protected async getOptions(
    token: string,
    fraudCheck?: boolean
  ): Promise<RequestOptions> {
    const fraudCheckHeader = fraudCheck
      ? { "x-ff-Application-Fraud-Check": "true" }
      : {};

    return {
      baseUrl: this.base,
      headers: {
        Authorization: `Bearer ${token}`,
        "User-Agent": "PFML Business Simulation Bot",
        ...fraudCheckHeader,
      },
    };
  }

  protected async getClaimantOptions(
    credentials = getClaimantCredentials(),
    fraudCheck: boolean = false
  ) {
    const token = await getClaimantToken(credentials);
    return this.getOptions(token, fraudCheck);
  }

  async submit(
    claim: GeneratedClaim,
    credentials: Credentials,
    options: SubmitOptions = {}
  ): Promise<ApplicationSubmissionResponse> {
    const { completionDelay, leaveAdminCredentials: employerCredentials } =
      options;
    const requestOptions = await this.getClaimantOptions(credentials);
    const application_id = await this.createApplication(requestOptions);
    const userNotFoundInfo = claim.claim.additional_user_not_found_info;

    if (userNotFoundInfo) {
      return this.submitUserNotFound(
        application_id,
        claim,
        credentials,
        requestOptions
      );
    }

    const { fineos_absence_id, first_name, last_name } =
      await this.submitPartOne(claim.claim, application_id, credentials);

    await this.submitPartTwo(application_id, claim, credentials);
    await this.submitPartThree(
      {
        applicationId: application_id,
        claim,
        credentials,
        fineosAbsenceId: fineos_absence_id,
      },
      { completionDelay }
    );

    if (claim.employerResponse) {
      if (!employerCredentials) {
        throw new Error(
          "Unable to submit employer response without leave admin credentials"
        );
      }

      await this.submitEmployerResponse(
        employerCredentials,
        fineos_absence_id,
        claim.employerResponse
      );
    }

    return {
      fineos_absence_id,
      application_id,
      first_name,
      last_name,
    };
  }

  async createApplicationAndSubmitPartOne(
    application: ApplicationRequestBody,
    credentials: Credentials,
    fraudCheck: boolean = false
  ): Promise<SubmitApplicationResponse & { application_id: string }> {
    const options = await this.getClaimantOptions(credentials, fraudCheck);
    const application_id = await this.createApplication(options);
    return this.submitPartOne(
      application,
      application_id,
      credentials,
      fraudCheck
    );
  }

  async submitPartOne(
    application: ApplicationRequestBody,
    application_id: string,
    credentials: Credentials,
    fraudCheck: boolean = false
  ): Promise<SubmitApplicationResponse & { application_id: string }> {
    const options = await this.getClaimantOptions(credentials, fraudCheck);

    await this.updateApplication(application_id, application, options);
    const submitResponseData = await this.submitApplication(
      application_id,
      options
    );

    return {
      ...submitResponseData,
      application_id,
    };
  }

  async submitPartTwo(
    application_id: string,
    claim: GeneratedClaim,
    credentials: Credentials
  ) {
    const options = await this.getClaimantOptions(credentials);
    const { is_withholding_tax, paymentPreference } = claim;
    await this.uploadPaymentPreference(
      application_id,
      paymentPreference,
      options
    );

    await this.submitTaxPreference(
      application_id,
      { is_withholding_tax },
      options
    );
  }

  async submitPartThree(
    parameters: SubmitPartThreeParameters,
    options: SubmitPartThreeOptions = {}
  ): Promise<ApplicationSubmissionResponse> {
    const { applicationId, claim, credentials, fineosAbsenceId } = parameters;
    const { completionDelay } = options;
    const requestOptions = await this.getClaimantOptions(credentials);
    const { documents, metadata } = claim;
    await this.uploadDocuments(applicationId, documents, requestOptions);

    if (!metadata?.applicationIncomplete) {
      // FINEOS has automation to mark identity proof evidence as "satisfied"
      // and suppress the "ID Review" task when the claimant has a valid Mass
      // ID. FINEOS must complete a call to the PFML API /rmv-check endpoint
      // before the PFML API marks the ID document as "received", or the
      // "ID Review" task will be created and the evidence will not be marked as
      // "satisfied". FINEOS needs a few seconds to make this call. A delay of a
      // few seconds between submitting documents and completing the application
      // is enough for consistency, barring any unusual performance issues.
      //
      // It is highly unlikely that a human using the Portal web app would
      // encounter this race condition. The automation bypasses the frontend and
      // is very fast. This typically only matters for training scenarios, or
      // when explicitly testing this feature.
      if (completionDelay) {
        await wait(completionDelay);
      }

      await this.completeApplication(applicationId, requestOptions);
    }

    assertIsString(claim.claim.first_name);
    assertIsString(claim.claim.last_name);

    return {
      application_id: applicationId,
      fineos_absence_id: fineosAbsenceId,
      first_name: claim.claim.first_name,
      last_name: claim.claim.last_name,
    };
  }

  async submitEmployerResponse(
    employerCredentials: Credentials,
    fineos_absence_id: string,
    response: EmployerClaimRequestBody
  ) {
    const token = await getLeaveAdminToken(employerCredentials);
    const options = await this.getOptions(token);
    // When we go to submit employer response, we need to first fetch the review doc.
    // The review doc does not become available until Fineos has posted a notification back to the API.
    // This can take several seconds, so we loop and wait until that notification has a chance to complete
    // before proceeding. Without the retry here, we'd fail immediately.
    const review = await pRetry(
      async () => {
        try {
          return await getEmployersClaimsByFineos_absence_idReview(
            { fineos_absence_id },
            options
          );
        } catch (e) {
          if (
            e.data &&
            e.data.message === "Claim does not exist for given absence ID"
          ) {
            throw new Error(
              `Unable to find claim as leave admin for ${fineos_absence_id}.`
            );
          }
          // Otherwise, abort immediately - there's some other problem.
          throw new pRetry.AbortError(e);
        }
      },
      { retries: 20, maxRetryTime: 30_000 }
    );
    const { data } = review.data;
    if (!data || !data.employer_benefits || !data.previous_leaves) {
      throw new Error(
        "Cannot submit employer response due to missing data on the employer review."
      );
    }
    await patchEmployersClaimsByFineos_absence_idReview(
      { fineos_absence_id },
      {
        ...response,
        employer_benefits: response.employer_benefits.map((benefit, idx) => {
          const reviewResponseEb = data.employer_benefits[idx];
          if (reviewResponseEb) {
            return { ...reviewResponseEb, ...benefit };
          }
          return benefit;
        }),
        previous_leaves: response.previous_leaves.map((leave, idx) => {
          const reviewResponsePl = data.previous_leaves[idx];
          if (reviewResponsePl) {
            return { ...reviewResponsePl, ...leave };
          }
          return leave;
        }),
      },
      options
    );
  }

  protected async uploadDocuments(
    application_id: string,
    documents: (DocumentUploadRequest | DocumentWithPromisedFile)[],
    options?: RequestOptions
  ) {
    // Documents must be uploaded in series rather than parallel. Overlapping
    // calls to upload and receive documents can result in employer review
    // requirements not being populated in FINEOS.
    for (const document of documents) {
      const file = this.documentIsPromisedFile(document)
        ? await document.file().then((d) => d.asStream())
        : document.file;

      await postApplicationsByApplication_idDocuments(
        { application_id },
        { ...document, file },
        options
      );
    }
  }

  protected async uploadAdditionalDocuments(
    change_request_id: string,
    documents: (DocumentUploadRequest | DocumentWithPromisedFile)[],
    options?: RequestOptions
  ) {
    const promises = documents.map(async (document) => {
      const file = this.documentIsPromisedFile(document)
        ? await document.file().then((d) => d.asStream())
        : document.file;

      return postChangeRequestByChange_request_idDocuments(
        { change_request_id },
        { ...document, file },
        options
      );
    });
    await Promise.all(promises);
  }

  protected documentIsPromisedFile(
    document: DocumentUploadRequest | DocumentWithPromisedFile
  ): document is DocumentWithPromisedFile {
    return typeof document.file === "function";
  }

  protected async createApplication(options?: RequestOptions): Promise<string> {
    const response = await postApplications(options);
    if (response?.data?.data && response?.data?.data.application_id) {
      return response.data.data.application_id;
    }
    throw new Error("Unable to create new application");
  }

  async getApplication(
    application_id: string,
    options?: RequestOptions
  ): ReturnType<typeof getApplicationsByApplication_id> {
    const opts = options ?? (await this.getClaimantOptions());
    return getApplicationsByApplication_id({ application_id }, opts);
  }

  async getBenefitYears(fineosAbsenceId: string) {
    const request = { terms: { fineos_absence_id: fineosAbsenceId } };
    const options = await this.getClaimantOptions();
    const response = await postBenefitYearsSearch(request, options);
    return response.data.data ?? [];
  }

  async getClaim(
    fineos_absence_id: string,
    options?: RequestOptions
  ): ReturnType<typeof getClaimsByFineos_absence_id> {
    const opts = options ?? (await this.getClaimantOptions());
    return getClaimsByFineos_absence_id({ fineos_absence_id }, opts);
  }

  protected async updateApplication(
    application_id: string,
    application: ApplicationRequestBody,
    options: RequestOptions
  ): ReturnType<typeof patchApplicationsByApplication_id> {
    return patchApplicationsByApplication_id(
      { application_id },
      application,
      options
    );
  }

  protected async submitApplication(
    application_id: string,
    options?: RequestOptions,
    isUNF?: boolean
  ): Promise<SubmitApplicationResponse> {
    const {
      data: { data: response },
    } = await postApplicationsByApplication_idSubmitApplication(
      { application_id },
      options
    );
    if (isUNF && response && response.first_name && response.last_name) {
      return {
        first_name: response.first_name as string,
        last_name: response.first_name as string,
        fineos_absence_id: "",
        fineos_absence_status: "",
      };
    }
    if (
      !isUNF &&
      response &&
      response.fineos_absence_id &&
      response.first_name &&
      response.last_name
    ) {
      return response as {
        fineos_absence_id: string;
        fineos_absence_status: string | null | undefined;
        first_name: string;
        last_name: string;
      };
    }
    throw new Error(
      "Submit application data did not contain one of the following required properties: fineos_absence_id, first_name, last_name"
    );
  }

  protected async completeApplication(
    application_id: string,
    options?: RequestOptions,
    // This flag is related to the work for PFMLPB-21093.
    certificate_document_deferred?: boolean
  ): ReturnType<typeof postApplicationsByApplication_idCompleteApplication> {
    return postApplicationsByApplication_idCompleteApplication(
      { application_id },
      { certificate_document_deferred },
      options
    );
  }

  protected async uploadPaymentPreference(
    application_id: string,
    paymentPreference: PaymentPreferenceRequestBody,
    options?: RequestOptions
  ): ReturnType<
    typeof postApplicationsByApplication_idSubmitCustomerPaymentPreference
  > {
    return postApplicationsByApplication_idSubmitCustomerPaymentPreference(
      { application_id },
      paymentPreference,
      options
    );
  }

  protected async submitTaxPreference(
    application_id: string,
    taxWithholdingPreferenceRequestBody: TaxWithholdingPreferenceRequestBody,
    options?: RequestOptions
  ): ReturnType<
    typeof postApplicationsByApplication_idSubmitTaxWithholdingPreference
  > {
    return postApplicationsByApplication_idSubmitTaxWithholdingPreference(
      {
        application_id,
      },
      taxWithholdingPreferenceRequestBody,
      options
    );
  }

  /*
  Submits a modification request to FINEOS via the API.
   */
  async submitModification(
    fineosAbsenceId: string,
    modification: ChangeRequest,
    claim: GeneratedClaim,
    options?: RequestOptions
  ) {
    const opts = options ?? (await this.getClaimantOptions());

    // This get request will sync up absence periods decisions between FINEOS and PFML
    await getClaimsByFineos_absence_id(
      { fineos_absence_id: fineosAbsenceId },
      opts
    );

    // There is validation that occurs at the 'change-request/{change_request_id}/submit' endpoint
    // and can fail if absence periods aren't approved there is a delay while syncing the decision between FINEOS and PFML.
    // 5 seconds seems to be the point where we see reliable successful calls
    await delay(1000 * 5);
    const {
      data: { data: responseData },
    } = await postChangeRequest(
      { fineos_absence_id: fineosAbsenceId },
      modification,
      opts
    );
    if (!responseData?.change_request_id) {
      throw Error("Undefined change_request_id");
    }
    if (modification.change_request_type === "Extension") {
      const documentType = getCertificationDocumentType(
        claim.claim.leave_details?.reason
      );
      const certDoc = claim.documents.find(
        (document) => document.document_type === documentType
      );
      if (!certDoc) {
        throw Error(
          `Missing certification document for documents:\n${JSON.stringify(
            claim.documents,
            null,
            2
          )}`
        );
      }
      const file = await certDoc.file().then((d) => d.asStream());
      await postChangeRequestByChange_request_idDocuments(
        { change_request_id: responseData.change_request_id },
        { ...certDoc, file },
        opts
      );
    } else if (
      modification.change_request_type === "Medical To Bonding Transition"
    ) {
      patchChangeRequestByChange_request_id(
        { change_request_id: responseData.change_request_id },
        modification,
        opts
      );
    }

    if (claim.metadata?.postModificationDocuments) {
      const documents = generateDocuments(
        claim.claim,
        claim.metadata?.postModificationDocuments ?? {}
      );

      this.uploadAdditionalDocuments(
        responseData.change_request_id,
        documents,
        opts
      );
    }

    await postChangeRequestByChange_request_idSubmit(
      { change_request_id: responseData.change_request_id as string },
      opts
    );
  }

  public async uploadPostSubmissionDocuments(
    fineosAbsenceId: string,
    claim: GeneratedClaim,
    applicationId: string,
    options?: RequestOptions
  ) {
    const opts = options ?? (await this.getClaimantOptions());

    await getClaimsByFineos_absence_id(
      { fineos_absence_id: fineosAbsenceId },
      opts
    );
    const documents = generateDocuments(
      claim.claim,
      claim.metadata?.postApprovalDocuments ?? {}
    );
    await this.uploadDocuments(applicationId, documents, opts);
  }

  async submitStepsOneTwoOfPartOne(
    application: ApplicationRequestBody,
    credentials: Credentials
  ) {
    const options = await this.getClaimantOptions(credentials);
    const application_id = await this.createApplication(options);
    const response = await this.updateApplication(
      application_id,
      application,
      options
    );
    return response.data.data?.application_id;
  }

  private async submitUserNotFound(
    application_id: string,
    claim: GeneratedClaim,
    credentials: Credentials,
    options: RequestOptions
  ): Promise<ApplicationSubmissionResponse> {
    if (claim.metadata?.randomizeSSN) {
      claim.claim.tax_identifier = generateSsn();
    }

    claim.claim.employer_fein =
      claim.metadata?.userNotFoundFEIN || generateFein();

    await this.updateApplication(application_id, claim.claim, options);
    await this.submitPartTwo(application_id, claim, credentials);
    const { first_name, last_name, fineos_absence_id } =
      await this.submitApplication(application_id, options, true);

    return {
      application_id,
      first_name,
      last_name,
      fineos_absence_id,
    };
  }

  async submitIntermittentLeavePeriods(
    fineosAbsenceId: string,
    claim: GeneratedClaim,
    intermittentLeaveEpisode: FineosIntermittentLeave,
    options?: RequestOptions,
    logger?: winston.Logger
  ) {
    const opts = options ?? (await this.getClaimantOptions());

    if (extractLeavePeriodType(claim.claim.leave_details) !== "Intermittent") {
      throw new Error(
        "Can only submit intermittent leave periods for an intermittent claim."
      );
    }

    assertValidClaim(claim.claim);

    // This call is required to sync the absence periods for the claim
    // so we can record actuals.
    await getClaimsByFineos_absence_id(
      { fineos_absence_id: fineosAbsenceId },
      opts
    );

    const period = getPeriodFromLeaveDetails(
      "intermittent_leave_periods",
      claim.claim.leave_details
    );
    const { start_date, end_date } = period as IntermittentLeavePeriods;

    if (!start_date || !end_date) {
      throw new Error("Can't determine start or end date for period.");
    }

    logger?.info(`Reporting for period ${start_date} to ${end_date}.`);

    // Timezones are painful, so we just trim out the first and last days of the period.
    const reportingPeriodStart = addBusinessDays(new Date(start_date), 1);
    const reportingPeriodEnd = subDays(new Date(end_date), 1);

    for (
      let reportingDate = reportingPeriodStart;
      reportingDate < reportingPeriodEnd;
      reportingDate = addDays(reportingDate, 1)
    ) {
      const requested_date = format(
        convertToTimeZone(reportingDate, { timeZone: "America/New_York" }),
        "yyyy-MM-dd"
      );
      logger?.info(
        `Requesting ${intermittentLeaveEpisode.spanHoursStart} hours for ${requested_date}`
      );
      await postReportIntermittentLeaveEpisode(
        { absence_case_id: fineosAbsenceId },
        {
          episodic_period: +intermittentLeaveEpisode.spanHoursStart,
          episodic_period_basis: "Hours",
          requested_date,
        },
        opts
      );
    }
  }

  //Claimaint Document upload for `/applications/${application_id}/documents` endpoint
  async claimaintDocumentUpload(
    application_id: string,
    claimantCredentials: { username: string; password: string },
    document: DocumentUploadRequest
  ) {
    const apiToken = await getClaimantToken(claimantCredentials);
    const pmflApiOptions = await this.getOptions(apiToken);

    const docRes = await postApplicationsByApplication_idDocuments(
      { application_id: application_id },
      document,
      pmflApiOptions
    ).catch((err) => {
      return err;
    });

    if (docRes.errno) {
      throw new Error(
        `request to /applications/${application_id}/documents failed, reason: write EPIPE`
      );
    }

    if (docRes.status !== 200) {
      throw new Error(`Unable to add document: ${JSON.stringify(docRes)}`);
    }

    return docRes;
  }

  // Leave Admin Claim document download
  async leaveAdminDocumentDownload(
    employer: Employer,
    fineos_absence_id: string,
    fineos_document_id: string
  ) {
    const adminCredentials: Credentials = getLeaveAdminCredentials(employer);

    const apiTokenLeaveAdmin = await getLeaveAdminToken(adminCredentials);
    const pfmlAdminOptions = await this.getOptions(apiTokenLeaveAdmin);

    const reqRes =
      await getEmployersClaimsByFineos_absence_idDocumentsAndFineos_document_id(
        {
          fineos_absence_id,
          fineos_document_id,
        },
        pfmlAdminOptions
      );

    if (reqRes?.status !== 200) {
      throw new Error(`Failed to download document: ${JSON.stringify(reqRes)}`);
    }

    return reqRes;
  }

  async syncLeaveDetails(
    fineosAbsenceId: string,
    credentials?: { username: string; password: string },
    options?: RequestOptions
  ): Promise<DetailedClaimResponse | undefined> {
    const opts = options ?? (await this.getClaimantOptions(credentials));

    return getClaimsByFineos_absence_id(
      { fineos_absence_id: fineosAbsenceId },
      opts
    ).then((response) => response.data.data);
  }
}

export interface SubmitOptions extends SubmitPartThreeOptions {
  leaveAdminCredentials?: Credentials;
}

export interface SubmitPartThreeParameters {
  applicationId: string;
  claim: GeneratedClaim;
  credentials: Credentials;
  fineosAbsenceId: string;
}

export interface SubmitPartThreeOptions {
  /**
   * Adds a delay between document upload and application completion. Expressed
   * in milliseconds.
   */
  completionDelay?: number;
  certificate_document_deferred?: boolean;
}
