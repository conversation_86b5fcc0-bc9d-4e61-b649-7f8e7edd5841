import { FineosPage } from "./FineosPage";

export class LeaveDetails extends FineosPage {
  async verifyWorkState() {
    // This only works if status is Pending or In Review (adjudication)
    await this.page.click("input[title='Edit Leave Request']");
    await this.onTab("Request Information", "Employment Information");
    const label = this.page.locator("text=USA Work State");
    const selectId = await label.getAttribute("for");
    const select = this.page.locator(`#${selectId}`);
    const selectedValue = await select.inputValue();
    if (selectedValue !== "20") {
      throw new Error("Work state was not set to MA");
    }
  }
}
