import { Page } from "@playwright/test";

import { gotoCase, waitForStablePage } from "../../util/playwright";
import { FineosPage } from "./FineosPage";

export class ServiceAgreementPage extends FineosPage {
  static async visit(page: Page, service_agreement_id: string) {
    await gotoCase(page, service_agreement_id);
    await waitForStablePage(page);
    return new ServiceAgreementPage(page);
  }

  async getCurrentVersion() {
    await this.onTab("Absence", "Leave Plans");
    return await this.page
      .locator('select[id$="serviceAgreementVersions"] option:checked')
      .first()
      .innerText();
  }

  /**
   * Returns an array of the service agreement’s leave plans.
   *
   * Optionally accepts a specific version for which to retrieve leave plans.
   */
  async getLeavePlans(version?: string) {
    await this.onTab("Absence", "Leave Plans");

    if (version) {
      await this.page
        .locator("[id$='_serviceAgreementVersions']")
        .selectOption(version);
      // There is a delay between selecting a version and the version’s leave
      // plans displaying in the GUI. This wait ensures the correct leave plans
      // are returned.
      await waitForStablePage(this.page);
    }

    return await this.page
      .locator("table[id*='statutoryListViewWidget'] tbody tr td:first-child")
      .allInnerTexts();
  }

  async getEndDate() {
    await this.onTab("Service Details");
    return await this.page
      .locator('span[id$="_endDate_WRAPPER"]')
      .first()
      .innerText();
  }

  async getStartDate() {
    await this.onTab("Service Details");
    return await this.page
      .locator('span[id$="_startDate_WRAPPER"]')
      .first()
      .innerText();
  }
}
