import { Page } from "@playwright/test";

import { DepartmentConfig } from "../../util/fineosRolePresets";
import * as util from "../../util/playwright";
import { FineosPage } from "./FineosPage";
import { RolesPage } from "./RolesPage";

export class ConfigPage extends FineosPage {
  static async visit(page: Page): Promise<ConfigPage> {
    await page.click(`a[aria-label='Configuration Studio']`);
    return new ConfigPage(page);
  }
  async setRoles(userId: string, roles: DepartmentConfig[]): Promise<void> {
    await this.roles(userId, async (rolePage) => {
      // Can't make this work without the wait.
      // Role selection doesn't work straigh away, and there's no UI indication of when it turns on.
      await this.page.waitForTimeout(2000);
      await rolePage.clearRoles();
      await rolePage.assignRoles(roles);
      await rolePage.applyRolesToUser();
    });
  }
  async roles(
    userId: string,
    cb: (page: RolesPage) => Promise<void>
  ): Promise<void> {
    await this.page.click(`text="Company Structure"`);
    await util.clickTab(this.page, "Users");
    await this.page.fill(`input[id$="userID"]`, userId);
    await this.page.click(`input[value="Search"]`);
    await this.page.click(`input[value="Edit"]`);
    // Lookup the user ID, then navigate to the edit roles page.
    await cb(new RolesPage(this.page));
  }
}
