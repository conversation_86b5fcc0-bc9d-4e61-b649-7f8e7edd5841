import { errors } from "@playwright/test";

import { FineosPage } from "./FineosPage";

// `undefined` leaves address part as-is; `null` clears it
interface FineosAddress {
  line1: string;
  line2?: string | null;
  line3?: string | null;
  state: string;
  city?: string | null;
  zipCode: string;
}

export class EmployerAddressPage extends FineosPage {
  async setAddress(address: FineosAddress): Promise<void> {
    await this.page.fill(
      "span#USEditCountryFormatAddressWidget input[id$='AddressLine1']",
      address.line1
    );
    if (address.line2 !== undefined) {
      await this.page.fill(
        "span#USEditCountryFormatAddressWidget input[id$='AddressLine2']",
        address.line2 ?? ""
      );
    }
    if (address.line3 !== undefined) {
      await this.page.fill(
        "span#USEditCountryFormatAddressWidget input[id$='AddressLine3']",
        address.line3 ?? ""
      );
    }
    if (address.city !== undefined) {
      await this.page.fill(
        "span#USEditCountryFormatAddressWidget input[id$='City']",
        address.city ?? ""
      );
    }
    await this.page.selectOption(
      "span#USEditCountryFormatAddressWidget select[id$='State']",
      { label: address.state }
    );
    await this.page.fill(
      "span#USEditCountryFormatAddressWidget input[id$='ZipCode']",
      address.zipCode
    );
    await this.page.click("span#footerButtonsBar input[id$='editPageSave']");
    const editing = await this.page.isVisible(
      "div.pageheader_flex_heading span.sub_header:has-text('Edit Address')"
    );
    if (editing) {
      await this.page.waitForLoadState("domcontentloaded");
      try {
        // if this times out, assume popup didn't appear, and we can move
        // on in the flow
        await this.page.click("input[id$='warningsPopup_yes']", {
          timeout: 10_000,
        });
      } catch (e) {
        if (!(e instanceof errors.TimeoutError)) {
          throw e;
        }
      }
    }
  }
}
