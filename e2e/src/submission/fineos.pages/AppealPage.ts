import { Documents } from "./Documents";
import { FineosPage, FineosPageCallback } from "./FineosPage";
import { Tasks } from "./Tasks";

export class AppealPage extends FineosPage {
  async documents(cb: FineosPageCallback<Documents>) {
    await this.onTab("Documents", "Documents For Case");
    await cb(new Documents(this.page));
  }

  async tasks(cb: FineosPageCallback<Tasks>) {
    await this.onTab("Tasks");
    await cb(new Tasks(this.page));
  }
}
