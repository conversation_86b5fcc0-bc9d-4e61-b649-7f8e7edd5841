import { Page } from "@playwright/test";
import delay from "delay";

import config from "../../config";
import { ClaimStatus, FineosCorrespondenceType } from "../../types";
import * as util from "../../util/playwright";
import { AdjudicationPage } from "./AdjudicationPage";
import { AppealPage } from "./AppealPage";
import { BenefitsExtensionPage } from "./BenefitsExtensionPage";
import { CancelPage } from "./CancelPage";
import { Documents } from "./Documents";
import { FineosPage, FineosPageCallback } from "./FineosPage";
import { LeaveDetails } from "./LeaveDetails";
import { OutstandingRequirements } from "./OutstandingRequirements";
import { RecordActualsPage } from "./RecordActualsPage";
import { Tasks } from "./Tasks";

export class ClaimPage extends FineosPage {
  static async visit(page: Page, id: string): Promise<ClaimPage> {
    await util.gotoCase(page, id);
    await util.waitForStablePage(page);
    return new ClaimPage(page);
  }

  async tasks(cb: FineosPageCallback<Tasks>): Promise<void> {
    await this.onTab(`Tasks`);
    await cb(new Tasks(this.page));
    await this.onTab(`Absence Hub`);
  }

  async closeAlertsIfShown() {
    const alertList = this.page.locator(".alertsList-wrap");
    const alertElementExists = await alertList.isVisible();

    if (alertElementExists) {
      const alertClasses = await alertList.getAttribute("class");

      if (alertClasses && !alertClasses.includes("hidden")) {
        await this.page.click(".bottom-close");
        // Allow for the alerts to be dismissed before further actions take place.
        await delay(250);
      }
    }
  }

  async adjudicate(cb: FineosPageCallback<AdjudicationPage>): Promise<void> {
    await this.page.click('input[type="submit"][value="Adjudicate"]');
    await cb(new AdjudicationPage(this.page));
    if (config("HAS_FR25_1")) {
      return;
    }
    await this.page.waitForSelector("#footerButtonsBar input[value='OK']", {
      state: "visible",
    });
    await this.page.click("#footerButtonsBar input[value='OK']");
  }

  async approve(): Promise<void> {
    if (config("HAS_FR25_1")) {
      await this.page
        .locator('input[type="submit"][value="Adjudicate"]')
        .click();
      await this.page
        .locator('button[data-testid="leave-request-secondary-actions"]')
        .click();
      await util.waitForStablePage(this.page);
      await this.page
        .locator('li[data-testid="leave-request-bulk-accept"]')
        .click();
      await this.page.locator('button:has-text("Progress Plans")').click();
      await util.waitForStablePage(this.page);
      await this.page
        .locator("#leave-request-progress-window-ok-button")
        .click();
      await util.waitForStablePage(this.page);
    } else {
      await this.page.click(
        'a[title="Approve the pending/in review leave request"]',
        {
          force: true, // eslint-disable-line playwright/no-force-option
          // This sometimes takes a while. Wait for it to complete.
          timeout: 60_000,
        }
      );
      await this.assertApprovedOrCompletedStatus();
    }
  }

  async assertClaimStatus(expected: ClaimStatus): Promise<void> {
    const status = await this.page.textContent(".key-info-bar .status dd");
    if (status !== expected) {
      throw new Error(
        `Expected status to be ${expected}, but it was ${status}`
      );
    }
  }

  // Useful for assertions when submitting large batches of claims with various end dates
  async assertApprovedOrCompletedStatus(): Promise<void> {
    const status = await this.page.textContent(".key-info-bar .status dd");
    if (status !== "Approved" && status !== "Completed") {
      throw new Error(
        `Expected status to be "Approved" or "Completed", but it was ${status}`
      );
    }
  }

  async deny(): Promise<void> {
    if (config("HAS_FR25_1")) {
      await this.page
        .locator('input[type="submit"][value="Adjudicate"]')
        .click();
      await this.page.locator('button:has-text("Progress Plans")').click();
      await util.waitForStablePage(this.page);
      await this.page
        .locator("#leave-request-progress-window-ok-button")
        .click();
      await util.waitForStablePage(this.page);
    } else {
      await this.page.click(
        'a[title="Deny the pending/in review leave request"]'
      );
      await this.page.selectOption("label:text-is('Denial Reason')", "5");
      await this.page.click('input[type="submit"][value="OK"]', {
        // This sometimes takes a while. Wait for it to complete.
        timeout: 60_000,
      });
      await this.assertClaimStatus("Declined");
    }
  }

  async documents(cb: FineosPageCallback<Documents>) {
    await this.onTab("Documents", "Documents For Case");
    await cb(new Documents(this.page));
  }

  async addActivity(activity: "Employer Reimbursement Process"): Promise<void> {
    await this.page.click("span:has-text('Add Activity')");
    await this.page.click(`span:has-text('${activity}')`);
  }

  async addCorrespondence(
    action: FineosCorrespondenceType,
    filename?: string
  ): Promise<void> {
    await this.page.click("span:has-text('Correspondence')");
    await this.page.click(`span:has-text('${action}')`);
    await this.page.waitForLoadState("domcontentloaded");
    if (filename) {
      await this.page.setInputFiles(
        'input[type="file"][id="uploadpath"]',
        filename
      );
    }
    await this.page.waitForLoadState("domcontentloaded");
    await this.page.click('#footerButtonsBar input[type="submit"][value="OK"]');
    await this.page.waitForLoadState("domcontentloaded");
  }

  convertDate(date: string): string {
    const parts = /(\d{4})-(\d{1,2})-(\d{1,2})/.exec(date);
    if (parts) {
      const [year, month, day] = parts.slice(1);
      return `${month}/${day}/${year}`;
    }

    throw Error(`Invalid date format for \`${date}\`. Expected yyyy-MM-dd.`);
  }

  async recordAdditionalTime(
    startDate: string,
    endDate: string
  ): Promise<void> {
    await this.page.click("[title='Register a Leave Extension or Transition']");
    await this.page.click("input[id*='another_reason_id_GROUP']");
    await this.page.click("input[title='Add Time Off Period']");
    await util.waitForStablePage(this.page);
    await this.page.selectOption("label:has-text('Absence Status')", "1");
    await this.page.waitForTimeout(350);
    await this.page.focus("label:has-text('Absence start date')");
    await this.page.fill(
      "label:has-text('Absence start date')",
      this.convertDate(startDate)
    );
    await this.page.waitForTimeout(350);
    await this.page.focus("label:has-text('Absence end date')");
    await this.page.fill(
      "label:has-text('Absence end date')",
      this.convertDate(endDate)
    );
    await this.page.click("input[id*='startDateAllDay_CHECKBOX']");
    await this.page.click("input[id*='endDateAllDay_CHECKBOX']");

    await this.clickOk();
    await this.clickFooterNext();
    await this.clickFooterNext();
    await this.clickFooterNext();
    await this.clickFooterNext();
    await this.clickOk();
  }

  async addAppeal() {
    await this.page.click('a[title="Add Sub Case"]');
    await this.page.click('a[title="Create Appeal"]');
    await util.waitForStablePage(this.page);
    return new AppealPage(this.page);
  }

  async leaveDetails(cb: FineosPageCallback<LeaveDetails>): Promise<void> {
    await this.onTab("Leave Details");
    await cb(new LeaveDetails(this.page));
  }

  async outstandingRequirements(
    cb: FineosPageCallback<OutstandingRequirements>
  ): Promise<void> {
    await this.onTab(`Outstanding Requirements`);
    await cb(new OutstandingRequirements(this.page));
  }

  async withdraw() {
    await this.page
      .locator('a[title="Withdraw the Pending Leave Request"]')
      .click();
    await this.page
      .getByLabel("Withdrawal Reason")
      .selectOption("Employee Withdrawal");
    await this.page.locator("[id$=_okButtonBean]").click();
    await util.waitForStablePage(this.page);
  }

  async visitCancelClaimPage() {
    await this.page.getByLabel("Record Cancellation").click();
    await util.waitForStablePage(this.page);
    return new CancelPage(this.page);
  }

  async visitBenefitsPage(): Promise<BenefitsExtensionPage> {
    await this.page.getByLabel("Add Time").click();
    return new BenefitsExtensionPage(this.page);
  }

  async visitRecordActualPage() {
    await this.page.getByLabel("Record Actual").click();
    await util.waitForStablePage(this.page);
    return new RecordActualsPage(this.page);
  }

  private async clickOk() {
    await util.waitForStablePage(this.page);
    await this.page.click("input:has-text('Ok')");
  }

  private async clickFooterNext() {
    await util.waitForStablePage(this.page);
    await this.page.click("#footerButtonsBar input:has-text('Next')");
  }
}
