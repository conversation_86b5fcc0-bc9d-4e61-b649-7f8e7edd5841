import { El<PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@playwright/test";
import { format } from "date-fns";
import delay from "delay";

import { PaymentMadeTableRow } from "../../types";
import * as util from "../../util/playwright";
import { Documents } from "./Documents";
import { FineosPage, FineosPageCallback } from "./FineosPage";

export type ReimbursementEntry = {
  leavePeriod: [Date, Date];
  amount: number;
};

function numToPaymentFormat(num: number): string {
  const decimal = num % 1 ? "" : ".00";
  return `${new Intl.NumberFormat("en-US", {
    style: "decimal",
  }).format(num)}${decimal}`;
}

async function transformPaymentRowToRecord(
  row: ElementHandle<SVGElement | HTMLElement>
) {
  const record: Record<keyof PaymentMadeTableRow, string> = {
    effectiveDate: "",
    netPayment: "",
    payeeName: "",
    status: "",
    periodStartDate: "",
    periodEndDate: "",
  };
  // Column indexes for payment rows using a 1 based index.
  const itemToColumnIndexMap: Record<keyof PaymentMadeTableRow, number> = {
    effectiveDate: 1,
    periodStartDate: 2,
    periodEndDate: 3,
    status: 6,
    netPayment: 7,
    payeeName: 8,
  };
  const entries = Object.entries(itemToColumnIndexMap) as [
    keyof PaymentMadeTableRow,
    number
  ][];
  for (const [key, val] of entries) {
    // effective date isn't displayed, so using waitForSelector will fail
    const el = await row.$(`:nth-child(${val})`);
    const innerText = await el?.innerText();
    if (!innerText) {
      throw Error(`Could not determine value for ${key} column value`);
    }
    record[key] = innerText;
  }
  return record;
}

export class PaidLeave extends FineosPage {
  async documents(cb: FineosPageCallback<Documents>) {
    await this.onTab("Documents", "Documents For Case");
    await cb(new Documents(this.page));
  }

  static async visit(page: Page, fineos_absence_id: string) {
    await util.gotoCase(page, fineos_absence_id);
    await util.waitForStablePage(page);
    const link = await page.waitForSelector(
      "a:has-text('Absence Paid Leave Case')"
    );
    await util.click(page, link);
    await page.waitForLoadState("domcontentloaded");
    return new PaidLeave(page);
  }

  async addErReimbursement(reimbursement: ReimbursementEntry): Promise<void> {
    const [start, end] = reimbursement.leavePeriod;
    await this.onTab(
      "Financials",
      "Recurring Payments",
      "Benefit Amount and Adjustments"
    );
    await this.page.click(
      `input[name^="BalancedPayeeOffsetsAndDeductions"]:has-text('Add')`
    );
    await util.waitForStablePage(this.page);
    await delay(350);
    await this.page.fill(
      'label:text-is("Start Date")',
      format(new Date(start), "MM/dd/yyyy")
    );
    await util.waitForStablePage(this.page);
    await delay(350);
    await this.page.fill(
      'label:text-is("End Date")',
      format(new Date(end), "MM/dd/yyyy")
    );
    await util.waitForStablePage(this.page);
    await delay(350);
    await this.page.fill(
      'input[type="text"][id$="adjustmentAmountMoney"]',
      numToPaymentFormat(reimbursement.amount)
    );
    await this.page.click('input[type="submit"][value="Add"]');
    await this.page.click("#footerButtonsBar input[value='OK']");
    await util.waitForStablePage(this.page);
    await delay(500);
  }

  async changeAutoPayStatus(status: boolean): Promise<void> {
    await this.onTab("Financials", "Recurring Payments", "Payment Details");
    await this.page.click("input[value='Edit']");
    await util.waitForStablePage(this.page);
    if (status) {
      await this.page.check("input[type='checkbox'][name*='AutoPay_CHECKBOX']");
    } else {
      await this.page.uncheck(
        "input[type='checkbox'][name*='AutoPay_CHECKBOX']"
      );
    }
    await util.waitForStablePage(this.page);
    await delay(350);
    const okBtn = await this.page.waitForSelector(
      "#footerButtonsBar input[value='OK']"
    );
    await okBtn.click();
    await util.waitForStablePage(this.page);
    await delay(350);
  }

  async approveCertPeriods(): Promise<void> {
    await this.onTab("Financials", "Recurring Payments", "Periods");
    await this.page.click("input[value='Edit']");
    await util.waitForStablePage(this.page);
    const selector = await this.page.waitForSelector(
      "select[id$='statusEnumBean']"
    );
    await selector.selectOption("1");
    await this.page.click("#footerButtonsBar input[value='OK']");
  }

  async editProcessingDates(startDate: Date): Promise<void> {
    await this.onTab("Financials", "Payment History", "Amounts Pending");
    await util.waitForStablePage(this.page);
    await delay(500);
    // get amount of payments for first pay period, this will vary depending on whether SIT/FIT is opted in or there is an ER reimbursement
    const paymentRowsToEdit = (
      await this.page.$$(
        `table[id^='amountspendingtabWidget'] tr:has-text('${format(
          startDate,
          "MM/dd/yyyy"
        )}')`
      )
    ).length;
    for (let i = 0; i < paymentRowsToEdit; i++) {
      await util.waitForStablePage(this.page);
      // Must query for payment rows every time we go through the edit process.
      const paymentRows = await this.page.$$(
        `table[id^='amountspendingtabWidget'] tr:has-text('${format(
          startDate,
          "MM/dd/yyyy"
        )}')`
      );
      // Go through payment rows for first pay period and find first row that doesn't contain today's date.
      // If the row contains today's date that row has already been edited
      for (let j = 0; j < paymentRows.length; j++) {
        const content = await paymentRows[j].innerText();
        if (content && !content.includes(format(new Date(), "MM/dd/yyyy"))) {
          await paymentRows[j].click();
          await this.page.click("input[value='Edit']");
          await util.waitForStablePage(this.page);
          await delay(500);
          const checkbox = await this.page.waitForSelector(
            'input[type="checkbox"][id$="overrideprocessingdate_CHECKBOX"]'
          );
          await checkbox.click();
          await util.waitForStablePage(this.page);
          await delay(500);
          await this.page.fill(
            'input[type="text"][id$="processingDate"]',
            format(new Date(), "MM/dd/yyyy")
          );
          await util.waitForStablePage(this.page);
          await delay(500);
          await this.page.click("#footerButtonsBar input[value='OK']");
          await util.waitForStablePage(this.page);
          await delay(500);
          break;
        }
      }
    }
    // validate payment processing update was successful
    await util.waitForStablePage(this.page);
    await delay(500);
    const matches = await this.page.$$(
      `table[id^='amountspendingtabWidget'] tr td:nth-child(3):has-text('${format(
        new Date(),
        "MM/dd/yyyy"
      )}')`
    );
    if (matches.length !== paymentRowsToEdit || paymentRowsToEdit === 0) {
      throw Error("Failed to override payment processing date");
    }
  }

  async goToPaymentsMade() {
    await this.onTab("Financials", "Payment History", "Payments Made");
    await util.waitForStablePage(this.page);
    await delay(250);
  }

  /**
   * While in a Paid Leave Case, navigate to the "Payments Made" tab
   * process and convert payment row(s) information into an array of
   * objects that contain payment information.
   * @returns Promise<PaymentsMadeTableRow[]>
   */
  async scrapePaymentsMade() {
    await this.goToPaymentsMade();
    const payments: PaymentMadeTableRow[] = [];
    const rows = await this.page.$$("table[id^='PaymentHistoryDetails'] tr");
    for (const row of rows) {
      payments.push(await transformPaymentRowToRecord(row));
    }
    return payments;
  }
}
