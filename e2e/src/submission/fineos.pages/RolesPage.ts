import { FineosRoles, FineosSecurityGroup } from "../../types";
import { DepartmentConfig } from "../../util/fineosRolePresets";
import { FineosPage } from "./FineosPage";

export class RolesPage extends FineosPage {
  async setRole(
    name: FineosSecurityGroup | FineosR<PERSON>s,
    member: boolean,
    supervisor: boolean
  ): Promise<void> {
    await this.page.click(
      `a[id^="LinkDepartmentToUserWidget_"][id$="_AvailableDepartments-Name-filter"]`
    );

    await this.page
      .waitForSelector(
        `input[id^="LinkDepartmentToUserWidget"][id$="_AvailableDepartments_Name_textFilter"]`
      )
      .then((filterInput) => filterInput.fill(name));
    await this.page.click(
      `input[id^="LinkDepartmentToUserWidget"][id$="_AvailableDepartments_cmdFilter"]`
    );
    await this.page.click(
      `table[id$="_AvailableDepartments"] tr:has-text("${name}")`
    );

    const setCheckboxState = async (
      selector: string,
      checked: boolean
    ): Promise<void> => {
      await this.page.waitForSelector(selector).then((checkbox) =>
        checkbox.isChecked().then((isChecked) =>
          // If current checkbox state is different from desired - click on it.
          // Have to use this because checkbox selection doesn't reset when linking/unlinking roles
          isChecked === checked ? null : checkbox.click()
        )
      );
    };
    await setCheckboxState(
      `input[type="checkbox"][id^="LinkDepartmentToUserWidget"][id$="_userToDeptMemberCheckbox_CHECKBOX"]`,
      member
    );
    await setCheckboxState(
      `input[type="checkbox"][id^="LinkDepartmentToUserWidget"][id$="_userToDeptSupervisorCheckbox_CHECKBOX"]`,
      supervisor
    );
    await this.page.click(`input[title="Link Department to User"]`);
  }
  async clearRoles(): Promise<void> {
    const checkboxSelector = `input[type="checkbox"][id^="LinkDepartmentToUserWidget"][id$="_LinkedDepartments_MasterMultiSelectCB_CHECKBOX"]`;
    await this.page.click(checkboxSelector);
    await this.page.waitForSelector(
      `table[id^="LinkDepartmentToUserWidget"][id$="_LinkedDepartments"] tr.ListRowSelected`
    );
    const activeRoles = await this.page.$$(
      `table[id^="LinkDepartmentToUserWidget"][id$="_LinkedDepartments"] tr`
    );
    for (const tr of activeRoles) {
      const rowText = await tr.textContent();
      // Don't unlink the Post-prod role by request from @mrossi113
      if (rowText?.includes("Post-Prod Admin(sec)")) {
        tr.$(`input[type="checkbox"]`).then((chbox) => chbox?.click());
      }
    }
    await this.page.click(`input[title="Unlink Department from User"]`);
  }
  async assignRoles(departments: DepartmentConfig[]): Promise<void> {
    for (const { role, memberOf, supervisorOf } of departments) {
      await this.setRole(role, memberOf, supervisorOf);
    }
  }
  async applyRolesToUser(): Promise<void> {
    await this.page.click(`input[title="Select to update the User"]`);
  }
}
