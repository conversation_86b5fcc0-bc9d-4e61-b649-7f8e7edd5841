import config from "../../config";
import { DocumentToReceive } from "../../generation/types";
import { FineosDocumentType, ReceiveEvidenceOptions } from "../../types";
import * as util from "../../util/playwright";
import { FineosPage } from "./FineosPage";

export class EvidencePage extends FineosPage {
  async receive(
    evidenceType: FineosDocumentType,
    options: ReceiveEvidenceOptions = {}
  ): Promise<void> {
    const resolvedOptions = this.withDefaults(options);

    await this.selectEvidence(evidenceType);
    await this.updateSelectedEvidence(resolvedOptions);
    if (config("HAS_FR25_1")) {
      return;
    } else {
      await this.waitForDecisionToUpdate(
        evidenceType,
        resolvedOptions.decision
      );
      await this.waitForReceiptToUpdate(evidenceType, resolvedOptions.receipt);
    }
  }

  async receiveAll(documents: DocumentToReceive[]) {
    for (const document of documents) {
      await util.waitForStablePage(this.page);
      if (config("HAS_FR25_1")) {
        const row = await this.findEvidenceRow(document.document_type);
        await row.waitFor({ state: "attached", timeout: 5000 });
        const isPending = await row
          .locator('[data-testid="decision"]:has-text("Pending")')
          .isVisible();

        if (isPending) {
          await this.receive(document.document_type, document);
        }
      } else {
        await this.receive(document.document_type, document);
      }
    }
    if (config("HAS_FR25_1")) {
      await this.page.click('input.Button[value="Close"]');
    }
  }

  private withDefaults(
    options: ReceiveEvidenceOptions
  ): Required<ReceiveEvidenceOptions> {
    const decision = options.decision ?? "Satisfied";
    const reason = options.reason || "Evidence has been reviewed and approved";
    const receipt = options.receipt ?? "Received";

    return {
      decision,
      reason,
      receipt,
    };
  }

  private async selectEvidence(evidence: FineosDocumentType) {
    const evidenceRow = await this.findEvidenceRow(evidence);
    if (config("HAS_FR25_1")) {
      await evidenceRow.locator('button:has-text("Manage Evidence")').click();
    } else {
      await evidenceRow.click();
    }
  }

  private async updateSelectedEvidence(
    options: Required<ReceiveEvidenceOptions>
  ) {
    await this.withinEvidencePopup(async () => {
      if (config("HAS_FR25_1")) {
        await this.page
          .locator(".ant-form-item", { hasText: "Evidence receipt" })
          .locator(".ant-select-selector")
          .click();

        await this.page
          .locator(".ant-select-item-option-content", {
            hasText: options.receipt,
          })
          .first()
          .click();

        await this.page
          .locator(".ant-form-item", { hasText: "Evidence decision" })
          .locator(".ant-select-selector")
          .click();

        await this.page
          .locator(".ant-select-item-option-content", {
            hasText: options.decision,
          })
          .first()
          .click();

        const reasonField = this.page.locator(".ant-form-item", {
          hasText: "Decision reason",
        });
        await reasonField.waitFor({ state: "visible" });
        await reasonField.locator("textarea").fill(options.reason);
      } else {
        await this.page.selectOption('label:text-is("Evidence Receipt")', {
          label: options.receipt,
        });
        await this.page.selectOption('label:text-is("Evidence Decision")', {
          label: options.decision,
        });
        await this.page.fill(
          'label:text-is("Evidence Decision Reason")',
          options.reason
        );
      }
    });
  }

  private async findEvidenceRow(evidenceType: FineosDocumentType) {
    if (config("HAS_FR25_1")) {
      const evidenceTypeMapping: Record<string, string> = {
        "Pregnancy/Maternity form": "Pregnancy and Maternity form",
      };
      const evidenceTypeName =
        evidenceTypeMapping[evidenceType] || evidenceType;
      return this.page.locator(
        `div[data-testid="evidence-row-${evidenceTypeName}"]`
      );
    } else {
      return this.page
        .locator("table[id*='evidenceResultListviewWidget'] tr", {
          has: this.page.locator(`text="${evidenceType}"`),
        })
        .first();
    }
  }

  private async withinEvidencePopup(cb: () => Promise<void>) {
    if (config("HAS_FR25_1")) {
      await this.page.waitForSelector(".ant-modal-content", {
        state: "visible",
      });
    } else {
      await this.page.click('input[value="Manage Evidence"]');
      await this.page.waitForSelector(".WidgetPanel_PopupWidget", {
        state: "visible",
      });
    }

    await cb();
    if (config("HAS_FR25_1")) {
      await this.page.click("#manage-evidence-ok-button");
      await this.page.waitForSelector(".ant-modal-content", {
        state: "hidden",
      });
    } else {
      await this.page.click('.WidgetPanel_PopupWidget input[value="OK"]');
    }
  }

  private async waitForReceiptToUpdate(
    evidence: FineosDocumentType,
    receipt: ReceiveEvidenceOptions["receipt"]
  ) {
    const row = await this.findEvidenceRow(evidence);
    await row.locator(`:nth-child(3):has-text('${receipt}')`).waitFor();
  }

  private async waitForDecisionToUpdate(
    evidence: FineosDocumentType,
    decision: ReceiveEvidenceOptions["decision"]
  ) {
    const row = await this.findEvidenceRow(evidence);
    await row.locator(`:nth-child(5):has-text('${decision}')`).waitFor();
  }
}
