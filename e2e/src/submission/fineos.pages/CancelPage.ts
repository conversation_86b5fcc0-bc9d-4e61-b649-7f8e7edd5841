import * as util from "../../util/playwright";
import { FineosPage } from "./FineosPage";

export class CancelPage extends FineosPage {
  async cancelClaim({
    cancellationStartDate,
  }: {
    cancellationStartDate: string;
  }) {
    await this.recordCancelledTime(cancellationStartDate);
    await util.waitForStablePage(this.page);
    await this.addAdditionalInformation();
    await util.waitForStablePage(this.page);
    await this.makeFinalDecision();
    return this;
  }

  async recordCancelledTime(cancellationStartDate: string) {
    await this.page.waitForSelector('role=cell[name="Known"]', {
      timeout: 5000,
    });
    const knownCells = await this.page
      .locator('role=cell[name="Known"]')
      .elementHandles();
    // If there is more than one leave period scheduled we want to cancel the most recent one
    if (knownCells.length === 1) {
      await knownCells[0].click();
    } else if (knownCells.length > 1) {
      await knownCells[1].click();
    }

    const cancelButtons = this.page.locator(
      'input[title="Record Cancelled Time for the selected absence period"]'
    );
    await cancelButtons.first().waitFor({ state: "attached", timeout: 5000 });

    const cancelButtonCount = await cancelButtons.count();

    if (cancelButtonCount === 1) {
      await cancelButtons.first().waitFor({ state: "visible", timeout: 5000 });
      await cancelButtons.first().click();
    } else if (cancelButtonCount > 1) {
      await cancelButtons.nth(1).waitFor({ state: "visible", timeout: 5000 });
      await cancelButtons.nth(1).click();
    }
    await this.page.fill('input[id$="_startDate"]', cancellationStartDate);
    await this.clickContinuationBtn("input:has-text('Ok')");
    await this.clickContinuationBtn("input:has-text('Next')");
  }

  private async addAdditionalInformation() {
    await this.page.selectOption('select[id$="reportedBy"]', "Employee");
    await util.waitForStablePage(this.page);
    await this.page.waitForSelector('select[id$="receivedVia"]', {
      state: "visible",
      timeout: 5000,
    });
    await this.page.selectOption('select[id$="receivedVia"]', {
      label: "Phone",
    });
    await this.page.selectOption(
      'select[id$="cancellationReason"]',
      "Employee Requested Cancellation"
    );

    await this.page.click(
      'input[type="checkbox"][id$="MasterMultiSelectCB_CHECKBOX"]'
    );
    await this.page.click('input[type="submit"][title="Apply"]');
    await this.clickContinuationBtn("input:has-text('Next')");
  }

  private async makeFinalDecision() {
    const dropdown = await this.page.waitForSelector(
      '//select[contains(@id, "period-decision-status")]',
      { timeout: 30_000 }
    ); // Wait for the drop down to render
    await dropdown.selectOption("Approved");

    await this.page.click(
      'input[type="checkbox"][id$="MasterMultiSelectCB_CHECKBOX"]'
    );
    await this.page.click('input[type="submit"][title="Apply"]');
    await this.clickContinuationBtn("input:has-text('Next')");
  }

  private async clickContinuationBtn(selector: string) {
    await util.waitForStablePage(this.page);
    await this.page.click(selector);
  }
}
