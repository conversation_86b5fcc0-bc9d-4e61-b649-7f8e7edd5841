import { clickTab } from "../../util/playwright";
import { FineosPage } from "./FineosPage";

export class EmployerStructurePage extends FineosPage {
  /**
   * Adds a location with a given address.
   */
  async addLocation(name: string, address: LocationAddress) {
    await this.page.getByTitle("Add a new location").click();
    await this.page.getByLabel("Name", { exact: true }).fill(name);
    await this.fillAddress(address);
    await this.clickFooterOk();
  }

  /**
   * Adds an organization unit with a given location and points of contact.
   */
  async addOrgUnit(
    name: string,
    location: string,
    pointsOfContact: readonly string[]
  ) {
    await this.page.getByTitle("Add New Unit").click();
    await this.page
      .locator("#organisationUnitDetailsWidget_un0_OrgUnitName")
      .fill(name);
    await this.selectPurposes();
    await clickTab(this.page, "Available");
    await this.linkLocation(location);
    await clickTab(this.page, "Linked");
    await this.linkPointsOfContact(pointsOfContact);
    await this.clickFooterOk();
  }

  /**
   * Resolves to `true` if the location is already added.
   *
   * Assumes there is at most one location.
   */
  async hasLocation(name: string) {
    const primaryWorkSiteHeader = this.page.locator("tr", {
      hasText: "Primary Work Site",
    });
    const primaryWorkSite = this.page.locator("tbody", {
      has: primaryWorkSiteHeader,
    });
    const matchCount = await primaryWorkSite
      .locator("tr > :nth-child(2)", { hasText: name })
      .count();
    return matchCount > 0;
  }

  /**
   * Resolves to `true` if the organization unit is already added.
   */
  async hasOrgUnit(name: string) {
    const orgUnitHeader = this.page.locator("tr", {
      // European spelling of "organization" uses an "s". FINEOS is Irish.
      hasText: "Organisation Units",
    });
    const orgUnits = this.page.locator("tbody", { has: orgUnitHeader });
    const matchCount = await orgUnits
      .locator("tr > :nth-child(1)", { hasText: name })
      .count();
    return matchCount > 0;
  }

  private clickFooterOk() {
    return this.page
      .locator("#footerButtonsBar")
      .getByRole("button", { name: "OK" })
      .click();
  }

  private async fillAddress(address: LocationAddress) {
    await this.page.getByLabel("Address Line 1").fill(address.street);
    await this.page.getByLabel("City").fill(address.city);
    await this.page.getByLabel("State").selectOption({ label: address.state });
    await this.page.getByLabel("Zip Code").fill(address.zipCode);
  }

  private async linkAdditionalPointOfContact(email: string) {
    await this.page.getByRole("button", { name: "Link", exact: true }).click();
    await this.page.getByRole("cell", { name: email }).click();
    await this.page.getByTitle("OK").click();
  }

  private async linkAdditionalPointsOfContact(
    pointsOfContact: readonly string[]
  ) {
    for (const email of pointsOfContact) {
      await this.linkAdditionalPointOfContact(email);
    }
  }

  private async linkFirstPointOfContact(email: string) {
    await this.page.getByRole("button", { name: "Add" }).click();
    await this.page.getByRole("cell", { name: email }).click();
    await this.page.getByTitle("OK").click();
  }

  private async linkLocation(location: string) {
    await this.page.getByRole("cell", { name: location }).click();
    await this.page.getByRole("button", { name: "Link" }).click();
  }

  private async linkPointsOfContact(pointsOfContact: readonly string[]) {
    // The buttons for linking a point of contact change after the first.
    const firstPointOfContact = pointsOfContact[0];
    await this.linkFirstPointOfContact(firstPointOfContact);
    const additionalPointsOfContact = pointsOfContact.slice(1);
    await this.linkAdditionalPointsOfContact(additionalPointsOfContact);
  }

  private async selectPurposes() {
    await this.page.getByLabel("Policy Admin").check();
    await this.page.getByLabel("Reporting", { exact: true }).check();
    await this.page.getByLabel("Billing").check();
    await this.page.getByLabel("Absence").check();
  }
}

export interface LocationAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}
