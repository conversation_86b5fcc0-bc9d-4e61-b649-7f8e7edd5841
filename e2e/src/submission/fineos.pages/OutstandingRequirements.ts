import { FineosPage } from "./FineosPage";

export class OutstandingRequirements extends FineosPage {
  async areRequirementsComplete() {
    return this.page.locator("input[value='Complete']").isDisabled();
  }

  async complete() {
    await this.page.click("input[value='Complete']");

    const widget = this.page
      .locator("#CompletionReasonWidget_PopupWidgetWrapper")
      .first();

    await widget
      .locator('label:text-is("Completion Reason")')
      .first()
      .selectOption({
        label: "Received",
      });

    await widget
      .locator('label:text-is("Completion Notes")')
      .fill("Complete Employer Confirmation");
    await widget.locator('input[value="Ok"]').click();
  }

  async returnToAbsenceHub() {
    await this.onTab("Absence Hub");
  }
}
