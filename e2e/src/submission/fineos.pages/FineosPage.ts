import { Locator, Page } from "@playwright/test";
import delay from "delay";

import PageObjectModel from "../../../playwright/pages/PageObjectModel";
import { clickTab, waitForStablePage } from "../../util/playwright";

/**
 * Base class which makes sure the page object is internally accessible without defining an explicit dependency.
 */
export class FineosPage extends PageObjectModel {
  protected async onTab(...path: string[]): Promise<void> {
    for (const part of path) {
      await clickTab(this.page, part);
      await delay(150);
    }

    await waitForStablePage(this.page);
  }

  async clickAntTab(label: string): Promise<void> {
    const tabsContainer = this.page.locator(".ant-tabs-nav-list");
    await tabsContainer.waitFor({ state: "visible" });
    const tabButton = tabsContainer.locator(".ant-tabs-tab-btn", {
      hasText: label,
    });

    await tabButton.waitFor({ state: "visible" });
    await tabButton.click();

    await waitForStablePage(this.page);
  }

  async chooseSelectOption(
    locator: Locator | Page,
    label: string,
    value: string
  ): Promise<void> {
    const dropdownId = await locator
      .locator(`label:has-text('${label}')`)
      .first()
      .getAttribute("for");
    await locator
      .locator(`select#${dropdownId}`)
      .first()
      .selectOption({ label: value });
    await delay(1000);
  }

  async fillInputWithLabel(
    locator: Locator | Page,
    label: string,
    value: string
  ): Promise<void> {
    const dropdownId = await locator
      .locator(`label:has-text('${label}')`)
      .first()
      .getAttribute("for");
    await delay(2000);
    const element = locator.locator(`input#${dropdownId}`).first();
    await element.focus();
    await element.fill(value);
    await delay(1000);
  }

  async fillDateWithLabel(label: string, value: string): Promise<void> {
    const dropdownId = await this.page
      .locator(`label:has-text('${label}')`)
      .first()
      .getAttribute("for");
    await this.page.focus(`input#${dropdownId}`);
    await delay(2000);
    await this.page.locator(`input#${dropdownId}`).first().fill(value);
    await waitForStablePage(this.page);
  }

  async clickFooterWidget(label: string) {
    await waitForStablePage(this.page);
    await this.page.click(`#footerButtonsBar input:has-text('${label}')`);
  }
}

export type FineosPageCallback<T extends FineosPage> = (
  fineosPage: T
) => Promise<void>;
