import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@playwright/test";
import { format } from "date-fns";
import delay from "delay";

import {
  FineosCloseTaskStep,
  FineosProcess,
  FineosTask,
  FineosTasksStatus,
} from "../../types";
import * as util from "../../util/playwright";
import { FineosPage } from "./FineosPage";

type TaskDetails = {
  description?: string;
  creation_date?: Date;
  status?: "Open" | "Closed";
};

export class Tasks extends FineosPage {
  async all(): Promise<void> {
    await util.waitForStablePage(this.page);
    await this.page.waitForSelector("input[type='radio'][value$='_allTasks']", {
      state: "visible",
    });
    await this.page.click("input[type='radio'][value$='_allTasks']");
    await this.page.waitForLoadState("domcontentloaded");
  }

  async assertDoesNotExist(name: FineosTask): Promise<void> {
    await this.all();
    const tasks = this.page.locator(
      `table[id*="TasksForCaseWidget"] tr td:nth-child(6)[title="${name}"]`
    );
    const count = await tasks.count();
    if (count > 0) {
      throw new Error(`Task ${name} found in table.`);
    }
  }

  async assertTaskExists(name: FineosTask): Promise<void> {
    try {
      await this.page.waitForSelector(
        `table[id*="TasksForCaseWidget"] tr td:nth-child(6)[title="${name}"]`
      );
    } catch (e) {
      throw new Error(`Task ${name} not found in table: ${e}`);
    }
  }

  async assertTaskStatus(
    name: FineosTask,
    status: FineosTasksStatus | FineosTasksStatus[]
  ): Promise<void> {
    await this.all();
    const table = await this.page.$("table[id*='TasksForCaseWidget']");
    if (!table) {
      throw new Error("Table not found");
    }

    const statuses = Array.isArray(status) ? status : [status];
    const statusSelectors = statuses
      .map((_status) => `tr td:nth-child(7)[title="${_status}"]`)
      .join(", ");

    const taskCells = await table.$$(statusSelectors);
    if (taskCells.length === 0) {
      throw new Error(
        `Expected to find a "${name}" task with status "${status}"`
      );
    }
  }

  async getTaskStatus(name: FineosTask): Promise<FineosTasksStatus> {
    await this.all();

    const table = await this.page.$("table[id*='TasksForCaseWidget']");
    if (!table) {
      throw new Error("Table not found");
    }

    const rows = await table.$$("tr");

    for (const row of rows) {
      const taskNameCell = await row.$("td:nth-child(6)");
      const statusCell = await row.$("td:nth-child(7)");

      if (!taskNameCell || !statusCell) {
        continue;
      }

      const taskName = await taskNameCell.getAttribute("title");
      if (taskName === name) {
        const status = await statusCell.getAttribute("title");
        if (!status) {
          throw new Error(`Status attribute missing for task "${name}"`);
        }
        return status as FineosTasksStatus;
      }
    }
    throw new Error(`Task "${name}" not found in the table.`);
  }

  async close(task: FineosTask): Promise<void> {
    await util.clickTab(this.page, "Tasks");
    await util.selectListTableRow(
      this.page,
      this.page.locator(`table[id*='TasksForCaseWidget']`).first(),
      task
    );
    await this.page.click('input[type="submit"][value="Close"]');
  }

  async closeAppealReview() {
    await this.close("Review Appeal");
    await this.clickFooterWidget("OK");
  }

  async closeConductHearing() {
    await this.page.click("td:has-text('Make Decision')");
    await this.page.click("td:has-text('Closed - Claim Decision Changed')");
    await this.clickFooterWidget("OK");
    return this;
  }

  async open(task: FineosTask): Promise<void> {
    await Promise.race([
      this.page.waitForNavigation(),
      this.page.click(`input[title="Add a task to this case"][type=submit]`),
    ]);
    await util.labelled(this.page, "Find Work Types Named").then(async (el) => {
      await el.fill(task);
      await el.press("Enter");
    });
    await Promise.race([
      this.page.waitForNavigation(),
      this.page.click('td[title="${taskName}"]'),
    ]);
    await Promise.race([
      this.page.waitForNavigation(),
      this.page.click("#footerButtonsBar input[value='Next']"),
    ]);
  }

  async closeWithAdditionalSelection(
    task: FineosTask,
    selection: FineosCloseTaskStep
  ): Promise<void> {
    await util.clickTab(this.page, "Tasks");
    await util.selectListTableRow(
      this.page,
      this.page.locator(`table[id*='TasksForCaseWidget']`).first(),
      task
    );
    await this.page.click('input[type="submit"][value="Close"]');
    await this.page.click(`td:has-text('${selection}')`);
    await this.page.click("#footerButtonsBar input[value='OK']");
    await this.page.waitForLoadState("domcontentloaded");
  }

  async triggerNotice(notice: FineosProcess): Promise<void> {
    await util.waitForStablePage(this.page);
    await util.clickTab(this.page, "Processes");
    await util.waitForStablePage(this.page);
    await this.page.click(`.TreeNodeElement:has-text('${notice}')`);
    await util.waitForStablePage(this.page);
    if (await this.page.isEnabled('input[type="submit"][value="Properties"]')) {
      await this.page.click('input[type="submit"][value="Properties"]');
      await util.waitForStablePage(this.page);
      await this.page.click('input[type="submit"][value="Continue"]');
      await util.waitForStablePage(this.page);
    }
  }

  /**
   *
   * @param name FineosTasks
   * Give a task name, while on the FINEOS tasks tab
   * find the most recently created task name by finding
   * the task with the most recent target date, then
   * click that task to reveal the task details
   */
  async clickMostRecentTask(name: FineosTask): Promise<void> {
    const locator = this.page.locator(
      `table[id*="TasksForCaseWidget"] tr:has-text("${name}")`
    );
    const els = await locator.elementHandles();
    const findTargetEl = els.reduce(async (target, currentElement) => {
      const targetElement = await Promise.resolve(target);
      if (!targetElement) {
        target = Promise.resolve(currentElement);
      } else {
        const currentElsTargetDate = (
          await (
            await currentElement.waitForSelector(":nth-child(8)")
          ).innerText()
        ).trim();
        const targetElsTargetDate = (
          await (
            await targetElement.waitForSelector(":nth-child(8)")
          ).innerText()
        ).trim();
        if (currentElsTargetDate > targetElsTargetDate) {
          target = Promise.resolve(currentElement);
        }
      }
      return target;
    }, Promise.resolve(undefined) as Promise<undefined | ElementHandle<Node>>);
    const el = await Promise.resolve(findTargetEl);
    if (el) {
      await el.click();
    }
  }

  async assertTaskDetails(details: TaskDetails, name: FineosTask) {
    await util.waitForStablePage(this.page);
    // even with the wait above the creationDate selector content will contain the
    // task target date (which is different) for a period of time. the hardcoded wait seems
    // to avoid that issue
    await delay(500);
    if (details.creation_date) {
      const creationDate = (
        await (
          await this.page.waitForSelector(
            'span[id="BasicDetailsUsersDeptWidget"] span[id$="_CreationDate"]'
          )
        ).innerText()
      ).trim();
      const dateWithStrippedTime = creationDate
        .match(/\d{2}\/\d{2}\/\d{4}/)
        ?.pop();
      if (!dateWithStrippedTime) {
        throw Error(`Couldn't determine task ${name} creation date`);
      }
      const formattedExpectedDate = format(details.creation_date, "MM/dd/yyyy");
      if (dateWithStrippedTime !== formattedExpectedDate) {
        throw Error(
          `Expected task creation date "${dateWithStrippedTime}" to match "${formattedExpectedDate}"`
        );
      }
    }
    if (details.description) {
      const description = (
        await (
          await this.page.waitForSelector('span[name$="_Description"]')
        ).innerText()
      ).trim();
      if (description !== details.description) {
        throw Error(
          `Expected description ${description} for task "${name}" to match "${details.description}" but failed`
        );
      }
    }
    if (details.status) {
      const status = await (
        await this.page.waitForSelector('span[id$="_Status"]')
      ).innerText();
      if (status !== details.status) {
        throw Error(
          `Expected status "${status}" for "${name}" task to match "${details.status}" but failed`
        );
      }
    }
  }

  async resetTasksTab() {
    await this.page.click("td[id$=Appeal_TasksView_cell]");
  }
}
