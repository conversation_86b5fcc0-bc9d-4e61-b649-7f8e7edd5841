import { errors, Locator, Page } from "@playwright/test";
import delay from "delay";
import pRetry from "p-retry";

import * as util from "../../util/playwright";
import { EmployerAddressPage } from "./EmployerAddressPage";
import { EmployerStructurePage } from "./EmployerStructurePage";
import { FineosPage, FineosPageCallback } from "./FineosPage";
import { ServiceAgreementPage } from "./ServiceAgreementPage";

type EmployerAddressType =
  | "Home"
  | "Business"
  | "Practice"
  | "Temporary"
  | "Mailing"
  | "Headquarters"
  | "Seasonal"
  | "Main"
  | "Alternate Mailing";

export class EmployerPage extends FineosPage {
  static async visit(
    page: Page,
    id: string,
    retries?: number
  ): Promise<EmployerPage> {
    await util.gotoEmployer(page, id);

    try {
      await pRetry(
        async () => {
          await page.waitForSelector(
            `span#ViewOrgSummaryWidget span.DataLabel[id$='taxNumber']:has-text('${id}')`
          );
        },
        {
          onFailedAttempt: async (error) => {
            console.log(
              `Attempt ${error.attemptNumber} failed. ${page.url()}.`
            );
            await util.searchEmployerFromOrganizationsTab(page, id);
            await delay(1000);
          },
          retries: retries || 5,
        }
      );
    } catch (e) {
      throw new Error(
        `Not able to go to employer page within ${retries || 5} retries: \n${e}`
      );
    }
    return new EmployerPage(page);
  }

  async visitCases(): Promise<void> {
    await this.onTab("Cases");
  }

  async visitEmployerStructure(cb: FineosPageCallback<EmployerStructurePage>) {
    await this.onTab("Employer Structure");
    await cb(new EmployerStructurePage(this.page));
  }

  async visitServiceAgreement(cb: FineosPageCallback<ServiceAgreementPage>) {
    await this.onTab("Cases");
    await this.page.click('a[id$="_serviceAgreementLink"]');
    await cb(new ServiceAgreementPage(this.page));
    await this.page.click(".key-info-bar a.PageLinkBeanDescriptor");
    return this;
  }

  async getMasterPlans(): Promise<MasterPlan[]> {
    await this.onTab("Portfolio", "Master Plans");
    const masterPlanLocator = this.page.locator(
      "table[id$='_masterPlansListviewWidgetId'] tbody tr"
    );
    const masterPlanRows = await masterPlanLocator.all();

    const scrapeMasterPlanTd = (row: Locator, label: string) =>
      row.locator(`td[id*='_masterPlansListviewWidgetId${label}']`).innerText();

    return Promise.all(
      masterPlanRows.map(async (row) => {
        const masterPlanId = await scrapeMasterPlanTd(row, "ID");
        const creationDate = await scrapeMasterPlanTd(row, "CreationDate");
        const status = await scrapeMasterPlanTd(row, "Status");

        return {
          masterPlanId,
          creationDate,
          status,
        };
      })
    );
  }

  async hasAddress(type: EmployerAddressType): Promise<boolean> {
    return this.page.isVisible(
      `span#PartyAddressesForPartyWidget table.ListTable tr:has-text('${type}')`
    );
  }

  async editAddress(
    type: EmployerAddressType,
    cb: FineosPageCallback<EmployerAddressPage>
  ): Promise<void> {
    await this.page.click(
      `span#PartyAddressesForPartyWidget table.ListTable tr:has-text('${type}')`
    );
    await this.page.click(
      "span#AddressesForPartyWidget table tbody tr input[id$='EditButton']"
    );
    await this.page.waitForLoadState("domcontentloaded");
    try {
      // if this times out, assume popup didn't appear, and we can move
      // on in the flow
      await this.page.click("input[id$='editChangeYesNoPopup_yes']", {
        timeout: 10_000,
      });
    } catch (e) {
      if (!(e instanceof errors.TimeoutError)) {
        throw e;
      }
    }
    await this.page.waitForSelector("span#USEditCountryFormatAddressWidget");
    return cb(new EmployerAddressPage(this.page));
  }

  async addAddress(
    type: EmployerAddressType,
    cb: FineosPageCallback<EmployerAddressPage>
  ): Promise<void> {
    await this.page.click(
      "span#AddressesForPartyWidget table tbody tr input[id$='NewButton']"
    );
    await this.page.selectOption(
      "div#PopupContainer select[id$='AddUsageDropDown_DropDown']",
      { label: type }
    );
    await this.page.click(
      "div#PopupContainer input[id$='AddUsageDropDown_yes']"
    );
    await this.page.waitForSelector("span#USEditCountryFormatAddressWidget");
    return cb(new EmployerAddressPage(this.page));
  }
}

interface MasterPlan {
  masterPlanId: string;
  creationDate: string;
  status: string;
}
