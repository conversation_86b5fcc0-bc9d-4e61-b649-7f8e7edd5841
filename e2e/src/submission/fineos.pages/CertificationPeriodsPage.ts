import delay from "delay";

import config from "../../config";
import { FineosPage } from "./FineosPage";

export class CertificationPeriodsPage extends FineosPage {
  async prefill(): Promise<void> {
    if (config("HAS_FR25_1")) {
      await this.page.click(
        'button[data-testid="prefill-certification-button"]'
      );
      await this.page.click("#prefill-certifications-confirm-popup-yes-button");

      await delay(150);
      await this.page.click('input.Button[value="Close"]');
    } else {
      await this.page.click(
        'input[value="Prefill with Requested Absence Periods"]'
      );
      await this.page.click('#PopupContainer input[value="Yes"]');
      await delay(150);
    }
  }
}
