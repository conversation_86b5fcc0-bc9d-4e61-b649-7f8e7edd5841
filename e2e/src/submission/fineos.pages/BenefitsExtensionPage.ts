import { waitForStablePage } from "../../util/playwright";
import { FineosPage } from "./FineosPage";

export class BenefitsExtensionPage extends FineosPage {
  async extendLeave({
    startDate,
    endDate,
    workPattern = "continuous",
    hasRelationship = false,
    dateOfBirth = startDate,
  }: {
    startDate: string;
    endDate: string;
    workPattern?: "continuous" | "reduced";
    hasRelationship?: boolean;
    dateOfBirth?: string;
  }): Promise<this> {
    if (hasRelationship) {
      await this.selectRelationshipReason();
    }

    await this.addTimeOffPeriod(workPattern);
    await this.enterExtensionLeaveDates(startDate, endDate, workPattern);
    await this.continueToCompleteDetailsTab();

    if (hasRelationship) {
      await this.fillRelationshipDetails(dateOfBirth);
    }

    await this.continueToWrapUpLeave();
    return this;
  }

  private async enterExtensionLeaveDates(
    startDate: string,
    endDate: string,
    workPattern: "continuous" | "reduced"
  ) {
    await this.chooseSelectOption(this.page, "Absence status", "Known");

    await this.fillDateWithLabel("Absence start date", startDate);
    await this.fillDateWithLabel("Absence end date", endDate);

    if (workPattern === "continuous") {
      await this.page.click(
        "input[type='checkbox'][id*='startDateAllDay_CHECKBOX']"
      );
      await waitForStablePage(this.page);
      await this.page.click(
        "input[type='checkbox'][id*='endDateAllDay_CHECKBOX']"
      );
      await waitForStablePage(this.page);
    } else {
      const hoursInputs = await this.page
        .locator("input[id^=hoursPerDayDetailsWidget][id$='hours']")
        .all();
      for (let i = 1; i < hoursInputs.length - 1; i++) {
        await hoursInputs[i].fill("4");
      }
    }
    await waitForStablePage(this.page);
    await this.page.click("input[title='OK']");
  }

  private async continueToWrapUpLeave(): Promise<this> {
    await this.clickFooterWidget("Next");
    await waitForStablePage(this.page);
    await this.clickFooterWidget("Next");
    await this.clickFooterWidget("OK");
    return this;
  }

  private async selectRelationshipReason(): Promise<void> {
    await this.page.click('input[type="radio"][value*="another_reason_id"]');
  }

  private async addTimeOffPeriod(
    workPattern: "continuous" | "reduced"
  ): Promise<void> {
    const addTimeButtonTitles = {
      continuous: "Add Time Off Period",
      reduced: "Add Reduced Schedule Period",
    } as const;

    await this.page.getByTitle(addTimeButtonTitles[workPattern]).click();
  }

  private async fillRelationshipDetails(dateOfBirth: string): Promise<void> {
    await waitForStablePage(this.page);
    await this.chooseSelectOption(
      this.page,
      "Primary Relationship to Employee",
      "Child"
    );
    await this.chooseSelectOption(this.page, "Qualifier 1", "Biological");

    await this.page
      .getByLabel("What is your Family Member's Date of Birth?")
      .fill(dateOfBirth);

    await this.page.getByLabel("First Name").fill("John");
    await this.page.getByLabel("Last Name").fill("Doe");
  }

  private async continueToCompleteDetailsTab(): Promise<this> {
    await this.clickFooterWidget("Next");
    await waitForStablePage(this.page);
    await this.clickFooterWidget("Next");
    return this;
  }
}
