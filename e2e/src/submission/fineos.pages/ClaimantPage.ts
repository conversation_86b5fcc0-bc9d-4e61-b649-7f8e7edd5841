import { Page } from "@playwright/test";
import { format } from "date-fns";

import { Address } from "../../_api";
import {
  CustomerSpecificDetails,
  FineosPhoneNumberType,
  PersonalIdentificationDetails,
  RequireNotNull,
} from "../../types";
import { FineosPage } from "./FineosPage";

export class ClaimantPage extends FineosPage {
  static async visit(page: Page, ssn: string): Promise<ClaimantPage> {
    await page.click('a[aria-label="Parties"]', { force: true }); // eslint-disable-line playwright/no-force-option
    await page.waitForLoadState("domcontentloaded");
    const ssnWithoutHyphens = ssn.replace(/-/g, "");
    await page.fill(
      "input[type='text'][name$='Social_Security_No._(SSN)']",
      ssnWithoutHyphens,
      { force: true } // eslint-disable-line playwright/no-force-option
    );
    await page.click('input[type="submit"][value="Search"]', {
      force: true, // eslint-disable-line playwright/no-force-option
    });
    const disabled = await page.isDisabled(
      "#footerButtonsBar input[value='OK']"
    );

    if (!disabled) {
      await page.click("#footerButtonsBar input[value='OK']");
    } else {
      throw new Error(`Fineos did NOT find any claimants w/SSN: ${ssn}`);
    }

    await page.waitForLoadState("domcontentloaded");
    return new ClaimantPage(page);
  }

  async addAddress(
    address: RequireNotNull<Address, "city" | "line_1" | "state" | "zip">
  ) {
    await this.page.waitForLoadState("domcontentloaded");

    const hasAddress = await this.page.$(
      "#addressesMultiPaint:has-text('Mailing address')"
    );
    if (hasAddress) {
      return;
    }

    await this.page.click("a:has-text('+ Add address')");
    const popup = this.page
      .locator("#addressPopupWidget_PopupWidgetWrapper")
      .first();

    await this.fillInputWithLabel(popup, "Address line 1", address.line_1);
    await this.fillInputWithLabel(popup, "City", address.city);
    await this.chooseSelectOption(popup, "State", address.state);
    await this.fillInputWithLabel(popup, "Zip code", address.zip);
    await this.page.click(
      '#addressPopupWidget_PopupWidgetWrapper input[title="OK"]'
    );
  }

  async addPhoneNumber(
    areaCode: string,
    lineNumber: string,
    type: FineosPhoneNumberType = "Phone"
  ) {
    await this.page.waitForLoadState("domcontentloaded");

    const hasPhoneNumber = await this.page.$(
      "#contactDetailsMultiPaint:has-text('Verified')"
    );
    if (hasPhoneNumber) {
      return;
    }

    await this.page.click("a:has-text('+ Add phone number')");
    const popup = this.page
      .locator("#addPhonePopupWidget_PopupWidgetWrapper")
      .first();

    await this.page
      .locator("[id$=_contactMethod]")
      .selectOption({ label: type });

    await this.fillInputWithLabel(popup, "Area code", areaCode);

    // This can't use fillInputWithLabel since two labels contain "Number".
    const lineNumberElement = popup
      .locator(`input[id$=telephoneNumber]`)
      .first();
    await lineNumberElement.focus();
    await lineNumberElement.fill(lineNumber);

    await this.page.click(
      '#addPhonePopupWidget_PopupWidgetWrapper input[title="OK"]'
    );
  }

  async editPhoneNumber(
    areaCode: string,
    lineNumber: string,
    type: FineosPhoneNumberType
  ) {
    await this.page.waitForLoadState("domcontentloaded");
    await this.page.click(
      '[id^=contactCard]:has-text("Number") span[title="Edit"]'
    );
    const popup = this.page.locator('[id^="editPhonePopupWidget_"]').first();
    await this.page
      .locator("[id$=_contactMethod]")
      .selectOption({ label: type });
    await this.fillInputWithLabel(popup, "Area code", areaCode);
    // This can't use fillInputWithLabel since two labels contain "Number".
    const lineNumberElement = popup
      .locator(`input[id$=telephoneNumber]`)
      .first();
    await lineNumberElement.focus();
    await lineNumberElement.fill(lineNumber);
    await this.page.click('[id$="Verified_GROUP"]');
    await this.page.click('[id^="editPhonePopupWidget"] input[title="OK"]');
    const verficationWindow = await this.page.$('[id$="isVerifedOk"]');

    if (verficationWindow) {
      await this.page.click('[id$="isVerifedOk"] input[value="Yes"]');
    }
  }

  async editCustomerSpecificInformation(
    changes: Partial<CustomerSpecificDetails>
  ) {
    await this.page.click("#classExtensionDetailsCardWidget [title='Edit']");
    const popup = this.page
      .locator("#cardEditPopupWidget_PopupWidgetWrapper")
      .first();

    if (typeof changes.mass_id === "string") {
      await this.fillInputWithLabel(popup, "Massachusetts ID", changes.mass_id);
    }

    if (typeof changes.out_of_state_id === "string") {
      await this.fillInputWithLabel(
        popup,
        "Out of State ID",
        changes.out_of_state_id
      );
    }

    if (changes.consent_to_share_data) {
      await this.page.check(
        "input[type='checkbox'][name*='ConsenttoShareData_CHECKBOX']"
      );
    } else if (changes.consent_to_share_data === false) {
      await this.page.uncheck(
        "input[type='checkbox'][name*='ConsenttoShareData_CHECKBOX']"
      );
    }

    await this.page.click(
      '#cardEditPopupWidget_PopupWidgetWrapper input[title="OK"]'
    );
  }

  async editOccupation(jobEndDate?: Date) {
    await this.page.click('a[title="Edit Occupation"]');

    await this.page
      .locator("[id$=_JobCat]")
      .selectOption({ label: "Self-Employed" });

    const formattedJobEndDate = jobEndDate
      ? format(jobEndDate, "MM/dd/yyyy")
      : "";

    await this.fillInputWithLabel(
      this.page,
      "Date Job Ended",
      formattedJobEndDate
    );

    await this.page.locator("#footerButtonsBar input[value='OK']").click();
  }

  async editPersonalInformation(
    changes: Partial<PersonalIdentificationDetails>
  ) {
    await this.page.click("#personalIdentificationCardWidget [title='Edit']");
    const popup = this.page
      .locator("#cardEditPopupWidget_PopupWidgetWrapper")
      .first();

    if (changes.date_of_birth) {
      await this.fillInputWithLabel(
        popup,
        "Date of birth",
        changes.date_of_birth
      );
    }

    await this.page.click(
      '#cardEditPopupWidget_PopupWidgetWrapper input[title="OK"]'
    );
  }

  /**
   * Gets the phone number listed on the claimant party record.
   *
   * Not tested with multiple phone numbers.
   */
  async getPhoneNumber() {
    const phoneNumberElement = await this.page.$("[id$='fullTelephoneNumber']");
    return (await phoneNumberElement?.innerText()) ?? null;
  }
}
