import * as util from "../../util/playwright";
import { FineosPage } from "./FineosPage";

export class RecordActualsPage extends FineosPage {
  async fillTimePeriod(startDate: string): Promise<void> {
    await this.recordTime(startDate);
    await this.addAdditionalInformation();

    await this.makeFinalDecision();
  }

  async recordTime(startDate: string) {
    await this.page.waitForSelector('role=cell[name="Episodic"]', {
      timeout: 15_000,
    });
    await this.page.locator('role=cell[name="Episodic"]').click();

    const cancelButtons = this.page.locator(
      'input[title="Record the actual for the selected absence period"]'
    );
    await cancelButtons.first().waitFor({ state: "attached", timeout: 5000 });
    await cancelButtons.click();
    await this.page.getByLabel("Absence start date").fill(startDate);

    await this.page.check(
      "#timeOffAbsencePeriodDetailsWidget_un31_startDateAllDay_CHECKBOX"
    );

    await this.page.check(
      "#timeOffAbsencePeriodDetailsWidget_un31_endDateAllDay_CHECKBOX"
    );

    await this.clickContinuationBtn("input:has-text('Ok')");

    await this.page.getByRole("button", { name: "Next" }).first().click();
  }

  private async addAdditionalInformation() {
    await this.page.selectOption('select[id$="reportedBy"]', "Employee");

    await this.page.waitForSelector('select[id$="receivedVia"]', {
      state: "visible",
      timeout: 5000,
    });
    await this.page.selectOption('select[id$="receivedVia"]', {
      label: "Phone",
    });

    await this.page.click('input[type="submit"][title="Apply"]');

    await this.clickContinuationBtn("input:has-text('Next')");
  }

  private async makeFinalDecision() {
    const dropdown = await this.page.waitForSelector(
      '//select[contains(@id, "period-decision-status")]',
      { timeout: 30_000 }
    );
    await dropdown.selectOption("Approved");
    await util.waitForStablePage(this.page);
    await this.page.waitForSelector(
      'input[type="checkbox"][id$="MasterMultiSelectCB_CHECKBOX"]',
      { state: "visible", timeout: 5000 }
    );

    await util.waitForStablePage(this.page);
    await this.page.click('td[id$="MultiSelectCol0"]');

    await this.page.click('input[type="submit"][title="Apply"]');

    await this.page.click('input.Button[type="submit"][value="Yes"]');

    await this.clickContinuationBtn("input:has-text('Next')");
  }

  private async clickContinuationBtn(selector: string) {
    await util.waitForStablePage(this.page);

    await this.page.locator(selector).first().click();
  }
}
