import delay from "delay";

import { IntermittentLeavePeriods } from "../../_api";
import { FineosPage } from "./FineosPage";

export class RequestDetailsPage extends FineosPage {
  async prefill(intermittentLeave: IntermittentLeavePeriods): Promise<void> {
    await this.page.waitForLoadState("domcontentloaded");

    let widget = this.page.locator("#captureEpisodicLeaveDetailsWidget");

    await this.page.click(
      "input[title='Edit an existing episodic absence period']"
    );
    await this.page.click("input:has-text('Yes')");
    widget = this.page
      .locator("#editPopupCaptureEpisodicLeaveDetailsWidget")
      .first();

    if (intermittentLeave.frequency) {
      await widget
        .locator('label:text-is("Occurs")')
        .fill(intermittentLeave.frequency.toString());
      await delay(250);
    }

    if (intermittentLeave.frequency_interval) {
      await widget
        .locator('label:text-is("Every")')
        .fill(intermittentLeave.frequency_interval.toString());
      await delay(250);
    }

    if (intermittentLeave.frequency_interval_basis) {
      await widget
        .locator('select:right-of(label:text-is("Every"))')
        .first()
        .selectOption({
          label: intermittentLeave.frequency_interval_basis,
        });
      await delay(250);
    }

    if (intermittentLeave.duration_basis) {
      await widget
        .locator('select:right-of(label:text-is("For"))')
        .first()
        .selectOption({
          label: intermittentLeave.duration_basis,
        });
      await delay(250);
    }

    if (intermittentLeave.duration) {
      await widget
        .locator('label:text-is("For")')
        .fill(intermittentLeave.duration.toString());
      await delay(250);
    }

    await this.page.click(
      "input[name*='editEpisodicAbsencePeriodPopupWidget'][value='OK']"
    );
  }
}
