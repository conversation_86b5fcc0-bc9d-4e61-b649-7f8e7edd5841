import delay from "delay";

import config from "../config";
import { GeneratedClaim } from "../generation/types";
import { ApplicationSubmissionResponse } from "../types";
import { generateAddress, generateDateOfBirth } from "../util/pii";
import { assertIsNotNull, assertValidClaim } from "../util/typeUtils";
import { ClaimPage, CreateNotificationPage, Fineos } from "./fineos.pages";

export default class FineosSubmitter {
  async submit(
    generatedClaim: GeneratedClaim,
    debug = false
  ): Promise<ApplicationSubmissionResponse> {
    return await Fineos.withBrowser(
      async (page): Promise<ApplicationSubmissionResponse> => {
        assertValidClaim(generatedClaim.claim);
        const claim = generatedClaim.claim;
        const isWithholdingTax = generatedClaim.is_withholding_tax;

        const createNotificationPage = await CreateNotificationPage.get(page);
        const claimantPage = await createNotificationPage.visitClaimantPage(
          claim.tax_identifier as string
        );

        await createNotificationPage.setPreferredLanguage(
          generatedClaim.preferredLanguage
        );

        await createNotificationPage.addAddress(generateAddress());

        await createNotificationPage.editPersonalInformation({
          date_of_birth: generateDateOfBirth(),
        });

        if (generatedClaim.claim.mass_id) {
          await claimantPage.editCustomerSpecificInformation({
            mass_id: generatedClaim.claim.mass_id,
          });
        }

        await createNotificationPage.startCreateNotification();

        const reason = claim.leave_details?.reason;

        assertIsNotNull(reason);

        // "Notification details" step, we are not changing anything here, so we just skip it.
        await createNotificationPage.clickNext();
        await createNotificationPage.fillOccupationDetailsStep(claim);
        await createNotificationPage.clickNext();
        await createNotificationPage.fillNotificationOptionsStep(reason);
        await createNotificationPage.clickNext();
        await createNotificationPage.fillReasonsForAbsenceStep(claim, reason);
        await delay(2000);
        await createNotificationPage.clickNext();
        await createNotificationPage.fillDatesOfAbsenceStep(claim);
        await createNotificationPage.clickNext();
        await createNotificationPage.fillWorkAbsenceDetailsStep(claim);
        await createNotificationPage.clickNext();
        await createNotificationPage.fillAdditionalAbsenceDetailsStep(
          claim,
          reason,
          isWithholdingTax
        );
        await createNotificationPage.clickNext();

        // Skip additional details step if needed
        if (
          reason === "Care for a Family Member" ||
          reason === "Military Exigency Family" ||
          reason === "Serious Health Condition - Employee" ||
          reason === "Military Caregiver" ||
          reason === "Child Bonding" ||
          reason === "Pregnancy/Maternity"
        ) {
          if (config("HAS_FR25_1")) {
            await page.locator("input[title='Finish']").first().click();
          } else {
            await createNotificationPage.clickNext();
          }
        }

        const caseNum = await createNotificationPage.getLeaveCaseNumber();

        if (reason === "Pregnancy/Maternity") {
          await createNotificationPage.clickNext();
        }

        try {
          if (claim.has_intermittent_leave_periods) {
            const claimPage = await ClaimPage.visit(page, caseNum);
            await claimPage.adjudicate(async (adjudication) => {
              const intermittentLeavePeriod =
                claim.leave_details.intermittent_leave_periods?.[0];

              if (intermittentLeavePeriod) {
                await adjudication.requestDetails(async (requestDetails) => {
                  await requestDetails.prefill(intermittentLeavePeriod);
                });
              }
            });
          }
        } catch (e) {
          e.message = `Failed to set intermittent leave period for ${caseNum}. ${e.message}`;
          throw e;
        }

        if (generatedClaim.employerResponse?.has_amendments) {
          const { employerResponse } = generatedClaim;
          const claimPage = await ClaimPage.visit(page, caseNum);

          await claimPage.documents(async (documents) => {
            await documents.add(
              "Employer Response to Leave Request - current version"
            );

            await documents.fillEmployerResponse(employerResponse);
          });
        }

        if (generatedClaim.claim.other_incomes) {
          const { other_incomes: otherIncomes } = generatedClaim.claim;
          const claimPage = await ClaimPage.visit(page, caseNum);

          await claimPage.documents(async (documents) => {
            await documents.add("Other Income - current version v2");
            await documents.fillOtherIncomes(otherIncomes);
          });
        }

        return {
          fineos_absence_id: caseNum,
          application_id: "",
          first_name: claim.first_name,
          last_name: claim.last_name,
        };
      },
      { debug }
    );
  }
}
