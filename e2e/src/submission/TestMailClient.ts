import Abort<PERSON><PERSON>roller from "abort-controller";
import { formatDuration } from "date-fns";
import { gql, GraphQLClient } from "graphql-request";
import fetch, { RequestInfo, RequestInit } from "node-fetch";

import config from "../config";
import NewRelicClient from "../NewRelicClient";
import { wait } from "../util/wait";

export type Email = {
  id: string;
  from: string;
  subject: string;
  text: string;
  html: string;
  timestamp: number;
  downloadUrl: string;
};

export type GetEmailsOpts = {
  address: string;
  subject?: string;
  subjectWildcard?: string;
  // Since Cypress serializes all arguments for nodeJS functions, we can't directly
  // use a regular expression and instead use an object that can define a `RegExp`.
  message?: string | { pattern: string; flags?: string };
  timestamp_from?: number;
  timestamp_to?: number;
  debugInfo?: DebugInfo;
  timeout?: number;
};

type NREmailSentLogs = {
  message: string;
  absenceCaseID: string;
  recipientType: string;
  triggerType: string;
  timestamp: number;
};

type DebugInfo = {
  fineosClaimID?: string;
  triggerType?: string;
  recipient?: string;
};

type FilterField =
  | "tag"
  | "envelope_from"
  | "envelope_to"
  | "from"
  | "to"
  | "cc"
  | "subject"
  | "text"
  | "html"
  | "sender_ip"
  | "id";

type FilterMatch = "exact" | "wildcard";

type FilterAction = "include" | "exclude";

type Filter = {
  field: FilterField;
  match: FilterMatch;
  action: FilterAction;
  value: string;
};

export default class TestMailClient {
  namespace: string;
  headers: Record<string, string>;
  apiKey: string;
  timeout: number;

  constructor(apiKey: string, namespace: string, timeout = 60_000) {
    this.headers = { Authorization: `Bearer ${apiKey}` };
    this.timeout = timeout;
    this.apiKey = apiKey;
    this.namespace = namespace;
  }

  async getEmails(opts: GetEmailsOpts): Promise<Email[]> {
    // We instantiate a new client for every call. This is the only way to share a single timeout across multiple
    // API calls (ie: we may make 5 requests, but we want to time out 120s from the time we started making calls).
    const controller = new AbortController();
    const timeout = setTimeout(
      () => controller.abort(),
      opts.timeout ?? this.timeout
    );
    const client = new GraphQLClient("https://api.testmail.app/api/graphql", {
      headers: this.headers,
      fetch: (url: RequestInfo, init: RequestInit = {}) =>
        fetch(url, {
          ...init,
          signal: controller.signal as AbortSignal,
        }),
    });

    try {
      const emails = await this._getMatchingEmails(client, opts);
      clearTimeout(timeout); // Clear the timeout to avoid hanging processes.
      return emails;
    } catch (e) {
      clearTimeout(timeout); // Clear the timeout to avoid hanging processes.
      throw e;
    }
  }

  /**
   * This method wraps our e-mail fetching to add additional functionality (body wildcard matching).
   *
   * If we've requested a message wildcard, we first query for all messages that match, then check to see if any of
   * the messages returned match the message wildcard. If they don't, we go back and look for more, excluding the
   * ones we've already found.
   *
   * Note, this function is not bounded and will continuously try to fetch new emails until a timeout is reached (handled externally)
   * When no emails are found, there is a 3 second delay -- after which emails are re-queried and the process repeats.
   */
  private async _getMatchingEmails(
    client: GraphQLClient,
    opts: GetEmailsOpts
  ): Promise<Email[]> {
    const { message } = opts;
    if (message === undefined) {
      return this._fetchEmails(opts, client);
    }

    // API is rate limited to 5 requests per second
    const maxRequests = 5;
    const delay = 1500;

    for (let iterations = 0, offset = 0; ; iterations++) {
      if (iterations > 0 && iterations % maxRequests === 0) {
        await wait(delay);
      }
      const candidates = await this._fetchEmails(opts, client, offset);
      offset += candidates.length;

      // If we've processed all emails, wait a brief moment and try again
      if (candidates.length === 0) {
        await wait(3000);
        continue;
      }

      const matches = candidates.filter((email) =>
        typeof message === "string"
          ? email.html?.includes(message)
          : new RegExp(message.pattern, message.flags).test(email.html)
      );

      if (matches.length > 0) {
        return matches;
      }
    }

    throw new Error("No matching emails found");
  }

  private async _fetchEmails(
    opts: GetEmailsOpts,
    client: GraphQLClient,
    offset = 0
  ): Promise<Email[]> {
    const filters: Filter[] = [];

    if (opts.subject) {
      filters.push({
        field: "subject",
        match: "exact",
        action: "include",
        value: opts.subject,
      });
    }
    if (opts.subjectWildcard) {
      filters.push({
        field: "subject",
        match: "wildcard",
        action: "include",
        value: opts.subjectWildcard,
      });
    }

    const tag = this.getTagFromAddress(opts.address);
    const variables = {
      tag: tag,
      namespace: this.namespace,
      advanced_filters: filters,
      timestamp_from: opts.timestamp_from,
      timestamp_to: opts.timestamp_to,
    };
    if (!variables.timestamp_to) {
      delete variables.timestamp_to;
    }
    try {
      const response = await client.request(unifiedQuery, {
        ...variables,
        offset,
        livequery: true,
      });
      return response.inbox.emails;
    } catch (e) {
      if (e.name === "AbortError") {
        const searchParams = new URLSearchParams({
          query: unifiedQuery,
          // Note: variables and headers can't currently be read from the query string in GraphQL.
        });
        const debugInfo = {
          "GraphQL URL": `https://api.testmail.app/api/graphql?${searchParams}`,
          "GraphQL Variables": JSON.stringify(
            // For debugging, add timestamp_to to variables, so the window is accurate.
            { ...variables, timestamp_to: opts.timestamp_to ?? Date.now() },
            undefined,
            4
          ),
          "GraphQL Headers": JSON.stringify(this.headers, undefined, 4),
          ...opts.debugInfo,
        };
        const timeoutDuration = {
          seconds: Math.round((opts.timeout || this.timeout) / 1000),
        };
        const absenceCaseID = debugInfo.fineosClaimID;
        const triggerType = debugInfo.triggerType;
        const envName = config("ENVIRONMENT");
        let emailSentStatusMessage: string;
        if (triggerType) {
          const emailSentLogs = await this.getNewRelicEmailSentLogs(
            envName,
            absenceCaseID,
            triggerType
          );
          emailSentStatusMessage =
            emailSentLogs.length > 0
              ? `PFML API sent e-mail successfully to SNOW.`
              : `PFML API did not deliver e-mail to SNOW.`;
        } else {
          emailSentStatusMessage =
            "Trigger Type not provided, therefore it cannot be verified if email was sent or not.";
        }
        throw new Error(
          `Timed out while looking for e-mail after ${formatDuration(
            timeoutDuration
          )}. This can happen when an e-mail is taking a long time to arrive, the e-mail was never sent, or you're looking for the wrong message.

          Debug information:
          ------------------

          ${Object.entries(debugInfo)
            .map(([key, value]) => `${key}: ${value}`)
            .join("\n\n")}

          Email Sent Status: ${emailSentStatusMessage}

          Subject: ${opts.subject || opts.subjectWildcard || ""}`
        );
      }
      throw e;
    }
  }

  getTagFromAddress(address: string): string {
    const re = new RegExp(`^${this.namespace}.(.*)@inbox.testmail.app$`);
    const match = address.match(re);
    if (!match || !(match[1].length > 0)) {
      throw new Error(
        `Oops, this doesn't look like a testmail address: ${address}`
      );
    }
    return match[1];
  }

  async getNewRelicEmailSentLogs(
    envName: string,
    absenceCaseID?: string,
    triggerType?: string
  ): Promise<NREmailSentLogs[]> {
    const nrClient = new NewRelicClient(
      config("NEWRELIC_APIKEY"),
      config("NEWRELIC_ACCOUNTID")
    );
    const queryString = `SELECT notification.absence_case_id, notification.recipient_type,
          notification.trigger, message from Log
        WHERE aws.logGroup in ('service/pfml-api-${envName}', 'service/pfml-api-${envName}/ecs-tasks')
        AND notification.absence_case_id = '${absenceCaseID}'
        AND message = 'Sent notification'
        AND notification.trigger = '${triggerType}'
        SINCE 1 week ago UNTIL now order by timestamp ASC limit max`;
    return await nrClient.nrql(queryString);
  }
}

/**
 * GraphQL queries:
 */
const unifiedQuery = gql`
  query getEmails(
    $namespace: String!
    $tag: String!
    $advanced_filters: [AdvancedFilter]
    $timestamp_from: Float
    $timestamp_to: Float
    $livequery: Boolean
    $offset: Int!
  ) {
    inbox(
      namespace: $namespace
      tag: $tag
      advanced_filters: $advanced_filters
      timestamp_from: $timestamp_from
      timestamp_to: $timestamp_to
      limit: 100
      livequery: $livequery
      offset: $offset
    ) {
      result
      message
      emails {
        id
        timestamp
        from
        subject
        text
        html
        downloadUrl
      }
      count
    }
  }
`;
