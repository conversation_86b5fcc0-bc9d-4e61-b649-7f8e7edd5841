/**
 * Manages the setting of feature flags either dynamically by querying component
 * versions, or directly by boolean values.
 *
 * These settings are overridden by environment variables or
 * environment-specific configuration in config.json.
 *
 * The implementation relies on a record of version strings stored in New Relic.
 * If the latest version string is invalid (e.g. a feature branch) it will find
 * the last valid version string as far back as history allows and use that.
 *
 * The `main` branch is a special case which satisfies all requirements because
 * it always has the latest code.
 */

import config, { configuration } from "./config";
import NewRelicClient from "./NewRelicClient";
import Version from "./Version";

const featureFlags = ["HAS_FR24_8"] as const;
const featureFlagConfig: Readonly<FeatureFlagConfig> = {
  HAS_FR24_8: { component: "fineos", version: "24.8.4-COM-C4.3.22" },
};

type FeatureFlagConfig = Record<FeatureFlag, FeatureFlagSetting>;
type FeatureFlag = (typeof featureFlags)[number];
type FeatureFlagSetting = boolean | VersionRequirement;

interface VersionRequirement {
  readonly component: Component;
  readonly version: string;
}

type Component = "api" | "fineos" | "portal";

/**
 * Returns a `Promise` that resolves to an `Array` of key:value pairs indicating
 * which feature flags are enabled.
 *
 * Gets feature flag settings by comparing the version strings of component
 * applications to `featureFlagConfig` as defined in `e2e/src/featureFlags.ts`.
 * Can be overridden by `e2e/config.json` or environment variables.
 */
export async function getFeatureFlagSettings(): Promise<[string, boolean][]> {
  if (featureFlagSettings === null) {
    return new Promise<[string, boolean][]>((resolve, reject) =>
      queue.push({
        reject,
        resolve: () => resolve(getFeatureFlagSettings()),
      })
    );
  }

  // This type assertion satisfies the compiler that Object.entries() will
  // return [string, boolean]. If there are no feature flags, the compiler will
  // infer featureFlagSettings to be Record<never, boolean>, which results in
  // Object.entries() returning [never, boolean]. This would change the
  // interface of this function in ways that break calling code. Casting is an
  // alternative solution, but it assumes that upstream changes will honor this
  // interface, which cannot be guaranteed.
  if (isRecordOfStringBoolean(featureFlagSettings)) {
    return Object.entries(featureFlagSettings);
  } else {
    return [];
  }
}

function isRecordOfStringBoolean(
  value: unknown
): value is Record<string, boolean> {
  if (typeof value !== "object" || value === null) {
    return false;
  }

  const entries = Object.entries(value);

  return (
    entries.length > 0 &&
    entries.every(([k, v]) => typeof k === "string" && typeof v === "boolean")
  );
}

/**
 * Returns a `Promise` that resolves to a `boolean` indicating if `featureFlag`
 * is enabled.
 *
 * Checks if `featureFlag` is enabled by comparing the version strings of
 * component applications to `featureFlagConfig` as defined in
 * `e2e/src/featureFlags.ts`. Can be overridden by `e2e/config.json` or
 * environment variables.
 */
export async function isFeatureFlagEnabled(
  featureFlag: FeatureFlag
): Promise<boolean> {
  if (featureFlagSettings === null) {
    return new Promise<boolean>((resolve, reject) =>
      queue.push({
        reject,
        resolve: () => resolve(isFeatureFlagEnabled(featureFlag)),
      })
    );
  }

  return featureFlagSettings[featureFlag];
}

let featureFlagSettings: Readonly<FeatureFlagSettings> | null = null;
type FeatureFlagSettings = Record<FeatureFlag, boolean>;
let queue: Request[] = [];

interface Request {
  readonly reject: (reason?: unknown) => void;
  readonly resolve: () => void;
}

initialize();

async function initialize() {
  try {
    featureFlagSettings = await initializeFeatureFlagSettings();
    queue.forEach((request) => request.resolve());
  } catch (exception: unknown) {
    queue.forEach((request) => request.reject(exception));
  } finally {
    // Allow processed requests in queue to be garbage collected.
    queue = [];
  }
}

async function initializeFeatureFlagSettings() {
  const featureFlagSettings: Record<string, boolean> = {};
  const componentVersions = await getComponentVersions();

  featureFlags.forEach((featureFlag) => {
    if (isConfiguredFeatureFlag(featureFlag)) {
      featureFlagSettings[featureFlag] = config(featureFlag);
    } else {
      const setting = featureFlagConfig[featureFlag];

      featureFlagSettings[featureFlag] = isVersionRequirement(setting)
        ? isVersionRequirementMet(setting, componentVersions)
        : setting;
    }
  });

  return featureFlagSettings as FeatureFlagSettings;
}

function isConfiguredFeatureFlag(featureFlag: FeatureFlag) {
  return configuredFeatureFlags.includes(featureFlag);
}

const configuredFeatureFlags = Object.keys(configuration).filter(isFeatureFlag);

function isFeatureFlag(value: string): value is FeatureFlag {
  return featureFlags.includes(value as FeatureFlag);
}

async function getComponentVersions(): Promise<Readonly<ComponentVersions>> {
  const [api, portal, fineos] = await Promise.all([
    fetchVersion("api"),
    fetchVersion("portal"),
    fetchVersion("fineos"),
  ]);

  return { api, fineos, portal };
}

type ComponentVersions = Record<Component, Version>;

async function fetchVersion(component: Component) {
  const env = config("ENVIRONMENT");

  const nrClient = new NewRelicClient(
    config("NEWRELIC_APIKEY"),
    +config("NEWRELIC_ACCOUNTID")
  );

  // New Relic’s data policy only retains data for the `version` custom event
  // for a period of 6 months.
  const query = `
    SELECT version
    FROM CustomDeploymentMarker
    WHERE environment = '${env}'
    AND component = '${component}'
    SINCE 6 MONTHS AGO UNTIL NOW
  `;

  const deploymentMarkers = await nrClient.nrql<DeploymentMarker>(query);

  if (deploymentMarkers.length === 0) {
    throw new Error(
      `No ${component} deployment markers found for environment "${env}".`
    );
  }

  for (const marker of deploymentMarkers) {
    const version = Version.create(marker.version);

    if (version.isValid) {
      return version;
    }
  }

  throw new Error(
    `No valid versions found for ${component} in environment "${env}".`
  );
}

interface DeploymentMarker {
  readonly timestamp: number;
  readonly version: string;
}

function isVersionRequirement(
  setting: FeatureFlagSetting
): setting is VersionRequirement {
  return typeof setting === "object";
}

function isVersionRequirementMet(
  requirement: VersionRequirement,
  componentVersions: Readonly<ComponentVersions>
) {
  const componentVersion = componentVersions[requirement.component];
  const requiredVersion = Version.create(requirement.version);
  return componentVersion.meets(requiredVersion);
}
