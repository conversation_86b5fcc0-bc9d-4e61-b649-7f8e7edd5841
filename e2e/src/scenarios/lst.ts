import { faker } from "@faker-js/faker";
import { addWeeks, nextMonday, subWeeks } from "date-fns";
import path from "path";

import { generateLeaveDates } from "../generation/LeaveDetails";
import { ScenarioSpecification } from "../generation/Scenario";
import { ClaimSpecification, EmployerResponseSpec } from "../generation/types";
import generateWorkPattern from "../generation/WorkPattern";
import * as CypressScenarios from "./cypress";

/**
 * Load & Stress Testing Scenarios.
 *
 */

function getIntermittentLeaveDates(): [Date, Date] {
  const today = new Date();
  const startingWeek = faker.date.between({
    from: subWeeks(today, 12),
    to: subWeeks(today, 4),
  });
  const start = nextMonday(startingWeek);
  const end = addWeeks(start, 3);
  return [start, end];
}

const olbWorkPattern = generateWorkPattern("0,480,480,480,480,480,0");
const earliestOLBStartDate = addWeeks(new Date(), 3).toDateString();

const otherLeavesAndBenefitsProps: Pick<
  ClaimSpecification,
  "other_incomes" | "employer_benefits" | "previous_leaves" | "leave_dates"
> = {
  other_incomes: [
    {
      income_type: "Earnings from another employment/self-employment",
      income_amount_dollars: 200,
      income_amount_frequency: "Per Week",
    },
  ],
  previous_leaves: [
    {
      type: "other_reason",
      leave_reason: "Bonding with my child after birth or placement",
      is_for_current_employer: true,
      leave_minutes: 2400,
      worked_per_week_minutes: 1200,
    },
  ],
  leave_dates: () =>
    generateLeaveDates(olbWorkPattern, { days: 10 }, earliestOLBStartDate),
};

const employerResponseLeavesAndBenefits: Partial<EmployerResponseSpec> = {
  employer_benefits: [
    {
      benefit_amount_dollars: 200,
      benefit_amount_frequency: "Per Week",
      benefit_type: "Short-term disability insurance",
      is_full_salary_continuous: false,
      employer_changes: "Added",
    },
  ],
  previous_leaves: [
    {
      type: "same_reason",
      leave_reason: "Bonding with my child after birth or placement",
      is_for_current_employer: true,
      leave_minutes: 1200,
      worked_per_week_minutes: 1200,
      employer_changes: "Added",
    },
  ],
};

const approval: Pick<ScenarioSpecification["claim"], "metadata"> = {
  metadata: {
    postSubmit: "approve",
  },
};

const submitActuals: Pick<ScenarioSpecification["claim"], "metadata"> = {
  metadata: {
    fineosIntermittentLeave: {
      spanHoursStart: "4",
      spanHoursEnd: "4",
      spanMinutesStart: "0",
      spanMinutesEnd: "0",
    },
    postSubmit: "approveAndSubmitActuals",
  },
};

const getDocPath = (filePath: string) => path.join("forms", "lst", filePath);

export const LSTOLB1_150KB: ScenarioSpecification = {
  ...CypressScenarios.BHAP1,
  claim: {
    ...CypressScenarios.BHAP1.claim,
    label: "PortalClaimSubmit - Other Leaves/Benefits  - 150KB file size",
    docs: {
      FOSTERPLACEMENT: {
        filePath: getDocPath("150KB.pdf"),
      },
      MASSID: {
        filePath: getDocPath("150KB.pdf"),
      },
    },
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
      ...employerResponseLeavesAndBenefits,
    },
    ...otherLeavesAndBenefitsProps,
    ...approval,
  },
};

// Portal claim submission with eligible employee
export const LSTBHAP_1MB: ScenarioSpecification = {
  ...CypressScenarios.BHAP1,
  claim: {
    ...CypressScenarios.BHAP1.claim,
    label: "PortalClaimSubmit - Bonding - 1MB file size",
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    docs: {
      FOSTERPLACEMENT: {
        filePath: getDocPath("1MB.jpg"),
      },
      MASSID: {
        filePath: getDocPath("1MB.jpg"),
      },
    },
    ...approval,
  },
};

export const LSTCHAP1_2MB: ScenarioSpecification = {
  employee: {
    wages: "eligible",
    mass_id: true,
  },
  claim: {
    label: "PortalClaimSubmit - Caring - 2.7MB file sizes",
    reason: "Care for a Family Member",
    work_pattern_spec: "0,720,0,720,0,720,0",
    docs: {
      MASSID: {
        filePath: getDocPath("2.7MB.png"),
      },
      CARING: {
        filePath: getDocPath("2.7MB.png"),
      },
    },
    shortClaim: true,
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    is_withholding_tax: true,
    ...approval,
  },
};

export const LSTOLB1_4MB: ScenarioSpecification = {
  ...CypressScenarios.BHAP1,
  claim: {
    ...CypressScenarios.BHAP1.claim,
    label: "PortalClaimSubmit - Other Leaves/Benefits  - 4.5MB file size",
    docs: {
      FOSTERPLACEMENT: {
        filePath: getDocPath("4.5MB.pdf"),
      },
      MASSID: {
        filePath: getDocPath("4.5MB.pdf"),
      },
    },
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
      ...employerResponseLeavesAndBenefits,
    },
    ...otherLeavesAndBenefitsProps,
    ...approval,
  },
};

// Portal claim submission with eligible employee
export const LSTBHAP_5MB: ScenarioSpecification = {
  ...CypressScenarios.BHAP1,
  claim: {
    ...CypressScenarios.BHAP1.claim,
    label: "PortalClaimSubmit - Bonding - 5MB file size",
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    docs: {
      FOSTERPLACEMENT: {
        filePath: getDocPath("5MB.pdf"),
      },
      MASSID: {
        filePath: getDocPath("5MB.pdf"),
      },
    },
    ...approval,
  },
};

export const LSTCHAP1_6MB: ScenarioSpecification = {
  employee: {
    wages: "eligible",
    mass_id: true,
  },
  claim: {
    label: "PortalClaimSubmit - Caring - 6MB file sizes",
    reason: "Care for a Family Member",
    work_pattern_spec: "0,720,0,720,0,720,0",
    docs: {
      MASSID: {
        filePath: getDocPath("6MB.pdf"),
      },
      CARING: {
        filePath: getDocPath("6MB.pdf"),
      },
    },
    shortClaim: true,
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    is_withholding_tax: true,
    ...approval,
  },
};

export const LSTOLB1_7MB: ScenarioSpecification = {
  ...CypressScenarios.BHAP1,
  claim: {
    ...CypressScenarios.BHAP1.claim,
    label: "PortalClaimSubmit - Other Leaves/Benefits  - 7MB file size",
    docs: {
      FOSTERPLACEMENT: {
        filePath: getDocPath("7MB.pdf"),
      },
      MASSID: {
        filePath: getDocPath("7MB.pdf"),
      },
    },
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
      ...employerResponseLeavesAndBenefits,
    },
    ...otherLeavesAndBenefitsProps,
    ...approval,
  },
};

// Portal claim submission with eligible employee
export const LSTBHAP_8MB: ScenarioSpecification = {
  ...CypressScenarios.BHAP1,
  claim: {
    ...CypressScenarios.BHAP1.claim,
    label: "PortalClaimSubmit - Bonding - 8MB file size",
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    docs: {
      FOSTERPLACEMENT: {
        filePath: getDocPath("8MB.pdf"),
      },
      MASSID: {
        filePath: getDocPath("8MB.pdf"),
      },
    },
    ...approval,
  },
};

export const LSTCHAP1_9MB: ScenarioSpecification = {
  employee: {
    wages: "eligible",
    mass_id: true,
  },
  claim: {
    label: "PortalClaimSubmit - Caring - 9.6MB file sizes",
    reason: "Care for a Family Member",
    work_pattern_spec: "0,720,0,720,0,720,0",
    docs: {
      MASSID: {
        filePath: getDocPath("9.6MB.pdf"),
      },
      CARING: {
        filePath: getDocPath("9.6MB.pdf"),
      },
    },
    shortClaim: true,
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    is_withholding_tax: true,
    ...approval,
  },
};

export const LST_INTERMITTENT_150KB: ScenarioSpecification = {
  employee: { wages: "eligible", mass_id: true },
  claim: {
    label: "PortalClaimSubmit - Medical - 150KB file sizes",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    docs: {
      MASSID: {
        filePath: getDocPath("150KB.pdf"),
      },
      HCP: {
        filePath: getDocPath("150KB.pdf"),
      },
    },
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    intermittent_leave_spec: {
      duration: 4,
      duration_basis: "Hours",
    },
    leave_dates: getIntermittentLeaveDates,
    is_withholding_tax: true,
    ...submitActuals,
  },
};

export const LST_INTERMITTENT_5MB: ScenarioSpecification = {
  employee: { wages: "eligible", mass_id: true },
  claim: {
    label: "PortalClaimSubmit - Medical - 5MB file sizes",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    docs: {
      MASSID: {
        filePath: getDocPath("5MB.pdf"),
      },
      HCP: {
        filePath: getDocPath("5MB.pdf"),
      },
    },
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    intermittent_leave_spec: {
      duration: 4,
      duration_basis: "Hours",
    },
    leave_dates: getIntermittentLeaveDates,
    is_withholding_tax: true,
    ...submitActuals,
  },
};

export const LST_INTERMITTENT_8MB: ScenarioSpecification = {
  employee: { wages: "eligible", mass_id: true },
  claim: {
    label: "PortalClaimSubmit - Medical - 8MB file sizes",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    docs: {
      MASSID: {
        filePath: getDocPath("8MB.pdf"),
      },
      HCP: {
        filePath: getDocPath("8MB.pdf"),
      },
    },
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    intermittent_leave_spec: {
      duration: 4,
      duration_basis: "Hours",
    },
    leave_dates: getIntermittentLeaveDates,
    is_withholding_tax: true,
    ...submitActuals,
  },
};
