/**
 *  Verifies work state (under Additional Employment Data) is set to MA for claims in adjudication.
 *
 */

import { Page } from "@playwright/test";
import csv from "csv-parser";
import fs from "fs";
import fsp from "fs/promises";
import path from "path";
import yargs from "yargs";

import { concurrencyWrapper } from "../commands/dataset/util";
import { ClaimPage, Fineos } from "../submission/fineos.pages";

const argv = yargs(process.argv.slice(2))
  .alias("h", "help")
  .help("help")
  .option("c", {
    alias: "concurrency",
    default: 1,
    demandOption: true,
    description:
      "Number of streaming-iterables to simultaneously run - not true concurrency.",
    type: "number",
  })
  .option("d", {
    alias: "directory-name",
    demandOption: true,
    description: "Folder containing submitted.csv",
    type: "string",
  })
  .option("s", {
    alias: "sample-size",
    default: 30,
    description: "Size of random sample from submitted.csv",
    type: "number",
  })
  .version(false)
  .parseSync();

const CONCURRENCY = argv.c;
const DIRECTORY_NAME = argv.d;
const SAMPLE_SIZE = argv.s;

const filePath = path.join(
  __dirname,
  "../../data",
  DIRECTORY_NAME,
  "submitted.csv"
);

const errored: CsvRow[] = [];
const invalids: CsvRow[] = [];

async function main() {
  let claims = await parseCSV(filePath);
  if (SAMPLE_SIZE > 0) {
    claims = randomSample(claims, SAMPLE_SIZE);
  }

  const total = claims.length;
  let checked = 0;
  let verified = 0;
  let invalid = 0;
  let errors = 0;

  await concurrencyWrapper(
    async (claim) => {
      checked++;
      try {
        await Fineos.withBrowser(
          async (page): Promise<void> => {
            const verification = await verifyWorkState(page, claim);
            if (verification) {
              verified++;
            } else {
              invalid++;
              invalids.push(claim);
            }

            console.info(
              `Checked ${checked} of ${total} claims: ${verified} verified, ${invalid} invalid. ${errors} errors (${
                (errors / verified) * 100
              }%).`
            );
          },
          {
            debug: false,
          }
        );
      } catch (e) {
        console.error(e);
        errored.push(claim);
        errors++;
      }
    },
    claims,
    CONCURRENCY
  );

  await maybeWriteFile("errored", errored);
  await maybeWriteFile("invalid", invalids);
}

interface CsvRow {
  [key: string]: string;
}

function parseCSV(filePath: string): Promise<CsvRow[]> {
  return new Promise((resolve, reject) => {
    const results: CsvRow[] = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on("data", (data: CsvRow) => {
        if (
          data["Fineos ID"] &&
          !data["Error"] &&
          data["Claim Status"] === "Adjudication"
        ) {
          results.push({
            ["Fineos ID"]: data["Fineos ID"],
          });
        }
      })
      .on("end", () => {
        resolve(results);
      })
      .on("error", (err: Error | null) => {
        reject(err);
      });
  });
}

async function maybeWriteFile(status: string, collection: CsvRow[]) {
  if (collection.length === 0) {
    return;
  }

  const fileName = `${status}-work-state-verification.csv`;
  await fsp.writeFile(
    path.join(__dirname, "../../data", DIRECTORY_NAME, fileName),
    collection.map((r) => `${r["Fineos ID"]}`).join("\r\n")
  );

  console.info(`Created ${fileName}`);
}

const verifyWorkState = async (page: Page, claim: CsvRow): Promise<boolean> => {
  console.log(`Verifying work state for ${claim["Fineos ID"]}`);

  const claimPage = await ClaimPage.visit(page, claim["Fineos ID"]);
  let verified = false;

  await claimPage.leaveDetails(async (leaveDetails) => {
    try {
      await leaveDetails.verifyWorkState();
      verified = true;
    } catch (e) {
      console.error(e);
      verified = false;
    }
  });

  return verified;
};

function randomSample(arr: CsvRow[], size: number) {
  // Shuffle the array
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }

  return arr.slice(0, size);
}

main();
