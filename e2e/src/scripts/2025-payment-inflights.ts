import { format } from "date-fns";
import path from "path";

import ClaimPool from "../generation/ClaimPool";
import dataDirectory from "../generation/DataDirectory";
import EmployeePool, { EmployeeGenerator } from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import { ScenarioSpecification } from "../generation/Scenario";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";
import * as scenarios from "../scenarios/2025-payment-inflights";

const QUANTITY_PER_SCENARIO = 30;

(async () => {
  const today = new Date();
  const storage = dataDirectory(
    `${format(today, "yyyy-MM-dd")}-payment-inflights-perf`
  );

  await storage.prepare();

  const firstEmployer = EmployerPool.generate(1);
  const secondEmployer = EmployerPool.generate(1);

  // Part 1: Employer generation.
  const employerPool = await EmployerPool.load(
    storage.employers
  ).orGenerateAndSave(() => EmployerPool.merge(firstEmployer, secondEmployer));

  await EmployerIndex.write(employerPool, storage.dir + "/employers.csv");
  await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));

  const occupations = [
    {
      employer: firstEmployer.pick(),
      wages: EmployeeGenerator.generateAllWages(60_000),
    },
    {
      employer: secondEmployer.pick(),
      wages: EmployeeGenerator.generateAllWages(40_000),
    },
  ];

  // Part 2: Employee generation.
  const employeePool = await EmployeePool.load(
    storage.employees
  ).orGenerateAndSave(() => {
    // Tying employee pools to scenarios isn't strictly necessary,
    // but it prevents bugs w/ mixing in multiple org employees w/ single employer scenarios
    // and gives some assurance that we're generating the right amounts.
    return EmployeePool.merge(
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "1a" },
        wages: 100_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "1b" },
        wages: 100_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "1b" },
        wages: 100_000,
      }),
      EmployeePool.generateWithOccupations(
        QUANTITY_PER_SCENARIO,
        { mass_id: true, metadata: { scenario: "2a" } },
        occupations
      ),
      EmployeePool.generateWithOccupations(
        QUANTITY_PER_SCENARIO,
        { mass_id: true, metadata: { scenario: "2b" } },
        occupations
      ),
      EmployeePool.generateWithOccupations(
        QUANTITY_PER_SCENARIO,
        { mass_id: true, metadata: { scenario: "3a" } },
        occupations
      ),
      EmployeePool.generateWithOccupations(
        QUANTITY_PER_SCENARIO,
        { mass_id: true, metadata: { scenario: "3b" } },
        occupations
      ),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "4a" },
        wages: 40_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "4b" },
        wages: 40_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "5a" },
        wages: 60_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "5b" },
        wages: 60_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "6a" },
        wages: 100_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "6b" },
        wages: 100_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "7a" },
        wages: 100_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "7b" },
        wages: 100_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "8a" },
        wages: 80_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "8b" },
        wages: 80_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "9a" },
        wages: 80_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "9b" },
        wages: 80_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "10a" },
        wages: 80_000,
      }),
      EmployeePool.generate(QUANTITY_PER_SCENARIO, firstEmployer, {
        mass_id: true,
        metadata: { scenario: "10b" },
        wages: 80_000,
      })
    );
  });

  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    employeePool,
    path.join(storage.dir, "employees.csv")
  );

  await employeePool.save(storage.employees, storage.usedEmployees);

  // Part 3: Claim generation.
  const generateClaims = (spec: ScenarioSpecification, count: number) =>
    ClaimPool.generate(employeePool, spec.employee, spec.claim, count);

  const _claimPool = await ClaimPool.load(
    storage.claims,
    storage.documents
  ).orGenerateAndSave(() =>
    ClaimPool.merge(
      ...Object.values(scenarios).map((spec) => {
        if (typeof spec?.claim.metadata?.quantity !== "number") {
          throw new Error("Missing 'quantity' value for scenario");
        }
        return generateClaims(spec, spec.claim.metadata.quantity);
      })
    )
  );

  // Save used employees with an extra field defining whether they've been used. Useful for training loads and other
  // scenarios that involve multiple batches of claims.
  await EmployeeIndex.writeWithConsumptionData(
    employeePool.consumptionStatus(),
    path.join(storage.dir, "employees_usage.csv")
  );
  // @default -->

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );
  // Catch and log any errors that bubble all the way up here.
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
