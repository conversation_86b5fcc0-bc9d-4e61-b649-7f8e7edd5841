/**
 * @file
 * Template for generating PFML test data.
 *
 * @abstract
 * Should be cloned in the same directory and customized before use.
 */

import { format } from "date-fns";
import path from "path";

import ClaimPool from "../generation/ClaimPool";
import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";
import * as scenarios from "../scenarios";

/**
 * @summary
 *
 * This is a data generation script, roughly consisting of three parts:
 *   1. Employer generation
 *   2. Employee generation
 *   3. Claim generation
 *
 * Clone this file to make a new script. Before use, take note of all @todo items listed below.
 *
 * NB. This script is mostly "idempotent"—if you run it multiple times, nothing bad happens.
 * Since we check if file exists each time, we may rerun this many times without overwriting existing data.
 * The script will generate additional DOR files on each run, but these can be safely ignored or redundant files deleted.
 *
 * @todo After customizing this script, you can generate the data by running:
 *   ```
 *     npx ts-node [PATH/TO/DATA_GEN_SCRIPT.ts]`
 *   ```
 *
 * By default, the resulting data will be placed within the `/data` directory at the project root.
 *
 * @todo After generating the data, follow-up steps may include:
 *   1. Upload the resulting DOR files to the appropriate S3 bucket based on the desired environment;
 *   2. After the employers are loaded into FINEOS, register Leave Admins for the employers by running:
 *     ```
 *       E2E_ENVIRONMENT=ENV_NAME npm run cli -- dataset register-las [-d data/DATA]
 *     ```
 *   2a. To register a single Leave Admin at a time, use the following command instead:
 *     ```
 *       npm run cli -- simulation register-leave-admin [FEIN] [WITHHOLDING_AMOUNT]
 *     ```
 *     Use the optional `--tag` argument if the leave admin email is different
 *     from the default.
 *   3. Submit the generated claims by running:
 *     ```
 *       npm run cli -- simulation submit <PATH/TO/CLAIMS_DIRECTORY> [--cooldownAfter number] [--concurrency number] [--errorLimit number]
 *     ```
 *   3a. If the claims have `postSubmit` steps, then Leave Admins must be registered before claim submission.
 */
(async () => {
  const today = new Date();
  // <!-- @default
  // @todo Rename the data directory as needed.
  const storage = dataDirectory(
    `${format(today, "yyyy-MM-dd")}-ADD-DESCRIPTIVE-NAME`
  );
  await storage.prepare();
  // @default -->

  // Part 1: Employer generation.
  const employerPool = await EmployerPool.load(
    storage.employers
  ).orGenerateAndSave(() => {
    // @todo Choose number of employers to generate, along with any other required features provided as an EmployeePickSpec argument.
    return EmployerPool.generate(30);
  });

  // <!-- @default
  await EmployerIndex.write(employerPool, storage.dir + "/employers.csv");
  await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));
  // @default -->

  // Part 2: Employee generation.
  const employeePool = await EmployeePool.load(
    storage.employees
  ).orGenerateAndSave(() => {
    // Define the kinds of employees we need to support. Each type of employee is generated as its own pool,
    // then we merge them all together.
    return EmployeePool.merge(
      // @todo Choose number of employees to generate.
      EmployeePool.generate(90, employerPool, {
        // @todo Define employee parameters.
        // Usually just the `wages` property.
        wages: "eligible",
        // @todo OPTIONAL: Add `metadata` property for ad hoc needs.
        metadata: { hair: "grey" },
      }),
      // @todo OPTIONAL: Add more employees with different properties.
      // @example Wages set to "ineligible" vs "eligible".
      EmployeePool.generate(10, employerPool, { wages: "ineligible" })
    );
  });

  // <!-- @default
  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    employeePool,
    path.join(storage.dir, "employees.csv")
  );
  // @default -->

  // Part 3: Claim generation.
  const _claimPool = await ClaimPool.load(
    storage.claims,
    storage.documents
  ).orGenerateAndSave(() => {
    /*
     * @todo Define the kinds of employees we need to support.
     *
     * Each type of employee is generated as its own pool,
     * then we merge them all together.
     *
     * @see ScenarioSpecification for expected structure.
     * @see `e2e/src/scenarios` for examples.
     *
     * NOTE: Scenarios may be defined here ad hoc, rather than being
     * defined within the `e2e/src/scenarios` directory, if they are
     * not meant to be reused elsewhere.
     */
    return ClaimPool.generate(
      employeePool,
      // @todo Set to the EmployeePickSpec of a given ScenarioSpecification.
      scenarios.BHAP1.employee,
      // @todo Set to the ClaimSpecification of a given ScenarioSpecification.
      // @todo To trigger `postSubmit` actions, the ClaimSpecification
      //   must include `{ metadata: { postSubmit: POST_SUBMIT_ACTION }`
      //   where POST_SUBMIT_ACTION is defined in src/PostSubmitAction.
      scenarios.BHAP1.claim,
      1
    );
  });
  // <!-- @default
  // Save used employees.
  // @todo OPTIONAL: Omit second arg to reuse employees.
  await employeePool.save(storage.employees, storage.usedEmployees);

  // Save used employees with an extra field defining whether they've been used. Useful for training loads and other
  // scenarios that involve multiple batches of claims.
  await EmployeeIndex.writeWithConsumptionData(
    employeePool.consumptionStatus(),
    path.join(storage.dir, "employees_usage.csv")
  );
  // @default -->

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );
  // Catch and log any errors that bubble all the way up here.
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
