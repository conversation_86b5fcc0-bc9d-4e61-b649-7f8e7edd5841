import { consume, filter, pipeline, tap } from "streaming-iterables";

import { GeneratedClaim } from "../generation/types";
import {
  logSubmissions,
  postProcess,
  submitAll,
  watchFailures,
} from "../submission/iterable";
import { PostSubmitCallback } from "../submission/PostSubmitAction/types";
import { getPortalSubmitter } from "../util/common";
import {
  ClaimStateTrackerInterface,
  SubmissionResult,
} from "./../submission/ClaimStateTracker";

export interface SubmitParameters {
  claims: AsyncIterable<GeneratedClaim>;
  tracker: ClaimStateTrackerInterface;
  completionDelay: number;
  concurrency: number;
  maxConsecErrors: number;
  cooldownAfter: number;
}

export interface SubmitOptions {
  postSubmit?: PostSubmitCallback;
}

/**
 * Submit a batch of claims to the system.
 *
 * The submission process uses async iterables to iterate through the claims to be sent, and send them off to the API.
 * This process is parallelized, so every step in the pipeline should be capable of operating
 * in parallel (ie: avoiding side effects)
 *
 * @param claims An AsyncIterable containing the claims to be submitted.
 * @param tracker A ClaimStateTracker instance to track submission progress and prevent resubmission of claims.
 * @param postSubmit An optional callback to invoke after submission of a single application.
 */
export async function submit(
  parameters: SubmitParameters,
  options: SubmitOptions = {}
): Promise<void> {
  const {
    claims,
    completionDelay,
    concurrency,
    cooldownAfter,
    maxConsecErrors,
    tracker,
  } = parameters;
  const { postSubmit } = options;
  /**
   * An iterator callback to filter out claims that have already been submitted.
   */
  const trackerFilter = filter(
    async (claim: GeneratedClaim): Promise<boolean> => {
      return !(await tracker.has(claim.id));
    }
  );

  /**
   * An iterator callback to mark claims as submitted as they are processed.
   */
  const trackerTrack = tap(async (result: SubmissionResult): Promise<void> => {
    tracker.set({
      claim_id: result.claim.id,
      fineos_absence_id: result.result?.fineos_absence_id ?? undefined,
      application_id: result.result?.application_id,
      error: result.error?.message,
    });
  });

  // Claims should be tracked as soon as they enter the submission pipeline.
  // This is useful because if our scripts suddenly to avoid reusing
  // previously submitted and untracked claims.
  // This creates overlapping absence requests which is something we'd like to avoid.
  const trackNewSubmissions = (claimId: string) => {
    tracker.set({
      claim_id: claimId,
      fineos_absence_id: undefined,
      application_id: undefined,
      error: undefined,
    });
  };
  // Use async iterables to consume all of the claims. Order of the processing steps in this pipeline matters!
  // Filtering must happen before submit, and tracking should happen before the failure watcher.
  await pipeline(
    () => claims,
    trackerFilter, // Filter out claims that have already been submitted.
    submitAll(getPortalSubmitter(), trackNewSubmissions, {
      completionDelay,
      concurrency,
    }), // Execute submission.
    postProcess(postSubmit ?? (() => Promise.resolve()), concurrency), // Run post-processing steps (this is optional).
    trackerTrack, // Track claims that have been submitted.
    logSubmissions, // Log submission results to console.
    (submission) => watchFailures(submission, maxConsecErrors, cooldownAfter), // Exit after 3 failures.
    consume
  );
}
