/**
 * This file contains typescript overrides for our extra Cypress extensions.
 *
 * @see ./cypress/support/commands.ts
 * @see https://github.com/cypress-io/cypress-realworld-app/tree/develop/cypress
 */
/// <reference types="cypress" />

// Import some types here. We'll reference them below.
type ApiResponse = import("./src/api").ApiResponse;
type ApplicationRequestBody = import("./src/api").ApplicationRequestBody;
type ApplicationResponse = import("./src/api").ApplicationResponse;
type BenefitYearResponse = import("./src/api").BenefitYearResponse;
type Credentials = import("./src/types").Credentials;
type Email = import("./src/submission/TestMailClient").Email;
type EmployerClaimRequestBody = import("./src/api").EmployerClaimRequestBody;
type EligibilityRequest = import("./src/api").EligibilityRequest;
type GetEmailsOpts = import("./src/submission/TestMailClient").GetEmailsOpts;
type POSTFinancialEligibilityResponse =
  import("./src/api").POSTFinancialEligibilityResponse;
type Result = import("pdf-parse").Result;
type DehydratedClaim = import("./src/generation/types").DehydratedClaim;
type Employer = import("./src/generation/Employer").Employer;
type MasterPlanData = import("./src/generation/Employer").MasterPlanData;
type EmployerPickSpec = import("./src/generation/Employer").EmployerPickSpec;
type pdf = import("pdf-parse").Result;
type Scenarios = import("./src/types").Scenarios;
type ClaimGenData = import("./src/types").ClaimGenData;
type EmployeeClaimGenData = import("./src/types").EmployeeClaimGenData;
type EmployeeClaimGenResponse = import("./src/types").EmployeeClaimGenResponse;
type ScenarioSpecs = import("./src/types").ScenarioSpecs;
type APIClaimSpec = import("./src/generation/types").APIClaimSpec;
type GeneratedClaim = import("./src/generation/types").GeneratedClaim;
type FineosExclusiveLeaveReasons =
  import("./src/generation/types").FineosExclusiveLeaveReasons;
type ApplicationSubmissionResponse =
  import("./src/types").ApplicationSubmissionResponse;
type DetailedClaimResponse = import("./src/api").DetailedClaimResponse;
type NrqlUNFLog = import("./src/types").NrqlUNFLog;
type TinyMce = import("@types/tinymce").EditorManager;
type FineosPresetKey = import("./src/util/fineosRolePresets").FineosPresetKey;

declare namespace Cypress {
  interface Cypress {
    // Declare the runner, which is an internal Cypress property.
    // We use it in stash/unstash to grab a unique ID for the run.
    runner: Cypress.Runner;
  }
  interface Chainable {
    typeMasked(
      text: string,
      options?: Partial<Cypress.TypeOptions>
    ): Chainable<Element>;
    stash(
      key: string,
      value: unknown,
      options?: { logToNewRelic: boolean }
    ): null;
    unstash<T>(key: string): Chainable<T>;
    stashLog(key: string, value: string | null | undefined): null;
    dependsOnPreviousPass(dependencies?: Mocha.Test[]): null;
    clickAction(text?: string): null;
    setTinyMceContent(tinyMceId: string, content: string): void;
    getTinyMceContent(tinyMceId: string): Chainable<string>;
    // Declare our custom tasks.
    task(
      event: "generateClaim",
      args: ClaimGenData
    ): Chainable<
      ScenarioSpecs[Scenarios]["claim"] extends APIClaimSpec
        ? DehydratedClaim
        : GeneratedClaim
    >;
    task(
      event: "generateClaimForEmployee",
      args: EmployeeClaimGenData
    ): Chainable<EmployeeClaimGenResponse>;
    task(
      event: "generateClaimForEmployeeWithoutClaims",
      args: ClaimGenData
    ): Chainable<EmployeeClaimGenResponse>;
    task(event: "getAuthVerification", mail: string): Chainable<string>;
    task(
      event: "generateClaimForEmployeeWithClaims",
      args: ClaimGenData
    ): Chainable<EmployeeClaimGenResponse>;
    task(
      event: "getEmployerFeins",
      employerFilePath: string
    ): Chainable<string[]>;
    task(
      event: "completeSSOLoginFineos",
      credentials?: Credentials
    ): Chainable<string>;
    task(
      event: "completeClaimantSsoLogin",
      credentials: Credentials
    ): Chainable<string>;
    task(
      event: "completeLeaveAdminSsoLogin",
      credentials: Credentials
    ): Chainable<string>;
    task(
      event: "startApplicationAndIDV",
      args: { claim: ApplicationRequestBody; credentials: Credentials }
    ): Chainable<string>;
    task(event: "generateCredentials"): Chainable<Credentials>;
    task(event: "getMasterPlanData", ssn: string): Promise<MasterPlanData>;
    task(event: "getParsedPDF", filename: string): Promise<pdf>;
    task(event: "deleteDownloadFolder", folderName: string): Chainable<true>;
    task(event: "waitForFileToBeReadable", file: string): Promise<null>;
    task(event: "readFile", file: string): Chainable<string | null>;
    // Supplying multiple forms of submitClaimToAPI seems to be necessary to provide typing for
    // both forms.
    task(
      event: "submitClaimToAPI",
      arg: DehydratedClaim
    ): Chainable<ApplicationSubmissionResponse>;
    task(event: "submitClaimToAPI", arg: GeneratedClaim): Chainable<never>;
    task(
      event: "submitStepsOneTwoOfPartOne",
      args: {
        application: ApplicationRequestBody;
        credentials: Credentials;
      }
    ): Chainable<string | undefined>;
    task(
      event: "submitClaimToAPI",
      arg: DehydratedClaim & {
        credentials?: Credentials;
        employerCredentials?: Credentials;
      }
    ): Chainable<ApplicationSubmissionResponse>;
    task(
      event: "submitClaimPartOneToAPI",
      args: {
        claim: DehydratedClaim;
        credentials?: Credentials;
      }
    ): Chainable<ApplicationResponse>;
    task(
      event: "submitClaimPartsTwoAndThreeToAPI",
      args: {
        claim: DehydratedClaim;
        credentials?: Credentials;
        submission: Submission;
      }
    ): Chainable<ApplicationSubmissionResponse>;
    task(
      event: "submitEmployerResponseToApi",
      args: {
        employerResponse: EmployerClaimRequestBody;
        identifiers: { employerFein: string; fineosAbsenceId: string };
      }
    ): Chainable<null>;
    task(
      event: "chooseFineosRole",
      arg: {
        /**ID of the account you want to switch the roles for */
        userId: string;
        /**Role preset you want to switch to. */
        preset: FineosPresetKey;
        debug?: boolean;
      }
    ): Chainable<null>;
    task(event: "pickEmployer", spec: EmployerPickSpec): Chainable<Employer>;
    task(event: "getEmails", opts: GetEmailsOpts): Chainable<Email[]>;
    task(
      event: "registerClaimant",
      options: Credentials & { captureAuthenticatorKey?: boolean }
    ): Chainable<true>;
    task(
      event: "registerLeaveAdmin",
      options: Credentials & {
        captureAuthenticatorKey?: boolean;
        fein?: string;
      }
    ): Chainable<true>;
    task(
      event: "registerAndVerifyLeaveAdmin",
      options: Credentials & {
        captureAuthenticatorKey?: boolean;
        fein: string;
        withholdings: number;
      }
    ): Chainable<true>;
    task(
      event: "findApplicationsLinkedNrLogs",
      args: {
        days: number;
      }
    ): Chainable<NrqlUNFLog[]>;
    task(
      event: "getApplicationById",
      args: {
        application_id: string;
      }
    ): Chainable<ApplicationResponse>;
    task<T>(event: "queryDb", query: string): Chainable<T>;
    /**
     * Gets the benefit years that apply to an absence case.
     */
    task(
      event: "getBenefitYears",
      fineosAbsenceId: string
    ): Chainable<BenefitYearResponse[]>;
    task(
      event: "checkFinancialEligibility",
      eligibilityRequest: EligibilityRequest
    ): Chainable<ApiResponse<POSTFinancialEligibilityResponse>>;
  }
}

declare namespace Cypress {
  interface ApplicationWindow {
    tinymce: TinyMce;
  }
}
