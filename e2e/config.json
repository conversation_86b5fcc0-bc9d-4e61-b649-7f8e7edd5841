{"_default": {"AUTHENTICATOR_KEY_TABLE": "massgov_pfml_e2e_auth_key", "AWS_REGION": "us-east-1", "HAS_ORGUNITS_SETUP": "false", "HAS_DEFAULT_ORGUNITS_SETUP": "false", "HAS_LIVECHAT_SETUP": "false", "NEWRELIC_ACCOUNTID": "2837112", "TESTMAIL_NAMESPACE": "gqzap", "LAP2_EC2_INSTANCEID": "i-074fd4158f3364b21", "PORTAL_USERNAME": "<EMAIL>", "SAVE_EMAIL_URLS": "false", "MMG_BUSINESS_BASE_URL": "https://admin.login.test.tss.mass.gov", "MMG_PERSONAL_BASE_URL": "https://personal.login.test.tss.mass.gov", "RMV_CHECK_IS_FULLY_MOCKED": "false", "GOOGLE_ANALYTICS_PATTERN": "**/g/collect*", "USE_MMG_SSO": "true", "LOG_TEST_RESULTS": "false", "USE_LATEST_RFI_NOTIFICATION_CONTENT": "true", "HAS_PROFILE_IDV_FEATURE": "false", "HAS_FR25_1": "false", "HAS_FINEOS_SSO_LANDING_PAGE": "false", "HAS_NOTICE_ENHANCEMENTS": "false"}, "training": {"PORTAL_BASEURL": "https://paidleave-training.mass.gov", "API_BASEURL": "https://paidleave-api-training.mass.gov/api/v1", "CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE": "./employees/concurrent-employees-2025-07-10.json", "CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE": "./employers/concurrent-employers-2025-07-10.json", "FINEOS_BASEURL": "https://trn-claims-webapp.masspfml.fineos.com/", "FINEOS_USERNAME": "CONTENT", "EMPLOYEES_FILE": "./employees/e2e-2025-06-23.json", "EMPLOYERS_FILE": "./employers/e2e-2025-06-23.json", "EMPLOYERS_MP_FILE": "./employers/e2e-2023-08-10-masterplan.json", "DOR_IMPORT_URI": "s3://massgov-pfml-training-agency-transfer/dor/received/", "DOR_ETL_ARN": "arn:aws:states:us-east-1:************:stateMachine:pfml-api-training-dor-fineos-etl", "S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET": "massgov-pfml-training-agency-transfer", "S3_MASSGOV_PFML_REPORTS_BUCKET": "massgov-pfml-training-reports", "S3_INTELLIGENCE_TOOL_BUCKET": "massgov-pfml-training-business-intelligence-tool", "FINEOS_DATA_EXPORTS_BUCKET": "s3://fin-sompre-data-export/TRN", "FINEOS_AWS_IAM_ROLE_ARN": "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-S0EP9ABIA02Z", "FINEOS_AWS_IAM_ROLE_EXTERNAL_ID": "8jFBtjr4UA@", "DB_HOSTNAME": "massgov-pfml-training.c6icrkacncoz.us-east-1.rds.amazonaws.com", "DB_NAME": "massgov_pfml_training", "RUN_BENEFIT_YEAR_TESTS": "false", "HAS_FINEOS_DEPARTMENTS_SET_UP": "false", "HAS_GOOGLE_ANALYTICS": "false", "HAS_ORGUNITS_SETUP": "false", "USE_LATEST_RFI_NOTIFICATION_CONTENT": "false", "HAS_FR25_1": "true"}, "trn2": {"PORTAL_BASEURL": "https://paidleave-trn2.dfml.eol.mass.gov", "API_BASEURL": "https://paidleave-api-trn2.dfml.eol.mass.gov/api/v1", "CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE": "./employees/concurrent-employees-2025-07-10.json", "CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE": "./employers/concurrent-employers-2025-07-10.json", "DUA_EMPLOYEES_FILE": "./employees/dua-employees-2025-05-01.json", "DUA_EMPLOYERS_FILE": "./employers/dua-employers-2025-05-01.json", "FINEOS_BASEURL": "https://trn2-claims-webapp.masspfml.fineos.com/", "FINEOS_USERNAME": "CONTENT", "EMPLOYEES_FILE": "./employees/e2e-2025-06-23.json", "EMPLOYERS_FILE": "./employers/e2e-2025-06-23.json", "EMPLOYERS_MP_FILE": "./employers/e2e-2023-08-10-masterplan.json", "DOR_IMPORT_URI": "s3://massgov-pfml-trn2-agency-transfer/dor/received/", "DOR_ETL_ARN": "arn:aws:states:us-east-1:************:stateMachine:pfml-api-trn2-dor-fineos-etl", "S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET": "massgov-pfml-trn2-agency-transfer", "S3_MASSGOV_PFML_REPORTS_BUCKET": "massgov-pfml-trn2-reports", "S3_INTELLIGENCE_TOOL_BUCKET": "massgov-pfml-trn2-business-intelligence-tool", "FINEOS_AWS_IAM_ROLE_ARN": "arn:aws:iam::************:role/somvrf-IAMRoles-CustomerAccountAccessRole-VsNw727kJDoq", "FINEOS_DATA_EXPORTS_BUCKET": "s3://fin-somvrf-data-export/TRN2", "FINEOS_AWS_IAM_ROLE_EXTERNAL_ID": "12345", "DB_HOSTNAME": "massgov-pfml-trn2.c6icrkacncoz.us-east-1.rds.amazonaws.com", "DB_NAME": "massgov_pfml_trn2", "RUN_BENEFIT_YEAR_TESTS": "false", "HAS_FINEOS_DEPARTMENTS_SET_UP": "false", "HAS_GOOGLE_ANALYTICS": "false", "HAS_ORGUNITS_SETUP": "false", "RMV_CHECK_IS_FULLY_MOCKED": "true", "USE_LATEST_RFI_NOTIFICATION_CONTENT": "false"}, "performance": {"PORTAL_BASEURL": "https://paidleave-performance.mass.gov", "API_BASEURL": "https://paidleave-api-performance.mass.gov/api/v1", "DUA_EMPLOYEES_FILE": "./employees/dua-employees-2025-05-01.json", "DUA_EMPLOYERS_FILE": "./employers/dua-employers-2025-05-01.json", "FINEOS_BASEURL": "https://perf-claims-webapp.masspfml.fineos.com/", "FINEOS_USERNAME": "CONTENT", "EMPLOYEES_FILE": "./employees/e2e-2025-06-23.json", "EMPLOYERS_FILE": "./employers/e2e-2025-06-23.json", "LST_EMPLOYEES_FILE": "./employees/lst-2025-02-25.json", "LST_EMPLOYERS_FILE": "./employers/lst-2025-02-25.json", "LST_PAYMENTS_FILE": "./src/artillery/payments/2025-01-16-performance-payments.json", "ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-2025-04-23-org-units.json", "ORGUNIT_EMPLOYERS_FILE": "./employers/e2e-2025-04-23-org-units.json", "NO_ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-2025-04-23-no-org-units.json", "CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE": "./employees/concurrent-employees-2025-07-10.json", "CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE": "./employers/concurrent-employers-2025-07-10.json", "DEFAULT_ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-2025-06-24-default-org-units.json", "CPS_EMPLOYEES_FILE": "./employees/cps-2025-02-04.json", "CPS_EMPLOYERS_FILE": "./employers/cps-2025-02-04.json", "EMPLOYERS_MP_FILE": "./employers/e2e-2022-11-15-masterplan.json", "EXEMPT_EMPLOYER_FILE": "./employers/e2e-2025-04-29-exempt.json", "EMPLOYEES_FOR_EXEMPT_EMPLOYER_FILE": "./employees/e2e-2025-04-29-for-exempt-employer.json", "DOR_IMPORT_URI": "s3://massgov-pfml-performance-agency-transfer/dor/received/", "DOR_ETL_ARN": "arn:aws:states:us-east-1:************:stateMachine:pfml-api-performance-dor-fineos-etl", "HAS_ORGUNITS_SETUP": "true", "HAS_DEFAULT_ORGUNITS_SETUP": "true", "S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET": "massgov-pfml-performance-agency-transfer", "S3_MASSGOV_PFML_REPORTS_BUCKET": "massgov-pfml-performance-reports", "S3_INTELLIGENCE_TOOL_BUCKET": "massgov-pfml-performance-business-intelligence-tool", "FINEOS_DATA_EXPORTS_BUCKET": "s3://fin-sompre-data-export/PERF", "FINEOS_AWS_IAM_ROLE_ARN": "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-S0EP9ABIA02Z", "FINEOS_AWS_IAM_ROLE_EXTERNAL_ID": "8jFBtjr4UA@", "DB_HOSTNAME": "massgov-pfml-performance.c6icrkacncoz.us-east-1.rds.amazonaws.com", "DB_NAME": "massgov_pfml_performance", "BENEFIT_YEAR_EMPLOYEE_FILE": "./employees/e2e-2025-02-20-benefit-year.json", "BENEFIT_YEAR_EMPLOYER_FILE": "./employers/e2e-2025-02-20-benefit-year.json", "RUN_BENEFIT_YEAR_TESTS": "true", "FINEOS_API_BASEURL": "https://perf-api.masspfml.fineos.com", "HAS_FINEOS_DEPARTMENTS_SET_UP": "false", "HAS_GOOGLE_ANALYTICS": "false", "HAS_FR25_1": "true"}, "uat": {"PORTAL_BASEURL": "https://paidleave-uat.mass.gov", "API_BASEURL": "https://paidleave-api-uat.mass.gov/api/v1", "DUA_EMPLOYEES_FILE": "./employees/dua-employees-2025-05-01.json", "DUA_EMPLOYERS_FILE": "./employers/dua-employers-2025-05-01.json", "FINEOS_BASEURL": "https://uat-claims-webapp.masspfml.fineos.com/", "FINEOS_USERNAME": "OASIS", "EMPLOYEES_FILE": "./employees/e2e-2025-06-23.json", "EMPLOYERS_FILE": "./employers/e2e-2025-06-23.json", "ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-2024-08-26-uat-org-units.json", "ORGUNIT_EMPLOYERS_FILE": "./employers/e2e-2023-01-19-uat-orgunits.json", "NO_ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-2024-08-26-uat-no-org-units.json", "CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE": "./employees/concurrent-employees-2025-07-10.json", "CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE": "./employers/concurrent-employers-2025-07-10.json", "CPS_EMPLOYEES_FILE": "./employees/cps-2025-02-04.json", "CPS_EMPLOYERS_FILE": "./employers/cps-2025-02-04.json", "EMPLOYERS_MP_FILE": "./employers/e2e-2022-12-08-masterplan.json", "DOR_IMPORT_URI": "s3://massgov-pfml-uat-agency-transfer/dor/received/", "DOR_ETL_ARN": "arn:aws:states:us-east-1:************:stateMachine:pfml-api-uat-dor-fineos-etl", "HAS_ORGUNITS_SETUP": "true", "S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET": "massgov-pfml-uat-agency-transfer", "S3_MASSGOV_PFML_REPORTS_BUCKET": "massgov-pfml-uat-reports", "S3_INTELLIGENCE_TOOL_BUCKET": "massgov-pfml-uat-business-intelligence-tool", "FINEOS_DATA_EXPORTS_BUCKET": "s3://fin-sompre-data-export/UAT", "FINEOS_AWS_IAM_ROLE_ARN": "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-S0EP9ABIA02Z", "FINEOS_AWS_IAM_ROLE_EXTERNAL_ID": "8jFBtjr4UA@", "DB_HOSTNAME": "massgov-pfml-uat.c6icrkacncoz.us-east-1.rds.amazonaws.com", "DB_NAME": "massgov_pfml_uat", "BENEFIT_YEAR_EMPLOYEE_FILE": "./employees/e2e-2025-02-20-benefit-year.json", "BENEFIT_YEAR_EMPLOYER_FILE": "./employers/e2e-2025-02-20-benefit-year.json", "RUN_BENEFIT_YEAR_TESTS": "true", "HAS_FINEOS_DEPARTMENTS_SET_UP": "false", "HAS_GOOGLE_ANALYTICS": "false", "LST_PAYMENTS_FILE": "./src/artillery/payments/2025-02-04-uat-payments.json", "HAS_FR25_1": "true"}, "breakfix": {"PORTAL_BASEURL": "https://paidleave-breakfix.eol.mass.gov", "API_BASEURL": "https://paidleave-api-breakfix.eol.mass.gov/api/v1", "DUA_EMPLOYEES_FILE": "./employees/dua-employees-2025-05-01.json", "DUA_EMPLOYERS_FILE": "./employers/dua-employers-2025-05-01.json", "FINEOS_BASEURL": "https://pfx-claims-webapp.masspfml.fineos.com", "FINEOS_USERNAME": "OASIS", "TESTMAIL_NAMESPACE": "gqzap", "EMPLOYEES_FILE": "./employees/e2e-2025-06-23.json", "EMPLOYERS_FILE": "./employers/e2e-2025-06-23.json", "ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-org-units-2024-08-19.json", "ORGUNIT_EMPLOYERS_FILE": "./employers/e2e-org-units-2024-08-19.json", "NO_ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-no-org-units-2024-08-19.json", "CPS_EMPLOYEES_FILE": "./employees/cps-2025-02-04.json", "CPS_EMPLOYERS_FILE": "./employers/cps-2025-02-04.json", "CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE": "./employees/concurrent-employees-2025-07-10.json", "CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE": "./employers/concurrent-employers-2025-07-10.json", "EMPLOYERS_MP_FILE": "./employers/e2e-2023-08-10-masterplan.json", "DOR_IMPORT_URI": "s3://massgov-pfml-breakfix-agency-transfer/dor/received/", "DOR_ETL_ARN": "arn:aws:states:us-east-1:************:stateMachine:pfml-api-breakfix-dor-fineos-etl", "S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET": "massgov-pfml-breakfix-agency-transfer", "S3_MASSGOV_PFML_REPORTS_BUCKET": "massgov-pfml-breakfix-reports", "S3_INTELLIGENCE_TOOL_BUCKET": "massgov-pfml-breakfix-business-intelligence-tool", "FINEOS_DATA_EXPORTS_BUCKET": "s3://fin-sompre-data-export/PFX", "FINEOS_AWS_IAM_ROLE_ARN": "arn:aws:iam::************:role/sompre-IAMRoles-CustomerAccountAccessRole-S0EP9ABIA02Z", "FINEOS_AWS_IAM_ROLE_EXTERNAL_ID": "8jFBtjr4UA@", "DB_HOSTNAME": "massgov-pfml-breakfix.c6icrkacncoz.us-east-1.rds.amazonaws.com", "DB_NAME": "massgov_pfml_breakfix", "BENEFIT_YEAR_EMPLOYEE_FILE": "./employees/e2e-2025-02-20-benefit-year.json", "BENEFIT_YEAR_EMPLOYER_FILE": "./employers/e2e-2025-02-20-benefit-year.json", "RUN_BENEFIT_YEAR_TESTS": "true", "HAS_FINEOS_DEPARTMENTS_SET_UP": "false", "HAS_GOOGLE_ANALYTICS": "false", "HAS_ORGUNITS_SETUP": "true", "RMV_CHECK_IS_FULLY_MOCKED": "true", "LST_PAYMENTS_FILE": "./src/artillery/payments/2025-02-04-breakfix-payments.json"}, "tst3": {"PORTAL_BASEURL": "https://paidleave-tst3.dfml.eol.mass.gov", "API_BASEURL": "https://paidleave-api-tst3.dfml.eol.mass.gov/api/v1", "DUA_EMPLOYEES_FILE": "./employees/dua-employees-2025-05-01.json", "DUA_EMPLOYERS_FILE": "./employers/dua-employers-2025-05-01.json", "FINEOS_BASEURL": "https://idt3-claims-webapp.masspfml.fineos.com/", "FINEOS_USERNAME": "CONTENT", "EMPLOYEES_FILE": "./employees/e2e-2025-06-23.json", "EMPLOYERS_FILE": "./employers/e2e-2025-06-23.json", "CPS_EMPLOYEES_FILE": "./employees/cps-2025-02-04.json", "CPS_EMPLOYERS_FILE": "./employers/cps-2025-02-04.json", "CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE": "./employees/concurrent-employees-2025-07-10.json", "CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE": "./employers/concurrent-employers-2025-07-10.json", "EMPLOYERS_MP_FILE": "./employers/e2e-2023-08-10-masterplan.json", "EXEMPT_EMPLOYER_FILE": "./employers/e2e-2025-04-29-exempt.json", "EMPLOYEES_FOR_EXEMPT_EMPLOYER_FILE": "./employees/e2e-2025-04-29-for-exempt-employer.json", "ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-org-units-2024-08-19.json", "ORGUNIT_EMPLOYERS_FILE": "./employers/e2e-org-units-2024-08-19.json", "NO_ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-no-org-units-2024-08-19.json", "DOR_IMPORT_URI": "s3://massgov-pfml-tst3-agency-transfer/dor/received/", "DOR_ETL_ARN": "arn:aws:states:us-east-1:************:stateMachine:pfml-api-tst3-dor-fineos-etl", "S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET": "massgov-pfml-tst3-agency-transfer", "S3_MASSGOV_PFML_REPORTS_BUCKET": "massgov-pfml-tst3-reports", "S3_INTELLIGENCE_TOOL_BUCKET": "massgov-pfml-tst3-business-intelligence-tool", "FINEOS_AWS_IAM_ROLE_ARN": "arn:aws:iam::************:role/somvrf-IAMRoles-CustomerAccountAccessRole-VsNw727kJDoq", "FINEOS_DATA_EXPORTS_BUCKET": "s3://fin-somvrf-data-export/IDT3", "FINEOS_AWS_IAM_ROLE_EXTERNAL_ID": "12345", "DB_HOSTNAME": "massgov-pfml-tst3.c6icrkacncoz.us-east-1.rds.amazonaws.com", "DB_NAME": "massgov_pfml_tst3", "BENEFIT_YEAR_EMPLOYEE_FILE": "./employees/e2e-2023-05-23-benefit-year.json", "BENEFIT_YEAR_EMPLOYER_FILE": "./employers/e2e-2023-05-23-benefit-year.json", "RUN_BENEFIT_YEAR_TESTS": "true", "HAS_FINEOS_DEPARTMENTS_SET_UP": "true", "HAS_GOOGLE_ANALYTICS": "true", "HAS_ORGUNITS_SETUP": "true", "HAS_FR25_1": "true", "HAS_FINEOS_SSO_LANDING_PAGE": "true", "HAS_NOTICE_ENHANCEMENTS": "true"}, "tst2": {"PORTAL_BASEURL": "https://paidleave-tst2.dfml.eol.mass.gov", "API_BASEURL": "https://paidleave-api-tst2.dfml.eol.mass.gov/api/v1", "DUA_EMPLOYEES_FILE": "./employees/dua-employees-2025-05-01.json", "DUA_EMPLOYERS_FILE": "./employers/dua-employers-2025-05-01.json", "FINEOS_BASEURL": "https://idt2-claims-webapp.masspfml.fineos.com/", "FINEOS_USERNAME": "CONTENT", "CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE": "./employees/concurrent-employees-2025-06-23.json", "CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE": "./employers/concurrent-employers-2025-06-23.json", "EMPLOYEES_FILE": "./employees/e2e-2025-06-23.json", "EMPLOYERS_FILE": "./employers/e2e-2025-06-23.json", "CPS_EMPLOYEES_FILE": "./employees/cps-2025-02-04.json", "CPS_EMPLOYERS_FILE": "./employers/cps-2025-02-04.json", "EMPLOYERS_MP_FILE": "./employers/e2e-2023-08-10-masterplan.json", "ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-org-units-2024-08-19.json", "ORGUNIT_EMPLOYERS_FILE": "./employers/e2e-org-units-2024-08-19.json", "NO_ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-no-org-units-2024-08-19.json", "DOR_IMPORT_URI": "s3://massgov-pfml-tst2-agency-transfer/dor/received/", "DOR_ETL_ARN": "arn:aws:states:us-east-1:************:stateMachine:pfml-api-tst2-dor-fineos-etl", "S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET": "massgov-pfml-tst2-agency-transfer", "S3_MASSGOV_PFML_REPORTS_BUCKET": "massgov-pfml-tst2-reports", "S3_INTELLIGENCE_TOOL_BUCKET": "massgov-pfml-tst2-business-intelligence-tool", "FINEOS_AWS_IAM_ROLE_ARN": "arn:aws:iam::************:role/somvrf-IAMRoles-CustomerAccountAccessRole-VsNw727kJDoq", "FINEOS_DATA_EXPORTS_BUCKET": "s3://fin-somvrf-data-export/IDT2", "FINEOS_AWS_IAM_ROLE_EXTERNAL_ID": "12345", "DB_HOSTNAME": "massgov-pfml-tst2.c6icrkacncoz.us-east-1.rds.amazonaws.com", "DB_NAME": "massgov_pfml_tst2", "BENEFIT_YEAR_EMPLOYEE_FILE": "./employees/e2e-2025-02-20-benefit-year.json", "BENEFIT_YEAR_EMPLOYER_FILE": "./employers/e2e-2025-02-20-benefit-year.json", "RUN_BENEFIT_YEAR_TESTS": "true", "HAS_FINEOS_DEPARTMENTS_SET_UP": "true", "HAS_GOOGLE_ANALYTICS": "false", "HAS_ORGUNITS_SETUP": "true", "HAS_LIVECHAT_SETUP": "true", "RMV_CHECK_IS_FULLY_MOCKED": "true", "LST_PAYMENTS_FILE": "./src/artillery/payments/2025-02-04-tst2-payments.json", "HAS_PROFILE_IDV_FEATURE": "false", "HAS_NOTICE_ENHANCEMENTS": "true", "HAS_FR25_1": "true", "HAS_FINEOS_SSO_LANDING_PAGE": "true"}, "tst1": {"API_BASEURL": "https://paidleave-api-tst1.dfml.eol.mass.gov/api/v1", "CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE": "./employees/concurrent-employees-2025-07-10.json", "CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE": "./employers/concurrent-employers-2025-07-10.json", "DUA_EMPLOYEES_FILE": "./employees/dua-employees-2025-05-01.json", "DUA_EMPLOYERS_FILE": "./employers/dua-employers-2025-05-01.json", "CPS_EMPLOYEES_FILE": "./employees/cps-2025-02-04.json", "CPS_EMPLOYERS_FILE": "./employers/cps-2025-02-04.json", "DB_HOSTNAME": "massgov-pfml-tst1.c6icrkacncoz.us-east-1.rds.amazonaws.com", "DB_NAME": "massgov_pfml_tst1", "DOR_ETL_ARN": "arn:aws:states:us-east-1:************:stateMachine:pfml-api-tst1-dor-fineos-etl", "DOR_IMPORT_URI": "s3://massgov-pfml-tst1-agency-transfer/dor/received/", "EMPLOYEES_FILE": "./employees/e2e-2025-06-23.json", "EMPLOYERS_FILE": "./employers/e2e-2025-06-23.json", "EMPLOYERS_MP_FILE": "./employers/e2e-2023-08-10-masterplan.json", "FINEOS_AWS_IAM_ROLE_ARN": "arn:aws:iam::************:role/somvrf-IAMRoles-CustomerAccountAccessRole-VsNw727kJDoq", "FINEOS_AWS_IAM_ROLE_EXTERNAL_ID": "12345", "FINEOS_DATA_EXPORTS_BUCKET": "s3://fin-somvrf-data-export/IDT1", "FINEOS_BASEURL": "https://idt1-claims-webapp.masspfml.fineos.com/", "FINEOS_USERNAME": "CONTENT", "HAS_FINEOS_DEPARTMENTS_SET_UP": "false", "HAS_GOOGLE_ANALYTICS": "false", "HAS_ORGUNITS_SETUP": "true", "NO_ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-no-org-units-2024-08-19.json", "ORGUNIT_EMPLOYEES_FILE": "./employees/e2e-org-units-2024-08-19.json", "ORGUNIT_EMPLOYERS_FILE": "./employers/e2e-org-units-2024-08-19.json", "PORTAL_BASEURL": "https://paidleave-tst1.dfml.eol.mass.gov", "RMV_CHECK_IS_FULLY_MOCKED": "true", "BENEFIT_YEAR_EMPLOYEE_FILE": "./employees/e2e-2025-02-20-benefit-year.json", "BENEFIT_YEAR_EMPLOYER_FILE": "./employers/e2e-2025-02-20-benefit-year.json", "RUN_BENEFIT_YEAR_TESTS": "true", "S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET": "massgov-pfml-tst1-agency-transfer", "S3_MASSGOV_PFML_REPORTS_BUCKET": "massgov-pfml-tst1-reports", "S3_INTELLIGENCE_TOOL_BUCKET": "massgov-pfml-tst1-business-intelligence-tool", "LST_PAYMENTS_FILE": "./src/artillery/payments/2025-02-04-tst1-payments.json", "HAS_NOTICE_ENHANCEMENTS": "true"}, "local": {"PORTAL_BASEURL": "http://localhost:3000", "PORTAL_USERNAME": "<EMAIL>", "API_BASEURL": "https://paidleave-api-tst1.dfml.eol.mass.gov/api/v1", "FINEOS_BASEURL": "https://idt1-claims-webapp.masspfml.fineos.com", "FINEOS_USERNAME": "CONTENT", "EMPLOYEES_FILE": "./employees/e2e-2025-06-23.json", "EMPLOYERS_FILE": "./employers/e2e-2025-06-23.json"}}