# PFML Shadow DB Creation or Deletion Automation

Automates the Shadow DB process via GitHub Actions. To run via CLI, see the comment block at top of the script for instructions.

### Creation
Provisions a single shadow DB instance from either the prod or tst2 environments. Using the `Date and Time` input parameter, the user can specify a specific date and time for the instance to be created from. If the input is left blank or is `latest`, than the shadow DB is created from the latest possible backup.

:clipboard: Note: This process now changes the root user (pfml) password to the value of `/service/pfml-api/<env>/db-password-shadow` in SSM parameter store. This allows the user to login as the `pfml` user rather than the `pfml_shadow` user without exposing the actual prod `pfml` password. This is an effort to remove a security concern in the prod DB where anyone with access to the db-password-shadow param could log-in with admin privs. TODO: Remove the `pfml_shadow` user from prod.

### Deletion
Destroys one or more shadow DB instances. The `Shadow DB Name` input accepts one or more shadow DB names so the user can easily destroy any number of shadow DBs in a single run. The `Environment` input is a required input, but is not referenced when the `Action` is `destroy` so you can delete shadow DBs from both prod and tst2 together.

## Permissions
This job runs in the `Shadow` GitHub environment which is protected. Only members of the GitHub [pfml-shadow-db](https://github.com/orgs/EOLWD/teams/pfml-shadow-db/members) team can approve or deny. If you wish to be added to this team, please reach out to Griffith, Christopher (EOL) <<EMAIL>>.

## Inputs
| **Input** | **Required** | **Type** | **Options** |
| --- | --- | --- | --- |
| `Action` | *yes* | `choice` | `create`, `destroy` |
| `Environment`  | *yes*  | `choice` | `prod`, `tst2` |
| `Date and Time` | *no* | `string` | `latest`, `yyyy-mm-ddThh:mm:ssZ` (Must be in ISO 8601 Format UTC) |
| `Shadow DB Name` | *yes* | `string` | `massgov-pfml-<env>-<date>-shadow` |
| `Storage Type` | *no* | `choice` | `gp3`, `io2` (defaults to `gp3`) |
| `Instance Size` | *no* | `choice` | `db.r5.xlarge`, `db.r5.2xlarge` (defaults to `db.r5.xlarge`) |

## Connect
To connect to the shadow instance, you must use your PFML workspace. `prod` and `tst2` have different passwords. The Endpoint, Username, and [password location](https://us-east-1.console.aws.amazon.com/systems-manager/parameters?region=us-east-1&tab=Table#list_parameter_filters=Name:Contains:shadow) will be at the end of the workflow logs:  
```
2023-12-05 12:14:39 - INFO - massgov-pfml-prod-20231024-shadow has been succesfully created!  
Endpoint: massgov-pfml-prod-20231024-shadow.c6icrkacncoz.us-east-1.rds.amazonaws.com  
Username: pfml  
Password: See /service/pfml-api/prod/db-password-shadow in AWS Parameter Store
``` 

## Examples
**Create a shadow prod DB from latest point in time**  
*Action*: `create` *Environment*: `prod` *Name*: `massgov-pfml-prod-20231128-shadow`

**Create a shadow prod DB from a specific point in time**  
*Action*: `create` *Environment*: `prod` *Date and Time*: `2023-09-07T09:00:00Z` *Name*: `massgov-pfml-prod-20230907-shadow`

**Create a shadow prod DB with io2 storage type**  
*Action*: `create` *Environment*: `prod` *Name*: `massgov-pfml-prod-20230907-shadow` *Storage Type*: `io2`

**Delete shadow prod DBs that are no longer needed**  
*Action*: `destroy` *Environment*: `prod` *Name*: `massgov-pfml-prod-20231128-shadow massgov-pfml-prod-20231129-shadow massgov-pfml-prod-pfmlpb-12842-shadow`

## Use Tags to Override Default Shutdown/Termination Behavior

The shutdown/termination behavior of RDS instances can be modified by applying tags to the instances. These tags allow instances to not be affected by daily Infra tooling processes for temporary shutdown and permanent termination.

## List of tags that control instance uptime and retention

| **Tag Keys** | **Tag Values** | **Description** |
| --- | --- | --- |
| `retention_days` | number | Controls how many days the instance will live since it's creation. i.e. `3` to delete the DB after 3 days. |
| `auto_stop` | false | Adding false means that the instance will not be temporarily stopped each night. |
| `auto_delete` | false | Adding false means that the instance will not be deleted after 7 days. |
