# Creates a shadow instance of a prod or tst2 DB from latest restorable time. https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_PIT.html
# Written to be invoked by GitHub Actions, but it can also be ran with CLI args - given the user has sufficent AWS permissions
# Input arguments:
#   --action    : What action for the script to take (Only accepts create or destroy)
#   --env       : The envionment name (Only accepts prod or tst2)
#   --date_time : The date and time the Shadow DB should be restored from (ISO 8601 format in UTC i.e. 2023-09-07T09:00:00Z). Defaults to latest.
#   --name      : The name of the shadow DB (Typically, it should follow this format: massgov-pfml-<env>-<date>-shadow). If destroying, you can pass in multiple Shadow DB names separated by a space.
#   --storage_type : The storage type to use (Only accepts gp3 or io2). Defaults to gp3.
# Invoke via CLI:
#   python create_destroy_shadow.py --action create --env prod --date_time 2023-09-07T09:00:00Z --name massgov-pfml-prod-20231128-shadow
#   python create_destroy_shadow.py --action create --env prod --name massgov-pfml-prod-20231128-shadow
#   python create_destroy_shadow.py --action create --env prod --name massgov-pfml-prod-20231128-shadow --storage_type io2
#   python create_destroy_shadow.py --action destroy --env prod --name massgov-pfml-prod-20231128-shadow massgov-pfml-prod-20231129-shadow massgov-pfml-prod-pfmlpb-12842-shadow

# NOTE: This may take anywhere from 30mins up to 3hrs

from time import sleep
import boto3
import botocore
import argparse
import logging as LOGGER
import json
from datetime import datetime
import sys
import math

PARSER = argparse.ArgumentParser()
LOGGER.basicConfig(
    level=LOGGER.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
RDS = boto3.client("rds")
EC2 = boto3.client("ec2")
SSM = boto3.client("ssm")


def get_input_arguments():
    PARSER.add_argument(
        "--action",
        choices=["create", "destroy"],
        required=True,
        help="What action to perform on the database(s)",
    )
    PARSER.add_argument(
        "--env",
        choices=["prod", "tst2"],
        required=True,
        help="Environment name i.e. prod",
    )
    PARSER.add_argument(
        "--date_time",
        required=False,
        default="latest",
        help="Date and Time of the restore. Must be in UTC time format i.e. 2023-09-07T09:00:00Z. If you do not pass this in, latest restorable time is used.",
    )
    PARSER.add_argument(
        "--name",
        nargs="+",
        help="Name of the Shadow DB instance i.e. massgov-pfml-prod-20231127-shadow. You can pass in multiple names if the action is destroy - just separate each name with a space.",
    )
    PARSER.add_argument(
        "--storage_type",
        choices=["gp3", "io2"],
        required=False,
        default="gp3",
        help="Whether to use General Purpose SSD (gp3) or Provisioned IOPS (io2). Unless you explicitly need to use io2, use the default gp3.",
    )
    PARSER.add_argument(
        "--instance_size",
        choices=["db.r5.xlarge", "db.r5.2xlarge"],
        required=False,
        default="db.r5.xlarge",
        help="The size of the RDS instance to create. Defaults to db.r5.xlarge. Prod uses db.r5.2xlarge.",
    )
    return PARSER.parse_args()


def get_db_details(db_name):
    response = RDS.describe_db_instances(DBInstanceIdentifier=db_name)
    return response


ARGS = get_input_arguments()
RESTORE_FROM = f"massgov-pfml-{ARGS.env}"
SHADOW = ARGS.name[0]
DB = get_db_details(RESTORE_FROM)
PASSWORD_PATH = f"/service/pfml-api/{ARGS.env}/db-password-shadow"
ALLOCATED_STORAGE = DB["DBInstances"][0]["AllocatedStorage"]
MAX_ALLOCATED_STORAGE = math.ceil(
    ALLOCATED_STORAGE * 1.1  # Must be at least 10% greater than ALLOCATED_STORAGE
)
MONITORING_ROLE_ARN = DB["DBInstances"][0]["MonitoringRoleArn"]
MONITORING_INTERVAL = DB["DBInstances"][0]["MonitoringInterval"]
PERFORMANCE_INSIGHTS_ENABLED = DB["DBInstances"][0]["PerformanceInsightsEnabled"]
PERFORMANCE_INSIGHTS_RETENTION_PERIOD = (
    31  # Shadow DBs are usually deleted after 30 days
)
SECURITY_GROUP = DB["DBInstances"][0]["VpcSecurityGroups"][0]["VpcSecurityGroupId"]
DB_PARAMETER_GROUP_NAME = DB["DBInstances"][0]["DBParameterGroups"][0][
    "DBParameterGroupName"
]
DB_SUBNET_GROUP_NAME = DB["DBInstances"][0]["DBSubnetGroup"]["DBSubnetGroupName"]
STORAGE_TYPE = ARGS.storage_type
SHADOW_PASSWORD = SSM.get_parameter(Name=PASSWORD_PATH, WithDecryption=True)[
    "Parameter"
]["Value"]
INSTANCE_SIZE = ARGS.instance_size

def validate_inputs():
    for i in ARGS.name:
        if not i.endswith("-shadow"):
            LOGGER.error(
                f"{i} must end with '-shadow' i.e. massgov-pfml-prod-20231127-shadow"
            )
            sys.exit(1)

    if ARGS.date_time != "latest":
        try:
            expected_format = "%Y-%m-%dT%H:%M:%SZ"
            datetime.strptime(ARGS.date_time, expected_format)
        except ValueError as e:
            LOGGER.error(
                f"{ARGS.date_time} is not in ISO 8601 format i.e. 2023-09-07T09:00:00Z"
            )
            sys.exit(1)

    if ARGS.env != "prod" and ARGS.storage_type == "io2":
        LOGGER.error("Nonprod shadows must use gp3 storage type.")
        sys.exit(1)
    return True


def restore_io2_latest():
    RDS.restore_db_instance_to_point_in_time(
        SourceDBInstanceIdentifier=RESTORE_FROM,
        TargetDBInstanceIdentifier=SHADOW,
        UseLatestRestorableTime=True,
        DBInstanceClass=INSTANCE_SIZE,
        DBSubnetGroupName=DB_SUBNET_GROUP_NAME,
        MultiAZ=False,
        AutoMinorVersionUpgrade=False,
        CopyTagsToSnapshot=True,
        StorageType=STORAGE_TYPE,
        Iops=calculate_ratio(),
        VpcSecurityGroupIds=[SECURITY_GROUP],
        EnableIAMDatabaseAuthentication=True,
        EnableCloudwatchLogsExports=["postgresql", "upgrade"],
        DBParameterGroupName=DB_PARAMETER_GROUP_NAME,
        AllocatedStorage=ALLOCATED_STORAGE,
        MaxAllocatedStorage=MAX_ALLOCATED_STORAGE,
    )


def restore_gp3_latest():
    RDS.restore_db_instance_to_point_in_time(
        SourceDBInstanceIdentifier=RESTORE_FROM,
        TargetDBInstanceIdentifier=SHADOW,
        UseLatestRestorableTime=True,
        DBInstanceClass=INSTANCE_SIZE,
        DBSubnetGroupName=DB_SUBNET_GROUP_NAME,
        MultiAZ=False,
        AutoMinorVersionUpgrade=False,
        CopyTagsToSnapshot=True,
        StorageType=STORAGE_TYPE,
        VpcSecurityGroupIds=[SECURITY_GROUP],
        EnableIAMDatabaseAuthentication=True,
        EnableCloudwatchLogsExports=["postgresql", "upgrade"],
        DBParameterGroupName=DB_PARAMETER_GROUP_NAME,
        AllocatedStorage=ALLOCATED_STORAGE,
        MaxAllocatedStorage=MAX_ALLOCATED_STORAGE,
    )


def restore_io2_pit():
    RDS.restore_db_instance_to_point_in_time(
        SourceDBInstanceIdentifier=RESTORE_FROM,
        TargetDBInstanceIdentifier=SHADOW,
        RestoreTime=ARGS.date_time,
        DBInstanceClass=INSTANCE_SIZE,
        DBSubnetGroupName=DB_SUBNET_GROUP_NAME,
        MultiAZ=False,
        AutoMinorVersionUpgrade=False,
        CopyTagsToSnapshot=True,
        StorageType=STORAGE_TYPE,
        Iops=calculate_ratio(),
        VpcSecurityGroupIds=[SECURITY_GROUP],
        EnableIAMDatabaseAuthentication=True,
        EnableCloudwatchLogsExports=["postgresql", "upgrade"],
        DBParameterGroupName=DB_PARAMETER_GROUP_NAME,
        AllocatedStorage=ALLOCATED_STORAGE,
        MaxAllocatedStorage=MAX_ALLOCATED_STORAGE,
    )


def restore_gp3_pit():
    RDS.restore_db_instance_to_point_in_time(
        SourceDBInstanceIdentifier=RESTORE_FROM,
        TargetDBInstanceIdentifier=SHADOW,
        RestoreTime=ARGS.date_time,
        DBInstanceClass=INSTANCE_SIZE,
        DBSubnetGroupName=DB_SUBNET_GROUP_NAME,
        MultiAZ=False,
        AutoMinorVersionUpgrade=False,
        CopyTagsToSnapshot=True,
        StorageType=STORAGE_TYPE,
        VpcSecurityGroupIds=[SECURITY_GROUP],
        EnableIAMDatabaseAuthentication=True,
        EnableCloudwatchLogsExports=["postgresql", "upgrade"],
        DBParameterGroupName=DB_PARAMETER_GROUP_NAME,
        AllocatedStorage=ALLOCATED_STORAGE,
        MaxAllocatedStorage=MAX_ALLOCATED_STORAGE,
    )


def destroy():
    for i in ARGS.name:
        try:
            RDS.delete_db_instance(
                DBInstanceIdentifier=i,
                SkipFinalSnapshot=True,
                DeleteAutomatedBackups=True,
            )
            LOGGER.info(f"{i} is being deleted.")

        except botocore.exceptions.ClientError as error:
            if error.response["Error"]["Code"] == "InvalidParameterCombination":
                LOGGER.warning(
                    f"{i} has DeletionProtection enabled. Disabling flag now."
                )

                RDS.modify_db_instance(DBInstanceIdentifier=i, DeletionProtection=False)

                RDS.delete_db_instance(
                    DBInstanceIdentifier=i,
                    SkipFinalSnapshot=True,
                    DeleteAutomatedBackups=True,
                )
                LOGGER.info(f"{i} is being deleted.")
            else:
                LOGGER.error(error)


def wait_for_available():
    available = False
    while not available:
        shadow_details = get_db_details(SHADOW)
        if shadow_details["DBInstances"][0]["DBInstanceStatus"] == "available":
            available = True
            return available
        else:
            sleep(60)


def modify():
    response = RDS.modify_db_instance(
        DBInstanceIdentifier=SHADOW,
        MasterUserPassword=SHADOW_PASSWORD,
        MonitoringRoleArn=MONITORING_ROLE_ARN,
        MonitoringInterval=MONITORING_INTERVAL,
        EnablePerformanceInsights=PERFORMANCE_INSIGHTS_ENABLED,
        PerformanceInsightsRetentionPeriod=PERFORMANCE_INSIGHTS_RETENTION_PERIOD,
        ApplyImmediately=True,
    )
    return response


# The iops to max allocated storage ratio must equal or exceed 0.50 (calculated by dividing iops by MAX_ALLOCATED_STORAGE)
def calculate_ratio():
    iops = DB["DBInstances"][0]["Iops"]
    ratio = round(iops / MAX_ALLOCATED_STORAGE, 5)
    if ratio >= 0.5:
        return iops
    else:
        LOGGER.warning(
            f"The IOPS to max storage ratio ({ratio}) must be 0.5 or greater. Increasing IOPS ({iops}) by 10 until the ratio is met."
        )
        while ratio < 0.5:
            iops += 10
            ratio = round(iops / MAX_ALLOCATED_STORAGE, 5)
    LOGGER.info(f"An IOPS of {iops} results in a ratio of {ratio}.")
    return iops


def main():
    if validate_inputs():
        if ARGS.action == "destroy":
            destroy()
            return
        elif STORAGE_TYPE == "gp3" and "latest" in ARGS.date_time:
            restore_gp3_latest()
        elif STORAGE_TYPE == "gp3" and "latest" not in ARGS.date_time:
            restore_gp3_pit()
        elif STORAGE_TYPE == "io2" and "latest" in ARGS.date_time:
            restore_io2_latest()
        elif STORAGE_TYPE == "io2" and "latest" not in ARGS.date_time:
            restore_io2_pit()
        LOGGER.info(
            f"Waiting for {SHADOW} to become available... this may take some time"
        )
        if wait_for_available():
            response = modify()
            LOGGER.info(json.dumps(response, default=str, indent=2))
            LOGGER.info(
                f"{SHADOW} has been succesfully created!\nEndpoint: {response['DBInstance']['Endpoint']['Address']}\nUsername: pfml\nPassword: See {PASSWORD_PATH} in AWS Parameter Store"
            )


if __name__ == "__main__":
    main()
