import boto3
import logging
import argparse
import sys
from datetime import datetime, timedelta, timezone

PARSER = argparse.ArgumentParser()
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)


def get_input_arguments():
    PARSER.add_argument(
        "--task",
        help="The ECS task name as shown in infra/ecs-tasks/template/tasks.tf. No 'pfml-api' prefix or env name. e.g. 'pub-payments-process-fineos'",
    )
    PARSER.add_argument(
        "--image",
        help="The image name (commit hash of the feature branch) has to exist in pfml-api ECR repo. e.g. '71da43bc4eff0ccd951404a06a8b5816d0e36a91'",
    )
    PARSER.add_argument(
        "--run_number",
        help="The GitHub Actions run number. Used to append unique identifier to the task name. e.g. 12345",
    )
    PARSER.add_argument(
        "--action",
        choices=["create", "cleanup"],
        default="create",
        help="Create a new task definition or remove stale task definitions",
    )
    return PARSER.parse_args()


ECS = boto3.client("ecs")
ARGS = get_input_arguments()


def retrieve_task_definition(task=None):
    logging.info(f"Retrieving pfml-api-prod-shadow-{task} task definition parameters")
    try:
        response = ECS.describe_task_definition(
            taskDefinition=f"pfml-api-prod-shadow-{task}"
        )
        return response["taskDefinition"]
    except Exception as e:
        logging.error(f"Error retrieving pfml-api-prod-shadow-{task} parameters: {e}")
        sys.exit(1)


def create_task_definition(task=None, image=None, run_number=None):
    logging.info(f"Creating Shadow Prod Adhoc ECS task definition for {task}")

    task_parameters = retrieve_task_definition(task=task)

    family = f"pfml-api-prod-shadow-adhoc-{task}-{run_number}"
    shadow_image = task_parameters["containerDefinitions"][0]["image"]
    ecr_repo_url = shadow_image.split(":")[0]
    new_image = f"{ecr_repo_url}:{image}"

    new_container_definitions = []
    for container in task_parameters["containerDefinitions"]:
        new_container = container.copy()
        new_container["image"] = new_image
        new_container["name"] = f"adhoc-{task}-{run_number}-shadow"
        new_container_definitions.append(new_container)

    try:
        ECS.register_task_definition(
            family=family,
            taskRoleArn=task_parameters["taskRoleArn"],
            executionRoleArn=task_parameters["executionRoleArn"],
            cpu=task_parameters["cpu"],
            memory=task_parameters["memory"],
            requiresCompatibilities=task_parameters["requiresCompatibilities"],
            networkMode=task_parameters["networkMode"],
            containerDefinitions=new_container_definitions,
        )

        logging.info(f"Successfully created {family}")

    except Exception as e:
        logging.error(f"Error creating {family}: {e}")
        sys.exit(1)


def remove_stale_task_definitions():
    logging.info(f"Removing stale Shadow Prod Adhoc ECS task definitions.")

    tasks = []
    next_token = None
    while True:
        if next_token:
            response = ECS.list_task_definitions(status="ACTIVE", nextToken=next_token)
        else:
            response = ECS.list_task_definitions(status="ACTIVE")
        tasks.extend(response["taskDefinitionArns"])
        if "nextToken" not in response:
            break
        next_token = response["nextToken"]

    shadow_adhoc_tasks = [
        task for task in tasks if "pfml-api-prod-shadow-adhoc-" in task
    ]
    stale = datetime.now(timezone.utc) - timedelta(days=7)
    for task in shadow_adhoc_tasks:
        response_def = ECS.describe_task_definition(taskDefinition=task)
        definition = response_def["taskDefinition"]
        try:
            if definition["registeredAt"] < stale:
                ECS.deregister_task_definition(taskDefinition=task)
                logging.info(f"Deregistered {definition['family']}")
        except Exception as e:
            logging.error(f"Error deregistering {definition['family']}: {e}")
            sys.exit(1)


if __name__ == "__main__":
    if ARGS.action == "create":
        create_task_definition(task=ARGS.task, image=ARGS.image, run_number=ARGS.run_number)
    elif ARGS.action == "cleanup":
        remove_stale_task_definitions()
