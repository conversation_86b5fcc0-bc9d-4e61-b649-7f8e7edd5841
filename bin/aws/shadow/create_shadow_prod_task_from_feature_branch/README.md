# Create Shadow Prod Task from Feature Branch (Adhoc Shadow Prod)
Shadow prod testing requires code to be merged to main; significantly slowing down a developer's ability to test their tasks with prod data. This feature accelerates their testing by provisioning an ECS task definition with an image created from their feature branch. Any other changes needed such as environment vars, secrets, cpu, mem, etc can also be adjusted by the use of overrides when running the task (more on that later).

For more info, please see [Tech Spec](https://lwd.atlassian.net/wiki/x/fIAxAgE)

## Creating a task from a feature branch
This feature is made to be ran by GitHub Actions. The workflow, [Infra Shadow Prod Task with Feature Branch](https://github.com/EOLWD/pfml/actions/workflows/infra-shadow-prod-task.yml), takes a number of inputs:
| **Input** | **Required** | **Type** | **Options** |
| --- | --- | --- | --- |
| `Branch` | *yes* | `choice` | `main` (default), any feature branch or tag pushed to remote |
| `Task Name`  | *yes*  | `string` | The task name you want to create a shadow adhoc task from e.g. `pub-payments-process-fineos` |
| `Create Shadow Database?` | *no* | `boolean` | `true` or `false` (defaults to `false`) |
| `Restore date and time` | *no* | `string` | `latest`, `yyyy-mm-ddThh:mm:ssZ` (Must be in ISO 8601 Format UTC) |
| `Storage Type` | *no* | `choice` | `gp3`, `io2` (defaults to `gp3`) |
| `Instance Size` | *no* | `choice` | `db.r5.xlarge`, `db.r5.2xlarge` (defaults to `db.r5.xlarge`) |

Using the `main` branch would defeat the purpose of this feature so please select the feature branch containing your app code changes. If your feature branch isn't appearing in the branch dropdown, then you haven't pushed it to remote yet. 

To combat any naming collisions, the task you create will be automatically named in the following format
- pfml-api-prod-shadow-adhoc-`<task name>`-`<github.run_number>`
- e.g. pfml-api-prod-shadow-adhoc-`pub-payments-process-fineos`-`123`.  
  :clipboard: The appended number is the GitHub run number. Begins at 1 and increments each run. 

## Optional Shadow DB
As shadow tasks are meant to be ran with prod data, a shadow task can be created in parallel. The inputs described above control how the database is provisioned. Leaving the `Create Shadow Database?` input unchecked (false) causes the remaining inputs to be ignored.

If the input is checked (true), then the database is created by calling the existing workflow, [Infra Shadow DB Create or Destroy](https://github.com/EOLWD/pfml/actions/workflows/infra-shadow-db-create-destroy.yml), so it's created in the exact same way they are now.  
:clipboard: Just as the task name is auto-generated, the database name is as well. Using the previous example of task name `pfml-api-prod-shadow-adhoc-pub-payments-process-fineos-123`, the database name would be `massgov-pfml-prod-adhoc-123-shadow`. So your task and db will share the same GitHub Run Number.

## Permissions
This job runs in the `Shadow` GitHub environment which is protected. Only members of the GitHub [pfml-shadow-db](https://github.com/orgs/EOLWD/teams/pfml-shadow-db/members) team can approve or deny. If you wish to be added to this team, please reach out to Griffith, Christopher (EOL) <<EMAIL>>.

## Usage

### When to use Shadow Prod and Adhoc Shadow Prod
- **Use Shadow Prod when** you just need to test a task in its current deployed state in prod. You can still run the task with overrides e.g. commands, env vars, secrets, cpu, memory, etc
- **Use Adhoc Shadow Prod when** you need to make app code changes from a feature branch. In other words, the task exists in prod, but you've made app code changes that have not yet been merged or deployed to prod. 

### Steps
1. Push your app code changes to remote
2. Run the [Infra Shadow Prod Task with Feature Branch](https://github.com/EOLWD/pfml/actions/workflows/infra-shadow-prod-task.yml) workflow by first selecting your feature branch from the branch dropdown, passing in the name of the task that exists in prod e.g. `pub-payments-process-fineos` (do not include `pfml-api` or environment prefix - the name should be identical to what's in [tasks.tf](https://github.com/EOLWD/pfml/blob/main/infra/ecs-tasks/template/tasks.tf#L89) or [tasks_1099.tf](https://github.com/EOLWD/pfml/blob/main/infra/ecs-tasks/template/tasks_1099.tf#L39)), and selecting/inputting the shadow database parameters if applicable.
    - The task should only take a few minutes to be created, but if you also created a shadow database that will take up to 2-3hrs.
3. Once your task and shadow database are successfully created, run the [run-task.sh](https://github.com/EOLWD/pfml/blob/main/bin/run-ecs-task/run-task.sh) bin script (see the [README.md](https://github.com/EOLWD/pfml/blob/main/bin/run-ecs-task/README.md) for more info on the command line arguments and overrides)
    - **IMPORTANT**: When running the task, the command-line arg for `env` is still `prod-shadow` but the `task` arg is more than just the task name. e.g. `pub-payments-process-fineos` would instead be `adhoc-pub-payments-process-fineos-123` (if the run number was `123`). Also, you must use the original task name as the command (not the shadow/adhoc name) - e.g. `pub-payments-process-fineos`
    - Example:
      ```
      ./bin/run-ecs-task/run-task.sh prod-shadow adhoc-pub-payments-process-fineos-123 ben.lake pub-payments-process-fineos
      ```
    - Don't forget to use [container_overrides.json.tpl](https://github.com/EOLWD/pfml/blob/main/bin/run-ecs-task/container_overrides.json.tpl) to pass in overrides like the shadow database endpoint, other env vars, secrets, cpu, memory, etc.
4. View your logs in New Relic by filtering by the `aws.logStream` (Adhoc shadow tasks have different log stream names/formats than their shadow equivalents)

    | **Type** | **aws.logStream Format** | **Example** |
    | --- | --- | --- |
    | Adhoc Shadow Prod | `prod-shadow/adhoc-<task name>-<run number>-shadow/<log stream id>` | `prod-shadow/adhoc-pub-payments-process-fineos-123-shadow/87ee5f7ae87643778fde3b661ef65908` |
    | Shadow Prod | `prod-shadow/<task name>-shadow/<log stream id>` | `prod-shadow/pub-payments-process-fineos-shadow/87ee5f7ae87643778fde3b661ef65908` |