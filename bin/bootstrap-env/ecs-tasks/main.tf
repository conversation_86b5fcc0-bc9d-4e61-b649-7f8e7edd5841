locals {
  environment_name = "$ENV_NAME"
}

provider "aws" {
  region = "us-east-1"
}

terraform {
  backend "s3" {
    bucket         = "massgov-pfml-$ENV_NAME-env-mgmt"
    key            = "terraform/ecs-tasks.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
  }
}

data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "aws_secretsmanager_secret" "rmv_client_certificate" {
  name = "/service/pfml-api-$ENV_NAME/rmv_client_certificate"
}

module "tasks" {
  source = "../../template"

  environment_name         = "$ENV_NAME"
  st_use_mock_dor_data     = false # true or false
  st_decrypt_dor_data      = false # true or false
  st_file_limit_specified  = true  # true or false
  st_employer_update_limit = 1500  # a number like 1500
  service_docker_tag       = local.service_docker_tag
  vpc_id                   = data.aws_vpc.vpc.id
  vpc_name                 = local.vpc
  app_subnet_ids           = data.aws_subnets.vpc_app.ids

  # TODO: These values are provided by FINEOS.
  fineos_client_integration_services_api_url = ""
  fineos_client_customer_api_url             = ""
  fineos_client_group_client_api_url         = ""
  fineos_client_wscomposer_api_url           = ""
  fineos_client_oauth2_url                   = ""

  fineos_aws_iam_role_arn                             = ""
  fineos_aws_iam_role_external_id                     = ""
  fineos_eligibility_feed_output_directory_path       = ""
  fineos_import_employee_updates_input_directory_path = ""

  # These can be kept blank.
  eolwd_moveit_sftp_uri   = ""
  pfml_error_reports_path = "s3://massgov-pfml-$ENV_NAME-agency-transfer/error-reports/outbound"

  dfml_project_manager_email_address     = "<EMAIL>"
  dfml_business_operations_email_address = "<EMAIL>"
  dfml_fines_repayments_email_address    = "<EMAIL>"
  dfml_fines_repayments_cc_email_address = "<EMAIL>"
  dor_employee_integrity_email_address   = "<EMAIL>"
  mmars_overpayment_email_address        = "<EMAIL>"
  mmars_overpayment_bcc_email_address    = "<EMAIL>"
  dfml_db_migration_email_address        = "<EMAIL>"
  dfml_db_migration_cc_email_address     = "<EMAIL>"

  # TODO: Values from FINEOS.
  fineos_data_export_path         = ""
  fineos_monthly_data_export_path = ""
  fineos_adhoc_data_export_path   = ""
  fineos_data_import_path         = ""
  fineos_error_export_path        = ""
  fineos_report_export_path       = ""

  pfml_fineos_inbound_path                            = "s3://massgov-pfml-$ENV_NAME-agency-transfer/cps/inbound"
  pfml_fineos_outbound_path                           = "s3://massgov-pfml-$ENV_NAME-agency-transfer/cps/outbound"
  pfml_fineos_eligibility_feed_archive_directory_path = "s3://massgov-pfml-$ENV_NAME-agency-transfer/cps/eligibility-feed-to-cps"
  pfml_fineos_eligibility_feed_temp_directory_path    = "/tmp"

  payment_audit_report_outbound_folder_path = "s3://massgov-pfml-$ENV_NAME-agency-transfer/audit/outbound"
  payment_audit_report_sent_folder_path     = "s3://massgov-pfml-$ENV_NAME-agency-transfer/audit/sent"

  # PaymentAuditReport Feature Flags
  dia_dua_annotations_enabled                           = "" # "1" or "0"
  sync_employee_feed_aggregate_step_enable              = "" # "1" or "0"
  enable_to_use_feed_aggregate_table                    = "" # "1" or "0"
  enable_sync_address_using_aggregate_table             = "" # "1" or "0"
  enable_sync_eft_using_aggregate_table                 = "" # "1" or "0"
  enable_sync_payment_preferences_using_aggregate_table = ""
  enable_payment_line_matching                          = "1"
  enable_document_upload_optional                       = "1"

  enable_register_admins_job = true

  enable_pub_automation_create_pub_files     = true  # true or false
  enable_pub_automation_process_returns      = false # true or false
  enable_fineos_import_iaww                  = true  # true or false
  enable_sync_fineos_extracts_to_pfml_models = true  # true or false
  enable_fines_and_repayments_automation     = false # true or false

  rmv_client_base_url               = "https://atlas-staging-gateway.massdot.state.ma.us/vs"
  rmv_client_certificate_binary_arn = data.aws_secretsmanager_secret.rmv_client_certificate.arn

  task_failure_email_address_list = ["<EMAIL>"]

  # FineOS ETL Variables, cron expressions are in ET timezone
  dor_fineos_etl_schedule_enabled    = true                        # true or false
  dor_fineos_etl_schedule_expression = "cron(30 19 ? * MON-FRI *)" # 7:30 PM ET Monday through Friday

  # Used for 1099 only
  localhost_pdf_api_host = "http://localhost:5000"
  # Used for all other PDF generation
  generate_1099_max_files         = "1000"
  upload_max_files_to_fineos      = "10"
  enable_1099_testfile_generation = "0" # "0" or "1"
  irs_1099_correction_ind         = "0" # "0" or "1"
  irs_1099_tax_year               = ""  # year like 2023


  enable_offset_get_1099      = "0"     # "0" or "1"
  upload_1099_doc_batch_start = "1"     # "0" or "1"
  upload_1099_doc_batch_end   = "10000" # a number like "10000"

  enable_fineos_api_logging_to_db = "0"

  # PDF API Service Variables
  pdf_api_lb_port = 3605

  use_overpayment_table_for_over_payments_max_weekly_benefit = "0" # "0" or "1"

  enable_pub_undeliverable_checks_file_processing = "1" # "0" or "1"
  enable_employer_overpayment                     = "0" # "0" or "1"

  pub_payment_starting_check_number = "106" # a number like "106"

  # Allow link_claim_to_application step to translate notifications
  link_claim_to_application_translation = "0"

  enable_sync_claims_step                   = "1" # "0" or "1"
  enable_sync_employees_step                = "1" # "0" or "1"
  enable_sync_efts_step                     = "1" # "0" or "1"
  enable_sync_absence_periods_step          = "1" # "0" or "1"
  enable_sync_absence_paid_leave_cases_step = "1" # "0" or "1"
  enable_sync_leave_requests_step           = "1" # "0" or "1"

  # Allow link_claim_to_application step to send notifications
  portal_base_url      = "https://paidleave-$ENV_NAME.dfml.eol.mass.gov"
  service_now_base_url = "https://savilinxuat.servicenowservices.com"

  release_version = var.release_version

  enable_prepaid_impact_payments = "1"
  enable_sync_payment_preference = "1"
  enable_s3_direct_copy          = "0"

  ####  Cloudwatch ecs schedules

  appeals_generate_intake_pdfs_schedule_expression = ""    # cron(30 6-20 ? * MON-FRI *)
  enable_appeals_generate_intake_pdfs              = false # true or false

  appeals_import_extract_schedule_expression = ""
  enable_appeals_import_extract              = false # true or false

  child_support_dor_import_schedule_expression = ""
  enable_child_support_automation              = "0" # 0 or 1 

  cps_errors_crawler_schedule_expression = ""
  enable_cps_errors_crawler              = true # true or false

  export_60_day_comms_report_schedule_expression = ""
  enable_export_60_day_comms_report              = true # true or false

  export_leave_admins_created_schedule_expression = ""
  enable_export_leave_admins_created              = true # true or false

  export_psd_report_schedule_expression = ""
  enable_export_psd_report              = true # true or false

  fineos_data_export_tool_schedule_expression = ""
  enable_fineos_data_export_tool              = true # true or false

  fineos_error_extract_tool_schedule_expression = ""
  enable_fineos_error_extract_tool              = true # true or false

  fineos_import_employee_updates_schedule_expression = ""
  enable_standalone_fineos_import_employee_updates   = true # true or false

  saturday_fineos_import_employee_updates_schedule_expression = ""
  enable_saturday_standalone_fineos_import_employee_updates   = true # true or false

  fineos_import_la_units_schedule_expression = ""
  enable_fineos_import_la_units              = true # true or false

  fineos_monthly_extract_scheduler_schedule_expression = ""
  enable_fineos_monthly_extract_scheduler              = true # true or false

  fineos_report_extract_tool_schedule_expression = ""
  enable_fineos_report_extracts_tool             = true # true or false

  fineos_snapshot_extract_schedule_expression = ""
  enable_fineos_snapshot_extract_tool         = true # true or false

  import_fineos_to_warehouse_schedule_expression = ""
  enable_import_fineos_to_warehouse              = true # true or false

  # If you are updating "enable_1099_generator" from "false" to "true", you must also update
  # pub_payments_process_1099_documents_schedule's "start_date" in eventbridge.tf since its
  # current value is older than what EventBridge allows. It has to run every other Thursday
  # at 13:00 UTC since "2024-02-01T13:00:00Z". Reach out to Infra Team for additional help.

  # we are switching to the cron scheduler to schedule the job evry business day. This will be 
  # revert back to the rate(14 days) in future. That time we need to uncomment the rate
  # and remove the cron scheduler below
  # pub_payments_process_1099_documents_schedule_expression = "rate(14 days)"
  pub_payments_process_1099_documents_schedule_expression = ""   # cron(0 12 ? * WED,FRI *)
  enable_1099_generator                                   = true # true or false

  pub_payments_copy_audit_report_schedule_expression = ""
  enable_pub_payments_copy_audit_report_schedule     = true # true or false

  pub_payments_process_fineos_schedule_expression        = ""
  pub_payments_process_snapshot_schedule_expression      = ""
  pub_payments_verify_fineos_extract_schedule_expression = ""
  enable_pub_automation_fineos                           = true # true or false
  enable_pub_payments_process_snapshot_fineos            = false

  pub_process_weekly_reports_schedule_expression = ""
  enable_pub_process_weekly_reports              = true # true or false

  reductions_dia_send_claimant_lists_schedule_expression     = ""
  reductions_dua_send_claimant_lists_schedule_expression     = ""
  enable_reductions_send_claimant_lists_to_agencies_schedule = true # true or false

  reductions_process_agency_data_lists_schedule_expression = ""
  enable_reductions_process_agency_data_schedule           = false # true or false

  process_prepaid_debit_registration_schedule_expression = ""
  enable_process_prepaid_debit_registration              = false # true or false

  dua_wages_from_dor_import_schedule_expression = ""
  enable_dua_wages_from_dor_import_schedule     = true

  # Weekend Jobs

  weekend_fineos_import_la_units_schedule_expression = ""
  enable_weekend_fineos_import_la_units              = false # true or false

  sunday_pub_payments_process_fineos_schedule_expression = ""
  enable_sunday_pub_payments_process_fineos_schedule     = false # true or false

  saturday_pub_payments_process_fineos_schedule_expression = ""
  enable_saturday_pub_payments_process_fineos_schedule     = false # true or false

  process_overpayment_referrals_schedule_expression = ""
  enable_process_overpayment_referrals              = false # true or false

  process_mmars_response_file_schedule_expression = ""
  enable_process_mmars_response_file              = false # true or false

  process_overpayment_collections_schedule_expression = ""
  enable_process_overpayment_collections              = false # true or false
}
