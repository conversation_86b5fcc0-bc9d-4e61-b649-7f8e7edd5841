/**
 * @file Configuration for building an xstate state machine for routing
 * @see https://xstate.js.org/docs/about/concepts.html#finite-state-machines
 * Each state represents a page in the portal application, keyed by the
 * page's url route. The CONTINUE transition represents the next page in the
 * the application flow.
 */
import EmployerExemptionsApplication, {
  EmployerExemptionApplicationStatus,
} from "src/models/EmployerExemptionsApplication";

import { ExemptionRequestSteps } from "src/models/EmployerExemptionsStep";
import type { GuardFn } from "./index";
import { UserLeaveAdministrator } from "src/models/User";
import { fields as contactDetailsFields } from "src/pages/employers/exemption-applications/contact-details";
import { fields as insurancePlanDetailFields } from "src/pages/employers/exemption-applications/insurance-details";
import { fields as organizationDetailsFields } from "src/pages/employers/exemption-applications/organization-details";
import routes from "src/routes";
import { fields as selfInsuredBenefitAndContributionDetailsFields } from "src/pages/employers/exemption-applications/self-insured/benefit-and-contribution-details";
import { fields as selfInsuredEmployeeCoverageDetailsFields } from "src/pages/employers/exemption-applications/self-insured/employee-coverage-details";
import { fields as selfInsuredEmployeeLeaveOptionsDetailsFields } from "src/pages/employers/exemption-applications/self-insured/leave-options-details";
import { fields as selfInsuredIntermittentLeaveDetailsFields } from "src/pages/employers/exemption-applications/self-insured/intermittent-leave-details";
import { fields as selfInsuredWorkplacePolicyDetailsFields } from "src/pages/employers/exemption-applications/self-insured/workplace-policy-details";
import { fields as thirdPartyAdministratorContactDetailsFields } from "src/pages/employers/exemption-applications/third-party-administrator-contact-details";
import { fields as thirdPartyAdministratorFields } from "src/pages/employers/exemption-applications/third-party-administrator";

export interface EmployerFlowContext {
  leaveAdmin?: UserLeaveAdministrator;
  exemptionRequest?: EmployerExemptionsApplication;
}

/**
 * Events shared by checklist and review
 */
const progressEvents = {
  CONTACT_DETAILS: routes.employers.employerExemptions.contactDetails,
  INSURANCE_DETAILS: routes.employers.employerExemptions.insuranceDetails,
  ORGANIZATION: routes.employers.employerExemptions.organizationDetails,
  DOCUMENT_UPLOAD: routes.employers.employerExemptions.documentUpload,
};
interface ConditionalEvent {
  target: string;
  cond?: string | { type: string; guardKeys: string[] };
}

export interface ExemptionsFlowState {
  meta?: {
    applicableRules?: string[];
    fields?: string[];
    step?: string;
  };
  on: { [event: string]: string | ConditionalEvent[] };
}

/**
 * @see https://xstate.js.org/docs/guides/guards.html
 */
export const guards: { [guardName: string]: GuardFn } = {
  isMissingVerificationData: ({ leaveAdmin }) => {
    return !leaveAdmin?.has_verification_data;
  },
  isVerified: ({ leaveAdmin }) => {
    return !!leaveAdmin?.verified;
  },
  hasVerificationData: ({ leaveAdmin }) => {
    return !!leaveAdmin?.has_verification_data;
  },
  /**
   * Determines if exemption request has a status of draft. This method is used
   * by pfml/portal/src/pages/employers/welcome.tsx to perform conditional
   * routing.
   *
   * @param {EmployerExemptionsApplication} exemptionRequest - Exemption Request to evalaute
   * @returns {boolean} - true if exemptionRequest has a status of draft; false otherwise
   */
  isDraftEmployerExemptionRequest: ({ exemptionRequest }) => {
    return (
      exemptionRequest?.employer_exemption_application_status_id ===
      EmployerExemptionApplicationStatus.draft.value
    );
  },
  noContributionsOrLeaveAdmin: ({ leaveAdmin }) => {
    return (
      !leaveAdmin?.has_verification_data &&
      !leaveAdmin?.has_verified_leave_admin
    );
  },
  noContributionsButHasLeaveAdmin: ({ leaveAdmin }) => {
    return (
      !leaveAdmin?.has_verification_data &&
      !!leaveAdmin?.has_verified_leave_admin
    );
  },
  hasThirdPartyAdministrator: ({ exemptionRequest }) => {
    return exemptionRequest?.has_third_party_administrator ?? false;
  },
  isSelfInsuredExemptionRequest: ({ exemptionRequest }) => {
    return exemptionRequest?.is_self_insured_plan ?? false;
  },
  isLegallyAcknowledgedDraftEmployerExemptionRequest: ({
    exemptionRequest,
  }) => {
    return (
      (exemptionRequest?.is_legally_acknowledged ?? false) &&
      exemptionRequest?.employer_exemption_application_status_id ===
        EmployerExemptionApplicationStatus.draft.value
    );
  },
};

const employerFlow: {
  states: { [route: string]: ExemptionsFlowState };
} = {
  states: {
    [routes.employers.welcome]: {
      meta: {},
      on: {
        EDIT_USER_INFO: routes.user.contactInfo,
        VERIFY_ORGS: routes.employers.organizations,
        EXEMPTIONS: [
          {
            target: routes.employers.employerExemptions.progress,
            cond: "isLegallyAcknowledgedDraftEmployerExemptionRequest",
          },
          {
            target: routes.employers.employerExemptions.legal,
            cond: "isDraftEmployerExemptionRequest",
          },
          { target: routes.employers.employerExemptions.getReady },
        ],
      },
    },
    [routes.employers.applications]: {
      meta: {},
      on: {
        VERIFY_ORG: routes.employers.organizations,
        VIEW_CLAIM: routes.employers.status,
        VIEW_LEAVE_ALLOTMENT: routes.employers.leaveAllotment,
      },
    },
    [routes.employers.dashboard]: {
      meta: {},
      on: {
        REDIRECT: routes.employers.applications,
      },
    },
    [routes.employers.addOrganization]: {
      meta: {},
      on: {
        CONTINUE: [
          {
            target: routes.employers.verifyContributions,
            cond: "hasVerificationData",
          },
          {
            target: routes.employers.verifyMTC,
            cond: "noContributionsOrLeaveAdmin",
          },
          {
            target: routes.employers.cannotVerify,
            cond: "noContributionsButHasLeaveAdmin",
          },
        ],
      },
    },
    [routes.employers.confirmation]: {
      meta: {},
      on: {},
    },
    [routes.employers.leaveAllotment]: {
      meta: {},
      on: {
        VIEW_CLAIM: routes.employers.status,
      },
    },
    [routes.employers.organizations]: {
      meta: {},
      on: {
        ADD_ORG: routes.employers.addOrganization,
        SHOW_ORG: [
          { target: routes.employers.orgDetails, cond: "isVerified" },
          {
            target: routes.employers.verifyContributions,
            cond: "hasVerificationData",
          },
          {
            target: routes.employers.verifyMTC,
            cond: "noContributionsOrLeaveAdmin",
          },
          {
            target: routes.employers.cannotVerify,
            cond: "noContributionsButHasLeaveAdmin",
          },
        ],
      },
    },
    [routes.employers.verifyMTC]: {
      meta: {},
      on: {
        CONTINUE: routes.employers.verificationSuccess,
        ERROR: routes.employers.cannotVerify,
      },
    },
    [routes.employers.review]: {
      meta: {},
      on: {
        CONTINUE: routes.employers.success,
        REDIRECT_REVIEWED_CLAIM: routes.employers.status,
      },
    },
    [routes.employers.orgDetails]: {
      meta: {},
      on: {
        ADD_LA: routes.employers.addLA,
        BACK: routes.employers.organizations,
        REMOVE_LA: routes.employers.removeLA,
      },
    },
    [routes.employers.removeLA]: {
      meta: {},
      on: {
        REDIRECT: routes.employers.orgDetails,
        REDIRECT_FROM_SELF_REMOVAL: routes.employers.organizations,
      },
    },
    [routes.employers.addLA]: {
      meta: {},
      on: {
        REDIRECT: routes.employers.orgDetails,
      },
    },
    [routes.employers.status]: {
      meta: {},
      on: {
        REDIRECT_REVIEWABLE_CLAIM: routes.employers.review,
      },
    },
    [routes.employers.success]: {
      meta: {},
      on: {
        BACK: routes.employers.applications,
      },
    },
    [routes.employers.newApplication]: {
      meta: {},
      on: {
        REDIRECT: routes.employers.status,
      },
    },
    [routes.employers.cannotVerify]: {
      meta: {},
      on: {},
    },
    [routes.employers.verificationSuccess]: {
      meta: {},
      on: {
        APPLICATIONS: routes.employers.applications,
        CONTINUE: routes.employers.organizations,
        ORG_DETAILS: routes.employers.orgDetails,
      },
    },
    [routes.employers.verifyContributions]: {
      meta: {},
      on: {
        CONTINUE: routes.employers.verificationSuccess,
      },
    },
    [routes.employers.employerExemptions.getReady]: {
      meta: {},
      on: {
        BACK: routes.employers.welcome,
        LEGAL: routes.employers.employerExemptions.legal,
      },
    },
    [routes.employers.employerExemptions.legal]: {
      meta: {},
      on: {
        BACK: routes.employers.employerExemptions.getReady,
        CONTINUE: routes.employers.employerExemptions.progress,
      },
    },
    [routes.employers.employerExemptions.contactDetails]: {
      meta: {
        fields: contactDetailsFields,
        step: ExemptionRequestSteps.contactDetails,
      },
      on: {
        CONTINUE: routes.employers.employerExemptions.thirdPartyAdministrator,
      },
    },
    [routes.employers.employerExemptions.thirdPartyAdministrator]: {
      meta: {
        fields: thirdPartyAdministratorFields,
        step: ExemptionRequestSteps.contactDetails,
      },
      on: {
        CONTINUE: [
          {
            target:
              routes.employers.employerExemptions
                .thirdPartyAdministratorContactDetails,
            cond: "hasThirdPartyAdministrator",
          },
          {
            target: routes.employers.employerExemptions.progress,
          },
        ],
      },
    },
    [routes.employers.employerExemptions.thirdPartyAdministratorContactDetails]:
      {
        meta: {
          fields: thirdPartyAdministratorContactDetailsFields,
          step: ExemptionRequestSteps.contactDetails,
        },
        on: {
          CONTINUE: routes.employers.employerExemptions.progress,
        },
      },
    [routes.employers.employerExemptions.insuranceDetails]: {
      meta: {
        step: ExemptionRequestSteps.insuranceDetails,
        fields: insurancePlanDetailFields,
      },
      on: {
        CONTINUE: [
          {
            target:
              routes.employers.employerExemptions
                .selfInsuredEmployeeCoverageDetails,
            cond: "isSelfInsuredExemptionRequest",
          },
          { target: routes.employers.employerExemptions.progress },
        ],
      },
    },
    [routes.employers.employerExemptions.selfInsuredEmployeeCoverageDetails]: {
      meta: {
        step: ExemptionRequestSteps.insuranceDetails,
        fields: selfInsuredEmployeeCoverageDetailsFields,
      },
      on: {
        BACK: routes.employers.employerExemptions.insuranceDetails,
        CONTINUE:
          routes.employers.employerExemptions.selfInsuredLeaveOptionsDetails,
      },
    },
    [routes.employers.employerExemptions.selfInsuredLeaveOptionsDetails]: {
      meta: {
        step: ExemptionRequestSteps.insuranceDetails,
        fields: selfInsuredEmployeeLeaveOptionsDetailsFields,
      },
      on: {
        BACK: routes.employers.employerExemptions
          .selfInsuredEmployeeCoverageDetails,
        CONTINUE:
          routes.employers.employerExemptions
            .selfInsuredBenefitAndContributionDetails,
      },
    },
    [routes.employers.employerExemptions
      .selfInsuredBenefitAndContributionDetails]: {
      meta: {
        step: ExemptionRequestSteps.insuranceDetails,
        fields: selfInsuredBenefitAndContributionDetailsFields,
      },
      on: {
        BACK: routes.employers.employerExemptions
          .selfInsuredLeaveOptionsDetails,
        CONTINUE:
          routes.employers.employerExemptions.selfInsuredWorkplacePolicyDetails,
      },
    },
    [routes.employers.employerExemptions.selfInsuredWorkplacePolicyDetails]: {
      meta: {
        step: ExemptionRequestSteps.insuranceDetails,
        fields: selfInsuredWorkplacePolicyDetailsFields,
      },
      on: {
        BACK: routes.employers.employerExemptions
          .selfInsuredBenefitAndContributionDetails,
        CONTINUE:
          routes.employers.employerExemptions
            .selfInsuredIntermittentLeaveDetails,
      },
    },
    [routes.employers.employerExemptions.selfInsuredIntermittentLeaveDetails]: {
      meta: {
        step: ExemptionRequestSteps.insuranceDetails,
        fields: selfInsuredIntermittentLeaveDetailsFields,
      },
      on: {
        BACK: routes.employers.employerExemptions
          .selfInsuredWorkplacePolicyDetails,
        CONTINUE: routes.employers.employerExemptions.progress,
      },
    },

    [routes.employers.employerExemptions.documentUpload]: {
      meta: {
        step: ExemptionRequestSteps.uploadDocuments,
      },
      on: {
        CONTINUE: [
          {
            target:
              routes.employers.employerExemptions
                .selfInsuredSuretyBondDocumentUpload,
            cond: "isSelfInsuredExemptionRequest",
          },
          { target: routes.employers.employerExemptions.progress },
        ],
      },
    },

    [routes.employers.employerExemptions.selfInsuredSuretyBondDocumentUpload]: {
      meta: {
        step: ExemptionRequestSteps.uploadDocuments,
      },
      on: {
        BACK: routes.employers.employerExemptions.documentUpload,
        CONTINUE:
          routes.employers.employerExemptions
            .selfInsuredProofOfBenefitsDocumentUpload,
      },
    },

    [routes.employers.employerExemptions
      .selfInsuredProofOfBenefitsDocumentUpload]: {
      meta: {
        step: ExemptionRequestSteps.uploadDocuments,
      },
      on: {
        BACK: routes.employers.employerExemptions
          .selfInsuredSuretyBondDocumentUpload,
        CONTINUE: routes.employers.employerExemptions.progress,
      },
    },

    [routes.employers.employerExemptions.organizationDetails]: {
      meta: {
        fields: organizationDetailsFields,
        step: ExemptionRequestSteps.organization,
      },
      on: {
        CONTINUE: routes.employers.employerExemptions.progress,
      },
    },
    [routes.employers.employerExemptions.progress]: {
      meta: {},
      on: {
        REVIEW: routes.employers.employerExemptions.review,
        HOME: routes.employers.welcome,
        ...progressEvents,
      },
    },
    [routes.employers.employerExemptions.review]: {
      meta: {},
      on: {
        CONTINUE: routes.employers.employerExemptions.confirmation,
        ...progressEvents,
      },
    },
    [routes.employers.employerExemptions.confirmation]: {
      meta: {},
      on: {},
    },
  },
};
export default employerFlow;
