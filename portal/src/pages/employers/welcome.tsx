import React, { useEffect, useReducer } from "react";

import { AppLogic } from "src/hooks/useAppLogic";
import ConditionalContent from "src/components/ConditionalContent";
import EmployerNavigationTabs from "src/components/EmployerNavigationTabs";
import EmployerSatisfactionSurveyBanner from "src/components/EmployerSatisfactionSurveyBanner";
import ExternalLink from "src/components/core/ExternalLink";
import Heading from "src/components/core/Heading";
import Icon from "src/components/core/Icon";
import Link from "next/link";
import MayflowerIcon from "src/components/mayflower/MayflowerIcon";
import SummaryBox from "src/components/SummaryBox";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import User from "src/models/User";
import { isFeatureEnabled } from "src/services/featureFlags";
import routes from "src/routes";
import { useChatWidget } from "src/hooks/useChatWidget";
import { useTranslation } from "src/locales/i18n";
import withUser from "src/hoc/withUser";

const iconProps = {
  className: "margin-right-2 text-secondary text-middle",
  height: "32",
  width: "32",
  fill: "currentColor",
};
// TODO (EMPLOYER-555) Change to alternate icon from USWDS
const IconWait = () => (
  <svg aria-hidden="true" viewBox="0 0 40 40" {...iconProps}>
    <path d="M40 20c0 11.045-8.96 20-20.01 20-1.8 0-3.54-.26-5.2-.705l1.11-3.157c1.31.332 2.68.529 4.09.529 9.2 0 16.68-7.477 16.68-16.667 0-9.19-7.48-16.667-16.68-16.667-3.72 0-7.15 1.24-9.93 3.316l3.42 3.414L1.79 12.32 4.05.632 7.68 4.27C11.08 1.607 15.34 0 19.99 0 31.04 0 40 8.955 40 20zM21.67 10v10H30v3.333H18.33V10zM5.78 28.667c.56.91 1.2 1.761 1.91 2.546l-2.34 2.389a20.092 20.092 0 01-2.56-3.43zm2.04 7.181l2.36-2.405a16.69 16.69 0 002.59 1.555l-1.11 3.165a19.847 19.847 0 01-3.84-2.315zM4.33 25.662l-2.99 1.51a19.43 19.43 0 01-1.09-4.101l3.33-.264c.16.983.42 1.936.75 2.855zM0 19.748l3.36-.268c.04-1.172.21-2.31.48-3.412H.38c-.24 1.19-.36 2.422-.38 3.68z" />
  </svg>
);

interface WelcomeProps {
  appLogic: AppLogic;
  user: User;
}

export const Welcome = ({ appLogic, user }: WelcomeProps) => {
  useChatWidget(user, appLogic.chatWidget);
  const { t } = useTranslation();
  let showNameAndPhone = true;
  const { phone_number, first_name, last_name } = user;
  if (!first_name || !last_name || !phone_number?.phone_number) {
    showNameAndPhone = false;
  }

  // employer exemptions do not have the ability to choose a leave admin employer
  // default to the first leave admin employer.
  // TODO (PFMLPB-22213): choose correct leave admin employer when leave admin
  // has multiple organizations
  const leaveAdminEmployerId = user.user_leave_administrators[0]?.employer_id;
  const { employerExemptionsApplication } = appLogic;

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const getRequestExemptionLink = () => {
    let url;

    if (user.user_leave_administrators.length === 0) {
      url = routes.employers.organizations;
    } else {
      url = appLogic.portalFlow.getRouteFor(
        "EXEMPTIONS",
        {
          exemptionRequest:
            employerExemptionsApplication.getDraftEmployerExemptionApplication(
              leaveAdminEmployerId
            ),
        },
        {
          employer_exemption_application_id:
            employerExemptionsApplication.getDraftEmployerExemptionApplicationId(
              leaveAdminEmployerId
            ),
        }
      );
    }

    return url;
  };

  const [requestAnExemptionLink, setRequestAnExemptionLink] = useReducer(
    getRequestExemptionLink,
    undefined
  );

  useEffect(() => {
    if (isEnableEmployerExemptionsPortal) {
      employerExemptionsApplication.loadAll();
      setRequestAnExemptionLink();
    }
  }, [isEnableEmployerExemptionsPortal, employerExemptionsApplication]);

  return (
    <React.Fragment>
      <div className="grid-row">
        <EmployerNavigationTabs
          activePath={appLogic.portalFlow.pathname}
          hasEmployerExemptionRequests={appLogic.employerExemptionsApplication.hasEmployerExemptionRequests()}
        />
      </div>
      <div className="grid-row">
        <EmployerSatisfactionSurveyBanner />
      </div>
      <div className="grid-row">
        <div className="desktop:grid-col-8">
          <Title>{t("pages.employersWelcome.welcomeTitle")}</Title>
          {isEnableEmployerExemptionsPortal &&
            employerExemptionsApplication.hasLoadedEmployerExemptionApplicationsList && (
              <div>
                <SummaryBox
                  body={t("pages.employersWelcome.summaryBoxText")}
                  list={
                    <Trans
                      i18nKey="pages.employersWelcome.summaryBoxListItems"
                      components={{
                        "see-all-applications-link": (
                          <a href={routes.employers.applications} />
                        ),
                        "request-an-exemption-link": (
                          <a href={requestAnExemptionLink} />
                        ),
                        ul: <ul className="usa-list" />,
                        li: <li />,
                      }}
                    />
                  }
                />
                <Heading level="2">
                  {t("pages.employersWelcome.manageApplicationsTitle")}
                </Heading>
              </div>
            )}
          {!isEnableEmployerExemptionsPortal && (
            <p>{t("pages.employersWelcome.welcomeBody")}</p>
          )}

          <Heading level={isEnableEmployerExemptionsPortal ? "3" : "2"}>
            <Icon name="list" size={4} {...iconProps} />
            {isEnableEmployerExemptionsPortal
              ? t("pages.employersWelcome.viewApplicationsTitle_v2")
              : t("pages.employersWelcome.viewApplicationsTitle")}
          </Heading>
          <p>
            <Trans
              i18nKey={
                isEnableEmployerExemptionsPortal
                  ? "pages.employersWelcome.viewApplicationsBody_v2"
                  : "pages.employersWelcome.viewApplicationsBody"
              }
              components={{
                "applications-link": <a href={routes.employers.applications} />,
              }}
            />
          </p>

          <Heading level={isEnableEmployerExemptionsPortal ? "3" : "2"}>
            <Icon name="mail" size={4} {...iconProps} />
            {t("pages.employersWelcome.checkEmailTitle")}
          </Heading>

          <p>
            <Trans
              i18nKey="pages.employersWelcome.checkEmailBody"
              components={{
                "compliance-link": (
                  <ExternalLink href={routes.external.maLegislature.section4} />
                ),
              }}
            />
          </p>

          <Heading level={isEnableEmployerExemptionsPortal ? "3" : "2"}>
            <IconWait />
            {t("pages.employersWelcome.respondTitle")}
          </Heading>
          <p>{t("pages.employersWelcome.respondBody")}</p>

          <Heading level={isEnableEmployerExemptionsPortal ? "3" : "2"}>
            <MayflowerIcon name="pdf" {...iconProps} />
            {t("pages.employersWelcome.viewFormsTitle")}
          </Heading>
          <p>
            <Trans
              i18nKey="pages.employersWelcome.viewFormsBody"
              components={{
                "healthcare-provider-form-link": (
                  <ExternalLink
                    href={routes.external.massgov.healthcareProviderForm}
                  />
                ),
                "caregiver-certification-form-link": (
                  <ExternalLink
                    href={routes.external.massgov.caregiverCertificationForm}
                  />
                ),
              }}
            />
          </p>
          <ConditionalContent visible={isEnableEmployerExemptionsPortal}>
            <Heading level="2">
              {t("pages.employersWelcome.requestExemptionTitle")}
            </Heading>
            <p>
              <Trans
                i18nKey="pages.employersWelcome.requestExemptionBody"
                components={{
                  "request-an-exemption-link": (
                    <a href={requestAnExemptionLink} />
                  ),
                }}
              />
            </p>

            <Heading level="2">
              {t("pages.employersWelcome.manageExemptionsTitle")}
            </Heading>
            <p>{t("pages.employersWelcome.manageExemptionsBody")}</p>
          </ConditionalContent>
        </div>
        <div className="grid-col-fill" />
        <aside className="desktop:grid-col-3 margin-top-7 desktop:margin-top-1">
          <Heading level="2">
            {t("pages.employersWelcome.settingsTitle")}
          </Heading>
          <ul className="usa-list desktop:font-body-2xs desktop:padding-top-05">
            {!showNameAndPhone && (
              <li>
                <Link href={appLogic.portalFlow.getRouteFor("EDIT_USER_INFO")}>
                  {t("pages.employersWelcome.contactInfo")}
                </Link>
              </li>
            )}
            {showNameAndPhone && (
              <li>
                <p className="text-bold">
                  <Trans
                    i18nKey="data.user.any"
                    values={{
                      value: first_name + " " + last_name,
                    }}
                  />
                </p>
                <Trans
                  i18nKey="data.user.any"
                  values={{
                    value: `${phone_number?.phone_number} ${
                      phone_number?.extension
                        ? "ext." + phone_number.extension
                        : ""
                    }`,
                  }}
                />
                <Link
                  href={appLogic.portalFlow.getRouteFor("EDIT_USER_INFO")}
                  className="margin-left-1"
                >
                  {t("pages.employersWelcome.contactInfoEditLabel")}
                </Link>
              </li>
            )}
          </ul>
          <Heading level="2">
            {t("pages.employersWelcome.learnMoreTitle")}
          </Heading>
          <Trans
            i18nKey="pages.employersWelcome.learnMoreLinks"
            components={{
              ul: (
                <ul className="usa-list desktop:font-body-2xs desktop:padding-top-05" />
              ),
              li: <li />,
              "mass-employer-role-link": (
                <ExternalLink href={routes.external.massgov.employersGuide} />
              ),
              "reimbursements-link": (
                <ExternalLink
                  href={routes.external.massgov.employerReimbursementsRequest}
                />
              ),
            }}
          />
        </aside>
      </div>
    </React.Fragment>
  );
};

export default withUser(Welcome);
