import Step, { ExemptionRequestSteps } from "src/models/EmployerExemptionsStep";
import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import BackButton from "src/components/BackButton";
import ConditionalContent from "src/components/ConditionalContent";
import HeadingPrefix from "src/components/core/HeadingPrefix";
import { NullableQueryParams } from "src/utils/routeWithParams";
import PageNotFound from "src/components/PageNotFound";
import React from "react";
import ReviewHeading from "src/components/ReviewHeading";
import ReviewRow from "src/components/ReviewRow";
import ThrottledButton from "src/components/ThrottledButton";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import { pageQuestions as contactDetailQuestions } from "./contact-details";
import employerConfigs from "src/flows/employer";
import getNestedObjectValue from "src/utils/getNestedObjectValue";
import { pageQuestions as hasTPAQuesions } from "./third-party-administrator";
import { pageQuestions as insuranceDetailsQuestionsPurchased } from "./insurance-details/purchased-plan";
import { pageQuestions as insuranceDetailsQuestionsSelfInsured } from "./insurance-details/self-insured-plan";
import { pageQuestions as insuranceDetailsQuestionsSelfInsured1of5 } from "./self-insured/employee-coverage-details";
import { pageQuestions as insuranceDetailsQuestionsSelfInsured2of5 } from "./self-insured/leave-options-details";
import { pageQuestions as insuranceDetailsQuestionsSelfInsured3of5 } from "./self-insured/benefit-and-contribution-details";
import { pageQuestions as insuranceDetailsQuestionsSelfInsured4of5 } from "./self-insured/workplace-policy-details";
import { pageQuestions as insuranceDetailsQuestionsSelfInsured5of5 } from "./self-insured/intermittent-leave-details";
import { isFeatureEnabled } from "src/services/featureFlags";
import { pageQuestions as organizationDetailQuestions } from "./organization-details";
import { pageQuestions as tpaQuestions } from "./third-party-administrator-contact-details";
// import { pageQuestions as uploadDocumentsQuestions } from "src/pages/employers/exemption-applications/upload-documents";
import { useTranslation } from "src/locales/i18n";

const translationPrefix = "pages.employersExemptionsReview";

export const EmployerExemptionsReview = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { appLogic, user, exemptionRequest } = props;
  const { t } = useTranslation();
  const { user_leave_administrators } = user;

  const steps = Step.createExemptionFromMachine(employerConfigs, {
    exemptionRequest: props.exemptionRequest,
  });
  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const handleSave = async () => {
    await appLogic.employerExemptionsApplication.submit(
      exemptionRequest.employer_exemption_application_id
    );
  };

  const getStepEditHref = (
    name: string,
    additionalParams?: NullableQueryParams
  ) => {
    const step = steps.find((s) => s.name === name);

    if (step && step.editable) {
      return appLogic.portalFlow.getRouteFor(
        step.name,
        {},
        {
          employer_exemption_application_id:
            exemptionRequest.employer_exemption_application_id,
          ...additionalParams,
        }
      );
    }
  };

  const buildReviewRows = (
    questions: Array<{ fieldName: string; questionLabel: string }>
  ) => {
    const reviewRows = questions.reduce((arr, detail) => {
      const exemptionRequestValue = getNestedObjectValue(
        exemptionRequest,
        detail.fieldName
      );
      let displayValue;

      if (typeof exemptionRequestValue === "boolean") {
        displayValue = exemptionRequestValue
          ? t("shared.choiceYes")
          : t("shared.choiceNo");
      } else {
        displayValue = exemptionRequestValue;
      }

      // fields sans values will not be displayed on the progress page
      if (displayValue === undefined || displayValue === null) {
        return arr;
      }

      arr.push(
        <ReviewRow
          key={detail.fieldName + "_" + detail.questionLabel}
          level={"3"}
          label={t(`${detail.questionLabel}`)}
        >
          <Trans
            i18nKey="data.user.any"
            values={{
              value: displayValue,
            }}
          />
        </ReviewRow>
      );

      return arr;
    }, [] as React.JSX.Element[]);

    return <React.Fragment>{reviewRows}</React.Fragment>;
  };

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  return (
    <React.Fragment>
      <BackButton />
      <form onSubmit={handleSave} className="usa-form" method="post">
        <HeadingPrefix>
          <Trans
            i18nKey="shared.employerExemptions.employerEIN"
            values={{
              employer_fein: user_leave_administrators[0].employer_fein,
            }}
          />
        </HeadingPrefix>
        <div className="margin-bottom-5 margin-top-2">
          <Title marginBottom="1">{t(`${translationPrefix}.title`)}</Title>
          <p>{t(`${translationPrefix}.sectionLabelHint`)}</p>
        </div>
        {/* Contact Details */}
        <ReviewHeading
          editHref={getStepEditHref(ExemptionRequestSteps.contactDetails)}
          editText={t(`${translationPrefix}.editLink`)}
          level={"2"}
        >
          {t(`${translationPrefix}.stepHeading`, { context: "contactDetails" })}
        </ReviewHeading>
        {buildReviewRows(contactDetailQuestions)}

        {/* Has TPA? */}
        {buildReviewRows(hasTPAQuesions)}

        {/* TPA Contact Details */}
        <ConditionalContent
          visible={exemptionRequest.has_third_party_administrator === true}
        >
          {buildReviewRows(tpaQuestions)}
        </ConditionalContent>

        {/* Organization Details */}
        <ReviewHeading
          editHref={getStepEditHref(ExemptionRequestSteps.organization)}
          editText={t(`${translationPrefix}.editLink`)}
          level={"2"}
        >
          {t(`${translationPrefix}.stepHeading`, {
            context: "organizationDetails",
          })}
        </ReviewHeading>
        {buildReviewRows(organizationDetailQuestions)}

        {/* insurance plan details */}
        <ReviewHeading
          editHref={getStepEditHref(ExemptionRequestSteps.insuranceDetails)}
          editText={t(`${translationPrefix}.editLink`)}
          level={"2"}
        >
          {t(`${translationPrefix}.stepHeading`, {
            context: "insurancePlanInfo",
          })}
        </ReviewHeading>
        {/* conditional render of purchased plan data */}
        <ConditionalContent
          visible={exemptionRequest.is_self_insured_plan === false}
        >
          {buildReviewRows(insuranceDetailsQuestionsPurchased)}
        </ConditionalContent>

        {/* conditional render of self-insured plan data */}
        <ConditionalContent
          visible={exemptionRequest.is_self_insured_plan === true}
        >
          {buildReviewRows(insuranceDetailsQuestionsSelfInsured)}
          {buildReviewRows(insuranceDetailsQuestionsSelfInsured1of5)}
          {buildReviewRows(insuranceDetailsQuestionsSelfInsured2of5)}
          {buildReviewRows(insuranceDetailsQuestionsSelfInsured3of5)}
          {buildReviewRows(insuranceDetailsQuestionsSelfInsured4of5)}
          {buildReviewRows(insuranceDetailsQuestionsSelfInsured5of5)}
        </ConditionalContent>

        {/* Upload Documents */}
        <ReviewHeading
          editHref={getStepEditHref(ExemptionRequestSteps.uploadDocuments)}
          editText={t(`${translationPrefix}.editLink`)}
          level={"2"}
        >
          {t(`${translationPrefix}.stepHeading`, {
            context: "uploadDocuments",
          })}
        </ReviewHeading>
        <ThrottledButton
          className="margin-top-3"
          onClick={handleSave}
          type="button"
          loadingMessage={t(`${translationPrefix}.submitButton`)}
        >
          {t(`${translationPrefix}.submitButton`)}
        </ThrottledButton>
      </form>
    </React.Fragment>
  );
};

export default withEmployerExemptionsApplication(EmployerExemptionsReview);
