import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import InputChoice from "src/components/core/InputChoice";
import PageNotFound from "src/components/PageNotFound";
import QuestionPage from "src/components/QuestionPage";
import React from "react";
import { Trans } from "react-i18next";
import { isFeatureEnabled } from "src/services/featureFlags";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";
import { useTranslation } from "src/locales/i18n";

export const EmployerExemptionsLegal = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { appLogic, exemptionRequest, user } = props;
  const { t } = useTranslation();
  const { user_leave_administrators } = user;

  const { formState, updateFields } = useFormState({
    is_legally_acknowledged: exemptionRequest.is_legally_acknowledged,
  });

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const handleSave = async () => {
    await appLogic.employerExemptionsApplication.update(
      exemptionRequest.employer_exemption_application_id,
      formState
    );
  };

  const handleIsLegallyAcknowledgedCheckboxChange = () => {
    updateFields({
      is_legally_acknowledged: !formState.is_legally_acknowledged,
    });
  };

  const getFunctionalInputProps = useFunctionalInputProps({
    errors: appLogic.errors,
    formState,
    updateFields,
  });

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  return (
    <QuestionPage
      title={t("pages.employersExemptionsLegalAcknowledgements.title")}
      titleSize="regular"
      disableSubmit={!formState.is_legally_acknowledged}
      headingPrefix={
        <Trans
          i18nKey="shared.employerExemptions.employerEIN"
          values={{
            employer_fein: user_leave_administrators[0].employer_fein,
          }}
        />
      }
      onSave={handleSave}
    >
      <div className="padding-top-1 margin-bottom-3">
        <Trans
          i18nKey="pages.employersExemptionsLegalAcknowledgements.mainContent"
          components={{
            ol: <ol className="usa-list" />,
            li: <li />,
          }}
        />
      </div>
      <InputChoice
        {...getFunctionalInputProps("is_legally_acknowledged")}
        type="checkbox"
        label={t(
          "pages.employersExemptionsLegalAcknowledgements.acknowledgementCheckbox"
        )}
        name={"employer_exemption_legal_acknowledgement"}
        checked={formState.is_legally_acknowledged}
        onChange={handleIsLegallyAcknowledgedCheckboxChange}
      />
    </QuestionPage>
  );
};

export default withEmployerExemptionsApplication(EmployerExemptionsLegal);
