import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import BackButton from "src/components/BackButton";
import ButtonLink from "src/components/ButtonLink";
import Heading from "src/components/core/Heading";
import HeadingPrefix from "src/components/core/HeadingPrefix";
import Icon from "src/components/core/Icon";
import PageNotFound from "src/components/PageNotFound";
import React from "react";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import { isFeatureEnabled } from "src/services/featureFlags";
import { useTranslation } from "src/locales/i18n";

export const EmployerExemptionsConfirmation = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { user, exemptionRequest } = props;
  const { t } = useTranslation();
  const { user_leave_administrators } = user;

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  return (
    <React.Fragment>
      <BackButton />
      <HeadingPrefix>
        <Trans
          i18nKey="shared.employerExemptions.employerEIN"
          values={{
            employer_fein: user_leave_administrators[0].employer_fein,
          }}
        />
      </HeadingPrefix>
      <Title>{t("pages.employersExemptionsConfirmation.title")}</Title>
      <p>
        <Trans
          i18nKey="pages.employersExemptionsConfirmation.yourRequestId"
          values={{
            employer_exemption_application_id:
              exemptionRequest.employer_exemption_application_id,
          }}
        />
      </p>
      <Heading level="2">
        <Icon
          size={4}
          className="margin-right-2 text-secondary text-middle"
          fill="currentColor"
          name="arrow_forward"
        />
        {t("pages.employersExemptionsConfirmation.trackAndManageRequestHeader")}
      </Heading>
      <ButtonLink href={"#"}>
        {t("pages.employersExemptionsConfirmation.viewRequestButtonText")}
      </ButtonLink>
      <Heading level="2">
        <Icon
          name="calendar_today"
          size={4}
          className="margin-right-2 text-secondary text-middle"
          fill="currentColor"
        />
        {t("pages.employersExemptionsConfirmation.whatHappensNextHeading")}
      </Heading>
      <Trans
        i18nKey="pages.employersExemptionsConfirmation.whatHappensNextText"
        components={{
          ul: <ul className="usa-list" />,
          li: <li />,
        }}
      />
      <ButtonLink href="#" onClick={() => window.print()} variation="outline">
        {t("pages.employersExemptionsConfirmation.printConfirmationButtonText")}
      </ButtonLink>
      <Heading level="2">
        <Icon
          name="search"
          size={4}
          className="margin-right-2 margin-bottom-2 text-secondary text-middle"
          fill="currentColor"
        />
        {t("pages.employersExemptionsConfirmation.supportHeading")}
      </Heading>
      <Trans
        i18nKey="pages.employersExemptionsConfirmation.supportText"
        components={{
          "contact-center-phone-link": (
            <a href={`tel:${t("shared.contactCenterPhoneNumber")}`} />
          ),
        }}
      />
    </React.Fragment>
  );
};

export default withEmployerExemptionsApplication(
  EmployerExemptionsConfirmation
);
