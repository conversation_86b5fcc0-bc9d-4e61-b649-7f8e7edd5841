import withUser, { WithUserProps } from "src/hoc/withUser";

import BackButton from "src/components/BackButton";
import Button from "src/components/core/Button";
import ExternalLink from "src/components/core/ExternalLink";
import HeadingPrefix from "src/components/core/HeadingPrefix";
import PageNotFound from "src/components/PageNotFound";
import React from "react";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import { isFeatureEnabled } from "src/services/featureFlags";
import routes from "src/routes";
import useThrottledHandler from "src/hooks/useThrottledHandler";
import { useTranslation } from "src/locales/i18n";

export const GetReadyExemptions = (props: WithUserProps) => {
  const { appLogic, user } = props;
  const { t } = useTranslation();
  const { user_leave_administrators } = user;

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const handleSubmit = useThrottledHandler(async (event) => {
    event.preventDefault();
    await appLogic.employerExemptionsApplication.create(
      user_leave_administrators[0].employer_id
    );
  });

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  return (
    <React.Fragment>
      <BackButton />
      <form onSubmit={handleSubmit} className="usa-form" method="post">
        <HeadingPrefix>
          <Trans
            i18nKey="shared.employerExemptions.employerEIN"
            values={{
              employer_fein: user_leave_administrators[0].employer_fein,
            }}
          />
        </HeadingPrefix>

        <Title>{t("pages.getReadyExemptions.title")}</Title>

        <div className="grid-row grid-gap">
          <div className="tablet:grid-col-12 margin-top-2">
            <Trans i18nKey="pages.getReadyExemptions.mainContent" />
          </div>
          <div className="tablet:grid-col-6 margin-top-2">
            <div>
              <Trans
                i18nKey="pages.getReadyExemptions.purchasedPrivatePlan"
                components={{
                  "approved-list-of-insurance-providers-link": (
                    <ExternalLink
                      href={routes.external.massgov.approvedListOfInsurers}
                    />
                  ),
                  "purchased-private-plan-link": (
                    <ExternalLink
                      href={
                        routes.external.massgov.purchasedPrivatePlanRequirements
                      }
                    />
                  ),
                  "mass-pfml-confirmation-insurance-form-link": (
                    <ExternalLink
                      href={
                        routes.external.massgov
                          .purchasedPrivatePlanConfirmationForm
                      }
                    />
                  ),
                  ul: <ul className="usa-list" />,
                  li: <li />,
                }}
              />
            </div>
          </div>
          <div className="tablet:grid-col-6 margin-top-2">
            <div>
              <Trans
                i18nKey="pages.getReadyExemptions.selfInsuredPrivatePlan"
                components={{
                  "self-insured-private-plan-link": (
                    <ExternalLink
                      href={
                        routes.external.massgov
                          .selfInsuredPrivatePlanRequirements
                      }
                    />
                  ),
                  "self-insuranced-declaration-form": (
                    <ExternalLink
                      href={routes.external.massgov.selfInsuredDeclarationForm}
                    />
                  ),
                  "surety-bond-link": (
                    <ExternalLink href={routes.external.massgov.suretyBond} />
                  ),
                  ul: <ul className="usa-list" />,
                  li: <li />,
                }}
              />
            </div>
          </div>
        </div>

        <Button
          type="submit"
          name="new-employer-exemption-application"
          loading={handleSubmit.isThrottled}
        >
          {t("pages.getReadyExemptions.createExemption")}
        </Button>
      </form>
    </React.Fragment>
  );
};

export default withUser(GetReadyExemptions);
