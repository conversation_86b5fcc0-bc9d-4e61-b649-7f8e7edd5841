import SelfInsuredQuestionList, {
  SelfInsuredQuestion,
} from "./SelfInsuredPageQuestions";
import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import FormLabel from "src/components/core/FormLabel";
import PageNotFound from "src/components/PageNotFound";
import QuestionPage from "src/components/QuestionPage";
import React from "react";
import { Trans } from "react-i18next";
import { isFeatureEnabled } from "src/services/featureFlags";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";
import { useTranslation } from "src/locales/i18n";

// page questions for use on the review page
export const pageQuestions = [
  {
    fieldName: "self_insured.questions.does_plan_pay_enough_benefits",
    questionLabel:
      "pages.employersExemptionsSelfInsuredBenefitAndContributionDetails.doesPlanPayEnoughBenefitsQuestion",
  },
  {
    fieldName: "self_insured.questions.does_employer_withhold_premiums",
    questionLabel:
      "pages.employersExemptionsSelfInsuredBenefitAndContributionDetails.doesEmployerWithholdPremiumsQuestion",
  },
  {
    fieldName:
      "self_insured.questions.are_employer_withholdings_within_allowable_amount",
    questionLabel:
      "pages.employersExemptionsSelfInsuredBenefitAndContributionDetails.areEmployerWithholdingsWithinAllowableAmountQuestion",
  },
];

/* 
  prefix each field 'exemptionRequest.' this is needed for the employer 
  exemptions progress page to display correctly
  */
export const fields = [
  "exemptionRequest.self_insured.questions.does_plan_pay_enough_benefits",
  "exemptionRequest.self_insured.questions.does_employer_withhold_premiums",
  "exemptionRequest.self_insured.questions.are_employer_withholdings_within_allowable_amount",
];

const translationPrefix =
  "pages.employersExemptionsSelfInsuredBenefitAndContributionDetails";

export const EmployerExemptionsSelfInsuredBenefitAndContributionDetails = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { appLogic, exemptionRequest, user } = props;
  const { t } = useTranslation();
  const { user_leave_administrators } = user;

  const { formState, updateFields } = useFormState({
    self_insured: {
      questions: {
        does_plan_pay_enough_benefits:
          exemptionRequest.self_insured?.questions
            ?.does_plan_pay_enough_benefits,
        does_employer_withhold_premiums:
          exemptionRequest.self_insured?.questions
            ?.does_employer_withhold_premiums,
        are_employer_withholdings_within_allowable_amount:
          exemptionRequest.self_insured?.questions
            ?.are_employer_withholdings_within_allowable_amount,
      },
    },
  });

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const handleSave = async () => {
    await appLogic.employerExemptionsApplication.update(
      exemptionRequest.employer_exemption_application_id,
      formState
    );
  };

  const getFunctionalInputProps = useFunctionalInputProps({
    errors: appLogic.errors,
    formState,
    updateFields,
  });

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  const doesPlanPayEnoughBenefitsQuestion: SelfInsuredQuestion = {
    label: `${translationPrefix}.doesPlanPayEnoughBenefitsQuestion`,
    inputProps: getFunctionalInputProps(
      "self_insured.questions.does_plan_pay_enough_benefits"
    ),
  };

  const doesEmployerWithholdPremiumsQuestion: SelfInsuredQuestion = {
    label: `${translationPrefix}.doesEmployerWithholdPremiumsQuestion`,
    inputProps: getFunctionalInputProps(
      "self_insured.questions.does_employer_withhold_premiums"
    ),
  };

  const areEmployerWithholdingsWithinAllowableAmountQuestion: SelfInsuredQuestion =
    {
      label: `${translationPrefix}.areEmployerWithholdingsWithinAllowableAmountQuestion`,
      inputProps: getFunctionalInputProps(
        "self_insured.questions.are_employer_withholdings_within_allowable_amount"
      ),
    };

  const questions: SelfInsuredQuestion[] = [
    doesPlanPayEnoughBenefitsQuestion,
    doesEmployerWithholdPremiumsQuestion,
    areEmployerWithholdingsWithinAllowableAmountQuestion,
  ];

  return (
    <React.Fragment>
      <QuestionPage
        title={t(`${translationPrefix}.title`)}
        headingPrefix={
          <Trans
            i18nKey="shared.employerExemptions.employerEIN"
            values={{
              employer_fein: user_leave_administrators[0].employer_fein,
            }}
          />
        }
        onSave={handleSave}
      >
        <FormLabel component="legend">
          {t(`${translationPrefix}.sectionLabel`)}
        </FormLabel>
        <SelfInsuredQuestionList
          labelTranslationPrefix={translationPrefix}
          questions={questions}
        />
      </QuestionPage>
    </React.Fragment>
  );
};

export default withEmployerExemptionsApplication(
  EmployerExemptionsSelfInsuredBenefitAndContributionDetails
);
