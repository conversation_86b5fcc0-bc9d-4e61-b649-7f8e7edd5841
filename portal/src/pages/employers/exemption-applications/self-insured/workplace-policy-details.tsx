import SelfInsuredQuestionList, {
  SelfInsuredQuestion,
} from "./SelfInsuredPageQuestions";
import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import FormLabel from "src/components/core/FormLabel";
import PageNotFound from "src/components/PageNotFound";
import QuestionPage from "src/components/QuestionPage";
import React from "react";
import { Trans } from "react-i18next";
import { isFeatureEnabled } from "src/services/featureFlags";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";
import { useTranslation } from "src/locales/i18n";

const translationPrefix =
  "pages.employersExemptionsSelfInsuredWorkplacePolicyDetails";

// page questions for use on the review page
const doesPlanProvidePFMLJobProtectionQuestion = {
  fieldName: "self_insured.questions.does_plan_provide_pfml_job_protection",
  questionLabel: `${translationPrefix}.doesPlanProvidePFMLJobProtectionQuestion`,
};
const doesPlanProvideReturnToWorkBenefitsQuestion = {
  fieldName: "self_insured.questions.does_plan_provide_return_to_work_benefits",
  questionLabel: `${translationPrefix}.doesPlanProvideReturnToWorkBenefitsQuestion`,
};

const doesPlanCoverFormerEmployeeContributionQuestion = {
  fieldName: "self_insured.questions.does_plan_cover_employee_contribution",
  questionLabel: `${translationPrefix}.doesPlanCoverFormerEmployeeContributionQuestion`,
};

const doesPlanFavorPaidLeaveBenefitsQuestion = {
  fieldName: "self_insured.questions.does_plan_favor_paid_leave_benefits",
  questionLabel: `${translationPrefix}.doesPlanFavorPaidLeaveBenefitsQuestion`,
};

export const pageQuestions = [
  doesPlanProvidePFMLJobProtectionQuestion,
  doesPlanProvideReturnToWorkBenefitsQuestion,
  doesPlanCoverFormerEmployeeContributionQuestion,
  doesPlanFavorPaidLeaveBenefitsQuestion,
];

/* 
prefix each field 'exemptionRequest.' this is needed for the employer 
exemptions progress page to display correctly
*/
export const fields = pageQuestions.map((question) => {
  return "exemptionRequest." + question.fieldName;
});

export const EmployerExemptionsSelfInsuredWorkplacePolicyDetails = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { appLogic, exemptionRequest, user } = props;
  const { t } = useTranslation();
  const { user_leave_administrators } = user;

  const { formState, updateFields } = useFormState({
    self_insured: {
      questions: {
        does_plan_provide_pfml_job_protection:
          exemptionRequest.self_insured?.questions
            ?.does_plan_provide_pfml_job_protection,
        does_plan_provide_return_to_work_benefits:
          exemptionRequest.self_insured?.questions
            ?.does_plan_provide_return_to_work_benefits,
        does_plan_cover_employee_contribution:
          exemptionRequest.self_insured?.questions
            ?.does_plan_cover_employee_contribution,
        does_plan_favor_paid_leave_benefits:
          exemptionRequest.self_insured?.questions
            ?.does_plan_favor_paid_leave_benefits,
      },
    },
  });

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const handleSave = async () => {
    await appLogic.employerExemptionsApplication.update(
      exemptionRequest.employer_exemption_application_id,
      formState
    );
  };

  const getFunctionalInputProps = useFunctionalInputProps({
    errors: appLogic.errors,
    formState,
    updateFields,
  });

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  const questionList: SelfInsuredQuestion[] = pageQuestions.map((question) => {
    return {
      label: question.questionLabel,
      inputProps: getFunctionalInputProps(question.fieldName),
    };
  });

  return (
    <React.Fragment>
      <QuestionPage
        title={t(`${translationPrefix}.title`)}
        headingPrefix={
          <Trans
            i18nKey="shared.employerExemptions.employerEIN"
            values={{
              employer_fein: user_leave_administrators[0].employer_fein,
            }}
          />
        }
        onSave={handleSave}
      >
        <FormLabel component="legend">
          {t(`${translationPrefix}.sectionLabel`)}
        </FormLabel>
        <SelfInsuredQuestionList
          labelTranslationPrefix={translationPrefix}
          questions={questionList}
        />
      </QuestionPage>
    </React.Fragment>
  );
};

export default withEmployerExemptionsApplication(
  EmployerExemptionsSelfInsuredWorkplacePolicyDetails
);
