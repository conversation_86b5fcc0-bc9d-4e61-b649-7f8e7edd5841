import SelfInsuredQuestionList, {
  SelfInsuredQuestion,
} from "./SelfInsuredPageQuestions";
import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import FormLabel from "src/components/core/FormLabel";
import PageNotFound from "src/components/PageNotFound";
import QuestionPage from "src/components/QuestionPage";
import React from "react";
import { Trans } from "react-i18next";
import { isFeatureEnabled } from "src/services/featureFlags";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";
import { useTranslation } from "src/locales/i18n";

// page questions for use on the review page
export const pageQuestions = [
  {
    fieldName: "self_insured.questions.does_plan_cover_all_employees",
    questionLabel:
      "pages.employersExemptionsSelfInsuredEmployeeCoverageDetails.doesPlanCoverAllEmployeesQuestion",
  },
  {
    fieldName: "self_insured.questions.does_plan_provide_enough_leave",
    questionLabel:
      "pages.employersExemptionsSelfInsuredEmployeeCoverageDetails.doesPlanProvideEnoughLeaveQuestion",
  },
  {
    fieldName: "self_insured.questions.does_plan_cover_former_employees",
    questionLabel:
      "pages.employersExemptionsSelfInsuredEmployeeCoverageDetails.doesPlanCoverFormerEmployeesQuestion",
  },
];

/* 
prefix each field 'exemptionRequest.' this is needed for the employer 
exemptions progress page to display correctly
*/
export const fields = [
  "exemptionRequest.self_insured.questions.does_plan_cover_all_employees",
  "exemptionRequest.self_insured.questions.does_plan_provide_enough_leave",
  "exemptionRequest.self_insured.questions.does_plan_cover_former_employees",
];

const translationPrefix =
  "pages.employersExemptionsSelfInsuredEmployeeCoverageDetails";

export const EmployerExemptionsSelfInsuredEmployeeCoverageDetails = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { appLogic, exemptionRequest, user } = props;
  const { t } = useTranslation();
  const { user_leave_administrators } = user;

  const { formState, updateFields } = useFormState({
    self_insured: {
      questions: {
        does_plan_cover_all_employees:
          exemptionRequest.self_insured?.questions
            ?.does_plan_cover_all_employees,
        does_plan_provide_enough_leave:
          exemptionRequest.self_insured?.questions
            ?.does_plan_provide_enough_leave,
        does_plan_cover_former_employees:
          exemptionRequest.self_insured?.questions
            ?.does_plan_cover_former_employees,
      },
    },
  });

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const handleSave = async () => {
    await appLogic.employerExemptionsApplication.update(
      exemptionRequest.employer_exemption_application_id,
      formState
    );
  };

  const getFunctionalInputProps = useFunctionalInputProps({
    errors: appLogic.errors,
    formState,
    updateFields,
  });

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  const doesPlanCoverAllEmployeesQuestion: SelfInsuredQuestion = {
    label: `${translationPrefix}.doesPlanCoverAllEmployeesQuestion`,
    inputProps: getFunctionalInputProps(
      "self_insured.questions.does_plan_cover_all_employees"
    ),
  };

  const doesPlanProvideEnoughLeaveQuestion: SelfInsuredQuestion = {
    label: `${translationPrefix}.doesPlanProvideEnoughLeaveQuestion`,
    inputProps: getFunctionalInputProps(
      "self_insured.questions.does_plan_provide_enough_leave"
    ),
  };

  const doesPlanCoverFormerEmployeesQuestion: SelfInsuredQuestion = {
    label: `${translationPrefix}.doesPlanCoverFormerEmployeesQuestion`,
    inputProps: getFunctionalInputProps(
      "self_insured.questions.does_plan_cover_former_employees"
    ),
  };

  const questions: SelfInsuredQuestion[] = [
    doesPlanCoverAllEmployeesQuestion,
    doesPlanProvideEnoughLeaveQuestion,
    doesPlanCoverFormerEmployeesQuestion,
  ];

  return (
    <React.Fragment>
      <QuestionPage
        title={t(`${translationPrefix}.title`)}
        headingPrefix={
          <Trans
            i18nKey="shared.employerExemptions.employerEIN"
            values={{
              employer_fein: user_leave_administrators[0].employer_fein,
            }}
          />
        }
        onSave={handleSave}
      >
        <FormLabel component="legend">
          {t(`${translationPrefix}.sectionLabel`)}
        </FormLabel>
        <SelfInsuredQuestionList
          labelTranslationPrefix={translationPrefix}
          questions={questions}
        />
      </QuestionPage>
    </React.Fragment>
  );
};

export default withEmployerExemptionsApplication(
  EmployerExemptionsSelfInsuredEmployeeCoverageDetails
);
