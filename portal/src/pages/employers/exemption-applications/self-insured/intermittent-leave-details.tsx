import SelfInsuredQuestionList, {
  SelfInsuredQuestion,
} from "./SelfInsuredPageQuestions";
import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import FormLabel from "src/components/core/FormLabel";
import PageNotFound from "src/components/PageNotFound";
import QuestionPage from "src/components/QuestionPage";
import React from "react";
import { Trans } from "react-i18next";
import { isFeatureEnabled } from "src/services/featureFlags";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";
import { useTranslation } from "src/locales/i18n";

const translationPrefix =
  "pages.employersExemptionsSelfInsuredIntermittentLeaveDetails";

// page questions for use on the review page
export const pageQuestions = [
  {
    fieldName:
      "self_insured.questions.does_plan_provide_intermittent_caring_leave",
    questionLabel: `${translationPrefix}.doesPlanProvideIntermittentCaringLeaveQuestion`,
  },
  {
    fieldName:
      "self_insured.questions.does_plan_provide_intermittent_bonding_leave",
    questionLabel: `${translationPrefix}.doesPlanProvideIntermittentBondingLeaveQuestion`,
  },
  {
    fieldName:
      "self_insured.questions.does_plan_provide_intermittent_armed_forces_leave",
    questionLabel: `${translationPrefix}.doesPlanProvideIntermittentArmedForcesLeaveQuestion`,
  },
  {
    fieldName:
      "self_insured.questions.does_plan_provide_intermittent_medical_leave",
    questionLabel: `${translationPrefix}.doesPlanProvideIntermittentMedicalLeaveQuestion`,
  },
];

/* 
  prefix each field 'exemptionRequest.' this is needed for the employer 
  exemptions progress page to display correctly
*/
export const fields = [
  "exemptionRequest.self_insured.questions.does_plan_provide_intermittent_caring_leave",
  "exemptionRequest.self_insured.questions.does_plan_provide_intermittent_bonding_leave",
  "exemptionRequest.self_insured.questions.does_plan_provide_intermittent_armed_forces_leave",
  "exemptionRequest.self_insured.questions.does_plan_provide_intermittent_medical_leave",
];

export const EmployerExemptionsSelfInsuredIntermittentLeaveDetails = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { appLogic, exemptionRequest, user } = props;
  const { t } = useTranslation();
  const { user_leave_administrators } = user;

  const { formState, updateFields } = useFormState({
    self_insured: {
      questions: {
        does_plan_provide_intermittent_caring_leave:
          exemptionRequest.self_insured?.questions
            ?.does_plan_provide_intermittent_caring_leave,
        does_plan_provide_intermittent_bonding_leave:
          exemptionRequest.self_insured?.questions
            ?.does_plan_provide_intermittent_bonding_leave,
        does_plan_provide_intermittent_armed_forces_leave:
          exemptionRequest.self_insured?.questions
            ?.does_plan_provide_intermittent_armed_forces_leave,
        does_plan_provide_intermittent_medical_leave:
          exemptionRequest.self_insured?.questions
            ?.does_plan_provide_intermittent_medical_leave,
      },
    },
  });

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const handleSave = async () => {
    await appLogic.employerExemptionsApplication.update(
      exemptionRequest.employer_exemption_application_id,
      formState
    );
  };

  const getFunctionalInputProps = useFunctionalInputProps({
    errors: appLogic.errors,
    formState,
    updateFields,
  });

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  const caringLeaveHint = (
    <Trans
      i18nKey={`${translationPrefix}.doesPlanProvideIntermittentCaringLeaveQuestionHint`}
      components={{
        ul: <ul className="usa-list" />,
        li: <li />,
      }}
    />
  );

  // Questions 1-3 are only shown when applying for a family exemption; Question 4 is shown for both family and medical exemptions
  const doesPlanProvideIntermittentCaringLeaveQuestion: SelfInsuredQuestion = {
    label: `${translationPrefix}.doesPlanProvideIntermittentCaringLeaveQuestion`,
    isVisible: exemptionRequest.has_family_exemption === true,
    inputProps: getFunctionalInputProps(
      "self_insured.questions.does_plan_provide_intermittent_caring_leave"
    ),
    hint: caringLeaveHint,
  };

  const doesPlanProvideIntermittentBondingLeaveQuestion: SelfInsuredQuestion = {
    label: `${translationPrefix}.doesPlanProvideIntermittentBondingLeaveQuestion`,
    isVisible: exemptionRequest.has_family_exemption === true,
    inputProps: getFunctionalInputProps(
      "self_insured.questions.does_plan_provide_intermittent_bonding_leave"
    ),
  };

  const doesPlanProvideIntermittentArmedForcesLeaveQuestion: SelfInsuredQuestion =
    {
      label: `${translationPrefix}.doesPlanProvideIntermittentArmedForcesLeaveQuestion`,
      isVisible: exemptionRequest.has_family_exemption === true,
      inputProps: getFunctionalInputProps(
        "self_insured.questions.does_plan_provide_intermittent_armed_forces_leave"
      ),
    };

  const doesPlanProvideIntermittentMedicalLeaveQuestion: SelfInsuredQuestion = {
    label: `${translationPrefix}.doesPlanProvideIntermittentMedicalLeaveQuestion`,
    inputProps: getFunctionalInputProps(
      "self_insured.questions.does_plan_provide_intermittent_medical_leave"
    ),
  };

  const questions: SelfInsuredQuestion[] = [
    doesPlanProvideIntermittentCaringLeaveQuestion,
    doesPlanProvideIntermittentBondingLeaveQuestion,
    doesPlanProvideIntermittentArmedForcesLeaveQuestion,
    doesPlanProvideIntermittentMedicalLeaveQuestion,
  ];

  return (
    <React.Fragment>
      <QuestionPage
        title={t(
          "pages.employersExemptionsSelfInsuredIntermittentLeaveDetails.title"
        )}
        headingPrefix={
          <Trans
            i18nKey="shared.employerExemptions.employerEIN"
            values={{
              employer_fein: user_leave_administrators[0].employer_fein,
            }}
          />
        }
        onSave={handleSave}
      >
        <FormLabel component="legend">
          {t(`${translationPrefix}.sectionLabel`)}
        </FormLabel>
        <SelfInsuredQuestionList
          labelTranslationPrefix={translationPrefix}
          questions={questions}
        />
      </QuestionPage>
    </React.Fragment>
  );
};

export default withEmployerExemptionsApplication(
  EmployerExemptionsSelfInsuredIntermittentLeaveDetails
);
