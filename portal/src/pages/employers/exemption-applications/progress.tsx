import StepModel, {
  ExemptionRequestSteps,
} from "src/models/EmployerExemptionsStep";
import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import BackButton from "src/components/BackButton";
import Button from "src/components/core/Button";
import ButtonLink from "src/components/ButtonLink";
import ExternalLink from "src/components/core/ExternalLink";
import HeadingPrefix from "src/components/core/HeadingPrefix";
import PageNotFound from "src/components/PageNotFound";
import React from "react";
import Step from "src/components/Step";
import StepList from "src/components/StepList";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import exemptionsConfig from "src/flows/employer";
import { isFeatureEnabled } from "src/services/featureFlags";
import routes from "src/routes";
import useThrottledHandler from "src/hooks/useThrottledHandler";
import { useTranslation } from "src/locales/i18n";

export const EmployerExemptionsProgress = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { appLogic, exemptionRequest, user } = props;
  const { t } = useTranslation();
  const { user_leave_administrators: user_exemption_administrator } = user;

  const translationPrefix = "pages.employersExemptionsProgress";

  const params = {
    employer_exemption_application_id:
      exemptionRequest.employer_exemption_application_id,
  };

  const warnings =
    appLogic.employerExemptionsApplication.warningsLists[
      exemptionRequest.employer_exemption_application_id
    ];

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );
  /**
   * @type {StepModel[]}
   */
  const allSteps = StepModel.createExemptionFromMachine(
    exemptionsConfig,
    {
      exemptionRequest,
    },
    warnings
  );

  /**
   * @type {boolean} Flag for determining whether to enable the submit button
   */
  const readyToSubmit = allSteps.every(
    (step) => step.isComplete || step.isNotApplicable
  );

  const sharedStepListProps = {
    startText: t(`${translationPrefix}.start`),
    resumeText: t(`${translationPrefix}.resume`),
    resumeScreenReaderText: t(`${translationPrefix}.resumeScreenReader`),
    editText: t(`${translationPrefix}.edit`),
    screenReaderNumberPrefix: t(
      `${translationPrefix}.screenReaderNumberPrefix`
    ),
  };

  const handleDelete = useThrottledHandler(async (event) => {
    event.preventDefault();
    await appLogic.employerExemptionsApplication.deleteApp(
      exemptionRequest.employer_exemption_application_id
    );
  });
  /**
   * Helper method for generating a context string used to differentiate i18n keys
   * for the various Step content strings.
   */
  function getStepDescription(stepName: string) {
    if (stepName === ExemptionRequestSteps.contactDetails) {
      return "contactDetails";
    }
    if (stepName === ExemptionRequestSteps.organization) {
      return "organization";
    }
    if (stepName === ExemptionRequestSteps.insuranceDetails) {
      return "insurancePlanInfo";
    }
    if (stepName === ExemptionRequestSteps.uploadDocuments) {
      return "uploadDocuments";
    }
  }

  /**
   * Get the number of a Step for display in the checklist.
   */
  function getStepNumber(step: StepModel) {
    const index = allSteps.findIndex((s) => s.name === step.name);
    return index + 1;
  }

  /**
   * Helper method for rendering steps for one of the StepLists
   */
  function renderSteps(steps: StepModel[]) {
    return steps.map((step) => {
      const description = getStepDescription(step.name);

      const stepHref = appLogic.portalFlow.getRouteFor(step.name, {}, params);
      return (
        <Step
          completedText={t(`${translationPrefix}.completed`, {
            context: step.editable ? "editable" : "uneditable",
          })}
          key={step.name}
          number={getStepNumber(step)}
          title={t(`${translationPrefix}.stepTitle`, {
            context: description,
          })}
          status={step.status}
          stepHref={stepHref}
          editable={!!step.editable}
          submittedContent={null}
          completedContent={null}
        >
          <Trans
            i18nKey={`${translationPrefix}.stepDescription`}
            context={description}
          />
        </Step>
      );
    });
  }

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  return (
    <React.Fragment>
      <BackButton
        label={t(`${translationPrefix}.backButtonLabel`)}
        href={routes.employers.welcome}
      />
      <HeadingPrefix>
        <Trans
          i18nKey="shared.employerExemptions.employerEIN"
          values={{
            employer_fein: user_exemption_administrator[0].employer_fein,
          }}
        />
      </HeadingPrefix>
      <Title>{t(`${translationPrefix}.title`)}</Title>
      <React.Fragment>
        <Trans
          i18nKey={`${translationPrefix}.stepListDescription`}
          components={{
            "purchased-private-plan-requirements-link": (
              <ExternalLink
                href={routes.external.massgov.purchasedPrivatePlanRequirements}
              />
            ),
            "self-insured-private-plan-requirements-link": (
              <ExternalLink
                href={
                  routes.external.massgov.selfInsuredPrivatePlanRequirements
                }
              />
            ),
          }}
        />
      </React.Fragment>
      <StepList title="" {...sharedStepListProps}>
        {renderSteps(allSteps)}
      </StepList>

      <ButtonLink
        className="margin-top-3 margin-bottom-3"
        href={appLogic.portalFlow.getRouteFor("REVIEW", {}, params)}
        disabled={!readyToSubmit}
      >
        {t(`${translationPrefix}.reviewAndSubmitButton`)}
      </ButtonLink>
      <Button
        className="margin-top-3 margin-bottom-3"
        variation="outline"
        onClick={handleDelete}
      >
        {t(`${translationPrefix}.deleteButton`)}
      </Button>
    </React.Fragment>
  );
};

export default withEmployerExemptionsApplication(EmployerExemptionsProgress);
