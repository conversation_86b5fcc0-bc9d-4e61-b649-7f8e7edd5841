import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import ExternalLink from "src/components/core/ExternalLink";
import Fieldset from "src/components/core/Fieldset";
import FormLabel from "src/components/core/FormLabel";
import InputChoiceGroup from "src/components/core/InputChoiceGroup";
import InputNumber from "src/components/core/InputNumber";
import PageNotFound from "src/components/PageNotFound";
import QuestionPage from "src/components/QuestionPage";
import React from "react";
import { Trans } from "react-i18next";
import { isFeatureEnabled } from "src/services/featureFlags";
import routes from "src/routes";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";
import { useTranslation } from "src/locales/i18n";

const translationPrefix = "pages.employersExemptionsOrganizationDetails";

// page questions for use on the review page
export const pageQuestions = [
  {
    fieldName: "should_workforce_count_include_1099_misc",
    questionLabel: `${translationPrefix}.halfOfWorkforcePaidThrough1099Question`,
  },
  {
    fieldName: "average_workforce_count",
    questionLabel: `${translationPrefix}.averageWorkforceSizeLabel`,
  },
];

export const fields = pageQuestions.map((question) => {
  return "exemptionRequest." + question.fieldName;
});

export const EmployerExemptionsOrganizationDetails = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { appLogic, user, exemptionRequest } = props;
  const { t } = useTranslation();
  const { user_leave_administrators: user_exemption_administrator } = user;
  const { formState, updateFields } = useFormState({
    average_workforce_count: exemptionRequest.average_workforce_count,
    should_workforce_count_include_1099_misc:
      exemptionRequest.should_workforce_count_include_1099_misc,
  });

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const handleSave = () =>
    appLogic.employerExemptionsApplication.update(
      exemptionRequest.employer_exemption_application_id,
      formState
    );

  const getFunctionalInputProps = useFunctionalInputProps({
    errors: appLogic.errors,
    formState,
    updateFields,
  });

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  return (
    <React.Fragment>
      <QuestionPage
        title={t(`${translationPrefix}.title`)}
        headingPrefix={
          <Trans
            i18nKey="shared.employerExemptions.employerEIN"
            values={{
              employer_fein: user_exemption_administrator[0].employer_fein,
            }}
          />
        }
        onSave={handleSave}
      >
        <Fieldset>
          <FormLabel
            component="legend"
            hint={
              <Trans
                i18nKey={`${translationPrefix}.sectionLabelHint`}
                components={{}}
              />
            }
          >
            {t(`${translationPrefix}.sectionLabel`)}
          </FormLabel>
          <InputChoiceGroup
            smallLabel
            {...getFunctionalInputProps(
              "should_workforce_count_include_1099_misc"
            )}
            choices={[
              {
                checked:
                  formState.should_workforce_count_include_1099_misc === true,
                label: t(`${translationPrefix}.choiceYes`),
                value: "true",
              },
              {
                checked:
                  formState.should_workforce_count_include_1099_misc === false,
                label: t(`${translationPrefix}.choiceNo`),
                value: "false",
              },
            ]}
            type="radio"
            label={t(
              `${translationPrefix}.halfOfWorkforcePaidThrough1099Question`
            )}
          />

          <InputNumber
            {...getFunctionalInputProps("average_workforce_count")}
            autoComplete="off"
            label={t(`${translationPrefix}.averageWorkforceSizeLabel`)}
            hint={
              <React.Fragment>
                <Trans
                  i18nKey={`${translationPrefix}.averageWorkforceSizeHint`}
                  components={{
                    "counting-total-workforce-link": (
                      <ExternalLink
                        href={routes.external.massgov.countingTotalWorkforce}
                      />
                    ),
                  }}
                />
              </React.Fragment>
            }
            smallLabel
            onChange={(e) => {
              updateFields({
                average_workforce_count: parseInt(e.target.value, 10) || null,
              });
            }}
          />
        </Fieldset>
      </QuestionPage>
    </React.Fragment>
  );
};

export default withEmployerExemptionsApplication(
  EmployerExemptionsOrganizationDetails
);
