import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import { DocumentsUploadError } from "src/errors";
import { ExemptionsDocumentUploadPrompt } from "src/components/employer-exemptions/ExemptionsDocumentUploadPrompt";
import ExternalLink from "src/components/core/ExternalLink";
import Heading from "src/components/core/Heading";
import Lead from "src/components/core/Lead";
import PageNotFound from "src/components/PageNotFound";
import QuestionPage from "src/components/QuestionPage";
import React from "react";
import { Trans } from "react-i18next";
import { createEmployerEINBanner } from "src/components/employer-exemptions/EmployerEINBanner";
import { isFeatureEnabled } from "src/services/featureFlags";
import routes from "src/routes";
import useFilesLogic from "src/hooks/useFilesLogic";
import { useTranslation } from "src/locales/i18n";

const translationPrefix =
  "pages.employersExemptionsSelfInsuredSuretyBondDocumentUpload";

export const EmployerExemptionsSelfInsuredSuretyBondDocumentUpload = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { appLogic, exemptionRequest } = props;
  const { t } = useTranslation();

  const { files, processFiles, removeFile } = useFilesLogic({
    clearErrors: appLogic.clearErrors,
    catchError: appLogic.catchError,
  });

  // TODO (PFMLPB-19806): removal of feature flag
  // TODO (PFMLPB-24037): Update the Document Download's storybook's index page - Move FF check into index page
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const handleSave = () =>
    Promise.resolve(
      // Allow user to skip this page
      appLogic.portalFlow.goToNextPage(
        { exemptionRequest },
        {
          employer_exemption_application_id:
            exemptionRequest.employer_exemption_application_id,
        }
      )
    );

  const handleFiles = async (files: File[]) => {
    await processFiles(files);
  };

  const fileErrors = appLogic.errors.filter(
    (error) => error instanceof DocumentsUploadError
  ) as DocumentsUploadError[];

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  return (
    <React.Fragment>
      <QuestionPage
        title={t(`${translationPrefix}.title`)}
        headingPrefix={createEmployerEINBanner(props)}
        onSave={handleSave}
      >
        <Heading level="2" size="1">
          {t(`${translationPrefix}.sectionLabel`)}
        </Heading>
        <Lead>
          <Trans
            i18nKey={`${translationPrefix}.sectionLabelHint`}
            components={{
              "self-insured-paid-leave-plans-requirements-link": (
                <ExternalLink
                  href={
                    routes.external.massgov.selfInsuredPrivatePlanRequirements
                  }
                />
              ),
            }}
          />
        </Lead>
        <ExemptionsDocumentUploadPrompt
          files={files}
          fileErrors={fileErrors}
          handleFiles={handleFiles}
          removeFile={removeFile}
        />
      </QuestionPage>
    </React.Fragment>
  );
};

export default withEmployerExemptionsApplication(
  EmployerExemptionsSelfInsuredSuretyBondDocumentUpload
);
