import withEmployerExemptionsApplication, {
  WithEmployerExemptionsApplicationProps,
} from "src/hoc/withEmployerExemptionsApplication";

import ConditionalContent from "src/components/ConditionalContent";
import EmployerExemptionsConfirmationOfInsuranceDocumentUpload from "./confirmation-of-insurance";
import EmployerExemptionsSelfInsuredDeclarationDocumentUpload from "./self-insured-declaration";
import PageNotFound from "src/components/PageNotFound";
import React from "react";
import { isFeatureEnabled } from "src/services/featureFlags";

export const EmployerExemptionsDocumentUpload = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { exemptionRequest } = props;

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  return (
    <div className="measure-6">
      <ConditionalContent
        visible={exemptionRequest.is_self_insured_plan === false}
      >
        <EmployerExemptionsConfirmationOfInsuranceDocumentUpload {...props} />
      </ConditionalContent>
      <ConditionalContent
        visible={exemptionRequest.is_self_insured_plan === true}
      >
        <EmployerExemptionsSelfInsuredDeclarationDocumentUpload {...props} />
      </ConditionalContent>
    </div>
  );
};

export default withEmployerExemptionsApplication(
  EmployerExemptionsDocumentUpload
);
