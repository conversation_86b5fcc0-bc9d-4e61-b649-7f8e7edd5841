import withUser, { WithUserProps } from "src/hoc/withUser";

import ButtonLink from "src/components/ButtonLink";
import PageNotFound from "src/components/PageNotFound";
import React from "react";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import { isFeatureEnabled } from "src/services/featureFlags";

export const Success = (
  props: WithUserProps & { query: { employer_id?: string; next?: string } }
) => {
  const { appLogic, query, user } = props;

  const employer = user.user_leave_administrators.find((employer) => {
    return employer.employer_id === query.employer_id;
  });

  if (!employer) {
    return <PageNotFound />;
  }

  return (
    <React.Fragment>
      <Title>
        {isFeatureEnabled("enableEmployerExemptionsPortal") ? (
          <Trans i18nKey="pages.employersOrganizationsSuccess.title_v2" />
        ) : (
          <Trans i18nKey="pages.employersOrganizationsSuccess.title_v1" />
        )}
      </Title>
      <p>
        <Trans
          i18nKey="pages.employersOrganizationsSuccess.companyNameLabel"
          values={{ company: employer.employer_dba }}
        />
        <br />
        <Trans
          i18nKey="pages.employersOrganizationsSuccess.employerIdNumberLabel"
          values={{ ein: employer.employer_fein }}
        />
      </p>
      <ButtonLink href={appLogic.portalFlow.getRouteFor("APPLICATIONS")}>
        {isFeatureEnabled("enableEmployerExemptionsPortal") ? (
          <Trans i18nKey="pages.employersOrganizationsSuccess.continueButton_v2" />
        ) : (
          <Trans i18nKey="pages.employersOrganizationsSuccess.continueButton_v1" />
        )}
      </ButtonLink>
    </React.Fragment>
  );
};

export default withUser(Success);
