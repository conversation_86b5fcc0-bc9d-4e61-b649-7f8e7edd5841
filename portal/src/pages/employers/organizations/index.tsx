import withLeaveAdmins, { WithLeaveAdminsProps } from "src/hoc/withLeaveAdmins";

import Alert from "src/components/core/Alert";
import ButtonLink from "src/components/ButtonLink";
import EmployerNavigationTabs from "src/components/EmployerNavigationTabs";
import NoOrganizationsAlert from "src/components/NoOrganizationsAlert";
import OrganizationTableRow from "src/features/leave-admins/OrganizationTableRow";
import React from "react";
import Table from "src/components/core/Table";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import UnverifiedOrganizationAlert from "src/components/UnverifiedOrganizationAlert";
import { isFeatureEnabled } from "src/services/featureFlags";
import routes from "src/routes";
import { useChatWidget } from "src/hooks/useChatWidget";
import { useTranslation } from "src/locales/i18n";

export const Index = (
  props: WithLeaveAdminsProps & {
    query: { account_converted?: string; org_name?: string; remove?: string };
  }
) => {
  useChatWidget(props.user, props.appLogic.chatWidget);
  const { appLogic, query, leaveAdmins } = props;
  const { t } = useTranslation();
  const { hasVerifiableEmployer, hasAssociatedOrgs } = props.user;
  const accountConverted = query?.account_converted === "true";

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  return (
    <React.Fragment>
      <EmployerNavigationTabs
        activePath={props.appLogic.portalFlow.pathname}
        hasEmployerExemptionRequests={props.appLogic.employerExemptionsApplication.hasEmployerExemptionRequests()}
      />
      <Title>{t("pages.employersOrganizations.title")}</Title>
      {accountConverted && (
        <Alert
          heading={t("pages.employersOrganizations.convertHeading")}
          state="success"
        >
          {t("pages.employersOrganizations.convertDescription")}
        </Alert>
      )}
      {!hasAssociatedOrgs && <NoOrganizationsAlert />}
      {query.remove && (
        <Alert
          state="success"
          heading={t("pages.employersRemoveLeaveAdmin.successHeading")}
          role="alert"
          className="margin-bottom-5"
        >
          <div title="employers remove leave admin success">
            <Trans
              i18nKey="pages.employersRemoveLeaveAdmin.success"
              values={{ email: query.remove, company: query.org_name }}
            />
          </div>
        </Alert>
      )}
      {hasVerifiableEmployer && (
        <UnverifiedOrganizationAlert pageContext="organizations" />
      )}
      <p>
        <Trans
          i18nKey={
            !isEnableEmployerExemptionsPortal
              ? "pages.employersOrganizations.lead_v2"
              : "pages.employersOrganizations.lead_v3"
          }
          components={{
            "applications-link": <a href={routes.employers.applications} />,
          }}
        />
      </p>

      <Table responsive className="width-full">
        <thead>
          <tr>
            <th scope="col">
              {t("pages.employersOrganizations.organizationsTableHeader")}
            </th>
            <th scope="col">
              {t("pages.employersOrganizations.einTableHeader")}
            </th>
          </tr>
        </thead>
        <tbody>
          {leaveAdmins.length > 0 &&
            leaveAdmins.map((leaveAdmin) => (
              <OrganizationTableRow
                key={leaveAdmin.user_leave_administrator_id}
                leaveAdmin={leaveAdmin}
                href={appLogic.portalFlow.getRouteFor(
                  "SHOW_ORG",
                  { leaveAdmin },
                  { employer_id: leaveAdmin.employer_id }
                )}
              />
            ))}
          {leaveAdmins.length === 0 && (
            <tr>
              <td>{t("shared.noneReported")}</td>
              <td />
            </tr>
          )}
        </tbody>
      </Table>
      <ButtonLink href={appLogic.portalFlow.getRouteFor("ADD_ORG")}>
        {t("pages.employersOrganizations.addOrganizationButton")}
      </ButtonLink>
    </React.Fragment>
  );
};

export default withLeaveAdmins(Index);
