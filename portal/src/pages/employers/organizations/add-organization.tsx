import withUser, { WithUserProps } from "src/hoc/withUser";

import { AppLogic } from "src/hooks/useAppLogic";
import BackButton from "src/components/BackButton";
import Button from "src/components/core/Button";
import InputText from "src/components/core/InputText";
import React from "react";
import Title from "src/components/core/Title";
import { isFeatureEnabled } from "src/services/featureFlags";
import { useChatWidget } from "src/hooks/useChatWidget";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";
import useThrottledHandler from "src/hooks/useThrottledHandler";
import { useTranslation } from "src/locales/i18n";

interface AddOrganizationProps extends WithUserProps {
  appLogic: AppLogic;
}

export const AddOrganization = (props: AddOrganizationProps) => {
  const { appLogic, user } = props;
  const { t } = useTranslation();
  useChatWidget(user, appLogic.chatWidget);

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  const { formState, updateFields } = useFormState({
    ein: "",
  });

  const handleSubmit = useThrottledHandler(async (event) => {
    event.preventDefault();

    const payload = {
      employer_fein: formState.ein,
    };

    await appLogic.employers.addEmployer(payload);
  });

  const getFunctionalInputProps = useFunctionalInputProps({
    errors: appLogic.errors,
    formState,
    updateFields,
  });

  return (
    <form className="usa-form" onSubmit={handleSubmit} method="post">
      <BackButton />
      <Title>{t("pages.employersOrganizationsAddOrganization.title")}</Title>
      <p>
        {isEnableEmployerExemptionsPortal
          ? t("pages.employersOrganizationsAddOrganization.instructions_v3")
          : t("pages.employersOrganizationsAddOrganization.instructions_v2")}
      </p>

      <InputText
        {...getFunctionalInputProps("ein")}
        label={t(
          "pages.employersOrganizationsAddOrganization.employerIdNumberLabel"
        )}
        mask="fein"
        smallLabel
      />
      <Button
        type="submit"
        className="margin-top-4"
        loading={handleSubmit.isThrottled}
      >
        {t("pages.employersOrganizationsAddOrganization.continueButton")}
      </Button>
    </form>
  );
};

export default withUser(AddOrganization);
