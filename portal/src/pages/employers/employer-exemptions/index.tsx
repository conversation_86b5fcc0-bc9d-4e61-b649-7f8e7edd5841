import withUser, { WithUserProps } from "src/hoc/withUser";

import EmployerNavigationTabs from "src/components/EmployerNavigationTabs";
import NoOrganizationsAlert from "src/components/NoOrganizationsAlert";
import PageNotFound from "src/components/PageNotFound";
import React from "react";
import Title from "src/components/core/Title";
import UnverifiedOrganizationAlert from "src/components/UnverifiedOrganizationAlert";
import { isFeatureEnabled } from "src/services/featureFlags";
import { useTranslation } from "src/locales/i18n";

export const ExemptionsTab = (props: WithUserProps) => {
  const { t } = useTranslation();

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  if (!isEnableEmployerExemptionsPortal) {
    return <PageNotFound />;
  }

  return (
    <React.Fragment>
      <EmployerNavigationTabs
        activePath={props.appLogic.portalFlow.pathname}
        hasEmployerExemptionRequests={props.appLogic.employerExemptionsApplication.hasEmployerExemptionRequests()}
      />
      <Title>{t("pages.employersExemptionsTab.title")}</Title>

      <div className="measure-6">
        {!props.user.hasAssociatedOrgs && <NoOrganizationsAlert />}
        {props.user.hasVerifiableEmployer && <UnverifiedOrganizationAlert />}
      </div>

      {/* 
        content to be added can be found here
        https://lwd.atlassian.net/wiki/spaces/DD/pages/4355948545/Employer+Exemptions+Exemptions+Tab+v1.2+Content+Design+Review
      */}
    </React.Fragment>
  );
};

export default withUser(ExemptionsTab);
