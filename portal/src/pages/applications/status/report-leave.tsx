import React, { useState } from "react";
import withClaimDetail, { WithClaimDetailProps } from "src/hoc/withClaimDetail";

import Alert from "src/components/core/Alert";
import { ApplicationID } from "src/features/status/ApplicationAndEmployerIdBox";
import ApprovedIntermittentLeaveSchedules from "src/components/ApprovedIntermittentLeaveSchedules";
import Heading from "src/components/core/Heading";
import HeadingPrefix from "src/components/core/HeadingPrefix";
import { LeaveHoursAndStatus } from "src/features/status/LeaveHoursAndStatus";
import PageNotFound from "src/components/PageNotFound";
import ReportIntermittentLeaveEpisode from "src/components/ReportIntermittentLeaveEpisode";
import StatusNavigationAndAlertsWrapper from "src/features/status/StatusNavigationAndAlertsWrapper";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import { t } from "i18next";

export interface IntermittentLeaveProps extends WithClaimDetailProps {
  query: {
    absence_id: string;
  };
}

export const ReportIntermittentLeave = (props: IntermittentLeaveProps) => {
  const { appLogic, claim_detail, query } = props;
  const { absence_id } = query;

  const [alertHeading, setAlertHeading] = useState("");
  const [alertText, setAlertText] = useState("");
  const [alertState, setAlertState] = useState<
    "error" | "info" | "success" | "warning"
  >("success");
  const [showAlert, setShowAlert] = useState(false);

  const handleAlert = (
    heading: string,
    text: string,
    state: "error" | "info" | "success" | "warning",
    show: boolean
  ) => {
    setAlertHeading(heading);
    setAlertText(text);
    setAlertState(state);
    setShowAlert(show);
  };

  if (!claim_detail.isIntermittent || !claim_detail.hasApprovedStatus) {
    return <PageNotFound />;
  }

  return (
    <div>
      {showAlert && (
        <Alert
          heading={alertHeading}
          headingSize="2"
          role="alert"
          state={alertState}
        >
          {alertText}
        </Alert>
      )}
      <StatusNavigationAndAlertsWrapper {...props}>
        <Title hidden>
          {t("pages.reportIntermittentLeave.reportIntermittentLeaveTitle")}
        </Title>
        <HeadingPrefix>
          <div className="display-flex grid-row flex-justify margin-top-5 margin-bottom-3">
            <ApplicationID fineos_absence_id={absence_id} />
          </div>
        </HeadingPrefix>
        <Heading level="2" size="1" className="margin-bottom-3">
          {t("pages.reportIntermittentLeave.reportIntermittentLeaveHeading")}
        </Heading>
        <Heading level="2" size="3">
          {t("pages.reportIntermittentLeave.reportLeaveHoursApprovedSchedule")}
        </Heading>
        <ApprovedIntermittentLeaveSchedules
          absence_periods={claim_detail.absence_periods}
        />
        <ReportIntermittentLeaveEpisode
          appLogic={appLogic}
          onSubmitResponse={handleAlert}
          absenceId={absence_id}
        />
        <LeaveHoursAndStatus
          absenceId={absence_id}
          intermittentLeaveEpisodes={claim_detail.intermittent_leave_episodes}
        />
        <br />
        <div>
          <Heading level="3" size="4">
            {t(
              "pages.reportIntermittentLeave.changeIntermittentLeaveScheduleFooterHeading"
            )}
          </Heading>
          <p className="margin-top-1">
            <Trans
              i18nKey="pages.reportIntermittentLeave.changeIntermittentLeaveScheduleFooterBody"
              components={{
                "contact-center-phone-link": (
                  <a href={`tel:${t("shared.contactCenterPhoneNumber")}`} />
                ),
              }}
            />
          </p>
        </div>
      </StatusNavigationAndAlertsWrapper>
    </div>
  );
};

export default withClaimDetail(ReportIntermittentLeave);
