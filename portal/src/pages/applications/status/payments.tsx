import Payment, { PAYMENT_METHOD } from "src/models/Payment";
import withClaimDetail, { WithClaimDetailProps } from "src/hoc/withClaimDetail";
import withPayment, { WithPaymentProps } from "src/hoc/withPayment";
import withWaitingPeriods, {
  WithWaitingPeriodsProps,
} from "src/hoc/withWaitingPeriods";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import ClaimDetail from "src/models/ClaimDetail";
import Details from "src/components/core/Details";
import EmployerClaimReview from "src/models/EmployerClaimReview";
import GetHelpSection from "src/features/status/GetHelpSection";
import Heading from "src/components/core/Heading";
import PaymentFAQSection from "src/features/status/PaymentFAQSection";
import PaymentsIntro from "src/features/status/PaymentsIntro";
import { PaymentsTable } from "src/features/status/PaymentsTable";
import React from "react";
import StatusNavigationAndAlertsWrapper from "src/features/status/StatusNavigationAndAlertsWrapper";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import { calculateCheckbackDate } from "src/utils/calculateCheckbackDate";
import { createRouteWithQuery } from "src/utils/routeWithParams";
import routes from "src/routes";
import { useTranslation } from "src/locales/i18n";

export interface PaymentProps
  extends WithClaimDetailProps,
    WithPaymentProps,
    WithWaitingPeriodsProps {
  payments: ApiResourceCollection<Payment>;
  query: {
    absence_id: string;
  };
}

export const PaymentsPage = (props: PaymentProps) => {
  const { claim_detail, payments, appLogic, query, user, waitingPeriods } =
    props;
  const { t } = useTranslation();
  const { absence_id } = query;
  const { portalFlow } = appLogic;

  const showFINEOSWaitingPeriod = claim_detail.does_claim_span_benefit_years;

  if (!showPaymentsTab(claim_detail, payments)) {
    portalFlow.goTo(routes.applications.status.claim, {
      absence_id,
    });
  }

  const shouldShowPaymentsTable =
    claim_detail.hasPayments(payments) || claim_detail.hasWaitingWeek;

  const showPrepaidCardContent =
    claim_detail.payment_preference?.payment_method ===
    PAYMENT_METHOD.PREPAID_CARD;

  return (
    <StatusNavigationAndAlertsWrapper {...props}>
      <Title hidden>{t("pages.payments.paymentsTitle")}</Title>
      <section className="margin-y-5" data-testid="your-payments">
        {/* Heading section */}
        <Heading level="2" size="1" className="margin-bottom-3">
          {t("pages.payments.yourPayments")}
        </Heading>
        <PaymentsIntro
          claim_detail={claim_detail}
          payments={payments}
          query={query}
          appLogic={appLogic}
          user={user}
        />
        <section className="margin-y-5" data-testid="how-to-get-full-salary">
          <Details label={t("pages.payments.paymentHowToGetFullSalary")}>
            <ul>
              <li>{t("pages.payments.paymentHowToGetFullSalary_lineItem1")}</li>
              <li>{t("pages.payments.paymentHowToGetFullSalary_lineItem2")}</li>
              <li>{t("pages.payments.paymentHowToGetFullSalary_lineItem3")}</li>
              <li>{t("pages.payments.paymentHowToGetFullSalary_lineItem4")}</li>
              <li>
                <Trans
                  i18nKey="pages.payments.paymentHowToGetFullSalary_lineItem5"
                  components={{
                    "approval-and-change-notice-link": (
                      <a
                        href={createRouteWithQuery(
                          routes.applications.status.claim,
                          { absence_id },
                          "view_notices"
                        )}
                        rel="noopener noreferrer"
                      />
                    ),
                  }}
                />
              </li>
              <li>{t("pages.payments.paymentHowToGetFullSalary_lineItem6")}</li>
              <li>
                <Trans
                  i18nKey="pages.payments.paymentHowToGetFullSalary_lineItem7"
                  components={{
                    "using-paid-time-off-during-leave-link": (
                      <a
                        href={routes.external.massgov.usingPaidTimeOff}
                        rel="noopener noreferrer"
                        target="_blank"
                      />
                    ),
                  }}
                />
              </li>
            </ul>
          </Details>
        </section>

        {/* Table section */}
        {shouldShowPaymentsTable && (
          <PaymentsTable
            payments={payments}
            absenceId={absence_id}
            claimDetail={claim_detail}
            userRole="Claimant"
            getRouteFor={portalFlow.getRouteFor}
            waitingPeriods={showFINEOSWaitingPeriod ? waitingPeriods : []}
          />
        )}
      </section>

      <PaymentFAQSection absence_id={absence_id} userRole="Claimant" />
      <GetHelpSection showPrepaidCardHelp={showPrepaidCardContent} />
    </StatusNavigationAndAlertsWrapper>
  );
};

export default withClaimDetail(withPayment(withWaitingPeriods(PaymentsPage)));

export type PaymentStatusViewHelper = ReturnType<
  typeof paymentStatusViewHelper
>;

// Function to calculate and return the various pieces of data the status and payments pages need.
// Pending a restructure of the Payments and Status components it's going to be tempting to return
// things like 'showPaymentsTab' from here. Instead, create a new function like 'showPaymentsTab'.
export function paymentStatusViewHelper(
  claimDetail: ClaimDetail | EmployerClaimReview,
  payments: ApiResourceCollection<Payment>
) {
  const hasPayments = claimDetail.hasPayments(payments);

  // for claims that have no payments and aren't intermittent
  // the date the user should come back to check their payment status
  // that shows in the intro text
  const checkbackDate = calculateCheckbackDate({
    claimDetail,
    hasPayments,
  });

  return {
    checkbackDate,
  };
}

// Determine whether the payments tab should be shown
export function showPaymentsTab(
  claimDetail: ClaimDetail | EmployerClaimReview,
  payments: ApiResourceCollection<Payment>
) {
  return claimDetail.hasApprovedStatus || claimDetail.hasPayments(payments);
}

export function getInfoAlertContext(
  claimDetail: ClaimDetail | EmployerClaimReview
) {
  // changes InfoAlert text
  const onlyHasNewBornBondingReason =
    claimDetail.hasBondingLeavePeriod &&
    !claimDetail.hasPregnancyLeavePeriod &&
    claimDetail.hasNewbornBondingPeriod;
  const onlyHasPregnancyReason =
    claimDetail.hasPregnancyLeavePeriod && !claimDetail.hasBondingLeavePeriod;

  if (onlyHasNewBornBondingReason) {
    return "bonding";
  }

  if (onlyHasPregnancyReason) {
    return "pregnancy";
  }
  return "";
}
