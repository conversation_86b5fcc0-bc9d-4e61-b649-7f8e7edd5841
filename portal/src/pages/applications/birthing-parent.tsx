import Fieldset from "src/components/core/Fieldset";
import PageNotFound from "src/components/PageNotFound";
import QuestionPage from "src/components/QuestionPage";
import React from "react";
import { isFeatureEnabled } from "src/services/featureFlags";
import { useTranslation } from "src/locales/i18n";
import withBenefitsApplication from "src/hoc/withBenefitsApplication";

/**
 * A form page to capture leave details for a birthing parent.
 */
export const BirthingParent = () => {
  const { t } = useTranslation();

  // @todo update handleSave to save the form state
  const handleSave = () => Promise.resolve();

  if (isFeatureEnabled("enableMedToBondIntakeEnhancements")) {
    return (
      <QuestionPage title={t("pages.birthingParent.title")} onSave={handleSave}>
        <Fieldset>
          <p>Coming soon to a browser near you.</p>
        </Fieldset>
      </QuestionPage>
    );
  }

  return <PageNotFound />;
};

export default withBenefitsApplication(BirthingParent);
