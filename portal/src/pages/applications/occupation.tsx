import { t, useTranslation } from "src/locales/i18n";
import withBenefitsApplication, {
  WithBenefitsApplicationProps,
} from "src/hoc/withBenefitsApplication";

import Dropdown from "src/components/core/Dropdown";
import Heading from "src/components/core/Heading";
import { IndustrySector } from "src/models/BenefitsApplication";
import PageNotFound from "src/components/PageNotFound";
import QuestionPage from "src/components/QuestionPage";
import React from "react";
import { Trans } from "react-i18next";
import { getEmployerNameContent } from "src/utils/getEmployerNameContent";
import { isFeatureEnabled } from "src/services/featureFlags";
import { pick } from "lodash";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";

export const fields = ["claim.industry_sector"];

/**
 * A form page to capture an applicant's job industry
 */
export const Occupation = (props: WithBenefitsApplicationProps) => {
  const { t } = useTranslation();
  const { appLogic, claim } = props;
  const { formState, updateFields } = useFormState(pick(props, fields).claim);
  const getFunctionalInputProps = useFunctionalInputProps({
    errors: appLogic.errors,
    formState,
    updateFields,
  });

  const handleSave = () =>
    appLogic.benefitsApplications.update(claim.application_id, formState);

  const industrySectorDropdownChoices = getChoices();

  /**
   * Constructs a translatable question title using the employer's DBA name and EIN.
   */
  const getQuestionTitle = () => {
    const { employer_dba: dba, employer_fein: ein } = claim;
    const defaultString = "pages.claimsOccupation.questionTitle";
    const employerNameContent = getEmployerNameContent(dba, ein, defaultString);
    const isInvalidFein = !ein || ein.replace(/-/g, "") === "000000000";

    if (isInvalidFein) {
      return <Trans i18nKey="pages.claimsOccupation.questionTitle_noEin" />;
    }

    return (
      <Trans
        i18nKey={employerNameContent}
        values={{
          dba,
          ein,
        }}
        defaults={defaultString}
      />
    );
  };

  if (isFeatureEnabled("enableOccupationDataCollection")) {
    return (
      <QuestionPage
        title={t("pages.claimsOccupation.title")}
        onSave={handleSave}
      >
        <Heading level="2" size="1">
          {getQuestionTitle()}
        </Heading>
        <Heading level="3">
          {t("pages.claimsOccupation.questionDescription")}
        </Heading>
        <Dropdown
          {...getFunctionalInputProps("industry_sector")}
          choices={industrySectorDropdownChoices}
          label={t("pages.claimsOccupation.questionLabel")}
          smallLabel
          autocomplete
          labelClassName="" // This prevents the label from being bold
        />
      </QuestionPage>
    );
  }

  return <PageNotFound />;
};

const getChoices = () => {
  const choices: Array<{
    label: string;
    value: string;
  }> = [];
  Object.keys(IndustrySector).forEach((key) => {
    choices.push({
      label: t("shared.industrySector", {
        context: key,
      }),
      value: IndustrySector[key as keyof typeof IndustrySector],
    });
  });
  return choices;
};

export default withBenefitsApplication(Occupation);
