import LeaveReason, { LeaveReasonType } from "src/models/LeaveReason";
import withBenefitsApplication, {
  WithBenefitsApplicationProps,
} from "src/hoc/withBenefitsApplication";

import Alert from "src/components/core/Alert";
import BackButton from "src/components/BackButton";
import ButtonLink from "src/components/ButtonLink";
import ExternalLink from "src/components/core/ExternalLink";
import GeneralInformationByApplicationType from "src/features/benefits-applications/GeneralInformationByApplicationType";
import Heading from "src/components/core/Heading";
import Icon from "src/components/core/Icon";
import React from "react";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import { get } from "lodash";
import { isFeatureEnabled } from "src/services/featureFlags";
import routeWithParams from "src/utils/routeWithParams";
import routes from "src/routes";
import { useTranslation } from "src/locales/i18n";

/**
 * Success page, shown when an application is successfully submitted.
 */
export const Success = (props: WithBenefitsApplicationProps) => {
  const { claim } = props;
  const { t } = useTranslation();
  const { isCompletedAndClaimIsClosed } = claim;

  const leaveReason = get(claim, "leave_details.reason") as LeaveReasonType;

  const getHealthConditionFormName = () => {
    if (leaveReason === LeaveReason.care) {
      return t("shared.certificationFormCare");
    } else {
      // Claims with other leave reasons (bonding, military, etc.) are not expected
      // to see a form link on this page. Show the medical content by default.
      return t("shared.certificationFormMedical");
    }
  };

  const getHealthConditionFormLink = () => {
    if (leaveReason === LeaveReason.care) {
      return routes.external.massgov.familyMemberSeriousHealthConditionForm;
    } else {
      // Claims with other leave reasons (bonding, military, etc.) are not expected
      // to see a form link on this page. Show the medical content by default.
      return routes.external.massgov.seriousHealthConditionForm;
    }
  };

  const showIncompleteDocumentationBanner =
    isFeatureEnabled("documentUploadOptional") &&
    // Completed but not ready for review means that there are outstanding evidence documents.
    claim.isCompleted &&
    !claim.isReadyForReview &&
    // banner should only show for medical, pregnancy, or care for now due to deciding to limit scope of PFMLPB-11656
    (leaveReason === LeaveReason.medical ||
      leaveReason === LeaveReason.care ||
      leaveReason === LeaveReason.pregnancy);

  const getTitle = () => {
    if (showIncompleteDocumentationBanner) {
      if (leaveReason === LeaveReason.medical) {
        return t("pages.claimsSuccess.titleIncompleteMedical");
      } else if (leaveReason === LeaveReason.pregnancy) {
        return t("pages.claimsSuccess.titleIncompletePregnancy");
      } else if (leaveReason === LeaveReason.care) {
        return t("pages.claimsSuccess.titleIncompleteCaring");
      }
    } else {
      return t("pages.claimsSuccess.title");
    }
  };

  const showGeneralInfoByApplicationType = !isCompletedAndClaimIsClosed;

  return (
    <React.Fragment>
      <BackButton
        label={t("pages.claimsSuccess.backButtonLabel")}
        href={routes.applications.index}
      />
      <Title>{getTitle()}</Title>

      <p className="margin-bottom-5">
        <Trans
          i18nKey="pages.claimsSuccess.claimantApplicationId"
          values={{ absence_id: claim.fineos_absence_id }}
        />
      </p>

      <Heading level="2">
        <Icon
          size={4}
          className="margin-right-2 text-secondary text-middle"
          fill="currentColor"
          name="arrow_forward"
        />
        {t("pages.claimsSuccess.trackAndManageApplicationHeader")}
      </Heading>
      {showIncompleteDocumentationBanner && (
        <Alert
          className="margin-bottom-3"
          heading=""
          state="warning"
          aria-label="Incomplete Documentation Alert"
        >
          <b>{t("pages.claimsSuccess.incompleteDocumentationAlert_header")}</b>

          <Trans
            i18nKey="pages.claimsSuccess.incompleteDocumentationAlert_body"
            components={{
              "health-condition-form-link": (
                <ExternalLink href={getHealthConditionFormLink()} />
              ),
            }}
            values={{
              health_condition_form_name: getHealthConditionFormName(),
            }}
          />
        </Alert>
      )}
      <ButtonLink
        className="margin-top-2 margin-bottom-4"
        href={routeWithParams("applications.status.claim", {
          absence_id: claim.fineos_absence_id,
        })}
      >
        {t("pages.claimsSuccess.viewApplicationButtonText")}
      </ButtonLink>

      <div className="measure-6">
        {showGeneralInfoByApplicationType && (
          <GeneralInformationByApplicationType {...props} />
        )}
      </div>
    </React.Fragment>
  );
};

export default withBenefitsApplication(Success);
