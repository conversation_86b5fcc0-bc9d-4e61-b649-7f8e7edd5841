import {
  BenefitsApplicationDocument,
  DocumentTypeEnum,
  MilitaryDocumentTypeEnum,
} from "src/models/Document";
import {
  DocumentsLoadError,
  DocumentsUploadError,
  ValidationError,
} from "src/errors";
import { useMemo, useState } from "react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import DocumentsApi from "src/api/DocumentsApi";
import { ErrorsLogic } from "./useErrorsLogic";
import TempFile from "src/models/TempFile";
import assert from "assert";
import useCollectionState from "./useCollectionState";

const useDocumentsLogic = ({ errorsLogic }: { errorsLogic: ErrorsLogic }) => {
  /**
   * State representing the collection of documents for the current user.
   * Initialize to empty collection, but will eventually store the document
   * state as API calls are made to fetch the documents on a per-application basis,
   * and as new documents are created.
   *
   * The collection from useCollectionState stores all documents together,
   * and we filter documents based on application_id to provide the correct documents
   * to the requesting components.
   */

  const {
    collection: documents,
    addItem: addDocument,
    addItems: addDocuments,
  } = useCollectionState(
    new ApiResourceCollection<BenefitsApplicationDocument>("fineos_document_id")
  );

  // Used in Checklist.tsx to determine if the user has deferred the certificate document
  const [hasDeferredCertDoc, setHasDeferredCertDoc] = useState<boolean>(false);

  const documentsApi = useMemo(() => new DocumentsApi(), []);
  const [loadedApplicationDocs, setLoadedApplicationDocs] = useState<{
    [application_id: string]: { isLoading: boolean };
  }>({});

  /**
   * Check if docs for this application have been loaded
   * We use a separate array and state here, rather than using the collection,
   * because documents that don't have items won't be represented in the collection.
   */
  const hasLoadedClaimDocuments = (application_id: string) =>
    application_id in loadedApplicationDocs &&
    loadedApplicationDocs[application_id].isLoading === false;

  const isLoadingClaimDocuments = (application_id: string) =>
    application_id in loadedApplicationDocs &&
    loadedApplicationDocs[application_id].isLoading === true;

  /**
   * Load all documents for a user's claim
   * This must be called before documents are available
   */
  const loadAll = async (application_id: string) => {
    // if documents already contains docs for application_id, don't load again
    // or if we started making a request to the API to load documents, don't load again
    if (
      hasLoadedClaimDocuments(application_id) ||
      isLoadingClaimDocuments(application_id)
    )
      return;

    errorsLogic.clearErrors();

    setLoadedApplicationDocs((loadingClaimDocuments) => {
      const docs = { ...loadingClaimDocuments };
      docs[application_id] = {
        isLoading: true,
      };
      return docs;
    });

    try {
      const { documents: loadedDocuments } =
        await documentsApi.getDocuments(application_id);
      addDocuments(loadedDocuments.items);
      setLoadedApplicationDocs((loadingClaimDocuments) => {
        const docs = { ...loadingClaimDocuments };
        docs[application_id] = {
          isLoading: false,
        };
        return docs;
      });
    } catch {
      errorsLogic.catchError(new DocumentsLoadError({ application_id }));
    }
  };

  /**
   * Submit files to the API and set application errors if any
   * @param application_id - application id for claim
   * @param filesWithUniqueId - array of objects including unique Id and File to upload and attach to the application
   * @param fineosDocumentType - FINEOS document type of the document(s)
   * @param pfmlDocumentType - PFML document type of the document(s) (possibly null)
   */
  const attachApplicationDocuments = (
    application_id: string,
    filesWithUniqueId: TempFile[],
    fineosDocumentType: DocumentTypeEnum | MilitaryDocumentTypeEnum,
    pfmlDocumentType: DocumentTypeEnum | null
  ) => {
    assert(application_id);
    const errorMetadata = { application_id };
    const uploadFile = async (fileWithUniqueId: TempFile) => {
      const { document } = await documentsApi.attachApplicationDocument(
        application_id,
        fileWithUniqueId,
        fineosDocumentType,
        pfmlDocumentType
      );
      return document;
    };

    return uploadFiles(filesWithUniqueId, uploadFile, errorMetadata);
  };

  /**
   * Submit files to the API for a change request and set application errors if any
   */
  const attachChangeRequestDocuments = (
    change_request_id: string,
    filesWithUniqueId: TempFile[],
    documentType: DocumentTypeEnum,
    pfmlDocumentType: DocumentTypeEnum | null
  ) => {
    assert(change_request_id);
    const errorMetadata = { change_request_id };
    const uploadFile = async (fileWithUniqueId: TempFile) => {
      const { document } = await documentsApi.attachChangeRequestDocument(
        change_request_id,
        fileWithUniqueId,
        documentType,
        pfmlDocumentType
      );
      return document;
    };

    return uploadFiles(filesWithUniqueId, uploadFile, errorMetadata);
  };

  /**
   * Helper method for uploading files, adding uploaded files to the documents collection, and handling errors.
   * @param uploadFile a function which, given a file, will return a Promise to make the actual file upload API request
   * @param errorMetadata additional data to log with any errors that occur
   */
  const uploadFiles = async (
    filesWithUniqueId: TempFile[],
    uploadFile: (file: TempFile) => Promise<BenefitsApplicationDocument>,
    errorMetadata: { application_id?: string; change_request_id?: string }
  ) => {
    errorsLogic.clearErrors();

    if (!filesWithUniqueId.length) {
      errorsLogic.catchError(
        new ValidationError([
          {
            // field and type will be used for forming the internationalized error message
            field: "file", // 'file' is the field name in the API
            message: "Client requires at least one file before sending request",
            type: "required",
            namespace: "documents",
          },
        ])
      );
      return [];
    }

    const uploadResults: Array<{ success: boolean }> = [];

    // The PFML API uploads and receives application and change request
    // documents in FINEOS, which is unable to handle uploading and receiving
    // multiple documents for the same entity in parallel. Overlapping calls to
    // receive documents will result in an error. It is necessary to await each
    // file upload in series.
    for (const fileWithUniqueId of filesWithUniqueId) {
      try {
        const document = await uploadFile(fileWithUniqueId);
        addDocument(document);
        uploadResults.push({ success: true });
      } catch (error) {
        errorsLogic.catchError(
          new DocumentsUploadError(
            fileWithUniqueId.id,
            errorMetadata,
            error instanceof ValidationError && error.issues.length
              ? error.issues[0]
              : null
          )
        );
        uploadResults.push({ success: false });
      }
    }

    return uploadResults;
  };

  /**
   * Download document from the API and sets app errors if any
   */
  const download = async (document: BenefitsApplicationDocument) => {
    errorsLogic.clearErrors();
    try {
      const response = await documentsApi.downloadDocument(document);
      return response;
    } catch (error) {
      errorsLogic.catchError(error);
    }
  };

  return {
    attachApplicationDocuments,
    attachChangeRequestDocuments,
    download,
    hasLoadedClaimDocuments,
    isLoadingClaimDocuments,
    documents,
    loadAll,
    hasDeferredCertDoc,
    setHasDeferredCertDoc,
  };
};

export default useDocumentsLogic;
