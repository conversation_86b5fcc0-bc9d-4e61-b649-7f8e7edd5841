import type { ChatWidgetLogic } from "./useChatWidgetLogic";
import OAuthServerApi from "src/api/OAuthServerApi";
import type User from "src/models/User";
import { isFeatureEnabled } from "src/services/featureFlags";
import { useEffect } from "react";

export const useChatWidget = (
  user: User,
  { isChatWidgetInitialized, setIsChatWidgetInitialized }: ChatWidgetLogic
) => {
  const oAuthServerApi = new OAuthServerApi();
  const isLiveChatEnabled = isFeatureEnabled("enableLiveChat"); // TODO (PFMLPB-21132): Cleanup when feature flag is removed
  const brandId = process.env.cxoneBrandId;

  const initializeChatWidget = async (brandId: string) => {
    if (!brandId) {
      console.error("CXOne brandId not found", {
        brandId,
        isLiveChatEnabled,
        hasVerifiedOrg: user.hasVerifiedAnOrganization?.(),
        page: window?.location?.pathname,
      });
      return;
    }

    if (typeof window.cxone !== "function") {
      console.error(
        "CXOne widget not initialized: window.cxone is not a function"
      );
      return;
    }

    try {
      const { code } = await oAuthServerApi.getOAuthServerCode();
      window.cxone("init", brandId);
      window.cxone("chat", "setAuthorizationCode", code.authz_code);
      window.cxone("guide", "init");
      window.cxone("guide", "setCustomFields", {
        customerName: { value: user.first_name, hidden: true },
      });
      window.cxone(
        "guide",
        "setCustomCss",
        '[data-selector="PRECONTACT_SURVEY_ICON"] {width: 143px !important; height: 39px !important; object-fit: contain !important; }'
      );
    } catch (e) {
      console.error("Failed to initialize chat widget:", e);
    }
  };

  // This effect will run only once after the component is mounted
  // so it should only happen once per page
  useEffect(() => {
    const canChatWidgetInitialize =
      !!brandId &&
      isLiveChatEnabled &&
      user.hasVerifiedAnOrganization() &&
      !isChatWidgetInitialized;

    if (!canChatWidgetInitialize) {
      return;
    }

    // Set as early as possible for safety
    setIsChatWidgetInitialized(true);
    initializeChatWidget(brandId);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};
