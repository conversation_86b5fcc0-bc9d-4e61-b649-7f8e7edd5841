import LeaveAdminsApi, {
  LeaveAdminsAddRequest,
  LeaveAdminsRemoveRequest,
  LeaveAdminsSearchRequest,
} from "src/api/LeaveAdminsApi";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import { AuthLogic } from "./useAuthLogic";
import { ErrorsLogic } from "./useErrorsLogic";
import { PortalFlow } from "./usePortalFlow";
import { UserLeaveAdministrator } from "src/models/User";
import { isEqual } from "lodash";
import useCollectionState from "./useCollectionState";
import { useState } from "react";

const useLeaveAdminsLogic = ({
  errorsLogic,
  portalFlow,
}: {
  errorsLogic: ErrorsLogic;
  portalFlow: PortalFlow;
  auth: AuthLogic;
}) => {
  const leaveAdminsApi = new LeaveAdminsApi();

  /**
   * Track loading state of leave admins data. We store the params that were
   * used to fetch the data, so we can re-fetch when the params change.
   */
  const [isLoadingLeaveAdmins, setIsLoadingLeaveAdmins] = useState<boolean>();
  const [activeParams, setActiveParams] = useState<LeaveAdminsSearchRequest>();
  const [isDownloadingCSV, setIsDownloadingCSV] = useState<boolean>(false);

  const { collection: leaveAdmins, setCollection: setLeaveAdmins } =
    useCollectionState(new ApiResourceCollection<UserLeaveAdministrator>("id"));

  const loadAll = async (params: LeaveAdminsSearchRequest) => {
    if (isEqual(activeParams, params) || isLoadingLeaveAdmins) return;
    errorsLogic.clearErrors();
    setIsLoadingLeaveAdmins(true);
    try {
      const { leaveAdmins } = await leaveAdminsApi.list(params);
      setLeaveAdmins(
        new ApiResourceCollection<UserLeaveAdministrator>("id", leaveAdmins)
      );
    } catch (error) {
      errorsLogic.catchError(error);
    } finally {
      setActiveParams(params);
      setIsLoadingLeaveAdmins(false);
    }
  };

  const add = async (requestBody: LeaveAdminsAddRequest) => {
    try {
      await leaveAdminsApi.add(requestBody);
      setActiveParams(undefined); // reset params so leave admin list will display fresh data on next load
      portalFlow.goToPageFor(
        "REDIRECT",
        {},
        {
          employer_id: requestBody.employer_id,
          add: requestBody.email_address,
        },
        { redirect: true }
      );
    } catch (error) {
      errorsLogic.catchError(error);
    }
  };

  const remove = async (
    requestBody: LeaveAdminsRemoveRequest,
    queryParams: { employer_id: string; org_name: string; remove: string },
    removingSelf = false
  ) => {
    try {
      await leaveAdminsApi.remove(requestBody);
      if (removingSelf) {
        portalFlow.goToPageFor(
          "REDIRECT_FROM_SELF_REMOVAL",
          {},
          { remove: queryParams.remove, org_name: queryParams.org_name },
          { redirect: true }
        );
      } else {
        setActiveParams(undefined); // reset params so leave admin list will display fresh data on next load
        portalFlow.goToPageFor("REDIRECT", {}, queryParams, { redirect: true });
      }
    } catch (error) {
      errorsLogic.catchError(error);
    }
  };

  const downloadCSV = async (
    fileName = "leave_admin_applications.csv",
    totalApplications?: number
  ) => {
    try {
      setIsDownloadingCSV(true);
      const csvBlob = await leaveAdminsApi.downloadCSV(totalApplications);

      const downloadUrl = URL.createObjectURL(csvBlob);

      const downloadLink = document.createElement("a");
      downloadLink.href = downloadUrl;
      downloadLink.download = fileName;
      document.body.appendChild(downloadLink);
      downloadLink.click();

      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(downloadUrl);
      return true;
    } catch (error) {
      errorsLogic.catchErrorNoShow(error);
      return false;
    } finally {
      setIsDownloadingCSV(false);
    }
  };

  return {
    add,
    isLoadingLeaveAdmins,
    leaveAdmins,
    loadAll,
    remove,
    setActiveParams,
    downloadCSV,
    isDownloadingCSV,
  };
};

export default useLeaveAdminsLogic;
export type LeaveAdminsLogic = ReturnType<typeof useLeaveAdminsLogic>;
