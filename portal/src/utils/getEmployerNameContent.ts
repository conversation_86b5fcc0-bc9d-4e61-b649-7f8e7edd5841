const INVALID_DBA = "EMPLOYER NOT FOUND";
const INVALID_FEIN = "000000000";

export function getEmployerNameContent(
  employerDba: string | null,
  employerFein: string | null,
  employerNameContent: string
) {
  const isValidDba = Boolean(
    employerDba && employerDba.toUpperCase() !== INVALID_DBA
  );
  const isValidFein = Boolean(
    employerFein && employerFein.replace(/-/g, "") !== INVALID_FEIN
  );

  if (isValidDba && isValidFein) {
    return `${employerNameContent}_dba`;
  }

  if (isValidFein) {
    return `${employerNameContent}_ein`;
  }

  return employerNameContent;
}
