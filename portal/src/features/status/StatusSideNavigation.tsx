import ClaimDetail from "src/models/ClaimDetail";
import { PortalFlow } from "src/hooks/usePortalFlow";
import React from "react";
import SideNavigation from "src/components/SideNavigation";
import { isFeatureEnabled } from "src/services/featureFlags";
import { useTranslation } from "src/locales/i18n";
import { useWindowSize } from "react-use";

interface StatusSideNavigationProps {
  activePath: string;
  getRouteFor: PortalFlow["getRouteFor"];
  claimDetail: ClaimDetail;
}

/**
 * Side Navigation tabs to be used on the Claimant Status Page
 */
const StatusSideNavigation = ({
  activePath,
  getRouteFor,
  claimDetail,
}: StatusSideNavigationProps) => {
  const { t } = useTranslation();
  const absence_id = claimDetail.fineos_absence_id;
  const showIntermittentLeaveTab =
    claimDetail.isIntermittent && claimDetail.hasApprovedStatus;
  const baseStatusTabs = [
    {
      label: t("components.statusNavigationTabs.statusDetails"),
      href: getRouteFor("STATUS", {}, { absence_id }),
    },
    {
      label: t("components.statusNavigationTabs.payments"),
      href: getRouteFor("VIEW_PAYMENTS", {}, { absence_id }),
    },
  ];

  const intermittentLeaveTab = {
    label: t("components.statusNavigationTabs.reportIntermittentLeave"),
    href: getRouteFor("REPORT_INTERMITTENT_LEAVE", {}, { absence_id }),
  };

  const intermittentLeaveStatusTabs = [...baseStatusTabs, intermittentLeaveTab];

  const { width: windowWidth } = useWindowSize();

  const getClassName = (windowWidth: number) => {
    if (isFeatureEnabled("enableClaimantPortalSideNavigationV2")) {
      // Window size where side nav is on the left side of the page
      // at a window size less than 1053 the side nav moves to the top of the page
      const windowWidthBreak = 1053;
      if (windowWidth >= windowWidthBreak) {
        // className width-card-lg caps the width of the side nav component at 18rem which is a font-size dependent measurement
        // If we ever change our default font size, fonts, or start respecting browser font settings,
        // this could potentially change the window width (1053) at which to make the side nav sticky
        return "desktop:width-card-lg position-sticky height-full top-1px";
      } else {
        return "width-full desktop:width-card-lg";
      }
    } else {
      // on mobile and tablet, nav fills all 12 columns, and floats to the top
      // on desktop, nav fills only 2 columns, and sits on the side. (aka "Side Nav")
      return "grid-col-12 desktop:grid-col-2";
    }
  };

  return (
    <div
      data-testid="status-side-navigation"
      className={getClassName(windowWidth)}
    >
      <SideNavigation
        activePath={activePath}
        ignoreQueries
        tabs={
          showIntermittentLeaveTab
            ? intermittentLeaveStatusTabs
            : baseStatusTabs
        }
      />
    </div>
  );
};

export default StatusSideNavigation;
