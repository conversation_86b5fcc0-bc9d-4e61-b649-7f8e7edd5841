import { LeaveDetails, SimplifiedLeaveDetails } from "./LeaveDetails";
import React, { useEffect, useMemo, useState } from "react";
import ReportOtherBenefitsSection, {
  showReportOtherBenefitsSection,
} from "./ReportOtherBenefitsSection";
import UploadDocs, { canUploadDocsAndAppeals } from "./UploadDocs";
import {
  getExtensionSubmissionDate,
  hasExtensionSubmittedViaPortal,
} from "./DocumentsSubmitted";
import {
  getRFIStatus,
  getSubmitProofOfBondingStatus,
  stepTypesForLeave,
} from "./utils/getStepsWithStatuses";
import {
  managedRequirementDates,
  mostRecentlyCreatedManagedRequirement,
} from "src/models/ManagedRequirement";
import useLoggingLogic, { LogObjectBody } from "src/hooks/useLoggingLogic";

import { AbsencePeriod } from "src/models/AbsencePeriod";
import Appeals from "./Appeals";
import ApplicationAndEmployerIdBox from "./ApplicationAndEmployerIdBox";
import { ApplicationTimelineSection } from "./ApplicationTimelineSection";
import BenefitYearDetail from "./BenefitYearDetail";
import ChangeRequestSections from "./ChangeRequestSections";
import ReportLeaveHours from "./ReportLeaveHours";
import StatusNavigationAndAlertsWrapper from "./StatusNavigationAndAlertsWrapper";
import { StatusPropsWithAppeal } from "src/pages/applications/status";
import StepsAndAccordions from "./StepsAndAccordions";
import Title from "src/components/core/Title";
import ViewYourNotices from "./ViewYourNotices";
import { debounce } from "lodash";
import { findLatestAppealByAbsenceId } from "src/models/Appeal";
import { getLogObject } from "./utils/getLogObject";
import { getRFIDocument } from "src/models/Document";
import { showPaymentsTab } from "src/pages/applications/status/payments";
import { useTranslation } from "src/locales/i18n";

export const ClaimStatus = (props: StatusPropsWithAppeal) => {
  const {
    appLogic,
    claim_detail,
    change_requests,
    documents,
    payments,
    appeals,
  } = props;
  const [rfiStatus, setRfiStatus] = useState("");

  // Extract data from appLogic
  const { benefitYears, waitingPeriods, users } = appLogic;
  const { t } = useTranslation();

  // Payments data
  const isPaymentsTab = showPaymentsTab(claim_detail, payments);
  // Claim detail data
  const { fineos_absence_id } = claim_detail;

  const latestAppeal = findLatestAppealByAbsenceId(
    appeals.appeals.items,
    fineos_absence_id
  );

  const userHasMultipleTaxIdentifiers = !!(
    users.user && users.user.has_multiple_tax_identifiers
  );
  const isApprovedIntermittentLeave =
    claim_detail.isIntermittent && claim_detail.hasApprovedStatus;

  // RFI document for the banner and step indicator
  const rfiDocument = getRFIDocument(documents);

  // Show step tracker and accordions for extension claims.
  // Claim has an extension if it was transitioned from med to bonding or the leave end date was extende.
  const canShowExtensionComponents = !!claim_detail.hasPendingExtensions;

  const showIntermittentLeaveReporting = isApprovedIntermittentLeave;

  // Step indicator data
  const leaveType = canShowExtensionComponents
    ? claim_detail.lastAbsencePeriodLeaveReason
    : claim_detail.firstAbsencePeriodLeaveReason;

  const mostRecentManagedRequirement = mostRecentlyCreatedManagedRequirement(
    claim_detail.managed_requirements
  );
  const { employerReviewedOn, employerFollowUpDate } = managedRequirementDates(
    mostRecentManagedRequirement
  );

  const canShowSimplifiedDetailsAndSteps =
    !claim_detail.hasFinalDecision &&
    !claim_detail.hasInReviewStatus &&
    !claim_detail.hasMultipleAbsencePeriods;

  const stepTypes = stepTypesForLeave(claim_detail, leaveType);

  const showApplicationTimelineSection =
    !canShowSimplifiedDetailsAndSteps &&
    !canShowExtensionComponents &&
    claim_detail.hasPendingStatus;

  // The steps in the StepIndicator should always have matching accordions, so we're checking for the presence of
  // the 'submitProofOfBonding" step. When that step is included, then we'll show the upload component.
  const showProofOfBondingSection = !!leaveType
    ? stepTypes.includes("submitProofOfBonding")
    : false;

  const submitProofOfBondingStep = getSubmitProofOfBondingStatus(claim_detail);
  const showProofOfBondingUploadBanner =
    showProofOfBondingSection && submitProofOfBondingStep !== "completed";

  const showRfiSection = !!rfiDocument;

  const { showUploadSection, showAppealSection } = canUploadDocsAndAppeals(
    claim_detail,
    latestAppeal
  );

  const typesOfLeave = AbsencePeriod.groupByReason(
    claim_detail.absence_periods
  );
  const claimHasMultipleLeaveTypesOfLeave =
    Object.keys(typesOfLeave).length > 1;
  const extensionViaPortal = hasExtensionSubmittedViaPortal(
    claim_detail,
    change_requests.items
  );
  const extensionSubmissionDate = extensionViaPortal
    ? getExtensionSubmissionDate(change_requests.items)
    : "";

  const mostRecentChangeRequest = change_requests.items.find(
    (changeRequest) => changeRequest.submitted_time === extensionSubmissionDate
  );
  const determinedRfiStatus = getRFIStatus(documents, claim_detail);
  // We need to re-determine the RFI status when documents are uploaded from this page.
  // without this, the page shows "inProgress" after a document is uploaded, until the
  // user manually refreshes the page.
  useEffect(() => {
    const newRFIStatus = showRfiSection ? determinedRfiStatus : "noRFI";
    setRfiStatus(newRFIStatus);
  }, [
    documents,
    setRfiStatus,
    showRfiSection,
    determinedRfiStatus,
    claim_detail,
  ]);

  useEffect(() => {
    /**
     * If URL includes a location.hash then page
     * should scroll into view on that id tag,
     * provided the id tag exists.
     */
    if (location.hash) {
      const anchorId = document.getElementById(location.hash.substring(1));
      if (anchorId) anchorId.scrollIntoView();
    }
  }, []);
  // Heavy logging so we can tell when simplified leave details components are shown for a claim and why

  const { trackEvent, formatLoggingObject, formattedLogObject } =
    useLoggingLogic();

  // Made a debounced trackEvent function so that we can wait for all the data to get into its final state
  // and not spam New Relic with intermediate states as the data loads (see PFMLPB-13738)
  const trackEventDebounced: (
    logMessage: string,
    logObject?: LogObjectBody
  ) => void = useMemo(
    () =>
      debounce((logMessage: string, logObject?: LogObjectBody) => {
        trackEvent(logMessage, logObject);
      }, 500),
    [trackEvent]
  );

  useEffect(() => {
    const logObject = getLogObject({
      claim_detail,
      canShowSimplifiedDetailsAndSteps,
      showProofOfBondingSection,
      showUploadSection,
      showAppealSection,
      rfiStatus,
      showRfiSection,
      canShowExtensionComponents,
      extensionViaPortal,
    });
    formatLoggingObject(logObject);

    if (!canShowSimplifiedDetailsAndSteps) {
      trackEventDebounced(
        "Loaded ClaimStatus - Fell back to LeaveDetails",
        formattedLogObject
      );
    }
    if (canShowSimplifiedDetailsAndSteps && !canShowExtensionComponents) {
      trackEventDebounced(
        "Loaded ClaimStatus - Showed SimplifiedLeaveDetails",
        formattedLogObject
      );
    }
    if (canShowSimplifiedDetailsAndSteps && canShowExtensionComponents) {
      trackEventDebounced(
        "Loaded ClaimStatus - Extensions present",
        formattedLogObject
      );
    }
  }, [
    claim_detail,
    extensionViaPortal,
    rfiStatus,
    showAppealSection,
    showProofOfBondingSection,
    showRfiSection,
    showUploadSection,
    canShowSimplifiedDetailsAndSteps,
    canShowExtensionComponents,
    formattedLogObject,
    formatLoggingObject,
    trackEventDebounced,
  ]);

  return (
    <StatusNavigationAndAlertsWrapper {...props}>
      <ApplicationAndEmployerIdBox claim_detail={claim_detail} />
      <Title hidden>{t("pages.claimsStatus.applicationTitle")}</Title>
      {showApplicationTimelineSection && (
        <ApplicationTimelineSection
          claimSubmittedDate={claim_detail.created_at}
          absencePeriods={claim_detail.absence_periods}
          employerFollowUpDate={employerFollowUpDate}
          employerReviewedOn={employerReviewedOn}
          applicationId={claim_detail.application_id}
          docList={documents}
          absenceId={claim_detail.fineos_absence_id}
          appLogic={appLogic}
        />
      )}
      {canShowSimplifiedDetailsAndSteps || canShowExtensionComponents ? (
        <SimplifiedLeaveDetails
          claimDetail={claim_detail}
          showLeaveReasonHeader={claimHasMultipleLeaveTypesOfLeave}
        />
      ) : (
        <LeaveDetails
          claimDetail={claim_detail}
          isPaymentsTab={isPaymentsTab}
          showLeaveReasonHeader={claimHasMultipleLeaveTypesOfLeave}
        />
      )}
      <BenefitYearDetail
        loadFromClaimDetails={userHasMultipleTaxIdentifiers}
        mostRecentChangeRequest={mostRecentChangeRequest}
        benefitYearsLogic={benefitYears}
        waitingPeriodsLogic={waitingPeriods}
        claimDetail={claim_detail}
        extensionSubmissionDate={extensionSubmissionDate ?? ""}
      />
      {(canShowSimplifiedDetailsAndSteps || canShowExtensionComponents) && (
        <StepsAndAccordions
          appLogic={appLogic}
          claim_detail={claim_detail}
          leaveType={leaveType}
          rfiDocument={rfiDocument}
          documents={documents}
          change_requests={change_requests.items}
          showRfiSection={showRfiSection}
          showProofOfBondingUploadBanner={showProofOfBondingUploadBanner}
          showProofOfBondingSection={showProofOfBondingSection}
          mostRecentManagedRequirement={mostRecentManagedRequirement}
        />
      )}
      {canShowExtensionComponents && (
        <LeaveDetails
          claimDetail={claim_detail}
          ignoreFirstAbsencePeriod={true}
          showLeaveReasonHeader={claimHasMultipleLeaveTypesOfLeave}
        />
      )}
      {showIntermittentLeaveReporting && (
        <ReportLeaveHours
          absenceId={claim_detail.fineos_absence_id}
          getRouteFor={appLogic.portalFlow.getRouteFor}
        />
      )}
      <ChangeRequestSections
        claim_detail={claim_detail}
        appLogic={appLogic}
        changeRequests={change_requests.items}
      />
      <ViewYourNotices
        claim={claim_detail}
        documents={documents}
        downloadDocument={appLogic.documents.download}
      />
      {showUploadSection && (
        <UploadDocs appLogic={appLogic} claimDetail={claim_detail} />
      )}
      <Appeals
        showAppeal={showAppealSection}
        appeals={appeals}
        latestAppeal={latestAppeal}
        absenceId={claim_detail.fineos_absence_id}
        getRouteFor={appLogic.portalFlow.getRouteFor}
      />
      {showReportOtherBenefitsSection(claim_detail) && (
        <ReportOtherBenefitsSection />
      )}
    </StatusNavigationAndAlertsWrapper>
  );
};
