import ButtonLink from "src/components/ButtonLink";
import { DocumentTypeEnum } from "src/models/Document";
import Icon from "src/components/core/Icon";
import React from "react";
import { StepStatus } from "src/components/core/StepIndicator";
import classNames from "classnames";

export type ActionStatusEnum =
  | "completed"
  | "inProgress"
  | "actionRequired"
  | "notStarted"
  | "cancelled";

export interface ProgressDisplayItemProps {
  actionStatus: StepStatus;
  text?: string | React.ReactElement;
  statusTextColor?: "green" | "black";
  buttonLink?: string;
  documentType?: DocumentTypeEnum | null;
}

export const ProgressDisplayItem = ({
  actionStatus,
  text,
  statusTextColor = "black",
  buttonLink,
  documentType,
}: ProgressDisplayItemProps) => {
  return (
    <div
      className="display-flex grid-col-auto"
      title={documentType || undefined}
    >
      <IconForActionStatus actionStatus={actionStatus} />
      <div>
        <LabelForIcon statusTextColor={statusTextColor} text={text} />
        {!!buttonLink && <UploadDocumentsButton buttonLink={buttonLink} />}
      </div>
    </div>
  );
};

export default ProgressDisplayItem;

const IconForActionStatus = ({
  actionStatus,
}: {
  actionStatus: StepStatus;
}) => {
  if (actionStatus === "completed") {
    return <CompletedIcon />;
  }
  if (actionStatus === "inProgress") {
    return <InProgressIcon />;
  }
  if (actionStatus === "actionRequired") {
    return <ActionRequiredIcon />;
  }
  // Default
  return <InProgressIcon />;
};

const LabelForIcon = ({
  statusTextColor,
  text,
}: {
  statusTextColor: string;
  text?: string | React.ReactElement;
}) => {
  // The "Complete" status has green text next to it
  if (statusTextColor === "green") {
    const colorClass = getClassForColor("green");
    return <p className={classNames(colorClass, "margin-top-0 ")}>{text}</p>;
  }
  // All other statuses use black text
  return <p className="margin-top-0">{text}</p>;
};

const UploadDocumentsButton = ({ buttonLink }: { buttonLink: string }) => (
  <ButtonLink
    href={buttonLink}
    className="flex-align-left flex-justify-left margin-top-1 margin-bottom-2"
  >
    Upload documents
  </ButtonLink>
);

const getClassForColor = (color?: string) => {
  if (color === "green") return "text-secondary";
  if (color === "yellow") return "text-warning";
  // Default to no extra color, usuallly will leave the text black
  return "";
};
const DefaultActionStatusIcon = ({
  divTestId,
  wsdsIconName,
  color,
}: {
  divTestId: string;
  wsdsIconName: string;
  color?: string;
}) => {
  const colorClass = getClassForColor(color);
  const defaultIconClasses = "margin-right-05 position-relative text-middle";
  return (
    <div data-testid={divTestId}>
      <Icon
        name={wsdsIconName}
        className={classNames(defaultIconClasses, colorClass)}
        size={3}
      />
    </div>
  );
};

const InProgressIcon = () => (
  <DefaultActionStatusIcon
    divTestId="inProgressIcon"
    wsdsIconName="autorenew"
  />
);

const CompletedIcon = () => (
  <DefaultActionStatusIcon
    divTestId="completedIcon"
    wsdsIconName="check"
    color="green"
  />
);

const ActionRequiredIcon = () => (
  <DefaultActionStatusIcon
    divTestId="actionRequiredIcon"
    wsdsIconName="warning"
    color="yellow"
  />
);
