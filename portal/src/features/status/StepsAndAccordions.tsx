import {
  BenefitsApplicationDocument,
  ClaimDocument,
} from "src/models/Document";
import CertificationUploadSection, {
  getCertificationUploadActionStatus,
  showCertificationUpload,
} from "./CertificationUploadSection";
import {
  ManagedRequirement,
  managedRequirementDates,
} from "src/models/ManagedRequirement";
import {
  getAllStepStatuses,
  getRFIStatus,
  getStepsWithStatuses,
} from "./utils/getStepsWithStatuses";

import { AppLogic } from "src/hooks/useAppLogic";
import CertificationUploadBanner from "./CertificationUploadBanner";
import ChangeRequest from "src/models/ChangeRequest";
import ClaimDetail from "src/models/ClaimDetail";
import DFMLDecision from "./DFMLDecision";
import DFMLReview from "./DFMLReview";
import DocumentsSubmitted from "./DocumentsSubmitted";
import EmployerResponse from "./EmployerResponse";
import Heading from "src/components/core/Heading";
import { LeaveReasonType } from "src/models/LeaveReason";
import NoActionRequiredBanner from "./NoActionRequiredBanner";
import ProofOfBondingUploadBanner from "./ProofOfBondingUploadBanner";
import ProofOfBondingUploadSection from "./ProofOfBondingUploadSection";
import RFIAccordion from "./RFIAccordion";
import RFIUploadBanner from "./RFIUploadBanner";
import React from "react";
import StepIndicator from "src/components/core/StepIndicator";
import { useTranslation } from "react-i18next";

interface StepsAndAccordionsTypes {
  appLogic: AppLogic;
  claim_detail: ClaimDetail;
  leaveType: LeaveReasonType | null;
  rfiDocument: ClaimDocument | BenefitsApplicationDocument | undefined;
  documents: BenefitsApplicationDocument[];
  change_requests: ChangeRequest[];
  showRfiSection: boolean;
  showProofOfBondingUploadBanner: boolean;
  showProofOfBondingSection: boolean;
  mostRecentManagedRequirement: ManagedRequirement | undefined;
}

const StepsAndAccordions = ({
  appLogic,
  claim_detail,
  leaveType,
  rfiDocument,
  documents,
  change_requests,
  showRfiSection,
  showProofOfBondingUploadBanner,
  showProofOfBondingSection,
  mostRecentManagedRequirement,
}: StepsAndAccordionsTypes) => {
  const { t } = useTranslation();

  const steps = !!leaveType
    ? getStepsWithStatuses(
        leaveType,
        claim_detail,
        rfiDocument,
        documents,
        change_requests
      )
    : [];

  const {
    employerResponseActionStatus,
    dfmlReviewActionStatus,
    dfmlDecisionActionStatus,
    submittedProof,
  } = getAllStepStatuses(steps);

  const showCertificationUploadSection = showCertificationUpload(claim_detail);
  const certificationUploadActionStatus = getCertificationUploadActionStatus(
    claim_detail,
    change_requests
  );

  const certificationUploadShowingAndNotCompleted =
    showCertificationUploadSection &&
    certificationUploadActionStatus !== "completed";

  const showNoActionRequiredBanner =
    !showRfiSection &&
    !showProofOfBondingUploadBanner &&
    !certificationUploadShowingAndNotCompleted;

  const { employerReviewedOn, employerFollowUpDueOn } = managedRequirementDates(
    mostRecentManagedRequirement
  );
  return (
    <React.Fragment>
      {certificationUploadShowingAndNotCompleted && (
        <CertificationUploadBanner
          getRouteFor={appLogic.portalFlow.getRouteFor}
          claimDetail={claim_detail}
          actionStatus={certificationUploadActionStatus}
        />
      )}
      {showNoActionRequiredBanner && <NoActionRequiredBanner />}
      {showRfiSection && rfiDocument && (
        <RFIUploadBanner
          getRouteFor={appLogic.portalFlow.getRouteFor}
          claimDetail={claim_detail}
          rfiDocument={rfiDocument}
          documents={documents}
          downloadDocument={appLogic.documents.download}
        />
      )}
      {showProofOfBondingUploadBanner && (
        <ProofOfBondingUploadBanner
          getRouteFor={appLogic.portalFlow.getRouteFor}
          claimDetail={claim_detail}
          actionStatus={submittedProof}
        />
      )}
      <StepIndicator
        steps={steps}
        header={t("pages.claimsStatus.stepIndicatorHeader")}
      />
      <Heading level="2" size="3">
        {t("pages.claimsStatus.documentStatusHeading")}
      </Heading>
      <DocumentsSubmitted
        claimDetail={claim_detail}
        changeRequests={change_requests}
      />
      {showProofOfBondingSection && (
        <ProofOfBondingUploadSection
          claimDetail={claim_detail}
          actionStatus={submittedProof}
        />
      )}
      {showCertificationUploadSection && (
        <CertificationUploadSection
          getRouteFor={appLogic.portalFlow.getRouteFor}
          claimDetail={claim_detail}
          documents={documents}
          actionStatus={certificationUploadActionStatus}
        />
      )}
      {showRfiSection && rfiDocument && (
        <RFIAccordion
          documents={documents}
          rfiDocument={rfiDocument}
          actionStatus={getRFIStatus(documents, claim_detail)}
        />
      )}
      <Heading level="2" size="3">
        {t("pages.claimsStatus.dfmlActionsHeading")}
      </Heading>
      <EmployerResponse
        employerFollowUpDueOn={employerFollowUpDueOn}
        employerReviewedOn={employerReviewedOn}
        mostRecentManagedRequirement={mostRecentManagedRequirement}
        actionStatus={employerResponseActionStatus}
      />
      <DFMLReview actionStatus={dfmlReviewActionStatus} />
      <DFMLDecision actionStatus={dfmlDecisionActionStatus} />
    </React.Fragment>
  );
};

export default StepsAndAccordions;
