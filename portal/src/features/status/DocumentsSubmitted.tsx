import ClaimDetail, { DocumentRequirements } from "src/models/ClaimDetail";

import ChangeRequest from "src/models/ChangeRequest";
import { ClaimStatusAccordionWrapper } from "./ClaimStatusSectionWrapper";
import { DocumentType } from "src/models/Document";
import { ProgressDisplayItemProps } from "./ProgressDisplayItem";
import ProgressDisplayList from "./ProgressDisplayList";
import React from "react";
import { Trans } from "react-i18next";
import changeRequestsViewHelper from "src/utils/changeRequestsViewHelper";
import formatDate from "src/utils/formatDate";
import { isFeatureEnabled } from "src/services/featureFlags";
import { t } from "src/locales/i18n";

export interface DocumentsSubmittedProps {
  claimDetail: ClaimDetail;
  changeRequests: ChangeRequest[];
}

export const DocumentsSubmitted = ({
  claimDetail,
  changeRequests,
}: DocumentsSubmittedProps) => {
  return (
    <ClaimStatusAccordionWrapper
      id="documents-submitted"
      testId="documentsSubmitted"
    >
      <ProgressDisplayList
        actionStatus={"completed"}
        boldLeaderText={documentsSubmittedAccordionHeader(claimDetail)}
        progressDisplayItems={documentRequirementItemsAndApplicationDoc(
          claimDetail,
          changeRequests
        )}
      />
    </ClaimStatusAccordionWrapper>
  );
};

// All the documents that will be required in this component are required to complete the application, so they will
// always be "completed" AKA uploaded by the user during the application step 3.
export const translateRequirementsToItems = (
  documentRequirements: DocumentRequirements[]
) => {
  const documentRequirementList: ProgressDisplayItemProps[] =
    documentRequirements.map((documentRequirement) => ({
      actionStatus: "completed",
      text: (
        <span title="document type submitted on">
          <Trans
            i18nKey="pages.claimsStatus.documentTypeSubmittedOn"
            // Show pfml_document_type if available, or [fineos] document_type if not, and fall
            // back to the first allowed type if both are blank (which isn't possible with the
            // way this function is currently used, but the type checker prefers completeness)

            context={
              documentRequirement.pfml_document_type ||
              documentRequirement.fineos_document_type ||
              documentRequirement.allowed_document_types[0]
            }
            values={{
              uploadDate: formatDate(documentRequirement.upload_date).full(),
            }}
          />
        </span>
      ),
      statusTextColor: "black",
      documentType: documentRequirement.fineos_document_type,
    }));

  return documentRequirementList;
};

export const getDocumentRequirementUploadedItems = (
  claimDetail: ClaimDetail
) => {
  const documentRequirements = claimDetail.document_requirements;
  const allDocsUploaded: DocumentRequirements[] = documentRequirements.filter(
    (doc) => !!doc.upload_date
  );
  const uploadedDocsExcludingBondingProof: DocumentRequirements[] =
    allDocsUploaded.filter(
      (doc) =>
        doc.fineos_document_type !== DocumentType.certification["Child Bonding"]
    );

  const documentRequirementItems = translateRequirementsToItems(
    uploadedDocsExcludingBondingProof
  );
  return documentRequirementItems;
};

const getSumbissionDateAndText = (
  changeRequests: ChangeRequest[],
  claimDetail: ClaimDetail
): { submittedText: string; submissionDate: string } => {
  let submittedText;
  let submissionDate;

  if (claimDetail.isPendingMedToBonding) {
    submissionDate = getExtensionSubmissionDate(changeRequests);
    // if there are no submitted change requests then we can assume this extension was
    // added via call center. In that case, we default to a generic text excluding the
    // submission date.
    submittedText = submissionDate
      ? "pages.claimsStatus.medToBondingSubmittedOn"
      : "pages.claimsStatus.medToBondingSubmitted";
  } else if (claimDetail.hasPendingNonMedToBondingExtension) {
    submissionDate = getExtensionSubmissionDate(changeRequests);
    // if there are no submitted change requests then we can assume this extension was
    // added via call center. In that case, we default to a generic text excluding the
    // submission date.
    submittedText = submissionDate
      ? "pages.claimsStatus.extensionSubmittedOn"
      : "pages.claimsStatus.extensionSubmitted";
  } else {
    // no extensions so use the claim creation date
    submittedText = "pages.claimsStatus.applicationSubmittedOn";
    submissionDate = claimDetail.created_at;
  }

  return {
    submittedText,
    submissionDate: formatDate(submissionDate)?.full(),
  };
};
const getLeaveOrExtensionSubmitted = (
  claimDetail: ClaimDetail,
  changeRequests: ChangeRequest[]
): ProgressDisplayItemProps => {
  const { submittedText, submissionDate } = getSumbissionDateAndText(
    changeRequests,
    claimDetail
  );

  return {
    actionStatus: "completed",
    text: (
      <span
        title={t(submittedText)}
        data-testid="progress-list-item-leave-or-extension-submitted-text"
      >
        <Trans i18nKey={submittedText} values={{ submissionDate }} />
      </span>
    ),
    statusTextColor: "black",
  };
};

const onlyIdentificationProofUploaded = (
  documentRequirementItems: ProgressDisplayItemProps[]
) =>
  documentRequirementItems.length === 1 &&
  documentRequirementItems.some(
    (documentRequirementItem) =>
      documentRequirementItem?.documentType != null &&
      documentRequirementItem.documentType.includes(
        DocumentType.identityVerification
      )
  );

export const documentRequirementMissingItems = (claimDetail: ClaimDetail) =>
  claimDetail.document_requirements.filter((doc) => !doc.upload_date);

export const hasDocumentRequirementMissingItems = (claimDetail: ClaimDetail) =>
  // TODO (PFMLPB-25310): Remove check when documentUploadOptional feature flag is removed.
  isFeatureEnabled("documentUploadOptional") &&
  !!documentRequirementMissingItems(claimDetail).length;

const documentsSubmittedAccordionHeader = (claimDetail: ClaimDetail) => {
  if (claimDetail.isPendingMedToBonding) {
    return t("pages.claimsStatus.documentsSubmittedMedToBondingHeader");
  }

  const documentRequirementUploadedItems =
    getDocumentRequirementUploadedItems(claimDetail);
  const noExtensionAndOnlyIDSubmitted =
    !claimDetail.hasPendingNonMedToBondingExtension &&
    !claimDetail.hasPendingExtensionForBondingType &&
    onlyIdentificationProofUploaded(documentRequirementUploadedItems);

  if (claimDetail.hasPendingExtensionForBondingType) {
    return t("pages.claimsStatus.documentsSubmittedExtensionHeader");
  } else if (claimDetail.hasPendingNonMedToBondingExtension) {
    return t(
      "pages.claimsStatus.documentsSubmittedExtensionAndDocumentsHeader"
    );
  } else if (hasDocumentRequirementMissingItems(claimDetail)) {
    // only ID and leave application were submitted
    return t("pages.claimsStatus.documentsSubmittedApplicationAndIdHeader");
  } else if (noExtensionAndOnlyIDSubmitted) {
    // no extensions and only ID was submitted
    return t("pages.claimsStatus.documentsSubmittedMedToBondingHeader");
  } else {
    // No extensions and other docs submitted
    return t("pages.claimsStatus.documentsSubmittedHeader");
  }
};

const documentRequirementItemsAndApplicationDoc = (
  claimDetail: ClaimDetail,
  changeRequests: ChangeRequest[]
): ProgressDisplayItemProps[] => [
  ...getDocumentRequirementUploadedItems(claimDetail),
  getLeaveOrExtensionSubmitted(claimDetail, changeRequests),
];

export const getExtensionSubmissionDate = (changeRequests: ChangeRequest[]) => {
  const { mostRecentChangeRequestSubmissionDate } =
    changeRequestsViewHelper(changeRequests);

  return mostRecentChangeRequestSubmissionDate;
};

export const hasExtensionSubmittedViaPortal = (
  claimDetail: ClaimDetail,
  changeRequests: ChangeRequest[]
) => {
  const hasExtension = claimDetail.has_extensions;
  const { submittedChangeRequests } = changeRequestsViewHelper(changeRequests);
  const hasSubmittedChangeRequests = submittedChangeRequests.length > 0;
  return !!(hasExtension && hasSubmittedChangeRequests);
};

export default DocumentsSubmitted;
