import { Trans, useTranslation } from "react-i18next";

import { AppLogic } from "src/hooks/useAppLogic";
import Appeal from "src/models/Appeal";
import ButtonLink from "src/components/ButtonLink";
import ClaimDetail from "src/models/ClaimDetail";
import ClaimStatusSectionWrapper from "./ClaimStatusSectionWrapper";
import { DocumentContext } from "src/models/Document";
import Heading from "src/components/core/Heading";
import React from "react";

interface UploadDocsProps {
  appLogic: AppLogic;
  claimDetail: ClaimDetail;
}

export const canUploadDocsAndAppeals = (
  claimDetail: ClaimDetail,
  latestAppeal?: Appeal
) => {
  const hasOneAbsencePeriod = claimDetail.absence_periods.length === 1;
  const hasMultipleAbsencePeriods = claimDetail.absence_periods.length > 1;
  const claimInProgress = !claimDetail.hasFinalDecision;

  const latestAppealIsNotClosed = latestAppeal && !latestAppeal.isClosed;
  if (hasOneAbsencePeriod) {
    // Claim is in progress and does not have appeals
    if (claimInProgress && !latestAppeal) {
      return { showUploadSection: true, showAppealSection: false };
    }
    // Claim is in progress and has appeals
    if (claimInProgress && latestAppealIsNotClosed) {
      return { showUploadSection: false, showAppealSection: true };
    }
    // Claim has final decision
    if (claimDetail.hasFinalDecision) {
      return { showUploadSection: false, showAppealSection: true };
    }
  }
  if (hasMultipleAbsencePeriods) {
    const secondLeaveRequestCompleted =
      claimDetail.lastAbsencePeriod?.hasFinalDecision;
    // The second leave request is completed (Has final decision Approved, Cancelled, Denied, Withdrawn, Voided)
    if (secondLeaveRequestCompleted) {
      if (claimDetail.hasInReviewStatus || claimDetail.hasPendingStatus) {
        return { showUploadSection: true, showAppealSection: true };
      } else {
        return { showUploadSection: false, showAppealSection: true };
      }
    }
    // One of the leave request does not have a final decision
    if (!secondLeaveRequestCompleted) {
      return { showUploadSection: true, showAppealSection: true };
    }
  }
  // Fall back to showing both upload docs and appeals so we don't block claimants
  return { showUploadSection: true, showAppealSection: true };
};

/**
 * Provides the ability to respond to requests for information (RFI).
 */
const UploadDocs = ({ appLogic, claimDetail }: UploadDocsProps) => {
  const { t } = useTranslation();

  const uploadHref = appLogic.portalFlow.getRouteFor(
    "UPLOAD_DOC_OPTIONS",
    {},
    {
      absence_id: claimDetail.fineos_absence_id,
      is_additional_doc: "true",
      document_context: DocumentContext.statusGeneral,
    }
  );
  const versionedTextKey = "pages.claimsStatus.infoRequestsBody";

  return (
    <ClaimStatusSectionWrapper id="upload_documents">
      <div>
        <Heading level="2">
          {t("pages.claimsStatus.infoRequestsHeading")}
        </Heading>
      </div>
      <div className="margin-top-1">
        <Trans
          i18nKey={versionedTextKey}
          components={{
            ul: <ul className="usa-list" />,
            li: <li />,
            p: <p />,
          }}
        />
      </div>
      <ButtonLink className="margin-top-2" href={uploadHref}>
        {t("pages.claimsStatus.uploadDocumentsButton")}
      </ButtonLink>
    </ClaimStatusSectionWrapper>
  );
};

export default UploadDocs;
