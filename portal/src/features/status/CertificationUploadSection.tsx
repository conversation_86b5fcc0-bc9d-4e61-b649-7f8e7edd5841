import {
  ActionStatusEnum,
  ProgressDisplayItemProps,
} from "./ProgressDisplayItem";
import {
  BenefitsApplicationDocument,
  CertificationType,
  DocumentContext,
  getLeaveCertificationDocs,
  latestDocumentOfTypeUploadedAt,
} from "src/models/Document";
import {
  hasDocumentRequirementMissingItems,
  hasExtensionSubmittedViaPortal,
} from "./DocumentsSubmitted";

import ButtonLink from "src/components/ButtonLink";
import ChangeRequest from "src/models/ChangeRequest";
import ClaimDetail from "src/models/ClaimDetail";
import { ClaimStatusAccordionWrapper } from "./ClaimStatusSectionWrapper";
import { PortalFlow } from "src/hooks/usePortalFlow";
import ProgressDisplayList from "./ProgressDisplayList";
import React from "react";
import { Trans } from "react-i18next";
import formatDate from "src/utils/formatDate";
import { getCertificationStepLabel } from "./utils/getStepsWithStatuses";
import { isFeatureEnabled } from "src/services/featureFlags";
import { t } from "src/locales/i18n";

interface CertificationUploadSectionProps {
  getRouteFor: PortalFlow["getRouteFor"];
  claimDetail: ClaimDetail;
  documents: BenefitsApplicationDocument[];
  actionStatus: ActionStatusEnum;
}

const CertificationUploadSection = ({
  getRouteFor,
  claimDetail,
  documents,
  actionStatus,
}: CertificationUploadSectionProps) => {
  const uploadRoute = getRouteFor(
    "UPLOAD_DOC_OPTIONS",
    {},
    {
      absence_id: claimDetail.fineos_absence_id,
      is_additional_doc: "true",
      document_context: DocumentContext.statusCertSection,
    }
  );
  const latestUploadDate = getLatestUploadDateForExtension(
    claimDetail,
    documents
  );

  const leaveReasonKey = claimDetail.hasCaringLeavePeriod ? "care" : "medical";

  if (actionStatus === "inProgress") {
    return (
      <InProgressCertificationUploadSection
        uploadRoute={uploadRoute}
        latestUploadDate={latestUploadDate}
        leaveReasonKey={leaveReasonKey}
      />
    );
  }
  // TODO (PFMLPB-25310): Remove isFeatureEnabled() check when feature flag is removed
  else if (
    isFeatureEnabled("documentUploadOptional") &&
    actionStatus === "actionRequired"
  ) {
    return (
      <ActionRequiredCertificationUploadSection
        uploadRoute={uploadRoute}
        leaveReasonKey={leaveReasonKey}
      />
    );
  }
  // if (actionStatus === "completed"), but also serves as the default
  return (
    <CompletedCertificationUploadSection
      uploadRoute={uploadRoute}
      documents={documents}
    />
  );
};

const CompletedCertificationUploadSection = ({
  documents,
  uploadRoute,
}: {
  documents: BenefitsApplicationDocument[];
  uploadRoute: string;
}) => {
  const status = "completed";
  const boldLeaderText = getCertificationStepLabel(status);
  const leaveCertificationDocs = getLeaveCertificationDocs(documents);
  const displayItems = translateDocumentsToDisplayItems(leaveCertificationDocs);

  return (
    <ClaimStatusAccordionWrapper testId="completed-certification-upload">
      <ProgressDisplayList
        actionStatus={status}
        boldLeaderText={boldLeaderText}
        progressDisplayItems={displayItems}
      >
        <ButtonLink className="margin-top-2" href={uploadRoute}>
          Submit Certification
        </ButtonLink>
      </ProgressDisplayList>
    </ClaimStatusAccordionWrapper>
  );
};

const InProgressCertificationUploadSection = ({
  uploadRoute,
  latestUploadDate,
  leaveReasonKey,
}: {
  uploadRoute: string;
  latestUploadDate: string;
  leaveReasonKey: string;
}) => {
  const status = "inProgress";
  const boldLeaderText = getCertificationStepLabel(status);
  return (
    <ClaimStatusAccordionWrapper testId="inprogress-certification-upload">
      <ProgressDisplayList
        actionStatus={status}
        boldLeaderText={boldLeaderText}
      >
        <InProgressCertificationTextAndUploadButton
          uploadRoute={uploadRoute}
          latestUploadDate={latestUploadDate}
          leaveReasonKey={leaveReasonKey}
        />
      </ProgressDisplayList>
    </ClaimStatusAccordionWrapper>
  );
};

const ActionRequiredCertificationUploadSection = ({
  uploadRoute,
  leaveReasonKey,
}: {
  uploadRoute: string;
  leaveReasonKey: string;
}) => {
  const status = "actionRequired";
  const boldLeaderText = getCertificationStepLabel(status);
  return (
    <ClaimStatusAccordionWrapper testId="actionrequired-certification-upload">
      <ProgressDisplayList
        actionStatus={status}
        boldLeaderText={boldLeaderText}
      >
        <ActionRequiredCertificationTextAndUploadButton
          uploadRoute={uploadRoute}
          leaveReasonKey={leaveReasonKey}
        />
      </ProgressDisplayList>
    </ClaimStatusAccordionWrapper>
  );
};

export const getCertificationUploadActionStatus = (
  claimDetail: ClaimDetail,
  changeRequests: ChangeRequest[]
) => {
  const extensionViaPortal = hasExtensionSubmittedViaPortal(
    claimDetail,
    changeRequests
  );

  if (claimDetail.hasBondingLeavePeriod) {
    // For bonding leave, ask for proof of birth, never certification upload so consider this completed
    return "completed";
  }
  if (claimDetail.has_extensions) {
    if (extensionViaPortal) {
      // Always return "completed" when the extension was submitted through the portal
      // When an extension is submitted through the portal, the change request (AKA extension)
      // process -always- requires them to upload a new certification form so it's fair to assume that
      // for any extension submitted through the portal a new form is not required at this stage.
      return "completed";
    } else {
      // Always returns "inProgress" when the extension was submitted through the call center
      // When an extension is submitted not through the portal (AKA through the call center) the
      // certification section and step should always show as "inProgress" (blue loading symbol).
      // Even after they upload a document. This is because we don't think we can reliably figure
      // out when it's "completed" and it's safer to keep showing "inProgress".
      return "inProgress";
    }
  } else if (hasDocumentRequirementMissingItems(claimDetail)) {
    // No extension and a required document on original leave request is missing
    return "actionRequired";
  } else {
    // No extension and no missing documents on original leave request
    return "completed";
  }
};

const translateDocumentsToDisplayItems = (
  documents: BenefitsApplicationDocument[]
): ProgressDisplayItemProps[] => {
  const displayItems = documents.map(
    (document: BenefitsApplicationDocument) => {
      const text = (
        <Trans
          i18nKey={"pages.claimsStatus.certificationDisplayItem"}
          values={{ uploadDate: formatDate(document.created_at).full() }}
        />
      );
      const displayItem: ProgressDisplayItemProps = {
        actionStatus: "completed",
        text,
      };
      return displayItem;
    }
  );

  return displayItems;
};

const InProgressCertificationText = ({
  latestUploadDate,
  leaveReasonKey,
}: {
  latestUploadDate: string;
  leaveReasonKey: string;
}) => {
  return (
    <React.Fragment>
      <Trans
        i18nKey="pages.claimsStatus.certificationAccordionInProgressOne"
        context={leaveReasonKey}
      />
      <br />
      <br />
      <Trans
        i18nKey={"pages.claimsStatus.certificationAccordionInProgressTwo"}
        values={{
          latestUploadDate: formatDate(latestUploadDate).full(),
        }}
      />
    </React.Fragment>
  );
};

const InProgressCertificationTextAndUploadButton = ({
  uploadRoute,
  latestUploadDate,
  leaveReasonKey,
}: {
  uploadRoute: string;
  latestUploadDate: string;
  leaveReasonKey: string;
}) => {
  return (
    <React.Fragment>
      <InProgressCertificationText
        latestUploadDate={latestUploadDate}
        leaveReasonKey={leaveReasonKey}
      />
      <br />
      <ButtonLink className="margin-top-2" href={uploadRoute}>
        {t("pages.claimsStatus.certificationButton")}
      </ButtonLink>
    </React.Fragment>
  );
};

const ActionRequiredCertificationText = ({
  leaveReasonKey,
}: {
  leaveReasonKey: string;
}) => {
  return (
    <React.Fragment>
      <Trans
        i18nKey="pages.claimsStatus.certificationAccordionActionRequiredText"
        context={leaveReasonKey}
      />
    </React.Fragment>
  );
};

const ActionRequiredCertificationTextAndUploadButton = ({
  uploadRoute,
  leaveReasonKey,
}: {
  uploadRoute: string;
  leaveReasonKey: string;
}) => {
  return (
    <React.Fragment>
      <ActionRequiredCertificationText leaveReasonKey={leaveReasonKey} />
      <br />
      <ButtonLink className="margin-top-2" href={uploadRoute}>
        {t("pages.claimsStatus.certificationActionRequiredButton")}
      </ButtonLink>
    </React.Fragment>
  );
};

const getLatestUploadedDocumentRequirement = (
  claimDetail: ClaimDetail
): string => {
  const document_requirements = claimDetail.document_requirements;
  const uploadDates = document_requirements.map(
    (requirement) => requirement.upload_date
  );
  const sortedUploadDates = uploadDates.sort().reverse();
  const mostRecentUploadDate = sortedUploadDates[0];
  return mostRecentUploadDate || "";
};

const getLatestUploadDateForExtension = (
  claimDetail: ClaimDetail,
  documents: BenefitsApplicationDocument[]
) => {
  const extensionLeaveReason = claimDetail.getLeaveReasonForExtension;
  if (!extensionLeaveReason) {
    return getLatestUploadedDocumentRequirement(claimDetail);
  }

  const certificationDocType = CertificationType[extensionLeaveReason];
  const latestUploadDate = latestDocumentOfTypeUploadedAt(
    documents,
    certificationDocType
  );

  if (latestUploadDate) {
    return latestUploadDate;
  }

  return getLatestUploadedDocumentRequirement(claimDetail);
};

export const showCertificationUpload = (claimDetail: ClaimDetail) => {
  // do not show if claim is "bonding" leave or extension is "bonding" leave
  if (claimDetail.hasBondingLeavePeriod) {
    return false;
  }

  // show if no bonding extension AND the claim has missing document requirements
  if (hasDocumentRequirementMissingItems(claimDetail)) {
    return true;
  }

  // show if no bonding extension, no missing doc requirements on original claim, and non medtobonding extension
  const hasNonMedBondingExtension =
    claimDetail.hasPendingNonMedToBondingExtension;
  if (hasNonMedBondingExtension) {
    return true;
  }

  return false;
};

export default CertificationUploadSection;
