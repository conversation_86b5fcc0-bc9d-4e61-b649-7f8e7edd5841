import AlertBanner from "./AlertBanner";
import BackToApplicationListButton from "./BackToApplicationListButton";
import React from "react";
import { StatusPropsWithAppeal } from "src/pages/applications/status";
import StatusSideNavigation from "./StatusSideNavigation";
import { showPaymentsTab } from "src/pages/applications/status/payments";
import withChangeRequests from "src/hoc/withChangeRequests";
import withClaimDetail from "src/hoc/withClaimDetail";
import withPayment from "src/hoc/withPayment";

interface StatusNavigationAndAlertsWrapperProps
  extends React.PropsWithChildren,
    StatusPropsWithAppeal {}

const StatusNavigationAndAlertsWrapper = ({
  query,
  appLogic,
  payments,
  claim_detail,
  children,
  change_requests,
}: StatusNavigationAndAlertsWrapperProps) => {
  const { holidays, portalFlow } = appLogic;
  const { getRouteFor, pathname: currentPathName } = portalFlow;
  // Payments data
  const isPaymentsTab = showPaymentsTab(claim_detail, payments);
  // isPaymentsTab checks if a claim has an approved status or if a claim has received payments and has been used to determine whether or not to display any navigation tabs
  // we want to make that more clear by creating this new showNavigationTabs variable which could contain more complexity in the future
  const showNavigationTabs = isPaymentsTab;

  return (
    <React.Fragment>
      <BackToApplicationListButton />
      <AlertBanner
        claimDetail={claim_detail}
        query={query}
        holidays={holidays}
        changeRequests={change_requests}
      />
      <div className={isPaymentsTab ? "grid-row grid-gap" : "measure-6"}>
        {showNavigationTabs && (
          <StatusSideNavigation
            claimDetail={claim_detail}
            getRouteFor={getRouteFor}
            activePath={currentPathName}
          />
        )}
        <div className="measure-6">{children}</div>
      </div>
    </React.Fragment>
  );
};

export default withClaimDetail(
  withPayment(withChangeRequests(StatusNavigationAndAlertsWrapper))
);
