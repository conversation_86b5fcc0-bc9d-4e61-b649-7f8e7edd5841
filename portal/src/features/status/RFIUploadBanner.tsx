import {
  BenefitsApplicationDocument,
  ClaimDocument,
  DocumentContext,
  getLatestDocumentUploadDate,
} from "src/models/Document";
import { Trans, useTranslation } from "react-i18next";

import { ActionStatusEnum } from "./ProgressDisplayItem";
import ButtonLink from "src/components/ButtonLink";
import ClaimDetail from "src/models/ClaimDetail";
import DocumentList from "src/components/DocumentList";
import { PortalFlow } from "src/hooks/usePortalFlow";
import React from "react";
import UploadActionRequiredBanner from "./UploadActionRequiredBanner";
import { calculateRFIDueDate } from "./utils/calculateRFIDueDate";
import formatDate from "src/utils/formatDate";
import { getRFIStatus } from "./utils/getStepsWithStatuses";

interface RFIUploadBannerProps {
  getRouteFor: PortalFlow["getRouteFor"];
  claimDetail: ClaimDetail;
  rfiDocument: BenefitsApplicationDocument | ClaimDocument;
  documents: Array<BenefitsApplicationDocument | ClaimDocument>;
  downloadDocument: (
    document: BenefitsApplicationDocument
  ) => Promise<Blob | undefined>;
}

const RFIUploadBanner = ({
  getRouteFor,
  claimDetail,
  rfiDocument,
  documents,
  downloadDocument,
}: RFIUploadBannerProps) => {
  const { t } = useTranslation();

  const uploadHref = getRouteFor(
    "UPLOAD_DOC_OPTIONS",
    {},
    {
      absence_id: claimDetail.fineos_absence_id,
      is_additional_doc: "true",
      document_context: DocumentContext.statusRfiSection,
    }
  );

  const actionStatus = getRFIStatus(documents, claimDetail);
  const rfiReceivedDate = rfiDocument.created_at;
  const rfiDueDate = calculateRFIDueDate(rfiDocument);
  const latestDocumentUploadDate = getLatestDocumentUploadDate(documents);

  const HeaderText = <RFIHeaderText actionStatus={actionStatus} />;
  return (
    <UploadActionRequiredBanner
      actionStatus={actionStatus}
      headerText={HeaderText}
    >
      <React.Fragment>
        {actionStatus === "actionRequired" && (
          <ActionRequiredParagraph
            rfiReceivedDate={rfiReceivedDate}
            rfiDueDate={rfiDueDate}
          />
        )}
        {actionStatus === "inProgress" && (
          <InProgressParagraph
            latestDocumentUploadDate={latestDocumentUploadDate}
            rfiDueDate={rfiDueDate}
          />
        )}
        <div className="margin-top-2">
          <DocumentList
            documents={[rfiDocument]}
            downloadBenefitsApplicationDocument={downloadDocument}
            showCreatedAt
          />
        </div>

        <ButtonLink className="margin-top-3" href={uploadHref}>
          {t("pages.claimsStatus.uploadDocumentsButton")}
        </ButtonLink>
      </React.Fragment>
    </UploadActionRequiredBanner>
  );
};

const RFIHeaderText = ({
  actionStatus,
}: {
  actionStatus: ActionStatusEnum;
}) => (
  <Trans
    i18nKey={"pages.claimsStatus.rfiBannerHeader"}
    context={actionStatus}
  />
);

const InProgressParagraph = ({
  latestDocumentUploadDate,
  rfiDueDate,
}: {
  latestDocumentUploadDate: string;
  rfiDueDate: string;
}) => {
  return (
    <React.Fragment>
      <div className="margin-bottom-2">
        <Trans
          i18nKey={`pages.claimsStatus.rfiBannerInProgressParagraphOne`}
          values={{
            latestDocumentUploadDate: formatDate(
              latestDocumentUploadDate
            ).full(),
          }}
        />
      </div>
      <div>
        <Trans
          i18nKey={`pages.claimsStatus.rfiBannerInProgressParagraphTwo`}
          values={{
            rfiDueDate: formatDate(rfiDueDate).full(),
          }}
        />
      </div>
    </React.Fragment>
  );
};

const ActionRequiredParagraph = ({
  rfiReceivedDate,
  rfiDueDate,
}: {
  rfiReceivedDate: string;
  rfiDueDate: string;
}) => {
  return (
    <div>
      <Trans
        i18nKey={`pages.claimsStatus.rfiBannerActionRequiredParagraph`}
        values={{
          rfiReceivedDate: formatDate(rfiReceivedDate).full(),
          rfiDueDate: formatDate(rfiDueDate).full(),
        }}
      />
    </div>
  );
};

export default RFIUploadBanner;
