import React, { useState } from "react";

import Alert from "./core/Alert";
import Icon from "./core/Icon";
import { PagePropsBase } from "types/common";
import Spinner from "./core/Spinner";
import { t } from "i18next";

interface EmployerDownloadCSVProps extends PagePropsBase {
  /** The name of the file to be downloaded. */
  fileName?: string;
  /** The i18n key for the download link text. */
  labelKey?: string;
  /** The i18n key for the downloading csv on secure computers text. */
  secureDownloadLabel?: string;
  /** The i18n key for the download csv timelimit text. */
  timelimitLabel?: string;
  /** The i18n key for the download csv failed text. */
  csvDownloadFailedLabel?: string;
  /** The total number of applications shown in applications table (for logging purposes) */
  totalApplications?: number;
}

const EmployerDownloadCSV: React.FC<EmployerDownloadCSVProps> = ({
  fileName = "leave_admin_applications.csv",
  labelKey = "pages.employersApplications.csvLinkTitle",
  secureDownloadLabel = "pages.employersApplications.csvDownloadSecureDownload",
  timelimitLabel = "pages.employersApplications.csvDownloadTimelimit",
  csvDownloadFailedLabel = "pages.employersApplications.csvDownloadFailed",
  appLogic,
  totalApplications,
}) => {
  const { isDownloadingCSV, downloadCSV } = appLogic.leaveAdmins;
  const [downloadError, setDownloadError] = useState(false);

  const initiateDownload = async () => {
    setDownloadError(false);
    appLogic.clearErrors();

    try {
      const success = await downloadCSV(fileName, totalApplications);
      setDownloadError(!success);
    } catch {
      setDownloadError(true);
    }
  };

  // Main click handler
  const handleDownload = async () => {
    await initiateDownload();
  };

  // Keyboard handler that defers to the common logic
  const handleKeyDown = async (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      await initiateDownload();
    }
  };
  const getDownloadCSVFailedBanner = () => (
    <div role="alert" aria-live="polite" aria-label="Error">
      <Alert state="error" className="c-alert--auto-width" slim>
        {t(csvDownloadFailedLabel)}
      </Alert>
    </div>
  );

  const getDownloadButton = () => (
    <div className="display-flex flex-align-center margin-y-1">
      <button
        onClick={handleDownload}
        onKeyDown={handleKeyDown}
        className={`usa-button--unstyled display-flex flex-align-center ${
          isDownloadingCSV ? "text-primary-darker" : "cursor-pointer"
        }`}
        disabled={isDownloadingCSV}
        type="button"
      >
        <span className="margin-right-05 display-inline-flex flex-align-center">
          {isDownloadingCSV ? (
            <Spinner small={true} aria-label="Downloading CSV" />
          ) : (
            <Icon size={3} name="file_download" />
          )}
        </span>
        <span className="text-middle text-bold">{t(labelKey)}</span>
      </button>
      <br />
    </div>
  );

  const getDisclaimerText = () => (
    <div className="display-flex flex-align-center">
      {t(secureDownloadLabel)}
      <br />
      {t(timelimitLabel)}
    </div>
  );

  return (
    <div className="margin-y-4">
      {downloadError && getDownloadCSVFailedBanner()}
      {getDownloadButton()}
      {getDisclaimerText()}
    </div>
  );
};

export default EmployerDownloadCSV;
