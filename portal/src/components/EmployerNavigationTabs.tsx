import React, { useEffect, useState } from "react";
import NavigationTabs from "./NavigationTabs";
import { isFeatureEnabled } from "src/services/featureFlags";
import routes from "src/routes";
import { useTranslation } from "src/locales/i18n";

interface EmployerNavigationTabsProps {
  activePath: string;
  hasEmployerExemptionRequests: boolean;
}

const EmployerNavigationTabs = ({
  activePath,
  hasEmployerExemptionRequests,
}: EmployerNavigationTabsProps) => {
  const { t } = useTranslation();
  const defaultTabList = [
    {
      label: t("components.employersNavigationTabs.welcome"),
      href: routes.employers.welcome,
    },
    {
      label: t("components.employersNavigationTabs.applications"),
      href: routes.employers.applications,
    },
    {
      label: t("components.employersNavigationTabs.organizations"),
      href: routes.employers.organizations,
    },
  ];
  const [tabs, setTabs] = useState(defaultTabList);

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  useEffect(() => {
    const EXEMPTIONS_TAB_INDEX = 2;
    const exemptionsTab = {
      label: t("components.employersNavigationTabs.exemptions"),
      href: routes.employers.employerExemptions.exemptionsTab,
    };

    if (isEnableEmployerExemptionsPortal && hasEmployerExemptionRequests) {
      setTabs((tabs) => [
        ...tabs.slice(0, EXEMPTIONS_TAB_INDEX),
        exemptionsTab,
        ...tabs.slice(EXEMPTIONS_TAB_INDEX),
      ]);
    }
  }, [t, isEnableEmployerExemptionsPortal, hasEmployerExemptionRequests]);

  return (
    <NavigationTabs
      aria-label={t("components.employersNavigationTabs.label")}
      tabs={tabs}
      activePath={activePath}
    />
  );
};

export default EmployerNavigationTabs;
