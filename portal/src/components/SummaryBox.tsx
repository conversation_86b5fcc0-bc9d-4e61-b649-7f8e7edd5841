import Heading from "./core/Heading";
import React from "react";

export interface SummaryBoxProps {
  /**
   * Text to display as title of box. Should be pre-translated.
   */
  header?: string;
  /**
   * Paragraph style text to be displayed between optional header and list. Should be
   * pre-translated.
   */
  body?: string;
  /**
   * Array of elements to be displayed in an unordered list. Elements should be
   * pre-translated with any text formatting already applied.
   */
  list?: React.ReactNode[] | React.ReactElement;
}

/**
 * A summary box that highlights key information.
 * [USWDS Reference ↗](https://designsystem.digital.gov/components/summary-box/)
 */
export default function SummaryBox(props: SummaryBoxProps) {
  return (
    <div className="usa-summary-box margin-bottom-2" role="complementary">
      <div className="usa-summary-box__body grid-row grid-gap">
        <div className="grid-col">
          {props.header && (
            <Heading className="usa-summary-box__heading" level="3" size="4">
              {props.header}
            </Heading>
          )}

          {props.body && (
            <div className="usa-summary-box__text">{props.body}</div>
          )}

          {props.list &&
            Array.isArray(props.list) &&
            props.list?.length > 0 && (
              <div className="usa-summary-box__text">
                <ul className="usa-list">
                  {props.list.map((listItem, index) => (
                    <React.Fragment key={index}>{listItem}</React.Fragment>
                  ))}
                </ul>
              </div>
            )}
          {props.list && React.isValidElement(props.list) && (
            <div className="usa-summary-box__text">{props.list}</div>
          )}
        </div>
      </div>
    </div>
  );
}
