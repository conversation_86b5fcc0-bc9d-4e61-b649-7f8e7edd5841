import ContentSecurityPolicy from "./ContentSecurityPolicy";
import React from "react";
import { isFeatureEnabled } from "src/services/featureFlags";
import localize from "src/services/localize";

/**
 * Global <head> tags. This is a component to better support unit testing, since
 * we had challenges directly testing _document.js, where this gets rendered.
 */
function GlobalDocumentHead() {
  const localizeVersions = localize.getVersions();
  const LOCALIZE_VERSION = "470";
  return (
    <React.Fragment>
      {isFeatureEnabled("enableContentSecurityPolicy") &&
        process.env.BUILD_ENV !== "local-api" && <ContentSecurityPolicy />}
      {/* New Relic script must be towards the top of the <head> and before our other scripts */}
      <script src="/new-relic.js?version=1.291.1" />
      {process.env.gtmConfigId && (
        <script
          src={`https://www.googletagmanager.com/gtag/js?id=${process.env.gtmConfigId}`}
        />
      )}
      {process.env.gtmConfigId && (
        <script
          dangerouslySetInnerHTML={{
            __html: `window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', '${process.env.gtmConfigId}');`,
          }}
        />
      )}
      <script
        src={`https://global.localizecdn.com/localize.${process.env.localizeVersion}.js`}
        integrity={`${
          localizeVersions[process.env.localizeVersion || LOCALIZE_VERSION]
        }`}
        crossOrigin="anonymous"
      ></script>
      <script
        dangerouslySetInnerHTML={{
          __html:
            "!function(a){if(!a.Localize){a.Localize={};for(var e=['translate','untranslate','phrase','initialize','translatePage','setLanguage','getLanguage','getSourceLanguage','detectLanguage','getAvailableLanguages','untranslatePage','bootstrap','prefetch','on','off','hideWidget','showWidget'],t=0;t<e.length;t++)a.Localize[e[t]]=function(){}}}(window);",
        }}
      />
      <script
        dangerouslySetInnerHTML={{
          __html: `
            Localize.initialize({
              key: 'febB1sLj2bUbc',
              rememberLanguage: true,
              autoApprove: true,
              disableWidget: true,
              blockedClasses: ['pretranslated-multilanguage-content']
            });
            `,
        }}
      />
      <script src="/cxone.js" />
      <link href="/favicon.png" rel="shortcut icon" type="image/png" />
      <meta httpEquiv="content-language" content="en" />
      {process.env.BUILD_ENV !== "prod" && (
        <meta name="robots" content="noindex" />
      )}
    </React.Fragment>
  );
}

export default GlobalDocumentHead;
