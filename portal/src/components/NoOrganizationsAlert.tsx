import { Trans, useTranslation } from "react-i18next";

import Alert from "./core/Alert";
import ExternalLink from "./core/ExternalLink";
import React from "react";
import { isFeatureEnabled } from "src/services/featureFlags";
import routes from "src/routes";

/**
 * Alert component displaying that no organizations are associated with this LA account
 */
function NoOrganizationsAlert() {
  const { t } = useTranslation();

  // TODO (PFMLPB-19806): removal of feature flag
  const isEnableEmployerExemptionsPortal = isFeatureEnabled(
    "enableEmployerExemptionsPortal"
  );

  return (
    <Alert
      className="margin-bottom-3"
      heading={t("components.noOrganizationsAlert.heading")}
      state="warning"
    >
      <p>
        {isEnableEmployerExemptionsPortal
          ? t("components.noOrganizationsAlert.gainAccess_v2")
          : t("components.noOrganizationsAlert.gainAccess_v1")}
      </p>
      <p>
        <Trans
          i18nKey="components.noOrganizationsAlert.noLeaveAdministrators"
          components={{
            "mass-tax-connect-link": (
              <ExternalLink href={routes.external.massTaxConnect} />
            ),
          }}
        />
      </p>
      <p>
        <Trans
          i18nKey="components.noOrganizationsAlert.learnMore"
          components={{
            "add-organization-link": (
              <ExternalLink href={routes.external.massgov.verifyEmployer} />
            ),
          }}
        />
      </p>
    </Alert>
  );
}

export default NoOrganizationsAlert;
