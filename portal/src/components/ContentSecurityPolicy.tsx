import React from "react";

const ContentSecurityPolicy = () => {
  // Google Tag Manager
  const googleTagManagerScriptSrcDomains = [
    "https://www.googletagmanager.com/",
    "https://www.google-analytics.com/",
  ].join(" ");

  const googleTagManagerScriptSrcHashes = [
    "'sha256-ukTTiJb/oQCrLr5MF+zwKcvseKiHZVRHRLFTOtg6WZY='", // base script
    "'sha256-q6z21+tdDjVyCj+tqSrxZ2QQPmeVyrMnICOEjUlXpYo='", // test
    "'sha256-kWyrXqrKqjGK00ky2z5jVGDD7I3Mh/qmqUl8Kz5Gkyc='", // prod
  ].join(" ");

  const googleTagManagerImgSrcDomains = [
    "https://www.google-analytics.com/",
  ].join(" ");

  // Localize
  const localizeScriptSrcDomains = ["https://global.localizecdn.com/"].join(
    " "
  );

  const localizeScriptSrcHashes = [
    "'unsafe-hashes'",
    "'sha256-v69/hW8Vxb1BRkwbZ6wEhUIwmOfDMzX0GiW78XsOM0U='", // First inline script
    "'sha256-n03mj2Lgj1Li9HUFFxdPTWpCY2UWTIPJXCyuRf2I45A='", // Second inline script
    "'sha256-iR+KGuGKVzAdP3XMe6hUfhT2jwaQOiS1AP4D1xPD89k='", // NEW
  ].join(" ");

  const localizeStyleSrcHashes = [
    "'unsafe-hashes'",
    "'sha256-KyJnQCDKQnVtSK6aMP9DpWhTZOwEIRNtR4Kgh+eMuDg='", // Second inline script
    "'sha256-vmFtKexlNr5vlNrPlg8deiL5F5A+eAt4SF3obSNvShw='", // Second inline script
    "'sha256-cDCp1LiW3BsB9G6aRravG06dByeDF9sgtd6/75Mgv4o='", // Second inline script
    "'sha256-AAPCiaGDPYIfZRhBAI/t57nsrI/varngbytbqX7gm4s='", // Second inline script
    "'sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU='", // Second inline script
    "'sha256-o09GkbcrSiF8ilR8tPit4HHb/Jy7MoeHadVooyDPDmU='", // Second inline script
    "'sha256-rbZ1kAn6k+/qJte6oJ/sIgCxTvpiiFAkJEDORNHNUxw='", // Second inline script
    "'sha256-g498o0X6JwR1ZQZ797F6emjmL3DmVlWJkUQJYumxZ7E='", // Second inline script
    "'sha256-TZBxLxGaS2yNr6F3Qe986jZXxy2nX7ciYuHvpr4NSJ8='", // Second inline script
    "'sha256-on+o1IUaY45/Xt7B1d6YxXizEU3Ssj/Z3GdbvChDrug='", // NEW
  ].join(" ");

  // CXone
  const cxoneScriptSrcDomains = ["https://*.nicecxone-gov.com"].join(" ");

  const cxoneScriptSrcHashes = [
    "'sha256-2T1djEtqQnHU1XR30PP2KCzjRiF4xJ8PyCxS0mvkvU0='",
    "'sha256-zoS8IQUkuZXujcOWb2a7Q+4EPd167ruP55CbfpUhnbA='",
  ].join(" ");
  const cxoneStyleSrcDomains = ["https://*.nicecxone-gov.com"].join(" ");

  const cxoneStyleSrcHashes = [
    "'sha256-lpCyGiCVOchx1h2NRCca/uAIpNCiIdMAyE3v2qSTksE='",
    "'sha256-JOe1TZjCR36sEuDZ3Xnq962d5Qi8DaQFKwD7wJBAldY='",
    "'sha256-TXrPTt5H4Upq9WuiPkKyBSVnNfjBgEntZ6wgQi7NruE='",
  ].join(" ");

  const cxoneImageSrcDomains = [
    "https://*.nicecxone-gov.com",
    "https://cv-de-web-modules.s3.amazonaws.com",
    "https://s3.amazonaws.com/cv-de-email-attachments/",
    "https://unpkg.com",
  ].join(" ");

  // New Relic
  const newRelicScriptSrcDomains = [
    "https://js-agent.newrelic.com/",
    "https://bam.nr-data.net/",
  ].join(" ");

  const newRelicScriptSrcHashes = [
    "'sha256-kPAkTfsQFvh377gvPDZ78zq1hJUe1jO6PIlfTwTE13Y='",
  ].join(" ");

  const newRelicStyleSrcHashes = [
    "'sha256-kPAkTfsQFvh377gvPDZ78zq1hJUe1jO6PIlfTwTE13Y='",
    "'sha256-PW16PFJVsmnH4Rya4FHBjeKOzpeiM93LZcxDHGY6RGI='",
  ].join(" ");

  // Default
  const defaultSrcDomains = ["wss://chat-gw-de-na2.nicecxone-gov.com"].join(
    " "
  );

  const defaultSrcDirective = `default-src 'self' https: ${defaultSrcDomains};`;
  const scriptSrcDirective = `script-src 'self' ${googleTagManagerScriptSrcDomains} ${googleTagManagerScriptSrcHashes} ${newRelicScriptSrcDomains} ${newRelicScriptSrcHashes} ${localizeScriptSrcDomains} ${localizeScriptSrcHashes} ${cxoneScriptSrcDomains} ${cxoneScriptSrcHashes};`;
  const baseUriDirective = "base-uri 'none';";
  const formActionDirective = "form-action 'none';";
  const styleSrcDirective = `style-src 'self' ${localizeScriptSrcDomains} ${localizeStyleSrcHashes} ${cxoneStyleSrcDomains} ${cxoneStyleSrcHashes} ${newRelicStyleSrcHashes};`;
  const imgSrcDirective = `img-src 'self' ${localizeScriptSrcDomains} ${cxoneImageSrcDomains} ${googleTagManagerImgSrcDomains} blob:;`;
  const fontSrcDirective = "font-src 'self' data: https:;";

  return (
    <meta
      httpEquiv="Content-Security-Policy"
      content={`${defaultSrcDirective} ${scriptSrcDirective} ${baseUriDirective} ${formActionDirective} ${styleSrcDirective} ${imgSrcDirective} ${fontSrcDirective}`}
    ></meta>
  );
};

export default ContentSecurityPolicy;
