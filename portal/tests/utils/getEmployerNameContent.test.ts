import { getEmployerNameContent } from "src/utils/getEmployerNameContent";

describe("getEmployerNameContent", () => {
  const DEFAULT_STRING = "pages.page.heading";
  const VALID_DBA = "Acme Corp";
  const INVALID_DBA = "EMPLOYER NOT FOUND";
  const VALID_FEIN = "14-7681389";
  const INVALID_FEIN = "000000000";

  it("returns the content with _dba suffix when DBA and FEIN are valid", () => {
    expect(getEmployerNameContent(VALID_DBA, VALID_FEIN, DEFAULT_STRING)).toBe(
      `${DEFAULT_STRING}_dba`
    );
  });

  it("returns the content with _ein suffix when only FEIN is valid", () => {
    expect(
      getEmployerNameContent(INVALID_DBA, VALID_FEIN, DEFAULT_STRING)
    ).toBe(`${DEFAULT_STRING}_ein`);
  });

  it("returns the content with _ein suffix when DBA is null and FEIN is valid", () => {
    expect(getEmployerNameContent(null, VALID_FEIN, DEFAULT_STRING)).toBe(
      `${DEFAULT_STRING}_ein`
    );
  });

  it("returns the original content when FEIN is invalid", () => {
    expect(
      getEmployerNameContent(VALID_DBA, INVALID_FEIN, DEFAULT_STRING)
    ).toBe(DEFAULT_STRING);
  });

  it("returns the original content when both DBA and FEIN are invalid", () => {
    expect(
      getEmployerNameContent(INVALID_DBA, INVALID_FEIN, DEFAULT_STRING)
    ).toBe(DEFAULT_STRING);
  });

  it("returns the original content when both DBA and FEIN are null", () => {
    expect(getEmployerNameContent(null, null, DEFAULT_STRING)).toBe(
      DEFAULT_STRING
    );
  });

  it("handles case insensitivity for DBA validation", () => {
    expect(
      getEmployerNameContent(
        INVALID_DBA.toLowerCase(),
        VALID_FEIN,
        DEFAULT_STRING
      )
    ).toBe(`${DEFAULT_STRING}_ein`);
  });

  it("handles '-' for FEIN validation", () => {
    expect(
      getEmployerNameContent(VALID_DBA, "00-0000000", DEFAULT_STRING)
    ).toBe(`${DEFAULT_STRING}`);
  });
});
