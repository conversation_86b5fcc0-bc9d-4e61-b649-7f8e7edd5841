import { render, screen } from "@testing-library/react";

import { DocumentRequirements } from "src/models/ClaimDetail";
import { DocumentType } from "src/models/Document";
import ProofOfBondingUploadBanner from "src/features/status/ProofOfBondingUploadBanner";
import React from "react";
import createMockClaimDetail from "lib/mock-helpers/createMockClaimDetail";

describe("ProofOfBondingUploadBanner", () => {
  it("shows action required text for bonding leave when the proof of bonding hasn't been uploaded", async () => {
    const proofOfBondingRequirement = {
      document_requirement_category: "certification",
      allowed_document_types: [DocumentType.certification["Child Bonding"]],
      fineos_document_type: null,
      pfml_document_type: null,
      upload_date: null,
    } as DocumentRequirements;
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Bonding (adoption)",
      documentRequirements: [proofOfBondingRequirement],
    });
    const getRouteFor = jest.fn().mockReturnValue("/mocked-router");

    render(
      <ProofOfBondingUploadBanner
        getRouteFor={getRouteFor}
        claimDetail={claimDetail}
        actionStatus={
          !proofOfBondingRequirement.upload_date
            ? "actionRequired"
            : "completed"
        }
      />
    );

    const proofOfAdoptionElements = await screen.findAllByText(
      "Submit proof of adoption"
    );
    expect(proofOfAdoptionElements).toHaveLength(2);
    expect(screen.getByTestId("UploadBanner")).toMatchSnapshot();
  });
});
