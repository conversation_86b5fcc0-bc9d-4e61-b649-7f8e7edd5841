import { CertificationType, DocumentType } from "src/models/Document";
import {
  getAllStepStatuses,
  getRFIStatus,
  getStepsWithStatuses,
} from "src/features/status/utils/getStepsWithStatuses";

import { DocumentRequirements } from "src/models/ClaimDetail";
import LeaveReason from "src/models/LeaveReason";
import { ManagedRequirement } from "src/models/ManagedRequirement";
import { StepSegmentProps } from "src/components/core/StepIndicator";
import { createAbsencePeriod } from "tests/test-utils";
import { createMockAppealDocument } from "lib/mock-helpers/createMockDocument";
import createMockClaimDetail from "lib/mock-helpers/createMockClaimDetail";
import { createMockManagedRequirement } from "lib/mock-helpers/createMockManagedRequirement";
import dayjs from "dayjs";
import { generateNotice } from "pfml-storybook/utils/generateNotice";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

describe("getStepsWithStatuses", () => {
  // Step order tests
  it("gets the correct step order for a bonding leave claim", () => {
    // Create the mock claimDetail
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Bonding (newborn)",
    });
    // Get the step order
    const stepOrder = getStepsWithStatuses("Child Bonding", claimDetail).map(
      (step) => step.label
    );
    // To get these dynamically is messy, so hard code the expected steps
    const expectedStepOrder = [
      "Request for leave submitted",
      "Submit proof of birth",
      "Employer response",
      "DFML review",
      "Decision",
    ];
    expect(stepOrder).toEqual(expectedStepOrder);
  });
  it("gets the correct step order for a non-bonding leave type", () => {
    // Create the mock claimDetail
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Medical (illness)",
    });
    // Get the step order
    const stepOrder = getStepsWithStatuses(
      "Serious Health Condition - Employee",
      claimDetail
    ).map((step) => step.label);
    // To get these dynamically is messy, so hard code the expected steps
    const expectedStepOrder = [
      "Documents submitted",
      "Employer response",
      "DFML review",
      "Decision",
    ];
    expect(stepOrder).toEqual(expectedStepOrder);
  });
  it("gets the correct step order for a med to bonding leave claim", () => {
    // Create the mock claimDetail
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Medical (approved pregnancy and pending bonding)",
      has_extensions: true,
    });
    // Get the step order
    const stepOrder = getStepsWithStatuses("Child Bonding", claimDetail).map(
      (step) => step.label
    );
    // To get these dynamically is messy, so hard code the expected steps
    const expectedStepOrder = [
      "Request for leave submitted",
      "Submit proof of birth",
      "Employer response",
      "DFML review",
      "Decision",
    ];
    expect(stepOrder).toEqual(expectedStepOrder);
  });

  it("gets the correct step order for an extended medical claim", () => {
    // Create the mock claimDetail
    const claimDetail = createMockClaimDetail({
      absencePeriods: [
        createAbsencePeriod({
          period_type: "Continuous",
          absence_period_start_date: "2022-01-01",
          absence_period_end_date: "2021-01-30",
          reason: LeaveReason.medical,
          request_decision: "Approved",
        }),
        createAbsencePeriod({
          period_type: "Continuous",
          absence_period_start_date: "2022-02-01",
          absence_period_end_date: "2021-03-30",
          reason: LeaveReason.medical,
          request_decision: "Pending",
        }),
      ],
      has_extensions: true,
    });
    // Get the step order
    const stepOrder = getStepsWithStatuses(
      LeaveReason.medical,
      claimDetail
    ).map((step) => step.label);
    // To get these dynamically is messy, so hard code the expected steps
    const expectedStepOrder = [
      "Extension request submitted",
      "Submit Certification",
      "Employer response",
      "DFML review",
      "Decision",
    ];
    expect(stepOrder).toEqual(expectedStepOrder);
  });

  it("gets the correct step order for an extended bonding claim", () => {
    // Create the mock claimDetail
    const claimDetail = createMockClaimDetail({
      absencePeriods: [
        createAbsencePeriod({
          period_type: "Continuous",
          absence_period_start_date: "2022-01-01",
          absence_period_end_date: "2021-01-30",
          reason: LeaveReason.bonding,
          request_decision: "Approved",
        }),
        createAbsencePeriod({
          period_type: "Continuous",
          absence_period_start_date: "2022-02-01",
          absence_period_end_date: "2021-03-30",
          reason: LeaveReason.bonding,
          request_decision: "Pending",
        }),
      ],
      has_extensions: true,
    });
    // Get the step order
    const stepOrder = getStepsWithStatuses(
      "Military Exigency Family",
      claimDetail
    ).map((step) => step.label);
    // To get these dynamically is messy, so hard code the expected steps
    const expectedStepOrder = [
      "Extension request submitted",
      "Employer response",
      "DFML review",
      "Decision",
    ];
    expect(stepOrder).toEqual(expectedStepOrder);
  });
  it("gets the correct step order for a medical leave type with deferred cert doc", () => {
    setFeatureFlags({
      documentUploadOptional: true,
    });
    // Create the mock claimDetail
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Medical (illness)",
      documentRequirements: [
        {
          document_requirement_category: "identification",
          allowed_document_types: [DocumentType.identityVerification],
          fineos_document_type: DocumentType.identityVerification,
          pfml_document_type: null,
          upload_date: "2020-11-11",
        },
        {
          document_requirement_category: "certification",
          allowed_document_types: [
            CertificationType["Serious Health Condition - Employee"],
            CertificationType.healthcareProviderForm,
          ],
          fineos_document_type: null,
          pfml_document_type: null,
          upload_date: null,
        },
      ],
    });
    // Get the step order
    const stepOrder = getStepsWithStatuses(
      "Serious Health Condition - Employee",
      claimDetail
    ).map((step) => step.label);
    // To get these dynamically is messy, so hard code the expected steps
    const expectedStepOrder = [
      "Request submitted",
      "Upload Certification",
      "Employer response",
      "DFML review",
      "Decision",
    ];
    expect(stepOrder).toEqual(expectedStepOrder);
  });
  // Employer Response tests
  it("shows a completed employer response step when the employer response is complete", () => {
    // Create the mock claimDetail
    const managedRequirement: ManagedRequirement = createMockManagedRequirement(
      {
        follow_up_date: dayjs().add(1, "day").format("YYYY-MM-DD"),
        responded_at: dayjs().format("YYYY-MM-DD"),
      }
    );

    const claimDetail = createMockClaimDetail({
      leaveScenario: "Medical (illness)",
      requestDecision: "In Review",
      managedRequirements: managedRequirement,
    });
    const steps = getStepsWithStatuses(
      "Serious Health Condition - Employee",
      claimDetail
    );

    const employerReviewStep = steps.find(
      (step) => step.label === "Employer response"
    );
    expect(employerReviewStep?.status).toEqual("completed");
  });
  it("shows an inProgress employer response step when claim has no decision and the employer response is not completed", () => {
    // Create the mock claimDetail
    const managedRequirement: ManagedRequirement = createMockManagedRequirement(
      {
        follow_up_date: dayjs().add(1, "day").format("YYYY-MM-DD"),
        responded_at: null,
      }
    );
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Medical (illness)",
      requestDecision: "In Review",
      managedRequirements: managedRequirement,
    });
    const steps = getStepsWithStatuses(
      "Serious Health Condition - Employee",
      claimDetail
    );

    const employerReviewStep = steps.find(
      (step) => step.label === "Employer response"
    );
    expect(employerReviewStep?.status).toEqual("inProgress");
  });

  // rfiResponse tests
  it("includes the rfi step when there is an RFI", () => {
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Medical (illness)",
      requestDecision: "Pending",
    });
    const rfiDocumentCreatedDate = "2023-01-15";
    const rfiDocument = generateNotice(
      "requestForInfoNotice",
      rfiDocumentCreatedDate
    );
    const documents = [rfiDocument];

    const steps = getStepsWithStatuses(
      "Serious Health Condition - Employee",
      claimDetail,
      rfiDocument,
      documents
    );
    const rfiResponseStep = steps.find(
      (step) => step.label === "Request for More Information"
    );
    expect(rfiResponseStep).toBeTruthy();
  });

  it("doesn't include the rfi step when there is no RFI", () => {
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Medical (illness)",
      requestDecision: "Pending",
    });
    // Including these to make the test clearer, leaving them off the argument list does the same thing.
    const rfiDocument = undefined;
    const documents = undefined;
    const steps = getStepsWithStatuses(
      "Serious Health Condition - Employee",
      claimDetail,
      rfiDocument,
      documents
    );

    const rfiResponseStep = steps.find(
      (step) => step.label === "Request for more information"
    );
    expect(rfiResponseStep).toBeFalsy();
  });

  // dfmlReview tests
  it("should have a dfmlReview status of inProgress when the employer has reviewed, but there's no applicationDecision", () => {
    const managedRequirement: ManagedRequirement = createMockManagedRequirement(
      {
        follow_up_date: dayjs().add(1, "day").format("YYYY-MM-DD"),
        responded_at: dayjs().format("YYYY-MM-DD"),
      }
    );
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Medical (illness)",
      requestDecision: "In Review",
      managedRequirements: managedRequirement,
    });
    const steps = getStepsWithStatuses(
      "Serious Health Condition - Employee",
      claimDetail
    );

    const departmentReviewStep = steps.find(
      (step) => step.label === "DFML review"
    );
    expect(departmentReviewStep?.status).toEqual("inProgress");
  });
  it("should have a dfmlReview status of inProgress when the employer has not reviewed and there's no applicationDecision", () => {
    const managedRequirement: ManagedRequirement = createMockManagedRequirement(
      {
        follow_up_date: dayjs().add(1, "day").format("YYYY-MM-DD"),
        responded_at: null,
      }
    );
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Medical (illness)",
      requestDecision: "In Review",
      managedRequirements: managedRequirement,
    });
    const steps = getStepsWithStatuses(
      "Serious Health Condition - Employee",
      claimDetail
    );

    const departmentReviewStep = steps.find(
      (step) => step.label === "DFML review"
    );
    expect(departmentReviewStep?.status).toEqual("inProgress");
  });

  // submitProofOfBonding tests
  it("should show actionRequired status for submitProofOfBonding when a proof of birth upload is required", () => {
    const proofOfBirthUploadNeededDocumentRequirements = [
      {
        document_requirement_category: "certification",
        allowed_document_types: [CertificationType[LeaveReason.bonding]],
        fineos_document_type: null,
        pfml_document_type: null,
        upload_date: null,
      } as DocumentRequirements,
    ];
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Bonding (newborn)",
      requestDecision: "In Review",
      documentRequirements: proofOfBirthUploadNeededDocumentRequirements,
    });
    const steps = getStepsWithStatuses("Child Bonding", claimDetail);

    const uploadProofOfBirthStep = steps.find(
      (step) => step.label === "Submit proof of birth"
    );
    expect(uploadProofOfBirthStep?.status).toEqual("actionRequired");
  });
  it("should show completed status for submitProofOfBonding when a proof of birth upload is required", () => {
    const proofOfBirthUploadSatisfiedDocumentRequirements = [
      {
        document_requirement_category: "certification",
        allowed_document_types: [CertificationType[LeaveReason.bonding]],
        fineos_document_type: CertificationType[LeaveReason.bonding],
        pfml_document_type: null,
        upload_date: dayjs().format("YYYY-MM-DD"),
      } as DocumentRequirements,
    ];
    const claimDetail = createMockClaimDetail({
      leaveScenario: "Bonding (newborn)",
      requestDecision: "In Review",
      documentRequirements: proofOfBirthUploadSatisfiedDocumentRequirements,
    });
    const steps = getStepsWithStatuses("Child Bonding", claimDetail);

    const uploadProofOfBirthStep = steps.find(
      (step) => step.label === "Proof of birth submitted"
    );
    expect(uploadProofOfBirthStep?.status).toEqual("completed");
  });

  describe("#getRFIStatus", () => {
    it("returns 'inProgress' when documents were uploaded after rfi creation date", () => {
      const rfiDocumentCreatedDate = "2023-01-15";
      const claimCreatedAt = "2023-01-01";
      const documentCreatedAt = "2023-01-20";
      const claimDetail = createMockClaimDetail({
        leaveScenario: "Medical (illness)",
        requestDecision: "Pending",
        created_at: claimCreatedAt,
      });
      const rfiDocument = generateNotice(
        "requestForInfoNotice",
        rfiDocumentCreatedDate
      );
      const document = createMockAppealDocument({
        created_at: documentCreatedAt,
      });
      const documents = [rfiDocument, document];
      const rfiStatus = getRFIStatus(documents, claimDetail);

      expect(rfiStatus).toEqual("inProgress");
    });

    it("returns 'actionRequired' when documents were uploaded on the same date as rfi creation date and app submission date", () => {
      const rfiDocumentCreatedDate = "2023-01-15";
      const claimCreatedAt = rfiDocumentCreatedDate;
      const documentCreatedAt = rfiDocumentCreatedDate;
      const claimDetail = createMockClaimDetail({
        leaveScenario: "Medical (illness)",
        requestDecision: "Pending",
        created_at: claimCreatedAt,
      });
      const rfiDocument = generateNotice(
        "requestForInfoNotice",
        rfiDocumentCreatedDate
      );
      const document = createMockAppealDocument({
        created_at: documentCreatedAt,
      });
      const documents = [rfiDocument, document];
      const rfiStatus = getRFIStatus(documents, claimDetail);

      expect(rfiStatus).toEqual("actionRequired");
    });

    it("returns 'inProgess' when documents were uploaded on the same date as rfi creation date but after the app submission date", () => {
      const rfiDocumentCreatedDate = "2023-01-15";
      const claimCreatedAt = "2023-01-10";
      const documentCreatedAt = rfiDocumentCreatedDate;
      const claimDetail = createMockClaimDetail({
        leaveScenario: "Medical (illness)",
        requestDecision: "Pending",
        created_at: claimCreatedAt,
      });
      const rfiDocument = generateNotice(
        "requestForInfoNotice",
        rfiDocumentCreatedDate
      );
      const document = createMockAppealDocument({
        created_at: documentCreatedAt,
      });
      const documents = [rfiDocument, document];
      const rfiStatus = getRFIStatus(documents, claimDetail);

      expect(rfiStatus).toEqual("inProgress");
    });
  });
  describe("#getAllStepStatuses", () => {
    it("returns values of the statuses based on the step values", () => {
      const mockSteps: StepSegmentProps[] = [
        {
          label: "Documents submitted",
          status: "completed",
        },
        {
          label: "Employer response",
          status: "inProgress",
        },
        {
          label: "DFML review",
          status: "inProgress",
        },
        {
          label: "Decision",
          status: "notStarted",
        },
      ];
      const stepStatuses = getAllStepStatuses(mockSteps);
      const {
        employerResponseActionStatus,
        dfmlReviewActionStatus,
        dfmlDecisionActionStatus,
        submittedProof,
      } = stepStatuses;
      expect(employerResponseActionStatus).toBe("inProgress");
      expect(dfmlReviewActionStatus).toBe("inProgress");
      expect(dfmlDecisionActionStatus).toBe("notStarted");
      expect(submittedProof).toBe("inProgress");
    });
  });
});
