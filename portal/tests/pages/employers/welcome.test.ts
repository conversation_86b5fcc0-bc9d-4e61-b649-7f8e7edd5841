import EmployerExemptionsApplication, {
  EmployerExemptionApplicationStatus,
} from "src/models/EmployerExemptionsApplication";
import User, { UserLeaveAdministrator } from "src/models/User";
import { act, screen, waitFor } from "@testing-library/react";

import OAuthServerApi from "src/api/OAuthServerApi";
import Welcome from "src/pages/employers/welcome";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import { setupChatWidgetLogic } from "tests/test-utils/chat-widget-helpers";

jest.mock("src/api/OAuthServerApi");

const userAttrs = {
  user_leave_administrators: [
    new UserLeaveAdministrator({
      has_verification_data: true,
      verified: false,
    }),
  ],
  user_id: "mock_user_id",
  first_name: "<PERSON>",
  last_name: "<PERSON><PERSON>",
  phone_number: {
    int_code: "1",
    extension: "234",
    phone_number: "***-***-1234",
    phone_type: null,
  },
};

function waitForLiveChatInitialization() {
  window.cxone = jest.fn();
  const mockAuthCode = "mock-auth-code";
  setFeatureFlags({
    enableLiveChat: true,
  });
  // Mock API call
  const mockGetOAuthServerCode = jest.fn().mockResolvedValue({
    code: { authz_code: mockAuthCode },
  });

  // type first as unknown - then as OAuthServerApi to
  // prevent having to mock entire object (basePath, namespace, headers etc..)
  jest.mocked(OAuthServerApi).mockImplementation(() => {
    return {
      getOAuthServerCode: mockGetOAuthServerCode,
    } as unknown as OAuthServerApi;
  });
}

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

describe("Employer welcome", () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date("2025-02-07"));
  });

  it("renders page", () => {
    const { container } = renderPage(Welcome, {
      pathname: routes.employers.welcome,
    });

    expect(container).toMatchSnapshot();
  });

  it("renders page with EmployerSatisfactionSurveyBanner", () => {
    jest.setSystemTime(new Date("2025-02-17"));

    const { container } = renderPage(Welcome, {
      pathname: routes.employers.welcome,
    });

    expect(container).toHaveTextContent(
      "Please participate in a survey about your experience as a Leave Administrator managing PFML. Your response is important and can help us to inform our ongoing planning and improvement efforts. The survey should take less than 5 minutes to complete."
    );
  });

  it("renders page with updated content LeaveAdminLegalContentChanges", () => {
    const { container } = renderPage(Welcome, {
      pathname: routes.employers.welcome,
    });

    expect(container).toMatchSnapshot();
  });

  // TODO (PFMLPB-15535) Remove test
  it("renders respond quickly updated content LeaveAdminLegalContentChanges", () => {
    const { container } = renderPage(Welcome, {
      pathname: routes.employers.welcome,
    });

    expect(container).toMatchSnapshot();
    expect(
      screen.getByText(
        /You will receive an email with a direct link when an employee submits an application./
      )
    ).toBeInTheDocument();
  });

  it("renders contact info link when the information is incomplete", () => {
    renderPage(Welcome, {
      pathname: routes.employers.welcome,
    });
    expect(
      screen.getByRole("link", { name: "Contact information" })
    ).toMatchSnapshot();
  });

  it("renders the name and phone number with ext when data is available", () => {
    renderPage(Welcome, {
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User(userAttrs);
      },
      pathname: routes.employers.welcome,
    });
    expect(screen.getByText(/Jane Doe/)).toBeInTheDocument();
    expect(screen.getByText("***-***-1234 ext.234")).toBeInTheDocument();
    expect(screen.getByRole("link", { name: "Edit" })).toMatchSnapshot();
  });

  it("does not render NoOrganizationsAlert when user does not have associated organizations and MTC flag is on", () => {
    const noOrgUserAttrs = {
      user_leave_administrators: [],
      first_name: "Jane",
      last_name: "Doe",
    };

    renderPage(Welcome, {
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User(noOrgUserAttrs);
      },
      pathname: routes.employers.welcome,
    });
    const alertText = screen.queryByText("Get access to your organization(s)");
    expect(alertText).not.toBeInTheDocument();
  });

  it("does not render UnverifiedOrganizationAlert when user has an unverified org and MTC flag is on", () => {
    renderPage(Welcome, {
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User(userAttrs);
      },
      pathname: routes.employers.welcome,
    });

    const alertText = screen.queryByText(
      "You need to finish verifying access to one of your organizations"
    );
    expect(alertText).not.toBeInTheDocument();
  });

  it("renders the name and phone number without ext when data is available", () => {
    setFeatureFlags({
      employerCollectNameAndPhone: true,
    });

    renderPage(Welcome, {
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          ...userAttrs,
          phone_number: {
            int_code: "1",
            extension: null,
            phone_number: "***-***-1234",
            phone_type: null,
          },
        });
      },
      pathname: routes.employers.welcome,
    });
    expect(screen.queryByText(/ext./)).not.toBeInTheDocument();
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    const userAttrsWithoutOrganization = { ...userAttrs };
    userAttrsWithoutOrganization.user_leave_administrators = [];

    const userWithOrganization = new User(userAttrs);
    const userWithoutOrganization = new User(userAttrsWithoutOrganization);

    describe("it displays the welcome page summary box", () => {
      const expectedSummaryBoxHeadingText =
        /Thanks for joining the Massachusetts Paid Family and Medical Leave \(PFML\) program/i;

      const employerExemptionApplication: EmployerExemptionsApplication =
        createMockEmployerExemptionsApplication();

      employerExemptionApplication.employer_exemption_application_status_id =
        EmployerExemptionApplicationStatus.draft.value;

      const employerExemptionApplicationId =
        employerExemptionApplication.employer_exemption_application_id;

      const url = {
        applications: {
          viewAll: '"/employers/applications"',
        },
        exemptions: {
          getReady: `"${routes.employers.employerExemptions.getReady}"`,
          progress: `"${routes.employers.employerExemptions.progress}?employer_exemption_application_id=${employerExemptionApplicationId}"`,
          legalAcknowledgement: `"${routes.employers.employerExemptions.legal}?employer_exemption_application_id=${employerExemptionApplicationId}"`,
        },
        organizations: {
          viewAll: `"${routes.employers.organizations}"`,
        },
      };

      it.each([
        [
          "organization",
          userWithoutOrganization,
          url.organizations.viewAll,
          undefined,
          undefined,
        ],
        [
          "exemptions get ready",
          userWithOrganization,
          url.exemptions.getReady,
          undefined,
          undefined,
        ],
        [
          "exemptions legally acknowledged",
          userWithOrganization,
          url.exemptions.legalAcknowledgement,
          employerExemptionApplication,
          false,
        ],
        [
          "exemptions progress page",
          userWithOrganization,
          url.exemptions.progress,
          employerExemptionApplication,
          true,
        ],
      ])(
        "with the %s url as the 'Request an exemption' link",
        async (
          urlDesc,
          user,
          requestAnExemptionUrl,
          employerExemptionApplication,
          isLegallyAcknowledged
        ) => {
          waitForLiveChatInitialization();
          setEnableEmployerExemptionsPortal(true);
          renderPage(Welcome, {
            addCustomSetup: (appLogic) => {
              appLogic.users.user = user;
              appLogic.employerExemptionsApplication.loadAll = jest.fn();

              if (employerExemptionApplication) {
                employerExemptionApplication.is_legally_acknowledged =
                  isLegallyAcknowledged;
              }

              appLogic.employerExemptionsApplication.getDraftEmployerExemptionApplication =
                jest.fn().mockReturnValue(employerExemptionApplication || null);
              appLogic.employerExemptionsApplication.getDraftEmployerExemptionApplicationId =
                jest
                  .fn()
                  .mockReturnValue(
                    employerExemptionApplication?.employer_exemption_application_id ||
                      null
                  );
              appLogic.employerExemptionsApplication.hasLoadedEmployerExemptionApplicationsList =
                true;
            },
            pathname: routes.employers.welcome,
          });

          const expectedListItems = [
            `<li><a href=${url.applications.viewAll}>See all applications</a> ` +
              "for PFML submitted by employees." +
              "</li>",
            "<li>" +
              `<a href=${requestAnExemptionUrl}>Request an exemption</a>, ` +
              "if your organization offers an approved private plan with paid leave benefits. " +
              "This is not common." +
              "</li>",
            "<li>Add or remove leave administrator access to your organization.</li>",
          ];

          await waitFor(() => {
            const summaryBoxHeader = screen.getByText(
              expectedSummaryBoxHeadingText
            );

            // obtain the summary box ul element
            const summaryBoxList = screen.getAllByRole("list").filter((v) => {
              return v
                .closest("div")
                ?.classList.contains("usa-summary-box__text");
            });

            // create a list of all li elements in the ul
            const summaryBoxListItems = Array.from(
              summaryBoxList[0].innerHTML.matchAll(/<li>.+?<\/li>/g)
            ).map((match) => match[0]);

            expect(summaryBoxHeader).toHaveClass("usa-summary-box__text");
            expect(summaryBoxListItems.length).toEqual(
              expectedListItems.length
            );

            summaryBoxListItems.forEach((li, index) => {
              expect(li).toEqual(expectedListItems[index]);
            });
          });
        }
      );
    });

    it("it displays the exemptions headings and content", async () => {
      waitForLiveChatInitialization();

      await act(() => {
        setEnableEmployerExemptionsPortal(true);
        renderPage(Welcome, {
          pathname: routes.employers.welcome,
        });
      });

      const expectedExemptionText = [
        /view PFML leave applications/i,
        /request a private paid leave exemption/i,
        /massachusetts employers that offer approved private plans with paid leave benefits/i,
        /if you have past or pending exemption requests, you can review each request’s/i,
        /manage your exemption requests/i,
      ];

      expectedExemptionText.forEach((text) => {
        expect(screen.getByText(text)).toBeInTheDocument();
      });
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    it("does not display exemptions content", () => {
      setEnableEmployerExemptionsPortal(false);
      renderPage(Welcome, {
        pathname: routes.employers.welcome,
      });

      const notExpectedExemptionText = [
        /view PFML leave applications/i,
        /Thanks for joining the Massachusetts Paid Family and Medical Leave \(PFML\) program/,
        /request a private paid leave exemption/i,
        /massachusetts employers that offer approved private plans with paid leave benefits/i,
        /if you have past or pending exemption requests, you can review each request’s/i,
        /manage your exemption requests/i,
      ];

      notExpectedExemptionText.forEach((text) => {
        expect(screen.queryByText(text)).not.toBeInTheDocument();
      });
    });
  });

  // TODO (PFMLPB-21132): Cleanup when feature flag is removed
  describe("chat widget initialization", () => {
    it("should initialize the chat widget when enableLiveChat is true", async () => {
      const chatWidget = setupChatWidgetLogic();
      waitForLiveChatInitialization();
      renderPage(Welcome, {
        pathname: routes.employers.welcome,
        addCustomSetup: (appLogic) => {
          appLogic.users.user = new User({
            user_id: "mock_user_id",
            user_leave_administrators: [
              new UserLeaveAdministrator({
                verified: true,
              }),
            ],
          });
          appLogic.chatWidget = chatWidget;
        },
      });

      await waitFor(() => {
        expect(chatWidget.isChatWidgetInitialized).toBe(true);
      });
    });
    it("should not initialize the chat widget when enableLiveChat is false", () => {
      const chatWidget = setupChatWidgetLogic();
      window.cxone = jest.fn();
      setFeatureFlags({
        enableLiveChat: false,
      });

      renderPage(Welcome, {
        pathname: routes.employers.welcome,
        addCustomSetup: (appLogic) => {
          appLogic.chatWidget = chatWidget;
        },
      });

      expect(chatWidget.isChatWidgetInitialized).toBe(false);
    });
  });
});
