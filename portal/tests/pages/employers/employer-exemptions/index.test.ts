import {
  TestGenerator,
  standardStubTests,
  testDescriptions,
} from "tests/pages/employers/exemption-applications/exemptions-utils/standard-test-handlers";

import ExemptionsTab from "src/pages/employers/employer-exemptions/index";
import routes from "src/routes";
import { setEnableEmployerExemptionsPortal } from "tests/pages/employers/exemption-applications/exemptions-utils/page-setup-utils";

const pageDef = {
  name: "EmployerExemptionsTab",
  component: ExemptionsTab,
  path: routes.employers.employerExemptions.exemptionsTab,
  baseContent: ["Exemptions"],
  inputs: [],
};

const runTests = (generators: TestGenerator[]) =>
  generators.forEach((generator) => {
    const testDef = generator(pageDef);
    // eslint-disable-next-line jest/expect-expect
    it(testDef.name, testDef.handler);
  });

describe(pageDef.name, () => {
  describe(testDescriptions.featureFlagOff, () => {
    runTests(standardStubTests);
  });

  describe(testDescriptions.featureFlagOn, () => {
    beforeEach(() => setEnableEmployerExemptionsPortal(true));
    runTests(standardStubTests);
  });
});
