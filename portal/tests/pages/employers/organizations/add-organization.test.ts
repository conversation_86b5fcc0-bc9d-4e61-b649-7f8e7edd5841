import User, { UserLeaveAdministrator } from "src/models/User";
import { screen, waitFor } from "@testing-library/react";

import AddOrganization from "src/pages/employers/organizations/add-organization";
import { AppLogic } from "src/hooks/useAppLogic";
import OAuthServerApi from "src/api/OAuthServerApi";
import { renderPage } from "tests/test-utils";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import { setupChatWidgetLogic } from "tests/test-utils/chat-widget-helpers";
import userEvent from "@testing-library/user-event";

jest.mock("src/api/OAuthServerApi");

const setup = (cb?: (appLogic: AppLogic) => void) => {
  let addEmployerSpy;
  const chatWidget = setupChatWidgetLogic();
  const utils = renderPage(AddOrganization, {
    addCustomSetup: (appLogic) => {
      addEmployerSpy = jest.spyOn(appLogic.employers, "addEmployer");
      appLogic.chatWidget = chatWidget;
      if (cb) {
        cb(appLogic);
      }
    },
  });
  return { addEmployerSpy, ...utils, chatWidget };
};
describe("AddOrganization", () => {
  it("renders the page", () => {
    const { container } = setup();
    expect(container.firstChild).toMatchSnapshot();
  });

  it("renders standard content when exemptions feature flag is disabled", () => {
    const { container } = setup();
    expect(container.querySelector("p")).toHaveTextContent(
      "If you manage leave for multiple organizations, you can add them to your account. You'll need to verify your access to this organization."
    );
    expect(container.querySelector("p")).not.toHaveTextContent(
      "If you manage leave or exemptions for multiple organizations, you can add them to your account. You'll need to verify your access to this organization."
    );
  });
  it("renders exemptions content when exemptions feature flag is enabled", () => {
    setFeatureFlags({
      enableEmployerExemptionsPortal: true,
    });
    const { container } = setup();
    expect(container.querySelector("p")).toHaveTextContent(
      "If you manage leave or exemptions for multiple organizations, you can add them to your account. You'll need to verify your access to this organization."
    );
    expect(container.querySelector("p")).not.toHaveTextContent(
      "If you manage leave for multiple organizations, you can add them to your account. You'll need to verify your access to this organization."
    );
  });

  it("submits FEIN", async () => {
    const { addEmployerSpy } = setup();
    await userEvent.type(screen.getByRole("textbox"), "01-2345678");
    await userEvent.click(screen.getByRole("button", { name: "Continue" }));

    await waitFor(() => {
      expect(addEmployerSpy).toHaveBeenCalledWith({
        employer_fein: "01-2345678",
      });
    });
  });

  // TODO (PFMLPB-21132): Cleanup when feature flag is removed
  describe("chat widget initialization", () => {
    it("should initialize the chat widget when enableLiveChat is true", async () => {
      window.cxone = jest.fn();
      const mockAuthCode = "mock-auth-code";
      setFeatureFlags({
        enableLiveChat: true,
      });
      // Mock API call
      const mockGetOAuthServerCode = jest.fn().mockResolvedValue({
        code: { authz_code: mockAuthCode },
      });

      // type first as unknown - then as OAuthServerApi to
      // prevent having to mock entire object (basePath, namespace, headers etc..)
      jest.mocked(OAuthServerApi).mockImplementation(() => {
        return {
          getOAuthServerCode: mockGetOAuthServerCode,
        } as unknown as OAuthServerApi;
      });

      const { chatWidget } = setup((appLogic: AppLogic) => {
        appLogic.users.user = new User({
          user_leave_administrators: [
            new UserLeaveAdministrator({
              verified: true,
            }),
          ],
        });
      });

      await waitFor(() => {
        expect(chatWidget.isChatWidgetInitialized).toBe(true);
      });
    });
    it("should not initialize the chat widget when enableLiveChat is false", () => {
      window.cxone = jest.fn();
      setFeatureFlags({
        enableLiveChat: false,
      });

      const { chatWidget } = setup();

      expect(chatWidget.isChatWidgetInitialized).toBe(false);
    });
  });
});
