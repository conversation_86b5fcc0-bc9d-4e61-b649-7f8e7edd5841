import User, { UserLeaveAdministrator } from "src/models/User";
import { screen, waitFor } from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import Index from "src/pages/employers/organizations";
import OAuthServerApi from "src/api/OAuthServerApi";
import createMockUserLeaveAdministrator from "lib/mock-helpers/createMockUserLeaveAdministrator";
import { renderPage } from "tests/test-utils";
import routeWithParams from "src/utils/routeWithParams";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import { setupChatWidgetLogic } from "tests/test-utils/chat-widget-helpers";

jest.mock("src/api/OAuthServerApi");

const verifiedAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Knitting Castle",
  employer_fein: "11-3453443",
  employer_id: "dda930f-93jfk-iej08",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: true,
});

const verifiableAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Book Bindings 'R Us",
  employer_fein: "00-3451823",
  employer_id: "dda903f-f093f-ff900",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: false,
});

const unverifiableAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Tomato Touchdown",
  employer_fein: "22-3457192",
  employer_id: "io19fj9-00jjf-uiw3r",
  has_fineos_registration: true,
  has_verification_data: false,
  has_verified_leave_admin: true,
  verified: false,
});

const EMPLOYERS = [
  verifiableAdministrator,
  verifiedAdministrator,
  unverifiableAdministrator,
];

async function waitForDataToLoad() {
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });
}

function waitForLiveChatInitialization() {
  window.cxone = jest.fn();
  const mockAuthCode = "mock-auth-code";
  setFeatureFlags({
    enableLiveChat: true,
  });
  // Mock API call
  const mockGetOAuthServerCode = jest.fn().mockResolvedValue({
    code: { authz_code: mockAuthCode },
  });

  // type first as unknown - then as OAuthServerApi to
  // prevent having to mock entire object (basePath, namespace, headers etc..)
  jest.mocked(OAuthServerApi).mockImplementation(() => {
    return {
      getOAuthServerCode: mockGetOAuthServerCode,
    } as unknown as OAuthServerApi;
  });
}

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

const setup = async (
  employers: UserLeaveAdministrator[] = [],
  props: { [key: string]: unknown } = {}
) => {
  const chatWidget = setupChatWidgetLogic();
  const utils = renderPage(
    Index,
    {
      pathname: routes.employers.organizations,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          user_id: "mock_user_id",
          consented_to_data_sharing: true,
          user_leave_administrators: employers,
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", employers);
        appLogic.chatWidget = chatWidget;
      },
    },
    { query: { account_converted: "false" }, ...props }
  );

  await waitForDataToLoad();
  return { ...utils, chatWidget };
};

describe("Index", () => {
  it("renders the page", async () => {
    const { container } = await setup();
    expect(container).toMatchSnapshot();
  });

  it("show the correct empty state", async () => {
    await setup();
    expect(
      screen.getByRole("row", { name: "None reported" })
    ).toBeInTheDocument();
  });

  it("renders NoOrganizationsAlert when user does not have associated organizations", async () => {
    await setup();
    expect(
      screen.getByText("Get access to your organization(s)")
    ).toBeInTheDocument();
  });

  it("displays a table row for each user leave administrator", async () => {
    waitForLiveChatInitialization();
    await setup(EMPLOYERS);

    expect(
      screen.queryByRole("row", { name: "None reported" })
    ).not.toBeInTheDocument();
    expect(
      screen.getByRole("row", {
        name: "Book Bindings 'R Us Verification required 00-3451823",
      })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("row", { name: "Knitting Castle 11-3453443" })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("row", {
        name: "Tomato Touchdown Verification blocked 22-3457192",
      })
    ).toBeInTheDocument();
  });

  it("displays a button linked to Add Organization page", async () => {
    await setup();
    expect(
      screen.getByRole("link", { name: "Add organization" })
    ).toBeInTheDocument();
  });

  it("renders UnverifiedOrganizationAlert when user has an unverified org", async () => {
    waitForLiveChatInitialization();
    await setup(EMPLOYERS);

    expect(
      screen.queryByText(
        /You need to finish verifying access to one of your organizations/
      )
    ).toBeInTheDocument();

    expect(
      screen.queryByText(/select the organization below/)
    ).toBeInTheDocument();
  });

  it("shows the 'Verification required' tag and link for verifiable administrators", async () => {
    await setup([verifiableAdministrator]);
    expect(
      screen.getByRole("rowheader", {
        name: "Book Bindings 'R Us Verification required",
      })
    ).toMatchSnapshot();
  });

  it("shows the 'Verification blocked' tag and link for unverifiable administrators", async () => {
    await setup([unverifiableAdministrator]);
    expect(
      screen.getByRole("rowheader", {
        name: "Tomato Touchdown Verification blocked",
      })
    ).toMatchSnapshot();
  });

  it("links to the cannot verify page for unverifiable administrators", async () => {
    await setup([unverifiableAdministrator]);
    const expectedUrl = routeWithParams("employers.cannotVerify", {
      employer_id: unverifiableAdministrator.employer_id,
    });
    expect(
      screen.getByRole("link", {
        name: new RegExp(unverifiableAdministrator.employer_dba as string),
      })
    ).toHaveAttribute("href", expectedUrl);
  });

  it("does not show the 'Verification required' tag for verified administrators", async () => {
    waitForLiveChatInitialization();
    await setup([verifiedAdministrator]);
    expect(
      screen.queryByRole("link", { name: "Verification required" })
    ).not.toBeInTheDocument();
  });

  it("does not render links for unverified administrators", async () => {
    await setup([unverifiableAdministrator]);
    expect(
      screen.queryByRole("link", {
        name: unverifiableAdministrator.employer_dba as string,
      })
    ).not.toBeInTheDocument();
  });

  it("shows a success message telling the user they are now a leave admin", async () => {
    waitForLiveChatInitialization();
    await setup([verifiedAdministrator], {
      query: { account_converted: "true" },
    });
    expect(
      screen.getByRole("heading", { name: "Success" })
    ).toBeInTheDocument();
    expect(screen.getByRole("region")).toMatchSnapshot();
  });

  it("shows a success alert if redirected from removal process (the LA just removed themselves)", async () => {
    await setup([], {
      query: { org_name: "Tomato Touchdown", remove: "<EMAIL>" },
    });
    expect(
      screen.getByRole("heading", {
        name: "Leave administrator removed",
      })
    ).toBeInTheDocument();
    expect(
      screen.getByTitle("employers remove leave admin success")
    ).toHaveTextContent(
      /<EMAIL> no longer has access to Tomato Touchdown./
    );
  });

  it("success alert for leave admin removal is hidden by default (without remove query param)", async () => {
    await setup([]);
    expect(
      screen.queryByText(
        /<NAME_EMAIL> has been removed from your organization./
      )
    ).not.toBeInTheDocument();
  });

  // TODO (PFMLPB-21132): Cleanup when feature flag is removed
  describe("chat widget initialization", () => {
    it("should initialize the chat widget when enableLiveChat is true", async () => {
      waitForLiveChatInitialization();

      const { chatWidget } = await setup([verifiedAdministrator]);

      await waitFor(() => {
        expect(chatWidget.isChatWidgetInitialized).toBe(true);
      });
    });
    it("should not initialize the chat widget when enableLiveChat is false", async () => {
      window.cxone = jest.fn();
      setFeatureFlags({
        enableLiveChat: false,
      });

      const { chatWidget } = await setup();

      expect(chatWidget.isChatWidgetInitialized).toBe(false);
    });
  });

  // TODO (PFMLPB-19806): removal of feature flag
  describe("employer exemptions portal", () => {
    it("renders the employer exemptions lead text when the enableEmployerExemptionsPortal feature flag is enabled", async () => {
      setEnableEmployerExemptionsPortal(true);
      await setup([verifiedAdministrator]);
      expect(
        screen.getByText(
          /If your organization has an approved private plan, then it may be eligible to request an exemption./i
        )
      ).toBeInTheDocument();
    });

    it("does not render the employer exemptions lead text when the enableEmployerExemptionsPortal feature flag is disabled", async () => {
      setEnableEmployerExemptionsPortal(false);
      await setup([verifiedAdministrator]);
      expect(
        screen.queryByText(
          /If your organization has an approved private plan, then it may be eligible to request an exemption./i
        )
      ).not.toBeInTheDocument();
    });
  });
});
