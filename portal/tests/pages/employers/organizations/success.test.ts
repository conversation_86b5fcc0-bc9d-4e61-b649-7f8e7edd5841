import User, { UserLeaveAdministrator } from "src/models/User";

import Success from "src/pages/employers/organizations/success";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";
import { screen } from "@testing-library/react";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

const setup = (props = {}, query = {}) => {
  const utils = renderPage(
    Success,
    {
      pathname: routes.employers.verificationSuccess,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: [
            new UserLeaveAdministrator({
              employer_dba: "Company Name",
              employer_fein: "12-3456789",
              employer_id: "mock_employer_id",
              verified: false,
            }),
          ],
        });
      },
    },
    {
      query: { employer_id: "mock_employer_id", ...query },
      ...props,
    }
  );
  return { ...utils };
};

describe("Success", () => {
  it("renders the page", () => {
    const { container } = setup();
    expect(container).toMatchSnapshot();
  });

  it("renders standard language when exemption feature flag is disabled", () => {
    setFeatureFlags({ enableEmployerExemptionsPortal: false });
    const { container } = setup();
    expect(container.querySelector("h1")).toHaveTextContent(
      "You can now manage leave for this organization"
    );
    expect(container.querySelector("h1")).not.toHaveTextContent(
      "You now have access to this organization"
    );
    expect(container.querySelector("a")).toHaveTextContent("Manage leave");
    expect(container.querySelector("a")).not.toHaveTextContent(
      "Manage leave and exemptions"
    );
  });

  it("renders exemptions language when exemption feature flag is enabled", () => {
    setFeatureFlags({ enableEmployerExemptionsPortal: true });
    const { container } = setup();
    expect(container.querySelector("h1")).not.toHaveTextContent(
      "You can now manage leave for this organization"
    );
    expect(container.querySelector("h1")).toHaveTextContent(
      "You now have access to this organization"
    );
    expect(container.querySelector("a")).toHaveTextContent(
      "Manage leave and exemptions"
    );
  });

  it("renders page not found when employer isn't found", () => {
    setup({}, { employer_id: "" });

    expect(
      screen.getByRole("heading", { name: "Page not found" })
    ).toBeInTheDocument();
  });
});
