import { screen, waitFor } from "@testing-library/react";

import { QuestionInputGetter } from "tests/test-utils";
import createMockUserLeaveAdministrator from "lib/mock-helpers/createMockUserLeaveAdministrator";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import userEvent from "@testing-library/user-event";

// TODO (PFMLPB-23216): Delete this and instead point at portal/tests/pages/employers/exemption-applications/exemptions-utils/errors-utils.ts
export type ErrorList = Array<{
  field: string;
  message: string;
}>;

// TODO (PFMLPB-23216): Deprecate in favor of standard-test-handlers.ts pattern instead
export const genericTestDescriptions = {
  featureFlagOff:
    "when the enableEmployerExemptionsPortal feature flag is disabled",
  featureFlagOn:
    "when the enableEmployerExemptionsPortal feature flag is enabled",
  testCase404: "page not found is displayed",
  notFoundText: "Page not found",
  matchesSnapshot: "page content matches snapshot",
  linksRenderProperly: "page displays links with the correct urls",
};

/* 
  TODO (PFMLPB-23216): Point existing tests reliant on these functions to /ememptions-utils/asserts.ts variants instead
*/
export function assertYesNoQuestionWithText(questionText: string) {
  const input = new QuestionInputGetter(questionText);
  const radioInputs = input.getRadioInputs();
  expect(radioInputs.length).toEqual(2);
  expect(input.getInput("Yes")).toBeInTheDocument();
  expect(input.getInput("No")).toBeInTheDocument();
}

export function assertRadioQuestionWithOptions(
  questionText: string,
  questionOptions: RegExp[]
) {
  const input = new QuestionInputGetter(questionText);
  const radioInputs = input.getRadioInputs();
  expect(questionOptions.length).toEqual(radioInputs.length);
  questionOptions.forEach((option) => {
    expect(input.getInputUsingRegex(option)).toBeInTheDocument();
  });
}

export const assertItemsShown = (
  container: HTMLElement,
  items: Array<string | RegExp>
) => {
  items.forEach((element) => {
    expect(container).toHaveTextContent(element);
  });
};

export const assertItemsHidden = (
  container: HTMLElement,
  items: Array<string | RegExp>
) => {
  items.forEach((element) => {
    expect(container).not.toHaveTextContent(element);
  });
};

// TODO (PFMLPB-23216): Investigate renaming "expect" to "assert" for consistency w/ other asserts
export const expectHyperlink = (name: string | RegExp, url: string) => {
  const linkElement = screen.getByRole("link", {
    name,
  });
  expect(linkElement).toHaveAttribute("href", url);
};

/* 
  TODO (PFMLPB-23216): Point existing tests reliant on these functions to /ememptions-utils/page-setup-utils.ts variants instead
*/

export const setEnableEmployerExemptionsPortal = (value: boolean) => {
  // TODO (PFMLPB-19806): removal of feature flag
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

export const verifiedAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Knitting Castle",
  employer_fein: "11-3453443",
  employer_id: "dda930f-93jfk-iej08",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: true,
});

// TODO (PFMLPB-23216): Investigate moving  into a project-level util file
export async function waitForDataToLoad() {
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });
}

/* 
  TODO (PFMLPB-23216): Point existing tests reliant on these functions to /ememptions-utils/input-utils.ts variants instead
*/

export const getRadioButton = (name: string | RegExp) => {
  const role = "radio";
  if (screen.queryByRole(role, { name })) {
    return screen.getByRole(role, { name });
  }
  throw new Error("Unable to find radio button: " + name);
};

export const getComboBox = (name: string | RegExp) => {
  const role = "combobox";
  if (screen.queryByRole(role, { name })) {
    return screen.getByRole(role, { name });
  }
  throw new Error("Unable to find combo box: " + name);
};

export const getButtonWithName = (name: string | RegExp) => {
  if (!screen.queryByRole("button", { name })) {
    throw new Error("Unable to find button with name " + name);
  }
  return screen.getByRole("button", { name });
};

export const getLinkWithName = (name: string | RegExp) => {
  if (!screen.queryByRole("link", { name })) {
    throw new Error("Unable to find link with name " + name);
  }
  return screen.getByRole("link", { name });
};

export const selectRadioButton = (buttonName: RegExp | string) => {
  return userEvent.click(getRadioButton(buttonName));
};

export const selectRadioOptionAtRandom = (options: RegExp[]) => {
  const randomOption = options[Math.floor(Math.random() * options.length)];
  return userEvent.click(getRadioButton(randomOption));
};

export const clickSaveAndContinueButton = () => {
  return userEvent.click(getButtonWithName("Save and continue"));
};
