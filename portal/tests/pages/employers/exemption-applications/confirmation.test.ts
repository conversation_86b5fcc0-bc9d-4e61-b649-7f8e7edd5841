import User, { UserLeaveAdministrator } from "src/models/User";
import { screen, waitFor } from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import EmployerExemptionsConfirmation from "src/pages/employers/exemption-applications/confirmation";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import createMockUserLeaveAdministrator from "lib/mock-helpers/createMockUserLeaveAdministrator";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import userEvent from "@testing-library/user-event";

const verifiedAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Knitting Castle",
  employer_fein: "11-3453443",
  employer_id: "dda930f-93jfk-iej08",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: true,
});

jest.mock("src/api/EmployerExemptionsApi");

const getViewRequestButton = () => {
  return screen.getByRole("link", { name: /View request/i });
};

const getPrintConfirmationButton = () => {
  return screen.getByRole("link", { name: /Print confirmation/i });
};

async function waitForDataToLoad() {
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });
}
const setup = async (
  employers: UserLeaveAdministrator[] = [],
  props: { [key: string]: unknown } = {}
) => {
  const employerExemptionApplication: EmployerExemptionsApplication =
    createMockEmployerExemptionsApplication();
  employerExemptionApplication.employer_id = employers[0].employer_id;
  const utils = renderPage(
    EmployerExemptionsConfirmation,
    {
      pathname: routes.employers.employerExemptions.confirmation,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: employers,
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", employers);
        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
      ...props,
    }
  );

  await waitForDataToLoad();
  return utils;
};

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

describe("EmployerExemptionsConfirmation", () => {
  describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    it("Page not found is displayed", async () => {
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup([verifiedAdministrator]);

      expect(container).toHaveTextContent("Page not found");
    });

    it("renders confirmation content", async () => {
      const { container } = await setup([verifiedAdministrator]);
      expect(container).toMatchSnapshot();
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    beforeEach(() => {
      setEnableEmployerExemptionsPortal(true);
    });

    it("The confirmation page is displayed", async () => {
      const { container } = await setup([verifiedAdministrator]);

      expect(container).toHaveTextContent(
        "You submitted your exemption request"
      );
      expect(container).toHaveTextContent("Your request ID is: ");
      expect(container).toHaveTextContent("Track and manage your request");
      expect(container).toHaveTextContent("What happens next");
      expect(container).toHaveTextContent("Interested in more support?");
      expect(container).toHaveTextContent(
        "If you need assistance, call the Contact Center at (833) 344‑7365. Business hours are 8:30 a.m. to 4:30 p.m. Monday - Friday."
      );
    });

    it("The expected button links are displayed", async () => {
      await setup([verifiedAdministrator]);

      const viewRequestButton = getViewRequestButton();
      const printConfirmationButton = getPrintConfirmationButton();

      expect(viewRequestButton).toHaveAttribute("href", "#");

      expect(printConfirmationButton).toHaveAttribute("href", "#");
    });

    it("Clicking the print button calls window.print()", async () => {
      await setup([verifiedAdministrator]);
      const printConfirmationButton = getPrintConfirmationButton();
      const print = jest.spyOn(window, "print").mockImplementation(() => {});
      await userEvent.click(printConfirmationButton);
      expect(print).toHaveBeenCalled();
      print.mockRestore();
    });
  });
});
