import User, { UserLeaveAdministrator } from "src/models/User";
import { screen, waitFor } from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import EmployerExemptionsReview from "src/pages/employers/exemption-applications/review";
import { ExemptionRequestSteps } from "src/models/EmployerExemptionsStep";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import createMockUserLeaveAdministrator from "lib/mock-helpers/createMockUserLeaveAdministrator";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

const verifiedAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Knitting Castle",
  employer_fein: "11-3453443",
  employer_id: "dda930f-93jfk-iej08",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: true,
});

jest.mock("src/api/EmployerExemptionsApi");

async function waitForDataToLoad() {
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });
}
const setup = async (
  employers: UserLeaveAdministrator[] = [],
  props: { [key: string]: unknown } = {}
) => {
  const employerExemptionApplication = createMockEmployerExemptionsApplication(
    "4a431e6a-4a49-419a-9c36-7ccbfedd5dc9",
    ExemptionRequestSteps.uploadDocuments
  );
  employerExemptionApplication.employer_id = employers[0].employer_id;

  const utils = renderPage(
    EmployerExemptionsReview,
    {
      pathname: routes.employers.employerExemptions.review,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: employers,
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", employers);

        appLogic.employerExemptionsApplication.hasLoadedEmployerExemptionApplicationsList =
          true;

        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
      ...props,
    }
  );

  await waitForDataToLoad();
  return utils;
};

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};
describe("EmployerExemptionsReview", () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2022, 1, 1));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    it("Page not found is displayed", async () => {
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup([verifiedAdministrator]);

      expect(container).toHaveTextContent("Page not found");
    });

    it("renders review content", async () => {
      setEnableEmployerExemptionsPortal(true);
      const { container } = await setup([verifiedAdministrator]);
      expect(container).toMatchSnapshot();
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    beforeEach(() => {
      setEnableEmployerExemptionsPortal(true);
    });

    it("the review page is displayed", async () => {
      const { container } = await setup([verifiedAdministrator]);

      expect(container).toHaveTextContent("Review exemption request");
      expect(container).toHaveTextContent(
        "Please review your responses for accuracy before submitting your exemption request."
      );
    });
    it("the review page has all 4 steps present for review", async () => {
      const { container } = await setup([verifiedAdministrator]);
      expect(container).toHaveTextContent("Contact information");
      expect(container).toHaveTextContent("Insurance information");
      expect(container).toHaveTextContent("Workforce information");
      expect(container).toHaveTextContent("Documentation upload");
    });
    it("the review page has Review Rows present for review", async () => {
      await setup([verifiedAdministrator]);
      const reviewRow = screen.getAllByTestId("ReviewRowContainer");
      expect(reviewRow.length > 0).toBe(true);
    });
  });
});
