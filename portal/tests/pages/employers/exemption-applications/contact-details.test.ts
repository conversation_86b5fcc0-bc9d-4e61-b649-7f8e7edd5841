import {
  TestGenerator,
  standardContentAndDesignTests,
  standardStubTests,
  testDescriptions,
} from "./exemptions-utils/standard-test-handlers";

import EmployerExemptionsContactDetails from "src/pages/employers/exemption-applications/contact-details";
import routes from "src/routes";
import { setEnableEmployerExemptionsPortal } from "./exemptions-utils/page-setup-utils";

/* eslint-disable jest/expect-expect */
jest.mock("src/api/EmployerExemptionsApi");

const pageDef = {
  name: "EmployerExemptionsContactDetails",
  component: EmployerExemptionsContactDetails,
  path: routes.employers.employerExemptions.contactDetails,
  baseContent: [
    "Contact details",
    "Who should we contact for more information?",
    "Provide the contact details of the best person to reach out to at your organization regarding this exemption request. We may contact them if we need more information.",
  ],
  inputs: [
    {
      name: "contact_first_name",
      type: "text",
      label: "First Name",
      errorMsg: "Enter a first name",
    },
    {
      name: "contact_last_name",
      type: "text",
      label: "Last Name",
      errorMsg: "Enter a last name",
    },
    {
      name: "contact_title",
      type: "text",
      label: "Title",
      errorMsg: "Enter a title",
    },
    {
      name: "contact_phone.phone_number",
      type: "tel",
      label: "Phone Number",
      errorMsg: "Enter a phone number",
    },
    {
      name: "contact_email_address",
      type: "text",
      label: "Email",
      errorMsg: "Enter your email address",
    },
  ],
};

const runTests = (generators: TestGenerator[]) =>
  generators.forEach((generator) => {
    const testDef = generator(pageDef);
    it(testDef.name, testDef.handler);
  });

describe(pageDef.name, () => {
  describe(testDescriptions.featureFlagOff, () => {
    runTests(standardStubTests);
  });

  describe(testDescriptions.featureFlagOn, () => {
    beforeEach(() => setEnableEmployerExemptionsPortal(true));
    runTests(standardContentAndDesignTests);
  });
});
