import User, { UserLeaveAdministrator } from "src/models/User";
import { screen, waitFor } from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import GetReadyExemptions from "src/pages/employers/exemption-applications/get-ready-exemptions";
import { assertItemsShown } from "./exemptions-utils/assert-utils";
import createMockUserLeaveAdministrator from "lib/mock-helpers/createMockUserLeaveAdministrator";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

// TODO (PFMLPB-24349): Reafactor this file to use standard testing framework
/* eslint-disable jest/expect-expect */

const verifiedAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Knitting Castle",
  employer_fein: "11-3453443",
  employer_id: "dda930f-93jfk-iej08",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: true,
});
async function waitForDataToLoad() {
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });
}
const setup = async (
  employers: UserLeaveAdministrator[] = [],
  props: { [key: string]: unknown } = {}
) => {
  const utils = renderPage(
    GetReadyExemptions,
    {
      pathname: routes.employers.employerExemptions.getReady,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: employers,
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", employers);
      },
    },
    { query: { account_converted: "false" }, ...props }
  );

  await waitForDataToLoad();
  return utils;
};

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};
describe("GetReadyExemptions", () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2022, 1, 1));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    it("Page not found is displayed", async () => {
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup([verifiedAdministrator]);

      expect(container).toHaveTextContent("Page not found");
    });

    it("renders get ready content", async () => {
      const { container } = await setup([verifiedAdministrator]);
      expect(container).toMatchSnapshot();
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    it("the get ready exemptions information is displayed", async () => {
      setEnableEmployerExemptionsPortal(true);
      const { container } = await setup([verifiedAdministrator]);

      assertItemsShown(container, [
        "Get ready to submit an exemption request",
        "If you're an employer who provides a paid leave benefit to your workforce, you may be eligible to recieve an exemption from collecting, remitting, and paying contributions under the state's Paid Family and Medical Leave (PFML) law.",
        "To submit an exemption request, you'll need to be able to reference or upload the following documents. Save time by gathering your documents now.",
        "For purchased private plan exemptions, you will need:",
        "An insurance plan provided by one of the carriers reviewed and acknowledged by the Division of Insurance.",
        "A completed Massachusetts Paid Family and Medical Leave Confirmation of Insurance form.",
        "For self-insured private plan exemptions, you will need:",
        "A completed Self-Insured Insurance Declaration document.",
        "A copy of your signed and notarized Surety Bond.",
        "A copy of your self-insured plan.",
      ]);
    });
  });
});
