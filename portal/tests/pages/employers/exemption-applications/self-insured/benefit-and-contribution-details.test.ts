import User, { UserLeaveAdministrator } from "src/models/User";
import { screen, waitFor } from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import EmployerExemptionsSelfInsuredBenefitAndContributionDetails from "src/pages/employers/exemption-applications/self-insured/benefit-and-contribution-details";
import { ValidationError } from "src/errors";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import createMockUserLeaveAdministrator from "lib/mock-helpers/createMockUserLeaveAdministrator";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

const verifiedAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Knitting Castle",
  employer_fein: "11-3453443",
  employer_id: "dda930f-93jfk-iej08",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: true,
});

jest.mock("src/api/EmployerExemptionsApi");

async function waitForDataToLoad() {
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });
}

const setup = async (
  employers: UserLeaveAdministrator[] = [],
  props: { [key: string]: unknown } = {},
  errors: ValidationError[] = []
) => {
  const employerExemptionApplication: EmployerExemptionsApplication =
    createMockEmployerExemptionsApplication();
  employerExemptionApplication.employer_id = employers[0].employer_id;

  const utils = renderPage(
    EmployerExemptionsSelfInsuredBenefitAndContributionDetails,
    {
      pathname:
        routes.employers.employerExemptions
          .selfInsuredBenefitAndContributionDetails,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: employers,
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", employers);
        appLogic.errors = errors;
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
      ...props,
    }
  );

  await waitForDataToLoad();
  return utils;
};

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

describe("EmployerExemptionsSelfInsuredBenefitAndContributionDetails", () => {
  describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    it("Page not found is displayed", async () => {
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup([verifiedAdministrator]);

      expect(container).toHaveTextContent("Page not found");
    });

    it("renders benefit and contribution details content", async () => {
      const { container } = await setup([verifiedAdministrator]);
      expect(container).toMatchSnapshot();
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    beforeEach(() => {
      setEnableEmployerExemptionsPortal(true);
    });

    it("the self insured benefit and contribution details page renders the title", async () => {
      const { container } = await setup([verifiedAdministrator]);
      expect(container).toHaveTextContent(
        "Insurance information | Part 3 of 5"
      );
    });

    it("the self insured benefit and contribution details page renders the section label", async () => {
      await setup([verifiedAdministrator]);
      expect(
        screen.queryByText(/Tell us about benefits and contributions/i)
      ).toBeInTheDocument();
    });

    it("displays expected radio buttons", async () => {
      await setup([verifiedAdministrator]);
      expect(
        screen.queryByText(
          /Does your plan pay out benefits that are greater to or equal to the state’s plan/i
        )
      ).toBeInTheDocument();

      expect(
        screen.queryByText(
          /Do you withhold premiums or contributions from your employees’ wages/i
        )
      ).toBeInTheDocument();

      expect(
        screen.queryByText(
          /Is the amount withheld less than or equal to the amount required for employee contributions/i
        )
      ).toBeInTheDocument();
    });
  });
});
