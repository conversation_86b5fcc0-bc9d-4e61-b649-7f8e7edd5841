import User, { UserLeaveAdministrator } from "src/models/User";
import {
  verifiedAdministrator,
  waitForDataToLoad,
} from "tests/pages/employers/exemption-applications/employer-exemptions-test-utils";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import { ValidationError } from "src/errors";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";

// TODO (PFMLPB-23216): Investigate merging this with the existing exemptionsPageSetup() method
export async function setup(params: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component: React.ComponentType<any>;
  hasFamilyExemption?: boolean | null;
  hasMedicalExemption?: boolean | null;
  props?: { [key: string]: unknown };
  errors?: ValidationError[];
}) {
  const employerExemptionApplication: EmployerExemptionsApplication =
    createMockEmployerExemptionsApplication();
  employerExemptionApplication.employer_id = verifiedAdministrator.employer_id;
  employerExemptionApplication.has_family_exemption =
    params.hasFamilyExemption ?? null;
  employerExemptionApplication.has_medical_exemption =
    params.hasMedicalExemption ?? null;

  const utils = renderPage(
    params.component,
    {
      pathname:
        routes.employers.employerExemptions.selfInsuredLeaveOptionsDetails,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: [verifiedAdministrator],
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", [
            verifiedAdministrator,
          ]);
        appLogic.errors = params.errors ?? [];
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
      ...params.props,
    }
  );

  await waitForDataToLoad();
  return utils;
}
