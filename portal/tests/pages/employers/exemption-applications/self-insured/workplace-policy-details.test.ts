import {
  assertYesNoQuestionWithText,
  setEnableEmployerExemptionsPortal,
} from "tests/pages/employers/exemption-applications/employer-exemptions-test-utils";

import EmployerExemptionsSelfInsuredWorkplacePolicyDetails from "src/pages/employers/exemption-applications/self-insured/workplace-policy-details";
import { screen } from "@testing-library/react";
import { setup } from "./self-insured-test-utils";

jest.mock("src/api/EmployerExemptionsApi");

describe("EmployerExemptionsSelfInsuredWorkplacePolicyDetails", () => {
  describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    it("Page not found is displayed", async () => {
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup({
        component: EmployerExemptionsSelfInsuredWorkplacePolicyDetails,
      });

      expect(container).toHaveTextContent("Page not found");
    });

    it("renders workplace policy details content", async () => {
      const { container } = await setup({
        component: EmployerExemptionsSelfInsuredWorkplacePolicyDetails,
      });
      expect(container).toMatchSnapshot();
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    beforeEach(() => {
      setEnableEmployerExemptionsPortal(true);
    });

    const titleText = "Insurance information | Part 4 of 5";
    it(`renders the title text: "${titleText}"`, async () => {
      const { container } = await setup({
        component: EmployerExemptionsSelfInsuredWorkplacePolicyDetails,
      });
      expect(container).toHaveTextContent(titleText);
    });

    const sectionLabel = "Tell us about your workplace policies";
    it(`renders the section label: "${sectionLabel}"`, async () => {
      const { container } = await setup({
        component: EmployerExemptionsSelfInsuredWorkplacePolicyDetails,
      });
      expect(container).toHaveTextContent(sectionLabel);
    });

    const questions = [
      "Does your workplace policy continue to contribute to the employer’s portion to the employee’s employment-related health insurance benefits, if any, at the level and under the conditions coverage would have been provided if the employee had continued working continuously for the duration of such leave?",
      "Does your plan specifically state that all presumptions shall be made in favor of the availability of leave and the payment of leave benefits?",
      "Does your workplace policy ensure that employees are granted the job protections to which they are entitled under the Paid Family and Medical Leave law in the event they take qualified leave?",
      "Does your workplace policy ensure the continuance of employees’ existing rights, if any, to vacation time, sick leave, bonuses, advancement, seniority, length-of-service credit or other employment benefits, plans, or programs upon their return to employment?",
    ];

    questions.forEach((question) => {
      it(`renders question: "${question}"`, async () => {
        await setup({
          component: EmployerExemptionsSelfInsuredWorkplacePolicyDetails,
        });
        assertYesNoQuestionWithText(question);
        expect(screen.queryByText(question)).toBeInTheDocument();
      });
    });
  });
});
