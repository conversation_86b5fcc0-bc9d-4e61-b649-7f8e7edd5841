import {
  assertYesNoQuestionWithText,
  setEnableEmployerExemptionsPortal,
} from "tests/pages/employers/exemption-applications/employer-exemptions-test-utils";

import EmployerExemptionsSelfInsuredIntermittentLeaveDetails from "src/pages/employers/exemption-applications/self-insured/intermittent-leave-details";
import { screen } from "@testing-library/react";
import { setup } from "./self-insured-test-utils";

jest.mock("src/api/EmployerExemptionsApi");

describe("EmployerExemptionsSelfInsuredIntermittentLeaveDetails", () => {
  describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    it("Page not found is displayed", async () => {
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup({
        component: EmployerExemptionsSelfInsuredIntermittentLeaveDetails,
      });

      expect(container).toHaveTextContent("Page not found");
    });

    it("renders intermittent leave details content", async () => {
      const { container } = await setup({
        component: EmployerExemptionsSelfInsuredIntermittentLeaveDetails,
      });
      expect(container).toMatchSnapshot();
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled, the self insured intermittent leave details page", () => {
    beforeEach(() => {
      setEnableEmployerExemptionsPortal(true);
    });

    it("renders the title text", async () => {
      const { container } = await setup({
        component: EmployerExemptionsSelfInsuredIntermittentLeaveDetails,
      });
      expect(container).toHaveTextContent(
        "Insurance information | Part 5 of 5"
      );
    });

    it("renders the section label", async () => {
      const { container } = await setup({
        component: EmployerExemptionsSelfInsuredIntermittentLeaveDetails,
      });
      expect(container).toHaveTextContent(
        /Tell us about your intermittent leave options/i
      );
    });

    // Questions that should appear when hasFamilyExemption is false + hasMedicalExemption is true
    const medicalQuestions = [
      "Does your plan allow for leave for an employee’s own serious health condition to be taken intermittently or on a reduced leave schedule, if medically necessary, with the benefit amount being prorated?",
    ];

    // Questions that should appear when hasFamilyExemption is true + hasMedicalExemption is false
    const familyQuestions = [
      "Does your plan allow for leave to be taken intermittently or on a reduced leave schedule, if medically necessary, with the weekly benefit amount being prorated:",
      "Does your plan allow for leave to be taken intermittently or on a reduced leave schedule, if the employer and employee agree to it, for leave to bond with a child during the first twelve months after the child’s birth, adoption, or foster care placement?",
      "Does your plan allow for leave to be taken intermittently or on a reduced leave schedule due to a qualifying exigency arising out of a family member’s active duty or impending call to active duty in the Armed Forces?",
      medicalQuestions[0],
    ];

    // Questions that should appear when hasFamilyExemption + hasMedicalExemption are both true
    const dualQuestions = [...medicalQuestions, ...familyQuestions];

    // Checking that the bulleted items for the caring leave question render properly
    const caringLeaveBulletItems = [
      /To care for a family member’s serious health condition;/i,
      /To care for a family member who is a covered service member, and/i,
      /For the employee’s own serious health condition?/i,
    ];

    it("renders the correct questions for a family-only exemption", async () => {
      await setup({
        component: EmployerExemptionsSelfInsuredIntermittentLeaveDetails,
        hasFamilyExemption: true,
      });
      familyQuestions.forEach((question) => {
        expect(screen.queryByText(question)).toBeInTheDocument();
        assertYesNoQuestionWithText(question);
      });
      caringLeaveBulletItems.forEach((bullet) => {
        expect(screen.queryByText(bullet)).toBeInTheDocument();
      });
    });

    it("renders the correct questions for a medical-only exemption", async () => {
      await setup({
        component: EmployerExemptionsSelfInsuredIntermittentLeaveDetails,
        hasMedicalExemption: true,
      });
      medicalQuestions.forEach((question) => {
        expect(screen.queryByText(question)).toBeInTheDocument();
        assertYesNoQuestionWithText(question);
      });
    });

    it("renders the correct questions for a dual (family + medical) exemption", async () => {
      await setup({
        component: EmployerExemptionsSelfInsuredIntermittentLeaveDetails,
        hasFamilyExemption: true,
        hasMedicalExemption: true,
      });
      dualQuestions.forEach((question) => {
        expect(screen.queryByText(question)).toBeInTheDocument();
        assertYesNoQuestionWithText(question);
      });
    });
  });
});
