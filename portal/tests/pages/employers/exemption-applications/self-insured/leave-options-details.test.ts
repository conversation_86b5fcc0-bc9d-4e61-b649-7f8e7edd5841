import User, { UserLeaveAdministrator } from "src/models/User";
import { screen, waitFor } from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import EmployerExemptionsSelfInsuredLeaveOptionsDetails from "src/pages/employers/exemption-applications/self-insured/leave-options-details";
import { QuestionInputGetter } from "tests/test-utils/questionInputHelpers";
import { ValidationError } from "src/errors";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import createMockUserLeaveAdministrator from "lib/mock-helpers/createMockUserLeaveAdministrator";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

const verifiedAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Knitting Castle",
  employer_fein: "11-3453443",
  employer_id: "dda930f-93jfk-iej08",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: true,
});

jest.mock("src/api/EmployerExemptionsApi");

async function waitForDataToLoad() {
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });
}

const setup = async (params: {
  employers: UserLeaveAdministrator[];
  hasFamilyExemption?: boolean | null;
  hasMedicalExemption?: boolean | null;
  props?: { [key: string]: unknown };
  errors?: ValidationError[];
}) => {
  const employerExemptionApplication: EmployerExemptionsApplication =
    createMockEmployerExemptionsApplication();
  employerExemptionApplication.employer_id = params.employers[0].employer_id;
  employerExemptionApplication.has_family_exemption =
    params.hasFamilyExemption ?? null;
  employerExemptionApplication.has_medical_exemption =
    params.hasMedicalExemption ?? null;

  const utils = renderPage(
    EmployerExemptionsSelfInsuredLeaveOptionsDetails,
    {
      pathname:
        routes.employers.employerExemptions.selfInsuredLeaveOptionsDetails,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: params.employers,
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>(
            "id",
            params.employers
          );
        appLogic.errors = params.errors ?? [];
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
      ...params.props,
    }
  );

  await waitForDataToLoad();
  return utils;
};

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

describe("EmployerExemptionsSelfInsuredLeaveOptionsDetails", () => {
  const medicalQuestions = [
    "Does your plan provide employees up to 20 weeks of paid leave if they are unable to work due to a serious health condition?",
  ];

  const familyQuestions = [
    "Does your plan provide employees at least 12 weeks of paid leave to bond with a child during the first 12 months after the child's birth, adoption, or foster care placement?",
    "Does your plan provide employees at least 12 weeks to care for a family member with a serious health condition?",
    "If a qualifying exigency arises out of the fact that an employee's spouse, child, or parent is on active duty in the Armed Forces, is the employee eligible for at least 12 weeks of paid family leave under your plan?",
    "Does your plan provide a minimum of 26 paid weeks in a benefit year to care for a family member who is or was a member of the Armed Forces and who requires medical care as a result of illness or injury related to the family member’s active service?",
  ];

  describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    it("Page not found is displayed", async () => {
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup({ employers: [verifiedAdministrator] });

      expect(container).toHaveTextContent("Page not found");
    });

    it("renders leave options details content", async () => {
      const { container } = await setup({ employers: [verifiedAdministrator] });
      expect(container).toMatchSnapshot();
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    beforeEach(() => {
      setEnableEmployerExemptionsPortal(true);
    });

    it("the self insured leave options details page renders the title", async () => {
      const { container } = await setup({ employers: [verifiedAdministrator] });
      expect(container).toHaveTextContent(
        "Insurance information | Part 2 of 5"
      );
    });

    it("the self insured leave options details renders the section label", async () => {
      await setup({ employers: [verifiedAdministrator] });
      expect(
        screen.queryByText(/Tell us about leave options/i)
      ).toBeInTheDocument();
    });

    it("display self-insured family questions when exemption is family only", async () => {
      await setup({
        employers: [verifiedAdministrator],
        hasFamilyExemption: true,
        hasMedicalExemption: false,
      });

      familyQuestions.forEach((question) => {
        const input = new QuestionInputGetter(question);
        expect(input.getRadioInputs().length).toEqual(2);
        expect(screen.queryByText(question)).toBeInTheDocument();
      });

      medicalQuestions.forEach((question) => {
        expect(screen.queryByText(question)).not.toBeInTheDocument();
      });
    });

    it("display self-insured medical questions when exemption is medical only", async () => {
      await setup({
        employers: [verifiedAdministrator],
        hasFamilyExemption: false,
        hasMedicalExemption: true,
      });

      familyQuestions.forEach((question) => {
        expect(screen.queryByText(question)).not.toBeInTheDocument();
      });

      medicalQuestions.forEach((question) => {
        const input = new QuestionInputGetter(question);
        expect(input.getRadioInputs().length).toEqual(2);
        expect(screen.queryByText(question)).toBeInTheDocument();
      });
    });

    it("display self-insured family and medical questions when exemption is both family and medical", async () => {
      await setup({
        employers: [verifiedAdministrator],
        hasFamilyExemption: true,
        hasMedicalExemption: true,
      });
      [...familyQuestions, ...medicalQuestions].forEach((question) => {
        const input = new QuestionInputGetter(question);
        expect(input.getRadioInputs().length).toEqual(2);
        expect(screen.queryByText(question)).toBeInTheDocument();
      });
    });
  });
});
