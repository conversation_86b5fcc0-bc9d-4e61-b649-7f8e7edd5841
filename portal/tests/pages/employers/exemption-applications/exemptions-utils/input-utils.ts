import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";

/*
    Common set of functionality related to getting + interacting with input UI that many exemptions tests will need
*/

export interface InputDefinition {
  name: string;
  type: string;
  label: string;
  errorMsg: string;
}

export const getRadioButton = (name: string | RegExp) => {
  const role = "radio";
  if (screen.queryByRole(role, { name })) {
    return screen.getByRole(role, { name });
  }
  throw new Error("Unable to find radio button: " + name);
};

export const getComboBox = (name: string | RegExp) => {
  const role = "combobox";
  if (screen.queryByRole(role, { name })) {
    return screen.getByRole(role, { name });
  }
  throw new Error("Unable to find combo box: " + name);
};

export const getButtonWithName = (name: string | RegExp) => {
  if (!screen.queryByRole("button", { name })) {
    throw new Error("Unable to find button with name " + name);
  }
  return screen.getByRole("button", { name });
};

export const getLinkWithName = (name: string | RegExp) => {
  if (!screen.queryByRole("link", { name })) {
    throw new Error("Unable to find link with name " + name);
  }
  return screen.getByRole("link", { name });
};

export const clickRadioButton = (buttonName: RegExp | string) => {
  return userEvent.click(getRadioButton(buttonName));
};

export const clickRadioOptionAtRandom = (options: RegExp[]) => {
  const randomOption = options[Math.floor(Math.random() * options.length)];
  return userEvent.click(getRadioButton(randomOption));
};

export const clickSaveAndContinueButton = () => {
  return userEvent.click(getButtonWithName("Save and continue"));
};

/* 
  TODO (PFMLPB-23216)
  Investigate rewriting the underlying logic of createInputOrderAndTypesTest + PageDefinition.inputs
  to rely on a higher-level component/method (e.g. QuestionInputGetter) so we don't need utils like this
  and can be more precise about *where* on the screen we're expecting the label text to show-up
*/
export const inputsForYesNoQuestion = (props: {
  fieldName: string;
  errorMsg: string;
}): InputDefinition[] => {
  return [
    {
      type: "radio",
      name: props.fieldName,
      label: "Yes",
      errorMsg: props.errorMsg,
    },
    {
      type: "radio",
      name: props.fieldName,
      label: "No",
      errorMsg: props.errorMsg,
    },
  ];
};
