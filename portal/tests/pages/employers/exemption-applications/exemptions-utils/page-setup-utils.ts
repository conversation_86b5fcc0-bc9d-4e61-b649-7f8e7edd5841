import User, { UserLeaveAdministrator } from "src/models/User";
import {
  createMockEmployerExemptionsApplication,
  renderPage,
} from "tests/test-utils";
import { screen, waitFor } from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import { ValidationError } from "src/errors";
import createMockUserLeaveAdministrator from "lib/mock-helpers/createMockUserLeaveAdministrator";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

export const setEnableEmployerExemptionsPortal = (value: boolean) => {
  // TODO (PFMLPB-19806): removal of feature flag
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

export const verifiedAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Knitting Castle",
  employer_fein: "11-3453443",
  employer_id: "dda930f-93jfk-iej08",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: true,
});

export async function waitForDataToLoad() {
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });
}

export const exemptionsPageSetup = async (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component: React.ComponentType<any>,
  pathname: string | undefined,
  props: { [key: string]: unknown } = {},
  errors: ValidationError[] = []
) => {
  const employerExemptionApplication: EmployerExemptionsApplication =
    createMockEmployerExemptionsApplication();

  employerExemptionApplication.employer_id = verifiedAdministrator.employer_id;

  const utils = renderPage(
    component,
    {
      pathname,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: [verifiedAdministrator],
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", [
            verifiedAdministrator,
          ]);
        appLogic.errors = errors;
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
      ...props,
    }
  );

  await waitForDataToLoad();
  return utils;
};
