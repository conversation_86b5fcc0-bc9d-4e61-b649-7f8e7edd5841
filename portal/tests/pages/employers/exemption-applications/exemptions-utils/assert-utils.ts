import { InputDefinition } from "./input-utils";
import { QuestionInputGetter } from "tests/test-utils";
import { screen } from "@testing-library/react";

/*
    Common set of asserts that many exemptions tests will need
*/

export function assertYesNoQuestionWithText(questionText: string) {
  const input = new QuestionInputGetter(questionText);
  const radioInputs = input.getRadioInputs();
  expect(radioInputs.length).toEqual(2);
  expect(input.getInput("Yes")).toBeInTheDocument();
  expect(input.getInput("No")).toBeInTheDocument();
}

export function assertInputsWithOrder(
  container: HTMLElement,
  expectedInputFields: InputDefinition[]
) {
  const inputs = container.getElementsByTagName("input");
  expect(inputs.length).toBe(expectedInputFields.length);

  const inputsArr: HTMLInputElement[] = [].slice.call(inputs);
  for (let i = 0; i < expectedInputFields.length; ++i) {
    const inputElt = inputsArr[i];
    expect(expectedInputFields[i].name).toEqual(inputElt.name);
    expect(inputElt.type).toEqual(expectedInputFields[i].type);
    expect(
      screen.getByLabelText(expectedInputFields[i].label)
    ).toBeInTheDocument();
  }
}

export function assertRadioQuestionWithOptions(
  questionText: string,
  questionOptions: RegExp[]
) {
  const input = new QuestionInputGetter(questionText);
  const radioInputs = input.getRadioInputs();
  expect(questionOptions.length).toEqual(radioInputs.length);
  questionOptions.forEach((option) => {
    expect(input.getInputUsingRegex(option)).toBeInTheDocument();
  });
}

export const assertItemsShown = (
  container: HTMLElement,
  items: Array<string | RegExp>
) => {
  items.forEach((element) => {
    expect(container).toHaveTextContent(element);
  });
};

export const assertItemsHidden = (
  container: HTMLElement,
  items: Array<string | RegExp>
) => {
  items.forEach((element) => {
    expect(container).not.toHaveTextContent(element);
  });
};

export const assertLinkWithUrl = (name: string | RegExp, url: string) => {
  const linkElement = screen.getByRole("link", {
    name,
  });
  expect(linkElement).toHaveAttribute("href", url);
};
