import { InputDefinition } from "./input-utils";
import { ValidationError } from "src/errors";

/*
    Common set of error generators + transformers
*/

export const createValidationErrorForRequiredField = (field: string) =>
  new ValidationError([
    {
      field,
      message: `.${field} is required`,
      type: "required",
      namespace: "employer_exemptions",
    },
  ]);

export const createValidationErrorsForRequiredInputs = (
  inputs: InputDefinition[]
) =>
  inputs.map(
    (input) =>
      new ValidationError([
        {
          field: input.name,
          message: `.${input.name} is required`,
          type: "required",
          namespace: "employer_exemptions",
        },
      ])
  );

export const createFieldErrorDefs = (inputs: InputDefinition[]) =>
  inputs.map((input) => {
    return { field: input.name, message: input.errorMsg };
  });
