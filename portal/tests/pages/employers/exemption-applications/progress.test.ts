import {
  TestGenerator,
  createBaseContentTest,
  standardStubTests,
  testDescriptions,
} from "./exemptions-utils/standard-test-handlers";
import {
  assertItemsShown,
  assertLinkWithUrl,
} from "./exemptions-utils/assert-utils";
import {
  exemptionsPageSetup,
  setEnableEmployerExemptionsPortal,
} from "./exemptions-utils/page-setup-utils";
import {
  getButtonWithName,
  getLinkWithName,
} from "./exemptions-utils/input-utils";

import EmployerExemptionsProgress from "src/pages/employers/exemption-applications/progress";
import ExemptionsStep from "src/models/EmployerExemptionsStep";
import routes from "src/routes";

/* eslint-disable jest/expect-expect */
const mockStepCompletion = (value: boolean) =>
  jest
    .spyOn(ExemptionsStep.prototype, "isComplete", "get")
    .mockReturnValue(value);

// TODO (PFMLPB-23216): Investigate adding more thorough test cases for each of these states
const mockStepStatus = (
  status:
    | "disabled"
    | "not_applicable"
    | "completed"
    | "in_progress"
    | "not_started"
) =>
  jest.spyOn(ExemptionsStep.prototype, "status", "get").mockReturnValue(status);

jest.mock("src/api/EmployerExemptionsApi");

const pageDef = {
  name: "EmployerExemptionsProgress",
  component: EmployerExemptionsProgress,
  path: routes.employers.employerExemptions.progress,
  baseContent: [
    /Your in-progress request/i,
    /Complete all sections of the application to submit your exemption request. For resources or additional guidance, go to benefit requirements for private plan exemptions or requirements for self-insured private paid leave plans./i,
  ],
  inputs: [],
};

const setup = async () =>
  await exemptionsPageSetup(
    EmployerExemptionsProgress,
    routes.employers.employerExemptions.progress
  );
const runTests = (generators: TestGenerator[]) =>
  generators.forEach((generator) => {
    const testDef = generator(pageDef);
    it(testDef.name, testDef.handler);
  });

describe(pageDef.name, () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2022, 1, 1));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe(testDescriptions.featureFlagOff, () => {
    runTests(standardStubTests);
  });

  describe(testDescriptions.featureFlagOn, () => {
    beforeEach(() => {
      setEnableEmployerExemptionsPortal(true);
    });

    runTests([createBaseContentTest]);

    // TODO (PFMLPB-23216): Investigate making a standard test variant of this
    it(testDescriptions.displaysCorrectLinks, async () => {
      await setup();
      assertLinkWithUrl(
        /benefit requirements for private plan exemptions/i,
        "https://www.mass.gov/info-details/requirements-for-purchased-private-paid-leave-plans"
      );
      assertLinkWithUrl(
        /requirements for self-insured private paid leave plans/i,
        "https://www.mass.gov/info-details/requirements-for-self-insured-private-paid-leave-plans"
      );
      assertLinkWithUrl(/back to requests/i, "/employers/welcome");
    });

    describe("the step indicators", () => {
      // TODO (PFMLPB-23216): Write tickets to add tests checking UI for each of the possible Step states (e.g. editable vs disabled by prerequisites)
      const stepTitles = [
        /Enter contact information/i,
        /Tell us more about your organization/i,
        /Tell us more about your private plan/i,
        /Upload documentation/i,
      ];
      it("have the correct titles", async () => {
        const { container } = await setup();
        assertItemsShown(container, stepTitles);
      });

      const stepDescriptions = [
        "Provide the contact details of the best people to reach out to at your organization regarding this exemption request. We may contact them if we need more information.",
        "Provide details about your organization’s private paid leave plan and coverage information. You may need to reference your insurance confirmation form for accuracy.",
        "Provide details about your organization’s workforce.",
        "Provide a completed and signed Massachusetts Paid Family and Medical Leave Confirmation of Insurance form.",
      ];
      it("have the correct descriptions", async () => {
        mockStepStatus("not_started");
        const { container } = await setup();
        assertItemsShown(container, stepDescriptions);
      });
    });

    describe("the submit button", () => {
      const submitButtonName = /Review and submit request/i;
      it("is disabled while the application still has incomplete steps", async () => {
        mockStepCompletion(false);
        await setup();
        const button = getButtonWithName(submitButtonName);
        expect(button).toBeDisabled();
      });
      it("is enabled once all steps have been marked as completed", async () => {
        mockStepCompletion(true);
        await setup();
        const button = getLinkWithName(submitButtonName);
        expect(button).toBeEnabled();
      });
    });
  });
});
