import User, { UserLeaveAdministrator } from "src/models/User";
import {
  assertItemsShown,
  expectHyperlink,
  genericTestDescriptions,
  setEnableEmployerExemptionsPortal,
  verifiedAdministrator,
  waitForDataToLoad,
} from "tests/pages/employers/exemption-applications/employer-exemptions-test-utils";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import EmployerExemptionsDocumentUpload from "src/pages/employers/exemption-applications/document-upload";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";

/* eslint-disable jest/expect-expect */
jest.mock("src/api/EmployerExemptionsApi");

const setup = async (params: {
  employers: UserLeaveAdministrator[];
  is_self_insured_plan?: boolean | null;
  props?: { [key: string]: unknown };
}) => {
  const employerExemptionApplication: EmployerExemptionsApplication =
    createMockEmployerExemptionsApplication();
  employerExemptionApplication.employer_id = params.employers[0].employer_id;
  employerExemptionApplication.is_self_insured_plan =
    params.is_self_insured_plan ?? null;

  const utils = renderPage(
    EmployerExemptionsDocumentUpload,
    {
      pathname: routes.employers.employerExemptions.documentUpload,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: params.employers,
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>(
            "id",
            params.employers
          );
        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
      ...params.props,
    }
  );

  await waitForDataToLoad();
  return utils;
};

describe("EmployerExemptionsSelfInsuredDeclarationDocumentUpload", () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2022, 1, 1));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe(genericTestDescriptions.featureFlagOff, () => {
    it(genericTestDescriptions.testCase404, async () => {
      // TODO (PFMLPB-19806): removal of feature flag
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup({ employers: [verifiedAdministrator] });
      expect(container).toHaveTextContent(genericTestDescriptions.notFoundText);
    });

    it(genericTestDescriptions.matchesSnapshot, async () => {
      const { container } = await setup({ employers: [verifiedAdministrator] });
      expect(container).toMatchSnapshot();
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    beforeEach(() => {
      // TODO (PFMLPB-19806): removal of feature flag
      setEnableEmployerExemptionsPortal(true);
    });

    it("the confirmation of insurance document upload page is displayed", async () => {
      const { container } = await setup({
        employers: [verifiedAdministrator],
        is_self_insured_plan: false,
      });
      assertItemsShown(container, [
        /Upload documentation/i,
        /Document requirements/i,
        /Tips for uploading images or PDFs/i,
        /New documents you’re uploading/i,
      ]);

      expectHyperlink(
        /purchased private paid leave plans/i,
        "https://www.mass.gov/info-details/requirements-for-purchased-private-paid-leave-plans"
      );
    });

    it("the self-insured insurance declaration document upload page is displayed", async () => {
      const { container } = await setup({
        employers: [verifiedAdministrator],
        is_self_insured_plan: true,
      });
      assertItemsShown(container, [
        /Upload self-insured insurance declaration document/i,
        /Document requirements/i,
        /Tips for uploading images or PDFs/i,
        /New documents you’re uploading/i,
      ]);

      expectHyperlink(
        /requirements for self-insured private paid leave plans/i,
        "https://www.mass.gov/info-details/requirements-for-self-insured-private-paid-leave-plans"
      );
    });
  });
});
