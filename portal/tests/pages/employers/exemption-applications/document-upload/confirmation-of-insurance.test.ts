import User, { UserLeaveAdministrator } from "src/models/User";
import {
  assertItemsShown,
  expectHyperlink,
  genericTestDescriptions,
  setEnableEmployerExemptionsPortal,
  verifiedAdministrator,
  waitForDataToLoad,
} from "tests/pages/employers/exemption-applications/employer-exemptions-test-utils";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import EmployerExemptionsConfirmationOfInsuranceDocumentUpload from "src/pages/employers/exemption-applications/document-upload/confirmation-of-insurance";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";

/* eslint-disable jest/expect-expect */
jest.mock("src/api/EmployerExemptionsApi");

const setup = async (
  employerExemptionApplication: EmployerExemptionsApplication = createMockEmployerExemptionsApplication()
) => {
  employerExemptionApplication.employer_id = verifiedAdministrator.employer_id;

  const utils = renderPage(
    EmployerExemptionsConfirmationOfInsuranceDocumentUpload,
    {
      pathname: routes.employers.employerExemptions.documentUpload,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: [verifiedAdministrator],
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", [
            verifiedAdministrator,
          ]);
        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
    }
  );

  await waitForDataToLoad();
  return utils;
};

describe("EmployerExemptionsConfirmationOfInsuranceDocumentUpload", () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2022, 1, 1));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe(genericTestDescriptions.featureFlagOff, () => {
    it(genericTestDescriptions.testCase404, async () => {
      // TODO (PFMLPB-19806): removal of feature flag
      // TODO (PFMLPB-24037): Update the Document Download's storybook's index page - Move FF check into index page
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup();
      expect(container).toHaveTextContent(genericTestDescriptions.notFoundText);
    });

    it(genericTestDescriptions.matchesSnapshot, async () => {
      const { container } = await setup();
      expect(container).toMatchSnapshot();
    });
  });

  describe(genericTestDescriptions.featureFlagOn, () => {
    beforeEach(() => {
      // TODO (PFMLPB-19806): removal of feature flag
      // TODO (PFMLPB-24037): Update the Document Download's storybook's index page - Move FF check into index page
      setEnableEmployerExemptionsPortal(true);
    });

    it("page displays the title and description", async () => {
      const { container } = await setup();
      assertItemsShown(container, [
        /Upload documentation/i,
        /Document requirements/i,
        /Tips for uploading images or PDFs/i,
        /New documents you’re uploading/i,
      ]);
    });

    it(genericTestDescriptions.linksRenderProperly, async () => {
      await setup();

      expectHyperlink(
        /purchased private paid leave plans/i,
        "https://www.mass.gov/info-details/requirements-for-purchased-private-paid-leave-plans"
      );
    });
  });
});
