import User, { UserLeaveAdministrator } from "src/models/User";
import { screen, waitFor } from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import EmployerExemptionsLegal from "src/pages/employers/exemption-applications/legal-acknowledgements";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import createMockUserLeaveAdministrator from "lib/mock-helpers/createMockUserLeaveAdministrator";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import userEvent from "@testing-library/user-event";

const verifiedAdministrator = createMockUserLeaveAdministrator({
  employer_dba: "Knitting Castle",
  employer_fein: "11-3453443",
  employer_id: "dda930f-93jfk-iej08",
  has_fineos_registration: true,
  has_verification_data: true,
  verified: true,
});

jest.mock("src/api/EmployerExemptionsApi");

async function waitForDataToLoad() {
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });
}
const setup = async (
  employers: UserLeaveAdministrator[],
  props: { [key: string]: unknown } = {}
) => {
  const employerExemptionApplication: EmployerExemptionsApplication =
    createMockEmployerExemptionsApplication();
  employerExemptionApplication.employer_id = employers[0].employer_id;

  const utils = renderPage(
    EmployerExemptionsLegal,
    {
      pathname: routes.employers.employerExemptions.legal,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: employers,
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", employers);
        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
      ...props,
    }
  );

  await waitForDataToLoad();
  return utils;
};

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

const clickAcknowledgmentCheckbox = () => {
  return userEvent.click(screen.getByRole("checkbox"), {
    advanceTimers: () => jest.advanceTimersByTime(0),
  });
};

describe("EmployerExemptionsLegal", () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2022, 1, 1));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    it("Page not found is displayed", async () => {
      setEnableEmployerExemptionsPortal(false);
      const { container } = await setup([verifiedAdministrator]);

      expect(container).toHaveTextContent("Page not found");
    });

    it("renders legal acknowledgement content", async () => {
      const { container } = await setup([verifiedAdministrator]);
      expect(container).toMatchSnapshot();
    });
  });

  describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    beforeEach(() => {
      setEnableEmployerExemptionsPortal(true);
    });

    it("the legal acknowledgements are displayed", async () => {
      const { container } = await setup([verifiedAdministrator]);
      expect(container).toHaveTextContent("Legal acknowledgements");
      expect(container).toHaveTextContent(
        "All the information provided in this application for an exemption is accurate and true."
      );
      expect(container).toHaveTextContent(
        "including the job- and benefit-protection provisions of MGL c. 175M, 52 and the non-retaliation provisions of M.GLc 175M, 59."
      );
    });

    it("the understand and agree checkbox is unchecked", async () => {
      await setup([verifiedAdministrator]);
      expect(screen.getByRole("checkbox")).not.toBeChecked();
    });

    it("the save and continue button is disabled", async () => {
      await setup([verifiedAdministrator]);
      expect(screen.getByText(/Save and continue/i)).toBeDisabled();
    });

    it("save and continue is enabled when 'understand and agree' checkbox is checked", async () => {
      await setup([verifiedAdministrator]);
      expect(screen.getByRole("checkbox")).not.toBeChecked();
      await clickAcknowledgmentCheckbox();
      expect(screen.getByRole("checkbox")).toBeChecked();
      expect(screen.getByText(/Save and continue/i)).toBeEnabled();
    });

    it("save and continue is disabled when 'understand and agree' checkbox is checked then unchecked", async () => {
      await setup([verifiedAdministrator]);
      expect(screen.getByRole("checkbox")).not.toBeChecked();
      await clickAcknowledgmentCheckbox();
      await clickAcknowledgmentCheckbox();
      expect(screen.getByRole("checkbox")).not.toBeChecked();
      expect(screen.getByText(/Save and continue/i)).toBeDisabled();
    });
  });
});
