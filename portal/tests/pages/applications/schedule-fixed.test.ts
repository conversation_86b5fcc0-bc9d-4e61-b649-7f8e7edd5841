import { MockBenefitsApplicationBuilder, renderPage } from "tests/test-utils";
import { WorkPattern, WorkPatternType } from "src/models/BenefitsApplication";
import { screen, waitFor, within } from "@testing-library/react";

import ScheduleFixed from "src/pages/applications/schedule-fixed";
import { setupBenefitsApplications } from "tests/test-utils/helpers";
import userEvent from "@testing-library/user-event";

const MINUTES_WORKED_PER_WEEK = 60 * 8 * 7;

const defaultClaim = new MockBenefitsApplicationBuilder()
  .continuous()
  .workPattern({
    work_pattern_type: WorkPatternType.fixed,
  })
  .create();

const updateClaim = jest.fn(() => {
  return Promise.resolve();
});

const setup = (claim = defaultClaim) => {
  return renderPage(
    ScheduleFixed,
    {
      addCustomSetup: (appLogic) => {
        setupBenefitsApplications(appLogic, [claim]);
        appLogic.benefitsApplications.update = updateClaim;
      },
    },
    { query: { claim_id: "mock_application_id" } }
  );
};

describe("ScheduleFixed", () => {
  it("renders the page", () => {
    const { container } = setup();
    expect(container).toMatchSnapshot();
  });

  it("displays work schedule values that were previously entered", () => {
    const workPattern = WorkPattern.createWithWeek(MINUTES_WORKED_PER_WEEK);
    const claim = new MockBenefitsApplicationBuilder()
      .continuous()
      .workPattern({
        work_pattern_type: WorkPatternType.fixed,
        work_pattern_days: workPattern.work_pattern_days,
      })
      .create();
    setup(claim);

    const hoursInputs = screen.getAllByRole("textbox", { name: "Hours" });
    hoursInputs.forEach((input) => {
      expect(input).toHaveValue("8");
    });
  });

  it("updates the claim's work_pattern_days and hours_worked_per_week when the page is submitted", async () => {
    setup();

    const sundayGroup = screen.getByRole("group", {
      name: /sunday/i,
    });

    await userEvent.type(
      within(sundayGroup).getByRole("textbox", {
        name: /hours/i,
      }),
      "1"
    );

    const mondayGroup = screen.getByRole("group", {
      name: /monday/i,
    });

    await userEvent.type(
      within(mondayGroup).getByRole("textbox", {
        name: /hours/i,
      }),
      "1"
    );

    const tuesdayGroup = screen.getByRole("group", {
      name: /tuesday/i,
    });

    await userEvent.type(
      within(tuesdayGroup).getByRole("textbox", {
        name: /hours/i,
      }),
      "1"
    );

    const wednesdayGroup = screen.getByRole("group", {
      name: /wednesday/i,
    });

    await userEvent.type(
      within(wednesdayGroup).getByRole("textbox", {
        name: /hours/i,
      }),
      "1"
    );

    const thursdayGroup = screen.getByRole("group", {
      name: /thursday/i,
    });

    await userEvent.type(
      within(thursdayGroup).getByRole("textbox", {
        name: /hours/i,
      }),
      "1"
    );

    const fridayGroup = screen.getByRole("group", {
      name: /friday/i,
    });

    await userEvent.type(
      within(fridayGroup).getByRole("textbox", {
        name: /hours/i,
      }),
      "1"
    );

    const saturdayGroup = screen.getByRole("group", {
      name: /saturday/i,
    });

    await userEvent.type(
      within(saturdayGroup).getByRole("textbox", {
        name: /hours/i,
      }),
      "1"
    );

    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith(
        "mock_application_id",
        expect.objectContaining({
          hours_worked_per_week: 7,
          work_pattern: {
            work_pattern_days: [
              { day_of_week: "Sunday", minutes: 60 },
              { day_of_week: "Monday", minutes: 60 },
              { day_of_week: "Tuesday", minutes: 60 },
              { day_of_week: "Wednesday", minutes: 60 },
              { day_of_week: "Thursday", minutes: 60 },
              { day_of_week: "Friday", minutes: 60 },
              { day_of_week: "Saturday", minutes: 60 },
            ],
          },
        })
      );
    });
  });

  describe("when present on the claim, presents an applicant's employer info", () => {
    const dba = "TEST COMPANY";
    const fein = "12-3456789";

    it("returns generic heading when employer info is missing", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .continuous()
        .workPattern({
          work_pattern_days: [],
          work_pattern_type: WorkPatternType.fixed,
        })
        .create();
      setup(claim);
      expect(
        screen.getByText(/Tell us your work schedule./i)
      ).toBeInTheDocument();
      expect(screen.queryByText(dba)).not.toBeInTheDocument();
      expect(screen.queryByText(fein)).not.toBeInTheDocument();
    });

    it("returns employer-specific heading when employer FEIN is valid", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .continuous()
        .workPattern({
          work_pattern_days: [],
          work_pattern_type: WorkPatternType.fixed,
        })
        .employed()
        .create();
      setup(claim);

      expect(
        screen.getByText(/Tell us your work schedule at/i)
      ).toBeInTheDocument();
      expect(screen.getByText(fein)).toBeInTheDocument();
      expect(screen.queryByText(dba)).not.toBeInTheDocument();
    });

    it("returns employer-specific heading when employer DBA and FEIN is valid", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .continuous()
        .workPattern({
          work_pattern_days: [],
          work_pattern_type: WorkPatternType.fixed,
        })
        .employed()
        .employerDba(dba)
        .create();
      setup(claim);

      expect(
        screen.getByText(/Tell us your work schedule at/i)
      ).toBeInTheDocument();
      expect(screen.getByText(dba)).toBeInTheDocument();
      expect(screen.getByText(fein)).toBeInTheDocument();
    });
  });
});
