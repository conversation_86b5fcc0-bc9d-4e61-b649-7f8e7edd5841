import { MockBenefitsApplicationBuilder, renderPage } from "tests/test-utils";

import BenefitsApplication from "src/models/BenefitsApplication";
import Success from "src/pages/applications/success";
import { screen } from "@testing-library/react";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import { setupBenefitsApplications } from "tests/test-utils/helpers";

const loadBenefitsMetrics = jest.fn(() => {
  return Promise.resolve();
});

const benefitsMetrics = {
  max_benefit_amount: 1149.9,
  max_benefit_year: 2024,
  new_max_benefit_amount: 1170.64,
  new_max_benefit_year: 2025,
};

// Create multiple claim types for testing different scenarios
const medicalClaim = new MockBenefitsApplicationBuilder()
  .completed()
  .continuous({ start_date: "2020-01-01" })
  .medicalLeaveReason()
  .absenceId()
  .create();

const caringClaim = new MockBenefitsApplicationBuilder()
  .completed()
  .continuous({ start_date: "2020-01-01" })
  .caringLeaveReason()
  .absenceId()
  .create();

const pregnancyClaim = new MockBenefitsApplicationBuilder()
  .completed()
  .continuous({ start_date: "2020-01-01" })
  .pregnancyLeaveReason()
  .absenceId()
  .create();

// Setup function that can use any of the claim types
const setup = ({ claim }: { claim: BenefitsApplication }) => {
  return renderPage(
    Success,
    {
      addCustomSetup: (appLogic) => {
        // Use specified claim that's passed in by tests
        setupBenefitsApplications(appLogic, [claim]);
        appLogic.benefitsMetrics.loadBenefitsMetrics = loadBenefitsMetrics;
        appLogic.benefitsMetrics.benefitsMetrics = benefitsMetrics;
      },
    },
    { query: { claim_id: claim.application_id } } // Use the specific claim's ID
  );
};

describe("Success", () => {
  afterAll(() => {
    jest.useRealTimers();
  });

  it("renders the page with medical leave", () => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2023, 1, 1));
    const { container } = setup({ claim: medicalClaim });
    expect(container).toMatchSnapshot();
  });

  it("renders the page with caring leave", () => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2023, 1, 1));
    const { container } = setup({ claim: caringClaim });

    expect(container).toMatchSnapshot();
  });

  it("renders the page with pregnancy leave", () => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2023, 1, 1));
    const { container } = setup({ claim: pregnancyClaim });
    expect(container).toMatchSnapshot();
  });
});

describe("displays the 2025 state benefits", () => {
  it("the 2024 and 2025 maximum benefit amounts are displayed for medical leave", () => {
    const { container } = setup({
      claim: new MockBenefitsApplicationBuilder()
        .continuous({ start_date: "2023-01-01" })
        .medicalLeaveReason()
        .absenceId()
        .create(),
    });
    expect(container).toHaveTextContent(
      "The maximum benefit a person can receive per week is $1,149.90 for a benefit year starting in 2024 and $1,170.64 for a benefit year starting in 2025"
    );
  });

  it("the 2024 and 2025 maximum benefit amounts are displayed for care leave", () => {
    const { container } = setup({
      claim: new MockBenefitsApplicationBuilder()
        .continuous({ start_date: "2023-01-01" })
        .caringLeaveReason()
        .absenceId()
        .create(),
    });
    expect(container).toHaveTextContent(
      "The maximum benefit a person can receive per week is $1,149.90 for a benefit year starting in 2024 and $1,170.64 for a benefit year starting in 2025"
    );
  });

  it("the 2024 and 2025 maximum benefit amounts are displayed for pregnancy leave", () => {
    const { container } = setup({
      claim: new MockBenefitsApplicationBuilder()
        .continuous({ start_date: "2023-01-01" })
        .pregnancyLeaveReason()
        .absenceId()
        .create(),
    });
    expect(container).toHaveTextContent(
      "The maximum benefit a person can receive per week is $1,149.90 for a benefit year starting in 2024 and $1,170.64 for a benefit year starting in 2025"
    );
  });
});

describe("feature flag documentUploadOptional is enabled", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    setFeatureFlags({
      documentUploadOptional: true,
    });
  });

  it("displays incomplete pregnancy leave application title", () => {
    const { container } = setup({ claim: pregnancyClaim });
    expect(container).toHaveTextContent(
      "Your leave for pregnancy or recovery from childbirth application is incomplete"
    );
  });

  it("displays incomplete medical leave application title", () => {
    const { container } = setup({ claim: medicalClaim });
    expect(container).toHaveTextContent(
      "Your medical leave application is incomplete"
    );
  });

  it("displays incomplete caring leave application title", () => {
    const { container } = setup({ claim: caringClaim });
    expect(container).toHaveTextContent(
      "Your caring leave application is incomplete"
    );
  });

  it("displays incomplete documentation banner", () => {
    const { container } = setup({ claim: caringClaim });
    expect(container).toHaveTextContent("Incomplete documentation:");
  });

  it("displays health condition form name for caring leave form link", () => {
    const { container } = setup({ claim: caringClaim });
    expect(container).toHaveTextContent(
      "Certification of Your Family Member’s Serious Health Condition"
    );
    expect(
      screen.getByRole("link", {
        name: "Certification of Your Family Member’s Serious Health Condition form",
      })
    ).toHaveAttribute(
      "href",
      "https://www.mass.gov/doc/certification-of-your-family-members-serious-health-condition-form/download"
    );
  });

  it("displays health condition form name for non-caring leave and form link", () => {
    const { container } = setup({ claim: medicalClaim });
    expect(container).toHaveTextContent(
      "Certification of Your Serious Health Condition"
    );
    expect(
      screen.getByRole("link", {
        name: "Certification of Your Serious Health Condition form",
      })
    ).toHaveAttribute(
      "href",
      "https://www.mass.gov/doc/certification-of-your-serious-health-condition-form/download"
    );
  });
});

describe("feature flag documentUploadOptional is disabled", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    setFeatureFlags({
      documentUploadOptional: false,
    });
  });

  it("displays claims success title", () => {
    const { container } = setup({
      claim: new MockBenefitsApplicationBuilder()
        .continuous({ start_date: "2023-01-01" })
        .caringLeaveReason()
        .absenceId()
        .create(),
    });
    expect(container).toHaveTextContent(/You submitted your application/);
  });
});
