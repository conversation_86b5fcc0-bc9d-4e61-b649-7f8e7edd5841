import {
  DayOfWeek,
  WorkPattern,
  WorkPatternDay,
  WorkPatternType,
} from "src/models/BenefitsApplication";
import { MockBenefitsApplicationBuilder, renderPage } from "tests/test-utils";
import { screen, waitFor } from "@testing-library/react";

import ScheduleVariable from "src/pages/applications/schedule-variable";
import { setupBenefitsApplications } from "tests/test-utils/helpers";
import userEvent from "@testing-library/user-event";

const defaultClaim = new MockBenefitsApplicationBuilder()
  .continuous()
  .workPattern({
    work_pattern_days: [],
    work_pattern_type: WorkPatternType.variable,
  })
  .create();

const updateClaim = jest.fn(() => {
  return Promise.resolve();
});
const setup = (claim = defaultClaim) => {
  return renderPage(
    ScheduleVariable,
    {
      addCustomSetup: (appLogic) => {
        setupBenefitsApplications(appLogic, [claim]);
        appLogic.benefitsApplications.update = updateClaim;
      },
    },
    { query: { claim_id: "mock_application_id" } }
  );
};

describe("ScheduleVariable", () => {
  it("renders the form", () => {
    const { container } = setup();
    expect(container).toMatchSnapshot();
  });

  it("submits hours_worked_per_week and 7 day work pattern when entering hours for the first time", async () => {
    setup();
    await userEvent.type(screen.getByRole("textbox", { name: "Hours" }), "7");
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith(defaultClaim.application_id, {
        hours_worked_per_week: 7,
        work_pattern: {
          work_pattern_days: Object.values(DayOfWeek).map(
            (day_of_week) => new WorkPatternDay({ day_of_week, minutes: 60 })
          ),
        },
      });
    });
  });

  it("submits updated data when user changes their answer", async () => {
    const initialWorkPattern = WorkPattern.createWithWeek(60 * 7); // 1 hour each day
    setup(
      new MockBenefitsApplicationBuilder()
        .continuous()
        .workPattern({
          work_pattern_days: initialWorkPattern.work_pattern_days,
          work_pattern_type: WorkPatternType.variable,
        })
        .create()
    );

    await userEvent.type(
      screen.getByRole("textbox", { name: "Hours" }),
      "{backspace}14"
    );
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        hours_worked_per_week: 14,
        work_pattern: {
          work_pattern_days: Object.values(DayOfWeek).map(
            (day_of_week) => new WorkPatternDay({ day_of_week, minutes: 120 })
          ),
        },
      });
    });
  });

  it("clears the form when the user clears their input", async () => {
    const initialWorkPattern = WorkPattern.createWithWeek(60 * 7); // 1 hour each day
    setup(
      new MockBenefitsApplicationBuilder()
        .continuous()
        .workPattern({
          work_pattern_days: initialWorkPattern.work_pattern_days,
          work_pattern_type: WorkPatternType.variable,
        })
        .create()
    );
    await userEvent.type(
      screen.getByRole("textbox", { name: "Hours" }),
      "{backspace}"
    );
    await userEvent.selectOptions(
      screen.getByRole("combobox", { name: "Minutes" }),
      [""]
    );
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );
    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        hours_worked_per_week: null,
        work_pattern: {
          work_pattern_days: [],
        },
      });
    });
  });

  it("submits data when user doesn't change their answers", async () => {
    const initialWorkPattern = WorkPattern.createWithWeek(60 * 7); // 1 hour each day
    setup(
      new MockBenefitsApplicationBuilder()
        .continuous()
        .workPattern({
          work_pattern_days: initialWorkPattern.work_pattern_days,
          work_pattern_type: WorkPatternType.variable,
        })
        .create()
    );

    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );
    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        hours_worked_per_week: 7,
        work_pattern: {
          work_pattern_days: Object.values(DayOfWeek).map(
            (day_of_week) => new WorkPatternDay({ day_of_week, minutes: 60 })
          ),
        },
      });
    });
  });

  it("creates a blank work pattern when user doesn't enter a time amount", async () => {
    setup();

    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );
    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith(defaultClaim.application_id, {
        hours_worked_per_week: null,
        work_pattern: {
          work_pattern_days: [],
        },
      });
    });
  });

  describe("when present on the claim, presents an applicant's employer info", () => {
    const dba = "TEST COMPANY";
    const fein = "12-3456789";

    it("returns employer-specific heading when employer FEIN is valid", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .continuous()
        .workPattern({
          work_pattern_days: [],
          work_pattern_type: WorkPatternType.variable,
        })
        .employed()
        .create();
      setup(claim);

      expect(
        screen.getByText(/How many hours do you work on average each week at/i)
      ).toBeInTheDocument();
      expect(screen.getByText(fein)).toBeInTheDocument();
      expect(screen.queryByText(dba)).not.toBeInTheDocument();
    });

    it("returns employer-specific heading when employer DBA and FEIN is valid", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .continuous()
        .workPattern({
          work_pattern_days: [],
          work_pattern_type: WorkPatternType.variable,
        })
        .employed()
        .employerDba(dba)
        .create();
      setup(claim);

      expect(
        screen.getByText(/How many hours do you work on average each week at/i)
      ).toBeInTheDocument();
      expect(screen.getByText(dba)).toBeInTheDocument();
      expect(screen.getByText(fein)).toBeInTheDocument();
    });
  });
});
