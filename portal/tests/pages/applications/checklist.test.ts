import { MockBenefitsApplicationBuilder, renderPage } from "tests/test-utils";
import Step, { ClaimSteps } from "src/models/Step";

import { AppLogic } from "src/hooks/useAppLogic";
import { Checklist } from "src/pages/applications/checklist";
import { DocumentType } from "src/models/Document";
import { Issue } from "src/errors";
import LeaveReason from "src/models/LeaveReason";
import applicationSplitBuilder from "lib/mock-helpers/createMockApplicationSplit";
import claimantConfig from "src/flows/claimant";
import dayjs from "dayjs";
import { screen } from "@testing-library/react";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import userEvent from "@testing-library/user-event";

function generateWarningsForStep(name: string): Issue[] | undefined {
  const allSteps = Step.createClaimStepsFromMachine(claimantConfig);

  const step = allSteps.find((s) => s.name === name);

  return step?.fields.map((field) => {
    return {
      field: field.replace(/^claim./, ""),
      message: "Mocked warning",
      namespace: "applications",
    };
  });
}

const renderChecklist = (
  claim = new MockBenefitsApplicationBuilder().create(),
  warnings: Issue[] = [
    {
      field: "first_name",
      message: "first_name is required",
      type: "required",
      namespace: "applications",
    },
  ],
  customProps?: { [key: string]: unknown }
) => {
  const options = {
    pathname: "/applications/checklist",
    addCustomSetup: (appLogic: AppLogic) => {
      appLogic.benefitsApplications.warningsLists = {
        mock_application_id: warnings,
      };
    },
  };
  const props = {
    query: { claim_id: "mock_application_id" },
    claim,
    documents: [],
    ...customProps,
  };
  return renderPage(Checklist, options, props);
};

describe("Checklist", () => {
  it("renders multiple steps when IDV is disabled", () => {
    setFeatureFlags({ enableMmgIDV: false, enableUniversalProfileIDV: false });
    const { container } = renderChecklist();
    expect(container).toMatchSnapshot();
    const titles = screen.getAllByTitle("step list title prefix");
    expect(titles[0]).toHaveTextContent("Part 1");
    expect(titles[1]).toHaveTextContent("Part 2");
    expect(titles[2]).toHaveTextContent("Part 3");
  });

  it("renders multiple steps when IDV is enabled", () => {
    setFeatureFlags({ enableMmgIDV: true, enableUniversalProfileIDV: true });
    const { container } = renderChecklist();
    expect(container).toMatchSnapshot();
    const titles = screen.getAllByTitle("step list title prefix");
    expect(titles[0]).toHaveTextContent("Part 1");
    expect(titles[1]).toHaveTextContent("Part 2");
    expect(titles[2]).toHaveTextContent("Part 3");
  });

  it("initially only the first CTA is enabled and verify id description is displayed", () => {
    setFeatureFlags({ enableMmgIDV: false });
    renderChecklist();
    expect(
      screen.getByText(
        /You can use a variety of documents to verify your identity, but it’s easiest if you have a Massachusetts driver’s license or Massachusetts Identification Card./
      )
    ).toBeInTheDocument();
    expect(screen.getAllByRole("listitem")).toHaveLength(3);
    expect(
      screen.getByText(/Your name as it appears on your ID./)
    ).toBeInTheDocument();
    const start = screen.getByRole("link", {
      name: "Start: Verify your identification",
    });
    expect(start).toHaveClass("usa-button");
  });

  it("displays the updated verify ID description when the feature flag is enabled", () => {
    setFeatureFlags({ enableMmgIDV: true });
    renderChecklist();
    expect(
      screen.getByText(
        /PFML is using MyMassGov to verify your identity so that we know it’s you./
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        /MyMassGov is a secure service that allows members of the public to reuse profile information/
      )
    ).toBeInTheDocument();
    const startCTA = screen.getByRole("link", {
      name: "External. Start: Verify your identification",
    });
    expect(startCTA).toHaveClass("usa-button");
  });

  it("renders the correct step 1 description based on the enableMmgIDV feature flag", () => {
    // Feature flag disabled: should show the default step 1 description
    setFeatureFlags({ enableMmgIDV: false });
    renderChecklist();
    expect(
      screen.getByText(
        /Your progress is automatically saved as you complete the application. You can edit any information you enter in Part 1 until step 5 is completed./
      )
    ).toBeInTheDocument();

    // Feature flag enabled: should show the updated step 1 description
    setFeatureFlags({ enableMmgIDV: true });
    renderChecklist();
    expect(
      screen.getByText(
        /Your progress is automatically saved as you complete the application. You can edit any information you enter in Part 1 until step 6 is completed./
      )
    ).toBeInTheDocument();
  });

  it("other CTA options are disabled", () => {
    renderChecklist();
    expect(
      screen.getByRole("button", {
        name: "Start: Enter employment information",
      })
    ).toBeDisabled();
    expect(
      screen.getByRole("button", {
        name: "Start: Enter leave details",
      })
    ).toBeDisabled();
    expect(
      screen.getByRole("button", {
        name: "Start: Report other leave, benefits, and income",
      })
    ).toBeDisabled();
    expect(
      screen.getByRole("button", {
        name: "Review and submit application",
      })
    ).toBeDisabled();
  });

  it("On click start, user would be routed to expected destination", () => {
    setFeatureFlags({ enableMmgIDV: false });
    renderChecklist();
    expect(
      screen.getByRole("link", {
        name: "Start: Verify your identification",
      })
    ).toHaveAttribute(
      "href",
      "/applications/name?claim_id=mock_application_id"
    );
  });

  it("On click start, for the IDV pilot, user would be routed to expected destination", () => {
    setFeatureFlags({ enableMmgIDV: true });
    renderChecklist();
    expect(
      screen.getByRole("link", {
        name: "External. Start: Verify your identification",
      })
    ).toHaveAttribute("href", "/idv-launch");
  });

  it("when verify your id is submitted, next section renders as expected", () => {
    const warnings = [
      {
        field: "employment_status",
        message: "employment_status is required",
        type: "required",
        namespace: "applications",
      },
      {
        field: "leave_details.employer_notified",
        message: "leave_details.employer_notified is required",
        type: "required",
        namespace: "applications",
      },
      {
        field: "work_pattern.work_pattern_type",
        message: "work_pattern.work_pattern_type is required",
        type: "required",
        namespace: "applications",
      },
    ];
    renderChecklist(new MockBenefitsApplicationBuilder().create(), warnings);
    expect(screen.getByText(/Completed/)).toBeInTheDocument();
    expect(
      screen.getByRole("link", { name: "Edit: Verify your identification" })
    ).toBeInTheDocument();
    expect(
      screen.getByText("The date you told your employer you were taking leave.")
    ).toBeInTheDocument();
    expect(
      screen.getByRole("link", {
        name: "Start: Enter employment information",
      })
    ).toHaveAttribute(
      "href",
      "/applications/employment-status?claim_id=mock_application_id"
    );
    expect(
      screen.getByRole("link", {
        name: "Start: Enter employment information",
      })
    ).toBeEnabled();
    expect(
      screen.getByRole("link", {
        name: "you must create separate applications for each job.",
      })
    ).toHaveAttribute("href");
  });

  describe("Title for final step in Part 1 renders based on enableMmgIDV feature flag", () => {
    it("renders Review and confirm when enableMmgIDV is disabled", () => {
      setFeatureFlags({ enableMmgIDV: false });
      renderChecklist();
      expect(
        screen.getByRole("heading", { name: "Review and confirm" })
      ).toBeInTheDocument();
    });
    it("renders Review and submit when enableMmgIDV is enabled", () => {
      setFeatureFlags({ enableMmgIDV: true });
      renderChecklist();
      expect(
        screen.getByRole("heading", { name: "Review and submit" })
      ).toBeInTheDocument();
    });
  });

  describe("Part 1 - Step 4 Not Started", () => {
    const application = new MockBenefitsApplicationBuilder()
      .employed()
      .create();
    const warnings = generateWarningsForStep(ClaimSteps.otherLeave);

    // Date must be set for FF and content to be displayed
    jest.useFakeTimers();
    jest.setSystemTime(new Date("2023-11-02T12:00:00-04:00"));

    it("does display correct content after PTO legislation date", () => {
      renderChecklist(application, warnings);

      expect(
        screen.queryByText(
          /Learn how other leave and benefits can affect your Paid Family and Medical Leave/
        )
      ).toBeInTheDocument();
    });
    jest.useRealTimers();
  });

  describe("Part 1 - Step 5 Not Started", () => {
    const application = new MockBenefitsApplicationBuilder()
      .continuous()
      .create();
    const warnings: Issue[] = [];

    it("displays Step 5 content", () => {
      renderChecklist(application, warnings);

      expect(
        screen.queryByText(
          /Once you confirm your leave information, we’ll notify your employer./
        )
      ).toBeInTheDocument();
    });

    describe("with application spanning benefit years", () => {
      beforeEach(() => {
        const application_split = applicationSplitBuilder(
          10,
          "foo"
        ).computed_application_split;
        application.computed_application_split = application_split;
      });

      describe("with application splitting disabled", () => {
        const bothLeavesReviewable =
          /The leave dates you’re requesting extend into a new benefit year\./;
        const onlyFirstLeaveReviewable =
          /you will need to submit a separate application on or after/;

        describe("with both leaves reviewable today", () => {
          beforeEach(() => {
            // set earliest submittable date for the second leave in the past
            const application_split = application.computed_application_split;
            if (application_split !== null) {
              const submittable_date = dayjs()
                .subtract(30, "day")
                .toISOString();
              application_split.application_outside_benefit_year_submittable_on =
                submittable_date;
            }
          });

          it("displays a Benefit Year alert with accurate content for scenario", () => {
            renderChecklist(application, warnings);

            expect(
              screen.queryByText(bothLeavesReviewable)
            ).toBeInTheDocument();
          });
        });

        describe("with only leave in first Benefit Year reviewable today", () => {
          beforeEach(() => {
            // set earliest submittable date for the second leave > 60 days in the future
            const application_split = application.computed_application_split;
            if (application_split !== null) {
              const not_submittable_date = dayjs().add(65, "day").toISOString();
              application_split.application_outside_benefit_year_submittable_on =
                not_submittable_date;
            }
          });

          it("displays a Benefit Year alert with accurate content for scenario", () => {
            renderChecklist(application, warnings);

            expect(
              screen.queryByText(onlyFirstLeaveReviewable)
            ).toBeInTheDocument();
          });
        });

        describe("with neither leave reviewable today", () => {
          beforeEach(() => {
            // set earliest submittable date for the application in the future
            application.computed_earliest_submission_date = dayjs()
              .add(1, "day")
              .toISOString();
          });

          it("does not display a Benefit Year alert", () => {
            renderChecklist(application, warnings);

            expect(
              screen.queryByText(bothLeavesReviewable)
            ).not.toBeInTheDocument();

            expect(
              screen.queryByText(onlyFirstLeaveReviewable)
            ).not.toBeInTheDocument();
          });
        });
      });
    });
  });

  describe("Part 2", () => {
    const setup = () => {
      const warnings: Issue[] = [];
      const customProps = {
        query: {
          claim_id: "mock_application_id",
          absence_id: "mock_absence_id",
          "part-one-submitted": "true",
        },
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder().submitted().create(),
        warnings,
        customProps
      );
    };

    it("renders alert", () => {
      setup();
      expect(
        screen.getByText(/You successfully submitted Part 1./)
      ).toBeInTheDocument();
      expect(screen.getByText(/Your application ID is/)).toBeInTheDocument();
    });

    it("renders prior steps in black", () => {
      setup();
      for (let step = 1; step <= 5; step++) {
        expect(screen.getByLabelText(`Step ${step}`)).toHaveClass("bg-black");
      }
    });

    it("renders with 9 steps including tax", () => {
      setup();
      for (let step = 1; step <= 9; step++) {
        expect(screen.getByLabelText(`Step ${step}`)).toBeInTheDocument();
      }
    });

    it("renders payment heading and description", () => {
      setup();
      expect(
        screen.getByRole("heading", { name: "Enter payment method" })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("heading", {
          name: "Enter tax withholding preference",
        })
      ).toBeInTheDocument();
    });

    it("CTA for tax withholding directs user to expected location", () => {
      setup();
      expect(
        screen.getByRole("link", {
          name: "Start: Enter tax withholding preference",
        })
      ).toBeEnabled();
      expect(
        screen.getByRole("link", {
          name: "Start: Enter tax withholding preference",
        })
      ).toHaveAttribute(
        "href",
        "/applications/tax-withholding?claim_id=mock_application_id&absence_id=NTN-111-ABS-01"
      );
    });
  });

  describe("Part 2 in manual review", () => {
    const setup = () => {
      const warnings: Issue[] = [];
      const customProps = {
        query: {
          claim_id: "mock_application_id",
          "part-one-in-review": "true",
        },
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder()
          .submittedForManualReview()
          .create(),
        warnings,
        customProps
      );
    };

    it("renders alert", () => {
      setup();
      expect(
        screen.getByText(/We couldn't find an association/)
      ).toBeInTheDocument();
      expect(
        screen.getAllByText(
          /Your application was submitted for manual review/
        )[1]
      ).toBeInTheDocument();
    });

    it("renders prior steps in black", () => {
      setup();
      for (let step = 1; step <= 7; step++) {
        expect(screen.getByLabelText(`Step ${step}`)).toHaveClass("bg-black");
      }
    });

    it("renders with 9 steps including tax", () => {
      setup();
      for (let step = 1; step <= 9; step++) {
        expect(screen.getByLabelText(`Step ${step}`)).toBeInTheDocument();
      }
    });

    it("renders payment heading and description", () => {
      setup();
      expect(
        screen.getByRole("heading", { name: "Enter payment method" })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("heading", {
          name: "Enter tax withholding preference",
        })
      ).toBeInTheDocument();
    });

    it("CTAs are not there", () => {
      setFeatureFlags({ enableMmgIDV: false });
      setup();
      // payment shows as black indicating done
      expect(screen.getByLabelText("Step 6")).toHaveClass("bg-black");
      // tax shows as black indicating done
      expect(screen.getByLabelText("Step 7")).toHaveClass("bg-black");

      expect(
        screen.getByRole("button", {
          name: "Start: Upload proof of identity",
        })
      ).toBeDisabled();
    });

    it("For the IDV pilot, CTAs are not there", () => {
      setFeatureFlags({ enableMmgIDV: true });
      setup();
      // payment shows as black indicating done
      expect(screen.getByLabelText("Step 6")).toHaveClass("bg-black");
      // tax shows as black indicating done
      expect(screen.getByLabelText("Step 7")).toHaveClass("bg-black");

      expect(
        screen.getByRole("button", {
          name: "Start: Upload leave certification documents",
        })
      ).toBeDisabled();
    });
  });

  it("with tax withholding incomplete, upload steps still disabled", () => {
    setFeatureFlags({ enableMmgIDV: false });
    renderChecklist(
      new MockBenefitsApplicationBuilder()
        .part1Complete()
        .submitted()
        .paymentPrefSubmitted()
        .create(),
      [],
      {
        query: {
          claim_id: "mock_application_id",
          "payment-pref-submitted": "true",
        },
      }
    );
    // payment shows as black indicating done
    expect(screen.getByLabelText("Step 6")).toHaveClass("bg-black");
    // tax shows as green with start option enabled
    expect(screen.getByLabelText("Step 7")).toHaveClass("bg-secondary");
    expect(
      screen.getByRole("link", {
        name: "Start: Enter tax withholding preference",
      })
    ).toBeEnabled();
    // custom description
    expect(
      screen.getByText(
        /If you need to edit your information in Part 2, you’ll need to call the Contact Center/
      )
    ).toBeInTheDocument();
    // upload option is disabled
    expect(
      screen.getByRole("button", {
        name: "Start: Upload proof of identity",
      })
    ).toBeDisabled();
  });

  it("with tax withholding done & payment not done, submitted description displays", () => {
    setFeatureFlags({ enableMmgIDV: false });
    renderChecklist(
      new MockBenefitsApplicationBuilder()
        .part1Complete()
        .submitted()
        .taxPrefSubmitted()
        .create(),
      [],
      {
        query: {
          claim_id: "mock_application_id",
          "payment-pref-submitted": "true",
        },
      }
    );
    // custom description
    expect(
      screen.getByText(
        /If you need to edit your information in Part 2, you’ll need to call the Contact Center/
      )
    ).toBeInTheDocument();
    // upload option is disabled
    expect(
      screen.getByRole("button", {
        name: "Start: Upload proof of identity",
      })
    ).toBeDisabled();
  });

  it("for IDV pilot, with tax withholding done & payment not done, submitted description displays", () => {
    setFeatureFlags({ enableMmgIDV: true });
    renderChecklist(
      new MockBenefitsApplicationBuilder()
        .part1Complete()
        .submitted()
        .taxPrefSubmitted()
        .create(),
      [],
      {
        query: {
          claim_id: "mock_application_id",
          "payment-pref-submitted": "true",
        },
      }
    );
    // custom description
    expect(
      screen.getByText(
        /If you need to edit your information in Part 2, you’ll need to call the Contact Center/
      )
    ).toBeInTheDocument();
    // upload option is disabled
    expect(
      screen.getByRole("button", {
        name: "Start: Upload leave certification documents",
      })
    ).toBeDisabled();
  });

  describe("Part 3", () => {
    const setup = () => {
      const warnings: Issue[] = [];
      const customProps = {
        query: {
          claim_id: "mock_application_id",
          "payment-pref-submitted": "true",
        },
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder()
          .complete()
          .directDeposit()
          .create(),
        warnings,
        customProps
      );
    };

    it("renders alert that Part 2 is confirmed", () => {
      setup();
      expect(
        screen.getByText(
          /You successfully submitted your payment method. Complete the remaining steps so that you can submit your application./
        )
      ).toBeInTheDocument();
    });

    it("renders prior step in black", () => {
      setup();
      for (let step = 1; step <= 6; step++) {
        expect(screen.getByLabelText(`Step ${step}`)).toHaveClass("bg-black");
      }
    });

    it("renders documents heading and description", () => {
      setFeatureFlags({ enableMmgIDV: false });
      setup();
      expect(screen.getByText("Upload required documents")).toBeInTheDocument();
      expect(
        screen.getByText(
          /Upload proof of identity. If you entered a Massachusetts driver’s license or Mass ID number in step 1, upload the same ID./
        )
      ).toBeInTheDocument();
      expect(
        screen.getByRole("link", {
          name: "Start: Upload proof of identity",
        })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("link", { name: "fax or mail documents" })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("link", {
          name: "Certification of Your Serious Health Condition",
        })
      ).toBeInTheDocument();
    });

    it("for the IDV pilot, renders documents heading and description", () => {
      setFeatureFlags({ enableMmgIDV: true });
      setup();
      expect(screen.getByText("Upload required documents")).toBeInTheDocument();
      expect(
        screen.getByRole("link", { name: "fax or mail documents" })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("link", {
          name: "Certification of Your Serious Health Condition",
        })
      ).toBeInTheDocument();
    });

    it("renders two CTA options that would direct to expected locations", () => {
      setFeatureFlags({ enableMmgIDV: false });
      setup();
      expect(
        screen.getByRole("link", {
          name: "Start: Upload proof of identity",
        })
      ).toBeEnabled();
      expect(
        screen.getByRole("link", {
          name: "Start: Upload leave certification documents",
        })
      ).toBeEnabled();
      expect(
        screen.getByRole("link", {
          name: "Start: Upload proof of identity",
        })
      ).toHaveAttribute(
        "href",
        "/applications/upload/state-id?claim_id=mock_application_id&absence_id=NTN-111-ABS-01&is_additional_doc=false"
      );
      expect(
        screen.getByRole("link", {
          name: "Start: Upload leave certification documents",
        })
      ).toHaveAttribute(
        "href",
        "/applications/upload-certification-type?claim_id=mock_application_id&absence_id=NTN-111-ABS-01&is_additional_doc=false"
      );
    });
    it("for the IDV Pilot, renders one CTA options that would direct to expected locations", () => {
      setFeatureFlags({ enableMmgIDV: true });
      setup();
      expect(
        screen.getByRole("link", {
          name: "Start: Upload leave certification documents",
        })
      ).toBeEnabled();
      expect(
        screen.getByRole("link", {
          name: "Start: Upload leave certification documents",
        })
      ).toHaveAttribute(
        "href",
        "/applications/upload-certification-type?claim_id=mock_application_id&absence_id=NTN-111-ABS-01&is_additional_doc=false"
      );
    });
  });

  describe("Part 3, with Healthcare Provider Form as cert doc", () => {
    const setup = () => {
      const application = new MockBenefitsApplicationBuilder()
        .complete()
        .medicalLeaveReason()
        .create();
      const documents = [
        {
          application_id: "mock-claim-id",
          document_type: DocumentType.identityVerification,
        },
        {
          application_id: "mock-claim-id",
          document_type: DocumentType.certification.healthcareProviderForm,
        },
      ];

      renderChecklist(application, [], { documents });
    };

    it("no longer has instructions for uploading cert doc", () => {
      setup();
      const uploadCertDocText = [
        /Provide your completed/,
        /Certification of Your Serious Health Condition/,
        /Your certification documents will be shared with your employer as part of your leave application./,
      ];

      uploadCertDocText.forEach((text) => {
        expect(screen.queryByText(text)).not.toBeInTheDocument();
      });
    });

    it("marks part 3 sections as completed", () => {
      setup();
      const numCompletedSectionsInPart3 =
        screen.getAllByText(/Completed:/).length;
      expect(numCompletedSectionsInPart3).toEqual(2);
    });
  });

  describe("Part 3, varied leave reasons", () => {
    it("renders proper description for leave reason bonding new born", () => {
      const warnings: Issue[] = [];
      const customProps = {
        query: {
          claim_id: "mock_application_id",
          "payment-pref-submitted": "true",
        },
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder()
          .complete()
          .directDeposit()
          .bondingBirthLeaveReason()
          .create(),
        warnings,
        customProps
      );
      expect(
        screen.getByText(
          /You need to provide your child’s birth certificate or a document from a health care provider that shows the child’s date of birth./
        )
      ).toBeInTheDocument();
    });

    it("renders proper description for leave reason adoptions", () => {
      const warnings: Issue[] = [];
      const customProps = {
        query: {
          claim_id: "mock_application_id",
          "payment-pref-submitted": "true",
        },
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder()
          .complete()
          .directDeposit()
          .bondingAdoptionLeaveReason()
          .create(),
        warnings,
        customProps
      );
      expect(
        screen.getByText(
          /You need to provide a statement confirming the placement and the date of placement./
        )
      ).toBeInTheDocument();
    });

    it("renders proper description for leave reason medical", () => {
      const warnings: Issue[] = [];
      const customProps = {
        query: {
          claim_id: "mock_application_id",
          "payment-pref-submitted": "true",
        },
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder()
          .complete()
          .directDeposit()
          .medicalLeaveReason()
          .create(),
        warnings,
        customProps
      );
      expect(
        screen.getByRole("link", {
          name: "Certification of Your Serious Health Condition",
        })
      ).toBeInTheDocument();
    });

    it("renders proper description for leave reason care", () => {
      const warnings: Issue[] = [];
      const customProps = {
        query: {
          claim_id: "mock_application_id",
          "payment-pref-submitted": "true",
        },
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder()
          .complete()
          .directDeposit()
          .caringLeaveReason()
          .create(),
        warnings,
        customProps
      );
      expect(
        screen.getByText(/Upload leave certification documents/)
      ).toBeInTheDocument();
      expect(
        screen.getByRole("link", {
          name: "Certification of Your Family Member’s Serious Health Condition",
        })
      ).toBeInTheDocument();
    });
  });

  describe("Submit", () => {
    it("enables Review and Submit once all portions are completed", () => {
      const warnings: Issue[] = [];
      const customProps = {
        documents: [
          {
            application_id: "mock-claim-id",
            document_type: DocumentType.certification[LeaveReason.pregnancy],
          },
          {
            application_id: "mock-claim-id",
            document_type: DocumentType.identityVerification,
          },
        ],
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder()
          .complete()
          .pregnancyLeaveReason()
          .create(),
        warnings,
        customProps
      );
      expect(
        screen.getByRole("link", { name: "Review and submit application" })
      ).toBeEnabled();
      expect(
        screen.getByRole("link", { name: "Review and submit application" })
      ).toHaveAttribute(
        "href",
        "/applications/review?claim_id=mock_application_id"
      );
    });

    it("does not enable Review and Submit when there is a no certification document", () => {
      const warnings: Issue[] = [];
      const customProps = {
        documents: [
          {
            application_id: "mock-claim-id",
            document_type: DocumentType.requestForInfoNotice,
          },
          {
            application_id: "mock-claim-id",
            document_type: DocumentType.identityVerification,
          },
        ],
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder()
          .complete()
          .caringLeaveReason()
          .create(),
        warnings,
        customProps
      );
      expect(
        screen.getByRole("button", { name: "Review and submit application" })
      ).toBeDisabled();
    });

    it("does enable Review and Submit when certificate document is deferred", async () => {
      setFeatureFlags({ documentUploadOptional: true });
      const warnings: Issue[] = [];
      const customProps = {
        documents: [
          {
            application_id: "mock-claim-id",
            document_type: DocumentType.requestForInfoNotice,
          },
          {
            application_id: "mock-claim-id",
            document_type: DocumentType.identityVerification,
          },
        ],
      };
      renderChecklist(
        new MockBenefitsApplicationBuilder()
          .complete()
          .medicalLeaveReason()
          .create(),
        warnings,
        customProps
      );
      const checkbox = screen.getByLabelText(
        "Select this box if you or your health care provider are faxing or mailing your documents."
      );
      await userEvent.click(checkbox);
      expect(
        screen.getByRole("link", { name: "Review and submit application" })
      ).toBeEnabled();
      expect(
        screen.getByRole("link", { name: "Review and submit application" })
      ).toHaveAttribute(
        "href",
        "/applications/review?claim_id=mock_application_id"
      );
    });
  });
});

it("does enable Review and Submit when pregnancy certificate document is deferred", async () => {
  setFeatureFlags({ documentUploadOptional: true });
  const warnings: Issue[] = [];
  const customProps = {
    documents: [
      {
        application_id: "mock-claim-id",
        document_type: DocumentType.requestForInfoNotice,
      },
      {
        application_id: "mock-claim-id",
        document_type: DocumentType.identityVerification,
      },
    ],
  };
  renderChecklist(
    new MockBenefitsApplicationBuilder()
      .complete()
      .pregnancyLeaveReason()
      .create(),
    warnings,
    customProps
  );
  const checkbox = screen.getByLabelText(
    "Select this box if you or your health care provider are faxing or mailing your documents."
  );
  await userEvent.click(checkbox);
  expect(
    screen.getByRole("link", { name: "Review and submit application" })
  ).toBeEnabled();
  expect(
    screen.getByRole("link", { name: "Review and submit application" })
  ).toHaveAttribute(
    "href",
    "/applications/review?claim_id=mock_application_id"
  );
});
