// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Status Appeals renders Continue Appeal action when an appeal is in-progress 1`] = `
<a
  class="usa-button usa-button--outline"
  href="/applications/appeals/reason?appeal_id=mock-appeal-id"
>
  Continue appeal
</a>
`;

exports[`Status Appeals renders Start Appeal action when at least one absence period has a final decision 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="appeals"
  id="appeals"
>
  <h2
    class="font-heading-md text-bold"
  >
    Appeal the decision
  </h2>
  <div>
    We have made a decision for this application. If you would like to appeal that decision, you can start an appeal online or do so using mail or fax.
  </div>
  <p>
    <a
      class="usa-button usa-button--outline"
      href="/applications/appeals/start?absence_id=mock-absence-case-id"
    >
      Start an appeal
    </a>
  </p>
</div>
`;

exports[`Status Appeals renders Upload Documents content when an appeal is submitted 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="appeals"
  id="appeals"
>
  <h2
    class="font-heading-md text-bold"
  >
    Appeal the decision
  </h2>
  <div>
    <p
      class="text-bold"
      title="claims status appeal submitted body"
    >
      Submitted: 
      <var>
        1/1/2022
      </var>
    </p>
    <p
      class="text-bold"
      title="claims status appeal submitted documents uploaded"
    >
      Documents uploaded: 
      <var>
        0
      </var>
    </p>
    <p>
      If needed, you can send in additional documents by uploading them.
    </p>
  </div>
  <p>
    <a
      class="usa-button usa-button--outline"
      href="/applications/appeals/upload?appeal_id=mock-appeal-id"
    >
      Add appeals documents
    </a>
  </p>
</div>
`;

exports[`Status Appeals renders the number of documents and matches snapshot 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="appeals"
  id="appeals"
>
  <h2
    class="font-heading-md text-bold"
  >
    Appeal the decision
  </h2>
  <div>
    <p
      class="text-bold"
      title="claims status appeal submitted body"
    >
      Submitted: 
      <var>
        1/1/2022
      </var>
    </p>
    <p
      class="text-bold"
      title="claims status appeal submitted documents uploaded"
    >
      Documents uploaded: 
      <var>
        1
      </var>
    </p>
    <p>
      If needed, you can send in additional documents by uploading them.
    </p>
  </div>
  <p>
    <a
      class="usa-button usa-button--outline"
      href="/applications/appeals/upload?appeal_id=mock-appeal-id"
    >
      Add appeals documents
    </a>
  </p>
</div>
`;

exports[`Status Change Request flow RequestChange displays 30 days past leave end notice if claim end date is 30 or more days ago 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="requestChange"
>
  <h2
    class="font-heading-md text-bold"
  >
    Request a change to your application
  </h2>
  <p>
    Since your leave ended more than 30 days ago, you’ll need to call the Contact Center at 
    <a
      href="tel:(*************"
    >
      (833) 344‑7365
    </a>
     to request a change.
  </p>
  <p>
    Read more about 
    <a
      href="https://www.mass.gov/info-details/updating-or-extending-your-approved-paid-family-or-medical-leave"
      rel="noopener noreferrer"
      target="_blank"
    >
      updating or extending your application
    </a>
    .
  </p>
</div>
`;

exports[`Status Change Request flow RequestChange displays call center alt text if all claim statuses are pending 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="requestChange"
>
  <h2
    class="font-heading-md text-bold"
  >
    Request a change to your application
  </h2>
  <p>
    To make changes while we are reviewing your application, call the Contact Center at 
    <a
      href="tel:(*************"
    >
      (833) 344‑7365
    </a>
    . If you request a change to your start and end dates, we may need to review your application again.
  </p>
  <p>
    Once your application is approved you will be able to make changes to your leave dates in this portal. Learn more about 
    <a
      href="https://www.mass.gov/info-details/updating-or-extending-your-approved-paid-family-or-medical-leave"
      rel="noopener noreferrer"
      target="_blank"
    >
      updating or extending your application
    </a>
    .
  </p>
</div>
`;

exports[`Status Change Request flow RequestChange displays call center alt text if claim stasues are at least one approved and one non-approved 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="requestChange"
>
  <h2
    class="font-heading-md text-bold"
  >
    Request a change to your application
  </h2>
  <p>
    To make changes while we are reviewing your application, call the Contact Center at 
    <a
      href="tel:(*************"
    >
      (833) 344‑7365
    </a>
    . If you request a change to your start and end dates, we may need to review your application again.
  </p>
  <p>
    Once your application is approved you will be able to make changes to your leave dates in this portal. Learn more about 
    <a
      href="https://www.mass.gov/info-details/updating-or-extending-your-approved-paid-family-or-medical-leave"
      rel="noopener noreferrer"
      target="_blank"
    >
      updating or extending your application
    </a>
    .
  </p>
</div>
`;

exports[`Status Change Request flow RequestChange displays request a change section if all of the claim statuses on an application are Approved 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="requestChange"
>
  <h2
    class="font-heading-md text-bold"
  >
    Request a change to your application
  </h2>
  <div>
    <ul
      class="usa-list"
    >
      <li>
        End your leave early
      </li>
      <li>
        Extend your leave
      </li>
    </ul>
  </div>
  <a
    class="usa-button margin-top-3 usa-button--outline"
    href="/applications/modify?absence_id=mock-absence-case-id"
  >
    Start a change request
  </a>
</div>
`;

exports[`Status Change Request flow RequestChange displays withdraw a change section if all periods are pending 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="withdrawChange"
>
  <div>
    <h2
      class="font-heading-md text-bold"
    >
      Withdraw your request for leave
    </h2>
  </div>
  <a
    class="usa-button margin-top-3 usa-button--outline"
    href="/applications/modify/withdraw-leave?claim_id=mock-application-id&absence_id=mock-absence-case-id"
  >
    Start a request
  </a>
</div>
`;

exports[`Status Change Request flow TransitionToBonding displays transition to bonding section if all of the pregnancy leave claim statuses on an application are Approved 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="transitionToBonding"
>
  <div>
    <h2
      class="font-heading-md text-bold"
    >
      Add leave to bond with your child
    </h2>
    <p>
      You may be able to take up to 12 weeks of paid family leave to bond with your child after your medical leave ends. Start your request to add family leave to bond with your child.
    </p>
  </div>
  <a
    class="usa-button margin-top-3 usa-button--outline"
    href="/applications/modify/med-to-bonding?absence_id=mock-absence-case-id"
  >
    Add leave to bond with a child
  </a>
</div>
`;

exports[`Status Change Request flow has submitted change request displays change request history if a submitted change request exists 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="changeRequestHistory"
>
  <div>
    <h2
      class="font-heading-md text-bold"
    >
      Change request history
    </h2>
    <p>
      When we make a decision about your change request you will receive a notice and your leave details will be updated. If you made requests to change your application over the phone, those requests will not appear in this history.
    </p>
    <p>
      <strong>
        Change requests you've submitted
      </strong>
    </p>
    <div
      class="padding-bottom-2"
    >
      Extend leave to 
      <var>
        October 1, 2022
      </var>
      <br />
      <div
        class="text-base-dark"
      >
        Submitted on 
        <var>
          December 1, 2022
        </var>
      </div>
    </div>
  </div>
</div>
`;

exports[`Status displays a description for the Approved request decision 1`] = `
<div
  data-testid="leaveStatusMessage"
  title="leave status message"
>
  <strong>
    This leave was approved.
  </strong>
  <p>
    View your approval notice below for more details about your benefit amount, and how to appeal if your benefits appear incorrect. Learn more about the 
    <a
      href="https://mass.gov/pfml/appeal"
      rel="noopener noreferrer"
      target="_blank"
    >
      appeal process
    </a>
    .
  </p>
</div>
`;

exports[`Status displays a description for the Cancelled request decision 1`] = `
<div
  data-testid="leaveStatusMessage"
  title="leave status message"
>
  <strong>
    This leave was cancelled.
  </strong>
  <p>
    This application will no longer be processed. If you want to apply for paid leave again, you can begin another application.
  </p>
  <p>
    <a
      href="/applications/get-ready"
    >
      Start another application
    </a>
  </p>
</div>
`;

exports[`Status displays a description for the Denied request decision 1`] = `
<div
  data-testid="leaveStatusMessage"
  title="leave status message"
>
  <strong>
    This leave was denied.
  </strong>
  <p>
    View your denial notice below for more details and an explanation of the appeal process.
  </p>
  <p>
    If you would like to appeal, you must 
    <a
      href="#appeals"
    >
      submit your request
    </a>
     within 10 calendar days of the date on your denial notice. Learn more about the 
    <a
      href="https://mass.gov/pfml/appeal"
      rel="noopener noreferrer"
      target="_blank"
    >
      appeal process
    </a>
    .
  </p>
</div>
`;

exports[`Status displays a description for the In Review request decision 1`] = `
<div
  data-testid="leaveStatusMessage"
  title="leave status message"
>
  <strong>
    We’re reviewing your requested changes or appeal for this leave.
  </strong>
</div>
`;

exports[`Status displays a description for the Voided request decision 1`] = `
<div
  data-testid="leaveStatusMessage"
  title="leave status message"
>
  <strong>
    This leave was cancelled.
  </strong>
  <p>
    This application will no longer be processed. If you want to apply for paid leave again, you can begin another application.
  </p>
  <p>
    <a
      href="/applications/get-ready"
    >
      Start another application
    </a>
  </p>
</div>
`;

exports[`Status displays a description for the Withdrawn request decision 1`] = `
<div
  data-testid="leaveStatusMessage"
  title="leave status message"
>
  <strong>
    This leave was withdrawn.
  </strong>
  <p>
    You have withdrawn your application from the review process. If you want to apply for paid leave again, you can begin another application.
  </p>
  <p>
    <a
      href="/applications/get-ready"
    >
      Start another application
    </a>
  </p>
</div>
`;

exports[`Status has an in progress change request change request can no longer be submitted displays informative text if a claim is 30 days past the end date 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="inProgressChangeRequest"
>
  <h2
    class="font-heading-md text-bold"
  >
    In-progress change request
  </h2>
  <div
    class="usa-alert usa-alert--warning usa-alert--no-icon c-alert--neutral margin-top-3"
    role="region"
    tabindex="-1"
  >
    <div
      class="usa-alert__body"
    >
      <div
        class="usa-alert__text"
        title="alert text"
      >
        <h3
          class="font-heading-sm text-bold"
        >
          Change request started
        </h3>
        <p
          class="margin-top-1"
        >
          Type: Change leave end date
        </p>
        <p
          class="margin-top-2"
        >
          <b>
            You can't continue this change request because your leave ended more than 30 days ago. 
          </b>
          If you still need to change your leave dates, call the Contact Center at 
          <a
            href="tel:(*************"
          >
            (833) 344‑7365
          </a>
        </p>
        <br />
        <a
          class="usa-button margin-top-1 usa-button--unstyled"
          href="/applications/modify/delete-change-request?change_request_id=change-request-id&absence_id=mock-absence-case-id"
        >
           
          Delete change request
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`Status has an in progress change request change request can no longer be submitted displays informative text if a med to bonding request has been started, but the claim already has a bonding period 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="inProgressChangeRequest"
>
  <h2
    class="font-heading-md text-bold"
  >
    In-progress change request
  </h2>
  <div
    class="usa-alert usa-alert--warning usa-alert--no-icon c-alert--neutral margin-top-3"
    role="region"
    tabindex="-1"
  >
    <div
      class="usa-alert__body"
    >
      <div
        class="usa-alert__text"
        title="alert text"
      >
        <h3
          class="font-heading-sm text-bold"
        >
          Change request started
        </h3>
        <p
          class="margin-top-1"
        >
          Type: Add family leave to bond with a child
        </p>
        <p
          class="margin-top-2"
        >
          <b>
            You already have a family leave to bond with your child tied to this application. 
          </b>
          You can make changes to your leave online once/if it has been approved. If you need to make a change before then you can call the Contact Center at 
          <a
            href="tel:(*************"
          >
            (833) 344‑7365
          </a>
        </p>
        <br />
        <a
          class="usa-button margin-top-1 usa-button--unstyled"
          href="/applications/modify/delete-change-request?change_request_id=change-request-id&absence_id=mock-absence-case-id"
        >
           
          Delete change request
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`Status has an in progress change request change request can no longer be submitted displays informative text if the claim now has a non-approved period 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="inProgressChangeRequest"
>
  <h2
    class="font-heading-md text-bold"
  >
    In-progress change request
  </h2>
  <div
    class="usa-alert usa-alert--warning usa-alert--no-icon c-alert--neutral margin-top-3"
    role="region"
    tabindex="-1"
  >
    <div
      class="usa-alert__body"
    >
      <div
        class="usa-alert__text"
        title="alert text"
      >
        <h3
          class="font-heading-sm text-bold"
        >
          Change request started
        </h3>
        <p
          class="margin-top-1"
        >
          Type: Change leave end date
        </p>
        <p
          class="margin-top-2"
        >
          <b>
            You can't continue this change request because you have a request for leave that is not currently approved. 
          </b>
          To change your leave dates, you can call the Contact Center at 
          <a
            href="tel:(*************"
          >
            (833) 344‑7365
          </a>
        </p>
        <br />
        <a
          class="usa-button margin-top-1 usa-button--unstyled"
          href="/applications/modify/delete-change-request?change_request_id=change-request-id&absence_id=mock-absence-case-id"
        >
           
          Delete change request
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`Status has an in progress change request change request can no longer be submitted displays informative text if the claim now has a pending period 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="inProgressChangeRequest"
>
  <h2
    class="font-heading-md text-bold"
  >
    In-progress change request
  </h2>
  <div
    class="usa-alert usa-alert--warning usa-alert--no-icon c-alert--neutral margin-top-3"
    role="region"
    tabindex="-1"
  >
    <div
      class="usa-alert__body"
    >
      <div
        class="usa-alert__text"
        title="alert text"
      >
        <h3
          class="font-heading-sm text-bold"
        >
          Change request started
        </h3>
        <p
          class="margin-top-1"
        >
          Type: Change leave end date
        </p>
        <p
          class="margin-top-2"
        >
          <b>
            You can't continue this change request because you have a pending request for leave. 
          </b>
          To change your leave dates, you can wait until your pending leave is approved or call the Contact Center at 
          <a
            href="tel:(*************"
          >
            (833) 344‑7365
          </a>
        </p>
        <br />
        <a
          class="usa-button margin-top-1 usa-button--unstyled"
          href="/applications/modify/delete-change-request?change_request_id=change-request-id&absence_id=mock-absence-case-id"
        >
           
          Delete change request
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`Status has an in progress change request displays a section for in progress change requests if one exists and doesn't show start request sections 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="inProgressChangeRequest"
>
  <h2
    class="font-heading-md text-bold"
  >
    In-progress change request
  </h2>
  <div
    class="usa-alert usa-alert--warning usa-alert--no-icon margin-top-3"
    role="region"
    tabindex="-1"
  >
    <div
      class="usa-alert__body"
    >
      <div
        class="usa-alert__text"
        title="alert text"
      >
        <h3
          class="font-heading-sm text-bold"
        >
          Change request started
        </h3>
        <p
          class="margin-top-1"
        >
          Type: Change leave end date
        </p>
        <a
          class="usa-button text-no-underline text-white margin-top-3 margin-bottom-2"
          href="/applications/modify/type?change_request_id=change-request-id&absence_id=mock-absence-case-id"
        >
           
          Continue request
        </a>
        <br />
        <a
          class="usa-button margin-top-1 usa-button--unstyled"
          href="/applications/modify/delete-change-request?change_request_id=change-request-id&absence_id=mock-absence-case-id"
        >
           
          Delete change request
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`Status includes a button to upload additional documents if there is an In Review absence period and no appeal: 
      <a
        class="usa-button margin-top-2"
        href="/applications/upload?absence_id=mock-absence-case-id&is_additional_doc=false"
      >
        Upload documents
      </a>
     1`] = `
<a
  class="usa-button margin-top-2"
  href="/applications/upload/select-document-type?absence_id=mock-absence-case-id&is_additional_doc=true&document_context=status-general"
>
  Upload documents
</a>
`;

exports[`Status manage your application displays manage application section if any of the claim statuses on an application are Approved  1`] = `
<div
  class="padding-y-4"
  data-testid="reportOtherBenefits"
>
  <h2
    class="font-heading-md text-bold"
  >
    Report other benefits or income
  </h2>
  <p>
    If your plans for other benefits or income during your paid leave have changed, call the Contact Center at 
    <a
      href="tel:(*************"
    >
      (833) 344‑7365
    </a>
    . Learn more about 
    <a
      href="https://www.mass.gov/info-details/how-other-leave-and-benefits-can-affect-your-paid-family-and-medical-leave"
    >
      How other leave and benefits can affect your Paid Family and Medical Leave
    </a>
  </p>
</div>
`;

exports[`Status manage your application displays manage application section if any of the claim statuses on an application are In Review  1`] = `
<div
  class="padding-y-4"
  data-testid="reportOtherBenefits"
>
  <h2
    class="font-heading-md text-bold"
  >
    Report other benefits or income
  </h2>
  <p>
    If your plans for other benefits or income during your paid leave have changed, call the Contact Center at 
    <a
      href="tel:(*************"
    >
      (833) 344‑7365
    </a>
    . Learn more about 
    <a
      href="https://www.mass.gov/info-details/how-other-leave-and-benefits-can-affect-your-paid-family-and-medical-leave"
    >
      How other leave and benefits can affect your Paid Family and Medical Leave
    </a>
  </p>
</div>
`;

exports[`Status manage your application displays manage application section if any of the claim statuses on an application are Pending  1`] = `
<div
  class="padding-y-4"
  data-testid="reportOtherBenefits"
>
  <h2
    class="font-heading-md text-bold"
  >
    Report other benefits or income
  </h2>
  <p>
    If your plans for other benefits or income during your paid leave have changed, call the Contact Center at 
    <a
      href="tel:(*************"
    >
      (833) 344‑7365
    </a>
    . Learn more about 
    <a
      href="https://www.mass.gov/info-details/how-other-leave-and-benefits-can-affect-your-paid-family-and-medical-leave"
    >
      How other leave and benefits can affect your Paid Family and Medical Leave
    </a>
  </p>
</div>
`;

exports[`Status manage your application displays manage application section if any of the claim statuses on an application are Projected  1`] = `
<div
  class="padding-y-4"
  data-testid="reportOtherBenefits"
>
  <h2
    class="font-heading-md text-bold"
  >
    Report other benefits or income
  </h2>
  <p>
    If your plans for other benefits or income during your paid leave have changed, call the Contact Center at 
    <a
      href="tel:(*************"
    >
      (833) 344‑7365
    </a>
    . Learn more about 
    <a
      href="https://www.mass.gov/info-details/how-other-leave-and-benefits-can-affect-your-paid-family-and-medical-leave"
    >
      How other leave and benefits can affect your Paid Family and Medical Leave
    </a>
  </p>
</div>
`;

exports[`Status renders the page with claim detail 1`] = `
<div>
  <a
    class="usa-button margin-bottom-5 usa-button--unstyled"
    href="/applications"
  >
    <svg
      class="margin-right-05"
      height="12"
      viewBox="0 0 448 512"
      width="12"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M257.5 445.1l-22.2 22.2c-9.4 9.4-24.6 9.4-33.9 0L7 273c-9.4-9.4-9.4-24.6 0-33.9L201.4 44.7c9.4-9.4 24.6-9.4 33.9 0l22.2 22.2c9.5 9.5 9.3 25-.4 34.3L136.6 216H424c13.3 0 24 10.7 24 24v32c0 13.3-10.7 24-24 24H136.6l120.5 114.8c9.8 9.3 10 24.8.4 34.3z"
        fill="currentColor"
      />
    </svg>
    Back to your applications
  </a>
  <div
    data-testid="alert-banner"
  />
  <div
    class="grid-row grid-gap"
  >
    <div
      class="grid-col-12 desktop:grid-col-2"
      data-testid="status-side-navigation"
    >
      <div
        class="margin-top-5 tablet:margin-bottom-0"
      >
        <nav>
          <ul
            class="usa-sidenav"
          >
            <li
              class="usa-sidenav__item"
            >
              <a
                class=""
                href="/applications/status?absence_id=mock-absence-case-id"
              >
                Application
              </a>
            </li>
            <li
              class="usa-sidenav__item"
            >
              <a
                class=""
                href="/applications/status/payments?absence_id=mock-absence-case-id"
              >
                Payments
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
    <div
      class="measure-6"
    >
      <span
        class="display-block font-heading-2xs margin-bottom-2 text-base-dark text-bold"
      >
        <div
          class="display-flex grid-row flex-justify margin-top-5 margin-bottom-3"
        >
          <div
            class="grid-col-12 tablet:grid-col-6 phone:grid-col-6"
            title="claims status application id"
          >
            Application ID: 
            <var>
              mock-absence-case-id
            </var>
          </div>
          <div
            class="grid-col-12 tablet:grid-col-6 phone:grid-col-6"
            title="claims status employer ein"
          >
            Employer Identification Number (EIN): 
            <var>
              12-1234567
            </var>
          </div>
        </div>
      </span>
      <h2
        class="margin-0 font-heading-lg text-bold"
      >
        Leave to bond with a child
      </h2>
      <h1
        class="js-title margin-top-0 margin-bottom-2 font-heading-lg line-height-sans-2 usa-sr-only"
        tabindex="-1"
      >
        Application details
      </h1>
      <div
        class="border-bottom border-base-lighter padding-bottom-4"
      >
        <div
          class="margin-top-4"
        >
          <h2
            class="font-heading-sm text-bold"
          >
            Continuous leave
          </h2>
          <p
            role="text"
            title="claims status leave period dates"
          >
            From 
            <var>
              October 21, 2021
            </var>
             to 
            <var>
              December 30, 2021
            </var>
          </p>
          <p>
            <span
              class="usa-tag display-inline-block text-bold text-middle text-center text-no-wrap text-success-dark bg-success-lighter"
            >
              Approved
            </span>
          </p>
          <div
            data-testid="leaveStatusMessage"
            title="leave status message"
          >
            <strong>
              This leave was approved.
            </strong>
            <p>
              View your approval notice below for more details about your benefit amount, and how to appeal if your benefits appear incorrect. Learn more about the 
              <a
                href="https://mass.gov/pfml/appeal"
                rel="noopener noreferrer"
                target="_blank"
              >
                appeal process
              </a>
              .
            </p>
          </div>
          <div
            class="padding-top-1"
            data-testid="paymentsLeaveStatusMessage"
          >
            <p>
              See your payment status and estimated payment date on your payments page.
            </p>
            <a
              href="/applications/status/payments?absence_id=mock-absence-case-id"
            >
              Track your payments
            </a>
          </div>
        </div>
      </div>
      <div
        class="border-bottom border-base-lighter padding-y-4"
        data-testid="requestChange"
      >
        <h2
          class="font-heading-md text-bold"
        >
          Request a change to your application
        </h2>
        <p>
          Since your leave ended more than 30 days ago, you’ll need to call the Contact Center at 
          <a
            href="tel:(*************"
          >
            (833) 344‑7365
          </a>
           to request a change.
        </p>
        <p>
          Read more about 
          <a
            href="https://www.mass.gov/info-details/updating-or-extending-your-approved-paid-family-or-medical-leave"
            rel="noopener noreferrer"
            target="_blank"
          >
            updating or extending your application
          </a>
          .
        </p>
      </div>
      <div
        class="border-bottom border-base-lighter padding-y-4"
        data-testid="view-your-notices"
        id="view-your-notices"
      >
        <h2
          class="padding-bottom-2 font-heading-md text-bold"
          id="view_notices"
        >
          View your notices
        </h2>
        <p
          class="padding-bottom-2 margin-top-05"
        >
          When you click the notice link, the file will download to your device.
          <br />
          <br />
          Your notices appear in the language you selected for receiving written communications. An English translation of your notices is included in the same file. Call the DFML Contact Center at 
          <a
            href="tel:(*************"
          >
            (833) 344‑7365
          </a>
           to change your language preference for notices. You can also call to get help with translating notices if your language is not available.
        </p>
        <ul
          class="add-list-reset"
        >
          <li
            class="grid-row flex-row flex-justify-start flex-align-start margin-bottom-1 text-primary"
          >
            <svg
              aria-hidden="true"
              class="usa-icon usa-icon--size-3 margin-right-1"
              fill="currentColor"
              focusable="false"
              role="img"
            >
              <use
                xlink:href="/img/sprite.svg#file_present"
              />
            </svg>
            <div>
              <div
                class="usa-prose"
              >
                <button
                  class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
                  type="button"
                >
                  Approval notice (PDF)
                </button>
                <div
                  class="text-base-dark text-normal"
                  title="downloadable document created date"
                >
                  Posted 
                  <var>
                    4/5/2020
                  </var>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div
        class="border-bottom border-base-lighter padding-y-4"
        data-testid="appeals"
        id="appeals"
      >
        <h2
          class="font-heading-md text-bold"
        >
          Appeal the decision
        </h2>
        <div>
          We have made a decision for this application. If you would like to appeal that decision, you can start an appeal online or do so using mail or fax.
        </div>
        <p>
          <a
            class="usa-button usa-button--outline"
            href="/applications/appeals/start?absence_id=mock-absence-case-id"
          >
            Start an appeal
          </a>
        </p>
      </div>
      <div
        class="padding-y-4"
        data-testid="reportOtherBenefits"
      >
        <h2
          class="font-heading-md text-bold"
        >
          Report other benefits or income
        </h2>
        <p>
          If your plans for other benefits or income during your paid leave have changed, call the Contact Center at 
          <a
            href="tel:(*************"
          >
            (833) 344‑7365
          </a>
          . Learn more about 
          <a
            href="https://www.mass.gov/info-details/how-other-leave-and-benefits-can-affect-your-paid-family-and-medical-leave"
          >
            How other leave and benefits can affect your Paid Family and Medical Leave
          </a>
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Status timeline is displayed if there is a pending absence period 1`] = `
<div
  class="border-bottom border-base-lighter padding-y-4"
  data-testid="timeline"
>
  <h2
    class="font-heading-md text-bold"
  >
    Timeline
  </h2>
  <p>
    Your employer has 
    <strong>
      10 business days
    </strong>
     to respond to your application.
  </p>
  <p>
    We have 
    <strong>
      14 calendar days
    </strong>
     after receiving your completed application to make a decision to approve, deny or request more information.
  </p>
  <p>
    Your application is complete when:
  </p>
  <ul
    class="usa-list"
  >
    <li>
      You have submitted all required documents
    </li>
    <li>
      Your employer has responded or their deadline passes
    </li>
  </ul>
  <p>
    The process may take longer if we request more information to complete your application or if you request changes to your application.
  </p>
  <p>
    Learn more about the 
    <a
      href="https://mass.gov/pfml/application-timeline"
      rel="noopener noreferrer"
      target="_blank"
    >
      application approval process.
    </a>
  </p>
</div>
`;

exports[`Status timeline when there is a pending bonding absence period and claimant has not uploaded certification documents shows link to upload proof of adoption when reason qualifier is adoption: 
                  <a
                    class="usa-button margin-top-2"
                    href="/applications/upload?absence_id=mock-absence-case-id"
                  >
                    Submit proof of adoption
                  </a>
               1`] = `
<a
  class="usa-button margin-top-2"
  href="/applications/upload/proof-of-placement?absence_id=mock-absence-case-id&claim_id=mock-application-id&is_additional_doc=true"
>
  Submit proof of adoption
</a>
`;

exports[`Status timeline when there is a pending bonding absence period and claimant has not uploaded certification documents shows link to upload proof of birth when reason qualifier is newborn: 
                  <a
                    class="usa-button margin-top-2"
                    href="/applications/upload?absence_id=mock-absence-case-id"
                  >
                    Submit proof of birth
                  </a>
               1`] = `
<a
  class="usa-button margin-top-2"
  href="/applications/upload/proof-of-birth?absence_id=mock-absence-case-id&claim_id=mock-application-id&is_additional_doc=true"
>
  Submit proof of birth
</a>
`;

exports[`Status timeline when there is a pending bonding absence period and claimant has not uploaded certification documents shows link to upload proof of placement when reason qualifier is foster: 
          <a
            class="usa-button margin-top-2"
            href="/applications/upload?absence_id=mock-absence-case-id"
          >
            Submit proof of placement
          </a>
         1`] = `
<a
  class="usa-button margin-top-2"
  href="/applications/upload/proof-of-placement?absence_id=mock-absence-case-id&claim_id=mock-application-id&is_additional_doc=true"
>
  Submit proof of placement
</a>
`;

exports[`Status timeline when there is a pending bonding absence period and claimant has submitted certification documents shows timeline with generic follow up dates when there are no open managed requirements with follow up dates: 
          <p>
            Your employer has 
            <strong>
              10 business days
            </strong>
             to respond to your application. DFML has contacted your employer to review your application. If your employer doesn’t respond by the deadline, DFML will conduct its review using the information you provided.
          </p>
         1`] = `
<p>
  Your employer has 
  <strong>
    10 business days
  </strong>
   to respond to your application. DFML has contacted your employer to review your application. If your employer doesn’t respond by the deadline, DFML will conduct its review using the information you provided.
</p>
`;

exports[`Status timeline when there is a pending caring, medical, or pregnancy absence period shows timeline with generic follow up dates when there are no open managed requirements with follow up dates: 
          <p>
            Your employer has 
            <strong>
              10 business days
            </strong>
             to respond to your application. DFML has contacted your employer to review your application. If your employer doesn’t respond by the deadline, DFML will conduct its review using the information you provided.
          </p>
         1`] = `
<p>
  Your employer has 
  <strong>
    10 business days
  </strong>
   to respond to your application. DFML has contacted your employer to review your application. If your employer doesn’t respond by the deadline, DFML will conduct its review using the information you provided.
</p>
`;
