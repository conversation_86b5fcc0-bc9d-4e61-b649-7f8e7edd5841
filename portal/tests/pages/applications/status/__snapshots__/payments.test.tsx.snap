// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Payments \`Your payments\` intro content section renders intermittentUnpaidIntroText when leave is intermittent, no payments are available, and there is no checkback date 1`] = `
<section
  data-testid="your-payments-intro"
>
  <p>
    This application has an unpaid 7-day waiting period that begins the first day leave is reported to be taken. During the 7-day waiting period, paid time off (PTO) can be used and job protection will be afforded.
  </p>
  <p>
    Once the unpaid 7-day waiting period is completed and at least 8 hours of leave have been reported, expect to receive a payment from DFML within 7-10 business days. More than one payment per week may be processed depending on how hours are reported. You can report your leave hours 
    <a
      href="/applications/status/report-leave?absence_id=mock-absence-case-id"
    >
      online
    </a>
    , or you can call the Hours Reporting Line at 
    <a
      href="tel:(*************"
    >
      (*************
    </a>
    .
  </p>
</section>
`;

exports[`Payments \`Your payments\` intro content section renders the \`Your payments\` intro content section 1`] = `
<section
  data-testid="your-payments-intro"
>
  Payments are processed to be paid each week of leave. Check back weekly to see when the next payment will be processed.
</section>
`;

exports[`Payments Checkback date implementation does not render checkback dates for claims that have at least one payment row 1`] = `
<section
  data-testid="your-payments-intro"
>
  Payments are processed to be paid each week of leave. Check back weekly to see when the next payment will be processed.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for continous leaves approved after fourteenth claim date if claim has reduced and continuous leaves 1`] = `
<section
  data-testid="your-payments-intro"
>
  Check back on 
  <strong>
    <var>
      November 15, 2021
    </var>
  </strong>
   to see when to expect a first payment. The first payment will include multiple weeks of leave. After the first payment is issued, expect to be paid weekly.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for continous leaves approved before claim start date if claim has reduced and continuous leaves 1`] = `
<section
  data-testid="your-payments-intro"
>
  Check back on 
  <strong>
    <var>
      November 10, 2021
    </var>
  </strong>
   to see when to expect the first payment. The first payment will be processed after the second week of leave ends. After the first payment is processed, expect to be paid weekly.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for continous leaves approved before fourteenth claim date if claim has reduced and continuous leaves 1`] = `
<section
  data-testid="your-payments-intro"
>
  Check back on 
  <strong>
    <var>
      November 10, 2021
    </var>
  </strong>
   to see when to expect the first payment. The first payment will be processed after the second week of leave ends. After the first payment is processed, expect to be paid weekly.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for continous leaves with retroactive claim date if claim has reduced and continuous leaves 1`] = `
<section
  data-testid="your-payments-intro"
>
  Since the application was approved after the leave ended, there will be one payment for the entire leave. Check back on 
  <strong>
    <var>
      January 24, 2022
    </var>
  </strong>
   to see when the payment will be processed.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for continuous leaves approved after fourteenth claim date  1`] = `
<section
  data-testid="your-payments-intro"
>
  Check back on 
  <strong>
    <var>
      November 15, 2021
    </var>
  </strong>
   to see when to expect a first payment. The first payment will include multiple weeks of leave. After the first payment is issued, expect to be paid weekly.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for continuous leaves approved before claim start date  1`] = `
<section
  data-testid="your-payments-intro"
>
  Check back on 
  <strong>
    <var>
      November 10, 2021
    </var>
  </strong>
   to see when to expect the first payment. The first payment will be processed after the second week of leave ends. After the first payment is processed, expect to be paid weekly.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for continuous leaves approved before fourteenth claim date  1`] = `
<section
  data-testid="your-payments-intro"
>
  Check back on 
  <strong>
    <var>
      November 10, 2021
    </var>
  </strong>
   to see when to expect the first payment. The first payment will be processed after the second week of leave ends. After the first payment is processed, expect to be paid weekly.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for continuous leaves with retroactive claim date  1`] = `
<section
  data-testid="your-payments-intro"
>
  Since the application was approved after the leave ended, there will be one payment for the entire leave. Check back on 
  <strong>
    <var>
      January 24, 2022
    </var>
  </strong>
   to see when the payment will be processed.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for reduced schedule leaves approved after fourteenth claim date  1`] = `
<section
  data-testid="your-payments-intro"
>
  Check back on 
  <strong>
    <var>
      November 15, 2021
    </var>
  </strong>
   to see when to expect a first payment. The first payment will include multiple weeks of leave. After the first payment is issued, expect to be paid weekly.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for reduced schedule leaves approved before claim start date  1`] = `
<section
  data-testid="your-payments-intro"
>
  Check back on 
  <strong>
    <var>
      November 10, 2021
    </var>
  </strong>
   to see when to expect the first payment. The first payment will be processed after the second week of leave ends. After the first payment is processed, expect to be paid weekly.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for reduced schedule leaves approved before fourteenth claim date  1`] = `
<section
  data-testid="your-payments-intro"
>
  Check back on 
  <strong>
    <var>
      November 10, 2021
    </var>
  </strong>
   to see when to expect the first payment. The first payment will be processed after the second week of leave ends. After the first payment is processed, expect to be paid weekly.
</section>
`;

exports[`Payments Checkback date implementation renders intro text for reduced schedule leaves with retroactive claim date  1`] = `
<section
  data-testid="your-payments-intro"
>
  Since the application was approved after the leave ended, there will be one payment for the entire leave. Check back on 
  <strong>
    <var>
      January 24, 2022
    </var>
  </strong>
   to see when the payment will be processed.
</section>
`;

exports[`Payments Post FINEOS deploy renders the \`help\` section containing questions and feedback 1`] = `
<section
  class="margin-y-5"
  data-testid="helpSection"
>
  <h2
    class="font-heading-md text-bold"
  >
    Questions?
  </h2>
  <p>
    Call the Contact Center at 
    <a
      href="tel:(*************"
    >
      (833) 344‑7365
    </a>
    .
  </p>
</section>
`;

exports[`Payments renders the \`help\` section containing questions and feedback 1`] = `
<section
  class="margin-y-5"
  data-testid="helpSection"
>
  <h2
    class="font-heading-md text-bold"
  >
    Questions?
  </h2>
  <p>
    Call the Contact Center at 
    <a
      href="tel:(*************"
    >
      (833) 344‑7365
    </a>
    .
  </p>
</section>
`;
