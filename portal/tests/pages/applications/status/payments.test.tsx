// TODO (PORTAL-1148) Update to use createMockClaim when ready
import {
  AbsencePeriod,
  AbsencePeriodRequestDecision,
} from "src/models/AbsencePeriod";
import {
  createAbsencePeriod,
  createMockAbsencePeriodsByDecisionStatus,
  renderPage,
} from "tests/test-utils";
import { screen, within } from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import { AppLogic } from "src/hooks/useAppLogic";
import ChangeRequest from "src/models/ChangeRequest";
import ClaimDetail from "src/models/ClaimDetail";
import ClaimPaymentInfo from "src/models/ClaimPaymentInfo";
import { Holiday } from "src/models/Holiday";
import LeaveReason from "src/models/LeaveReason";
import Payment from "src/models/Payment";
import PaymentsPage from "src/pages/applications/status/payments";
import WaitingPeriod from "src/models/WaitingPeriod";
import { createMockPayment } from "lib/mock-helpers/createMockPayment";
import dayjs from "dayjs";
import dayjsBusinessTime from "dayjs-business-time";
import routes from "src/routes";

jest.mock("src/api/WaitingPeriodsApi");

dayjs.extend(dayjsBusinessTime);
interface SetupOptions {
  absence_periods?: AbsencePeriod[];
  payments?: Payment[];
  goTo?: jest.Mock;
  approvalDate?: string;
  includeApprovalNotice?: boolean;
  holidays?: Holiday[];
  claimExists?: boolean;
  claimDetailAttrs?: Partial<ClaimDetail>;
  errors?: Error[];
  payment_schedule_type?: string;
  waitingPeriods?: WaitingPeriod[] | null;
}

const setupHelper =
  ({
    absence_periods = [defaultAbsencePeriod],
    payments = [],
    goTo = jest.fn(),
    approvalDate = defaultApprovalDate.format("YYYY-MM-DD"),
    includeApprovalNotice = false,
    holidays = defaultHolidays,
    claimExists = true,
    claimDetailAttrs = {},
    errors = [],
    payment_schedule_type,
    waitingPeriods = defaultWaitingPeriods,
  }: SetupOptions) =>
  (appLogicHook: AppLogic) => {
    appLogicHook.claims.claimDetail = claimExists
      ? new ClaimDetail({
          ...defaultClaimDetailAttributes,
          payment_schedule_type,
          absence_periods,
          approval_date: includeApprovalNotice ? approvalDate : null,
          ...claimDetailAttrs,
        })
      : undefined;
    appLogicHook.claims.loadClaimDetail = jest.fn();
    appLogicHook.errors = errors;
    appLogicHook.documents.loadAll = jest.fn();
    appLogicHook.documents.hasLoadedClaimDocuments = () => true;
    appLogicHook.holidays.holidays = holidays;
    appLogicHook.holidays.loadHolidays = jest.fn();
    appLogicHook.holidays.hasLoadedHolidays = true;
    appLogicHook.holidays.isLoadingHolidays = false;
    appLogicHook.portalFlow.goTo = goTo;
    appLogicHook.payments.loadPayments = jest.fn();
    appLogicHook.payments.payments = new ApiResourceCollection<Payment>(
      "payment_id",
      payments
    );
    appLogicHook.changeRequests.loadAll = jest.fn();
    appLogicHook.changeRequests.hasLoadedChangeRequests = () => true;
    appLogicHook.changeRequests.isLoadingChangeRequests = () => false;
    appLogicHook.changeRequests.changeRequests =
      new ApiResourceCollection<ChangeRequest>("change_request_id", []);
    appLogicHook.waitingPeriods.loadWaitingPeriods = jest.fn();
    appLogicHook.waitingPeriods.hasLoadedWaitingPeriods = () => true;
    if (waitingPeriods) {
      appLogicHook.waitingPeriods.waitingPeriods = waitingPeriods;
    }
  };

// Extracted from setupHelper arguments because abscence_period_start_date (and end)
// should be based on this in future work.
const defaultApprovalDate = dayjs().add(-3, "months");

const defaultAbsencePeriod = createAbsencePeriod({
  period_type: "Continuous",
  absence_period_start_date: "2021-10-21",
  absence_period_end_date: "2021-12-30",
  reason: "Child Bonding",
  request_decision: "Approved",
});

const defaultWaitingPeriods: WaitingPeriod[] = [
  {
    waiting_period_end_date: "2023-02-08",
    waiting_period_start_date: "2023-02-01",
    is_benefit_year_waiting_period: false,
  },
];

const spanningBenefitYearWaitingPeriods: WaitingPeriod[] = [
  ...defaultWaitingPeriods,
  {
    waiting_period_end_date: "2023-08-31",
    waiting_period_start_date: "2023-08-01",
    is_benefit_year_waiting_period: true,
  },
];

const defaultClaimDetailAttributes = {
  application_id: "mock-application-id",
  fineos_absence_id: "mock-absence-case-id",
  employer: {
    employer_fein: "12-1234567",
    employer_dba: "Acme",
    employer_id: "mock-employer-id",
  },
  absence_periods: [defaultAbsencePeriod],
  payment_schedule_type: undefined,
  payment_preference: null,
};

const defaultHolidays = [{ name: "Memorial Day", date: "2022-05-30" }];
const defaultHolidayAlertText =
  "holiday, payments may be delayed by one business day.";

const props = {
  query: {
    absence_id: defaultClaimDetailAttributes.fineos_absence_id,
  },
};

describe("Payments", () => {
  it("redirects to status page if claim is not approved and has no payments", () => {
    const goToMock = jest.fn();

    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        // includeApprovalNotice is false by default in setupHelper, passing for clarity
        addCustomSetup: setupHelper({
          absence_periods: [
            createAbsencePeriod({
              ...defaultAbsencePeriod,
              request_decision: "Pending",
            }),
          ],
          goTo: goToMock,
          includeApprovalNotice: false,
        }),
      },
      props
    );

    expect(goToMock).toHaveBeenCalledWith(routes.applications.status.claim, {
      absence_id: props.query.absence_id,
    });
  });

  it("renders the back button", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({}),
      },
      props
    );

    const backButton = screen.getByRole("link", {
      name: /back to your applications/i,
    });

    expect(backButton).toBeInTheDocument();
  });

  it("displays info alert if claimant has bonding-newborn but not pregnancy claims", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          absence_periods: [
            createAbsencePeriod({
              period_type: "Reduced Schedule",
              reason: LeaveReason.bonding,
              request_decision: "Pending",
              reason_qualifier_one: "Newborn",
            }),
          ],
        }),
      },
      props
    );

    const bondingAlertText =
      "If you are giving birth, you may also be eligible for paid leave for pregnancy or recovery from childbirth";
    expect(
      screen.getByText(bondingAlertText, { exact: false })
    ).toBeInTheDocument();
  });

  it("renders the back button above the info alert", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          absence_periods: [
            createAbsencePeriod({
              period_type: "Reduced Schedule",
              reason: LeaveReason.bonding,
              request_decision: "Pending",
              reason_qualifier_one: "Newborn",
            }),
          ],
        }),
      },
      props
    );
    const backButton = screen.getByRole("link", {
      name: /back to your applications/i,
    });
    const infoAlert = screen.getByTestId("alert-banner");

    expect(backButton.compareDocumentPosition(infoAlert)).toBe(
      Node.DOCUMENT_POSITION_FOLLOWING
    );
  });

  it("displays info alert if claimant has pregnancy but not bonding claims", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          absence_periods: [
            createAbsencePeriod({
              period_type: "Reduced Schedule",
              reason: LeaveReason.pregnancy,
              request_decision: "Approved",
            }),
          ],
          includeApprovalNotice: true,
        }),
      },
      props
    );

    const pregnancyAlertText =
      "You may be able to take up to 12 weeks of paid family leave to bond with your child after your medical leave ends.";
    expect(
      screen.getByText(pregnancyAlertText, { exact: false })
    ).toBeInTheDocument();
  });

  it("does not display info alert if claimant has bonding AND pregnancy claims", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          absence_periods: [
            createAbsencePeriod({
              period_type: "Reduced Schedule",
              reason: LeaveReason.pregnancy,
              request_decision: "Approved",
            }),
            createAbsencePeriod({
              period_type: "Reduced Schedule",
              reason: LeaveReason.bonding,
              request_decision: "Approved",
            }),
          ],
          includeApprovalNotice: true,
        }),
      },
      props
    );
    const bondingAlertText =
      "If you are giving birth, you may also be eligible for paid medical leave";
    const pregnancyAlertText =
      "You may be able to take up to 12 weeks of paid family leave to bond with your child after your medical leave ends.";
    expect(
      screen.queryByText(bondingAlertText, { exact: false })
    ).not.toBeInTheDocument();
    expect(
      screen.queryByText(pregnancyAlertText, { exact: false })
    ).not.toBeInTheDocument();
  });

  it("doesn't show the holiday alert when there are no holidays", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({ holidays: [] }),
      },
      props
    );
    expect(screen.queryByText(defaultHolidayAlertText)).not.toBeInTheDocument();
  });

  it("shows the holiday alert when there are holidays", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({}),
      },
      props
    );
    expect(
      screen.queryByText(defaultHolidayAlertText, { exact: false })
    ).toBeInTheDocument();
  });

  it("does not show the holiday alert when the claim can't receive payments", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          absence_periods: createMockAbsencePeriodsByDecisionStatus([
            AbsencePeriodRequestDecision.voided,
            AbsencePeriodRequestDecision.withdrawn,
            AbsencePeriodRequestDecision.cancelled,
            AbsencePeriodRequestDecision.denied,
          ]),
        }),
      },
      props
    );
    expect(
      screen.queryByText(defaultHolidayAlertText, { exact: false })
    ).not.toBeInTheDocument();
  });

  it("does show the holiday alert when the claim has multiple absence periods that can not receive payments, but at least one can", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          absence_periods: createMockAbsencePeriodsByDecisionStatus([
            AbsencePeriodRequestDecision.voided,
            AbsencePeriodRequestDecision.withdrawn,
            AbsencePeriodRequestDecision.cancelled,
            AbsencePeriodRequestDecision.denied,
            AbsencePeriodRequestDecision.approved,
          ]),
        }),
      },
      props
    );
    expect(
      screen.queryByText(defaultHolidayAlertText, { exact: false })
    ).toBeInTheDocument();
  });

  describe("`Your payments` intro content section", () => {
    it("renders the `Your payments` intro content section", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({}),
        },
        props
      );

      const section = screen.getByTestId("your-payments-intro");
      expect(section).toMatchSnapshot();
    });

    it("renders intermittentUnpaidIntroText when leave is intermittent, no payments are available, and there is no checkback date", () => {
      const intermittenAbsencePeriod: AbsencePeriod = createAbsencePeriod({
        ...defaultAbsencePeriod,
        period_type: "Intermittent",
      });
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({
            absence_periods: [defaultAbsencePeriod, intermittenAbsencePeriod],
          }),
        },
        {
          ...props,
        }
      );

      const intermittentUnpaidIntroText = screen.queryByText(
        /Once the unpaid 7-day waiting period is completed and at least 8 hours of leave have been reported, expect to receive a payment from DFML within 7-10 business days. More than one payment per week may be processed depending on how hours are reported. You can report your leave hours/
      );
      expect(intermittentUnpaidIntroText).toBeInTheDocument();
      expect(screen.getByTestId("your-payments-intro")).toMatchSnapshot();
    });
  });

  it("renders non-retroactive text if latest absence period date is in the future (not retroactive)", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          absence_periods: [
            defaultAbsencePeriod,
            createAbsencePeriod({
              period_type: "Reduced Schedule",
              absence_period_start_date: dayjs()
                .add(2, "weeks")
                .format("YYYY-MM-DD"),
              absence_period_end_date: dayjs()
                .add(2, "weeks")
                .add(4, "months")
                .format("YYYY-MM-DD"),
              reason: "Child Bonding",
            }),
          ],
        }),
      },
      props
    );

    expect(
      screen.queryByText(/receive one payment for your entire leave/)
    ).not.toBeInTheDocument();
    expect(screen.getByText(/Check back weekly/)).toBeInTheDocument();
  });

  it("renders retroactive text if latest absence period date is in the past (retroactive)", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          includeApprovalNotice: true,
          payment_schedule_type: "Leave Start-Based",
          absence_periods: [
            createAbsencePeriod({
              period_type: "Reduced Schedule",
              absence_period_start_date: dayjs()
                .add(-8, "months")
                .format("YYYY-MM-DD"),
              absence_period_end_date: dayjs()
                .add(-8, "months")
                .add(1, "months")
                .format("YYYY-MM-DD"),
              reason: "Child Bonding",
            }),
            createAbsencePeriod({
              period_type: "Reduced Schedule",
              absence_period_start_date: dayjs()
                .add(-6, "months")
                .format("YYYY-MM-DD"),
              absence_period_end_date: dayjs()
                .add(-6, "months")
                .add(1, "months")
                .format("YYYY-MM-DD"),
              reason: "Child Bonding",
            }),
          ],
        }),
      },
      props
    );

    expect(
      screen.getByText(/there will be one payment for the entire leave/)
    ).toBeInTheDocument();
  });

  it("renders the `payment-information` section", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({}),
      },
      props
    );
    expect(screen.queryByTestId("payment-information")).toBeInTheDocument();
  });

  it("renders the `help` section containing questions and feedback", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({}),
      },
      props
    );
    const section = screen.getByTestId("helpSection");
    expect(section).toMatchSnapshot();
    // Text should appear in <GetHelpSection> and <PaymentFAQSection> which are both on this screen
    const details = screen.getAllByText(/contact center/i);
    expect(details.length).toBe(2);
  });

  describe("PTO Top-off Content", () => {
    it("Displays 'How to get your full salary while taking PFML'", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({}),
        },
        props
      );
      const content = screen.queryAllByText(
        "How to get your full salary while taking PFML"
      );
      expect(content.length).toBe(1);
    });

    it("Displays 'Payment information' instead of 'Changes to payments", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({}),
        },
        props
      );

      const newTitle = screen.getAllByText(/Payment information/);
      expect(newTitle.length).toBe(1);

      const oldTitle = screen.queryAllByText(/Changes to payments/);
      expect(oldTitle.length).toBe(0);
    });

    it("No longer displays 'Paid time off taken on the same day PFML benefits are recieved' as cancelled reason in FAQ", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({}),
        },
        props
      );

      const oldContent = screen.queryAllByText(
        /Paid time off taken on the same day PFML benefits are recieved/
      );
      expect(oldContent.length).toBe(0);
    });

    it("Displays 'Weekly benefit amount' instead of 'Where to find the maximum weekly benefit amount'", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({}),
        },
        props
      );

      const newContent = screen.queryAllByText(/Weekly benefit amount/);
      expect(newContent.length).toBe(1);

      const oldContent = screen.queryAllByText(
        /Where to find the maximum weekly benefit amount/
      );
      expect(oldContent.length).toBe(0);
    });

    it("Displays 'Weekly benefit amount' updated details", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({}),
        },
        props
      );

      const newContent = screen.queryAllByText(
        /To see the weekly benefit amount, view the most recent/
      );
      expect(newContent.length).toBe(2);

      const oldContent = screen.queryAllByText(
        /A new notice will be sent if the benefit amount is changed for any reason other than tax withholding. Learn more about/
      );
      expect(oldContent.length).toBe(1);
    });

    it("Displays 'Why a payment may be less than the weekly benefit amount' instead of 'Why the payment may be less than the maximum weekly benefit amount'", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({}),
        },
        props
      );

      const newContent = screen.queryAllByText(
        /Why a payment may be less than the weekly benefit amount/
      );
      expect(newContent.length).toBe(1);

      const oldContent = screen.queryAllByText(
        /Why the payment may be less than the maximum weekly benefit amount/
      );
      expect(oldContent.length).toBe(0);
    });
  });

  it("renders the Payments table", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          payments: [
            createMockPayment({ status: "Sent to bank" }, true),
            createMockPayment(
              { status: "Delayed", sent_to_bank_date: null },
              true
            ),
            createMockPayment(
              { status: "Pending", sent_to_bank_date: null },
              true
            ),
            createMockPayment({ status: "Sent to bank" }, true),
          ],
        }),
      },
      {
        ...props,
      }
    );

    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();
    expect(within(table).getAllByRole("rowgroup")).toHaveLength(2);

    const columns = within(table).getAllByRole("columnheader");
    expect(columns).toHaveLength(3);

    const rows = within(table).getAllByRole("row");
    expect(rows).toHaveLength(6); // 1 header row + 4 mock payment rows + 1 waiting period row
  });

  it("renders default waiting period text if claim does not span BY", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          payments: [
            createMockPayment({ status: "Sent to bank" }, true),
            createMockPayment({ status: "Sent to bank" }, true),
          ],
          claimDetailAttrs: { does_claim_span_benefit_years: false },
        }),
      },
      {
        ...props,
      }
    );

    const defaultWaitingPeriodText =
      "There is an unpaid 7-day waiting period at the start of this leave. Payments are not scheduled during this time.";
    expect(
      screen.getByText(defaultWaitingPeriodText, { exact: false })
    ).toBeInTheDocument();
  });

  it("shows a spinner if there is no claim detail", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({
          claimExists: false,
        }),
      },
      props
    );
    expect(screen.getByRole("progressbar")).toBeInTheDocument();
  });

  it("displays page not found alert if there's no absence case ID", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({ claimExists: false }),
      },
      { query: {} }
    );

    const pageNotFoundHeading = screen.getByRole("heading", {
      name: /Page not found/,
    });
    expect(pageNotFoundHeading).toBeInTheDocument();
  });

  // TODO(PORTAL-1482): remove test cases for checkback dates
  describe("Checkback date implementation", () => {
    const approvalDate = {
      "approved before claim start date": dayjs(
        defaultAbsencePeriod.absence_period_start_date
      )
        .add(-1, "day")
        .format("YYYY-MM-DD"),
      "approved after fourteenth claim date": dayjs(
        defaultAbsencePeriod.absence_period_start_date
      )
        .add(14, "day")
        .format("YYYY-MM-DD"),
      "approved before fourteenth claim date": dayjs(
        defaultAbsencePeriod.absence_period_start_date
      )
        .add(7, "day")
        .format("YYYY-MM-DD"),
      "with retroactive claim date": dayjs(
        defaultAbsencePeriod.absence_period_end_date
      )
        .add(14, "day")
        .format("YYYY-MM-DD"),
    };
    const approvalDateScenarios = Object.keys(approvalDate) as Array<
      keyof typeof approvalDate
    >;

    it("does not render checkback dates for claims that have at least one payment row", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({
            ...defaultClaimDetailAttributes,
            payments: [
              createMockPayment(
                { status: "Delayed", sent_to_bank_date: null },
                true
              ),
            ],
          }),
        },
        props
      );

      expect(screen.queryByText(/Check back on/)).not.toBeInTheDocument();
      expect(screen.getByTestId("your-payments-intro")).toMatchSnapshot();
    });

    it.each(approvalDateScenarios)(
      "renders intro text for continuous leaves %s ",
      (state) => {
        renderPage(
          PaymentsPage,
          {
            pathname: routes.applications.status.payments,
            addCustomSetup: setupHelper({
              payment_schedule_type: "Leave Start-Based",
              approvalDate: approvalDate[state],
              includeApprovalNotice: true,
            }),
          },
          props
        );
        const table = screen.getByRole("table");
        expect(table).toBeInTheDocument();
        expect(screen.getByTestId("your-payments-intro")).toMatchSnapshot();
      }
    );

    it.each(approvalDateScenarios)(
      "renders intro text for reduced schedule leaves %s ",
      (state) => {
        renderPage(
          PaymentsPage,
          {
            pathname: routes.applications.status.payments,
            addCustomSetup: setupHelper({
              approvalDate: approvalDate[state],
              includeApprovalNotice: true,
              payment_schedule_type: "Leave Start-Based",
              absence_periods: [
                createAbsencePeriod({
                  ...defaultAbsencePeriod,
                  period_type: "Reduced Schedule",
                }),
              ],
            }),
          },
          props
        );
        expect(screen.getByTestId("your-payments-intro")).toMatchSnapshot();
        const table = screen.getByRole("table");
        expect(table).toBeInTheDocument();
      }
    );

    it.each(approvalDateScenarios)(
      "renders intro text for continous leaves %s if claim has reduced and continuous leaves",
      (state) => {
        const reducedAbsencePeriod: AbsencePeriod = createAbsencePeriod({
          ...defaultAbsencePeriod,
          period_type: "Reduced Schedule",
        });
        renderPage(
          PaymentsPage,
          {
            pathname: routes.applications.status.payments,
            addCustomSetup: setupHelper({
              payment_schedule_type: "Leave Start-Based",
              approvalDate: approvalDate[state],
              includeApprovalNotice: true,
              absence_periods: [defaultAbsencePeriod, reducedAbsencePeriod],
            }),
          },
          props
        );
        expect(screen.getByTestId("your-payments-intro")).toMatchSnapshot();
        const table = screen.getByRole("table");
        expect(table).toBeInTheDocument();
      }
    );

    it("does not render the Payments table when no payments are available and leave is intermittent", () => {
      const intermittenAbsencePeriod: AbsencePeriod = createAbsencePeriod({
        ...defaultAbsencePeriod,
        period_type: "Intermittent",
      });
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({
            absence_periods: [defaultAbsencePeriod, intermittenAbsencePeriod],
          }),
        },
        {
          ...props,
        }
      );

      const intermittentUnpaidIntroText =
        "This application has an unpaid 7-day waiting period that begins the first day leave is reported to be taken";
      expect(
        screen.getByText(intermittentUnpaidIntroText, { exact: false })
      ).toBeInTheDocument();
      const table = screen.queryByRole("table");
      expect(table).not.toBeInTheDocument();
    });

    it("renders checkback date with 6 business days from current date if retroactive", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({
            approvalDate: "2021-12-30",
            includeApprovalNotice: true,
            payment_schedule_type: "Leave Start-Based",
          }),
        },
        props
      );

      expect(
        screen.queryByText("January 10, 2022", { exact: false })
      ).toBeInTheDocument();
    });
  });

  describe("Post FINEOS deploy", () => {
    it("renders the phase 3 Payments table", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({
            payments: [
              createMockPayment({ status: "Sent to bank" }, true),
              createMockPayment(
                { status: "Delayed", sent_to_bank_date: null },
                true
              ),
              createMockPayment(
                { status: "Pending", sent_to_bank_date: null },
                true
              ),
              createMockPayment({ status: "Sent to bank" }, true),
            ],
          }),
        },
        {
          ...props,
        }
      );

      const table = screen.getByRole("table");
      expect(table).toBeInTheDocument();
      expect(within(table).getAllByRole("rowgroup")).toHaveLength(2);

      const columns = within(table).getAllByRole("columnheader");
      expect(columns).toHaveLength(3);

      const rows = within(table).getAllByRole("row");
      expect(rows).toHaveLength(6); // 1 header row + 4 mock payment rows + 1 waiting period row
    });

    it("renders the `payment-information` section", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({}),
        },
        props
      );

      expect(screen.queryByTestId("payment-information")).toBeInTheDocument();
    });

    it("renders the `help` section containing questions and feedback", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({}),
        },
        props
      );

      const section = screen.getByTestId("helpSection");
      expect(section).toMatchSnapshot();
      // Text should appear in <PaymentFAQSection>
      const details = screen.getAllByText(/call the contact center/i);
      expect(details.length).toBe(2);
    });
  });

  it("does not display the top navigation tabs", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({}),
      },
      props
    );

    // Content in the tabs is duplicated in the side navigation so we cant check for content
    expect(
      screen.queryByTestId("status-navigation-tabs")
    ).not.toBeInTheDocument();
  });

  it("displays the side navigation tabs", () => {
    renderPage(
      PaymentsPage,
      {
        pathname: routes.applications.status.payments,
        addCustomSetup: setupHelper({}),
      },
      props
    );
    expect(screen.queryByTestId("status-side-navigation")).toBeInTheDocument();
  });

  describe("with prepaid debit card payments", () => {
    const renderPageWithPrepaidCards = () =>
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({
            claimDetailAttrs: {
              created_at: "2024-12-11",
              payment_preference: {
                account_number: null,
                bank_account_type: null,
                payment_method: "Prepaid Card",
                routing_number: null,
              },
            },
          }),
        },
        props
      );

    it("shows an accurate card arrival date", () => {
      renderPageWithPrepaidCards();
      expect(
        // mock claim data has a created_at date of Dec 11
        // 10 business days from that date (not including holidays) is Dec 25
        screen.queryByText("December 25, 2024", { exact: false })
      ).toBeInTheDocument();
    });

    describe("when arrival date has passed", () => {
      it("shows info about when the prepaid card should have arrived if arrival date has passed", () => {
        renderPageWithPrepaidCards();
        expect(
          screen.queryByText("Your card should have arrived by", {
            exact: false,
          })
        ).toBeInTheDocument();
      });

      it("shows contact details for US Bank in intro section and get help section", () => {
        renderPageWithPrepaidCards();
        expect(
          screen.getAllByText("call U.S. Bank", { exact: false })
        ).toHaveLength(2);
      });
    });

    describe("when arrival date is in the future", () => {
      const today = dayjs();
      const cardArrivalDateInFuture = today.add(1, "day");
      const renderPageWithPrepaidCardsArrivalDateInFuture = () =>
        renderPage(
          PaymentsPage,
          {
            pathname: routes.applications.status.payments,
            addCustomSetup: setupHelper({
              claimDetailAttrs: {
                created_at: today.toString(),
                claim_payment_info: new ClaimPaymentInfo({
                  card_arrival_date: cardArrivalDateInFuture.toString(),
                }),
                payment_preference: {
                  account_number: null,
                  bank_account_type: null,
                  payment_method: "Prepaid Card",
                  routing_number: null,
                },
              },
            }),
          },
          props
        );

      it("shows info about when the prepaid card will arrive when card arrival date is in the future", () => {
        renderPageWithPrepaidCardsArrivalDateInFuture();
        expect(
          screen.queryByText(
            "You should expect to receive your prepaid card by",
            { exact: false }
          )
        ).toBeInTheDocument();
      });

      it("shows contact details for US Bank", () => {
        renderPageWithPrepaidCardsArrivalDateInFuture();
        expect(
          screen.queryByText("call U.S. Bank", { exact: false })
        ).toBeInTheDocument();
      });
    });
  });

  describe("with claim spanning benefit years", () => {
    it("displays all waiting periods", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({
            waitingPeriods: spanningBenefitYearWaitingPeriods,
            claimDetailAttrs: { does_claim_span_benefit_years: true },
          }),
        },
        props
      );
      expect(screen.queryAllByText("Unpaid Waiting Period").length).toBe(2);
    });
  });

  describe("with claim not spanning benefit years", () => {
    it("shows default waiting period", () => {
      renderPage(
        PaymentsPage,
        {
          pathname: routes.applications.status.payments,
          addCustomSetup: setupHelper({
            waitingPeriods: spanningBenefitYearWaitingPeriods,
            claimDetailAttrs: { does_claim_span_benefit_years: false },
          }),
        },
        props
      );
      expect(screen.queryAllByText("Unpaid Waiting Period").length).toBe(1); // portal creates a default waiting period
    });
  });
});
