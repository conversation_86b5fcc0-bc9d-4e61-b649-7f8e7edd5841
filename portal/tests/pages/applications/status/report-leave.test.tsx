import { createAbsencePeriod, renderPage } from "tests/test-utils";

import { AbsencePeriod } from "src/models/AbsencePeriod";
import ApiResourceCollection from "src/models/ApiResourceCollection";
import { AppLogic } from "src/hooks/useAppLogic";
import ChangeRequest from "src/models/ChangeRequest";
import ClaimDetail from "src/models/ClaimDetail";
import EpisodicLeavePeriodDetail from "src/models/EpisodicLeavePeriodDetail";
import { IntermittentLeaveResponse } from "src/models/IntermittentLeaveEpisode";
import ReportIntermittentLeave from "src/pages/applications/status/report-leave";
import routes from "src/routes";
import { screen } from "@testing-library/react";

interface SetupOptions {
  absence_periods?: AbsencePeriod[];
  goTo?: jest.Mock;
  useDefaultClaim?: boolean;
  errors?: Error[];
  reportedLeaveEpisodes?: IntermittentLeaveResponse[];
}

const leave_detail1 = new EpisodicLeavePeriodDetail({
  frequency: 1,
  frequency_interval: 1,
  frequency_interval_basis: "Weeks",
  duration: 4,
  duration_basis: "Hours",
  start_date: "2024-01-20",
  end_date: "2024-02-20",
});

const leave_detail2 = new EpisodicLeavePeriodDetail({
  frequency: 15,
  frequency_interval: 1,
  frequency_interval_basis: "Months",
  duration: 1,
  duration_basis: "Days",
  start_date: "2024-01-20",
  end_date: "2024-02-20",
});

const absence_period1 = createAbsencePeriod({
  period_type: "Intermittent",
  absence_period_start_date: "2021-10-21",
  absence_period_end_date: "2021-12-30",
  reason: "Child Bonding",
  request_decision: "Approved",
  episodic_leave_period_detail: leave_detail1,
});

const absence_period2 = createAbsencePeriod({
  period_type: "Intermittent",
  absence_period_start_date: "2021-10-21",
  absence_period_end_date: "2021-12-30",
  reason: "Child Bonding",
  request_decision: "Approved",
  episodic_leave_period_detail: leave_detail2,
});

const defaultClaimDetailAttributes = {
  application_id: "mock-application-id",
  fineos_absence_id: "mock-absence-case-id",
  employer: {
    employer_fein: "12-1234567",
    employer_dba: "Acme",
    employer_id: "mock-employer-id",
  },
  absence_periods: [absence_period1, absence_period2],
  payment_schedule_type: undefined,
};

const props = {
  query: {
    absence_id: defaultClaimDetailAttributes.fineos_absence_id,
  },
};

const setupHelper =
  ({
    absence_periods = [absence_period1, absence_period2],
    goTo = jest.fn(),
    useDefaultClaim = true,
    errors = [],
    reportedLeaveEpisodes = [],
  }: SetupOptions) =>
  (appLogicHook: AppLogic) => {
    appLogicHook.claims.claimDetail = useDefaultClaim
      ? new ClaimDetail({
          ...defaultClaimDetailAttributes,
          absence_periods,
        })
      : undefined;
    appLogicHook.claims.loadClaimDetail = jest.fn();
    appLogicHook.errors = errors;
    appLogicHook.documents.loadAll = jest.fn();
    appLogicHook.documents.hasLoadedClaimDocuments = () => true;
    appLogicHook.holidays.holidays = [];
    appLogicHook.holidays.loadHolidays = jest.fn();
    appLogicHook.holidays.hasLoadedHolidays = true;
    appLogicHook.holidays.isLoadingHolidays = false;
    appLogicHook.portalFlow.goTo = goTo;
    appLogicHook.payments.loadPayments = jest.fn();
    appLogicHook.changeRequests.loadAll = jest.fn();
    appLogicHook.changeRequests.hasLoadedChangeRequests = () => true;
    appLogicHook.changeRequests.isLoadingChangeRequests = () => false;
    appLogicHook.changeRequests.changeRequests =
      new ApiResourceCollection<ChangeRequest>("change_request_id", []);
    appLogicHook.reportLeaveEpisode.reportedLeaveEpisodes =
      reportedLeaveEpisodes;
  };

const renderReportIntermittentLeavePage = (
  setupHelperParameters: SetupOptions = {}
) => {
  renderPage(
    ReportIntermittentLeave,
    {
      pathname: routes.applications.status.reportIntermittentLeave,
      addCustomSetup: setupHelper(setupHelperParameters),
    },
    props
  );
};

describe("Report Leave", () => {
  it("renders the page", () => {
    renderReportIntermittentLeavePage();

    expect(
      screen.getByRole("heading", {
        name: "Need to change your intermittent leave schedule?",
      })
    ).toBeInTheDocument();
  });

  it("shows ApprovedIntermittentLeaveSchedules component", () => {
    renderReportIntermittentLeavePage();

    expect(
      screen.getAllByTitle("claims status leave period dates")
    ).toHaveLength(2);
  });

  it("shows the page heading", () => {
    renderReportIntermittentLeavePage();

    expect(
      screen.getByRole("heading", { name: "View and report leave hours" })
    ).toBeInTheDocument();
  });

  it("shows ReportedLeaveHoursTable component", () => {
    renderReportIntermittentLeavePage();

    expect(screen.queryByText("Reported leave hours")).toBeInTheDocument();
  });

  it("shows ReportIntermittentLeaveEpisode component", () => {
    renderReportIntermittentLeavePage();

    expect(
      screen.getByTestId("report-intermittent-leave-episode")
    ).toBeInTheDocument();
  });

  it("displays successful when reported episodes exist", () => {
    renderReportIntermittentLeavePage({
      reportedLeaveEpisodes: [
        new IntermittentLeaveResponse({
          date_of_leave: "2023-11-29",
          date_reported: "2023-12-01",
          duration_in_minutes: 30,
          episode_id: "episode_id",
          episode_leave_request_id: "episode_leave_request_id",
          episode_type: "Incapacity",
          status: "Pending",
        }),
      ],
    });

    expect(
      screen.getByText(/Your leave hours were submitted/)
    ).toBeInTheDocument();
  });

  it("does not display successful when reported episodes do not exist", () => {
    renderReportIntermittentLeavePage({
      reportedLeaveEpisodes: [],
    });

    expect(
      screen.queryByText(/Your leave hours were submitted/)
    ).not.toBeInTheDocument();
  });

  it("does not display the top navigation tabs", () => {
    renderReportIntermittentLeavePage({
      reportedLeaveEpisodes: [],
    });

    // Content in the tabs is duplicated in the side navigation so we cant check for content
    expect(
      screen.queryByTestId("status-navigation-tabs")
    ).not.toBeInTheDocument();
  });

  it("displays the side navigation tabs", () => {
    renderReportIntermittentLeavePage({
      reportedLeaveEpisodes: [],
    });

    expect(screen.queryByTestId("status-side-navigation")).toBeInTheDocument();
  });
});
