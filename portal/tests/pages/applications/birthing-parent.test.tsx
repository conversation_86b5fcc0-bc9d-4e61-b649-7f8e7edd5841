import { BirthingParent } from "src/pages/applications/birthing-parent";
import { renderPage } from "tests/test-utils";
import { screen } from "@testing-library/react";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

describe("BirthingParent", () => {
  const render = () => {
    const options = {};

    return renderPage(BirthingParent, options, {});
  };

  describe("with the enableMedToBondIntakeEnhancements FF disabled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableMedToBondIntakeEnhancements: false,
      });
    });

    it("renders PageNotFound", () => {
      render();
      expect(
        screen.getByRole("heading", { name: "Page not found" })
      ).toBeInTheDocument();
    });
  });

  describe("with the enableMedToBondIntakeEnhancements FF enabled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableMedToBondIntakeEnhancements: true,
      });
    });

    it("renders the birthing-parent page", () => {
      render();
      expect(
        screen.getByRole("heading", { name: "Birthing parent" })
      ).toBeInTheDocument();
      expect(
        screen.getByText("Coming soon to a browser near you.")
      ).toBeInTheDocument();
    });
  });
});
