import { MockBenefitsApplicationBuilder, renderPage } from "tests/test-utils";
import { screen, waitFor } from "@testing-library/react";

import NotifiedEmployer from "src/pages/applications/notified-employer";
import { setupBenefitsApplications } from "tests/test-utils/helpers";
import userEvent from "@testing-library/user-event";

const updateClaim = jest.fn(() => {
  return Promise.resolve();
});

const setup = (claim = new MockBenefitsApplicationBuilder().create()) => {
  return renderPage(
    NotifiedEmployer,
    {
      addCustomSetup: (appLogic) => {
        setupBenefitsApplications(appLogic, [claim]);
        appLogic.benefitsApplications.update = updateClaim;
      },
    },
    { query: { claim_id: "mock_application_id" } }
  );
};

describe("NotifiedEmployer", () => {
  it("renders the page", () => {
    const { container } = setup();
    expect(container).toMatchSnapshot();
  });

  it("shows employer notification date question when user selects yes to having notified employer", async () => {
    setup();
    await userEvent.click(screen.getByRole("radio", { name: "Yes" }));
    await waitFor(() => {
      expect(screen.getByText(/When did you tell them?/)).toBeInTheDocument();
    });
  });

  it("hides must notify employer warning when user selects yes to having notified employer", async () => {
    setup();
    await userEvent.click(screen.getByRole("radio", { name: "Yes" }));

    expect(
      screen.queryByText(
        /You can continue to enter information about your leave. Before you can submit your application, you must tell your employer that you’re taking$t(chars.nbsp)leave. Notify your employer at least 30 days before the start of your leave if possible./
      )
    ).not.toBeInTheDocument();
  });

  it("calls claims.update when user submits form with newly entered data", async () => {
    setup();

    await userEvent.click(screen.getByRole("radio", { name: "Yes" }));
    const [monthInput, dayInput, yearInput] = screen.getAllByRole("textbox");
    await userEvent.type(monthInput, "6");
    await userEvent.type(dayInput, "25");
    await userEvent.type(yearInput, "2020");
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );
    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        leave_details: {
          employer_notified: true,
          employer_notification_date: "2020-06-25",
        },
      });
    });
  });

  it("calls claims.update when user submits form with previously entered data", async () => {
    const claim = new MockBenefitsApplicationBuilder()
      .notifiedEmployer()
      .create();

    setup(claim);
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );
    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        leave_details: {
          employer_notified: true,
          employer_notification_date:
            claim.leave_details.employer_notification_date,
        },
      });
    });
  });

  describe("when user selects no to having notified employer", () => {
    it("hides employer notification date question", async () => {
      setup();

      await userEvent.click(screen.getByRole("radio", { name: "No" }));
      expect(
        screen.queryByRole("textbox", { name: "Year" })
      ).not.toBeInTheDocument();
    });

    it("shows employer notification warning", async () => {
      setup();

      await userEvent.click(screen.getByRole("radio", { name: "No" }));

      expect(
        screen.queryByText(
          /You can continue to enter information about your leave/
        )
      ).not.toBeInTheDocument();

      expect(
        screen.queryByText(/You can continue your application/)
      ).toBeInTheDocument();

      expect(
        screen.queryByText(
          /Your leave request may be delayed or denied if you do not notify your employer/
        )
      ).toBeInTheDocument();
    });
  });

  describe("when present on the claim, presents an applicant's employer info", () => {
    const dba = "TEST COMPANY";
    const fein = "12-3456789";
    it("returns employer-specific label when employer FEIN is valid", () => {
      const claim = new MockBenefitsApplicationBuilder().employed().create();
      setup(claim);

      expect(
        screen.getByText(/Have you told your employer/i)
      ).toBeInTheDocument();
      expect(screen.getByText(fein)).toBeInTheDocument();
      expect(screen.queryByText(dba)).not.toBeInTheDocument();
    });

    it("returns employer-specific label when employer DBA and FEIN is valid", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .employed()
        .employerDba(dba)
        .create();
      setup(claim);

      expect(screen.getByText(/Have you told/i)).toBeInTheDocument();
      expect(screen.getByText(dba)).toBeInTheDocument();
      expect(screen.getByText(fein)).toBeInTheDocument();
    });
  });
});
