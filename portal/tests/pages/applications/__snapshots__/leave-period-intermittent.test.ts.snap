// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LeavePeriodIntermittent renders a variation of the page when claimant is taking bonding leave 1`] = `
<div>
  <form
    class="usa-form"
    method="post"
  >
    <h1
      class="js-title margin-top-0 margin-bottom-2 font-heading-sm line-height-sans-3"
      tabindex="-1"
    >
      Leave details
    </h1>
    <fieldset
      class="usa-fieldset margin-top-3 usa-form-group"
    >
      <legend
        class="usa-label text-bold usa-legend font-heading-lg line-height-sans-3 margin-bottom-1 maxw-tablet"
      >
        Do you need to take off work in uneven blocks of time (
        <a
          href="https://mass.gov/pfml/leave-schedules"
          rel="noopener noreferrer"
          target="_blank"
        >
          intermittent leave
        </a>
        )?
      </legend>
      <span
        class="display-block line-height-sans-5 measure-5 usa-intro"
      >
        For example, you need to take time off for: 
        <ul
          class="usa-list"
        >
          <li>
            Court dates for your foster child
          </li>
          <li>
            Social worker visits
          </li>
          <li>
            Gaps in your childcare
          </li>
        </ul>
      </span>
      <div
        class="margin-top-3"
      >
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r1»"
            name="has_intermittent_leave_periods"
            type="radio"
            value="true"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r1»"
          >
            Yes
          </label>
        </div>
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r2»"
            name="has_intermittent_leave_periods"
            type="radio"
            value="false"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r2»"
          >
            No
          </label>
        </div>
      </div>
    </fieldset>
    <button
      class="usa-button position-relative margin-top-4"
      type="submit"
    >
      Save and continue
    </button>
  </form>
</div>
`;

exports[`LeavePeriodIntermittent renders a variation of the page when claimant is taking caring leave 1`] = `
<div>
  <form
    class="usa-form"
    method="post"
  >
    <h1
      class="js-title margin-top-0 margin-bottom-2 font-heading-sm line-height-sans-3"
      tabindex="-1"
    >
      Leave details
    </h1>
    <div
      class="usa-alert usa-alert--info c-alert--neutral"
      role="region"
      tabindex="-1"
    >
      <div
        class="usa-alert__body"
      >
        <div
          class="usa-alert__text"
          title="alert text"
        >
          You will need a 
          <a
            href="https://www.mass.gov/family-caring-form"
            rel="noopener noreferrer"
            target="_blank"
          >
            Certification of Your Family Member’s Serious Health Condition
          </a>
           for this section.
        </div>
      </div>
    </div>
    <fieldset
      class="usa-fieldset margin-top-3 usa-form-group"
    >
      <legend
        class="usa-label text-bold usa-legend font-heading-lg line-height-sans-3 margin-bottom-1 maxw-tablet"
      >
        Do you need to take off work in uneven blocks of time (
        <a
          href="https://mass.gov/pfml/leave-schedules"
          rel="noopener noreferrer"
          target="_blank"
        >
          intermittent leave
        </a>
        )?
      </legend>
      <span
        class="display-block line-height-sans-5 measure-5 usa-intro"
      >
         Your answer must match the Certification of Your Family Member’s Serious Health Condition.
      </span>
      <div
        class="margin-top-3"
      >
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r6»"
            name="has_intermittent_leave_periods"
            type="radio"
            value="true"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r6»"
          >
            Yes
          </label>
        </div>
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r7»"
            name="has_intermittent_leave_periods"
            type="radio"
            value="false"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r7»"
          >
            No
          </label>
        </div>
      </div>
    </fieldset>
    <button
      class="usa-button position-relative margin-top-4"
      type="submit"
    >
      Save and continue
    </button>
  </form>
</div>
`;

exports[`LeavePeriodIntermittent renders a variation of the page when claimant is taking medical leave 1`] = `
<div>
  <form
    class="usa-form"
    method="post"
  >
    <h1
      class="js-title margin-top-0 margin-bottom-2 font-heading-sm line-height-sans-3"
      tabindex="-1"
    >
      Leave details
    </h1>
    <div
      class="usa-alert usa-alert--info c-alert--neutral"
      role="region"
      tabindex="-1"
    >
      <div
        class="usa-alert__body"
      >
        <div
          class="usa-alert__text"
          title="alert text"
        >
          You will need a 
          <a
            href="https://www.mass.gov/medical-leave-form"
            rel="noopener noreferrer"
            target="_blank"
          >
            Certification of Your Serious Health Condition
          </a>
           for this section.
        </div>
      </div>
    </div>
    <fieldset
      class="usa-fieldset margin-top-3 usa-form-group"
    >
      <legend
        class="usa-label text-bold usa-legend font-heading-lg line-height-sans-3 margin-bottom-1 maxw-tablet"
      >
        Do you need to take off work in uneven blocks of time (
        <a
          href="https://mass.gov/pfml/leave-schedules"
          rel="noopener noreferrer"
          target="_blank"
        >
          intermittent leave
        </a>
        )?
      </legend>
      <span
        class="display-block line-height-sans-5 measure-5 usa-intro"
      >
        Your answer must match the Certification of Your Serious Health Condition.
      </span>
      <div
        class="margin-top-3"
      >
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«rb»"
            name="has_intermittent_leave_periods"
            type="radio"
            value="true"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«rb»"
          >
            Yes
          </label>
        </div>
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«rc»"
            name="has_intermittent_leave_periods"
            type="radio"
            value="false"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«rc»"
          >
            No
          </label>
        </div>
      </div>
    </fieldset>
    <button
      class="usa-button position-relative margin-top-4"
      type="submit"
    >
      Save and continue
    </button>
  </form>
</div>
`;

exports[`LeavePeriodIntermittent renders a variation of the page when claimant is taking pregnancy leave 1`] = `
<div>
  <form
    class="usa-form"
    method="post"
  >
    <h1
      class="js-title margin-top-0 margin-bottom-2 font-heading-sm line-height-sans-3"
      tabindex="-1"
    >
      Leave details
    </h1>
    <div
      class="usa-alert usa-alert--info c-alert--neutral"
      role="region"
      tabindex="-1"
    >
      <div
        class="usa-alert__body"
      >
        <div
          class="usa-alert__text"
          title="alert text"
        >
          You will need a 
          <a
            href="https://www.mass.gov/medical-leave-form"
            rel="noopener noreferrer"
            target="_blank"
          >
            Certification of Your Serious Health Condition
          </a>
           for this section.
        </div>
      </div>
    </div>
    <fieldset
      class="usa-fieldset margin-top-3 usa-form-group"
    >
      <legend
        class="usa-label text-bold usa-legend font-heading-lg line-height-sans-3 margin-bottom-1 maxw-tablet"
      >
        Do you need to take off work in uneven blocks of time (
        <a
          href="https://mass.gov/pfml/leave-schedules"
          rel="noopener noreferrer"
          target="_blank"
        >
          intermittent leave
        </a>
        )?
      </legend>
      <span
        class="display-block line-height-sans-5 measure-5 usa-intro"
      >
        Your answer must match the Certification of Your Serious Health Condition.
      </span>
      <div
        class="margin-top-3"
      >
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«rg»"
            name="has_intermittent_leave_periods"
            type="radio"
            value="true"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«rg»"
          >
            Yes
          </label>
        </div>
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«rh»"
            name="has_intermittent_leave_periods"
            type="radio"
            value="false"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«rh»"
          >
            No
          </label>
        </div>
      </div>
    </fieldset>
    <button
      class="usa-button position-relative margin-top-4"
      type="submit"
    >
      Save and continue
    </button>
  </form>
</div>
`;
