// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LeavePeriodContinuous renders a variation of the page when claimant is taking bonding leave 1`] = `
<div>
  <form
    class="usa-form"
    method="post"
  >
    <h1
      class="js-title margin-top-0 margin-bottom-2 font-heading-sm line-height-sans-3"
      tabindex="-1"
    >
      Leave details
    </h1>
    <fieldset
      class="usa-fieldset margin-top-3 usa-form-group"
    >
      <legend
        class="usa-label text-bold usa-legend font-heading-lg line-height-sans-3 margin-bottom-1 maxw-tablet"
      >
        Do you need to take off work completely for a period of time (continuous leave)?
      </legend>
      <div
        class="margin-top-3"
      >
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r0»"
            name="has_continuous_leave_periods"
            type="radio"
            value="true"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r0»"
          >
            Yes
          </label>
        </div>
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r1»"
            name="has_continuous_leave_periods"
            type="radio"
            value="false"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r1»"
          >
            No
          </label>
        </div>
      </div>
    </fieldset>
    <button
      class="usa-button position-relative margin-top-4"
      type="submit"
    >
      Save and continue
    </button>
  </form>
</div>
`;

exports[`LeavePeriodContinuous renders a variation of the page when claimant is taking caring leave 1`] = `
<div>
  <form
    class="usa-form"
    method="post"
  >
    <h1
      class="js-title margin-top-0 margin-bottom-2 font-heading-sm line-height-sans-3"
      tabindex="-1"
    >
      Leave details
    </h1>
    <div
      class="usa-alert usa-alert--info c-alert--neutral"
      role="region"
      tabindex="-1"
    >
      <div
        class="usa-alert__body"
      >
        <div
          class="usa-alert__text"
          title="alert text"
        >
          You will need a 
          <a
            href="https://www.mass.gov/family-caring-form"
            rel="noopener noreferrer"
            target="_blank"
          >
            Certification of Your Family Member’s Serious Health Condition
          </a>
           for this section.
        </div>
      </div>
    </div>
    <fieldset
      class="usa-fieldset margin-top-3 usa-form-group"
    >
      <legend
        class="usa-label text-bold usa-legend font-heading-lg line-height-sans-3 margin-bottom-1 maxw-tablet"
      >
        Do you need to take off work completely for a period of time (continuous leave)?
      </legend>
      <span
        class="display-block line-height-sans-5 measure-5 usa-intro"
      >
        Your answer must match the Certification of Your Family Member’s Serious Health Condition.
      </span>
      <div
        class="margin-top-3"
      >
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r4»"
            name="has_continuous_leave_periods"
            type="radio"
            value="true"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r4»"
          >
            Yes
          </label>
        </div>
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r5»"
            name="has_continuous_leave_periods"
            type="radio"
            value="false"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r5»"
          >
            No
          </label>
        </div>
      </div>
    </fieldset>
    <button
      class="usa-button position-relative margin-top-4"
      type="submit"
    >
      Save and continue
    </button>
  </form>
</div>
`;

exports[`LeavePeriodContinuous renders a variation of the page when claimant is taking medical leave 1`] = `
<div>
  <form
    class="usa-form"
    method="post"
  >
    <h1
      class="js-title margin-top-0 margin-bottom-2 font-heading-sm line-height-sans-3"
      tabindex="-1"
    >
      Leave details
    </h1>
    <div
      class="usa-alert usa-alert--info c-alert--neutral"
      role="region"
      tabindex="-1"
    >
      <div
        class="usa-alert__body"
      >
        <div
          class="usa-alert__text"
          title="alert text"
        >
          You will need a 
          <a
            href="https://www.mass.gov/medical-leave-form"
            rel="noopener noreferrer"
            target="_blank"
          >
            Certification of Your Serious Health Condition
          </a>
           for this section.
        </div>
      </div>
    </div>
    <fieldset
      class="usa-fieldset margin-top-3 usa-form-group"
    >
      <legend
        class="usa-label text-bold usa-legend font-heading-lg line-height-sans-3 margin-bottom-1 maxw-tablet"
      >
        Do you need to take off work completely for a period of time (continuous leave)?
      </legend>
      <span
        class="display-block line-height-sans-5 measure-5 usa-intro"
      >
        Your answer must match the Certification of Your Serious Health Condition.
      </span>
      <div
        class="margin-top-3"
      >
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r8»"
            name="has_continuous_leave_periods"
            type="radio"
            value="true"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r8»"
          >
            Yes
          </label>
        </div>
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«r9»"
            name="has_continuous_leave_periods"
            type="radio"
            value="false"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«r9»"
          >
            No
          </label>
        </div>
      </div>
    </fieldset>
    <button
      class="usa-button position-relative margin-top-4"
      type="submit"
    >
      Save and continue
    </button>
  </form>
</div>
`;

exports[`LeavePeriodContinuous renders a variation of the page when claimant is taking pregnancy leave 1`] = `
<div>
  <form
    class="usa-form"
    method="post"
  >
    <h1
      class="js-title margin-top-0 margin-bottom-2 font-heading-sm line-height-sans-3"
      tabindex="-1"
    >
      Leave details
    </h1>
    <div
      class="usa-alert usa-alert--info c-alert--neutral"
      role="region"
      tabindex="-1"
    >
      <div
        class="usa-alert__body"
      >
        <div
          class="usa-alert__text"
          title="alert text"
        >
          You will need a 
          <a
            href="https://www.mass.gov/medical-leave-form"
            rel="noopener noreferrer"
            target="_blank"
          >
            Certification of Your Serious Health Condition
          </a>
           for this section.
        </div>
      </div>
    </div>
    <fieldset
      class="usa-fieldset margin-top-3 usa-form-group"
    >
      <legend
        class="usa-label text-bold usa-legend font-heading-lg line-height-sans-3 margin-bottom-1 maxw-tablet"
      >
        Do you need to take off work completely for a period of time (continuous leave)?
      </legend>
      <span
        class="display-block line-height-sans-5 measure-5 usa-intro"
      >
        Your answer must match the Certification of Your Serious Health Condition.
      </span>
      <div
        class="margin-top-3"
      >
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«rc»"
            name="has_continuous_leave_periods"
            type="radio"
            value="true"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«rc»"
          >
            Yes
          </label>
        </div>
        <div
          class="usa-radio measure-5 bg-transparent"
        >
          <input
            class="usa-radio__input"
            id="InputChoice«rd»"
            name="has_continuous_leave_periods"
            type="radio"
            value="false"
          />
          <label
            class="usa-radio__label"
            for="InputChoice«rd»"
          >
            No
          </label>
        </div>
      </div>
    </fieldset>
    <button
      class="usa-button position-relative margin-top-4"
      type="submit"
    >
      Save and continue
    </button>
  </form>
</div>
`;
