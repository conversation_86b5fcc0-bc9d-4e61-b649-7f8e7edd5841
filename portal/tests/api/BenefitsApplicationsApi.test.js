import PaymentPreference, {
  PaymentPreferenceMethod,
} from "src/models/PaymentPreference";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import BenefitsApplication from "src/models/BenefitsApplication";
import BenefitsApplicationsApi from "src/api/BenefitsApplicationsApi";
import { ValidationError } from "src/errors";
import { isFeatureEnabled } from "src/services/featureFlags";
import { mockAuth } from "tests/test-utils";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

jest.mock("../../src/services/tracker");

const mockFetch = ({
  response = { data: [], errors: [], warnings: [] },
  ok = true,
  status = 200,
}) => {
  return jest.fn().mockResolvedValueOnce({
    json: jest.fn().mockResolvedValueOnce(response),
    ok,
    status,
  });
};

describe("BenefitsApplicationsApi", () => {
  /** @type {BenefitsApplicationsApi} */
  let claimsApi;
  const accessTokenJwt =
    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa.bbbbbbbbbbbbbbbbbbb.ccccccccccccccccccccccccccccccccccccccccccc";
  const baseRequestHeaders = {
    Authorization: `Bearer ${accessTokenJwt}`,
    "Content-Type": "application/json",
    "X-FF-Disable-Overlapping-Benefit-Year-Claim-Creation": "true",
    "X-FF-Disable-Application-Split-On-Benefit-Year-During-Submit": "true",
  };

  beforeEach(() => {
    jest.resetAllMocks();
    mockAuth(true, accessTokenJwt);

    claimsApi = new BenefitsApplicationsApi();
  });

  describe("getClaim", () => {
    let claim;

    beforeEach(() => {
      claim = new BenefitsApplication({
        application_id: "mock-application_id",
      });
      global.fetch = mockFetch({
        response: {
          data: claim,
          warnings: [
            {
              field: "first_name",
              type: "required",
              message: "First name is required",
            },
          ],
        },
      });
      if (isFeatureEnabled("enableMmgIDV")) {
        baseRequestHeaders["X-FF-Enable-Mmg-IDV"] = "true";
      } else {
        baseRequestHeaders["X-FF-Enable-Mmg-IDV"] = "false";
      }
      if (isFeatureEnabled("enableUniversalProfileIDV")) {
        baseRequestHeaders["X-FF-Enable-Universal-Profile-IDV"] = "true";
      } else {
        baseRequestHeaders["X-FF-Enable-Universal-Profile-IDV"] = "false";
      }
      if (isFeatureEnabled("enableOccupationDataCollection")) {
        baseRequestHeaders["X-FF-Enable-Occupation-Data-Collection"] = "true";
      } else {
        baseRequestHeaders["X-FF-Enable-Occupation-Data-Collection"] = "false";
      }
      if (isFeatureEnabled("enableBackendInvalidation")) {
        baseRequestHeaders["X-FF-Enable-Backend-Invalidation"] = "true";
      } else {
        baseRequestHeaders["X-FF-Enable-Backend-Invalidation"] = "false";
      }
    });

    it("sends GET request to /applications/:application_id", async () => {
      await claimsApi.getClaim(claim.application_id);
      expect(fetch).toHaveBeenCalledWith(
        `${process.env.apiUrl}/applications/${claim.application_id}`,
        {
          body: null,
          headers: expect.objectContaining(baseRequestHeaders),
          method: "GET",
        }
      );
    });

    it("resolves with claim, and warnings properties", async () => {
      const { claim: claimResponse, ...rest } = await claimsApi.getClaim(
        claim.application_id
      );

      expect(claimResponse).toBeInstanceOf(BenefitsApplication);
      expect(claimResponse).toEqual(claim);
      expect(rest).toMatchInlineSnapshot(`
        {
          "warnings": [
            {
              "field": "first_name",
              "message": "First name is required",
              "namespace": "applications",
              "type": "required",
            },
          ],
        }
      `);
    });

    describe("getClaim - IDV Pilot headers are set to false when the pilot is disabled", () => {
      beforeEach(() => {
        setFeatureFlags({
          enableMmgIDV: false,
          enableUniversalProfileIDV: false,
        });
      });
      it("sends GET request to /applications/:application_id", async () => {
        await claimsApi.getClaim(claim.application_id);
        const copyBaseRequestHeaders = { ...baseRequestHeaders };
        copyBaseRequestHeaders["X-FF-Enable-Universal-Profile-IDV"] = "false";
        copyBaseRequestHeaders["X-FF-Enable-Mmg-IDV"] = "false";

        expect(fetch).toHaveBeenCalledWith(
          `${process.env.apiUrl}/applications/${claim.application_id}`,
          {
            body: null,
            headers: expect.objectContaining(copyBaseRequestHeaders),
            method: "GET",
          }
        );
      });
    });
  });

  describe("getClaims", () => {
    describe("successful request", () => {
      let claim;

      beforeEach(() => {
        claim = new BenefitsApplication({
          application_id: "mock-application_id",
        });
        global.fetch = mockFetch({
          response: {
            data: [
              {
                ...claim,
              },
            ],
          },
        });
      });

      it("sends GET request to /applications", async () => {
        await claimsApi.getClaims();
        expect(fetch).toHaveBeenCalledWith(
          `${process.env.apiUrl}/applications?order_by=created_at&order_direction=descending&page_offset=1`,
          {
            body: null,
            headers: expect.objectContaining(baseRequestHeaders),
            method: "GET",
          }
        );
      });

      it("resolves with claims properties", async () => {
        const { claims: claimsResponse } = await claimsApi.getClaims();

        expect(claimsResponse).toBeInstanceOf(ApiResourceCollection);
        expect(claimsResponse.items).toEqual([claim]);
      });
    });
  });

  describe("createClaim", () => {
    describe("successful request", () => {
      let claim;

      beforeEach(() => {
        claim = new BenefitsApplication({
          application_id: "mock-application_id",
        });

        global.fetch = mockFetch({
          response: {
            data: {
              ...claim,
            },
          },
          status: 201,
        });
      });

      it("sends POST request to /applications", async () => {
        await claimsApi.createClaim();
        expect(fetch).toHaveBeenCalledWith(
          `${process.env.apiUrl}/applications`,
          {
            body: null,
            headers: expect.objectContaining(baseRequestHeaders),
            method: "POST",
          }
        );
      });

      it("resolves with claim properties", async () => {
        const { claim: claimResponse } = await claimsApi.createClaim();

        expect(claimResponse).toBeInstanceOf(BenefitsApplication);
        expect(claimResponse).toEqual(claim);
      });
    });

    describe("unsuccessful request", () => {
      beforeEach(() => {
        global.fetch = mockFetch({
          response: { data: null, errors: [{ type: "invalid" }] },
          status: 400,
          ok: false,
        });
      });

      it("throws error", async () => {
        try {
          await claimsApi.createClaim({});
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect(error.issues[0].namespace).toBe("applications");
        }
      });
    });
  });

  describe("completeClaim", () => {
    let claim;

    beforeEach(() => {
      claim = new BenefitsApplication({
        application_id: "mock-application_id",
      });

      global.fetch = mockFetch({
        response: { data: { ...claim } },
      });
    });

    it("sends POST request to /applications/:application_id/complete-application with certificate document", async () => {
      const certificateDocumentDeferred = false;
      const requestBody = {
        certificate_document_deferred: certificateDocumentDeferred,
      };

      await claimsApi.completeClaim(
        claim.application_id,
        certificateDocumentDeferred
      );
      expect(fetch).toHaveBeenCalledWith(
        `${process.env.apiUrl}/applications/${claim.application_id}/complete-application`,
        {
          body: JSON.stringify(requestBody),
          headers: expect.objectContaining(baseRequestHeaders),
          method: "POST",
        }
      );
    });

    it("sends POST request to /applications/:application_id/complete-application with deferred certificate document", async () => {
      const certificateDocumentDeferred = true;
      const requestBody = {
        certificate_document_deferred: certificateDocumentDeferred,
      };

      await claimsApi.completeClaim(
        claim.application_id,
        certificateDocumentDeferred
      );
      expect(fetch).toHaveBeenCalledWith(
        `${process.env.apiUrl}/applications/${claim.application_id}/complete-application`,
        {
          body: JSON.stringify(requestBody),
          headers: expect.objectContaining(baseRequestHeaders),
          method: "POST",
        }
      );
    });

    it("resolves with claim properties", async () => {
      const { claim: claimResponse } = await claimsApi.completeClaim(
        claim.application_id
      );

      expect(claimResponse).toBeInstanceOf(BenefitsApplication);
      expect(claimResponse).toEqual(claim);
    });
  });

  describe("updateClaim", () => {
    let claim;

    beforeEach(() => {
      claim = new BenefitsApplication({
        application_id: "mock-application_id",
      });

      global.fetch = mockFetch({
        response: { data: { ...claim } },
      });
    });

    it("sends PATCH request to /applications/:application_id", async () => {
      await claimsApi.updateClaim(claim.application_id, claim);

      expect(fetch).toHaveBeenCalledWith(
        `${process.env.apiUrl}/applications/${claim.application_id}`,
        {
          body: JSON.stringify(claim),
          headers: expect.objectContaining(baseRequestHeaders),
          method: "PATCH",
        }
      );
    });

    it("resolves with claim, errors and warnings properties", async () => {
      const { claim: claimResponse, ...rest } = await claimsApi.updateClaim(
        claim.application_id,
        claim
      );

      expect(claimResponse).toBeInstanceOf(BenefitsApplication);
      expect(claimResponse).toEqual(claim);
      expect(rest).toMatchInlineSnapshot(`
        {
          "warnings": [],
        }
      `);
    });
  });

  describe("submitClaim", () => {
    let claim;

    beforeEach(() => {
      claim = new BenefitsApplication({
        application_id: "mock-application_id",
      });

      global.fetch = mockFetch({
        response: { data: { ...claim } },
        status: 201,
      });
    });

    it("sends POST request to /applications/:application_id/submit-application", async () => {
      await claimsApi.submitClaim(claim.application_id);
      expect(fetch).toHaveBeenCalledWith(
        `${process.env.apiUrl}/applications/${claim.application_id}/submit-application`,
        {
          body: null,
          headers: expect.objectContaining(baseRequestHeaders),
          method: "POST",
        }
      );
    });

    it("resolves with claim properties", async () => {
      const { claim: claimResponse } = await claimsApi.submitClaim(
        claim.application_id
      );

      expect(claimResponse).toBeInstanceOf(BenefitsApplication);
      expect(claimResponse).toEqual(claim);
    });
  });

  describe("submitCustomerPaymentPreference", () => {
    let claim, payment_preference;

    beforeEach(() => {
      payment_preference = new PaymentPreference({
        payment_method: PaymentPreferenceMethod.check,
      });

      claim = new BenefitsApplication({ payment_preference });

      global.fetch = mockFetch({
        response: { data: { ...claim } },
        status: 201,
      });
    });

    it("sends POST request to /applications/:application_id/submit-customer-payment-preference", async () => {
      await claimsApi.submitCustomerPaymentPreference(
        claim.application_id,
        payment_preference
      );
      expect(fetch).toHaveBeenCalledWith(
        `${process.env.apiUrl}/applications/${claim.application_id}/submit-customer-payment-preference`,
        {
          body: JSON.stringify(payment_preference),
          headers: expect.objectContaining(baseRequestHeaders),
          method: "POST",
        }
      );
    });

    it("resolves with claim properties", async () => {
      const { claim: claimResponse, ...rest } =
        await claimsApi.submitCustomerPaymentPreference(
          claim.application_id,
          payment_preference
        );

      expect(claimResponse).toBeInstanceOf(BenefitsApplication);
      expect(claimResponse).toEqual(claim);
      expect(rest).toMatchInlineSnapshot(`
        {
          "warnings": [],
        }
      `);
    });
  });

  describe("SubmitTaxWithholdingPreference", () => {
    let claim, tax_preference;

    beforeEach(() => {
      claim = new BenefitsApplication();
      tax_preference = { is_withholding_tax: true };

      global.fetch = mockFetch({
        response: { data: { ...claim } },
        status: 201,
      });
    });

    it("sends POST request to /applications/:application_id/submit-tax-withholding-preference", async () => {
      await claimsApi.submitTaxWithholdingPreference(
        claim.application_id,
        tax_preference
      );

      expect(fetch).toHaveBeenCalledWith(
        `${process.env.apiUrl}/applications/${claim.application_id}/submit-tax-withholding-preference`,
        {
          body: JSON.stringify(tax_preference),
          headers: expect.objectContaining(baseRequestHeaders),
          method: "POST",
        }
      );
    });

    it("resolves with claim properties", async () => {
      const { claim: claimResponse, ...rest } =
        await claimsApi.submitTaxWithholdingPreference(
          claim.application_id,
          tax_preference
        );

      expect(claimResponse).toBeInstanceOf(BenefitsApplication);
      expect(claimResponse).toEqual(claim);
      expect(rest).toMatchInlineSnapshot(`
        {
          "warnings": [],
        }
      `);
    });
  });
});
