import {
  ClaimDocument,
  DocumentType,
  canShowDenialNotice,
  employerLegalDocTypes,
  filterByApplication,
  findDocumentsByTypes,
  getClaimantLegalNotices,
  getDecisionNotices,
  getEmployerLegalNotices,
  getLatestDocumentUploadDate,
  getLeaveCertificationDocs,
  getRFIDocument,
  latestDocumentOfTypeUploadedAt,
  showEmployeeApprovalNotice,
  sortNewToOld,
} from "src/models/Document";
import {
  createMockBenefitsApplicationDocument,
  mockedClaimDocumentsToTestDateSort,
} from "lib/mock-helpers/createMockDocument";

import LeaveReason from "src/models/LeaveReason";

describe("filterByApplication", () => {
  it("returns only Documents associated with the given application", () => {
    const applicationADocument1 = createMockBenefitsApplicationDocument({
      application_id: "a",
    });
    const applicationADocument2 = createMockBenefitsApplicationDocument({
      application_id: "a",
    });
    const applicationBDocument1 = createMockBenefitsApplicationDocument({
      application_id: "b",
    });

    const documents = [
      applicationADocument1,
      applicationADocument2,
      applicationBDocument1,
    ];

    expect(filterByApplication(documents, "a")).toEqual([
      applicationADocument1,
      applicationADocument2,
    ]);
    expect(filterByApplication(documents, "b")).toEqual([
      applicationBDocument1,
    ]);
  });
});

describe("findDocumentsByTypes", () => {
  describe("when no documents are found", () => {
    it("returns an empty array", () => {
      const documentsList = [
        createMockBenefitsApplicationDocument({
          document_type: DocumentType.identityVerification,
        }),
        createMockBenefitsApplicationDocument({
          document_type: DocumentType.certification.medicalCertification,
        }),
      ];
      const documents = findDocumentsByTypes(documentsList, [
        DocumentType.certification["Child Bonding"],
      ]);

      expect(documents).toEqual([]);
    });
  });

  describe("when the documents list has multiple matching documents", () => {
    it("returns an array with all of the matching documents", () => {
      const documentsList = [
        createMockBenefitsApplicationDocument({
          document_type: DocumentType.identityVerification,
        }),
        createMockBenefitsApplicationDocument({
          document_type: DocumentType.identityVerification,
        }),
        createMockBenefitsApplicationDocument({
          document_type: DocumentType.certification.medicalCertification,
        }),
      ];
      const documents = findDocumentsByTypes(documentsList, [
        DocumentType.identityVerification,
      ]);

      expect(documents).toHaveLength(2);
    });
  });

  describe("when the documents list is empty", () => {
    it("returns an empty array", () => {
      const documents = findDocumentsByTypes(
        [],
        [DocumentType.identityVerification]
      );
      expect(documents).toEqual([]);
    });
  });

  it("returns matching documents even if casing of document_type is different", () => {
    const documentsList = [
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.identityVerification,
      }),
    ];
    const documents = findDocumentsByTypes(documentsList, [
      // @ts-expect-error Intentionally using a different casing
      DocumentType.identityVerification.toLocaleLowerCase(),
    ]);

    expect(documents).toHaveLength(1);
  });

  it("returns matching documents when there are multiple document types", () => {
    const documentsList = [
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.identityVerification,
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.certification.medicalCertification,
      }),
    ];
    const documents = findDocumentsByTypes(documentsList, [
      DocumentType.identityVerification,
      DocumentType.certification.medicalCertification,
    ]);

    expect(documents).toHaveLength(2);
  });
});

describe("getLeaveCertificationDocs", () => {
  it("filters out legal notices and ID proof documents", () => {
    const allDocuments = [
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.approvalNotice,
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.identityVerification,
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.certification.medicalCertification,
      }),
      createMockBenefitsApplicationDocument({
        document_type:
          DocumentType.certification["Serious Health Condition - Employee"],
      }),
    ];

    const certDocs = getLeaveCertificationDocs(allDocuments);

    expect(certDocs.map((d) => d.document_type)).toEqual([
      DocumentType.certification.medicalCertification,
      DocumentType.certification["Serious Health Condition - Employee"],
    ]);
  });
});

describe("getClaimantLegalNotices", () => {
  it("filters out notices of non-legal type", () => {
    const legalNoticeTypes = new Set<string>([
      DocumentType.appealAcknowledgment,
      DocumentType.approvalNotice,
      DocumentType.denialNotice,
      DocumentType.requestForInfoNotice,
      DocumentType.withdrawalNotice,
      DocumentType.maximumWeeklyBenefitChangeNotice,
      DocumentType.benefitAmountChangeNotice,
      DocumentType.leaveAllotmentChangeNotice,
      DocumentType.approvedTimeCancelled,
      DocumentType.changeRequestApproved,
      DocumentType.changeRequestDenied,
      DocumentType.overpaymentFullBalanceDemand,
      DocumentType.overpaymentFullBalanceRecovery,
      DocumentType.overpaymentFullBalanceRecoveryManual,
      DocumentType.overpaymentPartialBalance,
      DocumentType.overpaymentPayoff,
      DocumentType.intermittentTimeApprovalNotice,
      DocumentType.appealApproved,
      DocumentType.appealDismissedExempt,
      DocumentType.appealDismissedOther,
      DocumentType.appealHearingVirtualFillable,
      DocumentType.appealModifyDecision,
      DocumentType.appealRFI,
      DocumentType.appealReturnedToAdjudication,
      DocumentType.appealWithdrawn,
      DocumentType.paymentReceivedUpdatedOverpaymentBalance,
      DocumentType.explanationOfWages,
      DocumentType.denialOfApplication,
      DocumentType.approvalOfApplicationChange,
      DocumentType.denialOfApplicationChange,
      DocumentType.approvedLeaveDatesCancelled,
      DocumentType.intermittentTimeReported,
    ]);
    const manyDocumentTypes = [
      DocumentType.appealAcknowledgment,
      DocumentType.approvalNotice,
      DocumentType.certification[LeaveReason.care],
      DocumentType.certification.medicalCertification,
      DocumentType.denialNotice,
      DocumentType.explanationOfWages,
      DocumentType.identityVerification,
      DocumentType.medicalCertification,
      DocumentType.requestForInfoNotice,
      DocumentType.withdrawalNotice,
      DocumentType.maximumWeeklyBenefitChangeNotice,
      DocumentType.benefitAmountChangeNotice,
      DocumentType.leaveAllotmentChangeNotice,
      DocumentType.approvedTimeCancelled,
      DocumentType.changeRequestApproved,
      DocumentType.changeRequestDenied,
      DocumentType.approvalOfApplicationChange,
      DocumentType.denialOfApplicationChange,
      DocumentType.denialOfApplication,
      DocumentType.approvedLeaveDatesCancelled,
    ];
    const documents = manyDocumentTypes.map((document_type) => {
      return createMockBenefitsApplicationDocument({
        document_type,
        is_legal_notice: legalNoticeTypes.has(document_type),
      });
    });

    const legalNotices = getClaimantLegalNotices(documents);

    for (const doc of legalNotices) {
      expect(legalNoticeTypes.has(doc.document_type)).toBe(true);
    }
  });

  describe("#getClaimantLegalNotices", () => {
    it("legal notices shows only denial EOW notice if denial notice created after ff flip", () => {
      const denialDocumentTypes = [
        DocumentType.denialNotice,
        DocumentType.denialOfApplication,
        DocumentType.denialNoticeExplanationOfWages,
      ];
      const documents = denialDocumentTypes.map((document_type) => {
        return createMockBenefitsApplicationDocument({
          document_type,
          created_at: "2023-08-01",
          is_legal_notice: true,
        });
      });

      const legalNotices = getClaimantLegalNotices(documents);

      expect(legalNotices.length).toEqual(1);
      expect(legalNotices[0].document_type).toEqual(
        DocumentType.denialNoticeExplanationOfWages
      );
    });

    it("legal notices shows denial notice and denial EOW notice if denial notice created before ff flip", () => {
      const denialDocumentTypes = [
        DocumentType.denialNotice,
        DocumentType.denialNoticeExplanationOfWages,
      ];
      const documents = denialDocumentTypes.map((document_type) => {
        return createMockBenefitsApplicationDocument({
          document_type,
          created_at: "2021-05-01",
          is_legal_notice: true,
        });
      });

      const legalNotices = getClaimantLegalNotices(documents);

      expect(legalNotices.length).toEqual(2);
    });

    it("legal notices falls back to regular denial notice when denial EOW notice missing", () => {
      const denialDocumentTypes = [DocumentType.denialOfApplication];
      const documents = denialDocumentTypes.map((document_type) => {
        return createMockBenefitsApplicationDocument({
          document_type,
          created_at: "2021-05-01",
          is_legal_notice: true,
        });
      });

      const legalNotices = getClaimantLegalNotices(documents);

      expect(legalNotices.length).toEqual(1);
      expect(legalNotices[0].document_type).toEqual(
        DocumentType.denialOfApplication
      );
    });

    it("legal notices falls back to regular approval notice when approval Explanation of wages notice is missing", () => {
      const approvalDocumentTypes = [DocumentType.approvalNotice];
      const documents = approvalDocumentTypes.map((document_type) => {
        return createMockBenefitsApplicationDocument({
          document_type,
          created_at: "2021-05-01",
          is_legal_notice: true,
        });
      });

      const legalNotices = getClaimantLegalNotices(documents);

      expect(legalNotices.length).toEqual(1);
      expect(legalNotices[0].document_type).toEqual(
        DocumentType.approvalNotice
      );
    });
  });
});

describe("canShowDenialNotice", () => {
  it("returns false when denial notice EOW exists and denial notice was created after ff flip date ", () => {
    const denialDocumentTypes = [
      DocumentType.denialNotice,
      DocumentType.denialNoticeExplanationOfWages,
      DocumentType.appealModifyDecision,
    ];

    const documents = denialDocumentTypes.map((document_type) => {
      return {
        document_type,
        content_type: "application/pdf",
        created_at: "2023-08-01",
        description: "",
        fineos_document_id: "mock-doc-id",
        name: "",
      };
    });

    const denialDocument = {
      document_type: DocumentType.denialNotice,
      content_type: "application/pdf",
      created_at: "2023-08-01",
      description: "",
      fineos_document_id: "mock-doc-id",
      name: "",
    };

    const result = canShowDenialNotice(documents, denialDocument);

    expect(result).toEqual(false);
  });

  it("returns true when denial notice EOW exists and denial notice was created before ff flip date ", () => {
    const denialDocumentTypes = [
      DocumentType.denialNotice,
      DocumentType.denialNoticeExplanationOfWages,
    ];

    const documents = denialDocumentTypes.map((document_type) => {
      return {
        document_type,
        content_type: "application/pdf",
        created_at: "2021-05-01",
        description: "",
        fineos_document_id: "mock-doc-id",
        name: "",
      };
    });
    const denialDocument = {
      document_type: DocumentType.denialNotice,
      content_type: "application/pdf",
      created_at: "2021-05-01",
      description: "",
      fineos_document_id: "mock-doc-id",
      name: "",
    };

    const result = canShowDenialNotice(documents, denialDocument);

    expect(result).toEqual(true);
  });

  it("returns true when denial notice EOW doesn't exist and denial notice was created after ff flip date", () => {
    const denialDocumentTypes = [DocumentType.denialNotice];

    const documents = denialDocumentTypes.map((document_type) => {
      return {
        document_type,
        content_type: "application/pdf",
        created_at: "2023-08-01",
        description: "",
        fineos_document_id: "mock-doc-id",
        name: "",
      };
    });

    const denialDocument = documents[0];

    const result = canShowDenialNotice(documents, denialDocument);

    expect(result).toEqual(true);
  });

  it("returns true when the document is not a denial notice", () => {
    const denialDocumentTypes = [
      DocumentType.denialNotice,
      DocumentType.denialNoticeExplanationOfWages,
    ];

    const approvalNotice = {
      document_type: DocumentType.approvalNotice,
      // document_type: "Approval Notice",
      content_type: "application/pdf",
      created_at: "2023-08-01",
      description: "",
      fineos_document_id: "mock-doc-id",
      name: "",
      is_legal_notice: false,
      user_id: "mock-user-id",
      application_id: "mock-application-id",
    };

    const documents = [
      approvalNotice,
      ...denialDocumentTypes.map((document_type) => {
        return {
          document_type,
          content_type: "application/pdf",
          created_at: "2023-08-01",
          description: "",
          fineos_document_id: "mock-doc-id",
          name: "",
          is_legal_notice: false,
          user_id: "mock-user-id",
          application_id: "mock-application-id",
        };
      }),
    ];

    const canShowNotice = canShowDenialNotice(documents, approvalNotice);

    expect(canShowNotice).toBe(true);
  });
});

describe("showEmployeeApprovalNotice", () => {
  it("returns false when approval notice explanation of wages exists", () => {
    const approvalDocumentTypes = [
      DocumentType.approvalNotice,
      DocumentType.approvalNoticeExplanationOfWages,
    ];

    const documents = approvalDocumentTypes.map((document_type) => {
      return {
        document_type,
        content_type: "application/pdf",
        created_at: "2023-08-01",
        description: "",
        fineos_document_id: "mock-doc-id",
        name: "",
      };
    });

    const result = showEmployeeApprovalNotice(documents);

    expect(result).toEqual(false);
  });
  it("returns true when approval notice explanation of wages does not exist", () => {
    const approvalDocumentTypes = [DocumentType.approvalNotice];

    const documents = approvalDocumentTypes.map((document_type) => {
      return {
        document_type,
        content_type: "application/pdf",
        created_at: "2021-05-01",
        description: "",
        fineos_document_id: "mock-doc-id",
        name: "",
      };
    });

    const result = showEmployeeApprovalNotice(documents);

    expect(result).toEqual(true);
  });
});

describe("getEmployerLegalNotices", () => {
  it("confirms that notices are sorted from oldest to newest", () => {
    const legalNotices = getEmployerLegalNotices(
      mockedClaimDocumentsToTestDateSort
    );

    expect(legalNotices.length).toEqual(4);
    expect(new Date(getCreatedDate(legalNotices.at(0))).getTime()).toBeLessThan(
      new Date(getCreatedDate(legalNotices.at(1))).getTime()
    );
    expect(new Date(getCreatedDate(legalNotices.at(1))).getTime()).toEqual(
      new Date(getCreatedDate(legalNotices.at(2))).getTime()
    );
    expect(new Date(getCreatedDate(legalNotices.at(2))).getTime()).toBeLessThan(
      new Date(getCreatedDate(legalNotices.at(3))).getTime()
    );
  });
  it("returns list of employer only notices", () => {
    const allLegalDocTypes = [
      ...employerLegalDocTypes,
      DocumentType.explanationOfWages,
      DocumentType.denialNoticeExplanationOfWages,
    ];
    const documents = allLegalDocTypes.map((document_type) => {
      return {
        document_type,
        content_type: "application/pdf",
        created_at: "2021-05-01",
        description: "",
        fineos_document_id: "mock-doc-id",
        name: "",
      };
    });

    const legalNotices = getEmployerLegalNotices(documents);
    expect(legalNotices.length).toEqual(employerLegalDocTypes.length);
    expect(legalNotices.map((d) => d.document_type)).toEqual(
      employerLegalDocTypes
    );
  });
  it("returns original denial notice when ff is on and denial notice created after ff flip date", () => {
    const denialDocumentTypes = [
      DocumentType.denialNotice,
      DocumentType.denialNoticeExplanationOfWages,
    ];
    const documents = denialDocumentTypes.map((document_type) => {
      return {
        document_type,
        content_type: "application/pdf",
        created_at: "2023-08-01",
        description: "",
        fineos_document_id: "mock-doc-id",
        name: "",
      };
    });

    const legalNotices = getEmployerLegalNotices(documents);

    expect(legalNotices.length).toEqual(1);
    expect(legalNotices[0].document_type).toEqual(DocumentType.denialNotice);
  });
});

function getCreatedDate(document: ClaimDocument | undefined) {
  if (document !== undefined) {
    return document.created_at;
  }

  return "UNDEFINED";
}

describe("getDecisionNotices", () => {
  it("filters out notices of non-decision type with denial EOW", () => {
    const decisionNoticeTypes = new Set<string>([
      DocumentType.approvalNotice,
      DocumentType.denialNoticeExplanationOfWages,
      DocumentType.withdrawalNotice,
    ]);
    const manyDocumentTypes = [
      DocumentType.approvalNotice,
      DocumentType.denialNotice,
      DocumentType.denialNoticeExplanationOfWages,
      DocumentType.withdrawalNotice,
    ];
    const documents = manyDocumentTypes.map((document_type) => {
      return {
        document_type,
        content_type: "application/pdf",
        created_at: "2023-08-01",
        description: "",
        fineos_document_id: "mock-doc-id",
        name: "",
      };
    });

    const decisionNotices = getDecisionNotices(documents);

    expect(decisionNotices.length).toEqual(3);
    for (const doc of decisionNotices) {
      expect(decisionNoticeTypes.has(doc.document_type)).toBe(true);
    }
  });
});

describe("getRFIDocument", () => {
  it("gets the most recent RFI document", () => {
    const documentsList = [
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.identityVerification,
        created_at: "2022-01-18",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.identityVerification,
        created_at: "2022-01-20",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.requestForInfoNotice,
        created_at: "2022-01-16",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.requestForInfoNotice,
        created_at: "2022-01-17",
      }),
    ];
    const rfiDocument = getRFIDocument(documentsList);
    expect(rfiDocument?.created_at).toEqual("2022-01-17");
  });
});
describe("sortNewToOld", () => {
  it("sorts an array of RFI documents correctly", () => {
    const documentsList = [
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.requestForInfoNotice,
        created_at: "2022-01-18",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.requestForInfoNotice,
        created_at: "2022-01-20",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.requestForInfoNotice,
        created_at: "2022-01-19",
      }),
    ];
    const mostRecentDocument = sortNewToOld(documentsList)[0];
    expect(mostRecentDocument.created_at).toEqual("2022-01-20");
  });
  it("sorts an array of mixed documents correctly", () => {
    const documentsList = [
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.identityVerification,
        created_at: "2022-01-18",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.identityVerification,
        created_at: "2022-01-20",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.requestForInfoNotice,
        created_at: "2022-01-16",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.requestForInfoNotice,
        created_at: "2022-01-17",
      }),
    ];
    const mostRecentDocument = sortNewToOld(documentsList)[0];
    expect(mostRecentDocument.created_at).toEqual("2022-01-20");
  });
});
describe("getLatestDocumentUploadDate", () => {
  it("gets the most recent document upload date", () => {
    const documentsList = [
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.identityVerification,
        created_at: "2022-01-18",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.identityVerification,
        created_at: "2022-01-20",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.requestForInfoNotice,
        created_at: "2022-01-16",
      }),
      createMockBenefitsApplicationDocument({
        document_type: DocumentType.requestForInfoNotice,
        created_at: "2022-01-17",
      }),
    ];
    const mostRecentDocument = sortNewToOld(documentsList)[0];
    const mostRecentDocumentDate = getLatestDocumentUploadDate(documentsList);
    expect(mostRecentDocument.created_at).toEqual(mostRecentDocumentDate);
  });
  describe("latestDocumentOfTypeUploadedAt", () => {
    it("gets the most recent document for the type", () => {
      const documentsList = [
        createMockBenefitsApplicationDocument({
          document_type: DocumentType.identityVerification,
          created_at: "2022-01-18",
        }),
        createMockBenefitsApplicationDocument({
          document_type: DocumentType.identityVerification,
          created_at: "2022-01-20",
        }),
        createMockBenefitsApplicationDocument({
          document_type: DocumentType.requestForInfoNotice,
          created_at: "2022-01-16",
        }),
        createMockBenefitsApplicationDocument({
          document_type: DocumentType.requestForInfoNotice,
          created_at: "2022-01-17",
        }),
      ];
      const uploadDate = latestDocumentOfTypeUploadedAt(
        documentsList,
        DocumentType.identityVerification
      );
      expect(uploadDate).toBe("2022-01-20");
    });
  });
});
