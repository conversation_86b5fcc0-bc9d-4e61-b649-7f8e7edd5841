import User, { UserLeaveAdministrator } from "src/models/User";
import { renderHook, waitFor } from "@testing-library/react";

import OAuthServerApi from "src/api/OAuthServerApi";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import { setupChatWidgetLogic } from "tests/test-utils/chat-widget-helpers";
import { useChatWidget } from "src/hooks/useChatWidget";

jest.mock("src/api/OAuthServerApi");

// TODO (PFMLPB-21132): Cleanup when feature flag is removed
describe("useChatWidget", () => {
  beforeEach(() => {
    window.cxone = jest.fn();
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it("does not initialize chat widget if live chat feature is disabled", () => {
    setFeatureFlags({ enableLiveChat: false });
    const chatWidgetLogic = setupChatWidgetLogic();
    const user = new User({ user_id: "mock-user-id" });
    renderHook(() => useChatWidget(user, chatWidgetLogic));

    expect(chatWidgetLogic.isChatWidgetInitialized).toBe(false);
  });

  it("does not initialize chat widget if chat widget is already initialized", () => {
    setFeatureFlags({ enableLiveChat: true });
    const user = new User({ user_id: "mock-user-id" });
    const chatWidgetLogic = setupChatWidgetLogic();
    renderHook(() => useChatWidget(user, chatWidgetLogic));

    expect(chatWidgetLogic.isChatWidgetInitialized).toBe(false);
  });

  it("does not initialize chat widget if user does not have verified an organization", () => {
    setFeatureFlags({ enableLiveChat: true });
    const user = new User({ user_id: "mock-user-id" });
    const chatWidgetLogic = setupChatWidgetLogic();
    user.hasVerifiedAnOrganization = jest.fn().mockReturnValue(false);
    renderHook(() => useChatWidget(user, chatWidgetLogic));

    expect(chatWidgetLogic.isChatWidgetInitialized).toBe(false);
  });

  it("initializes the chat widget", async () => {
    // Setup
    setFeatureFlags({ enableLiveChat: true });
    const mockAuthCode = "mock-auth-code";
    const user = new User({
      user_id: "mock-user-id",
      first_name: "mock-first-name",
      last_name: "mock-last-name",
      email_address: "mock-email-address",
      user_leave_administrators: [
        new UserLeaveAdministrator({
          employer_fein: "123456789",
          has_verification_data: true,
          verified: true,
        }),
      ],
    });
    const chatWidgetLogic = setupChatWidgetLogic();

    // Mock API call
    const mockGetOAuthServerCode = jest.fn().mockResolvedValue({
      code: { authz_code: mockAuthCode },
    });

    // type first as unknown - then as OAuthServerApi to
    // prevent having to mock entire object (basePath, namespace, headers etc..)
    jest.mocked(OAuthServerApi).mockImplementation(() => {
      return {
        getOAuthServerCode: mockGetOAuthServerCode,
      } as unknown as OAuthServerApi;
    });

    renderHook(() => useChatWidget(user, chatWidgetLogic));

    await waitFor(() => {
      expect(chatWidgetLogic.isChatWidgetInitialized).toBe(true);
    });

    expect(mockGetOAuthServerCode).toHaveBeenCalled();
    expect(window.cxone).toHaveBeenNthCalledWith(1, "init", "TEST");
    expect(window.cxone).toHaveBeenNthCalledWith(
      2,
      "chat",
      "setAuthorizationCode",
      mockAuthCode
    );
    expect(window.cxone).toHaveBeenNthCalledWith(3, "guide", "init");
    expect(window.cxone).toHaveBeenNthCalledWith(
      4,
      "guide",
      "setCustomFields",
      {
        customerName: { value: user.first_name, hidden: true },
      }
    );
  });
});
