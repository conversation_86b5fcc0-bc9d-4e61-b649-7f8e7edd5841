import { fireEvent, render, screen, waitFor } from "@testing-library/react";

import { AppLogic } from "src/hooks/useAppLogic";
import EmployerDownloadCSV from "src/components/EmployerDownloadCSV";
import React from "react";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import userEvent from "@testing-library/user-event";

jest.mock("i18next", () => ({
  t: (key: string) => key,
}));

const createMockAppLogic = (isDownloading = false) => {
  // Return success by default, can be overridden in tests
  const mockDownloadCSV = jest.fn(() => Promise.resolve(true)) as jest.Mock<
    Promise<boolean>
  >;
  const mockClearErrors = jest.fn() as jest.Mock;

  return {
    leaveAdmins: {
      isDownloadingCSV: isDownloading,
      downloadCSV: mockDownloadCSV,
    },
    clearErrors: mockClearErrors,
  } as unknown as AppLogic;
};

let mockAppLogic: ReturnType<typeof createMockAppLogic>;

const setupWithFeatureFlag = (
  featureFlag: boolean,
  isDownloading = false,
  totalApplication: number = 1000
) => {
  jest.clearAllMocks();

  mockAppLogic = createMockAppLogic(isDownloading);

  setFeatureFlags({ enableSKICSVExport: featureFlag });
  jest.spyOn(console, "error").mockImplementation(() => {});

  if (featureFlag) {
    render(
      <EmployerDownloadCSV
        appLogic={mockAppLogic}
        totalApplications={totalApplication}
      />
    );
  }
};

describe("EmployerDownloadCSV Component", () => {
  it("calls the API and triggers a download when clicked", async () => {
    // TODO (PFMLPB-22297): Cleanup feature flag
    setupWithFeatureFlag(true);
    const downloadButton = screen.getByRole("button", {
      name: "pages.employersApplications.csvLinkTitle",
    });
    await userEvent.click(downloadButton);

    // Should clear errors before downloading
    expect(mockAppLogic.clearErrors).toHaveBeenCalled();

    await waitFor(() => {
      expect(mockAppLogic.leaveAdmins.downloadCSV).toHaveBeenCalledWith(
        "leave_admin_applications.csv",
        1000
      );
    });

    expect(
      screen.queryByText(/pages.employersApplications.csvDownloadFailed/)
    ).not.toBeInTheDocument();
  });

  it("disables download button and shows spinner while downloading", () => {
    setupWithFeatureFlag(true, true);

    // Check that spinner is visible
    expect(screen.getByLabelText("Downloading CSV")).toBeInTheDocument();

    // Button should be disabled
    const downloadButton = screen.getByRole("button", {
      name: "pages.employersApplications.csvLinkTitle",
    });
    expect(downloadButton).toBeDisabled();
    expect(downloadButton).toHaveClass("text-primary-darker");
    expect(
      screen.queryByText(/pages.employersApplications.csvDownloadFailed/)
    ).not.toBeInTheDocument();
  });

  it("handles API errors gracefully", async () => {
    setupWithFeatureFlag(true);
    // Mock the downloadCSV function to return false (failed)
    (mockAppLogic.leaveAdmins.downloadCSV as jest.Mock).mockResolvedValueOnce(
      false
    );

    const downloadButton = screen.getByRole("button", {
      name: "pages.employersApplications.csvLinkTitle",
    });
    await userEvent.click(downloadButton);

    // Should clear errors before downloading
    expect(mockAppLogic.clearErrors).toHaveBeenCalled();

    await waitFor(() => {
      expect(mockAppLogic.leaveAdmins.downloadCSV).toHaveBeenCalled();
    });

    // Error banner should be displayed
    await waitFor(() => {
      expect(
        screen.getByText("pages.employersApplications.csvDownloadFailed")
      ).toBeInTheDocument();
    });
  });

  describe("keyboard interaction", () => {
    it("triggers download when Enter key is pressed", async () => {
      setupWithFeatureFlag(true);
      const downloadButton = screen.getByRole("button", {
        name: "pages.employersApplications.csvLinkTitle",
      });
      await userEvent.type(downloadButton, "{Enter}");

      // Should clear errors before downloading
      expect(mockAppLogic.clearErrors).toHaveBeenCalled();

      await waitFor(() => {
        expect(mockAppLogic.leaveAdmins.downloadCSV).toHaveBeenCalled();
      });

      expect(
        screen.queryByText(/pages.employersApplications.csvDownloadFailed/)
      ).not.toBeInTheDocument();
    });

    it("triggers download when Space key is pressed", async () => {
      setupWithFeatureFlag(true);
      const downloadButton = screen.getByRole("button", {
        name: "pages.employersApplications.csvLinkTitle",
      });
      await userEvent.type(downloadButton, " ");

      // Should clear errors before downloading
      expect(mockAppLogic.clearErrors).toHaveBeenCalled();

      await waitFor(() => {
        expect(mockAppLogic.leaveAdmins.downloadCSV).toHaveBeenCalled();
      });

      expect(
        screen.queryByText(/pages.employersApplications.csvDownloadFailed/)
      ).not.toBeInTheDocument();
    });

    it("does not trigger download for other keys", () => {
      // eslint-disable-next-line testing-library/prefer-user-event
      setupWithFeatureFlag(true);
      const downloadButton = screen.getByRole("button", {
        name: "pages.employersApplications.csvLinkTitle",
      });

      (mockAppLogic.leaveAdmins.downloadCSV as jest.Mock).mockClear();
      (mockAppLogic.clearErrors as jest.Mock).mockClear();

      // eslint-disable-next-line testing-library/prefer-user-event
      fireEvent.keyDown(downloadButton, { key: "x", code: "KeyX" });

      expect(mockAppLogic.leaveAdmins.downloadCSV).not.toHaveBeenCalled();
      expect(mockAppLogic.clearErrors).not.toHaveBeenCalled();
    });

    it("shows error message when download fails on keyboard trigger", async () => {
      setupWithFeatureFlag(true);
      // Mock the downloadCSV function to return false (failed)
      (mockAppLogic.leaveAdmins.downloadCSV as jest.Mock).mockResolvedValueOnce(
        false
      );

      const downloadButton = screen.getByRole("button", {
        name: "pages.employersApplications.csvLinkTitle",
      });

      // Use fireEvent for more direct control over the keyDown event
      // eslint-disable-next-line testing-library/prefer-user-event
      fireEvent.keyDown(downloadButton, { key: "Enter" });

      // Should clear errors before downloading
      expect(mockAppLogic.clearErrors).toHaveBeenCalled();

      await waitFor(() => {
        expect(mockAppLogic.leaveAdmins.downloadCSV).toHaveBeenCalled();
      });

      // Wait for the error banner to appear
      await waitFor(() => {
        expect(
          screen.getByText("pages.employersApplications.csvDownloadFailed")
        ).toBeInTheDocument();
      });
    });
  });

  describe("with feature flag off", () => {
    it("should not display the download CSV button", () => {
      setupWithFeatureFlag(false);
      const downloadButton = screen.queryByRole("button", {
        name: /download csv/i,
      });
      expect(downloadButton).not.toBeInTheDocument();
    });
  });
});
