import { render, screen } from "@testing-library/react";

import NoOrganizationsAlert from "src/components/NoOrganizationsAlert";
import React from "react";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

describe("NoOrganizationsAlert", () => {
  it("renders Alert", () => {
    const { container } = render(<NoOrganizationsAlert />);
    expect(container.firstChild).toMatchSnapshot();
    expect(
      screen.getByText("Get access to your organization(s)")
    ).toBeInTheDocument();
  });
});

// TODO (PFMLPB-19806): removal of feature flag
describe("employer exemptions portal", () => {
  it("renders the employer exemptions alert text when the enableEmployerExemptionsPortal feature flag is enabled", () => {
    setEnableEmployerExemptionsPortal(true);
    render(<NoOrganizationsAlert />);
    expect(
      screen.getByText(
        /To manage leave applications or exemption requests, you will need to add an organization./i
      )
    ).toBeInTheDocument();
  });

  it("does not render the employer exemptions alert text when the enableEmployerExemptionsPortal feature flag is disabled", () => {
    setEnableEmployerExemptionsPortal(false);
    render(<NoOrganizationsAlert />);
    expect(
      screen.queryByText(
        /To manage leave applications or exemption requests, you will need to add an organization./i
      )
    ).not.toBeInTheDocument();
  });
});
