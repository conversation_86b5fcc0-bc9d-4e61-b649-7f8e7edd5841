import { render, screen } from "@testing-library/react";
import EmployerNavigationTabs from "src/components/EmployerNavigationTabs";
import React from "react";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

// TODO (PFMLPB-19806): removal of feature flag
const setEnableEmployerExemptionsPortal = (value: boolean) => {
  setFeatureFlags({
    enableEmployerExemptionsPortal: value,
  });
};

describe("EmployerNavigationTabs", () => {
  it("renders the component", () => {
    const { container } = render(
      <EmployerNavigationTabs
        activePath={routes.employers.welcome}
        // @ts-expect-error TS2345
        hasEmployerExemptionRequests={undefined}
      />
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});

describe("when the enableEmployerExemptionsPortal feature flag is enabled", () => {
  beforeEach(() => {
    setEnableEmployerExemptionsPortal(true);
  });

  it("renders the exemption tab if hasEmployerExemptionRequests is true", () => {
    render(
      <EmployerNavigationTabs
        activePath={routes.employers.welcome}
        hasEmployerExemptionRequests={true}
      />
    );

    expect(screen.queryByText("Exemptions")).toBeInTheDocument();
  });

  it.each([undefined, false])(
    "does not render the exemption tab if hasEmployerExemptionRequests is %s",
    (hasEmployerExemptionRequests) => {
      render(
        <EmployerNavigationTabs
          activePath={routes.employers.welcome}
          // @ts-expect-error TS2345
          hasEmployerExemptionRequests={hasEmployerExemptionRequests}
        />
      );

      expect(screen.queryByText("Exemptions")).not.toBeInTheDocument();
    }
  );
});

describe("when the enableEmployerExemptionsPortal feature flag is disabled", () => {
  beforeEach(() => {
    setEnableEmployerExemptionsPortal(false);
  });

  it.each([undefined, true, false])(
    "does not render the exemptions tab if hasEmployerExemptionRequests is %s",
    (hasEmployerExemptionRequests) => {
      render(
        <EmployerNavigationTabs
          activePath={routes.employers.welcome}
          // @ts-expect-error TS2345
          hasEmployerExemptionRequests={hasEmployerExemptionRequests}
        />
      );
      expect(screen.queryByText("Exemptions")).not.toBeInTheDocument();
    }
  );
});
