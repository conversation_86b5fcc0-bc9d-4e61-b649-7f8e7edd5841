import { Faker, en } from "@faker-js/faker";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import { ExemptionRequestSteps } from "src/models/EmployerExemptionsStep";
import { PhoneWithExtension } from "src/models/Phone";

const faker = new Faker({ locale: [en] });
faker.seed(100);

export class MockEmployerExemptionsApplicationBuilder {
  exemptionAttrs: EmployerExemptionsApplication;

  private static readonly defaultPhone: PhoneWithExtension = {
    int_code: "1",
    phone_number: "5555555555",
    extension: null,
    phone_type: null,
  };

  private static readonly emptyExemptionsApplication = {
    employer_exemption_application_id: "4a431e6a-4a49-419a-9c36-7ccbfedd5dc9",
    contact_first_name: null,
    contact_last_name: null,
    contact_title: null,
    contact_phone: {
      int_code: null,
      phone_number: null,
      extension: null,
      phone_type: null,
    },
    contact_email_address: null,
    should_workforce_count_include_1099_misc: null,
    average_workforce_count: null,
    has_family_exemption: null,
    has_medical_exemption: null,
    is_self_insured_plan: null,
    insurance_plan_effective_at: null,
    insurance_plan_expires_at: null,
    purchased_plan: {
      insurance_provider_id: null,
      insurance_provider_name: null,
      insurance_plan_name: null,
      insurance_plan_id: null,
    },
    self_insured: {
      has_obtained_surety_bond: null,
      surety_company: null,
      surety_bond_amount: null,
      has_third_party_administrator: null,
      tpa_business_name: null,
      tpa_contact_first_name: null,
      tpa_contact_last_name: null,
      tpa_contact_title: null,
      tpa_contact_phone: null,
      tpa_contact_email_address: null,
      questions: {
        does_plan_cover_all_employees: null,
        does_plan_provide_enough_leave: null,
        does_plan_provide_enough_medical_leave: null,
        does_plan_provide_enough_caring_leave: null,
        does_plan_provide_enough_bonding_leave: null,
        does_plan_provide_enough_armed_forces_leave: null,
        does_plan_provide_enough_armed_forces_illness_leave: null,
        does_plan_pay_enough_benefits: null,
        does_employer_withhold_premiums: null,
        are_employer_withholdings_within_allowable_amount: null,
        does_plan_provide_pfml_job_protection: null,
        does_plan_provide_return_to_work_benefits: null,
        does_plan_cover_employee_contribution: null,
        does_plan_provide_intermittent_caring_leave: null,
        does_plan_provide_intermittent_bonding_leave: null,
        does_plan_provide_intermittent_armed_forces_leave: null,
        does_plan_provide_intermittent_medical_leave: null,
        does_plan_cover_former_employees: null,
        does_plan_favor_paid_leave_benefits: null,
      },
    },
    documents: {
      // TODO (PFMLPB-20633): add exemption document types
    },
    employer_exemption_application_status_id: null,
    is_legally_acknowledged: false,
    submitted_at: null,
    created_by_user_id: null,
    employer_id: "dda903f-f093f-ff900",
    is_application_status_auto_decided: null,
    submitted_by_user_id: null,
  };

  constructor(employer_exemption_application_id?: string) {
    this.exemptionAttrs = new EmployerExemptionsApplication(
      MockEmployerExemptionsApplicationBuilder.emptyExemptionsApplication
    );

    if (employer_exemption_application_id) {
      this.exemptionAttrs.employer_exemption_application_id =
        employer_exemption_application_id;
    }
  }

  init() {
    this.exemptionAttrs.employer_exemption_application_status_id = 1;
    this.exemptionAttrs.is_legally_acknowledged = false;
    this.exemptionAttrs.is_application_status_auto_decided = false;
    return this;
  }

  contactDetails() {
    this.exemptionAttrs.is_legally_acknowledged = true;
    this.exemptionAttrs.contact_first_name = faker.person.firstName();
    this.exemptionAttrs.contact_last_name = faker.person.lastName();
    this.exemptionAttrs.contact_title = faker.person.jobTitle();
    this.exemptionAttrs.contact_phone =
      MockEmployerExemptionsApplicationBuilder.defaultPhone;
    this.exemptionAttrs.contact_email_address = faker.internet.email();

    this.exemptionAttrs.has_third_party_administrator =
      faker.datatype.boolean();

    if (this.exemptionAttrs.has_third_party_administrator) {
      this.exemptionAttrs.tpa_business_name = faker.company.name();
      this.exemptionAttrs.tpa_contact_first_name = faker.person.firstName();
      this.exemptionAttrs.tpa_contact_last_name = faker.person.lastName();
      this.exemptionAttrs.tpa_contact_title = faker.person.jobTitle();
      this.exemptionAttrs.tpa_contact_phone =
        MockEmployerExemptionsApplicationBuilder.defaultPhone;
      this.exemptionAttrs.tpa_contact_email_address = faker.internet.email();
    }

    return this;
  }

  organizationDetails() {
    this.contactDetails();
    this.exemptionAttrs.should_workforce_count_include_1099_misc =
      faker.datatype.boolean();
    this.exemptionAttrs.average_workforce_count = faker.number.int();
    return this;
  }

  insuranceDetails() {
    this.contactDetails().organizationDetails();
    this.exemptionAttrs.has_family_exemption = faker.datatype.boolean();
    this.exemptionAttrs.has_medical_exemption = faker.datatype.boolean();
    this.exemptionAttrs.is_self_insured_plan = faker.datatype.boolean();
    this.exemptionAttrs.insurance_plan_effective_at = `${new Date().getFullYear()}-01-01`;
    this.exemptionAttrs.insurance_plan_expires_at = `${new Date().getFullYear()}-12-31`;

    if (this.exemptionAttrs.is_self_insured_plan) {
      this.exemptionAttrs.self_insured.has_obtained_surety_bond =
        faker.datatype.boolean();
      this.exemptionAttrs.self_insured.surety_company = faker.company.name();
      this.exemptionAttrs.self_insured.surety_bond_amount = parseFloat(
        faker.finance.amount()
      );

      let key: keyof EmployerExemptionsApplication["self_insured"]["questions"];
      for (key in this.exemptionAttrs.self_insured.questions) {
        this.exemptionAttrs.self_insured.questions[key] =
          faker.datatype.boolean();
      }
    } else {
      this.exemptionAttrs.purchased_plan.insurance_provider_id = 1;
      this.exemptionAttrs.purchased_plan.insurance_plan_id = 1;
    }

    return this;
  }

  create() {
    return new EmployerExemptionsApplication(this.exemptionAttrs);
  }
}

const EMPTY_APPLICATION = "EMPTY_APPLICATION";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
let VALID_APPLICATION_STEP:
  | (typeof ExemptionRequestSteps)[keyof typeof ExemptionRequestSteps]
  | typeof EMPTY_APPLICATION;

export const createMockEmployerExemptionsApplication = (
  application_id?: string,
  step: typeof VALID_APPLICATION_STEP = EMPTY_APPLICATION,
  patchData: Partial<EmployerExemptionsApplication> | object = {}
): EmployerExemptionsApplication => {
  const addStepDetailsToApplication = (
    app: MockEmployerExemptionsApplicationBuilder,
    step: typeof VALID_APPLICATION_STEP
  ): MockEmployerExemptionsApplicationBuilder => {
    const map = new Map<
      typeof VALID_APPLICATION_STEP,
      (
        app: MockEmployerExemptionsApplicationBuilder
      ) => MockEmployerExemptionsApplicationBuilder
    >([
      [
        "EMPTY_APPLICATION",
        (app: MockEmployerExemptionsApplicationBuilder) => app,
      ],
      [
        ExemptionRequestSteps.contactDetails,
        (app: MockEmployerExemptionsApplicationBuilder) => app.contactDetails(),
      ],
      [
        ExemptionRequestSteps.organization,
        (app: MockEmployerExemptionsApplicationBuilder) =>
          app.organizationDetails(),
      ],
      [
        ExemptionRequestSteps.insuranceDetails,
        (app: MockEmployerExemptionsApplicationBuilder) =>
          app.insuranceDetails(),
      ],
      [
        ExemptionRequestSteps.uploadDocuments,
        (app: MockEmployerExemptionsApplicationBuilder) =>
          app.insuranceDetails(),
      ],
    ]);

    return map.get(step)!(app);
  };

  const exemptionApplication = addStepDetailsToApplication(
    new MockEmployerExemptionsApplicationBuilder(application_id).init(),
    step
  );

  // assign patch properties to exemption application builder, prior to mock application creation
  Object.assign(exemptionApplication.exemptionAttrs, patchData);

  return exemptionApplication.create();
};
