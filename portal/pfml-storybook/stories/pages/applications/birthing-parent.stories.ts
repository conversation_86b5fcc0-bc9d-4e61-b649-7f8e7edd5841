import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { MockBenefitsApplicationBuilder } from "lib/mock-helpers/mock-model-builder";
import generateClaimPageStory from "pfml-storybook/utils/generateClaimPageStory";

const mockClaims = {
  empty: new MockBenefitsApplicationBuilder().create(),
};

const { config, DefaultStory } = generateClaimPageStory(
  "birthing-parent",
  mockClaims
);

const meta: Meta<typeof DefaultStory> = {
  title: "Pages/Applications/Birthing Parent",
  component: config.component,
};
export default meta;

type Story = StoryObj<typeof DefaultStory>;
export const Default: Story = DefaultStory;
