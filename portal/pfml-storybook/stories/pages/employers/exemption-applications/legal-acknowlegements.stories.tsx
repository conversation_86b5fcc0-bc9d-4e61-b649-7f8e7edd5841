import User, { UserLeaveAdministrator } from "src/models/User";

import { EmployerExemptionsLegal } from "src/pages/employers/exemption-applications/legal-acknowledgements";
import React from "react";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import routes from "src/routes";
import useMockableAppLogic from "lib/mock-helpers/useMockableAppLogic";

export default {
  title: "Pages/Employers/Employer Exemptions/Legal Acknowlegements",
  component: EmployerExemptionsLegal,
  argTypes: {},
  args: {},
};

export const Page = (_args: object) => {
  const appLogic = useMockableAppLogic({
    portalFlow: {
      pathname: routes.employers.employerExemptions.legal,
    },
  });
  const user = new User({
    user_leave_administrators: [
      new UserLeaveAdministrator({
        employer_dba: "Some Company",
        employer_fein: "11-11111",
        employer_id: "mock_employer_id",
      }),
    ],
  });

  const exemptionRequest = createMockEmployerExemptionsApplication();
  return (
    <EmployerExemptionsLegal
      appLogic={appLogic}
      user={user}
      exemptionRequest={exemptionRequest}
    />
  );
};
