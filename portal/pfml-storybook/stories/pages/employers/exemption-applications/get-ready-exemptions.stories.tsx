import User, { UserLeaveAdministrator } from "src/models/User";

import { GetReadyExemptions } from "src/pages/employers/exemption-applications/get-ready-exemptions";
import React from "react";
import routes from "src/routes";
import useMockableAppLogic from "lib/mock-helpers/useMockableAppLogic";

export default {
  title: "Pages/Employers/Employer Exemptions/Get Ready",
  component: GetReadyExemptions,
  argTypes: {},
  args: {},
};

export const Page = (_args: object) => {
  const appLogic = useMockableAppLogic({
    portalFlow: {
      pathname: routes.employers.employerExemptions.getReady,
    },
  });
  const user = new User({
    user_leave_administrators: [
      new UserLeaveAdministrator({
        employer_dba: "Some Company",
        employer_fein: "11-11111",
        employer_id: "mock_employer_id",
      }),
    ],
  });
  return <GetReadyExemptions appLogic={appLogic} user={user} />;
};
