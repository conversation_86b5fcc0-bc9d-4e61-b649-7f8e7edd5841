import User, { UserLeaveAdministrator } from "src/models/User";

import { EmployerExemptionsConfirmation } from "src/pages/employers/exemption-applications/confirmation";
import React from "react";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import routes from "src/routes";
import useMockableAppLogic from "lib/mock-helpers/useMockableAppLogic";

export default {
  title: "Pages/Employers/Employer Exemptions/Confirmation",
  component: EmployerExemptionsConfirmation,
  argTypes: {},
  args: {},
};

export const Page = (_args: object) => {
  const appLogic = useMockableAppLogic({
    portalFlow: {
      pathname: routes.employers.employerExemptions.confirmation,
    },
  });
  const user = new User({
    user_leave_administrators: [
      new UserLeaveAdministrator({
        employer_dba: "Some Company",
        employer_fein: "11-11111",
        employer_id: "mock_employer_id",
      }),
    ],
  });

  const exemptionRequest = createMockEmployerExemptionsApplication();

  return (
    <EmployerExemptionsConfirmation
      appLogic={appLogic}
      exemptionRequest={exemptionRequest}
      user={user}
    />
  );
};
