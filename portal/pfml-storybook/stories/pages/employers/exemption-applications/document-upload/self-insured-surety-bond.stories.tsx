import User, { UserLeaveAdministrator } from "src/models/User";

import { EmployerExemptionsSelfInsuredSuretyBondDocumentUpload } from "src/pages/employers/exemption-applications/document-upload/self-insured-surety-bond";
import React from "react";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import routes from "src/routes";
import useMockableAppLogic from "lib/mock-helpers/useMockableAppLogic";

export default {
  title:
    "Pages/Employers/Employer Exemptions/Document Upload/Self-Insured/Self-Insured Surety Bond",
  component: EmployerExemptionsSelfInsuredSuretyBondDocumentUpload,
  argTypes: {},
  args: {},
};

export const Page = (_args: object) => {
  const appLogic = useMockableAppLogic({
    portalFlow: {
      pathname: routes.employers.employerExemptions.documentUpload,
    },
  });
  const user = new User({
    user_leave_administrators: [
      new UserLeaveAdministrator({
        employer_dba: "Some Company",
        employer_fein: "11-11111",
        employer_id: "mock_employer_id",
      }),
    ],
  });
  const exemptionRequest = createMockEmployerExemptionsApplication();
  exemptionRequest.is_self_insured_plan = true;

  return (
    <EmployerExemptionsSelfInsuredSuretyBondDocumentUpload
      appLogic={appLogic}
      exemptionRequest={exemptionRequest}
      user={user}
    />
  );
};
