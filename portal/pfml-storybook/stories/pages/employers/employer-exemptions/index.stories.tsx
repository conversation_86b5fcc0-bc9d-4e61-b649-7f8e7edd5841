import User, { UserLeaveAdministrator } from "src/models/User";

import { ExemptionsTab } from "src/pages/employers/employer-exemptions/index";
import React from "react";
import routes from "src/routes";
import useMockableAppLogic from "lib/mock-helpers/useMockableAppLogic";

export default {
  title: "Pages/Employers/Exemptions",
  component: ExemptionsTab,
  args: {},
  argTypes: {},
};

export const Page = (_args: object) => {
  const appLogic = useMockableAppLogic({
    portalFlow: {
      pathname: routes.employers.employerExemptions.exemptionsTab,
    },
  });
  const user = new User({
    user_leave_administrators: [
      new UserLeaveAdministrator({
        employer_dba: "Some Company",
        employer_fein: "11-11111",
        employer_id: "mock_employer_id",
      }),
    ],
  });

  return <ExemptionsTab appLogic={appLogic} user={user} />;
};
