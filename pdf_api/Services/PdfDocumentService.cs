namespace PfmlPdfApi.Services
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using iText.Html2pdf;
    using iText.Kernel.Pdf;
    using iText.Kernel.Utils;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Logging;
    using PfmlPdfApi.Common;
    using PfmlPdfApi.Models;
    using PfmlPdfApi.Utilities;

    public interface IPdfDocumentService
    {
        Task<ResponseMessage<CreatedDocumentResponse>> Generate(Document dto);

        Task<ResponseMessage<MergeDocumentsResponse>> Merge(MergeDocumentsRequest dto);

        Task<ResponseMessage<MergeDocumentsResponse>> MergeBySubBatch(MergeDocumentsRequest dto, string subBatchId);

    }

    public class PdfDocumentService : IPdfDocumentService
    {
        private readonly IAmazonS3Service amazonS3Service;
        private readonly ILogger<PdfDocumentService> logger;

        public PdfDocumentService(IConfiguration configuration, IAmazonS3Service amazonS3Service, ILogger<PdfDocumentService> logger)
        {
            this.amazonS3Service = amazonS3Service;
            this.logger = logger;
        }

        public async Task<ResponseMessage<CreatedDocumentResponse>> Generate(Document dto)
        {
            ResponseMessage<CreatedDocumentResponse> response = new ResponseMessage<CreatedDocumentResponse>(null);

            try
            {
                response.Payload = await GenerateDocument(dto);
            }
            catch (Exception ex)
            {
                response.Status = MessageConstants.MsgStatusFailed;
                response.ErrorMessage = ex.Message;
            }

            return response;
        }

        public async Task<ResponseMessage<MergeDocumentsResponse>> Merge(MergeDocumentsRequest dto)
        {
            ResponseMessage<MergeDocumentsResponse> response = new ResponseMessage<MergeDocumentsResponse>(new MergeDocumentsResponse());
            List<string> createdDocumentList = new List<string>();
            string folderName = $"Batch-{dto.BatchId}";
            string formsFolderName = $"{dto.Type}/{folderName}/Forms";
            string mergedFolderName = $"{dto.Type}/{folderName}/Merged";
            IList<string> subBatches = await amazonS3Service.GetFoldersAsync(formsFolderName);
            int conForm = 0;
            int conMerge = 1;

            response.Payload.FormsFolderName = formsFolderName;
            response.Payload.MergedFolderName = mergedFolderName;
            string envTagetSubBatch = Environment.GetEnvironmentVariable("PFML_1099_MERGE_DOCUMENT_START_BATCH");
            int tagetSubBatch = 0;
            bool result = int.TryParse(envTagetSubBatch, out tagetSubBatch);
            var timer = new System.Diagnostics.Stopwatch();

            try
            {
                foreach (string subBatch in subBatches)
                {
                    if (conMerge < tagetSubBatch)
                    {
                        logger.LogInformation($"skipping {subBatch}");
                        conMerge++;
                        continue;
                    }
                    timer.Restart();
                    IList<Stream> files = await amazonS3Service.GetFilesAsync(subBatch);
                    timer.Stop();
                    logger.LogInformation($"Time to get files: {timer.ElapsedMilliseconds} ms");

                    string fileName = $"{mergedFolderName}/{folderName}.{conMerge}.pdf";
                    MemoryStream stream = new MemoryStream();
                    PdfWriter pdfWriter = new PdfWriter(stream);
                    PdfDocument pdfMergedDocument = new PdfDocument(pdfWriter);
                    PdfMerger merger = new PdfMerger(pdfMergedDocument);

                    try
                    {
                        var file_counter = 0;
                        foreach (Stream file in files)
                        {
                            timer.Restart();
                            file_counter++;
                            logger.LogInformation($"reading file number {file_counter}, canRead: {file.CanRead}");
                            PdfReader pdfReader = new PdfReader(file);
                            PdfDocument pdfDocument = new PdfDocument(pdfReader);

                            timer.Stop();
                            logger.LogInformation($"Time to read file: {timer.ElapsedMilliseconds} ms");
                            timer.Restart();
                            merger.Merge(pdfDocument, 1, pdfDocument.GetNumberOfPages());
                            timer.Stop();
                            logger.LogInformation($"Time to merge file: {timer.ElapsedMilliseconds} ms");
                            conForm++;
                        }

                        merger.Close();

                        logger.LogInformation($"Pfml Api: File {fileName} with {files.Count} Pdf(s) were successfully merged.");
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex.ToString());
                        response.Status = MessageConstants.MsgStatusFailed;
                        response.ErrorMessage = $"IText Exception detected! - {ex.Message}";
                        throw;
                    }

                    timer.Restart();
                    await amazonS3Service.CreateFileAsync(fileName, stream);

                    timer.Stop();
                    logger.LogInformation($"Time to upload merged file: {timer.ElapsedMilliseconds} ms");
                    createdDocumentList.Add($"s3://{amazonS3Service.GetBucket()}/{fileName}");

                    conMerge++;
                }

                response.Payload.CreatedDocumentList = createdDocumentList;
                response.Payload.TotalFormDocuments = conForm;
                response.Payload.TotalMergedDocuments = conMerge - 1; // Subtract 1 because conMerge starts at 1.
            }
            catch (Exception ex)
            {
                logger.LogError(ex.ToString());
                response.Status = MessageConstants.MsgStatusFailed;
                response.ErrorMessage = ex.Message;
            }

            return response;
        }

        public async Task<ResponseMessage<MergeDocumentsResponse>> MergeBySubBatch(MergeDocumentsRequest dto, string subBatchId)
        {
            ResponseMessage<MergeDocumentsResponse> response = new ResponseMessage<MergeDocumentsResponse>(new MergeDocumentsResponse());
            if (string.IsNullOrEmpty(subBatchId))
            {
                response.Status = MessageConstants.MsgStatusFailed;
                response.ErrorMessage = "SubBatchId cannot be null or empty.";
                return response;
            }
            List<string> createdDocumentList = new List<string>();
            string folderName = $"Batch-{dto.BatchId}";
            string formsFolderName = $"{dto.Type}/{folderName}/Forms";
            string mergedFolderName = $"{dto.Type}/{folderName}/Merged";
            IList<string> subBatches = await amazonS3Service.GetFoldersAsync(formsFolderName);

            // Find the sub-batch folder that matches the subBatchId
            // Assuming subBatchId is a part of the folder name, we can use a case-insensitive search
            string subBatchFolder = subBatches.FirstOrDefault(f => f != null && f.IndexOf(subBatchId, StringComparison.OrdinalIgnoreCase) >= 0);

            // If no matching sub-batch folder is found, return an error response
            if (string.IsNullOrEmpty(subBatchFolder))
            {
                response.Status = MessageConstants.MsgStatusFailed;
                response.ErrorMessage = $"Sub-batch folder '{subBatchId}' not found in S3.";
                return response;
            }
            logger.LogInformation($"Sub-batch folder found: {subBatchFolder}");
            response.Payload.FormsFolderName = subBatchFolder;
            response.Payload.MergedFolderName = mergedFolderName;
            var timer = new System.Diagnostics.Stopwatch();

            // Extract the batch number from the subBatchId
            // Assuming subBatchId is in the format "Sub-batch-<number>"
            string batchNumber = Regex.Match(subBatchId, @"Sub-batch-(\d+)").Groups[1].Value;

            try
            {
                timer.Restart();
                IList<Stream> files = await amazonS3Service.GetFilesAsync(subBatchFolder);
                timer.Stop();
                logger.LogInformation($"Time to get files: {timer.ElapsedMilliseconds} ms");

                string fileName = $"{mergedFolderName}/{folderName}.{batchNumber}.pdf";
                MemoryStream stream = new MemoryStream();
                PdfWriter pdfWriter = new PdfWriter(stream);
                PdfDocument pdfMergedDocument = new PdfDocument(pdfWriter);
                PdfMerger merger = new PdfMerger(pdfMergedDocument);

                try
                {
                    var file_counter = 0;
                    foreach (Stream file in files)
                    {
                        timer.Restart();
                        file_counter++;
                        logger.LogInformation($"reading file number {file_counter}, canRead: {file.CanRead}");
                        PdfReader pdfReader = new PdfReader(file);
                        PdfDocument pdfDocument = new PdfDocument(pdfReader);

                        timer.Stop();
                        logger.LogInformation($"Time to read file: {timer.ElapsedMilliseconds} ms");
                        timer.Restart();
                        merger.Merge(pdfDocument, 1, pdfDocument.GetNumberOfPages());
                        timer.Stop();
                        logger.LogInformation($"Time to merge file: {timer.ElapsedMilliseconds} ms");
                    }

                    merger.Close();

                    logger.LogInformation($"Pfml Api: File {fileName} with {files.Count} Pdf(s) were successfully merged.");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex.ToString());
                    response.Status = MessageConstants.MsgStatusFailed;
                    response.ErrorMessage = $"IText Exception detected! - {ex.Message}";
                    throw;
                }

                timer.Restart();
                await amazonS3Service.CreateFileAsync(fileName, stream);

                timer.Stop();
                logger.LogInformation($"Time to upload merged file: {timer.ElapsedMilliseconds} ms");
                createdDocumentList.Add($"s3://{amazonS3Service.GetBucket()}/{fileName}");

                response.Payload.CreatedDocumentList = createdDocumentList;
            }
            catch (Exception ex)
            {
                logger.LogError(ex.ToString());
                response.Status = MessageConstants.MsgStatusFailed;
                response.ErrorMessage = ex.Message;
            }

            return response;
        }

        private async Task<CreatedDocumentResponse> GenerateDocument(Document dto)
        {
            if (dto.Template.Contains(".."))
            {
                throw new Exception("Path traversal detected.");
            }

            // Read html template file
            MemoryStream template = new MemoryStream(await File.ReadAllBytesAsync(dto.Template));

            // Replace all template values
            string document = dto.ReplaceValuesInTemplate(new StreamReader(template).ReadToEnd());

            // Convert HTML file to PDF
            MemoryStream stream = new MemoryStream();
            HtmlConverter.ConvertToPdf(document, stream);

            // Save PDF to S3
            string location = $"{dto.Id}.pdf";
            if (dto.Save)
            {
                await amazonS3Service.CreateFileAsync(dto.FileName, stream);

                // Build path to PDF in S3
                location = $"s3://{amazonS3Service.GetBucket()}/{dto.FileName}";
            }

            // Build initial response payload
            CreatedDocumentResponse createdDocumentDto = new CreatedDocumentResponse
            {
                Location = location,
            };

            // If we're not saving to S3, the Response will be the PDF itself
            if (!dto.Save)
            {
                MemoryStream pdfStream = new MemoryStream(stream.ToArray());
                pdfStream.Seek(0, SeekOrigin.Begin);
                createdDocumentDto.File = pdfStream;
            }

            return createdDocumentDto;
        }
    }
}