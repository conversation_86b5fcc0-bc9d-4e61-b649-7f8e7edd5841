namespace PfmlPdfApi.Controllers.V2
{
    using System;
    using System.Collections.Generic;
    using System.Net;
    using System.Net.Mime;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using PfmlPdfApi.Common;
    using PfmlPdfApi.Models;
    using PfmlPdfApi.Services;
    using Swashbuckle.AspNetCore.Annotations;

    [ApiController]
    [ApiVersion("2.0")]
    [Route("v{version:apiVersion}/api/pdf")]
    public class PdfControllerV2 : ControllerBase
    {
        private readonly IPdfDocumentService pdfDocumentService;

        public PdfControllerV2(IPdfDocumentService pdfDocumentService)
        {
            this.pdfDocumentService = pdfDocumentService;
        }

        [MapToApiVersion("2.0")]
        [SwaggerOperation(
            Summary = "Home",
            Description = "Used to determine if the PDF API is up and running on a particular environment and which version."
        )]
        [SwaggerResponse(200, "PFML PDF API is running!")]
        [HttpGet]
        public ActionResult Get()
        {
            return Ok(string.Format("{0} - PFML PDF API V2 is running.", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT").Substring(0, 3)));
        }

        [MapToApiVersion("2.0")]
        [SwaggerOperation(
            Summary = "Generates a new pdf",
            Description = "Attempts to generate a PDF file using the data provided in the `templateValues` and the HTML template corresponding to the `type` of desired document.\n\nWhen `save` is true, it will also attempt to save the PDF to S3, otherwise returns it as a file stream."
        )]
        [SwaggerResponse(200, "The pdf was successfully generated!", typeof(FileResult), "application/pdf")]
        [SwaggerResponse(201, "The pdf was successfully generated and uploaded to S3!", typeof(string), "text/plain")]
        [SwaggerResponse(500, "The pdf could not be generated!")]
        [HttpPost]
        [Route("generate")]
        public async Task<ActionResult> Generate([FromBody, SwaggerRequestBody("Information required for document generation", Required = true)] Document document)
        {
            ResponseMessage<CreatedDocumentResponse> response = await pdfDocumentService.Generate(document);

            if (response.Status == MessageConstants.MsgStatusSuccess)
            {
                // If the PDF is in S3
                if (response.Payload.File == null)
                {
                    // Return payload with full S3 location
                    return Created(response.Payload.Location, response.Payload.Location);
                }
                else
                {
                    // Otherwise, the Response will be the PDF file stream itself
                    return File(response.Payload.File, MediaTypeNames.Application.Pdf, response.Payload.Location);
                }
            }

            return StatusCode((int)HttpStatusCode.InternalServerError, response.ErrorMessage);
        }

        [MapToApiVersion("2.0")]
        [SwaggerOperation(
            Summary = "Merges a batch of documents",
            Description = "Will attempt to find the specified batch id in S3 and merge all documents it finds into a single PDF for each sub batch."
        )]
        [SwaggerResponse(200, "Document batch successfully merged!", typeof(MergeDocumentsResponse))]
        [SwaggerResponse(500, "Failed to merge document batch.")]
        [HttpPost]
        [Route("merge")]
        public async Task<ActionResult> Merge([FromBody, SwaggerRequestBody("Information required for document batch merging", Required = true)] MergeDocumentsRequest batch)
        {
            ResponseMessage<MergeDocumentsResponse> response = await pdfDocumentService.Merge(batch);

            if (response.Status == MessageConstants.MsgStatusSuccess)
            {
                return Ok(response.Payload);
            }

            return StatusCode((int)HttpStatusCode.InternalServerError, response.ErrorMessage);
        }

        [MapToApiVersion("2.0")]
        [SwaggerOperation(
            Summary = "Merges a single subBatch of documents",
            Description = "Will attempt to find the specified subBatch per batch id in S3 and merge all documents it finds into a single PDF for that sub batch.")]
        [SwaggerResponse(200, "Document subBatch successfully merged!")]
        [SwaggerResponse(500, "Failed to merge document subBatch.")]
        [HttpPost]
        [Route("mergeBySubBatch")]
        public async Task<ActionResult> MergeBySubBatch([FromQuery, SwaggerRequestBody("SubBatch Id", Required = true)] string subBatchId, [FromBody, SwaggerRequestBody("Information required for document batch merging", Required = true)] MergeDocumentsRequest batch)
        {
            ResponseMessage<MergeDocumentsResponse> response = await pdfDocumentService.MergeBySubBatch(batch, subBatchId);

            if (response.Status == MessageConstants.MsgStatusSuccess)
            {
                return Ok();
            }

            return StatusCode((int)HttpStatusCode.InternalServerError, response.ErrorMessage);
        }
    }
}
